﻿using ArtisanManage.MyJXC;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace ArtisanManage.YingJiangMallMini.Model;

public class MallMiniControllerParameter
{
}

public class MallMiniCompanyIdParameter
{
    public string companyId { get; set; }
}

/// <summary>
/// 基础参数
/// </summary>
public class MallMinBaseParameter
{
    public string operKey { get; set; }
    public string? companyId { get; set; }
    public int supcustId { get; set; }

    public MallMinBaseParameter()
    {
    }

    public MallMinBaseParameter(string operKey, string companyId, int supcustId)
    {
        this.operKey = operKey;
        this.companyId = companyId;
        this.supcustId = supcustId;
    }
}
/// <summary>
/// 获取新客红包参数（从小程序发送过来）
/// </summary>
public class MallMinSendParameter
{
    public string operKey { get; set; }
    public string? companyId { get; set; }
    public int supcustId { get; set; }
}
/// <summary>
/// 记录红包流水以及余额变化（从小程序发送过来）
/// </summary>
public class MallMinRedPacketLogandChangeParameter
{
    public string operKey { get; set; }
    public int supcustId { get; set; }
    public string red_packet_id { get; set; }
    public List<RedPacketDetail> red_packet_details { get; set; }

    public class RedPacketDetail
    {
        public string red_packet_id { get; set; } 
        public string red_packet_type { get; set; }
        public decimal red_packet_amount { get; set; }
    }
}
/// <summary>
/// 下单返利参数（从小程序发送过来）
/// </summary>
public class MallMinPurchaseRewardParameter
{
    public string operKey { get; set; }
    public string? companyId { get; set; }
    public int supcustId { get; set; }
    public List<GetPurchaseRewardRequiredSheetRow> sheet_rows { get; set; } = new List<GetPurchaseRewardRequiredSheetRow>();
}
/// <summary>
/// 下单返利参数（从小程序发送过来）-- 行参数
/// </summary>
public class GetPurchaseRewardRequiredSheetRow
{
    public string item_id { get; set; }
    public string? item_brand { get; set; }
    public string item_class { get; set; }

    public decimal total_price { get; set; }//实付金额
}
/// <summary>
/// 下单返利参数（返回给小程序）
/// </summary>
public class GetPurchaseRewardResponse
{
    public string result { get; set; }
    public string msg { get; set; }
    public decimal total_purchase_reward_redpacket { get; set; }
    public List<PurchaseRewardDetail> details { get; set; }

    public class PurchaseRewardDetail
    {
        public int item_id { get; set; }
        public string red_packet_id { get; set; }
        public string? item_brand { get; set; }
        public string item_class { get; set; }
        public decimal total_price { get; set; }
        public decimal purchase_reward { get; set; }
        public string reward_type { get; set; }
    }
}
/// <summary>
/// 下单返利参数（返回给service）
/// </summary>
public class GetPurchaseRewardParameter
{
    public string redPacketId { get; set; }
    public string companyId { get; set; }
}
public class GetPurchaseRewardDao
{
    public int item_id { get; set; }
    public int item_brand { get; set; }
    public string item_class { get; set; }
    public string type { get; set; }
    public string policy { get; set; }
}
public class PolicyItem
{
    public decimal reach_cost { get; set; }
    public string red_packet_amount { get; set; }
    public int red_packet_id { get; set; }
}
public class RestrictGoods
{
    public List<string> brands { get; set; }
    public List<string> classes { get; set; }
    public List<RestrictItem> itemList { get; set; }
    public List<string> items_ban { get; set; }
    public class RestrictItem
    {
        public string item_id { get; set; }
        public string item_name { get; set; }
        public string brand_name { get; set; }
        public string class_name { get; set; }
        public string s_buy_price { get; set; }
        public string s_cost_price { get; set; }
    }
    // 自定义反序列化方法
    public static RestrictGoods FromJson(string json)
    {
        var restrictGoods = JsonConvert.DeserializeObject<RestrictGoods>(json);

        // 处理 "all" 的情况
        if (restrictGoods.brands != null && restrictGoods.brands.Count == 1 && restrictGoods.brands[0] == "all")
        {
            restrictGoods.brands = new List<string> {"-1"}; // 允许所有品牌
        }
        else if (restrictGoods.brands == null)
        {
            restrictGoods.brands = new List<string>(); // 初始化为空列表
        }

        if (restrictGoods.classes != null && restrictGoods.classes.Count == 1 && restrictGoods.classes[0] == "all")
        {
            restrictGoods.classes = new List<string> { "-1" }; // 允许所有类别
        }
        else if (restrictGoods.classes == null)
        {
            restrictGoods.classes = new List<string>(); // 初始化为空列表
        }

        if (restrictGoods.items_ban != null && restrictGoods.items_ban.Count == 1 && restrictGoods.items_ban[0] == "all")
        {
            restrictGoods.items_ban = new List<string> { "-1" }; // 允许所有禁止项
        }
        else if (restrictGoods.items_ban == null)
        {
            restrictGoods.items_ban = new List<string>(); // 初始化为空列表
        }

        return restrictGoods;
    }
}
/// <summary>
/// 获取红包方案返回参数
/// </summary>
public class GetRedpacketPlanDao
{
    public int red_packet_id { get; set; }
    public string red_packet_name { get; set; }
    public string red_packet_amount { get; set; }
    public string restrict_goods { get; set; }
    public decimal reach_cost { get; set; }
}

/// <summary>
/// 涉及微信用户基础信息
/// </summary>
public class MallMinWxUserBaseParameter
{
    public string operKey { get; set; }
    public string? companyId { get; set; }
    public int? supcustId { get; set; }
    public int? wx_user_id { get; set; }

    public MallMinWxUserBaseParameter()
    {
    }

    public MallMinWxUserBaseParameter(string operKey, string companyId, int? supcustId, int? wxUserId)
    {
        this.operKey = operKey;
        this.companyId = companyId;
        this.supcustId = supcustId;
        wx_user_id = wxUserId;
    }
}


public class MallMiniSummaryAccountParameter : MallMinBaseParameter
{
    public List<string> accountTypes { get; set; }
}

public class MallMiniGetRedPacketHistoryParameter : MallMinBaseParameter
{
    public string startDate { get; set; }
    public string endDate { get; set; }
    public int pageNo { get; set; }
    public int pageSize { get; set; }
}

public class MallMiniGetItemsOrderedBalanceParameter : MallMinBaseParameter
{
    public int prepaySubId { get; set; }
}

public class GetMallMiniDisplayAgreementSheetListParameter : MallMinBaseParameter
{
    public int prepaySubId { get; set; }
    public string? startDate { get; set; }
    public string? endDate { get; set; }
    public int? pageSize { get; set; }
    public int? pageNo { get; set; }
}

/// <summary>
/// 后台加载模板数据
/// </summary>
public class LoadManageTemplateInfoParameter
{
    public bool frontBranchDiffClassValue { get; set; }
    public bool useMallItemClass { get; set; }
    public int? frontBranchId { get; set; }
    public string companyID { get; set; }
    public string operKey { get; set; }
    public int templateId { get; set; }
    public int branch_id { get; set; }
}

// 首页模板参数
public class LoadNewIndexTemplateParameter : MallMinBaseParameter
{
    public string front_branch_id { get; set; }
    public int branch_id { get; set; }

    public bool mallAllowNegativeStockOrder { get; set; }
    public bool mallShowNegativeStockItem { get; set; }
    public bool? frontBranchDiffClass { get; set; }
    public bool useMallItemClass { get; set; }
}

public class GetMallMiniBranchInfoParameter : MallMinBaseParameter
{
    public string other_region { get; set; }
    public string wx_share_seller_dept_id { get; set; }
}

public class GetMallMiniPromotionContentParameter : MallMinBaseParameter
{
    public string mall_binding_seller { get; set; }
    public string sup_group { get; set; }
    public string sup_rank { get; set; }
    public string other_region { get; set; }
    public string creator_depart_id { get; set; }
    public string charge_seller_depart_id { get; set; }
    public string wx_share_seller_dept_id { get; set; }
    public string front_branch_id { get; set; }
    public string front_dept_id { get; set; }
}

public class UpdateWxUserAvatarOrMobileOrMiniNickNameParameter
{
    public int wx_user_id { get; set; }
    public string avatar_url { get; set; }
    public string mini_mobile { get; set; }
    public string mini_nick_name { get; set; }
}

public class GetInfoCustContactListParameter
{
    public string operKey { get; set; }
    public string? companyId { get; set; }
    public int pageSize { get; set; }
    public int pageNo { get; set; }
    public string mini_nick_name { get; set; }
    public string mobile { get; set; }
    public string supcust_id { get; set; }
    public string mini_open_id { get; set; }
    public string retail_wholesale_flag { get; set; }
}

public class UpdateInfoCustContactApproveStatusParameter
{
    public string operKey { get; set; }
    public string? companyId { get; set; }
    public string? oper_id { get; set; }
    public int contact_id { get; set; }
    public bool approve_status { get; set; }
}

public class UnBindInfoCustContactParameter
{
    public string operKey { get; set; }
    public string? companyId { get; set; }
    public int contact_id { get; set; }
}

public class BulkUpdateInfoCustContactApproveStatusParameter
{
    public string operKey { get; set; }
    public string? companyId { get; set; }
    public string? oper_id { get; set; }
    public string contact_ids { get; set; }
    public bool approve_status { get; set; }
}

public class MallBindVipCardParameter
{
    public string operKey { get; set; }
    public string? companyId { get; set; }
    public int supcustId { get; set; }
    public int wx_user_id { get; set; }
    public int vip_level_id { get; set; }
}

public class GetVipLevelLogParameter
{
    public string operKey { get; set; }
    public string? companyId { get; set; }
    public int supcustId { get; set; }
    public int vip_card_id { get; set; }
}

public class MallVipLevelInsertLogParameter
{
    public string operKey { get; set; }
    public string? companyId { get; set; }
    public int vip_card_id { get; set; }
    public int client_id { get; set; }
    public int? wx_user_id { get; set; }
    public int? pre_level_id { get; set; }
    public int cur_level_id { get; set; }
    public string change_reason { get; set; }
    public int? oper_id { get; set; }
}

public class GetVipPointOfSheetResponse
{
    public string result { get; set; }
    public string msg { get; set; }
    public decimal total_points { get; set; }
    public decimal proportion { get; set; }
    public int? expire_days { get; set; }
    public List<VipPointDetail> details { get; set; }

    public class VipPointDetail
    {
        public int item_id { get; set; }
        public int row_index { get; set; }
        public decimal row_get { get; set; }

        public VipPointDetail (GetVipPointRequiredSheetRow row, int rowIndex, decimal row_get)
        {
            this.item_id = row.item_id;
            this.row_index = rowIndex;
            this.row_get = row_get;
        }
    }

    public GetVipPointOfSheetResponse(decimal total_points, List<VipPointDetail> details, decimal proportion = 1, int? expire_days = 0)
    {
        this.result = "OK"; this.msg = "";
        this.total_points = total_points;
        this.proportion = proportion;
        this.expire_days = expire_days;
        this.details = details;
    }
}
public class GetVipPointOfSheetParameter
{
    public string operKey { get; set; }
    public string? companyId { get; set; }
    public int supcustId { get; set; }
    public int wx_user_id { get; set; }
    public int? vip_class_id { get; set; }
    public string order_source { get; set; }
    public string? sheet_id { get; set; }
    public decimal paid_amount { get; set; }
    public decimal sub_amount { get; set; }
    public List<GetVipPointRequiredSheetRow> sheet_rows { get; set; }

    public MallGetVipStrategyBaseParameter ToParameterForGetVipStrategy()
    {
        var result = new MallGetVipStrategyBaseParameter();
        result.operKey = this.operKey;
        result.companyId = this.companyId;
        result.supcustId = this.supcustId;
        result.wx_user_id = this.wx_user_id;
        result.vip_class_id = this.vip_class_id;
        result.order_source = this.order_source;
        return result;
    }

    public GetVipPointOfSheetParameter()
    {
    }

    public GetVipPointOfSheetParameter(string operKey, int supcustId, int wx_user_id, string order_source, decimal paid_amount, decimal sub_amount, List<GetVipPointRequiredSheetRow> sheet_rows)
    {
        this.operKey = operKey;
        this.supcustId = supcustId;
        this.wx_user_id = wx_user_id;
        this.order_source = order_source;
        this.paid_amount = paid_amount;
        this.sub_amount = sub_amount;
        this.sheet_rows = sheet_rows;
    }


    public GetVipPointOfSheetParameter (SheetSale sheetSale)
    {
        int.TryParse(sheetSale.supcust_id, out var supcustId);
        int.TryParse(sheetSale.wx_user_id, out var wx_user_id);

        this.operKey = sheetSale.OperKey;
        this.companyId = sheetSale.company_id;
        this.supcustId = supcustId;
        this.wx_user_id = wx_user_id;
        this.order_source = sheetSale.order_source;
        this.paid_amount = sheetSale.paid_amount;
        this.sub_amount = sheetSale.total_amount;

        this.sheet_rows = [];
        foreach (var row in sheetSale.SheetRows)
        {
            this.sheet_rows.Add(new(row));
        }
    }
}
public class GetVipPointRequiredSheetRow
{
    public int? flow_id { get; set; }
    public int item_id { get; set; }
    public int? item_brand { get; set; }
    public string item_class { get; set; }

    public string? trade_type { get; set; }
    public string? promotion_id { get; set; }
    /// <summary>
    /// 促销类型，可能为source,gift(组合)/seckill(限时特价)/exchange,proof(兑奖)
    /// </summary>
    public string? promotion_type { get; set; }

    public string vip_is_redeem { get; set; }

    /// <summary>
    /// 实付金额
    /// </summary>
    public decimal total_price { get; set; }

    public decimal b_quantity { get; set; }
    public decimal m_quantity { get; set; }
    public decimal s_quantity { get; set; }

    public GetVipPointRequiredSheetRow()
    {
    }

    public GetVipPointRequiredSheetRow(int item_id, int? item_brand, string item_class, string trade_type, string promotion_id, string promotion_type, string vip_is_redeem, decimal total_price, decimal b_quantity, decimal m_quantity, decimal s_quantity)
    {
        this.item_id = item_id;
        this.item_brand = item_brand;
        this.item_class = item_class;
        this.trade_type = trade_type;
        this.promotion_id = promotion_id;
        this.promotion_type = promotion_type;
        this.vip_is_redeem = vip_is_redeem;
        this.total_price = total_price;
        this.b_quantity = b_quantity;
        this.m_quantity = m_quantity;
        this.s_quantity = s_quantity;
    }

    public GetVipPointRequiredSheetRow (SheetRowSale sheetRowSale)
    {
        if (int.TryParse(sheetRowSale.flow_id, out var flow_id))
            this.flow_id = flow_id;
        if (int.TryParse(sheetRowSale.item_id, out var item_id))
            this.item_id = item_id;
        if (int.TryParse(sheetRowSale.brand_id, out var brand_id))
            this.item_brand = brand_id;
        this.item_class = sheetRowSale.other_class;
        this.trade_type = sheetRowSale.trade_type;
        this.promotion_id = sheetRowSale.promotion_id;
        this.promotion_type = sheetRowSale.promotion_type;
        this.vip_is_redeem = sheetRowSale.vip_is_redeem;
        this.total_price = sheetRowSale.sub_amount;
        this.b_quantity = sheetRowSale.b_quantity;
        this.m_quantity = sheetRowSale.m_quantity;
        this.s_quantity = sheetRowSale.s_quantity;
    }
}

public class VipBalanceRecord
{
    public int flow_id { get; set; }
    public int vip_card_id { get; set; }
    public decimal point_avail_balance { get; set; }
}
public class VipDetailsForLog
{
    public int flow_id { get; set; }
    public decimal pend_points { get; set; }
    public decimal points_balance { get; set; }
}

public class MallGetVipStrategyBaseParameter
{
    public string operKey { get; set; }
    public string? companyId { get; set; }
    public int supcustId { get; set; }
    public int wx_user_id { get; set; }
    public int? vip_class_id { get; set; }
    public string order_source { get; set; }
}

public class MallGetVipStrategyDetailParameter : MallGetVipStrategyBaseParameter
{
    public int vip_level_id { get; set; }
    public int? vip_class_id { get; set; }
    public string retail_whole_flag { get; set; }
    public int? groups_id { get; set; }
    public int? ranks_id { get; set; }
    public int? regions_id { get; set; }
    public string other_region { get; set; }

}

public class MallVipPlanRedeemableParameter : MallGetVipStrategyBaseParameter
{
    public int branchID { get; set; }
    
    public int? vip_plan_id { get; set; }
    public bool mallAllowNegativeStockOrder { get; set; }
    public bool mallShowNegativeStockItem { get; set; }
    public string? frontBranchId { get; set; }
    public bool? frontBranchDiffClassValue { get; set; }
    public string mallOrManage { get; set; }
}

public class MallVipPointChangeLogParameter
{
    public string operKey { get; set; }
    public string? companyId { get; set; }
    public int supcustId { get; set; }
    public int vipCardId { get; set; }
    public string startDate  { get; set; }
    public string endDate { get; set; }
}

public class GetMallVipPlanPointParameter
{
    public string operKey { get; set; }
    public string? companyId { get; set; }
    public int vipPlanId { get; set; }
}

public class HandelExpirePointParameter
{
    public int companyId { get; set; }
    public int supcustId { get; set; }
    public int vipCardId { get; set; }

    public HandelExpirePointParameter()
    {
    }
    public HandelExpirePointParameter(int companyId, int supcustId, int vipCardId)
    {
  
        this.companyId = companyId;
        this.supcustId = supcustId;
        this.vipCardId = vipCardId;
    }
}

public class GetMallNoticeListParameter
{
    public string operKey { get; set; }
   
    public string? companyId { get; set; }
    public int wx_user_id { get; set; }
    public int supcust_id { get; set; }
    public string group_id { get; set; }
    public string rank_id { get; set; }
    public string other_region { get; set; }
    public int? pageSize { get; set; }
    public int? pageNo { get; set; }
    public string viewRange { get; set; }
}

public class InsertMallNoticeCloseParameter
{
    public string operKey { get; set; }
    public string? companyId { get; set; }
    public List<int> notice_ids { get; set; }
    public int supcust_id { get; set; }
    public int wx_user_id { get; set; }
}