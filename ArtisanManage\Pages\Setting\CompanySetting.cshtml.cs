using ArtisanManage.Models;
using ArtisanManage.Services;
using ArtisanManage.Services.Helpers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Primitives;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using NLog.Web.LayoutRenderers;
using System.Net.Http;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using System.Globalization;
using Org.BouncyCastle.Asn1.Ocsp;
using Microsoft.CodeAnalysis.Elfie.Extensions;
using Microsoft.AspNetCore.Http.HttpResults;
using System.ComponentModel.Design;

namespace ArtisanManage
{
    public class CompanySettingModel : PageBaseModel
    {
        public string operKey;


        public List<KeyValuePair<string, string>> costPriceTypeOptions = new List<KeyValuePair<string, string>>
        {
            new KeyValuePair<string, string>("3", "预设进价"),
            new KeyValuePair<string, string>("2", "加权平均价"),
            new KeyValuePair<string, string>("1", "预设成本"),
            new KeyValuePair<string, string>("4", "最近平均进价")
        };
        public List<KeyValuePair<string, string>> batchTypeOptions = new List<KeyValuePair<string, string>>
        {
            new KeyValuePair<string, string>("2", "批次"),
            new KeyValuePair<string, string>("1", "严格产期"),
            new KeyValuePair<string, string>("0", "非严格产期"),
            new KeyValuePair<string, string>("-1", "无")
        };
        public List<KeyValuePair<string, string>> recentPriceTimeOptions = new List<KeyValuePair<string, string>>
        {
            new KeyValuePair<string, string>("3", "3次"),
            new KeyValuePair<string, string>("2", "2次"),
            new KeyValuePair<string, string>("1", "1次")
        };

        public List<KeyValuePair<string, string>> saleSheetDefaultUnitOptions = new List<KeyValuePair<string, string>>
        {
            new KeyValuePair<string, string>("b", "大单位"),
            new KeyValuePair<string, string>("s", "小单位"),
            new KeyValuePair<string, string>("m", "中单位")
        };
        public List<KeyValuePair<string, string>> moveSheetDefaultUnitOptions = new List<KeyValuePair<string, string>>
        {
            new KeyValuePair<string, string>("b", "大单位"),
            new KeyValuePair<string, string>("s", "小单位"),
            new KeyValuePair<string, string>("m", "中单位")
        };
        public List<KeyValuePair<string, string>> buySheetDefaultUnitOptions = new List<KeyValuePair<string, string>>
        {
            new KeyValuePair<string, string>("b", "大单位"),
            new KeyValuePair<string, string>("s", "小单位"),
            new KeyValuePair<string, string>("m", "中单位")
        };

        public List<KeyValuePair<string, string>> sheetShowBarcodeStyleOptions = new List<KeyValuePair<string, string>>
        {
            new KeyValuePair<string, string>("1", "实际条码"),
            new KeyValuePair<string, string>("2", "小单位条码")
        };
        public List<KeyValuePair<string, string>> orderItemSheetOptions = new List<KeyValuePair<string, string>>
        {
            new KeyValuePair<string, string>("prepaySub", "先收款后发货"),
            new KeyValuePair<string, string>("all", "两者皆可"),
            new KeyValuePair<string, string>("noPrepaySub", "先发货后收款")
        };
        public List<KeyValuePair<string, string>> appPrtItemNamePosOptions = new List<KeyValuePair<string, string>>
        {
            new KeyValuePair<string, string>("name", "品名独占一行"),
            new KeyValuePair<string, string>("nameCode", "品名 条码在一行"),
            new KeyValuePair<string, string>("codeName", "条码 品名在一行"),
            new KeyValuePair<string, string>("nameQty", "品名和数量价格在一行"),
            new KeyValuePair<string, string>("auto", "根据品名长短自适应"),

        };


        public List<KeyValuePair<string, string>> sheetAmountRoundOptions = new List<KeyValuePair<string, string>>
        {
            new KeyValuePair<string, string>("1", "元"),
            new KeyValuePair<string, string>("2", "角"),
            new KeyValuePair<string, string>("3", "分")
        };

        public List<KeyValuePair<string, string>> showBarcodeOptions = new List<KeyValuePair<string, string>>
        {
            new KeyValuePair<string, string>("notshow", "不展示条码"),
            new KeyValuePair<string, string>("sbarcode", "小单位条码"),
            new KeyValuePair<string, string>("bbarcode", "大单位条码"),
            new KeyValuePair<string, string>("mbarcode", "中单位条码"),
            new KeyValuePair<string, string>("ubarcode", "实际单位条码")

        };

        public List<KeyValuePair<string, string>> appSheetItemShowStyleOptions = new List<KeyValuePair<string, string>>
        {
            new KeyValuePair<string, string>("compact", "紧凑"),
            new KeyValuePair<string, string>("loose", "宽松")
        };

        public List<KeyValuePair<string, string>> oftenItemMonthsOptions = new List<KeyValuePair<string, string>>
        {
            new KeyValuePair<string, string>("3", "3个月"),
            new KeyValuePair<string, string>("6", "6个月"),
            new KeyValuePair<string, string>("12", "12个月"),
            new KeyValuePair<string, string>("18", "18个月"),
            new KeyValuePair<string, string>("24", "24个月"),
            new KeyValuePair<string, string>("48", "48个月"),

        };
        public List<KeyValuePair<string, string>> CheckAccountPayWayOptions = new List<KeyValuePair<string, string>>
        {
            new KeyValuePair<string, string>("summary", "收款按单据支付方式汇总"),
            new KeyValuePair<string, string>("self", "单独收款"),

        };
        public CompanySettingModel(CMySbCommand cmd) : base(MenuId.companySetting)
        {
            this.cmd = cmd;
        }
        public string appOpenBranchsForSheet;
        public string company_cachet;
        public string costPriceType;
        public string recentPriceTime;
        public string sheetShowBarcodeStyle;
        public string orderItemSheet;
        public string appPrtItemNamePos;

        public string saleSheetDefaultUnit;
        public string moveSheetDefaultUnit;
        public string buySheetDefaultUnit;
        public string showBarcode;
        public string sheetShowPriceList;
        public string appSheetShowItemSpec;
        public string appSheetQtyMerge;
        public string appSheetUseAssistQty;
        public string show_DH_CL_JH_inOftenItem;
        public string appDbNoStockSave;
        public string appXsNoStockSave;
        public string appShareRetailPrice;
        public string exactItemNameSearch;
        public string separateDisplayAndSale;
        public string noStockAttrSplitShow;
        public string saleNeedMarkIOU;
        public string showNoProduceDate;
        //  public string noCountArrearsDayFromReturnOrder;
        public string appSheetItemShowStyle;
        public string oftenItemMonths;
        public string sheetAmountRound;
        public string companyName;
        public string contactTel;
        public string companyAddress;
        public string receiptTail;
        public string companyNamesToPrint;
        public string receiptShowSmallUnitPrice;
        public string receiptShowSmallUnitNumber;
        public string receiptReturnItemsAtTail;//
        public string receiptShowSellerInfo;
        public string receiptShowSenderInfo;
        public string receiptShowApproveTime;   //小票打印审核时间
        public string receiptShowClientTel;     //小票打印客户电话 
        public string receiptShowBranchInfo;
        public string receiptShowClientAddr;
        public string receiptShowItemModel;

        public string lineBetweenRows;
        public string lineBetweenRowsMove;
        public string lineBetweenRowsSale;
        public string lineBetweenAcctRows;


        public string receiptShowItemRealPrice;
        public string receiptShowItemSubAmount;
        public string receiptShowItemRetailPrice;

        public string receiptShowValidDays;
        public string receiptShowVirtualProduceDate;
        public string receiptShowArrearsBalance;
        public string receiptShowBeforeArrears;
        public string receiptShowPrepayBalance;
        public string receiptShowOrderItemsBalance;
        public string receiptShowSheetOrderItemsBalance;

        public string appPrtMoveItemNamePos;
        public string appPrtMovePrice;
        public string appPrintLeftAmt;
        public string appPrtMoveUnitRelation;
        public string openPlaceholderSheet;
        public string showNegativeStock;
        public string needBatchOnReview;
        public string flowExistMove;
        public string reviewOrderBeforeAssignVan;
        public string ignorePayFailedSheetOnAssignVan;
        public string ignorePayFailedSheetOnOrderToSale;
        public string canMultiBranchAssign;
        public string flowExistMoveBack;
        public string printCheckWhole;
        public string printCheckOpenStock;
        public string reservePrintCountOnRedChange;
        public string allowRepeatedBackBranch;
        public string visitDistance;
        public string limitVisitDistance;
        public string autoVisitEnd;
        public string autoVisitEndDistance;
        public string positionPeriodStart;
        public string positionPeriodEnd;
        public string minVisitDuration;
        public string forceVisit;
        public string forceRemark;
        public string forceDoorPic;
        public string forceDisplayPic;
        public string forceBrief;
        public string displayPicCount;
        public string billHeadImage;
        public string billTailImage;
        public string billTailImage2;
        public string printBillTailImage;
        public string printBillHeadImage;
        public string feeOutSubForKS;
        //public string mallBranch;
        public string orderBranch;
        public string branchForReturn;
        public string branchForOrderReturn;
        public string backBranchPositionType;
        public string backBranchType;
        public string defaultBranchPositionType;
        public string openTicketAccessSys;
        public string ticketAccessSysAccount;
        public string ticketAccessSysPwd;
        public string ticketAccessSysLisense;
        public string ticketAccessSysKey;
        public string newSheetAfterApprove;
        public string newSheetAfterSave;
        public string newCheckAccountShowLineAmountDetail;

        public string newSheetAfterSaleOrderSheetApprove;
        public string newSheetAfterSaleOrderSheetSave;


        public string appPriceBeforeChange;
        public string saleSheetNeedSender;
        public string saleOrderSheetNeedSender;
        public string checkAccountBySender;

        public string saleSheetNoSameAsOrder;
        public string showSonItems;
        public string happenTimeOnSave;
        public string requerySheetsOnTabClick;
        public string appUseVirtualProduceDate;
        public string appSaleUseSender;
        public string appSaleUseSn;

        public string printSheetPrintTime;
        public string printAccountSheetDate;
        public string printNoWrapAfterBarcode;
        public string printBiggerSubAmount;
        public string printBiggerPayway;
        public string useAccounting="false";
        public string autoCreateVoucher="false";


        public string clientBossNecessary;
        public string supcustNoSerial;
        public string clientMobileNecessary;

        public string doorPicNecessary;
        public string clientLocationNecessary;

        public string clientRegionNecessary;
        public string clientGroupNecessary;
        public string clientLevelNecessary;
        public string clientRelateSeller;
        public string validDayType;
        public string unitPriceRelated;
        public string dispTemplateNecessary;
        public string imageClarityLevel;

        public string isDistinctStock;
        public string attrName;
        public string isCombinePrint;
        public string isRememberSonItemPrice;


        public string checkAccountPayWayType;
        public string loadVirtualProduceDate;
        public string batchType = "0";
        // public string qrCodeExpiresTime;
        public string UrlRandom = CPubVars.GetDateText(DateTime.Now).Replace("-", "").Replace(":", "").Replace(" ", "");
        public List<ExpandoObject> BranchesList;
        public List<ExpandoObject> BranchPositionTypeList;
        public List<KeyValuePair<string, string>> BranchTypeList = new List<KeyValuePair<string, string>>
        {
            new KeyValuePair<string, string>("store", "仓库"),
            new KeyValuePair<string, string>("truck", "车辆"),
            new KeyValuePair<string, string>("approaching", "临期"),
            new KeyValuePair<string, string>("", "无")
        };


        public async Task OnGet()
        {
            operKey = HttpContext.Request.Query["operKey"].ToString();
            /*  cmd.CommandText = $"select cost_price_type from company_setting where company_id = {Token.CompanyID}";
             priceType = (int)( cmd.ExecuteScalar());
             cmd.CommandText = $"select barcode_type from company_setting where company_id = {Token.CompanyID}";
             barcodeType = (int)(cmd.ExecuteScalar());
             */
            SQLQueue QQ = new SQLQueue(cmd);
            var sql =
                $"select cost_price_type, company_cachet,barcode_type,setting from company_setting where company_id = {Token.CompanyID}";
            QQ.Enqueue("setting", sql);

            sql = $"select branch_id,branch_name from info_branch where company_id = {Token.CompanyID}";
            QQ.Enqueue("branches", sql);
            sql = $"select attr_id,attr_name attrname,distinct_stock isdistinctstock,sale_combine_print iscombineprint,remember_price isRememberSonItemPrice from info_attribute where company_id={Token.CompanyID}  ORDER BY attr_id desc limit 1";
            QQ.Enqueue("attr", sql);
            sql = $"select type_id,type_name from info_branch_position_type where company_id = {Token.CompanyID} union select -1 as type_id,'无' as type_name";
            QQ.Enqueue("branchPositionTypes", sql);
            //= await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            // sql = $@"";
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                string tbl = QQ.Dequeue();
                if (tbl == "setting")
                {
                    dynamic data = CDbDealer.Get1RecordFromDr(dr, false);
                    if (data != null)
                    {
                        company_cachet = data.company_cachet;
                        costPriceType = data.cost_price_type;

                        sheetShowBarcodeStyle = data.barcode_type;

                        string jsonSetting = data.setting;
                        if (jsonSetting.IsValid())
                        {
                            dynamic setting = JsonConvert.DeserializeObject((jsonSetting));
                            costPriceType = setting.costPriceType;
                            recentPriceTime = setting.recentPriceTime;
                            validDayType = setting.validDayType;

                            sheetShowBarcodeStyle = setting.sheetShowBarcodeStyle;
                            orderItemSheet = setting.orderItemSheet;
                            appPrtItemNamePos = setting.appPrtItemNamePos;
                            appPrtMoveItemNamePos = setting.appPrtMoveItemNamePos;
                            saleSheetDefaultUnit = setting.saleSheetDefaultUnit;
                            moveSheetDefaultUnit = setting.moveSheetDefaultUnit;
                            buySheetDefaultUnit = setting.buySheetDefaultUnit;
                            showBarcode = setting.showBarcode;
                            checkAccountPayWayType = setting.checkAccountPayWayType;
                            loadVirtualProduceDate = setting.loadVirtualProduceDate;
                            appSheetItemShowStyle = setting.appSheetItemShowStyle;
                            oftenItemMonths = setting.oftenItemMonths;
                            appSheetShowItemSpec = setting.appSheetShowItemSpec;
                            appSheetShowItemSpec = appSheetShowItemSpec?.ToLower();
                            sheetShowPriceList = setting.sheetShowPriceList;
                            sheetShowPriceList = sheetShowPriceList?.ToLower();

                            separateDisplayAndSale = setting.separateDisplayAndSale;
                            separateDisplayAndSale = separateDisplayAndSale?.ToLower();

                            noStockAttrSplitShow = setting.noStockAttrSplitShow;
                            noStockAttrSplitShow = noStockAttrSplitShow?.ToLower();

                            saleNeedMarkIOU = setting.saleNeedMarkIOU;
                            saleNeedMarkIOU = saleNeedMarkIOU?.ToLower();

                            showNoProduceDate = setting.showNoProduceDate;
                            showNoProduceDate = showNoProduceDate?.ToLower();
                            // noCountArrearsDayFromReturnOrder = setting.noCountArrearsDayFromReturnOrder;
                            // noCountArrearsDayFromReturnOrder = noCountArrearsDayFromReturnOrder?.ToLower();


                            if (setting.appSheetQtyMerge != null)
                            {
                                appSheetQtyMerge = setting.appSheetQtyMerge;
                                appSheetQtyMerge = appSheetQtyMerge?.ToLower();
                            }
                            if (setting.appSheetUseAssistQty != null)
                            {
                                appSheetUseAssistQty = setting.appSheetUseAssistQty;
                                appSheetUseAssistQty = appSheetUseAssistQty?.ToLower();
                            }
                            if (setting.show_DH_CL_JH_inOftenItem != null)
                            {
                                show_DH_CL_JH_inOftenItem = setting.show_DH_CL_JH_inOftenItem;
                                show_DH_CL_JH_inOftenItem = show_DH_CL_JH_inOftenItem?.ToLower();
                            }

                            if (setting.appDbNoStockSave != null)
                            {
                                appDbNoStockSave = setting.appDbNoStockSave;
                                appDbNoStockSave = appDbNoStockSave?.ToLower();
                            }
                            if (setting.appXsNoStockSave != null)
                            {
                                appXsNoStockSave = setting.appXsNoStockSave;
                                appXsNoStockSave = appXsNoStockSave?.ToLower();
                            }
                            if (setting.appShareRetailPrice != null)
                            {
                                appShareRetailPrice = setting.appShareRetailPrice;
                                appShareRetailPrice = appShareRetailPrice?.ToLower();
                            }

                            if (setting.exactItemNameSearch != null)
                            {
                                exactItemNameSearch = setting.exactItemNameSearch;
                                exactItemNameSearch = exactItemNameSearch?.ToLower();
                            }


                            sheetAmountRound = setting.sheetAmountRound;
                            receiptTail = setting.receiptTail;
                            companyNamesToPrint = setting.companyNamesToPrint;
                            companyName = setting.companyName;
                            // qrCodeExpiresTime = setting.qrCodeExpiresTime;
                            companyAddress = setting.companyAddress;
                            contactTel = setting.contactTel;
                            visitDistance = setting.visitDistance;
                            limitVisitDistance = setting.limitVisitDistance;
                            limitVisitDistance = limitVisitDistance?.ToLower();
                            autoVisitEnd = setting.autoVisitEnd;
                            autoVisitEnd = autoVisitEnd?.ToLower();
                            autoVisitEndDistance = setting.autoVisitEndDistance;
                            forceVisit = setting.forceVisit;
                            forceVisit = forceVisit?.ToLower();

                            lineBetweenRows = setting.lineBetweenRows;
                            lineBetweenRows = lineBetweenRows?.ToLower();
                            lineBetweenRowsMove = setting.lineBetweenRowsMove;
                            lineBetweenRowsMove = lineBetweenRowsMove?.ToLower();
                            lineBetweenRowsSale = setting.lineBetweenRowsSale;
                            lineBetweenRowsSale = lineBetweenRowsSale?.ToLower();
                            //if (setting.lineBetweenRowsMove == null) lineBetweenRowsMove = lineBetweenRows;
                            if (setting.lineBetweenRowsSale == null) lineBetweenRowsSale = lineBetweenRows;
                            lineBetweenAcctRows = setting.lineBetweenAcctRows;
                            lineBetweenAcctRows = lineBetweenAcctRows?.ToLower();

                            imageClarityLevel = setting.imageClarityLevel;
                            imageClarityLevel = imageClarityLevel?.ToLower();

                            forceRemark = setting.forceRemark;
                            forceRemark = forceRemark?.ToLower();
                            forceDoorPic = setting.forceDoorPic;
                            forceDoorPic = forceDoorPic?.ToLower();
                            forceDisplayPic = setting.forceDisplayPic;
                            forceDisplayPic = forceDisplayPic?.ToLower();
                            displayPicCount = setting.displayPicCount;
                            positionPeriodStart = setting.positionPeriodStart;
                            positionPeriodEnd = setting.positionPeriodEnd;
                            minVisitDuration = setting.minVisitDuration;
                            receiptShowSmallUnitPrice = setting.receiptShowSmallUnitPrice;
                            receiptShowSmallUnitPrice = receiptShowSmallUnitPrice?.ToLower();
                            receiptShowSmallUnitNumber = setting.receiptShowSmallUnitNumber;
                            receiptShowSmallUnitNumber = receiptShowSmallUnitNumber?.ToLower();
                            //转小写
                            receiptReturnItemsAtTail = setting.receiptReturnItemsAtTail;
                            receiptReturnItemsAtTail = receiptReturnItemsAtTail?.ToLower();
                            receiptShowSellerInfo = setting.receiptShowSellerInfo;
                            receiptShowSellerInfo = receiptShowSellerInfo?.ToLower();
                            receiptShowSenderInfo = setting.receiptShowSenderInfo;
                            receiptShowSenderInfo = receiptShowSenderInfo?.ToLower();
                            receiptShowApproveTime = setting.receiptShowApproveTime;
                            receiptShowApproveTime = receiptShowApproveTime?.ToLower();
                            receiptShowClientTel = setting.receiptShowClientTel;
                            receiptShowClientTel = receiptShowClientTel?.ToLower();
                            receiptShowBranchInfo = setting.receiptShowBranchInfo;
                            receiptShowBranchInfo = receiptShowBranchInfo?.ToLower();
                            receiptShowClientAddr = setting.receiptShowClientAddr;
                            receiptShowClientAddr = receiptShowClientAddr?.ToLower();

                            billHeadImage = setting.billHeadImage;
                            billTailImage = setting.billTailImage;
                            billTailImage2 = setting.billTailImage2;

                            receiptShowItemModel = setting.receiptShowItemModel;
                            receiptShowItemModel = receiptShowItemModel?.ToLower();

                            receiptShowItemRealPrice = setting.receiptShowItemRealPrice;
                            receiptShowItemRealPrice = receiptShowItemRealPrice?.ToLower();

                            receiptShowItemSubAmount = setting.receiptShowItemSubAmount;
                            receiptShowItemSubAmount = receiptShowItemSubAmount?.ToLower();

                            receiptShowItemRetailPrice = setting.receiptShowItemRetailPrice;
                            receiptShowItemRetailPrice = receiptShowItemRetailPrice?.ToLower();



                            receiptShowValidDays = setting.receiptShowValidDays;
                            receiptShowValidDays = receiptShowValidDays?.ToLower();

                            receiptShowVirtualProduceDate = setting.receiptShowVirtualProduceDate;
                            receiptShowVirtualProduceDate = receiptShowVirtualProduceDate?.ToLower();

                            receiptShowArrearsBalance = setting.receiptShowArrearsBalance;
                            receiptShowArrearsBalance = receiptShowArrearsBalance?.ToLower();

                            receiptShowBeforeArrears = setting.receiptShowBeforeArrears;
                            receiptShowBeforeArrears = receiptShowBeforeArrears?.ToLower();

                            receiptShowPrepayBalance = setting.receiptShowPrepayBalance;
                            receiptShowPrepayBalance = receiptShowPrepayBalance?.ToLower();
                            appPrtMovePrice = setting.appPrtMovePrice;
                            appPrtMovePrice = appPrtMovePrice?.ToLower();
                            appPrintLeftAmt = setting.appPrintLeftAmt;
                            appPrintLeftAmt = appPrintLeftAmt?.ToLower();

                            appPrtMoveUnitRelation = setting.appPrtMoveUnitRelation;
                            appPrtMoveUnitRelation = appPrtMoveUnitRelation?.ToLower();


                            receiptShowOrderItemsBalance = setting.receiptShowOrderItemsBalance;
                            receiptShowOrderItemsBalance = receiptShowOrderItemsBalance?.ToLower();

                            receiptShowSheetOrderItemsBalance = setting.receiptShowSheetOrderItemsBalance;
                            receiptShowSheetOrderItemsBalance = receiptShowSheetOrderItemsBalance?.ToLower();
                            printAccountSheetDate = setting.printAccountSheetDate;
                            printAccountSheetDate = printAccountSheetDate?.ToLower();
                            printBillHeadImage = setting.printBillHeadImage;
                            printBillHeadImage = printBillHeadImage?.ToLower();
                            printBillTailImage = setting.printBillTailImage;
                            printBillTailImage = printBillTailImage?.ToLower();

                            printSheetPrintTime = setting.printSheetPrintTime;
                            printSheetPrintTime = printSheetPrintTime?.ToLower();
                            printNoWrapAfterBarcode = setting.printNoWrapAfterBarcode;
                            printNoWrapAfterBarcode = printNoWrapAfterBarcode?.ToLower();
                            printBiggerSubAmount = setting.printBiggerSubAmount;
                            printBiggerSubAmount = printBiggerSubAmount?.ToLower();
                            printBiggerPayway = setting.printBiggerPayway;
                            printBiggerPayway = printBiggerPayway?.ToLower();

                            openPlaceholderSheet = setting.openPlaceholderSheet?.ToString().ToLower();
                            showNegativeStock = setting.showNegativeStock?.ToString().ToLower();
                            needBatchOnReview = setting.needBatchOnReview?.ToString().ToLower();
                            flowExistMove = setting.flowExistMove?.ToString().ToLower();
                            reviewOrderBeforeAssignVan = setting.reviewOrderBeforeAssignVan?.ToString().ToLower();
                            ignorePayFailedSheetOnAssignVan = setting.ignorePayFailedSheetOnAssignVan?.ToString().ToLower();
                            ignorePayFailedSheetOnOrderToSale = setting.ignorePayFailedSheetOnOrderToSale?.ToString().ToLower();
                            canMultiBranchAssign = setting.canMultiBranchAssign?.ToString().ToLower();
                            flowExistMoveBack = setting.flowExistMoveBack?.ToString().ToLower();
                            printCheckWhole = setting.printCheckWhole?.ToString().ToLower();
                            printCheckOpenStock = setting.printCheckOpenStock?.ToString().ToLower();
                            reservePrintCountOnRedChange = setting.reservePrintCountOnRedChange?.ToString().ToLower();
                            allowRepeatedBackBranch = setting.allowRepeatedBackBranch?.ToString().ToLower();
                            feeOutSubForKS = setting.feeOutSubForKS?.ToString().ToLower();
                            // mallBranch = setting.mallBranch?.ToString().ToLower();
                            orderBranch = setting.orderBranch?.ToString().ToLower();
                            branchForReturn = setting.branchForReturn?.ToString().ToLower();
                            branchForOrderReturn = setting.branchForOrderReturn?.ToString().ToLower();
                            backBranchPositionType = setting.backBranchPositionType?.ToString();
                            backBranchType = setting.backBranchType?.ToString();
                            defaultBranchPositionType = setting.defaultBranchPositionType?.ToString();

                            openTicketAccessSys = setting.openTicketAccessSys?.ToString().ToLower();
                            ticketAccessSysAccount = setting.ticketAccessSysAccount;
                            ticketAccessSysPwd = setting.ticketAccessSysPwd;
                            ticketAccessSysKey = setting.ticketAccessSysKey;
                            ticketAccessSysLisense = setting.ticketAccessSysLisense;
                            newSheetAfterApprove = setting.newSheetAfterApprove?.ToString().ToLower();
                            newSheetAfterSave = setting.newSheetAfterSave?.ToString().ToLower();
                            newCheckAccountShowLineAmountDetail = setting.newCheckAccountShowLineAmountDetail?.ToString().ToLower();
                            newSheetAfterSaleOrderSheetSave = setting.newSheetAfterSaleOrderSheetSave?.ToString().ToLower();
                            newSheetAfterSaleOrderSheetApprove = setting.newSheetAfterSaleOrderSheetApprove?.ToString().ToLower();
                            appPriceBeforeChange = setting.appPriceBeforeChange?.ToString().ToLower();
                            appOpenBranchsForSheet = setting.appOpenBranchsForSheet?.ToString().ToLower();
                            saleSheetNeedSender = setting.saleSheetNeedSender?.ToString().ToLower();
                            saleOrderSheetNeedSender = setting.saleOrderSheetNeedSender?.ToString().ToLower();
                            checkAccountBySender = setting.checkAccountBySender?.ToString().ToLower();
                            saleSheetNoSameAsOrder = setting.saleSheetNoSameAsOrder?.ToString().ToLower();
                            showSonItems = setting.showSonItems?.ToString().ToLower();
                            happenTimeOnSave = setting.happenTimeOnSave?.ToString().ToLower();
                            requerySheetsOnTabClick = setting.requerySheetsOnTabClick?.ToString().ToLower();
                            appUseVirtualProduceDate = setting.appUseVirtualProduceDate?.ToString().ToLower();
                            appSaleUseSender = setting.appSaleUseSender?.ToString().ToLower();
                            appSaleUseSn = setting.appSaleUseSn?.ToString().ToLower();


                            clientBossNecessary = setting.clientBossNecessary;
                            clientBossNecessary = clientBossNecessary?.ToLower();

                            supcustNoSerial = setting.supcustNoSerial;
                            supcustNoSerial = supcustNoSerial?.ToLower();

                            clientMobileNecessary = setting.clientMobileNecessary;
                            clientMobileNecessary = clientMobileNecessary?.ToLower();

                            doorPicNecessary = setting.doorPicNecessary;
                            doorPicNecessary = doorPicNecessary?.ToLower();

                            clientLocationNecessary = setting.clientLocationNecessary;
                            clientLocationNecessary = clientLocationNecessary?.ToLower();

                            clientRegionNecessary = setting.clientRegionNecessary;
                            clientRegionNecessary = clientRegionNecessary?.ToLower();

                            clientLevelNecessary = setting.clientLevelNecessary;
                            clientLevelNecessary = clientLevelNecessary?.ToLower();

                            clientGroupNecessary = setting.clientGroupNecessary;
                            clientGroupNecessary = clientGroupNecessary?.ToLower();

                            clientRelateSeller = setting.clientRelateSeller;
                            clientRelateSeller = clientRelateSeller?.ToLower();

                            unitPriceRelated = setting.unitPriceRelated;
                            unitPriceRelated = unitPriceRelated?.ToLower();



                            dispTemplateNecessary = setting.dispTemplateNecessary;
                            dispTemplateNecessary = dispTemplateNecessary?.ToLower();


                            if (setting.useAccounting != null)
                            {
                                useAccounting = setting.useAccounting?.ToString().ToLower();
                            }
                            if (setting.autoCreateVoucher != null)
                            {
                                autoCreateVoucher = setting.autoCreateVoucher?.ToString().ToLower();
                            }
                            batchType = setting.batchType != null ? setting.batchType : "";
                            loadVirtualProduceDate = setting.loadVirtualProduceDate?.ToString().ToLower();
                            //if (setting.useBusinessPeriod != null)
                            //{
                            //    useBusinessPeriod = setting.useBusinessPeriod?.ToString();
                            //}

                        }
                    }
                }
                else if (tbl == "branches")
                {
                    BranchesList = CDbDealer.GetRecordsFromDr(dr, false);
                    if (BranchesList.Count > 0)
                    {
                        dynamic newBranch = new { branch_id = "", branch_name = "无" };
                        BranchesList.Add(JsonConvert.DeserializeObject<ExpandoObject>(JsonConvert.SerializeObject(newBranch)));
                    }

                }
                else if (tbl == "branchPositionTypes")
                {
                    BranchPositionTypeList = CDbDealer.GetRecordsFromDr(dr, false);

                }
                else if (tbl == "attr")
                {
                    dynamic rec = CDbDealer.Get1RecordFromDr(dr, false);
                    if (rec != null)
                    {
                        isDistinctStock = rec.isdistinctstock;
                        isDistinctStock = isDistinctStock?.ToLower();

                        attrName = rec.attrname;
                        attrName = attrName?.ToLower();

                        isCombinePrint = rec.iscombineprint;
                        isRememberSonItemPrice = rec.isremembersonitemprice;
                        isCombinePrint = isCombinePrint?.ToLower();
                        isRememberSonItemPrice = isRememberSonItemPrice?.ToLower();
                    }

                }
            }
            QQ.Clear();
            if (!positionPeriodStart.IsValid())
            {
                positionPeriodStart = "06:30";
            }

            if (!positionPeriodEnd.IsValid())
            {
                positionPeriodEnd = "20:30";
            }
        }
    }

    [Route("api/[controller]/[action]")]
    public class CompanySettingController : YjController
    {
        CMySbCommand cmd;

        private readonly IHttpClientFactory _httpClientFactory;
        public CompanySettingController(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {

            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }

        [HttpPost]
        public async Task<JsonResult> SendClearDataMessage(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string companySQL = @$"SELECT boss_mobile,create_time,company_status,wos.staff_mobile,wos.staff_name from g_company
                            left join work_order_staff wos on wos.id =  g_company.sale_staff_id
                            where company_id = {companyID}";
            dynamic data = await CDbDealer.Get1RecordFromSQLAsync(companySQL, cmd);
            DateTime createDateTime = DateTime.ParseExact(data.create_time, "yyyy-MM-dd", null);
            bool isRegistLessOneMonth = DateTime.Now < createDateTime.AddMonths(1);
            string verifyCode = ((int)(new Random().NextDouble() * 10000)).ToString().PadLeft(4, '0');
            bool needNotifyBoss = false;
            if (isRegistLessOneMonth || data.company_status == "trial")
            {
                await SmsSender.SendVeriCodeSMS(_httpClientFactory, data.staff_mobile, verifyCode);
                RedisHelper.Set("clear_data_key_" + companyID, verifyCode, 300);
                Console.WriteLine("员工：" + verifyCode);

            }
            else
            {
                string bossVerifyCode = ((int)(new Random().NextDouble() * 10000)).ToString().PadLeft(4, '0');
                string staffVerifyCode = ((int)(new Random().NextDouble() * 10000)).ToString().PadLeft(4, '0');
                await SmsSender.SendVeriCodeSMS(_httpClientFactory, data.boss_mobile, bossVerifyCode);
                await SmsSender.SendVeriCodeSMS(_httpClientFactory, data.staff_mobile, staffVerifyCode);
                Console.WriteLine("老板：" + bossVerifyCode);
                Console.WriteLine("员工：" + staffVerifyCode);
                needNotifyBoss = true;
                RedisHelper.Set("clear_data_key_" + companyID, staffVerifyCode + "_" + bossVerifyCode, 300);
            }
            //181 1520 1784
            string bossMobile = (string)data.boss_mobile;
            string desensitizeBossMobile = bossMobile.Substring(0, 3) + "****" + bossMobile.Substring(7, 4);
            return Json(new { result = "OK", staff_name = data.staff_name, staff_mobile = data.staff_mobile, boss_mobile = desensitizeBossMobile, needNotifyBoss });

        }
        [HttpPost]
        public async Task<JsonResult> ClearData(string operKey, string bossVerifyCode, string staffVerifyCode, string clearType)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            string verifyCode = "";
            if (string.IsNullOrEmpty(staffVerifyCode))
            {
                return Json(new { result = "Error", msg = "代理商验证码不能为空" });
            }
            verifyCode += staffVerifyCode;
            if (!string.IsNullOrEmpty(bossVerifyCode))
            {
                verifyCode += "_" + bossVerifyCode;
            }
            string actualVerifyCode = RedisHelper.Get("clear_data_key_" + companyID);
            if (string.IsNullOrEmpty(actualVerifyCode))
            {
                return Json(new { result = "Error", msg = "操作已过期，重新验证" });
            }
            if (verifyCode == actualVerifyCode)
            {
                string companySQL = @$"SELECT company_name from g_company where company_id = {companyID}";
                dynamic data = await CDbDealer.Get1RecordFromSQLAsync(companySQL, cmd);
                cmd.oper_id = operID;
                cmd.CommandText = @$"SELECT yj_clearcompanydata({companyID},'{data.company_name}','auto','{clearType}')";
                object ov = await cmd.ExecuteScalarAsync();
                if (ov.ToString() == "OK")
                {
                    MyLogger.LogMsg($"{operID} 清空了 {clearType}", companyID);
                    return Json(new { result = "OK", msg = "数据已清空" });
                }
                else
                {
                    return Json(new { result = "Error", msg = ov.ToString() });
                }
            }
            return Json(new { result = "Error", msg = "验证失败！" });

        }
        [HttpPost]
        public async Task<JsonResult> ImportBillImage(string type)
        {
            var result = "OK";
            var msg = "操作成功";
            //try
            //{
            var file = Request.Form.Files[0];
            byte[] buffer = new byte[file.Length];
            string fileName = "";
            using (MemoryStream f = new MemoryStream(buffer))
            {
                await file.CopyToAsync(f);
                var uploadDir = $"uploads/{DateTime.Today:yyyyMM}/";
                fileName = $"cachet_{Token.CompanyID}_{type}.bmp";
                await ImageHelper.To1bpp(_httpClientFactory, f, uploadDir + fileName);
                f.Close();
            }

            var cachet = $"/{DateTime.Today:yyyyMM}/{fileName}";

            cmd.CommandText = $"insert into company_setting (company_id,company_cachet) values ({Token.CompanyID},'{cachet}') on conflict(company_id) do update set company_cachet='{cachet}'";
            if (type == "headImage")
            {
                cmd.CommandText += @$",setting=company_setting.setting::jsonb||'{{""billHeadImage"":""{cachet}""}}'::jsonb ";
            }
            if (type == "tailImage")
            {
                cmd.CommandText += @$",setting=company_setting.setting::jsonb||'{{""billTailImage"":""{cachet}""}}'::jsonb ";
            }
            if (type == "tailImage2")
            {
                cmd.CommandText += @$",setting=company_setting.setting::jsonb||'{{""billTailImage2"":""{cachet}""}}'::jsonb ";
            }
            cmd.CommandText += ";";
            await cmd.ExecuteNonQueryAsync();
            /*}
            catch (Exception ex)
            {
                result = "Failed";
                msg = ex.Message;
            }*/

            return Json(new { result, msg });
        }


        [HttpPost]
        public async Task<JsonResult> Save([FromBody] dynamic args)
        {
            string costPriceType = args.costPriceType;
            string recentPriceTime = args.recentPriceTime;
            string validDayType = args.validDayType;

            string receiptTail = args.receiptTail;
            string companyNamesToPrint = args.companyNamesToPrint;
            string companyName = args.companyName;
            string companyAddress = args.companyAddress;
            string contactTel = args.contactTel;
            string visitDistance = args.visitDistance;
            string limitVisitDistance = args.limitVisitDistance;
            string sheetShowBarcodeStyle = args.sheetShowBarcodeStyle;
            string orderItemSheet = args.orderItemSheet;
            string appPrtItemNamePos = args.appPrtItemNamePos;
            string appPrtMoveItemNamePos = args.appPrtMoveItemNamePos;

            string saleSheetDefaultUnit = args.saleSheetDefaultUnit;
            string moveSheetDefaultUnit = args.moveSheetDefaultUnit;
            string buySheetDefaultUnit = args.buySheetDefaultUnit;
            string showBarcode = args.showBarcode;
            string checkAccountPayWayType = args.checkAccountPayWayType;
            string loadVirtualProduceDate = args.loadVirtualProduceDate;
            string appSheetShowItemSpec = args.appSheetShowItemSpec;
            string sheetShowPriceList = args.sheetShowPriceList;

            string imageClarityLevel = args.imageClarityLevel;

            string appSheetQtyMerge = args.appSheetQtyMerge;
            string appSheetUseAssistQty = args.appSheetUseAssistQty;
            string show_DH_CL_JH_inOftenItem = args.show_DH_CL_JH_inOftenItem;
            string appDbNoStockSave = args.appDbNoStockSave;
            string appXsNoStockSave = args.appXsNoStockSave;
            string appShareRetailPrice = args.appShareRetailPrice;
            string exactItemNameSearch = args.exactItemNameSearch;




            string separateDisplayAndSale = args.separateDisplayAndSale;
            string noStockAttrSplitShow = args.noStockAttrSplitShow;
            string saleNeedMarkIOU = args.saleNeedMarkIOU;
            string showNoProduceDate = args.showNoProduceDate;
            //  string noCountArrearsDayFromReturnOrder = args.noCountArrearsDayFromReturnOrder;
            string appSheetItemShowStyle = args.appSheetItemShowStyle;
            string oftenItemMonths = args.oftenItemMonths;

            string sheetAmountRound = args.sheetAmountRound; //单据精度
            string receiptShowSmallUnitPrice = args.receiptShowSmallUnitPrice;
            string receiptShowSmallUnitNumber = args.receiptShowSmallUnitNumber;
            string receiptReturnItemsAtTail = args.receiptReturnItemsAtTail;//args
            string receiptShowSellerInfo = args.receiptShowSellerInfo;
            string receiptShowSenderInfo = args.receiptShowSenderInfo;
            string receiptShowApproveTime = args.receiptShowApproveTime;
            string receiptShowClientTel = args.receiptShowClientTel;
            string receiptShowBranchInfo = args.receiptShowBranchInfo;
            string receiptShowClientAddr = args.receiptShowClientAddr;


            string receiptShowItemModel = args.receiptShowItemModel;

            string receiptShowItemRealPrice = args.receiptShowItemRealPrice;
            string receiptShowItemSubAmount = args.receiptShowItemSubAmount;
            string receiptShowItemRetailPrice = args.receiptShowItemRetailPrice;

            string forceBrief = args.forceBrief;


            string receiptShowValidDays = args.receiptShowValidDays;

            string receiptShowVirtualProduceDate = args.receiptShowVirtualProduceDate;

            string lineBetweenRowsMove = args.lineBetweenRowsMove;
            string lineBetweenRowsSale = args.lineBetweenRowsSale;
            string lineBetweenAcctRows = args.lineBetweenAcctRows;




            string receiptShowArrearsBalance = args.receiptShowArrearsBalance;
            string receiptShowBeforeArrears = args.receiptShowBeforeArrears;
            string receiptShowPrepayBalance = args.receiptShowPrepayBalance;
            string receiptShowOrderItemsBalance = args.receiptShowOrderItemsBalance;
            string receiptShowSheetOrderItemsBalance = args.receiptShowSheetOrderItemsBalance;
            string appPrtMovePrice = args.appPrtMovePrice;
            string appPrintLeftAmt = args.appPrintLeftAmt;
            string appPrtMoveUnitRelation = args.appPrtMoveUnitRelation;

            string autoVisitEnd = args.autoVisitEnd;
            string autoVisitEndDistance = args.autoVisitEndDistance;
            string positionPeriodStart = args.positionPeriodStart;
            string positionPeriodEnd = args.positionPeriodEnd;
            string minVisitDuration = args.minVisitDuration;

            string forceVisit = args.forceVisit;
            string forceRemark = args.forceRemark;
            string forceDoorPic = args.forceDoorPic;
            string forceDisplayPic = args.forceDisplayPic;
            string displayPicCount = args.displayPicCount;
            string flowExistMove = args.flowExistMove;
            string openPlaceholderSheet = args.openPlaceholderSheet;
            string showNegativeStock = args.showNegativeStock;
            string needBatchOnReview = args.needBatchOnReview;
            string reviewOrderBeforeAssignVan = args.reviewOrderBeforeAssignVan;
            string ignorePayFailedSheetOnAssignVan = args.ignorePayFailedSheetOnAssignVan;
            string ignorePayFailedSheetOnOrderToSale = args.ignorePayFailedSheetOnOrderToSale;
            string canMultiBranchAssign = args.canMultiBranchAssign;

            string flowExistMoveBack = args.flowExistMoveBack;
            string printCheckWhole = args.printCheckWhole;
            string printCheckOpenStock = args.printCheckOpenStock;
            string reservePrintCountOnRedChange = args.reservePrintCountOnRedChange;
            string allowRepeatedBackBranch = args.allowRepeatedBackBranch;
            string openTicketAccessSys = args.openTicketAccessSys;
            string ticketAccessSysAccount = args.ticketAccessSysAccount;
            string ticketAccessSysPwd = args.ticketAccessSysPwd;
            string ticketAccessSysLisense = args.ticketAccessSysLisense;
            string ticketAccessSysKey = args.ticketAccessSysKey;
            string printBillTailImage = args.printBillTailImage;
            string printSheetPrintTime = args.printSheetPrintTime;
            string printNoWrapAfterBarcode = args.printNoWrapAfterBarcode;
            string printBiggerSubAmount = args.printBiggerSubAmount;
            string printBiggerPayway = args.printBiggerPayway;

            string printBillHeadImage = args.printBillHeadImage;
            string newSheetAfterApprove = args.newSheetAfterApprove;
            string newSheetAfterSave = args.newSheetAfterSave;
            string newCheckAccountShowLineAmountDetail = args.newCheckAccountShowLineAmountDetail;
            string newSheetAfterSaleOrderSheetSave = args.newSheetAfterSaleOrderSheetSave;
            string newSheetAfterSaleOrderSheetApprove = args.newSheetAfterSaleOrderSheetApprove;
            string appPriceBeforeChange = args.appPriceBeforeChange;
            string appOpenBranchsForSheet = args.appOpenBranchsForSheet;
            string saleSheetNeedSender = args.saleSheetNeedSender;
            string saleOrderSheetNeedSender = args.saleOrderSheetNeedSender;
            string checkAccountBySender = args.checkAccountBySender;

            string saleSheetNoSameAsOrder = args.saleSheetNoSameAsOrder;

            string showSonItems = args.showSonItems;

            string happenTimeOnSave = args.happenTimeOnSave;
            string requerySheetsOnTabClick = args.requerySheetsOnTabClick;
            string printAccountSheetDate = args.printAccountSheetDate;

            string appUseVirtualProduceDate = args.appUseVirtualProduceDate;
            if (appUseVirtualProduceDate != null) appUseVirtualProduceDate = appUseVirtualProduceDate.ToLower();
            string appSaleUseSender = args.appSaleUseSender;
            if (appSaleUseSender != null) appSaleUseSender = appSaleUseSender.ToLower();

            string appSaleUseSn = args.appSaleUseSn;
            if (appSaleUseSn != null) appSaleUseSn = appSaleUseSn.ToLower();


            string feeOutSubForKS = args.feeOutSubForKS;
            // string mallBranch = args.mallBranch;
            string orderBranch = args.orderBranch;
            string branchForReturn = args.branchForReturn;
            string branchForOrderReturn = args.branchForOrderReturn;
            string backBranchPositionType = args.backBranchPositionType;
            string backBranchType = args.backBranchType;
            string defaultBranchPositionType = args.defaultBranchPositionType;

            string clientBossNecessary = args.clientBossNecessary;
            string supcustNoSerial = args.supcustNoSerial;
            string clientMobileNecessary = args.clientMobileNecessary;

            string doorPicNecessary = args.doorPicNecessary;
            string clientLocationNecessary = args.clientLocationNecessary;

            string clientLevelNecessary = args.clientLevelNecessary;
            string clientRegionNecessary = args.clientRegionNecessary;
            string clientGroupNecessary = args.clientGroupNecessary;
            string clientRelateSeller = args.clientRelateSeller;

            string useAccounting = args.useAccounting;
            string autoCreateVoucher = args.autoCreateVoucher;

            string dispTemplateNecessary = args.dispTemplateNecessary;

            string unitPriceRelated = args.unitPriceRelated;
            string batchType = args.batchType;


            string isDistinctStock = args.isDistinctStock;

            string attrName = args.attrName;

            string isCombinePrint = args.isCombinePrint;
            string isRememberSonItemPrice = args.isRememberSonItemPrice;

            // string qrCodeExpiresTime = args.qrCodeExpiresTime;


            var setting = new
            {
                costPriceType,
                recentPriceTime,
                validDayType,
                companyName,
                contactTel,
                receiptTail,
                companyNamesToPrint,
                companyAddress,
                visitDistance,
                limitVisitDistance,
                autoVisitEnd,
                autoVisitEndDistance,
                forceVisit,
                forceRemark,
                forceDisplayPic,
                displayPicCount,
                forceDoorPic,
                positionPeriodStart,
                positionPeriodEnd,
                minVisitDuration,
                sheetShowBarcodeStyle,
                orderItemSheet,
                appPrtItemNamePos,
                appPrtMoveItemNamePos,
                appPrtMovePrice,
                appPrintLeftAmt,
                appPrtMoveUnitRelation,
                saleSheetDefaultUnit,
                moveSheetDefaultUnit,
                buySheetDefaultUnit,
                imageClarityLevel,
                showBarcode,
                checkAccountPayWayType,
                loadVirtualProduceDate,
                appSheetShowItemSpec,
                sheetShowPriceList,
                appSheetQtyMerge,
                appSheetUseAssistQty,
                show_DH_CL_JH_inOftenItem,
                appDbNoStockSave,
                appXsNoStockSave,
                appShareRetailPrice,
                exactItemNameSearch,
                separateDisplayAndSale,
                noStockAttrSplitShow,
                saleNeedMarkIOU,
                showNoProduceDate,
                //       noCountArrearsDayFromReturnOrder,
                appSheetItemShowStyle,
                oftenItemMonths,
                sheetAmountRound,
                receiptShowSmallUnitPrice,
                receiptShowSmallUnitNumber,
                receiptReturnItemsAtTail,//
                receiptShowSellerInfo,
                receiptShowSenderInfo,
                receiptShowApproveTime,        //小票打印审核时间
                receiptShowClientTel,          //小票打印客户电话
                receiptShowBranchInfo,
                receiptShowClientAddr,
                receiptShowItemModel,
                receiptShowItemRealPrice,
                receiptShowItemSubAmount,
                receiptShowItemRetailPrice,
                printAccountSheetDate,
                forceBrief,
                receiptShowValidDays,
                receiptShowVirtualProduceDate,
                lineBetweenRowsMove,
                lineBetweenRowsSale,
                lineBetweenAcctRows,
                receiptShowArrearsBalance,
                receiptShowBeforeArrears,
                receiptShowPrepayBalance,
                receiptShowOrderItemsBalance,
                receiptShowSheetOrderItemsBalance,
                flowExistMove,
                openPlaceholderSheet,
                showNegativeStock,
                needBatchOnReview,
                reviewOrderBeforeAssignVan,
                ignorePayFailedSheetOnAssignVan,
                ignorePayFailedSheetOnOrderToSale,
                canMultiBranchAssign,
                flowExistMoveBack,
                printCheckWhole,
                printCheckOpenStock,
                reservePrintCountOnRedChange,
                allowRepeatedBackBranch,
                printSheetPrintTime,
                printNoWrapAfterBarcode,
                printBiggerSubAmount,
                printBiggerPayway,
                openTicketAccessSys,
                ticketAccessSysAccount,
                ticketAccessSysPwd,
                ticketAccessSysLisense,
                ticketAccessSysKey,
                newSheetAfterSave,
                newCheckAccountShowLineAmountDetail,
                newSheetAfterSaleOrderSheetSave,
                newSheetAfterSaleOrderSheetApprove,
                appPriceBeforeChange,
                appOpenBranchsForSheet,
                newSheetAfterApprove,
                saleSheetNeedSender,
                saleOrderSheetNeedSender,
                checkAccountBySender,
                saleSheetNoSameAsOrder,
                showSonItems,
                happenTimeOnSave,
                requerySheetsOnTabClick,
                printBillTailImage,
                printBillHeadImage,
                feeOutSubForKS,
                // mallBranch,
                orderBranch,
                branchForReturn,
                branchForOrderReturn,
                backBranchPositionType,
                backBranchType,
                defaultBranchPositionType,
                appUseVirtualProduceDate,
                appSaleUseSender,
                appSaleUseSn,
                clientBossNecessary,
                supcustNoSerial,
                clientMobileNecessary,

                doorPicNecessary,
                clientLocationNecessary,

                clientRegionNecessary,
                clientGroupNecessary,
                clientLevelNecessary,
                clientRelateSeller,
                dispTemplateNecessary,
                unitPriceRelated,

                // qrCodeExpiresTime


                useAccounting,
                autoCreateVoucher,
                batchType

            };
            var jsonSetting = JsonConvert.SerializeObject(setting);
            cmd.CommandText =
                $"insert into company_setting (company_id,cost_price_type,barcode_type,setting) values ({Token.CompanyID},{costPriceType},{sheetShowBarcodeStyle},'{jsonSetting}') on conflict(company_id) do update set cost_price_type = {costPriceType},barcode_type = {sheetShowBarcodeStyle},setting=COALESCE(company_setting.setting,'{{}}')::jsonb||'{jsonSetting}'::jsonb;";

            await cmd.ExecuteNonQueryAsync();//测试得保存成功

            //开通口味
            string querySql = $"select * from info_attribute where company_id={Token.CompanyID}  ORDER BY attr_id desc limit 1";
            dynamic rec = await CDbDealer.Get1RecordFromSQLAsync(querySql, cmd);
            if (rec == null)
            {
                string findMaxAttrIDSql = $"select attr_id from info_attribute ORDER BY attr_id desc limit 1";
                dynamic queryResult = await CDbDealer.Get1RecordFromSQLAsync(findMaxAttrIDSql, cmd);
                if (attrName == "")
                {
                    attrName = "口味";
                }
                cmd.CommandText = @$"insert into info_attribute
                                    (company_id,attr_id, attr_name, spec_opt_in_item, use_opt_group, distinct_stock, distinct_stock_editable, not_distinct_stock_in_van, sale_combine_print,remember_price)
                                    values('{Token.CompanyID}','{Convert.ToInt32(queryResult.attr_id) + 1}' ,'{attrName}', 't', 'f', '{isDistinctStock}', 'f', 'f', '{isCombinePrint}','{isRememberSonItemPrice}')";
                await cmd.ExecuteNonQueryAsync();
            }
            else
            {
                if (attrName == "")
                {
                    attrName = "口味";
                }
                cmd.CommandText = $"UPDATE info_attribute set attr_name='{attrName}',sale_combine_print='{isCombinePrint}',remember_price='{isRememberSonItemPrice}' where company_id={Token.CompanyID} and attr_id={rec.attr_id} ";
                cmd.company_id = Token.CompanyID;
                await cmd.ExecuteNonQueryAsync();
            }
            var result = await CommonTool.CheckToUpdateDb(cmd, Token.CompanyID, "", true);//在打开欠款策略的时候，对三张表进行数据迁移，以后要删除此方法。

            return Json(new { result = "OK", msg = "" });
        }

        //private List<DataRow> GetItems(string idList)
        //{
        //    var where = $" WHERE iip.company_id = {Token.CompanyID} and iip.item_name in ({idList})";
        //    var list = cmd.Query<StockInfo>(where);
        //    var group = list.GroupBy(x => x.ItemId);
        //    List<DataRow> data = group.Select(x =>
        //    {
        //        var dr = new DataRow().AddColumn(x.Key).AddColumn(x.First().ItemName);

        //        var b_unit_no = "";
        //        var m_unit_no = "";
        //        float bigRatio = 0;
        //        float midRatio = 0;
        //        var unit_no = "";
        //        foreach (var stock in x)
        //        {
        //            if (stock.UnitType == Enums.UnitType.b)
        //            {
        //                b_unit_no = stock.UnitName;
        //                bigRatio = stock.Ratio;
        //            }else if(stock.UnitType == Enums.UnitType.m)
        //            {
        //                m_unit_no = stock.UnitName;
        //                midRatio = stock.Ratio;
        //            }
        //            else
        //            {
        //                unit_no = stock.UnitName;
        //            }
        //        }
        //        dr.AddColumn(b_unit_no).AddColumn(bigRatio.ToString()).AddColumn(m_unit_no).AddColumn(midRatio.ToString()).AddColumn(unit_no).AddColumn("1");
        //        return dr;

        //    }).ToList();

        //    return data;

        //}

        [HttpPost]
        public async Task<JsonResult> ResetCostPrice([FromBody] dynamic data)
        {
            var result = "OK";
            var msg = "";
            try
            {
                string item_id = data.item_id;
                string start_time = data.start_time;
                string end_time = data.end_time;
                start_time = CPubVars.GetDateText(start_time);
                end_time = CPubVars.GetDateText(end_time);
                string cost_price_type = data.cost_price_type;
                string recent_price_time = data.recent_price_time;
                string price = data.price;
                string cost_price = "";
                switch (cost_price_type)
                {

                    case "1"://预设成本
                        cost_price = "cost_price_prop";
                        break;
                    case "2"://加权价
                        cost_price = "cost_price_avg";
                        break;
                    case "3"://当前进价
                        cost_price = "cost_price_buy";
                        break;
                    case "4"://最近平均进价
                        switch (recent_price_time)
                        {
                            case "3":
                                cost_price = "avg3";
                                break;
                            case "2":
                                cost_price = "avg2";
                                break;
                            case "1":
                                cost_price = "avg1";
                                break;
                        }
                        break;
                }
                if (cost_price != "")
                {
                    string sql = "";
                    if (cost_price_type == "4")
                    {
                        sql =
                        $"update sheet_sale_detail set cost_price_recent = {CPubVars.ToDecimal(price)} where company_id = {Token.CompanyID} and item_id = {item_id} and happen_time >= '{start_time}' and happen_time<= '{end_time}'; ";
                    }
                    else
                    {
                        sql =
                        $"update sheet_sale_detail set {cost_price}={price} where company_id = {Token.CompanyID} and item_id = {item_id} and happen_time >='{start_time}' and happen_time<='{end_time}';";
                    }
                    if (cost_price == "cost_price_avg")
                    {
                        sql += $"update info_item_prop set {cost_price}={price} where company_id = {Token.CompanyID} and item_id = {item_id};";
                    }

                    cmd.CommandText = sql;
                    await cmd.ExecuteNonQueryAsync();
                }
            }
            catch (Exception ex)
            {
                result = "Error";
                msg = ex.Message;
            }

            return Json(new { result, msg });
        }
    }
}