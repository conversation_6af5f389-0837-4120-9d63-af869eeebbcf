using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using ArtisanManage.Models;
using ArtisanManage.Services;
using ArtisanManage.MyJXC;
using System.Dynamic;
using System.Net.Http;
using Microsoft.AspNetCore.Http;
using NPOI.XSSF.UserModel;
using static ArtisanManage.Models.PageQueryModel;
using System.IO;
using System.Text;
using System.Xml;
using NPOI.XSSF.Streaming;
using Org.BouncyCastle.Asn1.Ocsp;
using System.Net;
using System.Text.RegularExpressions;
using Microsoft.AspNetCore.Components.Forms;
namespace ArtisanManage.Pages
{

    public class GetArrearsSheetModel : PageSheetModel<SheetRowArrears>
    { 
        public string SheetTitle = "";
        public GetArrearsSheetModel(CMySbCommand cmd):base(MenuId.sheetGetArrears)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"sheet_no",new DataItem(){Title="单号",UseJQWidgets=false}},
                {"order_sheet_id",new DataItem(){UseJQWidgets=false, Title="order_sheet_id"}},
                {"order_sheet_no",new DataItem(){UseJQWidgets=false, Title="order_sheet_no"}},
                {"sheet_id",new DataItem(){Title="sheet_id", CtrlType="hidden", FldArea="divHead"}},
                {"red_flag",new DataItem(){Title="red_flag", CtrlType="hidden", FldArea="divHead"}},
                {"sheet_type",new DataItem(){Title="sheet_type", CtrlType="hidden", FldArea="divHead"}},
                {"sheetType",new DataItem(){Title="sheetType", CtrlType="hidden", FldArea="divHead"}},
                {"startDay",new DataItem(){Title="开始日期",FldArea="box", CtrlType="jqxDateTimeInput", SqlFld="sm.happen_time", CompareOperator=">="}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="box", CtrlType="jqxDateTimeInput", SqlFld="sm.happen_time", CompareOperator="<",
                    JSDealItemOnSelect=@"
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},

                {"seller_id",new DataItem(){FldArea="box",Title="业务员",LabelFld="seller_name",ButtonUsage="list",SqlForOptions=CommonTool.selectSellers } },

                {
                    "senders_id",
                    new DataItem()
                    {
                        FldArea = "box", Title = "送货/收货员",SqlFld = "senders_id", LabelFld = "senders_name", ButtonUsage = "list",
                        DealQueryItem = status => ""+status+"",
                        SqlForOptions=CommonTool.selectSenders,  //SqlForOptions = "select oper_id as v,oper_name as l,py_str as z from info_operator",
                        CompareOperator = "like"
                    }
                },
                {"sheetno",new DataItem(){FldArea="box",Title="单号"} },

                {"supcust_id",new DataItem(){FldArea="divHead",Title="客户",LabelFld="sup_name",SqlFld="sht.supcust_id",Pinned=true, ButtonUsage="event",SqlForOptions="select supcust_id as v,sup_name as l,py_str as z ,supcust_flag as sf from info_supcust where (supcust_flag like '%C%' or supcust_flag='W') and coalesce(status,0) <> 0",DropDownWidth = "280"}},
                {"getter_id",new DataItem(){FldArea="divHead",Title="收款人",LabelFld="getter_name",ButtonUsage="list",SqlForOptions=CommonTool.selectSellers } },//SqlForOptions="select oper_id as v,oper_name as l,py_str as z from info_operator"}},
             
                //{"oper_date",new DataItem(){title="制单日期",ctrlType="jqxDateTimeInput"}},

                {"happen_time",new DataItem(){FldArea="divHead",Title="发生日期",CtrlType="jqxDateTimeInput",PlaceHolder="当前时间" }},
                {"make_brief",new DataItem(){FldArea="divHead",Title="备注"}},
                {"sheet_amount",new DataItem(){FldArea="divTail",Title="总额",Width="40",TitleWidth="60", Disabled=true,Hidden=true}},
                {"paid_amount",new DataItem(){FldArea="divTail",Title="已支付",Width="40",TitleWidth="60",Disabled=true,Hidden=true}},
                {"disc_amount",new DataItem() { FldArea = "divTail", Title = "已优惠", Width = "80",TitleWidth="60",Disabled=true,Hidden=true}},
                {"ought_amount",new DataItem(){FldArea="divTail",Title="应收",Width="60",TitleWidth="60",Disabled=true}},
                {"now_disc_amount",new DataItem(){FldArea="divTail",Title="本次优惠",Width="70",TitleWidth="60",Value="",Disabled=false}},
                {"now_pay_amount",new DataItem(){FldArea="divTail",Title="本次支付",Width="70",TitleWidth="60",Value="",Disabled=false}},
               //{"no_disc_amount",new DataItem(){FldArea="divTail",Title="惠后合计",SqlFld="total_amount-disc_amount",Width="80"}},
                {"payway1_id",new DataItem(){FldArea="divTail",HideGroup="payway1", HideClass="payway",Title="",LabelFld="payway1_name",ClassName="itemLeft", SqlFld="",GetOptionsOnLoad=true,FirstOptionAsDefault=true,
                    PlaceHolder="支付方式",ButtonUsage="list",SqlForOptions=CommonTool.selectPayWayGetArrears,Width="70",InnerTitle="支付方式1"}},
                {"payway1_type",new DataItem(){FldArea = "divTail", Title = "", InnerTitle = "支付类型1", Hidden = true, HideOnLoad = true, Width = "80"}},
                {"payway1_amount",new DataItem(){FldArea="divTail",HideGroup="payway1",HideClass="payway", Title="", ClassName="itemRight",PlaceHolder="支付金额",Width="80",InnerTitle="支付金额1"}},
                {"payway2_id",new DataItem(){FldArea="divTail",HideGroup="payway2",HideClass="payway", Title="",LabelFld="payway2_name",ClassName="titleItem",SqlFld="",GetOptionsOnLoad=true,PlaceHolder="支付方式", ButtonUsage="list",SqlForOptions=CommonTool.selectPayWayGetArrears,Width="80",Hidden=true,InnerTitle="支付方式2"}},
                {"payway2_type",new DataItem(){FldArea = "divTail", Title = "", InnerTitle = "支付类型2", Hidden = true, HideOnLoad = true, Width = "80"}},
                {"payway2_amount",new DataItem(){FldArea="divTail",HideGroup="payway2",HideClass="payway", Title="", PlaceHolder="支付金额",Width="80",Hidden=true,InnerTitle="支付金额2"}},
                {"payway3_id",new DataItem(){FldArea="divTail",HideGroup="payway3",HideClass="payway", Title="",LabelFld="payway3_name",ClassName="titleItem",SqlFld="",GetOptionsOnLoad=true,PlaceHolder="支付方式", ButtonUsage="list",SqlForOptions=CommonTool.selectPayWayGetArrears,Width="80",Hidden=true,InnerTitle="支付方式3"}},
                {"payway3_type",new DataItem(){FldArea = "divTail", Title = "", InnerTitle = "支付类型3", Hidden = true, HideOnLoad = true, Width = "80"}},
                {"payway3_amount",new DataItem(){FldArea="divTail",HideGroup="payway3",HideClass="payway", Title="", PlaceHolder="支付金额",Width="80",Hidden=true,InnerTitle="支付金额3"}},
                {"left_amount",new DataItem(){FldArea="divTail",Title="尚欠",Width="80",TitleWidth="60", Disabled=true}},
                {"maker_id",new DataItem(){UseJQWidgets=false, Title="制单人", Width="80"}},
                {"maker_name",new DataItem(){UseJQWidgets=false, Title="制单人", Width="80"}},
                {"make_time",new DataItem(){UseJQWidgets=false, Title="审核时间"}},
                {"approver_id",new DataItem(){UseJQWidgets=false, Title="审核人", Width="80"}},
                {"approver_name",new DataItem(){UseJQWidgets=false, Title="审核人", Width="80"}},
                {"approve_time",new DataItem(){UseJQWidgets=false, Title="审核时间"}},
                {"review_time",new DataItem(){UseJQWidgets=false, Title="复核时间"}},
                {"reviewer_name",new DataItem(){UseJQWidgets=false, Title="复核人", Width="80"}},
                {"isRedAndChange",new DataItem(){Title="isRedAndChange",CtrlType="hidden",  FldArea="divHead"} },
                {"old_sheet_id",new DataItem(){Title="old_sheet_id",CtrlType="hidden",  FldArea="divHead"} },
                {"red_sheet_id",new DataItem(){Title="red_sheet_id", CtrlType="hidden", FldArea="divHead"}},
                //{"red_sheet_no",new DataItem(){UseJQWidgets=false, Title="red_sheet_no"}},
                {"sheet_id_red_me",new DataItem(){Title="sheet_id_red_me", CtrlType="hidden", FldArea="divHead"}},
                {"prepay_sub_ids",new DataItem(){Title="prepay_sub_ids", CtrlType="hidden", FldArea="divHead"}},
                {"appendix_photos",new DataItem(){Title="appendix_photos", CtrlType="hidden", FldArea="divHead"}}

               // {"branch_state",new DataItem(){title="状态",labelFld="branch_status_name",labelInDB=false,value="1",label="正常", buttonUsage="list", source = "[{v:1,l:'正常'},{v:0,l:'停用'}]"}}
            };
            /*   m_idFld = "sheet_id"; 
             m_tableName = "sheet_item_";
             m_selectFromSQL = "from sheet_item_master sht left join info_supcust on sht.supcust_id=info_supcust.supcust_id left join info_branch on sht.branch_id=info_branch.brand_id left join (select oper_id,oper_name as order_man_name from info_operator) tb_order_man on sht.order_man=tb_order_man.oper_id where sheet_no='~ID'";

              Grids = new Dictionary<string, FormDataGrid>()
              {
                  {"gridUnit" ,new FormDataGrid(){
                     Columns = new Dictionary<string, DataItem>()
                     {
                         //{"unit_no",new DataItem(){title="单位",width="100",url="../api/ItemEdit/GetUnits"}},
                         {"unit_no",new DataItem(){title="单位",width="100",SqlForOptions="select unit_no from info_item_unit",getOptionsOnLoad=true,buttonUsage="list"}},
                         {"unit_factor",new DataItem(){title="包装率",width="80"}},
                     },
                     TableName="info_item_multi_unit",
                     IdFld="item_no",
                     SelectFromSQL="from info_item_multi_unit where item_no='~ID'"
                  }}
              };*/
        }

        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            bool forPayOrGet = false;
            if (CPubVars.RequestV(Request, "forPayOrGet") != "")
            {
                forPayOrGet = Convert.ToBoolean(CPubVars.RequestV(Request, "forPayOrGet"));
                
            }

            string sqlOptions = CommonTool.selectPayWay_Sale;
            if (forPayOrGet)
            {
                DataItems["supcust_id"].Title = "供应商";
                DataItems["getter_id"].Title = "付款人";
           
                sqlOptions = CommonTool.selectPayWay_Buy;
                DataItems["supcust_id"].OtherQueryStrForOptions = "sheet_type=FK";
            }
            else
            {
                DataItems["supcust_id"].Title = "客户";
                DataItems["getter_id"].Title = "收款人";

                sqlOptions = CommonTool.selectPayWay_Sale;
                DataItems["supcust_id"].OtherQueryStrForOptions = "";
            }
            DataItems["payway1_id"].SqlForOptions = sqlOptions;
            DataItems["payway2_id"].SqlForOptions = sqlOptions;
            DataItems["payway3_id"].SqlForOptions = sqlOptions;

            /*if (DataItems["payway1_id"].Value == "")
            {
                string sql = $"select  p.sub_id as v,s.sub_name as l from info_pay_way p left join cw_subject s on p.sub_id=s.sub_id where p.company_id={company_id} and payway_type = 'QT' order by p.payway_index";

                dynamic record = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                if (record != null)
                {
                    DataItems["payway1_id"].Value = record.v;
                    DataItems["payway1_id"].Label = record.l;
                }
            }*/

            DataItems["ought_amount"].Value = (Convert.ToSingle(DataItems["left_amount"].Value) + Convert.ToSingle(DataItems["now_pay_amount"].Value) + Convert.ToSingle(DataItems["now_disc_amount"].Value)).ToString();

            if (DataItems["sheet_id"].Value == "" && DataItems["order_sheet_id"].Value =="")
            {
                DataItems["now_disc_amount"].Value = "";
                DataItems["now_pay_amount"].Value = ""; 
             
            }

            string sqlPrepay = $"select string_agg(sub_id::text,',') sub_ids  from cw_subject where company_id = {company_id} and sub_type in ('YS','YF') and not coalesce(is_order,false)";
            dynamic recordPrepay = await CDbDealer.Get1RecordFromSQLAsync(sqlPrepay, cmd);
            if (recordPrepay != null) DataItems["prepay_sub_ids"].Value = recordPrepay.sub_ids;
        }

        public override async Task<SheetBase<SheetRowArrears>> GetSheetByOrder(CMySbCommand cmd, string operKey, string order_sheet_id, bool isDefaultFromSheetType)
        {
            dynamic orderSheet = null;
            if (isDefaultFromSheetType) orderSheet = new SheetGetArrearsOrder(LOAD_PURPOSE.SHOW);

            await orderSheet.Load(cmd, company_id, order_sheet_id);
            //if(!isDefaultFromSheetType) orderSheet = JsonConvert.DeserializeObject<SheetSaleOrder>(JsonConvert.SerializeObject(orderSheet));
            SheetGetArrears getArrearsSheet = await orderSheet.ToGetArrearsSheet(operKey,cmd);
            
            return getArrearsSheet;
        }
        public async Task OnGet(bool forPayOrGet)
        {
            string supcust_id = CPubVars.RequestV(Request,"supcust_id");
            string sup_name = WebUtility.UrlDecode(CPubVars.RequestV(Request, "sup_name"));

            SheetGetArrears sheet = new SheetGetArrears(forPayOrGet ? SHEET_GET_ARREARS.NOT_GET:SHEET_GET_ARREARS.IS_GET, LOAD_PURPOSE.SHOW);
            if (supcust_id != ""&&sup_name!="")
            {
                sheet.supcust_id = supcust_id;
                sheet.sup_name = sup_name;
            }

            //在拓展cs往来类型后，下面代码会导致load出的单据缺少一些单据类型，本身DetailLeftJoin兼容所有单据
            //if (sheet.sheet_type == SHEET_TYPE.SHEET_PAY_MONEY) sheet.DetailLeftJoin = sheet.DetailLeftJoin.Replace("sheet_sale_main", "sheet_buy_main");
            await InitGet(cmd, sheet);

            SheetTitle = sheet.sheet_type == SHEET_TYPE.SHEET_GET_MONEY ? "收款单" : "付款单";
            SheetRowsJson = Newtonsoft.Json.JsonConvert.SerializeObject(sheet.SheetRows);
        }

        
    }
    [ApiController]
    [Route("api/[controller]/[action]")]
    public class GetArrearsSheetController : BaseController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        public GetArrearsSheetController(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues, string sheet_type)
        {
            var model = new GetArrearsSheetModel(cmd);
            if (sheet_type == "FK")
            {
                model.DataItems["supcust_id"].SqlForOptions = model.DataItems["supcust_id"].SqlForOptions.Replace("%C%", "%S%").Replace("or supcust_flag='W'", "");
            }


            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }

        [HttpGet]
        public async Task<IActionResult> GetClientAccountInfo(string operKey, string supcust_id, string sheet_type)
        {
            string paywayType = "YS";
            if (sheet_type == "YF") paywayType = "YF";
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            var dispCondi = " where t.disp_month<=(to_char(now(),'MM')::int) and disp_year=(to_char(now(),'YYYY')::int) and sub_name is not null";
            string sql = $"select rights->'delicacy'->'allowAdvanceDisplayFee'->'value' allow_advance_disp_fee from info_operator o left join info_role r on r.role_id= o.role_id where o.company_id={companyID} and oper_id={operID}";
            string restrictPaywayCondi = $"(b.sub_id::text IN(SELECT json_array_elements_text(avail_pay_ways) AS individual_value FROM info_operator WHERE company_id = {companyID} and oper_id = {operID} and restrict_pay_ways = TRUE) OR (select COUNT(*) from info_operator where company_id = {companyID} and oper_id = {operID} and restrict_pay_ways = TRUE) = 0)";
            dynamic advanceDisp = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            if (advanceDisp != null && advanceDisp.allow_advance_disp_fee == "true")
                dispCondi = $"where sub_name is not null";
            SQLQueue QQ = new SQLQueue(cmd);

            sql = $"select b.sub_id,sub_name,round(balance::numeric,2) balance from prepay_balance b left join cw_subject p on p.sub_id = b.sub_id where b.company_id = {companyID} and supcust_id = {supcust_id} and p.sub_type = '{paywayType}' and balance<>0 AND p.is_order is not true and {restrictPaywayCondi};";
            QQ.Enqueue("prepay", sql);
            sql = $"select mobile,sup_addr,boss_name from info_supcust where company_id = {companyID} and supcust_id = {supcust_id}";
            QQ.Enqueue("info", sql);
            restrictPaywayCondi = $"(t.sub_id::text IN(SELECT json_array_elements_text(avail_pay_ways) AS individual_value FROM info_operator WHERE company_id = {companyID} and oper_id = {operID} and restrict_pay_ways = TRUE) OR (select COUNT(*) from info_operator where company_id = {companyID} and oper_id = {operID} and restrict_pay_ways = TRUE) = 0)";
            sql = @$"select t.sub_id,sub_name,sum(disp_left_money) disp_amount from 
                        (select sub_id,disp_left_money,
                            (case when start_month+months-1>12 then start_month+months-13 else start_month+months-1 end) disp_month,
                            (case when start_month+months-1>12 then start_year+1 else start_year end) as disp_year,months as disp_month_id
                            from (
                            select m.fee_sub_id sub_id,d.sheet_id,items_id,items_name,unnest(string_to_array((								
                                    COALESCE(month1_qty,0)-COALESCE(month1_given,0)||','||COALESCE(month2_qty,0)-COALESCE(month2_given,0)||','||
                                    COALESCE(month3_qty,0)-COALESCE(month3_given,0)||','||COALESCE(month4_qty,0)-COALESCE(month4_given,0)||','||
                                    COALESCE(month5_qty,0)-COALESCE(month5_given,0)||','||COALESCE(month6_qty,0)-COALESCE(month6_given,0)||','||
                                    COALESCE(month7_qty,0)-COALESCE(month7_given,0)||','||COALESCE(month8_qty,0)-COALESCE(month8_given,0)||','||
                                    COALESCE(month9_qty,0)-COALESCE(month9_given,0)||','||COALESCE(month10_qty,0)-COALESCE(month10_given,0)||','||
                                    COALESCE(month11_qty,0)-COALESCE(month11_given,0)||','||COALESCE(month12_qty,0)-COALESCE(month12_given,0)) ,','))::numeric disp_left_money,
                                    unnest(string_to_array('01,02,03,04,05,06,07,08,09,10,11,12',','))::int months,to_char(m.start_time,'YYYY')::int start_year,
                                    to_char(m.start_time,'MM')::int start_month 
                            from display_agreement_detail d left join display_agreement_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID}
                            where d.company_id = {companyID} and approve_time is not null and red_flag is null and supcust_id = {supcust_id} and items_id ='money' ) t
                        where disp_left_money > 0 ) t
                left join cw_subject c on c.sub_id = t.sub_id and c.company_id = {companyID} 
                {dispCondi} 
                and {restrictPaywayCondi} GROUP BY t.sub_id,sub_name;";
            QQ.Enqueue("disp", sql);
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            var prepay = new List<ExpandoObject>();
            var disp = new List<ExpandoObject>();
            dynamic info = null;
            while (QQ.Count > 0)
            {
                string sqlName = QQ.Dequeue();
                if (sqlName == "prepay")
                {
                    prepay = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "info")
                {
                    info = CDbDealer.Get1RecordFromDr(dr, false);
                }
                else if (sqlName == "disp")
                {
                    disp = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();
            return new JsonResult(new { result = "OK", prepay,disp, info.mobile, info.sup_addr, info.boss_name });
        }


        [HttpPost]
        public async Task<IActionResult> Save([FromBody] SheetGetArrears sheet)  //[FromBody] dynamic sheet)
        {
            if (sheet.sheet_type == SHEET_TYPE.SHEET_PAY_MONEY) sheet.mmSheetTable = "sheet_buy_main";
            sheet.Init();
            sheet._httpClientFactory = this._httpClientFactory;
            await sheet.ProcessPcAppendix();
            string msg = await sheet.Save(cmd);
            string result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no });
        }

        [HttpPost]
        public async Task<IActionResult> SaveAndApprove([FromBody] SheetGetArrears sheet)
        {
            if (sheet.sheet_type == SHEET_TYPE.SHEET_PAY_MONEY) sheet.mmSheetTable = "sheet_buy_main";
            sheet.Init();
            if (sheet.bReview)
            {
                sheet.reviewer_id = sheet.OperID;
                sheet.review_time = CPubVars.GetDateText(DateTime.Now);
            }
            sheet._httpClientFactory = this._httpClientFactory;
            await sheet.ProcessPcAppendix();
            string msg = "";
            if (sheet.isRedAndChange)
            {
                if (!sheet.old_sheet_id.IsValid())
                {
                    msg = "修改时没有获取原单据的编号";
                }
                else
                {
                    msg = await sheet.RedAndChange<SheetGetArrears>(cmd);
                }
            }
            else
            {
                msg = await sheet.SaveAndApprove(cmd);
            }
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time, sheet.approve_time , sheet.review_time });
        }
        [HttpPost]
        public async Task<IActionResult> Review([FromBody] dynamic data)
        {
            string sheet_id = data.sheet_id;
            string now = CPubVars.GetDateText(DateTime.Now);
            string msg = "";
            dynamic rec = await CDbDealer.Get1RecordFromSQLAsync($"select approve_time,red_flag from sheet_get_arrears_main where company_id={Token.CompanyID} and sheet_id={sheet_id};", cmd);
            if (rec == null)
            {
                msg = "单据不存在";
            }
            else if (rec.red_flag != "")
            {
                msg = "被红冲单据不能复核";
            }
            else if (rec.approve_time == "")
            {
                msg = "单据未审核不能复核";
            }
            else
            {
                cmd.CommandText = $"update sheet_get_arrears_main set review_time ='{now}',reviewer_id={Token.OperID} where company_id={Token.CompanyID} and sheet_id={sheet_id};";
                await cmd.ExecuteNonQueryAsync();
            }

            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";

            return new JsonResult(new { result, msg, review_time = now });
        }

        [HttpGet]
        public async Task<IActionResult> GetSheetToPrint(string operKey, string sheet_id, SHEET_TYPE sheet_type)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetGetArrears sheet = new SheetGetArrears(sheet_type == SHEET_TYPE.SHEET_PAY_MONEY ? SHEET_GET_ARREARS.NOT_GET : SHEET_GET_ARREARS.IS_GET, LOAD_PURPOSE.SHOW);
            cmd.ActiveDatabase = "";
            await sheet.Load(cmd, companyID, sheet_id);
            await sheet.LoadInfoForPrint(cmd, false);
            string msg = "";
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, sheet });

        }

        [HttpPost]
        public async Task<IActionResult> Red([FromBody] dynamic data)
        {

            string sheet_id = data.sheet_id;
            string operKey = data.operKey;
            string redBrief = data.redBrief;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetGetArrears sheet = new SheetGetArrears(SHEET_GET_ARREARS.EMPTY, LOAD_PURPOSE.SHOW);
            sheet._httpClientFactory = this._httpClientFactory;
            string msg = await sheet.Red(cmd, companyID, sheet_id, operID, redBrief);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time });
        }

        [HttpPost]
        public async Task<IActionResult> Delete([FromBody] dynamic data)
        {
            string sheet_id = data.sheet_id;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetGetArrears sheet = new SheetGetArrears(SHEET_GET_ARREARS.EMPTY, LOAD_PURPOSE.SHOW);

            string msg = await sheet.Delete(cmd, companyID, sheet_id, operID);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time });
        }

        [HttpPost]
        public async Task<IActionResult> AppendBrief([FromBody] dynamic data)
        {
            string sheetID = data.sheetID;
            string newBrief = data.newBrief;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetGetArrears sheet = new SheetGetArrears(SHEET_GET_ARREARS.EMPTY, LOAD_PURPOSE.APPROVE);

            string msg = await sheet.AppendBrief(cmd, companyID, sheetID, newBrief);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, });
        }

        [HttpPost]
        public async Task<IActionResult> JqxExportExcel([FromForm] IFormCollection data)
        {
            Console.WriteLine(data.ToString());
            string contentXml = data["content"];
            string result = "OK";
            string msg = "";

            #region 返回格式漂亮的xls文件，jqx版本太低不支持返回xlsx文件
            // // 设置响应头
            // Response.Clear();
            // Response.ContentType = "application/vnd.ms-excel";
            // Response.Headers.Add("Content-Disposition", $"attachment; filename=SK.xls");
            // Response.Headers.Add("Cache-Control", "must-revalidate, post-check=0, pre-check=0, private");
            // Response.Headers.Add("Expires", "0");
            // Response.Headers.Add("Pragma", "public");
            // // 写入内存流
            // 
            // using (MemoryStream memoryStream = new MemoryStream())
            // {
            //     using (StreamWriter sw = new StreamWriter(memoryStream, Encoding.UTF8, leaveOpen: true))
            //     {
            //         await sw.WriteAsync(contentXml);
            //         await sw.FlushAsync();
            //     }
            // 
            //     // 将内存流的内容异步写入响应体
            //     memoryStream.Seek(0, SeekOrigin.Begin);
            //    await memoryStream.CopyToAsync(Response.Body);
            // 
            // 
            // }
            // return new EmptyResult();
            #endregion
            #region 返回一个没有样式的xlsx文件
            // 创建一个新的 SXSSFWorkbook 实例
            using (var workbook = new XSSFWorkbook())
            {
                // 用来匹配日期
                string pattern = @"^\d{1,2}/\d{1,2}/\d{4}$";
                Regex regex = new Regex(pattern);
                // 添加一个工作表
                var sheet = workbook.CreateSheet("Sheet1");

                // 将 XML 内容加载到工作表中
                using (var stream = new MemoryStream(Encoding.UTF8.GetBytes(contentXml)))
                {
                    var xmlDoc = new XmlDocument();
                    xmlDoc.Load(stream);

                    // 创建命名空间管理器
                    var nsManager = new XmlNamespaceManager(xmlDoc.NameTable);
                    nsManager.AddNamespace("ss", "urn:schemas-microsoft-com:office:spreadsheet");

                    // 使用带有命名空间的 XPath 获取所有行节点
                    var rows = xmlDoc.SelectNodes("//ss:Row", nsManager);
                    int rowIdx = 0;

                    foreach (XmlNode rowNode in rows)
                    {
                        var row = sheet.CreateRow(rowIdx++);
                        int colIdx = 0;

                        // 使用带有命名空间的 XPath 获取当前行中的所有单元格节点
                        var cells = rowNode.SelectNodes("ss:Cell", nsManager);

                        foreach (XmlNode cellNode in cells)
                        {
                            // 使用带有命名空间的 XPath 获取单元格中的数据
                            var dataNode = cellNode.SelectSingleNode("ss:Data", nsManager);
                            var value = dataNode.InnerText;
                            // 如果匹配为日期，修改日期格式
                            if (regex.IsMatch(value))
                            {
                                DateTime date = DateTime.ParseExact(value, "M/d/yyyy", null);
                                value = date.ToString("yyyy-MM-dd");
                            }
                            var cell = row.CreateCell(colIdx++);
                            cell.SetCellValue(value);
                        }
                    }
                }

                // 使用 NPOIMemoryStream 将 XSSFWorkbook 写入内存流
                byte[] fileData;
                using (var ms = new NPOIMemoryStream())
                {
                    workbook.Write(ms);
                    ms.Flush();
                    ms.Position = 0;
                    fileData = ms.ToArray();
                }

                // 返回 FileResult，提供内存流中的数据
                return File(fileData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "SK.xlsx");
            }
            #endregion

        }
    }
}