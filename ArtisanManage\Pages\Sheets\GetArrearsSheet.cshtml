@page
@model GetArrearsSheetModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html lang="en">
<head>
    <partial name="_SheetHead" model="Model.PartialViewModel" />
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxpopover.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdata.export.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.export.js"></script>
    <script type="text/javascript" src="~/Sheet/SheetExport.js?v=@Model.PartialViewModel.Version"></script>
    <script type="text/javascript" src="~/Sheet/GetArrearsSheet.js?v=@Model.PartialViewModel.Version"></script>
    <script src="~/js/Vue.js"></script>
    <link href="~/lib/element-ui/theme/index.css" rel="stylesheet" />
    <script src="~/lib/element-ui/index.min.js"></script>
    <script>
        var Href = '@Html.Raw(Model.ObsBucketLinkHref)'
    </script>
    <script>

        function divbox_show() {
            //$('#box').css("opacity", "1")
            $('#box').css("display", "block")
        }
        function divbox_hide() {
            //$('#box').css("opacity", "0")
            $('#box').css("display", "none")
        }


    </script>
    <style>
        #box {
            width: 550px;
            height: 250px;
            position: fixed;
            top: 27%;
            left: 30%;
            z-index: 9999;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: rgb(255, 255, 255);
            padding: 20px;
            text-align: center;
            box-shadow: 0px 0px 20px 5px rgba(0, 0, 0, 0.25);
            display: none;
            /*           opacity:0;
        */
        }
        /*       #addArrearSheets{
                    width:800px;
                    min-height:600px;
                    z-index:9999;
                    position:absolute;
                    left:50%;
                    top:50%;
                    margin-left:-400px;
                    margin-top:-300px;
                    border:0.5px solid black;
                     border-radius:10px;
                     background-color:white
                }*/
        #seller_id input {
            width: 149px !important;
            height: 100% !important;
        }

        #senders_id input {
            width: 149px !important;
            height: 100% !important;
        }

        #sheetno input {
            width: 149px !important;
            height: 100% !important;
        }

        .el-picker-panel__icon-btn {
            font-size: 12px;
            color: #303133;
            border: 0;
            background: 0 0;
            cursor: pointer;
            outline: 0;
            margin-top: 8px;
            width: 30px;
        }

    </style>
</head>

<body class='default' style="overflow:hidden;">

    <div id="divTitle" style="text-align:center;height:45px;">
        <label id="lblSheetTitle" style="font-weight:500;font-size:25px;">@Html.Raw(Model.SheetTitle)</label>
        <img id="imgState" style="display:none;position:fixed;top:0px;left:calc(50% - 150px);" src="" />
        <!--<div class="makeInfo" style="position:absolute;top:0; right:0px;width:330px;">
            <div><div><label>单号:</label></div> <div id="sheet_no"></div></div>
            <div><div><label>制单:</label></div> <div><span id="make_time"></span><span id="maker_name"></span></div></div>
            <div><div><label>审核:</label></div> <div><span id="approve_time"></span><span id="approver_name"></span></div></div>
        </div>-->
        <div class="makeInfo" style="position:absolute;top:5px; right:0px;">
            <div>
                <div><label>单号:</label></div>
                <div id="sheet_no"></div>
            </div>
            <div id="getOrder">
                <div><label>对账单:</label></div>
                <div id="order_sheet_no"></div>
                <div id="order_sheet_id" style="display:none"></div>
            </div>
            <div>
                <div><label>制单:</label></div>
                <div><span id="make_time"></span><span id="maker_name"></span></div>
            </div>
            <div>
                <div><label>审核:</label></div>
                <div><span id="approve_time"></span><span id="approver_name"></span></div>
            </div>
            <div>
                <div><label>复核:</label></div>
                <div><span id="review_time"></span><span id="reviewer_name"></span></div>
            </div>

        </div>

    </div>


    <div id="box" class="headtail" style="position:fixed">

        <div style="position: absolute; top: 113px; left: 115px">
            <button id="check" style="align-content:center;margin-top:45px;margin-left:20px;">确认</button>
            <button onclick="divbox_hide()" style="align-content:center;margin-top:45px;margin-left:75px">关闭</button>
        </div>
    </div>

    <div style="display:flex;padding-top:20px;justify-content:space-between; ">
        <div id="divHead" class="headtail" style="width:calc(100% - 120px);">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <div style="width:120px;">
            <button id="btnClearSheets" style="margin-left:0px;height:25px;width:45px;" class="margin">清空</button>
            <button id="btnAddSheets" style="margin-left: 5px; height:25px; width:45px;" class="margin">添加</button>

        </div>

    </div>



    <el-dialog title="添加欠款单据" id="addArrearSheets" style="display:none;" :visible.sync="popAddArrearSheets" width="1000px" v-cloak @@close="addArrearSheetsClose()">

        <el-form label-width="100%">
            <el-row :gutter="15" style=" display:flex">
                <el-col :span="8" style="display: flex; align-items: center;">
                    <span style="width: 120px;">交易日期:</span>
                    <el-date-picker v-model="dateRange"
                                    type="daterange"
                                    unlink-panels
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期">
                                    style="flex: 1;"
                    </el-date-picker>
                </el-col>
                <el-col :span="5" style="align-items: center; display: flex;">
                    <span style="width: 50px;">单号:</span>
                    <el-input placeholder="订单单号" v-model="addSheets.sheetNo" clearable></el-input>
                </el-col>
                <el-col :span="5" style="display:flex; align-items: center;">
                    <span style="width: 95px;">业务员:</span>
                    <el-select v-model="addSheets.sellerID" placeholder="业务员" clearable >
                        <el-option v-for="val in sellerList" :label="val.display" :key="val.value"
                                   :value="val.value+','+val.display">
                        </el-option>
                    </el-select>
                </el-col>
                <el-col :span="5" style="align-items: center; display:flex;">
                    <span style="width: 95px;">送货员:</span>
                    <el-select v-model="addSheets.sendersID" placeholder="送货员" clearable>
                        <el-option v-for="val in senderList" :label="val.display" :key="val.value"
                                   :value="val.value+','+val.display">
                        </el-option>
                    </el-select>
                </el-col>
            </el-row>
            <el-row style=" display:flex;margin-top:15px;">
                <el-col :span="22" style="text-align: right;">
                    <el-button type="danger" style="line-height:10px" @@click="getArrearSheets">查询</el-button>
                </el-col>
            </el-row>

            <el-row>
                <el-col>
                    <div style="display:flex">
                        <h3>
                            单据信息
                        </h3>
                        <div style="line-height:54px;margin-left:10px;">({{sup_name}})</div>
                    </div>

                    <el-table :data="needAddArrearSheets" style="width: 100%; padding:0 0 0 5px"
                              height="350px">
                        <el-table-column label="" width="60px">
                            <template slot="header" slot-scope="scope">
                                <el-checkbox @@change='allChooseClick'>全选</el-checkbox>
                            </template>
                            <template slot-scope="scope">
                                <div style="display:flex">
                                    <span>{{scope.$index+1}}.</span>
                                    <div id="checkboxes"><input type="checkbox" v-model="scope.row.isChecked" @@click="getCheckedSheets(scope.row,scope.row.isChecked)" /></div>
                                </div>


                            </template>
                        </el-table-column>
                        <el-table-column prop="mm_sheet_no" label="订单号" width="160px">
                        </el-table-column>
                        <el-table-column prop="mm_sheet_time" label="交易时间" width="160px">
                        </el-table-column>
                        <el-table-column prop="mm_sheet_type_name" label="类型">
                        </el-table-column>
                        <el-table-column prop="sheet_amount" label="单据金额">
                        </el-table-column>
                        <el-table-column prop="left_amount" label="尚欠">
                        </el-table-column>
                        <el-table-column prop="paid_amount" label="已支付">
                        </el-table-column>
                        <el-table-column prop="disc_amount" label="优惠">
                        </el-table-column>
                        <el-table-column prop="mm_make_brief" label="备注">
                        </el-table-column>

                    </el-table>
                </el-col>
            </el-row>
            <el-row v-if="needAddArrearSheets.length>0">
                <el-col>
                    <div style="display:flex">
                        <div>共{{needAddArrearSheets.length}}条记录;</div>
                        <div v-if="hasCheckedSheets">已选:{{checkedSheetsCount}}条记录,欠款:￥{{checkedSheetsLeftAmount}}</div>
                    </div>
                </el-col>
            </el-row>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button style="line-height: 0;" @@click="addArrearSheetsClose()">退 出</el-button>
            <el-button style="line-height: 0;" type="danger" plain @@click="btnAddArrearSheets_click">确 定</el-button>
        </span>
    </el-dialog>


    <div id="jqxgrid" style="margin-left:10px; position:static;width:100%;height:100%;border-bottom-color:#dedede;"></div>

    <div style="display:flex;">
        <div id="divTail" class="headtail" style="margin-top:10px;">

            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <div style="display:flex;max-width:250px;margin-top:15px; height:20px;position:relative;">
            <div id="div_get_account" style="display:block;max-width:200px;width:auto;height:auto;">
            </div>
            <div id="btnMoreAcctInfo" style="display:none;cursor:pointer;font-size:14px; margin-top:1px; top:0px; right:0px;width:50px;height:30px;color:#46f;">
                更多
            </div>
        </div>
    </div>
    <div id="divButtons" style="text-align:center;z-index:999;">
        <button type="button" id="btnSave">保存</button>
        <button type="button" id="btnApprove" class="main-button">审核</button>
        <button id="btnReview" type="button" style="margin-right:40px;background-color:#ffc8e0;" class="main-button">复核</button>
        <button id="btnRed" type="button" disabled>红冲</button>
        <button id="btnRedAndChange" type="button" style="margin-right:40px;">冲改</button>
        <button id="btnPrint" type="button">打印</button>

        <button id="btnCopy" type="button">复制</button>
        <button id="btnAdd" type="button">新增</button>

        <button id="btnMore" type="button" style="margin-right:0; border-right:none; border-radius: 3px 0px 0px 3px">更多</button><button id="btnMoreChooseSheets" class="btnright" style="width:30px;margin-right:20px;margin-left:0px;margin-top:30px;border-radius: 0px 3px 3px 0px">
            <img src="~/PrintTemplate/img/triangle.svg" style="margin-top: -1px; width: 14px; display: inline-block;vertical-align: middle;" />
        </button>
        <div id="popoverMore" style="position:absolute;display:none;font-size:15px;">
            <div style="width:100px;height:100px;">
                <div style="margin:10px;">
                    @* btnGetArrearsSheetExportExcel_click()可以导出带一些单据表头信息的收款单 *@
                    <button style="border:none;margin-top:10px;" onclick="btnGetArrearsSheetExportExcel_click()">导出Excel</button>
                    <button id="btnAppendix" style="border:none;margin-top:10px;">附件</button>
                    @* <button style="border:none;margin-top:10px;" onclick="btnExportExcel()">导出Excel</button> *@
                    @*<button style="border:none;margin-top:10px;" onclick="btnItemInfoSyncTest_click()">商品档案同步测试</button>*@
                </div>
            </div>
        </div>
        <button id="btnDelete" type="button" disabled>删除</button>
        <button id="btnClose" type="button">关闭</button>

    </div>

    <div id="popClient" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择客户</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    <div id="popItem" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择商品</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    <div id="popAppendix" style="display:none;">
        <div id="appendixCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">上传附件</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

</body>
<script>
    var app = new Vue({
        el: "#addArrearSheets",
        data() {
            return {
                sheetType: 'SK',
                GetSheetType: "GetSaleSheets",
                supcust_id: '',
                supcust_flag: 'C',
                popAddArrearSheets: false,
                operKey: g_operKey,
                senderList: [],
                sellerList: [],
                addSheets: {
                    sheetNo: '',
                    sellerID: '',
                    sendersID: '',
                    startTime: '',
                    endTime: '',
                },
                dateRange: [],
                getSellerID: '',
                getSenderID: '',
                existArrearSheets: [],
                needAddArrearSheets: [],
                sup_name: '',
                checkedSheetsCount: 0,
                checkedSheetsLeftAmount: 0,
                hasCheckedSheets: false,

            }
        },
        watch: {
            dateRange(newVal) {
                if (newVal && newVal.length ===2) {
                    this.addSheets.startTime = newVal[0];
                    this.addSheets.endTime = newVal[1];
                } else {
                    this.addSheets.startTime = '';
                    this.addSheets.endTime = '';
                }
            }
        },
        mounted() {
            let yy = new Date().getFullYear();
            let mm = new Date().getMonth() + 1;
            let dd = new Date().getDate();
            this.addSheets.endTime = yy + '-' + mm + '-' + dd
        },
        methods: {
            allChooseClick(isChecked) {
                console.log(isChecked)
                for (var index = 0; index < this.needAddArrearSheets.length; index++) {
                    var row = this.needAddArrearSheets[index];
                    row.isChecked = isChecked
                    this.getCheckedSheets(row, isChecked,false)
                }
                this.$forceUpdate()
            },
            getCheckedSheets(checkedRow, isCheck,fromSingleChoose=true) {
                var count = 0
                if (fromSingleChoose) {
                    checkedRow.isChecked = (isCheck == false)
                }
                var hasChecked = false
                var totalLeft = 0
                this.needAddArrearSheets.forEach(row => {
                    if (row.isChecked) {
                        count += 1
                        totalLeft += Number(row.left_amount)
                        hasChecked = true
                    }
                })
                this.hasCheckedSheets = hasChecked
                this.checkedSheetsCount = count
                this.checkedSheetsLeftAmount = totalLeft
            },
            btnAddArrearSheets_click() {
                if (this.needAddArrearSheets.length === 0) {
                    bw.toast('无可添加的欠款单子', 3000)
                    return
                }
                var rows = new Array()
                if (this.existArrearSheets.length > 0) {
                    this.existArrearSheets.forEach(row => {
                        rows.push(row)
                    })
                }
                var hasChecked = false
                this.needAddArrearSheets.forEach(row => {
                    if (row.isChecked) {
                        rows.push(row)
                        hasChecked = true
                    }
                })
                if (hasChecked) {
                    var preCount = $('#jqxgrid').jqxGrid('getrows').length;
                    var currCount = rows.length
                    $('#jqxgrid').jqxGrid('clear');
                    if (currCount < preCount) {
                        for (var i = currCount - 1; i < preCount; i++) {
                            rows.push({})
                        }
                    }
                    $('#jqxgrid').jqxGrid('addrow', null, rows);
                    updateTotalAmount();
                    this.addArrearSheetsClose()
                } else {
                    bw.toast('请先勾选要添加的单子', 3000)
                    return
                }

            },
            getGetSellerInfo() {
                var self = this
                $.ajax({
                    url: `/api/OrderManagePrint/GetSellerInfo?operKey=${self.operKey}`,
                    type: 'GET',
                    contentType: 'application/json',
                    success: function (res) {
                        //self.data = res;
                        self.sellerList.splice(0);
                        res.forEach(el => {
                            self.sellerList.push(el);
                            if (self.getSellerID) {
                                if (el.value === self.getSellerID) {
                                    self.addSheets.sellerID = el.value + ',' + el.display
                                }
                            }
                        })
                    },
                    error: function (response, ajaxOptions, thrownError) {
                        console.log('error' + response);
                    }
                });
            },
            getGetSenderInfo() {
                var self = this
                $.ajax({
                    url: `/api/OrderManagePrint/GetSenderInfo?operKey=${self.operKey}`,
                    type: 'GET',
                    contentType: 'application/json',
                    success: function (res) {
                        //self.data = res;
                        self.senderList.splice(0);
                        res.forEach(el => {
                            self.senderList.push(el);
                            if (self.getSenderID) {
                                if (el.value === self.getSenderID) {
                                    self.addSheets.sendersID = el.value + ',' + el.display
                                }
                            }
                        })
                    },
                    error: function (response, ajaxOptions, thrownError) {
                        console.log('error' + response);
                    }
                });
            },
            formatDate(date, fmt) {
                if (typeof date == 'string') {
                    date = new Date(date)
                }
                if (/(y+)/.test(fmt)) {
                    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
                }
                let o = {
                    'M+': date.getMonth() + 1,
                    'd+': date.getDate(),
                    'h+': date.getHours(),
                    'm+': date.getMinutes(),
                    's+': date.getSeconds()
                }
                for (let k in o) {
                    if (new RegExp(`(${k})`).test(fmt)) {
                        let str = o[k] + ''
                        fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? str : this.padLeftZero(str))
                    }
                }
                return fmt
            },
            padLeftZero(str) {
                return ('00' + str).substr(str.length)
            },
            getArrearSheets() {
                if (!this.supcust_id) {
                    bw.toast('没有获取客户', 3000)
                    return
                }
                if (!this.addSheets.startTime) {
                    bw.toast('请输入开始时间', 3000)
                    return
                }
                if (!this.addSheets.endTime) {
                    bw.toast('请输入结束时间', 3000)
                    return
                }
                var seller_id = this.addSheets.sellerID
                var senders_id = this.addSheets.sendersID
                if (seller_id) {
                    var sellerArr = seller_id.split(',')
                    seller_id = sellerArr[0]
                }
                if (senders_id) {
                    var senderArr = senders_id.split(',')
                    senders_id = senderArr[0]
                }

                var startDate = this.formatDate(this.addSheets.startTime, "yyyy-MM-dd")
                startDate += " 00:00:00"
                var endDate = this.formatDate(this.addSheets.endTime, "yyyy-MM-dd")
                endDate += " 23:59:59"
                let data = {
                    operKey: this.operKey,
                    supcustID: this.supcust_id,
                    sellerId: seller_id,
                    senderID: senders_id,
                    startDate: startDate,
                    endDate: endDate,
                    sheetno: this.addSheets.sheetNo,
                    supcust_flag: this.supcust_flag,
                    sheetType: this.sheetType,
                }
                var self = this
                $.ajax({
                    url: `/AppApi/AppSheetGetArrears/GetALLsheets`,
                    type: 'GET',
                    contentType: 'application/json',
                    data: data,
                    success: function (data) {
                        if (data.result === 'OK') {
                            self.existArrearSheets = []
                            self.needAddArrearSheets = []
                            var preRows = $('#jqxgrid').jqxGrid('getrows')
                            var existRows = new Array();
                            var needAddRows = new Array()
                            for (var i = 0; i < preRows.length; i++) {
                                var pRow = $('#jqxgrid').jqxGrid('getrowdata', i);
                                if (pRow.mm_sheet_id) {
                                    existRows.push(pRow)
                                }
                            }
                            if (existRows.length > 0) self.existArrearSheets = existRows
                            data.data.forEach(nRow => {
                                nRow.isChecked = false
                                nRow.disc_amount = toMoney(nRow.disc_amount);
                                nRow.left_amount = toMoney(nRow.left_amount);
                                nRow.paid_amount = toMoney(nRow.paid_amount);
                                nRow.sheet_amount = toMoney(nRow.sheet_amount);
                                var exist = false
                                if (existRows.length > 0) {
                                    existRows.forEach(pRow => {
                                        if (nRow.mm_sheet_id === pRow.mm_sheet_id) {
                                            exist = true
                                        }
                                    })
                                }
                                if (!exist) needAddRows.push(nRow)
                            })
                            if (needAddRows.length > 0) self.needAddArrearSheets = needAddRows
                        }
                    }
                });    
            },
            onRowAddArrearSheets() {
                var forPayOrGet = getQueryVariable("forPayOrGet")
                var GetSheetType = "GetSaleSheets";
                var sheetType = "SK"
                if (forPayOrGet == "true") {
                    GetSheetType = "GetBuySheets";
                    sheetType = "FK"
                }

                var supcust_id = $('#supcust_id').jqxInput('val').value
                if (supcust_id) {
                    var supcust_flag = $('#supcust_id').jqxInput('val').sf
                    if (!supcust_flag) {
                        if (GetSheetType == "GetBuySheets") supcust_flag = "S"
                    }
                } else {
                    bw.toast('请先选择客户/供应商', 3000)
                    return false
                }
                this.sup_name = $('#supcust_id').val().label;
                var seller_id = $('#seller_id').val().value;
                if (seller_id) this.getSellerID = seller_id
                var senders_id = $('#senders_id').val().value;
                if (senders_id) this.getSenderID = senders_id
                var startDate = $('#jqxgrid').jqxGrid('getrows')[0].mm_sheet_time;  //第一张单据时间
                if (startDate) this.addSheets.startTime = startDate
                var endDate = $('#endDay').val()
                if (endDate) this.addSheets.endTime = endDate
                var sheetno = $('#sheetno').val();
                if (sheetno) this.addSheets.sheetNo = sheetno
                this.sheetType = sheetType
                this.GetSheetType = GetSheetType
                this.supcust_flag = supcust_flag
                this.supcust_id = supcust_id
                setTimeout(() => {
                    this.getGetSellerInfo()
                    this.getGetSenderInfo()
                }, 100)
                this.popAddArrearSheets = true
                return this.popAddArrearSheets
            },
            addArrearSheetsClose() {
                this.popAddArrearSheets = false
                this.needAddArrearSheets = []
                this.existArrearSheets = []
                this.checkedSheetsCount = 0
                this.checkedSheetsLeftAmount = 0
                this.hasCheckedSheets = false
            },
        }
    })
    $("#btnAddSheets").click(function () {
        var isShow = app.onRowAddArrearSheets()
        if (isShow) $("#addArrearSheets").show()
    })
    $("#btnClearSheets").click(function () {
        var rows = $('#jqxgrid').jqxGrid('getrows')
        app.addSheets.startTime = rows[0].mm_sheet_time
        rows.forEach(row => {
            for (var k in row) {
                row[k] = ''
            }
        })
        // $('#jqxgrid').jqxGrid('clear', null, rows);//只需要一次填充

        //计算下方金额
        updateTotalAmount();
        $('#jqxgrid').jqxGrid('updategrid')
    })


</script>
</html>
