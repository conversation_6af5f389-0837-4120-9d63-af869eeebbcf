﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Net.Http;
using System.Threading.Tasks;
using static ArtisanManage.WebAPI.PayBillController;
using TZBClient;
using Renci.SshNet.Common;
using ArtisanManage.MyJXC;
using System.ComponentModel.Design;
using Jurassic.Library;

namespace ArtisanManage.WebAPI
{
    [Route("AppApi/[controller]/[action]")]
    public class TzbankController : BaseController
    {
        #region Predefs
        private readonly IHttpClientFactory _httpClientFactory;

        public TzbankController(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }
        #endregion

        #region Constants
        // 数据库存取相关参数
        public const int PayChannelId = 4;
        #endregion

        #region Public: 获取微信支付所需的ExtraData接口 (Static)
        public static async Task<CallResult> GetWechatExtraData(CMySbCommand cmd,
            string companyId, string sheetId, string sheetType, decimal payAmount, string name, string phone)
        {
            // 1. 进行基础校验
            string err = string.Empty;
            if (sheetId.IsInvalid())
                err += "单据未审核,无法付款;";
            if (sheetType.IsInvalid())
                err += "单据类型未指定;";
            if (payAmount <= 0)
                err += "付款金额不能为0;";
            if (err.Length > 0)
                return new CallResult("Error", err);

            // 2. 向数据库通信获得订单号
            // 一个单据对应多笔订单时,仅取最新的一笔,从而规避掉旧有订单
            var getSheetMainTable = PayBillController.PayBillGetSheetMainTable(sheetType);
            if (!getSheetMainTable.IsOK) return getSheetMainTable;
            var _sql_queue = new SQLQueue(cmd);
            var pay_order_info = new List<ExpandoObject>();
            string sql = $@"
                SELECT
                    s.pay_bill_id, b.pay_qrcode, b.bill_status
                FROM
                    {getSheetMainTable.data} s
                LEFT JOIN
                    pay_bill b on s.pay_bill_id = b.bill_id
                WHERE
                    s.company_id = {companyId}
                    and s.sheet_id = '{sheetId}';
            ";
            _sql_queue.Enqueue("load_bill", sql);
            var _dr = await _sql_queue.ExecuteReaderAsync();
            while (_sql_queue.Count > 0)
            {
                var sqlName = _sql_queue.Dequeue();
                if (sqlName == "load_bill")
                {
                    pay_order_info = CDbDealer.GetRecordsFromDr(_dr, false);
                }
            }
            _dr.Close();
            _sql_queue.Clear();

            // 3. 根据获取的订单信息决定接下来的操作
            if (pay_order_info is null || pay_order_info.Count == 0)
            {
                // 没有任何关联订单,代表是新单据,此时新建订单即可
                var create_result = await DoBillCreate(cmd, companyId, payAmount, sheetType, sheetId, name, phone);
                return create_result;
            }
            else
            {
                string billStatus = ((dynamic)pay_order_info[0])?.bill_status ?? "";

                // 已支付的场合,返回成功结果
                if (billStatus == BillStatus.Paid)
                {
                    return new CallResult("OK", "已支付");
                }


                // 订单被取消、Redis过期或是billStatus未定义(无订单)的场合,新建一笔订单
                var create_result = await DoBillCreate(cmd, companyId, payAmount, sheetType, sheetId, name, phone);
                return create_result;
            }
        }
        #endregion

        #region Public: 获取微信支付所需的三个参数接口 (Static)
        public static async Task<CallResult> GetWechatPayCode(CMySbCommand cmd,
            string companyId, string sheetId, string sheetType, decimal payAmount, string name, string phone)
        {
            // 1. 进行基础校验
            string err = string.Empty;
            if (sheetId.IsInvalid())
                err += "单据未审核,无法付款;";
            if (sheetType.IsInvalid())
                err += "单据类型未指定;";
            if (payAmount <= 0)
                err += "付款金额不能为0;";
            if (err.Length > 0)
                return new CallResult("Error", err);

            // 2. 向数据库通信获得订单号
            // 一个单据对应多笔订单时,仅取最新的一笔,从而规避掉旧有订单
            var getSheetMainTable = PayBillController.PayBillGetSheetMainTable(sheetType);
            if (!getSheetMainTable.IsOK) return getSheetMainTable;
            var _sql_queue = new SQLQueue(cmd);
            var pay_order_info = new List<ExpandoObject>();
            string sql = $@"
                SELECT
                    s.pay_bill_id, b.pay_qrcode, b.bill_status
                FROM
                    {getSheetMainTable.data} s
                LEFT JOIN
                    pay_bill b on s.pay_bill_id = b.bill_id
                WHERE
                    s.company_id = {companyId}
                    and s.sheet_id = '{sheetId}';
            ";
            _sql_queue.Enqueue("load_bill", sql);
            var _dr = await _sql_queue.ExecuteReaderAsync();
            while (_sql_queue.Count > 0)
            {
                var sqlName = _sql_queue.Dequeue();
                if (sqlName == "load_bill")
                {
                    pay_order_info = CDbDealer.GetRecordsFromDr(_dr, false);
                }
            }
            _dr.Close();
            _sql_queue.Clear();

            // 3. 根据获取的订单信息决定接下来的操作
            if (pay_order_info is null || pay_order_info.Count == 0)
            {
                // 没有任何关联订单,代表是新单据,此时新建订单即可
                var create_result = await DoBillCreate2(cmd, companyId, payAmount, sheetType, sheetId, name, phone);
                return create_result;
            }
            else
            {
                string billStatus = ((dynamic)pay_order_info[0])?.bill_status ?? "";

                // 已支付的场合,返回成功结果
                if (billStatus == BillStatus.Paid)
                {
                    return new CallResult("Paid", "已支付");
                }


                // 订单被取消、Redis过期或是billStatus未定义(无订单)的场合,新建一笔订单
                var create_result = await DoBillCreate2(cmd, companyId, payAmount, sheetType, sheetId, name, phone);
                return create_result;
            }
        }
        #endregion

        #region Public: 从台州银行支付密钥 (Static)
        [HttpPost]
        public static async Task<CallResult> GetPayMerchantInfo(CMySbCommand cmd, string companyId, string merchantId)
        {
            
            try
            {
                var _sql_queue = new SQLQueue(cmd);
                var pay_merchant_info = new List<ExpandoObject>();
                string sql = $@"
                    SELECT
                        company_id,merchant_id,merchant_name, private_certi_key, platform_public_key,mypublic_key,app_key,app_secret
                    FROM
                        pay_merchant
                    WHERE
                        1=1
                        and company_id = @companyId
                        and merchant_id = @merchantId
                ";
                cmd.Parameters.AddWithValue("@companyId", companyId);
                cmd.Parameters.AddWithValue("@merchantId", merchantId);
                _sql_queue.Enqueue("load_pay_merchant", sql);
                var _dr = await _sql_queue.ExecuteReaderAsync();
                while (_sql_queue.Count > 0)
                {
                    var sqlName = _sql_queue.Dequeue();
                    if (sqlName == "load_pay_merchant")
                    {
                        pay_merchant_info = CDbDealer.GetRecordsFromDr(_dr, false);
                    }
                }
                _dr.Close();
                _sql_queue.Clear();

                //string privateCertiKey = ((dynamic)pay_merchant_info[0])?.private_certi_key ?? "";
                //string platformPublicKey = ((dynamic)pay_merchant_info[0])?.platform_public_key ?? "";
                return new CallResult("OK","SUCCESS!", "");
            }
            catch (Exception ex)
            {
                NLogger.Error(ex.ToString());
                return new CallResult("Error", ex.Message);
            }
        }
        #endregion

        #region Public: 从台州银行服务器获取支付订单状态 (Static)
        public static async Task<CallResult> GetPaymentStatus(CMySbCommand cmd,
            string tradeNo, string merchantId)
        {
            try
            {
                var _sql_queue = new SQLQueue(cmd);
                var pay_order_info = new List<ExpandoObject>();
                string sql = $@"
                    SELECT
                        order_flow_no, refund_trade_no
                    FROM
                        pay_bill
                    WHERE
                        trade_no = @tradeNo
                ";
                cmd.Parameters.AddWithValue("@tradeNo", tradeNo);
                _sql_queue.Enqueue("load_order_flow_no", sql);
                var _dr = await _sql_queue.ExecuteReaderAsync();
                while (_sql_queue.Count > 0)
                {
                    var sqlName = _sql_queue.Dequeue();
                    if (sqlName == "load_order_flow_no")
                    {
                        pay_order_info = CDbDealer.GetRecordsFromDr(_dr, false);
                    }
                }
                _dr.Close();
                _sql_queue.Clear();
                string orderFlowNo = ((dynamic)pay_order_info[0])?.order_flow_no ?? "";
                string refundTradeNo = ((dynamic)pay_order_info[0])?.refund_trade_no ?? "";
                dynamic tokenResponse = await TzbankApi.GetToken();
                string tokenResponseCode = tokenResponse?.retType ?? "";
                if (tokenResponseCode == "E")
                {
                    string tokenResponseMsg = tokenResponse?.retMsg ?? "未知错误";
                    NLogger.Error($"台州银行支付.获取token失败 - " + tokenResponseMsg);
                    return new CallResult("Error", tokenResponseMsg);
                }
                string token = tokenResponse?.token ?? "";
                string randomSec = tokenResponse?.randomSec ?? "";
                if (token.IsInvalid() || randomSec.IsInvalid())
                {
                    NLogger.Error($"台州银行支付.获取token失败 - " + "token为空");
                    return new CallResult("Error", "token获取失败");
                }

                if (refundTradeNo != null && refundTradeNo.IsValid())
                {
                    Console.WriteLine($"refundTradeNo: {refundTradeNo}");
                    var queryResponse = await TzbankApi.GetRefundBillStatus(refundTradeNo, DateTime.Now.ToString("yyyyMMddHHmmss"), randomSec, token);
                    string responseCode = queryResponse?.retType ?? "";
                    string responseMsg = queryResponse?.retMsg ?? "";
                    if (responseCode == "S")
                    {
                        string tradeStatus = queryResponse?.body?.refundOrdStatus ?? "";
                        var paymentStatus = TzbankApi.TzbankTradeStatus.ParseToDbStatusRefund(tradeStatus);
                        return new CallResult("OK", "", paymentStatus);
                    }
                    else
                    {
                        return new CallResult("Failed", responseMsg);
                    }
                }
                else
                {
                    Console.WriteLine($"orderFlowNo: {orderFlowNo}");
                    var queryResponse = await TzbankApi.GetPayOrderStatus(orderFlowNo, randomSec, token);
                    string responseCode = queryResponse?.retType ?? "";
                    string responseMsg = queryResponse?.retMsg ?? "";
                    if (responseCode == "S")
                    {
                        string tradeStatus = queryResponse?.body?.status ?? "";
                        var paymentStatus = TzbankApi.TzbankTradeStatus.ParseToDbStatusTrade(tradeStatus);
                        return new CallResult("OK", "", paymentStatus);
                    }
                    else
                    {
                        return new CallResult("Failed", responseMsg);
                    }
                }
            }
            catch (Exception ex)
            {
                NLogger.Error(ex.ToString());
                return new CallResult("Error", ex.Message);
            }
        }
        #endregion

        #region Public: 订单退款接口 (Static)
        [HttpPost]
        public static async Task<CallResult> RefundBill(CMySbCommand cmd,
            string companyId, string sheetId, string sheetType)
        {
            // 初始化台州银行密钥
            await TzbankApi.InitMerchantKeysAsync(cmd, companyId);
            if (sheetId.IsInvalid())
                return new CallResult("Error", "单据未保存");
            if (sheetType.IsInvalid())
                return new CallResult("Error", "单据类型错误");

            var _sql_queue = new SQLQueue(cmd);
            var pay_order_info = new List<ExpandoObject>();
            string sql = $@"
                SELECT
                    bill_id, trade_no, amount
                FROM
                    pay_bill
                WHERE
                    company_id = {companyId}
                    and pay_channel = {PayChannelId}
                    and sheet_id = '{sheetId}'
                    and sheet_type = '{sheetType}'
                    and bill_status = '{BillStatus.Paid}'
                ORDER BY 
                    create_time DESC;
            ";
            _sql_queue.Enqueue("load_bill", sql);
            var _dr = await _sql_queue.ExecuteReaderAsync();
            while (_sql_queue.Count > 0)
            {
                var sqlName = _sql_queue.Dequeue();
                if (sqlName == "load_bill")
                {
                    pay_order_info = CDbDealer.GetRecordsFromDr(_dr, false);
                }
            }
            _dr.Close();
            _sql_queue.Clear();

            if (pay_order_info is null || pay_order_info.Count == 0)
                return new CallResult("Error", "无付款记录或已经被退款");

            string billId = ((dynamic)pay_order_info[0])?.bill_id ?? "";
            string tradeNo = ((dynamic)pay_order_info[0])?.trade_no ?? "";
            decimal paidAmount = CPubVars.ToDecimal(((dynamic)pay_order_info[0])?.amount ?? "0");
            if (billId.IsInvalid() || tradeNo.IsInvalid())
                return new CallResult("Error", "订单信息损坏");
            if (paidAmount <= 0)
                return new CallResult("Error", "订单金额为0,无法退款");

            DateTime billHappenTime = DateTime.Now;
            string refundTradeNo = TzbankApi.TzbankRefundNo.Encrypt(sheetId, sheetType, billId, companyId, billHappenTime);

            dynamic tokenResponse = await TzbankApi.GetToken();
            string tokenResponseCode = tokenResponse?.retType ?? "";
            if (tokenResponseCode == "E")
            {
                string tokenResponseMsg = tokenResponse?.retMsg ?? "未知错误";
                NLogger.Error($"台州银行支付.获取token失败 - " + tokenResponseMsg);
                return new CallResult("Error", tokenResponseMsg);
            }
            string token = tokenResponse?.token ?? "";
            string randomSec = tokenResponse?.randomSec ?? "";
            if (token.IsInvalid() || randomSec.IsInvalid())
            {
                NLogger.Error($"台州银行支付.获取token失败 - " + "token为空");
                return new CallResult("Error", "token获取失败");
            }

            var refundResult = await TzbankApi.RefundBill(tradeNo, refundTradeNo, DateTime.Now.ToString("yyyyMMddHHmmss"), paidAmount, token, randomSec);
            string responseCode = refundResult?.retType ?? "";
            string responseMsg = refundResult?.retMsg ?? "";
            if (responseCode == "S")
            {
                sql = $@"
                UPDATE
                    pay_bill
                SET
                    refund_trade_no = '{refundTradeNo}'
                WHERE
                    bill_id = {billId} and company_id = {companyId};";
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
                var dbResult = await EditBillStatus(cmd, PayChannelId, billId, BillStatus.Returned);
                return dbResult;
            }
            else
            {
                NLogger.Info($"台州银行错误:公司{companyId}的{billId}号订单退款失败。响应信息:" + JsonConvert.SerializeObject(refundResult));
                return new CallResult("Failed", responseMsg);
            }
        }
        #endregion

        #region Prvitate: 创建订单 (Static)

        private static async Task<CallResult> DoBillCreate(CMySbCommand cmd,
            string companyId, decimal payAmount, string sheetType, string sheetId, string name, string phone)
        {
            // 初始化台州银行密钥
            await TzbankApi.InitMerchantKeysAsync(cmd, companyId);
            // (1) 重新插入一个pay_bill并获取单号
            var getSheetMainTable = PayBillController.PayBillGetSheetMainTable(sheetType);
            if (!getSheetMainTable.IsOK) return getSheetMainTable;
            string happenTime = CPubVars.GetDateText(DateTime.Now);
            var bill_info = new List<ExpandoObject>();
            var merchant_info = new List<ExpandoObject>();
            var sql = $@"
                INSERT INTO
                    pay_bill
                    (company_id, create_time, bill_status, amount,
                        sheet_type, sheet_id, pay_channel)
                VALUES
                    ({companyId}, '{happenTime}', '{BillStatus.Unpaid}', {payAmount},
                        '{sheetType}', {sheetId}, {PayChannelId})
                RETURNING
                    bill_id;
            ";
            cmd.ActiveDatabase = "";
            var _sql_queue = new SQLQueue(cmd);
            _sql_queue.Enqueue("create_bill", sql);

            // (2) 获取用户的基础支付信息
            sql = $@"
                SELECT 
                    merchant_id, merchant_name
                FROM
                    pay_merchant
                WHERE
                    company_id = {companyId} and channel_id = {PayChannelId};
            ";
            _sql_queue.Enqueue("get_merchant_info", sql);

            // (3) 开始数据库操作
            var _dr = await _sql_queue.ExecuteReaderAsync();
            while (_sql_queue.Count > 0)
            {
                var sqlName = _sql_queue.Dequeue();
                if (sqlName == "create_bill")
                    bill_info = CDbDealer.GetRecordsFromDr(_dr, false);
                else if (sqlName == "get_merchant_info")
                    merchant_info = CDbDealer.GetRecordsFromDr(_dr, false);
            }
            _dr.Close();
            _sql_queue.Clear();

            // (4) 执行最终校验
            var err = string.Empty;
            if (bill_info is null || bill_info.Count <= 0)
                err += "数据库出错,请联系技术人员";
            if (merchant_info is null || merchant_info.Count <= 0)
                err += "未开户";
            if (err.Length > 0)
                return new CallResult("Error", err);

            // (5) 组装其他参数
            string billId = ((dynamic)bill_info[0]).bill_id;
            string merchantId = ((dynamic)merchant_info[0]).merchant_id;
            DateTime billHappenTime = DateTime.Now;
            string tradeNo = TzbankApi.TzbankTradeNo.Encrypt(sheetId, sheetType, billId, companyId, billHappenTime);

            // (6) 获取token和randomSec
            dynamic tokenResponse = await TzbankApi.GetToken();
            string tokenResponseCode = tokenResponse?.retType ?? "";
            if(tokenResponseCode == "E")
            {
                string tokenResponseMsg = tokenResponse?.retMsg ?? "未知错误";
                NLogger.Error($"台州银行支付.获取token失败 - " + tokenResponseMsg);
                return new CallResult("Error", tokenResponseMsg);
            }
            string token = tokenResponse?.token ?? "";
            string randomSec = tokenResponse?.randomSec ?? "";
            if (token.IsInvalid() || randomSec.IsInvalid())
            {
                NLogger.Error($"台州银行支付.获取token失败 - " + "token为空");
                return new CallResult("Error", "token获取失败");
            }

            // (7) 生成订单
            dynamic payBillResponse = await TzbankApi.PayBill(tradeNo, payAmount, merchantId, companyId, name, phone, token, randomSec);

            string payBillResponseCode = payBillResponse?.retType ?? "";
            if(payBillResponseCode == "E")
            {
                string payBillResponseMsg = payBillResponse?.retMsg ?? "未知错误";
                NLogger.Error($"台州银行支付.生成{companyId}的订单失败 - " + payBillResponseMsg);
                return new CallResult("Error", payBillResponseMsg);
            }
            string orderFlowNo = payBillResponse?.body?.orderFlowNo ?? "";
            string orderNo = payBillResponse?.body?.orderNo ?? "";

            //（8）获取微信小程序支付授权码
            dynamic WakeupCashierPayResponse = await TzbankApi.WakeupCashierPay(companyId, orderFlowNo, token, randomSec);
            string WakeupCashierPayResponseCode = WakeupCashierPayResponse?.retType ?? "";
            
            var wechatExtraDataJson = WakeupCashierPayResponse?.body;
            string wechatExtraData = JsonConvert.SerializeObject(wechatExtraDataJson);

            if (WakeupCashierPayResponseCode == "E" || wechatExtraData.IsInvalid())
            {
                string WakeupCashierPayResponseMessage = WakeupCashierPayResponse?.retMsg ?? "未知错误";
                NLogger.Error($"台州银行支付.{companyId}的订单获取小程序支付授权码失败 - " + WakeupCashierPayResponseMessage);
                return new CallResult("Error", WakeupCashierPayResponseMessage);
            }

            //（9）创建订单
            string redisKey = $"_GetWechatExtraData_Tzbank_{tradeNo}";
            await RedisHelper.SetAsync(redisKey, wechatExtraData, 595);
            sql = $@"
                UPDATE
                    pay_bill
                SET
                    pay_qrcode = '{redisKey}',
                    trade_no = '{tradeNo}',
                    order_flow_no = '{orderFlowNo}'
                WHERE
                    bill_id = {billId} and company_id = {companyId};"
            + $@"
                UPDATE
                    {getSheetMainTable.data}
                SET
                    pay_bill_id = '{billId}'
                WHERE
                    sheet_id = {sheetId} and company_id = {companyId};
            ";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();

            //（10）返回结果
            return new CallResult("OK", "", wechatExtraData);
        }



        // 用于wechatPayCode调用
        private static async Task<CallResult> DoBillCreate2(CMySbCommand cmd,
            string companyId, decimal payAmount, string sheetType, string sheetId, string name, string phone)
        {
            // 初始化台州银行密钥
            await TzbankApi.InitMerchantKeysAsync(cmd, companyId);
            // (1) 重新插入一个pay_bill并获取单号
            var getSheetMainTable = PayBillController.PayBillGetSheetMainTable(sheetType);
            if (!getSheetMainTable.IsOK) return getSheetMainTable;
            string happenTime = CPubVars.GetDateText(DateTime.Now);
            var bill_info = new List<ExpandoObject>();
            var merchant_info = new List<ExpandoObject>();
            var sql = $@"
                INSERT INTO
                    pay_bill
                    (company_id, create_time, bill_status, amount,
                        sheet_type, sheet_id, pay_channel)
                VALUES
                    ({companyId}, '{happenTime}', '{BillStatus.Unpaid}', {payAmount},
                        '{sheetType}', {sheetId}, {PayChannelId})
                RETURNING
                    bill_id;
            ";
            cmd.ActiveDatabase = "";
            var _sql_queue = new SQLQueue(cmd);
            _sql_queue.Enqueue("create_bill", sql);

            // (2) 获取用户的基础支付信息
            sql = $@"
                SELECT 
                    merchant_id, merchant_name
                FROM
                    pay_merchant
                WHERE
                    company_id = {companyId} and channel_id = {PayChannelId};
            ";
            _sql_queue.Enqueue("get_merchant_info", sql);

            // (3) 开始数据库操作
            var _dr = await _sql_queue.ExecuteReaderAsync();
            while (_sql_queue.Count > 0)
            {
                var sqlName = _sql_queue.Dequeue();
                if (sqlName == "create_bill")
                    bill_info = CDbDealer.GetRecordsFromDr(_dr, false);
                else if (sqlName == "get_merchant_info")
                    merchant_info = CDbDealer.GetRecordsFromDr(_dr, false);
            }
            _dr.Close();
            _sql_queue.Clear();

            // (4) 执行最终校验
            var err = string.Empty;
            if (bill_info is null || bill_info.Count <= 0)
                err += "数据库出错,请联系技术人员";
            if (merchant_info is null || merchant_info.Count <= 0)
                err += "未开户";
            if (err.Length > 0)
                return new CallResult("Error", err);

            // (5) 组装其他参数
            string billId = ((dynamic)bill_info[0]).bill_id;
            string merchantId = ((dynamic)merchant_info[0]).merchant_id;
            DateTime billHappenTime = DateTime.Now;
            string tradeNo = TzbankApi.TzbankTradeNo.Encrypt(sheetId, sheetType, billId, companyId, billHappenTime);

            // (6) 获取token和randomSec
            dynamic tokenResponse = await TzbankApi.GetToken();
            string tokenResponseCode = tokenResponse?.retType ?? "";
            if (tokenResponseCode == "E")
            {
                string tokenResponseMsg = tokenResponse?.retMsg ?? "未知错误";
                NLogger.Error($"台州银行支付.获取token失败 - " + tokenResponseMsg);
                return new CallResult("Error", tokenResponseMsg);
            }
            string token = tokenResponse?.token ?? "";
            string randomSec = tokenResponse?.randomSec ?? "";
            if (token.IsInvalid() || randomSec.IsInvalid())
            {
                NLogger.Error($"台州银行支付.获取token失败 - " + "token为空");
                return new CallResult("Error", "token获取失败");
            }

            // (7) 生成订单
            dynamic payBillResponse = await TzbankApi.PayBill(tradeNo, payAmount, merchantId, companyId, name, phone, token, randomSec);

            string payBillResponseCode = payBillResponse?.retType ?? "";
            if (payBillResponseCode == "E")
            {
                string payBillResponseMsg = payBillResponse?.retMsg ?? "未知错误";
                NLogger.Error($"台州银行支付.生成{companyId}的订单失败 - " + payBillResponseMsg);
                return new CallResult("Error", payBillResponseMsg);
            }
            string orderFlowNo = payBillResponse?.body?.orderFlowNo ?? "";
            string orderNo = payBillResponse?.body?.orderNo ?? "";

            var res = new Dictionary<string, dynamic>
            {
                { "businessCstNo", companyId },
                { "platMerCstNo", TzbankApi.getPartnerId() },
                {"orderNoList", orderFlowNo }
            };

            //（9）创建订单
            string redisKey = $"_GetWechatPayCode_Tzbank_{tradeNo}";
            await RedisHelper.SetAsync(redisKey, JsonConvert.SerializeObject(res), 595);
            sql = $@"
                UPDATE
                    pay_bill
                SET
                    pay_qrcode = '{redisKey}',
                    trade_no = '{tradeNo}',
                    order_flow_no = '{orderFlowNo}'
                WHERE
                    bill_id = {billId} and company_id = {companyId};"
            + $@"
                UPDATE
                    {getSheetMainTable.data}
                SET
                    pay_bill_id = '{billId}'
                WHERE
                    sheet_id = {sheetId} and company_id = {companyId};
            ";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();

            //（10）返回结果
            return new CallResult("OK", "", JsonConvert.SerializeObject(res));
        }
        #endregion
    }
}
