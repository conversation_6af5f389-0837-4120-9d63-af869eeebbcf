﻿//???sys_quantity stock_qty是否重复了
function onPageReady(sheetRows) {
    let windowHeight = document.body.offsetHeight - 50
    let windowWidth = document.body.offsetWidth - 80
    $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 900, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

    $("#popInventoryAlert").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 900, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
    $("#popBrand").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 900, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
    $("#popClass").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 900, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
    var approve_time = $('#approve_time').text();
    if (approve_time) {
        $('#btnApprove').attr('disabled', true);
        $("#btnSave").attr('disabled', true);
    }
    var sheet_id = $("#sheet_id").val()
    if (sheet_id) {
        $('#btnExport').attr('disabled', false)
    }


    cost_price_type = $('#cost_price_type').attr("value");
    window.onCellEndEdit = function (rowIndex, colName, cellValue, bUpdateNow) {
        if (typeof (bUpdateNow) == 'undefined') bUpdateNow = true

        var dataRows = $('#jqxgrid').jqxGrid('getRows')
        //var dataRows = $('#jqxgrid').jqxGrid('getboundrows')
        //  dataRows = source.localdata
        var rowData = dataRows[rowIndex];// $('#jqxgrid').jqxGrid('getrowdata', rowIndex)
        /*
        source.localdata.length = 0
        dataRows.forEach(function (row) {
            source.localdata.push(row)
        })
        */
        var source = $("#jqxgrid").jqxGrid('getsource')
        source._source.localdata = dataRows;// $("#jqxgrid").jqxGrid('getrows')
        /*
         var copyRows=[]
         boundRows.forEach(function (row) {
             copyRows.push(row)  
         })
         dataRows.length = 0
         boundRows.forEach(function (row) {
             copyRows.push(row)
         })
         */
        if (colName === 'item_id') {


        }
        else if (colName === 'unit_no') {
            if (window.g_queriedItems && item_id) {
                var item = window.g_queriedItems[item_id];
                if (item) {
                    item.units.forEach(function (unit) {
                        if (unit.unit_no === cellValue) {
                            var qty = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'quantity');
                            var sub_amount = qty * unit.price;
                            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'real_price', toMoney(unit.price));
                            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'sub_amount', toMoney(sub_amount));
                        }
                    });
                }
            }
        }
        else if (colName == "b_unit_qty" || colName == "m_unit_qty" || colName == "s_unit_qty") {
            // $('#jqxgrid').jqxGrid('beginupdate')

            var stock_qty = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'stock_qty');
            var sys_quantity = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'sys_quantity');
            var real_quantity = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'real_quantity');
            var b_unit_qty = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'b_unit_qty');
            var m_unit_qty = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'm_unit_qty');
            var s_unit_qty = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 's_unit_qty');
            if (s_unit_qty) checkNumber(s_unit_qty)
            var b_unit_no = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'b_unit_no');
            var m_unit_no = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'm_unit_no');
            var s_unit_no = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 's_unit_no');
            // var s_unit_factor = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'unit_factor');
            var m_unit_factor = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'm_unit_factor');
            var b_unit_factor = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'b_unit_factor');
            var wholesale_price = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'wholesale_price');
            var cost_price_avg = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'cost_price_avg');
            var cost_price_buy = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'buy_price');
            var buy_price = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'buy_price');
            var profit_wholesale_amount = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'profit_wholesale_amount');
            var loss_wholesale_amount = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'loss_wholesale_amount');
            var wholesale_amount = '100';

            var profit_cost_amount = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'profit_cost_amount');
            var loss_cost_amount = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'loss_cost_amount');
            wholesale_price = parseFloat(wholesale_price || 0);
            cost_price = parseFloat(cost_price_avg || 0);
            if (cost_price_type == '3') cost_price = parseFloat(cost_price_buy || 0);
            profit_wholesale_amount = parseFloat(profit_wholesale_amount || 0);
            loss_wholesale_amount = parseFloat(loss_wholesale_amount || 0);
            profit_cost_amount = parseFloat(profit_cost_amount || 0);
            loss_cost_amount = parseFloat(loss_cost_amount || 0);
            var new_change_qty = "";
            //var b_unit_qty = b_unit_qty || 0; var m_unit_qty = m_unit_qty || 0;
            sys_quantity = (stock_qty || 0);
            real_quantity = toMoney((b_unit_qty || 0) * (b_unit_factor || 0) + (m_unit_qty || 0) * (m_unit_factor || 0) + (s_unit_qty || 0) * 1, 4);

            rowData.real_quantity = real_quantity
            rowData.sys_quantity = sys_quantity
            /*
            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'real_quantity', real_quantity);
            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'sys_quantity', sys_quantity);
            */
            var difference_qty = (b_unit_qty || 0) * (b_unit_factor || 0) + (m_unit_qty || 0) * (m_unit_factor || 0) + (s_unit_qty || 0) * 1 - sys_quantity;
            if (difference_qty > 0) {
                profit_wholesale_amount = toMoney(difference_qty * wholesale_price);
                profit_cost_amount = toMoney(difference_qty * cost_price);
                profit_buy_amount = toMoney(difference_qty * buy_price);

                rowData.profit_wholesale_amount = profit_wholesale_amount
                rowData.profit_cost_amount = profit_cost_amount
                rowData.profit_buy_amount = profit_buy_amount

                rowData.loss_qty = ""
                rowData.loss_wholesale_amount = ''
                rowData.loss_cost_amount = ''
                rowData.loss_buy_amount = ''
                rowData.b_loss_qty = 0
                rowData.m_loss_qty = 0
                rowData.s_loss_qty = 0

                var res = getQtyUnit(difference_qty, b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no)
                rowData.b_profit_qty = res.b_qty
                rowData.m_profit_qty = res.m_qty
                rowData.s_profit_qty = res.s_qty
                rowData.profit_qty = res.qtyUnit


            } else if (difference_qty < 0) {
                loss_wholesale_amount = toMoney(difference_qty * wholesale_price);
                loss_cost_amount = toMoney(difference_qty * cost_price);
                loss_buy_amount = toMoney(difference_qty * buy_price);

                rowData.loss_wholesale_amount = loss_wholesale_amount
                rowData.loss_cost_amount = loss_cost_amount
                rowData.loss_buy_amount = loss_buy_amount

                rowData.profit_qty = ""
                rowData.profit_wholesale_amount = ""
                rowData.profit_cost_amount = ""
                rowData.profit_buy_amount = ""
                rowData.b_profit_qty = 0
                rowData.m_profit_qty = 0
                rowData.s_profit_qty = 0

                var res = getQtyUnit(difference_qty, b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no)
                rowData.b_loss_qty = res.b_qty
                rowData.m_loss_qty = res.m_qty
                rowData.s_loss_qty = res.s_qty
                rowData.loss_qty = res.qtyUnit


            } else if (difference_qty == 0) {
                rowData.profit_qty = ""
                rowData.profit_wholesale_amount = ""
                rowData.profit_cost_amount = ""
                rowData.profit_buy_amount = ""

                rowData.loss_qty = ''
                rowData.loss_wholesale_amount = ''
                rowData.loss_cost_amount = ''
                rowData.loss_buy_amount = ''

                rowData.b_profit_qty = 0
                rowData.m_profit_qty = 0
                rowData.s_profit_qty = 0
                rowData.b_loss_qty = 0
                rowData.m_loss_qty = 0
                rowData.s_loss_qty = 0
            }
        }
        else if (colName === 'batch_id') {

            let rowBranchId = $("#branch_id").val().value ? $("#branch_id").val().value : "-1"
            let rowBranchPosition = rowData.branch_position ? rowData.branch_position : "0"
            let key = rowData.item_id + "_" + rowBranchId + "_" + rowBranchPosition
            let produceDate = args.row.produce_date
            if (produceDate.length == 6) {
                produceDate = '20' + produceDate.substr(0, 2) + '-' + produceDate.substr(2, 2) + '-' + produceDate.substr(4, 2)
                rowData[colName] = produceDate
            }
            let batchNo = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, "batch_no")
            if (!batchNo) batchNo = ""
            bUpdateNow = false
            setStockQty(key, rowIndex, produceDate, batchNo, "from", "from")
        }
        else if (colName === 'batch_id_f') {

            let rowBranchId = $("#branch_id").val() ? $("#branch_id").val().value : "-1"
            let rowBranchPosition = rowData.branch_position ? rowData.branch_position : "0"
            let key = rowData.item_id + "_" + rowBranchId + "_" + rowBranchPosition
            let batchNo = args.row.batch_no
            let produceDate = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, "produce_date")
            if (!produceDate) produceDate = ""
            bUpdateNow = false
            setStockQty(key, rowIndex, produceDate, batchNo, "from", "from")

        }
        else if (colName === 'branch_position') {
            rowData.branch_position_name = args.value ? args.value.label : ""
            rowData.branch_position = cellValue ? cellValue : "0"
            bUpdateNow = false
            GetBatchStock([rowIndex], rowData.branch_position, "from", "from")

        }
        //else if (colName == "batch_id") {
        //    bUpdateNow = false

        //    //if (rowData.batch_level == "2" && !rowData.batch_no) $("#jqxgrid").jqxGrid('setcellvalue', rowIndex, "batch_no", cellValue.slice(2, 4) + cellValue.slice(5, 7) + cellValue.slice(8, 10));
        //} else if (colName == "batch_id_f") {
        //    bUpdateNow = false
        //    //if (rowData.batch_level == "2" && !rowData.batch_no) $("#jqxgrid").jqxGrid('setcellvalue', rowIndex, "batch_no", cellValue.slice(2, 4) + cellValue.slice(5, 7) + cellValue.slice(8, 10));
        //}
        //else if (colName == "branch_position") {
        //    bUpdateNow = false
        //}

        console.log("bUpdateNow", bUpdateNow)
        if (bUpdateNow) {

            //var rowid = $('#jqxgrid').jqxGrid('getrowid', rowIndex)
            //      $('#jqxgrid').jqxGrid('updatebounddata')
            if (window.g_lastEditCell) {
                //  setTimeout(function () {
                //    console.log('window.g_lastEditCell', window.g_lastEditCell)
                //   $('#jqxgrid').jqxGrid('begincelledit', window.g_lastEditCell.row, window.g_lastEditCell.col) 
                //   },3000)


            }
            // $('#jqxgrid').jqxGrid('updaterow', rowid, dataRows[rowIndex]);
            console.log('updating ', rowIndex)
            //
        }

        //var rows = $('#jqxgrid').jqxGrid('getrows');
        console.log(dataRows)
        if (rowIndex > dataRows.length - 3) {
            var addrows = []
            for (var i = 0; i < 10; i++) {
                var row = {};
                GridData.columns.forEach(function (col) {
                    if (col.datafield) row[col.datafield] = "";
                    if (col.displayfield) row[col.displayfield] = "";
                })
                addrows.push(row)
            }
            if (bUpdateNow) {
                $('#jqxgrid').jqxGrid('addrow', null, addrows)
            }
            else {
                dataRows.push(...addrows)
            }
        }
        if (bUpdateNow) {
            updateTotalAmount()
        }


    }
    window.setRowFields = function (row) {
        GridData.columns.forEach(col => {
            if (col.datafield && row[col.datafield] === undefined) {
                row[col.datafield] = ''
            }
        })
        // 把没有产期的行初始化为"无产期"
        if (row['produce_date'] === '') {
            row['produce_date'] = '无产期'
        }
    }

    var TempHappenTime = $('#TempHappenTime').val() || ''
    var approve_time = $('#approve_time').val()
    if (TempHappenTime.toLowerCase() == "true" && !approve_time) {
        $('#happen_time').val('')
    }
    var happen_time = $('#happen_time').val()
    var approve_time = $('#approve_time').text();
    if (happen_time != "" && happen_time != approve_time) {
        $('#happen_time').parent().addClass('spec_invent_time');
    }

    $('#happen_time').on('change', function (event) {
        var value = $('#happen_time').val()
        if (value)
            $('#TempHappenTime').val('false')
        else $('#TempHappenTime').val('true')

    })
    window.GetItemsByClasses = function (rows_select, isLoadNoStock) {
        var happen_time = $('#happen_time').jqxDateTimeInput('val');
        var message = "";
        let loadItemsByClasses = function () {
            var classes_id = ''
            rows_select.forEach(classInfo => {
                if (classes_id != '') classes_id += ','
                classes_id += classInfo.value

            })
            var branch_id = $('#branch_id').jqxInput('val').value;
            var formData = new FormData();
            formData.append('branch_id', branch_id)
            formData.append('classes_id', classes_id)
            formData.append('isLoadNoStock', isLoadNoStock)
            $.ajax({
                url: `/api/InventorySheet/GetItemsByClasses?operKey=${g_operKey}`,
                type: 'post',
                contentType: false,
                processData: false,
                data: formData,
                success: function (data) {
                    if (data.result === 'OK') {
                        if (data.data.length === 0) return
                        var res = GetSheetData();
                        if (res.result === "Error") {
                            bw.toast(res.msg, 5000); return;
                        }
                        var len = res.sheet.SheetRows.length
                        var rowIndex = len ? res.sheet.SheetRows[len - 1].boundindex + 1 : 0;
                        //AddItemRows(rowIndex, data.data, false, 'item_class', false, false)
                        AddItemRows(rowIndex, data.data, false, 'item_class', false, !isLoadNoStock)
                    }

                }
            })
        }
        if (happen_time != "") {
            message = "您指定了盘点时间，将带出这个时间的库存 <br/> ";
            jConfirm(message + '是否开始盘点？', loadItemsByClasses())
        } else {
            loadItemsByClasses()
        }
    }
    function GetClassTreeData() {
        $.ajax({
            url: `/api/InventorySheet/GetClassTreeData?operKey=${g_operKey}`,
            type: 'get',
            contentType: false,
            processData: false,
            success: function (data) {
                if (data.result === 'OK') {
                    var treeSource =
                    {
                        datatype: 'json',
                        datafields: [
                            { name: 'v' },
                            { name: 'pv' },
                            { name: 'l' },
                            { name: 'z' },
                            { name: 'status' }
                        ],
                        id: 'id',
                        localdata: data.data
                    };
                    var dataAdapter = new $.jqx.dataAdapter(treeSource);
                    dataAdapter.dataBind();
                    var records = dataAdapter.getRecordsHierarchy('v', 'pv', 'items', [{ name: 'l', map: 'label' }, { name: 'v', map: 'value' }]);
                    $("#jqxTree").jqxTree({
                        source: records,
                        checkboxes: true,
                        height: '480px',
                        hasThreeStates: true
                    });
                    var items = $('#jqxTree').jqxTree('getItems');
                    $('#jqxTree').jqxTree('expandItem', items[0].element);
                }

            }
        });
    }

    window.onStartInvent = function (bImportFile, file) {
        var branch_id = $('#branch_id').jqxInput('val').value;
        if (!branch_id) {
            bw.toast('请先选择仓库', 1000)
            return
        }

        var formData = new FormData();
        if (bImportFile) {
            var bWholeStock = false

            var inventType = $('input[name="inventType"]:checked')[0]
            if (inventType.id == "wholeInvent") bWholeStock = true


            formData.append('file', file, file.name);
            formData.append('importFile', true);
            formData.append('wholeStock', bWholeStock);
        }
        else {
            formData.append('stockOnly', true);
        }

        formData.append('branch_id', branch_id);

        formData.append('bGetAttrs', !window.attributes)

        var happen_time = $('#happen_time').jqxDateTimeInput('val');
        formData.append('happen_time', happen_time)

        let loadItems = function () {

            $('#happen_time').jqxDateTimeInput({ disabled: true })

            $.ajax({
                url: `/api/InventorySheet/GetStockQtyList?operKey=${g_operKey}`,
                type: 'post',
                contentType: false,
                processData: false,
                data: formData,//JSON.stringify({ operKey: g_operKey, branch_id: branch_id, stockOnly: true }),
                success: function (data) {
                    if (data.result === 'OK') {
                        var rows = $('#jqxgrid').jqxGrid('getrows');

                        if (data.attrOptions) window.attrOptions = data.attrOptions
                        if (data.attributes) window.attributes = data.attributes
                        //  var preCount = rows.length;
                        data.records.forEach(function (row) {
                            if (!row.b_unit_qty) row.b_unit_qty = '';
                            if (!row.m_unit_qty) row.m_unit_qty = '';
                            if (!row.s_unit_qty) row.s_unit_qty = '';
                            row.sys_quantity = row.stock_qty
                            setRowFields(row)
                        });
                        //var insertRows = data.records;
                        var insertRows = data.records.filter(row => {
                            return (row.stock_qty && row.stock_qty != "0") || (row.real_quantity != "")
                        })

                        insertRows.forEach(row => {
                            if (row.mum_attributes)
                                row.mum_attributes = JSON.parse(row.mum_attributes)

                            if (row.mum_attributes) {
                                var attrs = row.mum_attributes
                                for (var i = attrs.length - 1; i >= 0; i--) {
                                    var attr = attrs[i]
                                    if (!attr.distinctStock) {
                                        attrs.splice(i, 1)
                                    }
                                }
                                if (row.mum_attributes.length == 0) row.mum_attributes = null
                            }

                        })


                        $('#jqxgrid').jqxGrid('clear');
                        $('#jqxgrid').jqxGrid('addrow', null, insertRows);


                        disableBranch()

                        addEmptyRows(10)
                        if (bImportFile) {
                            rows = $('#jqxgrid').jqxGrid('getrows');
                            rows.forEach(function (row, index) {
                                if (!row.item_id) return
                                onCellEndEdit(index, "b_unit_qty", undefined, false);
                                onCellEndEdit(index, "m_unit_qty", undefined, false);
                                onCellEndEdit(index, "s_unit_qty", undefined, false);
                            });

                            $('#jqxgrid').jqxGrid('updatebounddata')

                            $('#popImport').jqxWindow('close')
                        }
                    }
                    else {
                        showLongMsg(data.msg)
                    }
                },
                error: function (XMLHttpRequest, textStatus, errorThrown, e1, e2) {
                    bw.toast('文件可能已被编辑过,请重新选择')
                    var file = $("#fileImportExcel")
                    file.val('')
                }
            })

            if (happen_time != "") {
                $('#happen_time').parent().addClass('spec_invent_time');
            }


        }
        var message = "";

        if (happen_time != "") {
            message = "您指定了盘点时间，将带出这个时间的库存 <br/> ";
            jConfirm(message + '是否开始盘点？', loadItems(), "");
        }
        loadItems()
    }
    window.onBrandInvent = function () {
        var branch_id = $('#branch_id').jqxInput('val').value;
        if (!branch_id) {
            bw.toast('请先选择仓库', 1000)
            return
        }
        if ($("#brandsInfo")[0].innerHTML == "") {
            $("#popBrand").jqxWindow('setContent', `<iframe src="../BaseInfo/BrandsView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="auto" frameborder="no" id="selectBranchs"></iframe>`);
        }
        $('#popBrand').jqxWindow('open');
    }
    window.onClassInvent = function () {
        var branch_id = $('#branch_id').jqxInput('val').value;
        if (!branch_id) {
            bw.toast('请先选择仓库', 1000)
            return
        }
        //获取类别
        console.log($("#jqxTree")[0])
        if ($("#jqxTree")[0].innerText == "") {
            GetClassTreeData()
        }
        $('#popClass').jqxWindow('open');


    }

    $("#popImport").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 380, width: 700, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
    $("#popoverOther").jqxPopover({ showArrow: false, autoClose: true, offset: { left: 0, top: -10 }, position: "bottom", title: "", showCloseButton: false, selector: $("#btnOtherOp") });


    window.btnImport_click = function () {
        var branch_id = $('#branch_id').jqxInput('val').value;
        if (!branch_id) {
            bw.toast('请先选择仓库', 1000)
            return
        }

        $('#popImport').jqxWindow('open')
    }
    window.btnSelectImportFile_click = function () {
        onSelectFile('fileImportExcel', 'excel', 'labelForImportFileName')

    }
    window.btnImportConfirm_click = function () {
        var file = $("#fileImportExcel")
        if (file.length == 0) {
            bw.toast('请选择EXCEL文件')
            return
        }
        if (file[0].files.length == 0) {
            bw.toast('请选择一个EXCEL文件')
            return
        }
        file = file[0].files[0]
        onStartInvent(true, file)
    }


    window.onCurrQtyInvent = function () {
        var rows = $('#jqxgrid').jqxGrid('getrows');
        var s_unit_qty = 0;
        var bupdate = false;
        for (var i = 0; i < rows.length; i++) {
            var row = rows[i]
            if (row.item_name && !myIsNumber(row.b_unit_qty) && !myIsNumber(row.m_unit_qty) && !myIsNumber(row.s_unit_qty)) {
                bupdate = true;
                if (row.bstock) {
                    row.b_unit_qty = row.bstock
                    onCellEndEdit(i, "b_unit_qty", row.bstock, false);
                }
                if (row.mstock) {
                    row.m_unit_qty = parseFloat(row.mstock)
                    onCellEndEdit(i, "m_unit_qty", row.mstock, false);
                }
                if (row.sstock) {
                    row.s_unit_qty = parseFloat(row.sstock)
                    onCellEndEdit(i, "s_unit_qty", row.sstock, false);
                }
            }
        }
        if (bupdate) $('#jqxgrid').jqxGrid('updatebounddata')
    }
    window.onZeroInvent = function () {
        //代办：若没有从首行添加商品 mum_attributes为undefined
        var rows = $('#jqxgrid').jqxGrid('getrows');
        let result = checkHasSonItem()
        if (result.result === 'ERROR') return
        var s_unit_qty = 0;
        var bupdate = false;
        for (var i = 0; i < rows.length; i++) {
            var row = rows[i];// $('#jqxgrid').jqxGrid('getrowdata', i);
            if (row.item_name && !row.b_unit_qty && !row.m_unit_qty && !row.s_unit_qty) {
                bupdate = true;
                row.s_unit_qty = s_unit_qty
                // $('#jqxgrid').jqxGrid('setcellvalue', i, "s_unit_qty", s_unit_qty);
                onCellEndEdit(i, "s_unit_qty", s_unit_qty, false);
            }
        }
        if (bupdate) $('#jqxgrid').jqxGrid('updatebounddata')
    }

    window.delZeroInvent = function () {
        var rows = $('#jqxgrid').jqxGrid('getrows');
        for (var i = rows.length - 1; i >= 0; i--) {
            var row = rows[i]
            if (row.item_name && !row.current_qty && !row.real_quantity && !row.b_unit_qty && !row.m_unit_qty && !row.s_unit_qty) {
                rows.splice(i, 1)
            }
        }
        $('#jqxgrid').jqxGrid('refreshdata')

    }
    function SaveAndApprove(confirmInfo) {
        updateTotalAmount()
        var res = GetSheetData();
        if (res.result === "Error") {
            bw.toast(res.msg, 5000); return;
        }
        var sheet = res.sheet;
        var rows = sheet.SheetRows

        var msg = "";
        for (var i = 0; i < rows.length; i++) {
            var row = rows[i];
            if ((!myIsNumber(row.b_unit_qty)) && (!myIsNumber(row.m_unit_qty)) && (!myIsNumber(row.s_unit_qty))) {
                msg = '请输入第' + (i + 1) + '行 ' + row.item_name + ' 的盘点数量'
                break
            }
            var real_quantity = toMoney((row.b_unit_qty || 0) * (row.b_unit_factor || 0) + (row.m_unit_qty || 0) * (row.m_unit_factor || 0) + (row.s_unit_qty || 0) * 1, 4);
            if (real_quantity != row.real_quantity) {
                msg = '第' + (i + 1) + '行 ' + row.item_name + `的盘点数量不一致:${real_quantity},${row.real_quantity},请联系技术人员`
                break
            }
        }

        if (msg) {
            bw.toast(msg, 3000)
            return
        }
        jConfirm(confirmInfo, function () {
            $("#btnApprove").attr('disabled', true);
            var err = refreshStockBeforeSave(g_operKey, res.branch_id, res.items_id, '审核', () => {
                closeLongMsg()
                $.ajax({
                    url: '/api/InventorySheet/SaveAndApprove',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(sheet),
                    success: function (data) {
                        if (data.result === 'OK') {
                            $('#sheet_id').val(data.sheet_id)
                            $('#sheet_no').text(data.sheet_no)
                            $('#approve_time').text(data.approve_time)
                            updateSheetState()
                            removeSheetFromCach()
                            bw.toast('审核成功', 3000)
                        }
                        else {
                            $("#btnApprove").attr('disabled', false)

                            bw.toast(data.msg, 3000)
                        }
                    },
                    error: function (xhr) {
                        console.log("返回响应信息：" + xhr.responseText);
                    }
                })
            })

        }, "")
    }
    window.addEventListener('message', function (rs) {

        if (rs.data.msgHead === "ItemsView") {
            if (rs.data.action === "selectMulti") {
                var rows_select = rs.data.checkedRows

                var cell = $('#jqxgrid').jqxGrid('getselectedcell');
                var rowIndex = cell.rowindex;
                //!!!下面一句非常重要，必须先endcelledit,才能setcellvalue,否则单元格结束编辑时会覆盖掉cellvalue
                var editable = $("#jqxgrid").jqxGrid('endcelledit', rowIndex, "item_id", false);
                AddItemRows(rowIndex, rows_select, false, '', true, false)

            }
        } else if (rs.data.msgHead === "InventoryAlert") {
            if (rs.data.action === "close") {
                $('#popInventoryAlert').jqxWindow('close');
                SaveAndApprove("确认继续审核吗？")
            } else if (rs.data.action === "submit") {
                var res = GetSheetData();
                if (res.result === "Error") {
                    bw.toast(res.msg, 5000); return;
                }
                var sheet = res.sheet;
                var SheetRows = sheet.SheetRows
                console.log(rs.data.rows)

                AddItemRows(SheetRows[SheetRows.length - 1].boundindex + 1, rs.data.rows, true, '', false, true)
                $('#popInventoryAlert').jqxWindow('close');
            }
        } else if (rs.data.msgHead === "BrandsView") {
            if (rs.data.action === "selectMulti") {
                var rows_select = rs.data.checkedRows

                console.log(rows_select)
                //!!!下面一句非常重要，必须先endcelledit,才能setcellvalue,否则单元格结束编辑时会覆盖掉cellvalue
                var editable = $("#jqxgrid").jqxGrid('endcelledit', rowIndex, "brand_id", false);
                var isLoadNoStock = rs.data.isLoadNoStock;
                GetItemsByBrands(rows_select, isLoadNoStock)
                //$('#popBrand').jqxWindow('close');

            }
        }
    })

    function addEmptyRows(needAddRowNums) {
        var rows = new Array();
        for (let index = 0; index < needAddRowNums; index++) {
            var row = {}
            setRowFields(row)
            rows.push(row);
        }
        $('#jqxgrid').jqxGrid('addrow', null, rows);

    }
    function GetItemsByBrands(rows_select, isLoadNoStock) {
        $('#popBrand').jqxWindow('close');
        var happen_time = $('#happen_time').jqxDateTimeInput('val');
        let loadItemsByBrands = function () {
            var brands_id = ''
            rows_select.forEach(brand => {
                if (brands_id != '') brands_id += ','
                brands_id += brand.brand_id
            })
            var branch_id = $('#branch_id').jqxInput('val').value;
            var formData = new FormData();
            formData.append('brands_id', brands_id)
            formData.append('branch_id', branch_id)

            formData.append('isLoadNoStock', isLoadNoStock)
            $.ajax({
                url: `/api/InventorySheet/GetItemsByBrands?operKey=${g_operKey}`,
                type: 'post',
                contentType: false,
                processData: false,
                data: formData,
                success: function (data) {
                    if (data.result === 'OK') {
                        console.log(data.data)
                        if (data.data.length === 0) return
                        //$('#jqxgrid').jqxGrid('clear');
                        var res = GetSheetData();
                        if (res.result === "Error") {
                            bw.toast(res.msg, 5000); return;
                        }
                        var len = res.sheet.SheetRows.length
                        var rowIndex = len ? res.sheet.SheetRows[len - 1].boundindex + 1 : 0;
                        //AddItemRows(rowIndex, data.data, false, 'item_brand', false, false)
                        AddItemRows(rowIndex, data.data, false, 'item_brand', false, !isLoadNoStock)
                    }

                }
            });
        }
        if (happen_time != "") {
            var message = "您指定了盘点时间，将带出这个时间的库存 <br/> ";
            jConfirm(message + '是否开始盘点？', loadItemsByBrands())
        } else {
            loadItemsByBrands()
        }



    }

    function AddItemRows(rowIndex, rows_select, zeroFlag, orderBy, stillAddOnExist, ignoreZeroStock) {
        var items_id = ''
        var msg = "";
        var noNeedItems = []
        var rows = $('#jqxgrid').jqxGrid('getrows');
        console.log(rows)
        var nAddRows = rowIndex + rows_select.length + 10 - rows.length
        if (nAddRows > 0)
            addEmptyRows(nAddRows)
        for (var i = 0; i < rows.length; i++) {
            var row = rows[i]
            if (row.item_id && row.item_name && i != rowIndex) {
                for (var j = 0; j < rows_select.length; j++) {
                    var itemRow = rows_select[j]
                    if (itemRow.item_id === row.item_id && row.batch_level === "") {
                        var index = i + 1
                        msg = itemRow.item_name + '与第' + index + '行商品' + row.item_name + '重复'
                        bw.toast(msg, 5000)
                        noNeedItems.push(itemRow.item_id)
                    }
                }
            }
        }
        rows_select.forEach(function (row_select) {
            if (!noNeedItems.includes(row_select.item_id)) {
                if (items_id != '') items_id += ','
                items_id += row_select.item_id
            }

        })


        $('#popItem').jqxWindow('close');

        if (items_id) {

            if (zeroFlag) {
                LoadItemInfo(rowIndex, items_id, true, orderBy, stillAddOnExist, ignoreZeroStock);
            } else {
                LoadItemInfo(rowIndex, items_id, false, orderBy, stillAddOnExist, ignoreZeroStock);
            }


            disableBranch()
        }
    }


    var theme = "";
    var datafields = [];
    var data = new Array; var rowscount = 10;
    if (sheetRows) data = sheetRows;

    var source =
    {
        sort: funcSortByColumn,
        localdata: data,
        unboundmode: true,
        totalrecords: 10,
        datafields: datafields,
        //  updaterow: function (rowid, rowdata) {
        // }
    };

    var dataAdapter = new $.jqx.dataAdapter(source);


    var fixColCss = 'jqx-widget-header';
    if (theme !== '') fixColCss += ' jqx-widget-header-' + theme;


    var qty_createeditor = function (row, cellvalue, editor, cellText, width, height) {
        var element = $(`<div><input style="width:${width}px;height:${height}px;border:none;padding-left:3px;"/></div>`);
        editor.append(element);
    }
    var qty_initeditor = function (row, cellvalue, editor, celltext, pressedkey) {
        // set the editor's current value. The callback is called each time the editor is displayed.
        var inputField = editor.find('input')
        if (pressedkey) {
            inputField.val(pressedkey)
            inputField.focus()
        }
        else {
            celltext = celltext || ''
            inputField[0].value = celltext
            inputField.focus()
            inputField[0].selectionStart = 0
            inputField[0].selectionLength = celltext.length
            setTimeout(() => {
                inputField[0].selectionStart = 0
                inputField[0].selectionLength = celltext.length
            }, 200)

        }
        var unit_no = $('#jqxgrid').jqxGrid('getcellvalue', row, 'b_unit_no')
        var lbl = editor.find('.unit')
        lbl.text(unit_no)
    }
    var qty_geteditorvalue = function (row, cellvalue, editor) {
        var v = editor.find('input').val();
        return v;
    }

    window.GridData = {
        source: dataAdapter,
        showaggregates: true,
        showstatusbar: true,
        columnsheight: 20,
        rowsheight: 36,
        statusbarheight: 30,
        pageable: false,
        // autoheight: true,
        sortable: true,


        editable: true,
        columnsresize: true,

        ready: function () {
            $("#jqxgrid").jqxGrid('focus');
        },
        cellhover: cellhover,
        renderstatusbar: function (statusbar) {
        },
        editmode: 'click',//: 'selectedcell',
        selectionmode: 'multiplecellsadvanced',//'singlecell',// 
        hoverrow: true,
        theme: theme,
        handlekeyboardnavigation: function (event) {

            var key = event.charCode ? event.charCode : event.keyCode ? event.keyCode : 0;
            cell = $('#jqxgrid').jqxGrid('getselectedcell');
            var nextRow = null
            var row = null
            var rows = null
            if (key == 13 || key == 38 || key == 40) {
                $("#jqxgrid").jqxGrid('endcelledit', cell.row, cell.column, false);
                rows = $("#jqxgrid").jqxGrid('getrows');
                row = rows[cell.row]

                if (cell.row < rows.length - 1) nextRow = rows[cell.row + 1]
            }
            if (key === 13) {

                if (cell.column === "item_id") {

                    if (!row.item_id) return true
                    setTimeout(function () {
                        $('#jqxgrid').jqxGrid('clearselection');
                        if (row.b_unit_no) $('#jqxgrid').jqxGrid('selectcell', cell.row, 'b_unit_qty')
                        else $('#jqxgrid').jqxGrid('selectcell', cell.row, 's_unit_qty')
                    }, 300)
                    //   setTimeout(function () {
                    //        $('#jqxgrid').jqxGrid('begincelledit', cell.row, 'unit_no');
                    //   }, 300)
                }
                else if (cell.column === "b_unit_qty") {
                    $('#jqxgrid').jqxGrid('clearselection')
                    if (row.m_unit_no) $('#jqxgrid').jqxGrid('selectcell', cell.row, 'm_unit_qty')
                    else $('#jqxgrid').jqxGrid('selectcell', cell.row, 's_unit_qty')
                }
                else if (cell.column === "m_unit_qty") {
                    $('#jqxgrid').jqxGrid('clearselection')
                    $('#jqxgrid').jqxGrid('selectcell', cell.row, 's_unit_qty')
                }
                else if (cell.column === "s_unit_qty") {
                    $('#jqxgrid').jqxGrid('clearselection')
                    $('#jqxgrid').jqxGrid('selectcell', cell.row, 's_unit_qty')
                    if (nextRow && nextRow.item_id) {
                        if (row.b_unit_no) $('#jqxgrid').jqxGrid('selectcell', cell.row + 1, 'b_unit_qty')
                        else $('#jqxgrid').jqxGrid('selectcell', cell.row + 1, 's_unit_qty')
                    }
                    else {
                        $('#jqxgrid').jqxGrid('clearselection');
                        $('#jqxgrid').jqxGrid('selectcell', cell.row + 1, 'item_id');
                    }
                } else if (cell.column === "produce_date") {
                    $('#jqxgrid').jqxGrid('clearselection');
                    $('#jqxgrid').jqxGrid('selectcell', cell.row, 'quantity');
                } else if (cell.column === "batch_no") {
                    $('#jqxgrid').jqxGrid('clearselection');
                    $('#jqxgrid').jqxGrid('selectcell', cell.row, 'quantity');
                }
                return true;
            }
            else if (key === 27) {

                return true;
            }
            else if (key === 38) {
                $('#jqxgrid').jqxGrid('clearselection')
                if (cell.row > 0) {
                    if (row.b_unit_no) $('#jqxgrid').jqxGrid('selectcell', cell.row - 1, 'b_unit_qty')
                    else $('#jqxgrid').jqxGrid('selectcell', cell.row - 1, 's_unit_qty')
                }
                return true;
            }
            else if (key === 40) {
                $('#jqxgrid').jqxGrid('clearselection')
                if (cell.row < rows.length) {
                    if (row.b_unit_no) $('#jqxgrid').jqxGrid('selectcell', cell.row + 1, 'b_unit_qty')
                    else $('#jqxgrid').jqxGrid('selectcell', cell.row + 1, 's_unit_qty')
                }
                return true;
            }
        },
        columns: [
            {
                text: '', sortable: false, filterable: false, editable: false, pinned: true,
                groupable: false, draggable: false, resizable: false,
                datafield: '', columntype: 'number', width: 40,
                cellclassname: fixColCss,
                cellsrenderer: pinCellsRenderer,
                renderer: leftTopCellRenderer
            },
            {
                text: '库位',
                sortable: true,
                hidden: true,
                datafield: 'branch_position',
                displayfield: 'branch_position_name',
                width: 150, align: 'center',
                columntype: 'template',
                createeditor: createeditor_branch_position_name,
                initeditor: initeditor_branch_position_name,
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val();
                    return v;
                },
                cellbeginedit: function (row, datafield, columntype, value) {
                    var branch_id = $('#branch_id').jqxInput('val').value
                    var rowData = $("#jqxgrid").jqxGrid('getrows')
                    rowData = rowData[row]
                    if (!branch_id || !rowData.editable) return false;
                },
            },
            {
                text: '商品名称', datafield: 'item_id', displayfield: 'item_name', width: '150', columntype: 'template',
                sortable: true,
                createeditor: createeditor_item_name,
                align: 'center',
                initeditor: function (row, cellvalue, editor, celltext, pressedkey) {
                    var inputField = editor.find('input');
                    if (pressedkey) {
                        inputField.val(pressedkey);
                        inputField.jqxInput('selectLast');
                    }
                    else {
                        inputField.val({ value: cellvalue || '', label: celltext || '' });
                        if (celltext != undefined) inputField[0].value = celltext;
                        inputField.jqxInput('selectAll');
                    }
                },
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val();
                    return v;
                }
            },
            {
                text: '类别', datafield: 'class_name', width: '120', align: 'center', cellsalign: 'center',
            },
            {
                text: '规格', datafield: 'item_spec', width: '60', align: 'center', cellsalign: 'right', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '商品编码', datafield: 'item_no', width: '150', align: 'center', cellsalign: 'right',
                cellbeginedit: function (row, datafield, columntype, value) {
                    if (datafield === "item_no") return false;
                }
            },
            {
                text: '条码(小)', datafield: 's_barcode', width: '150', align: 'center', cellsalign: 'right',
                cellbeginedit: function (row, datafield, columntype, value) {
                    if (datafield === "s_barcode") return false;
                }
            },
            {
                text: '条码(大)', datafield: 'b_barcode', width: '150', align: 'center', cellsalign: 'right',
                cellbeginedit: function (row, datafield, columntype, value) {
                    if (datafield === "b_barcode") return false;
                }
            },
            {
                text: '生产日期', datafield: 'batch_id', displayfield: 'produce_date', width: '150', align: 'center', cellsalign: 'center', columntype: 'template', editable: true, hidden: true,
                initeditor: initeditor_produce_date,
                createeditor: createeditor_produce_date,
                cellbeginedit: function (row, datafield, columntype, value) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var rowsData = $("#jqxgrid").jqxGrid('getrows')
                    rowData = rowsData[row]
                    //var sysQty = rowData.sys_quantity
                    if (item_id && !parseFloat(rowData.sys_quantity) && rowData.batch_level && rowData.batch_level !== "0") {
                        return true
                    } else {
                        return false
                    }
                },
                cellsrenderer: function (row, columnfield, value) {
                    var rowsData = $("#jqxgrid").jqxGrid('getrows')
                    var rowData = rowsData[row]
                    if (rowData.batch_level == "" || rowData.batch_level == "0") value = "无产期"
                    return '<div style="line-height:37px;text-align:center;">' + value + '</div>';
                },
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val()
                    if (v && v.length == 6) {
                        v = '20' + v.substr(0, 2) + '-' + v.substr(2, 2) + '-' + v.substr(4, 2)
                    }
                    return v;
                },
            },
            {
                text: '批次', datafield: 'batch_id_f', displayfield: 'batch_no', width: '60', align: 'center', cellsalign: 'center', columntype: 'template', hidden: true,
                initeditor: initeditor_batch_no,
                createeditor: createeditor_batch_no,
                cellbeginedit: function (row, datafield, columntype, value) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var rowsData = $("#jqxgrid").jqxGrid('getrows')
                    rowData = rowsData[row]
                    if (item_id && rowData.editable && rowData.batch_level == "2") {
                        return true
                    } else {
                        return false
                    }
                },
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val(); return v;
                },

            },
            {
                text: '单位换算', datafield: 'unit_conv', width: '100', align: 'center', cellsalign: 'right',
                cellbeginedit: function (row, datafield, columntype, value) {
                    if (datafield === "unit_conv") return false;
                }
            },
            {
                text: '系统库存', datafield: 'current_qty', width: '100', align: 'center', cellsalign: 'right',
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '占用库存', datafield: 'sell_pend_qty', width: '100', align: 'center', cellsalign: 'right', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '可用库存', datafield: 'avail_qty', width: '100', align: 'center', cellsalign: 'right', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '盘点库存(小)', datafield: 'real_quantity', width: '100', align: 'center', cellsalign: 'center', hidden: true, hideOnLoad: true,

            },
            /*
            {
                text: '库存数', datafield: 'stork_qty', width: '6%', align: 'center', cellsalign: 'right', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    if (datafield === "stork_qty") return false;
                }
            },*/
            {
                text: '大单位', datafield: 'b_unit_qty', width: '100', align: 'center', cellsalign: 'center', columngroup: 'invent_qty',
                columntype: 'template',
                createeditor: qty_createeditor,
                initeditor: qty_initeditor,
                geteditorvalue: qty_geteditorvalue,
                cellbeginedit: function (row, datafield, columntype, value) {
                    var gridRow = $('#jqxgrid').jqxGrid('getrows')[row]
                    var unit_no = gridRow.b_unit_no
                    if (!unit_no) return false

                    if (gridRow.mum_attributes) {
                        if (gridRow.mum_attributes.find(attr => attr.distinctStock))
                            return false
                    }
                },
                cellsrenderer: function (row, columnfield, value, defaulthtml, columnproperties) {
                    var rowData = $('#jqxgrid').jqxGrid('getrows')
                    rowData = rowData[row]
                    var unit_no = rowData.b_unit_no
                    var attrSvg = '<div></div>'
                    if (rowData.mum_attributes && unit_no) {
                        attrSvg = `<svg id="svgFeather" onmousedown="onBtnAttrClick(event,${row},'b')" height="15" width="15" style="cursor:pointer">
                                        <use xlink:href="/images/images.svg#feather" />
                                    </svg>`
                    }

                    var s = `<div style="margin:4px;color:#000;display:flex;justify-content:space-between">
                        ${attrSvg}
                        <div><label>${value}</label><label>${unit_no || ''}</label></div></div>`
                    return s
                }
            },
            {
                text: '大单位单位', datafield: 'b_unit_no', width: '120', align: 'center', cellsalign: 'center', hidden: true, hideOnLoad: true, columngroup: 'invent_qty'

            },
            {
                text: '中单位', datafield: 'm_unit_qty', width: '80', align: 'center', cellsalign: 'center', columntype: 'template', columngroup: 'invent_qty',
                createeditor: qty_createeditor,
                initeditor: qty_initeditor,
                geteditorvalue: qty_geteditorvalue,
                cellbeginedit: function (row, datafield, columntype, value) {
                    var gridRow = $('#jqxgrid').jqxGrid('getrows')[row]
                    if (!gridRow.m_unit_no) return false

                    if (gridRow.mum_attributes) {
                        if (gridRow.mum_attributes.find(attr => attr.distinctStock))
                            return false
                    }

                },
                cellsrenderer: function (row, columnfield, value, defaulthtml, columnproperties) {
                    var rowData = $('#jqxgrid').jqxGrid('getrows')
                    rowData = rowData[row]
                    var unit_no = rowData.m_unit_no
                    var attrSvg = '<div></div>'
                    if (rowData.mum_attributes && unit_no) {
                        attrSvg = `<svg id="svgFeather" onmousedown="onBtnAttrClick(event,${row},'m')" height="15" width="15" style="cursor:pointer">
                                        <use xlink:href="/images/images.svg#feather" />
                                    </svg>`
                    }

                    var s = `<div style="margin:4px;color:#000;display:flex;justify-content:space-between">
                        ${attrSvg}
                        <div><label>${value}</label><label>${unit_no || ''}</label></div></div>`
                    return s
                }
            },
            {
                text: '中单位单位', datafield: 'm_unit_no', width: '120', align: 'center', cellsalign: 'center', hidden: true, hideOnLoad: true, columngroup: 'invent_qty'

            },
            {
                text: '小单位', datafield: 's_unit_qty', width: '80', align: 'center', cellsalign: 'center', columngroup: 'invent_qty',
                columntype: 'template',
                createeditor: qty_createeditor,
                initeditor: qty_initeditor,
                geteditorvalue: qty_geteditorvalue,
                cellbeginedit: function (row, datafield, columntype, value) {
                    var gridRow = $('#jqxgrid').jqxGrid('getrows')[row]
                    if (gridRow.mum_attributes) {
                        if (gridRow.mum_attributes.find(attr => attr.distinctStock))
                            return false
                    }
                },
                cellsrenderer: function (row, columnfield, value, defaulthtml, columnproperties) {
                    var rowData = $('#jqxgrid').jqxGrid('getrows')
                    rowData = rowData[row]
                    var unit_no = rowData.s_unit_no
                    var attrSvg = '<div></div>'
                    if (rowData.mum_attributes && unit_no) {

                        attrSvg = `<svg id="svgFeather" onmousedown="onBtnAttrClick(event,${row},'s')" height="15" width="15" style="cursor:pointer">
                                        <use xlink:href="/images/images.svg#feather" />
                                    </svg>`
                    }

                    var s = `<div style="margin:4px;color:#000;display:flex;justify-content:space-between">
                        ${attrSvg}
                        <div><label>${value}</label><label>${unit_no || ''}</label></div></div>`
                    return s
                }
            },
            {
                text: '小单位单位', datafield: 's_unit_no', width: '120', align: 'center', cellsalign: 'center', hidden: true, hideOnLoad: true, columngroup: 'invent_qty'
            },
            /*
            {
                text: '小单位包装率', datafield: 'unit_factor', width: '5%', align: 'center', cellsalign: 'center', hidden: true,

            },*/
            {
                text: '中单位包装率', datafield: 'm_unit_factor', width: '120', align: 'center', cellsalign: 'center', hidden: true,

            },
            {
                text: '大单位包装率', datafield: 'b_unit_factor', width: '120', align: 'center', cellsalign: 'center', hidden: true,

            },
            {
                text: '系统库存(小单位)', datafield: 'sys_quantity', width: '100', align: 'center', cellsalign: 'center', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false
                },
            },
            {
                text: '系统库存1', datafield: 'stock_qty', width: '100', align: 'center', cellsalign: 'center', hidden: true, hideOnLoad: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false
                },
            },
            {
                text: '盘盈数量', datafield: 'profit_qty', width: '100', align: 'center', cellsalign: 'right', editable: false,
                cellsrenderer: function (row, columnfield, value, defaulthtml, columnproperties) {
                    //if (value < 20) {
                    //    return '<span style="margin: 4px; float: ' + columnproperties.cellsalign + '; color: #0000ff;">' + value + '</span>';
                    //}
                    //else {
                    //    return '<span style="margin: 4px; float: ' + columnproperties.cellsalign + '; color: #008000;">' + value + '</span>';
                    //}
                    if (value) {
                        return '<span style="margin: 4px; float: ' + columnproperties.cellsalign + '; color: #008000;">' + value + '</span>';
                    }
                }, aggregates: [{
                    'pysl':
                        function (aggregatedValue, currentValue) {
                            return Number(aggregatedValue || 0) + Number(currentValue || 0);
                        }
                }],
                aggregatesrenderer: function (aggregates, column, element, summaryData) {
                    var rows = $('#jqxgrid').jqxGrid('getrows')
                    var b_qty = 0, m_qty = 0, s_qty = 0
                    rows.forEach(row => {
                        b_qty += parseFloat(row.b_profit_qty) || 0
                        m_qty += parseFloat(row.m_profit_qty) || 0
                        s_qty += parseFloat(row.s_profit_qty) || 0
                    })

                    var sum_qty = ''
                    if (b_qty != 0) sum_qty += b_qty + '大'
                    if (m_qty != 0) sum_qty += m_qty + '中'
                    if (s_qty != 0) sum_qty += s_qty + '小'
                    var renderstring = "<div class='jqx-widget-content jqx-widget-content-" + theme + "' style='float: left; width: 100%; height: 100%; '>";
                    $.each(aggregates, function (key, value) {
                        renderstring += '<div id = "sum_profit_qty" style="position: relative; margin: 6px; text-align: right; overflow: hidden;">' + sum_qty + '</div>';
                    });
                    renderstring += "</div>";
                    return renderstring;
                }

            },
            {
                text: '盘亏数量', datafield: 'loss_qty', width: '100', align: 'center', cellsalign: 'right', editable: false,
                cellsrenderer: function (row, columnfield, value, defaulthtml, columnproperties) {
                    //if (value < 20) {
                    //    return '<span style="margin: 4px; float: ' + columnproperties.cellsalign + '; color: #ff0000;">' + value + '</span>';
                    //}
                    //else {
                    //    return '<span style="margin: 4px; float: ' + columnproperties.cellsalign + '; color: #008000;">' + value + '</span>';
                    //}
                    if (value) {
                        return '<span style="margin: 4px; float: ' + columnproperties.cellsalign + '; color: #f00;">' + value + '</span>';
                    }
                }, aggregates: [{
                    'pksl':
                        function (aggregatedValue, currentValue) {
                            return Number(aggregatedValue || 0) + Number(currentValue || 0);
                        }
                }],
                aggregatesrenderer: function (aggregates, column, element, summaryData) {
                    var rows = $('#jqxgrid').jqxGrid('getrows')
                    var b_qty = 0, m_qty = 0, s_qty = 0
                    rows.forEach(row => {
                        b_qty += parseFloat(row.b_loss_qty) || 0
                        m_qty += parseFloat(row.m_loss_qty) || 0
                        s_qty += parseFloat(row.s_loss_qty) || 0
                    })


                    var sum_qty = ''
                    if (b_qty != 0) sum_qty += b_qty + '大'
                    if (m_qty != 0) sum_qty += m_qty + '中'
                    if (s_qty != 0) sum_qty += s_qty + '小'
                    var renderstring = "<div class='jqx-widget-content jqx-widget-content-" + theme + "' style='float: left; width: 100%; height: 100%; '>";
                    $.each(aggregates, function (key, value) {
                        renderstring += '<div id="sum_loss_qty" style="position: relative; margin: 6px; text-align: right; overflow: hidden;">' + sum_qty + '</div>';
                    });
                    renderstring += "</div>";
                    return renderstring;
                }
            },
            { text: '加权成本价', datafield: 'cost_price_avg', width: '100', align: 'center', cellsalign: 'right', hidden: !seeInPrice, hideOnLoad: !seeInPrice, editable: seeInPrice },


            { text: '批发价', datafield: 'wholesale_price', width: '100', align: 'center', cellsalign: 'right', hidden: true },
            {
                text: '盘盈批发金额', datafield: 'profit_wholesale_amount', width: '100', align: 'center', cellsalign: 'right', editable: false,
                cellsrenderer: function (row, columnfield, value, defaulthtml, columnproperties) {
                    //if (value < 20) {
                    //    return '<span style="margin: 4px; float: ' + columnproperties.cellsalign + '; color: #0000ff;">' + value + '</span>';
                    //}
                    //else {
                    //    return '<span style="margin: 4px; float: ' + columnproperties.cellsalign + '; color: #008000;">' + value + '</span>';
                    //}
                    if (value) {
                        return '<span style="margin: 4px; float: ' + columnproperties.cellsalign + '; color: #008000;">' + value + '</span>';
                    }
                }, aggregates: [{
                    'pypf':
                        function (aggregatedValue, currentValue) {
                            return Number(aggregatedValue || 0) + Number(currentValue || 0);
                        }
                }],
                aggregatesrenderer: function (aggregates, column, element, summaryData) {
                    var renderstring = "<div class='jqx-widget-content jqx-widget-content-" + theme + "' style='float: left; width: 100%; height: 100%; '>";
                    $.each(aggregates, function (key, value) {
                        renderstring += '<div id ="sum_profit_wholesale_amount" style="position: relative; margin: 6px; text-align: right; overflow: hidden;">' + value + '</div>';
                    });
                    renderstring += "</div>";
                    return renderstring;
                }
            },
            {
                text: '盘亏批发金额', datafield: 'loss_wholesale_amount', width: '100', align: 'center', cellsalign: 'right', editable: false,
                cellsrenderer: function (row, columnfield, value, defaulthtml, columnproperties) {
                    //if (value < 20) {
                    //    return '<span style="margin: 4px; float: ' + columnproperties.cellsalign + '; color: #0000ff;">' + value + '</span>';
                    //}
                    //else {
                    //    return '<span style="margin: 4px; float: ' + columnproperties.cellsalign + '; color: #008000;">' + value + '</span>';
                    //}
                    if (value) {
                        return '<span style="margin: 4px; float: ' + columnproperties.cellsalign + '; color: #ff0000;">' + value + '</span>';
                    }
                }, aggregates: [{
                    'pkpf':
                        function (aggregatedValue, currentValue) {
                            return Number(aggregatedValue || 0) + Number(currentValue || 0);
                        }
                }],
                aggregatesrenderer: function (aggregates, column, element, summaryData) {
                    var renderstring = "<div class='jqx-widget-content jqx-widget-content-" + theme + "' style='float: left; width: 100%; height: 100%; '>";
                    $.each(aggregates, function (key, value) {
                        renderstring += '<div id="sum_loss_wholesale_amount" style="position: relative; margin: 6px; text-align: right; overflow: hidden;">' + value + '</div>';
                    });
                    renderstring += "</div>";
                    return renderstring;
                }
            },
            { text: '进价', datafield: 'buy_price', width: '100', align: 'center', cellsalign: 'right', hidden: true, hideOnLoad: !seeInPrice, },
            {
                text: '盘盈进价金额', datafield: 'profit_buy_amount', width: '100', align: 'center', cellsalign: 'right', editable: false, hidden: !seeInPrice, hideOnLoad: !seeInPrice,
                cellsrenderer: function (row, columnfield, value, defaulthtml, columnproperties) {
                    //if (value < 20) {
                    //    return '<span style="margin: 4px; float: ' + columnproperties.cellsalign + '; color: #0000ff;">' + value + '</span>';
                    //}
                    //else {
                    //    return '<span style="margin: 4px; float: ' + columnproperties.cellsalign + '; color: #008000;">' + value + '</span>';
                    //}
                    if (value) {
                        return '<span style="margin: 4px; float: ' + columnproperties.cellsalign + '; color: #008000;">' + value + '</span>';
                    }
                }, aggregates: [{
                    'pycbje':
                        function (aggregatedValue, currentValue) {
                            return Number(aggregatedValue || 0) + Number(currentValue || 0);
                        }
                }],
                aggregatesrenderer: function (aggregates, column, element, summaryData) {
                    var renderstring = "<div class='jqx-widget-content jqx-widget-content-" + theme + "' style='float: left; width: 100%; height: 100%; '>";
                    $.each(aggregates, function (key, value) {
                        renderstring += '<div id="sum_profit_buy_amount" style="position: relative; margin: 6px; text-align: right; overflow: hidden;">' + value + '</div>';
                    });
                    renderstring += "</div>";
                    return renderstring;
                }
            },
            {
                text: '盘亏进价金额', datafield: 'loss_buy_amount', width: '100', align: 'center', cellsalign: 'right', editable: false, hidden: !seeInPrice, hideOnLoad: !seeInPrice,
                cellsrenderer: function (row, columnfield, value, defaulthtml, columnproperties) {
                    //if (value < 20) {
                    //    return '<span style="margin: 4px; float: ' + columnproperties.cellsalign + '; color: #0000ff;">' + value + '</span>';
                    //}
                    //else {
                    //    return '<span style="margin: 4px; float: ' + columnproperties.cellsalign + '; color: #008000;">' + value + '</span>';
                    //}
                    if (value) {
                        return '<span style="margin: 4px; float: ' + columnproperties.cellsalign + '; color: #ff0000;">' + value + '</span>';
                    }
                }, aggregates: [{
                    'pkcbje':
                        function (aggregatedValue, currentValue) {
                            return Number(aggregatedValue || 0) + Number(currentValue || 0);
                        }
                }],
                aggregatesrenderer: function (aggregates, column, element, summaryData) {
                    var renderstring = "<div class='jqx-widget-content jqx-widget-content-" + theme + "' style='float: left; width: 100%; height: 100%; '>";
                    $.each(aggregates, function (key, value) {
                        renderstring += '<div id="sum_loss_buy_amount" style="position: relative; margin: 6px; text-align: right; overflow: hidden;">' + value + '</div>';
                    });
                    renderstring += "</div>";
                    return renderstring;
                },
            },
            {
                text: '盘盈成本金额', datafield: 'profit_cost_amount', width: '100', align: 'center', cellsalign: 'right', editable: false, hidden: !seeInPrice, hideOnLoad: !seeInPrice,
                cellsrenderer: function (row, columnfield, value, defaulthtml, columnproperties) {
                    //if (value < 20) {
                    //    return '<span style="margin: 4px; float: ' + columnproperties.cellsalign + '; color: #0000ff;">' + value + '</span>';
                    //}
                    //else {
                    //    return '<span style="margin: 4px; float: ' + columnproperties.cellsalign + '; color: #008000;">' + value + '</span>';
                    //}
                    if (value) {
                        return '<span style="margin: 4px; float: ' + columnproperties.cellsalign + '; color: #008000;">' + value + '</span>';
                    }
                }, aggregates: [{
                    'pycbje':
                        function (aggregatedValue, currentValue) {
                            return Number(aggregatedValue || 0) + Number(currentValue || 0);
                        }
                }],
                aggregatesrenderer: function (aggregates, column, element, summaryData) {
                    var renderstring = "<div class='jqx-widget-content jqx-widget-content-" + theme + "' style='float: left; width: 100%; height: 100%; '>";
                    $.each(aggregates, function (key, value) {
                        renderstring += '<div id="sum_profit_cost_amount" style="position: relative; margin: 6px; text-align: right; overflow: hidden;">' + value + '</div>';
                    });
                    renderstring += "</div>";
                    return renderstring;
                }
            },
            {
                text: '盘亏成本金额', datafield: 'loss_cost_amount', width: '100', align: 'center', cellsalign: 'right', editable: false, hidden: !seeInPrice, hideOnLoad: !seeInPrice,
                cellsrenderer: function (row, columnfield, value, defaulthtml, columnproperties) {
                    //if (value < 20) {
                    //    return '<span style="margin: 4px; float: ' + columnproperties.cellsalign + '; color: #0000ff;">' + value + '</span>';
                    //}
                    //else {
                    //    return '<span style="margin: 4px; float: ' + columnproperties.cellsalign + '; color: #008000;">' + value + '</span>';
                    //}
                    if (value) {
                        return '<span style="margin: 4px; float: ' + columnproperties.cellsalign + '; color: #ff0000;">' + value + '</span>';
                    }
                }, aggregates: [{
                    'pkcbje':
                        function (aggregatedValue, currentValue) {
                            return Number(aggregatedValue || 0) + Number(currentValue || 0);
                        }
                }],
                aggregatesrenderer: function (aggregates, column, element, summaryData) {
                    var renderstring = "<div class='jqx-widget-content jqx-widget-content-" + theme + "' style='float: left; width: 100%; height: 100%; '>";
                    $.each(aggregates, function (key, value) {
                        renderstring += '<div id="sum_loss_cost_amount" style="position: relative; margin: 6px; text-align: right; overflow: hidden;">' + value + '</div>';
                    });
                    renderstring += "</div>";
                    return renderstring;
                }
            },


            //{
            //    text: '批发金额', datafield: 'wholesale_amount', width: '10%', align: 'center', cellsalign: 'right', aggregates: [{
            //        'xj':
            //            function (aggregatedValue, currentValue) {
            //                return Number(aggregatedValue) + Number(currentValue);
            //            }
            //    }],
            //    aggregatesrenderer: function (aggregates, column, element, summaryData) {
            //        var renderstring = "<div class='jqx-widget-content jqx-widget-content-" + theme + "' style='float: left; width: 100%; height: 100%; '>";
            //        $.each(aggregates, function (key, value) {
            //            renderstring += '<div style="position: relative; margin: 6px; text-align: right; overflow: hidden;">' + value + '</div>';
            //        });
            //        renderstring += "</div>";
            //        return renderstring;
            //    }
            //},

            { text: '备注', datafield: 'remark', align: 'center' }
            /*{
                text: '', width: '15%', pinned: true, cellsrenderer: function (row, column, value) {

                    var selectedrowindex = $('#jqxgrid').jqxGrid('selectedrowindex'); 
                     var cell = $('#jqxgrid').jqxGrid('getselectedcell');
                     return '<div style="margin:4px;"><a class="rowBtn" style="text-decoration:none;" href="#" onclick="">删除</a></div>';
                    if ((cell && row == cell.rowindex ) || selectedrowindex==row)
                        return '<div style="margin:4px;"><a style="text-decoration:none;color:#555;" href="#" onclick="">删除</a><a style="text-decoration:none;color:#555;margin-left:10px;" href="#" onclick="">删除</a></div>';
                    else
                        return '<div></div>';
                       // return '<div style="margin:4px;"><a style="text-decoration:none;color:#555;" href="#" onclick="">删除</a><a style="text-decoration:none;color:#555;margin-left:10px;" href="#" onclick="">删除</a></div>'; 

                }, align: 'center', cellsalign: 'right',
                
                //  , initeditor: initeditor
            }*/
        ]
        , columngroups:
            [
                { text: '盘点数量', align: 'center', name: 'invent_qty' }

            ]
    };

    adjustColumnsBySetting()

    $("#jqxgrid").jqxGrid(GridData);
    window.loadSheetData = function (sheetRows) {
        initProduceDate(sheetRows)
        loadSheetRows(sheetRows)
    }

    loadSheetData(sheetRows)
    //updateSheetState();
    window.setRowOnItemSelected = function (rowIndex, item) {
        /*var row = $('#jqxgrid').jqxGrid('getrowdata', rowIndex)
        row.item_id = item1.value
        row.item_name = item1.label
        var item_id = item1.value
        var item_name = item1.label      
        if (!item_id) return
        */
        var rows = []
        if (Array.isArray(item)) {
            item.forEach(i => {
                var r = { item_id: i.value, item_name: i.label }
                rows.push(r)
            })
        }
        else {
            var row = {}
            row.item_id = item.value
            row.item_name = item.label
            rows.push(row)
        }
        AddItemRows(rowIndex, rows, false, '', true, false)
        disableBranch()
    }

    window.cellendedit = function (event) {
        var args = event.args;
        var colName = args.datafield;
        var rowIndex = args.rowindex;
        var cellValue = args.value;
        var oldValue = args.oldvalue;
        if (cellValue && typeof cellValue == 'object')
            cellValue = cellValue.value
        if (oldValue && typeof oldValue == 'object')
            oldValue = oldValue.value


        if (oldValue === cellValue) return;

        if (cellValue && cellValue.value != undefined) cellValue = cellValue.value
        var row = args.row;
        row[colName] = cellValue

        var item_id = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'item_id');
        if (!item_id) return

        if (typeof item_id == 'object') {
            if (!item_id.value) return
        }
        var rows = $('#jqxgrid').jqxGrid('getrows');
        rows[rowIndex][colName] = cellValue
        onCellEndEdit(rowIndex, colName, cellValue);
        saveSheetToCach()
    }
    $("#jqxgrid").on('cellendedit', cellendedit)

    $("#jqxgrid").on('cellbeginedit', function (event) {


        var args = event.args;
        var colName = args.datafield;

        var rowIndex = args.rowindex;
        // if (rowIndex==0) return

        setFocus(rowIndex, colName)
        function setFocus(rowIndex, colName) {

            setTimeout(() => {
                var cell = $("#jqxgrid").jqxGrid('getselectedcell');
                if (cell.row != rowIndex || cell.column != colName) return
                $("#jqxgrid").jqxGrid('begincelledit', rowIndex, colName);
                $('#jqxgrid input').focus()
                //console.log('当前为' + rowIndex)
            }, 200)
        }
        updateTotalAmount();
    });

    function initProduceDate(rows) {
        rows.forEach((row) => {
            if (row.item_id && row.batch_level && row.batch_level !== "0" && !row.produce_date) {
                row.produce_date = "无产期"
                row.batch_id = "0"
            }
        })
    }
    function LoadItemInfo(rowIndex, items_id, zeroFlag, orderBy, stillAddOnExist, ignoreZeroStock) {
        var branch_id = $('#branch_id').jqxInput('val').value;
        var formData = new FormData();

        formData.append('branch_id', branch_id)
        formData.append('stockOnly', false)

        if (ignoreZeroStock) {
            formData.append('ignoreZeroStock', true)
        }
        formData.append('items_id', items_id)
        formData.append('bGetAttrs', !window.attributes)

        var happen_time = $('#happen_time').jqxDateTimeInput('val');


        formData.append('happen_time', happen_time)
        //决定orderby what
        formData.append('orderBy', orderBy)

        $('#happen_time').jqxDateTimeInput({ disabled: true })
        var approve_time = $('#approve_time').text();
        if (happen_time != "" && happen_time != approve_time) {
            $('#happen_time').parent().addClass('spec_invent_time');
        }



        $.ajax({
            url: `/api/InventorySheet/GetStockQtyList?operKey=${g_operKey}`,
            type: 'post',
            contentType: false,
            processData: false,
            data: formData,
            success: function (data) {
                if (data.result === 'OK') {
                    var total = data.records.length
                    if (total == 0) return
                    var gridRows = $('#jqxgrid').jqxGrid('getrows')
                    var nAddRows = rowIndex + total + 10 - gridRows.length
                    if (nAddRows > 0)
                        addEmptyRows(nAddRows)
                    if (data.attrOptions) window.attrOptions = data.attrOptions
                    if (data.attributes) window.attributes = data.attributes
                    let existItems = {}//本次操作之前已存在的商品行
                    var funGetRowKey = function (row) {
                        if (!row.produce_date) row.produce_date = '无产期'
                        return row.item_id + '_' + row.branch_position + '_' + row.produce_date + '_' + row.batch_no
                    }

                    gridRows.forEach((row, index) => {
                        if (row.item_id && row.item_name) {
                            if (index != rowIndex) {//当前行如果有数据，不加入已存在字典，因为当前行会被新录入的商品覆盖
                                let key = funGetRowKey(row)
                                existItems[key] = row
                            }
                        }
                    })
                    let newExistItems = {}//本次操作新增的商品集合,key是item_id
                    let showBranchPosition = false
                    let showProduceDate = false
                    let showBatchNo = false
                    var startRowIndex = rowIndex
                    data.records.forEach(function (row) {
                        if (true || (row.stock_qty && row.stock_qty !== "0")) {
                            let key = row.item_id

                            let keyForInvent = funGetRowKey(row)

                            var isAddingExistItem = false
                            if (existItems[keyForInvent]) {

                                if (!stillAddOnExist) {
                                    return
                                }
                                else if (newExistItems[key]) {
                                    return
                                }
                                else {
                                    isAddingExistItem = true
                                }
                            }

                            if (rowIndex > startRowIndex) {
                                var newRow = {}
                                newRow.uid = rowIndex
                                newRow.boundindex = rowIndex
                                newRow.visibleindex = rowIndex
                                $("#jqxgrid").jqxGrid('addrow', null, newRow, rowIndex);
                            }

                            if (row.branch_position_name) showBranchPosition = true
                            if (row.produce_date && row.produce_date != '无产期') showProduceDate = true
                            if (row.batch_no) showBatchNo = true
                            row.b_unit_qty = '';
                            row.m_unit_qty = '';
                            row.s_unit_qty = '';
                            if (zeroFlag) {
                                row.s_unit_qty = 0;
                                row.real_quantity = 0;
                            }


                            var gridRow = gridRows[rowIndex]

                            setRowFields(row)
                            for (var k in row) {
                                var v = row[k];
                                try {
                                    gridRow[k] = v
                                }
                                catch (e) { }
                            }


                            if (!row.stock_qty || row.stock_qty == "0") gridRow.editable = true

                            if (isAddingExistItem && existItems[keyForInvent].s_unit_no) {
                                //如果已在表格 存在，用户可能想输入不同产期/库位的库存，就清空 产期/库位，让用户自由输入
                                gridRow.branch_position_name = '';
                                gridRow.branch_position = '0';
                                gridRow.produce_date = '';
                                gridRow.batch_no = '';
                                gridRow.current_qty = 0
                                gridRow.sys_quantity = 0
                                gridRow.stock_qty = 0
                                gridRow.batch_id = ''
                                gridRow.editable = true
                                gridRow.uid = rowIndex
                                gridRow.boundindex = rowIndex
                                gridRow.visibleindex = rowIndex
                            }

                            gridRow.sys_quantity = gridRows[rowIndex].stock_qty
                            if (data.attrOptions) window.attrOptions = data.attrOptions
                            if (data.attributes) window.attributes = data.attributes
                            if (row.mum_attributes)
                                gridRow.mum_attributes = JSON.parse(row.mum_attributes)
                            if (gridRow.mum_attributes) {
                                var attrs = gridRow.mum_attributes
                                for (var i = attrs.length - 1; i >= 0; i--) {
                                    var attr = attrs[i]
                                    if (!attr.distinctStock) {
                                        attrs.splice(i, 1)
                                    }
                                }

                                if (gridRow.mum_attributes.length == 0) {
                                    gridRow.mum_attributes = null
                                }
                                else {
                                    gridRow.mum_attributes = JSON.stringify(attrs)
                                }
                            }
                            console.log(gridRow)
                            rowIndex++
                            if (!newExistItems[key]) newExistItems[key] = row
                        }

                    })
                    if (showBranchPosition) $('#jqxgrid').jqxGrid('showcolumn', 'branch_position');
                    if (showProduceDate) $('#jqxgrid').jqxGrid('showcolumn', 'produce_date');
                    if (showBatchNo) $('#jqxgrid').jqxGrid('showcolumn', 'batch_no');
                    var ddd = source.localdata
                    $('#jqxgrid').jqxGrid('updategrid')
                }
            }
        });
    }
    var rows = $('#jqxgrid').jqxGrid('getrows');

    rows.forEach(function (row, index) {
        if (row.item_id == "") return;
        onCellEndEdit(index, "b_unit_qty", undefined, false);
        onCellEndEdit(index, "m_unit_qty", undefined, false);
        onCellEndEdit(index, "s_unit_qty", undefined, false);
    })
    $('#jqxgrid').jqxGrid('updatebounddata')
    function checkNumber(theObj) {
        var reg = /^[-|+]?[0-9]+.?[0-9]*$/;
        if (!reg.test(theObj) && theObj) {
            bw.toast("请输入正确数字", 2000)
        }

        return theObj;
    }

    $("#btnClose").on('click', function () {
        window.parent.closeTab(window);

    });

    function disableBranch() {
        $('#branch_id').jqxInput({ disabled: true })
    }
    function isInteger(s) {
        // 排除 null 和非字符串类型（可选）
        if (s === null || s === "") return false;
        // 严格匹配：纯数字且无空格
        return /^[0-9]+$/.test(s);
    }
    function refreshStockBeforeSave(g_operKey, branch_id, items_id, actionText, cbContinue) {
        var rows = $('#jqxgrid').jqxGrid('getrows')
        console.log(rows)
        var msg = '';


        var formData = new FormData();

        formData.append('branch_id', branch_id)
        formData.append('stockOnly', false)
        formData.append('items_id', items_id)
        formData.append('bGetAttrs', !window.attributes)
        //formData.append('bCheckBeforeSave', true)

        var happen_time = $('#happen_time').jqxDateTimeInput('val');
        formData.append('happen_time', happen_time)

        $('#happen_time').jqxDateTimeInput({ disabled: true })
        var approve_time = $('#approve_time').text();
        if (happen_time != "" && happen_time != approve_time) {
            $('#happen_time').parent().addClass('spec_invent_time');
        }



        $.ajax({
            url: `/api/InventorySheet/GetStockQtyList?operKey=${g_operKey}`,
            type: 'post',
            contentType: false,
            processData: false,
            data: formData,
            success: function (data) {
                if (data.result === 'OK') {

                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i]
                        row.bMet = false
                    }
                    data.records.forEach(function (queryRow) {
                        for (var i = 0; i < rows.length; i++) {
                            var row = rows[i]
                            if (!row.item_id) continue
                            var cellItemID = row.item_id
                            var cellProduceDate = row.produce_date || ""
                            var cellBatchNo = row.batch_no ? row.batch_no : ""
                            var cellItemName = row.item_name
                            var cellCurrentQty = row.current_qty
                            var cellBranchPosition = row.branch_position || '0'
                            queryRow.branch_position = queryRow.branch_position || '0'
                            if (queryRow.item_id == cellItemID) {
                                if (cellItemName.indexOf('200克槟') >= 0) {
                                    debugger
                                }
                                var batchMet = false
                                if (isInteger(row.batch_id) && isInteger(queryRow.batch_id) && row.batch_id != queryRow.batch_id) {

                                }
                                else {
                                    batchMet = (queryRow.batch_no == cellBatchNo && produceDateEqual(queryRow.produce_date, cellProduceDate))
                                }

                                if (batchMet && queryRow.branch_position == cellBranchPosition) {
                                    row.bMet = true
                                    if ((row.stock_qty || 0) != (queryRow.stock_qty || 0)) {
                                        row.current_qty = queryRow.current_qty;
                                        row.stock_qty = queryRow.stock_qty;

                                        onCellEndEdit(i, "b_unit_qty", undefined, false);
                                        onCellEndEdit(i, "m_unit_qty", undefined, false);
                                        onCellEndEdit(i, "s_unit_qty", undefined, false);

                                        msg += '第' + (i + 1) + '行:' + cellItemName + '<br/>';
                                        break

                                    }
                                }
                            }

                        }

                    })


                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i]
                        if (!row.item_id) continue
                        if (!row.stock_qty) continue

                        if (!row.bMet && parseFloat(row.stock_qty)) {
                            row.current_qty = '';
                            row.stock_qty = 0;
                            onCellEndEdit(i, "b_unit_qty", undefined, false);
                            onCellEndEdit(i, "m_unit_qty", undefined, false);
                            onCellEndEdit(i, "s_unit_qty", undefined, false);
                            msg += '第' + (i + 1) + '行:' + row.item_name + '<br/>';
                        }
                        row.bMet = false
                    }

                    if (msg) {
                        $('#jqxgrid').jqxGrid('updatebounddata')
                        msg = `以下商品库存变了, 请确定盘点数量后再${actionText}.\r\n` + msg;
                        showLongMsg(msg)
                        updateTotalAmount()

                    } else {
                        cbContinue()
                    }
                }
            }
        })
        return msg;
    }
    function GetSheetData() {
        var msg = "";
        var formData = getFormData();
        console.log(formData)
        formData.OperKey = g_operKey;
        var rows = $('#jqxgrid').jqxGrid('getrows');
        rows = JSON.parse(JSON.stringify(rows))
        var branch_id = $("#branch_id").val().value
        var sheetRows = new Array;
        var items_id = '';
        var existItems = {}
        for (var i = 0; i < rows.length; i++) {

            // var row = $('#jqxgrid').jqxGrid('getrowdata', i);
            var sheetRow = rows[i];
            if (sheetRow.sys_quantity === '') delete sheetRow.sys_quantity
            if (sheetRow.real_quantity === '') delete sheetRow.real_quantity//否则会导致不能保存
            if (!sheetRow.item_id && sheetRow.item_name) {
                msg = `第${i + 1}行 ${sheetRow.item_name}商品ID为空,请删除后重新添加`
                break
            }

            if (sheetRow.item_id) {
                sheetRow["sheet_item_name"] = sheetRow.item_name
                var batchInfo = sheetRow.produce_date + '_' + sheetRow.batch_no
                if (sheetRow.batch_id && (!sheetRow.produce_date || sheetRow.produce_date == '无产期') && !sheetRow.batch_no) {//stock表中有该batch_id但是在info_item_batch中没有，这时候需要取ID,否则会和batch_id为0的误判为重复行
                    batchInfo = 'id' + sheetRow.batch_id
                }
                var itemKey = sheetRow.item_id + '_' + batchInfo + '_' + sheetRow.branch_position
                var itemInfo = existItems[itemKey]
                if (!sheetRow.produce_date) sheetRow.produce_date = ''
                if (!sheetRow.batch_no) sheetRow.batch_no = ''
                if (!sheetRow.branch_position) sheetRow.branch_position = '0'
                //sheetRow.branch_id = branch_id

                if (itemInfo) {
                    msg = `第${i + 1}行 ${sheetRow.item_name}在${itemInfo.rowIndex}行已存在,请删除`
                    break
                }
                if (sheetRow.produce_date && !isDateValid(sheetRow.produce_date)) {
                    msg = '请输入第' + (i + 1) + '行商品正确的生产日期:230901或2023-09-01';
                    break;
                }
                if (!sheetRow.produce_date && sheetRow.batch_no) {
                    msg = '请输入' + sheetRow.item_name + '的生产日期';
                    break;
                }
                var bHaveValidProduceDate = sheetRow.produce_date && sheetRow.produce_date != '无产期'
                var bHaveInvalidBatchlevel = !sheetRow.batch_level || sheetRow.batch_level === "0"
                if (bHaveInvalidBatchlevel && (sheetRow.batch_no || bHaveValidProduceDate)) {
                    msg = '第' + (i + 1) + '行商品没有开启严格产期/批次管理';
                    break;
                }
                /*
                暂时去掉，有些客户需要不严格录入产期，2024-12-16 大相要求加入校验
               
                }*/

                if (sheetRow.batch_level && !sheetRow.produce_date) {
                    msg = '请输入第' + (i + 1) + '行商品正确的生产日期, 或选择"无产期"';
                    break;
                }
                if (sheetRow.batch_level == "1" && sheetRow.batch_no) {
                    msg = '第' + (i + 1) + '行商品没有开启批次管理';
                    break;
                }
                existItems[itemKey] = {
                    value: 1,
                    rowIndex: Number(i + 1)
                }


                sheetRows.push(sheetRow)

                if (items_id != '') items_id += ','
                items_id += sheetRow.item_id
            }
        }
        var branch_id = $('#branch_id').jqxInput('val').value
        formData.SheetRows = sheetRows
        var result = msg ? 'Error' : "OK"
        return { result: result, msg: msg, sheet: formData, items_id, branch_id }
    }
    function checkHasSonItem() {
        var rows = $('#jqxgrid').jqxGrid('getrows');
        var newRows = []
        rows.forEach(r => {
            if (r.item_id !== "") newRows.push(r)
        })
        console.log(newRows)

        for (let i = 0; i < newRows.length; i++) {
            let mumAttrs = newRows[i].mum_attributes
            if (mumAttrs && mumAttrs !== "") {
                if (typeof mumAttrs === "string") mumAttrs = JSON.parse(mumAttrs)
                for (let j = 0; j < mumAttrs.length; j++) {
                    let mumAttr = mumAttrs[j]
                    if (mumAttr.distinctStock && mumAttr.attrName === "口味" && mumAttr.options.length > 0) {
                        bw.toast(`第${newRows[i].boundindex + 1} 行商品存在子口味，请盘点子商品`)
                        return { result: 'ERROR' }
                    }
                }
            }

        }
        return { result: 'OK' }
    }

    $("#btnSave").on('click', function () {
        let result = checkHasSonItem()
        if (result.result === 'ERROR') return
        updateTotalAmount()
        var res = GetSheetData();
        if (res.result === "Error") {
            bw.toast(res.msg, 5000); return;
        }
        var sheet = res.sheet;
        console.log(sheet)

        var err = refreshStockBeforeSave(g_operKey, res.branch_id, res.items_id, '保存', () => {
            closeLongMsg()
            $.ajax({
                url: '/api/InventorySheet/Save',
                type: 'POST',
                async: false,
                contentType: 'application/json',
                data: JSON.stringify(sheet),
                success: function (data) {
                    if (data.result === 'OK') {
                        $('#sheet_id').val(data.sheet_id)
                        $('#sheet_no').text(data.sheet_no)
                        removeSheetFromCach()
                        bw.toast('保存成功', 3000)
                        updateSheetState()
                        $('#btnExport').attr('disabled', false)
                    }
                    else {
                        bw.toast(data.msg, 3000)
                    }
                },
                error: function (xhr) {
                    console.log("返回响应信息：" + xhr.responseText);
                }
            })
        })

        // if (err) {
        //     bw.toast(err, 3000)
        //     return
        // }

    })

    $("#btnAdd").on('click', function () {
        window.parent.newTabPage('盘点单', `Sheets/InventorySheet?`);
    })

    $("#btnDelete").on('click', function () {

        var sheet_id = $('#sheet_id').val()
        jConfirm('确定要删除本单据吗？', function () {
            $.ajax({
                url: '/api/InventorySheet/Delete',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ operKey: g_operKey, sheet_id: sheet_id }),
                success: function (data) {
                    if (data.result === 'OK') {
                        //$("#btnApprove").attr('disabled', true);
                        $("#btnSave").attr('disabled', true);
                        $("#btnApprove").attr('disabled', true);
                        $("#btnDelete").attr('disabled', true);
                        $("#btnPrint").attr('disabled', true);
                        $("#btnCopy").attr('disabled', true);
                        $("#btnAdd").attr('disabled', true);
                        removeSheetFromCach()
                        bw.toast('删除成功,即将关闭窗口', 3000);
                        setTimeout(function () {
                            window.parent.closeTab(window);
                        }, 2000);

                    }
                    else {
                        bw.toast(data.msg, 3000);
                    }
                },
                error: function (xhr) {
                    console.log("返回响应信息：" + xhr.responseText);
                }
            });
        }, "");

    });
    $("#btnCopy").on('click', function () {
        var res = GetSheetData();
        if (res.result === "Error") {
            bw.toast(res.msg, 5000); return;
        }

        var sheet = res.sheet;
        refreshStockBeforeSave(g_operKey, res.branch_id, res.items_id, '复制', () => {
            closeLongMsg()
        })
    })


    function checkNotListItemsForFullInvent() {
        var res = GetSheetData();
        if (res.result === "Error") {
            bw.toast(res.msg, 5000); return;
        }
        var sheet = res.sheet;
        var rows = sheet.SheetRows
        var branch_id = $('#branch_id').jqxInput('val').value;
        if (!branch_id) {
            bw.toast('请先选择仓库', 1000)
            return
        }
        var formData = new FormData();
        formData.append('stockOnly', true);
        formData.append('ignoreZeroStock', true);

        formData.append('branch_id', branch_id);
        formData.append('bGetAttrs', !window.attributes)
        var happen_time = $('#happen_time').jqxDateTimeInput('val');
        formData.append('happen_time', happen_time)
        $.ajax({
            url: `/api/InventorySheet/GetStockQtyList?operKey=${g_operKey}`,
            type: 'post',
            contentType: false,
            processData: false,
            data: formData,//JSON.stringify({ operKey: g_operKey, branch_id: branch_id, stockOnly: true }),
            success: function (data) {
                if (data.result === 'OK') {
                    //if (data.records.length !== rows.length) {
                    let otherRows = JSON.parse(JSON.stringify(data.records))
                    rows.forEach(row => {
                        let rowBranchPosition = row.branch_position ? row.branch_position : "0"
                        let rowBatchNo = row.batch_no ? row.batch_no : ""
                        let rowProduceDate = row.produce_date ? row.produce_date : ""
                        otherRows = otherRows.filter(e => {
                            let branchPosition = e.branch_position ? e.branch_position : "0"
                            let batchNo = e.batch_no ? e.batch_no : ""
                            let produceDate = e.produce_date ? e.produce_date : ""
                            return e.item_id !== row.item_id || rowBranchPosition !== branchPosition || rowBatchNo !== batchNo || !produceDateEqual(rowProduceDate, produceDate)
                        })
                    })

                    if (otherRows.length > 0) {
                        otherRows.forEach(row => {
                            if (!row.produce_date) {
                                row.produce_date = '无产期'
                            }
                        })
                        $("#popInventoryAlert").jqxWindow('setContent', `<iframe src="../BaseInfo/InventoryAlert?operKey=${g_operKey}" width="100%" height="100%" scrolling="auto" frameborder="no" id="inventoryAlert"></iframe>`);
                        document.getElementById("inventoryAlert").onload = function () {
                            document.getElementById("inventoryAlert").contentWindow.postMessage(JSON.stringify(otherRows), '*');
                        }
                        $('#popInventoryAlert').jqxWindow('open');
                    }
                    else {
                        SaveAndApprove("确定审核吗？")
                    }
                }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown, e1, e2) {
                bw.toast('文件可能已被编辑过,请重新选择')
                var file = $("#fileImportExcel")
                file.val('')
            }
        })
    }
    $("#btnApprove").on('click', function () {
        let result = checkHasSonItem()
        if (result.result === 'ERROR') return
        var inventoryType = $("#inventory_type").val().value
        console.log(inventoryType)
        if (inventoryType === "0") {//部分盘点
            SaveAndApprove("确定审核吗？")
        } else if (inventoryType === "1") {//整仓盘点
            checkNotListItemsForFullInvent()
        } else {
            bw.toast("请选择盘点类型")
            return
        }
    });



    $("#btnPrint").on('click', function () {


        //获取表格的类型
        var sheetType = $('#sheet_type').val();
        var sheet_id = $('#sheet_id').val();

        $.ajax({
            url: '/api/PrintTemplate/GetTemplateToUse',
            type: 'GET',
            contentType: 'application/json',
            data: {
                operKey: g_operKey,
                sheetType: sheetType
            },
            success: function (printInfo) {
                if (printInfo.result === 'OK') {
                    if (printInfo.templateList.length == 0) {
                        bw.toast("没有可用打印模板", 5000)
                        return
                    }

                    var tmp = printInfo.templateList[0]
                    tmp = JSON.parse(tmp.template_content)


                    $.ajax({
                        url: '/api/InventorySheet/GetSheetToPrint',
                        type: 'GET',
                        contentType: 'application/json',
                        data: {
                            operKey: g_operKey,
                            sheet_id: sheet_id
                        },
                        success: function (data) {
                            if (data.result === 'OK') {
                                var sheet = data.sheet
                                var sheetRows = sheet.sheetRows
                                var qtyFlag = false
                                sheetRows.forEach(function (row) {
                                    row.barcode = row.s_barcode
                                    if (!row.real_quantity && !row.s_unit_qty && !row.b_unit_qty && !row.m_unit_qty) {
                                        row.add_qty = ""
                                        qtyFlag = true
                                    }

                                })
                                if (!sheet.sum_inventory_qty && qtyFlag) {
                                    sheet.sum_add_qty = ""
                                }

                                var container = window.parent.CEFPrinter
                                if (!container)
                                    container = window.parent.CefGlue

                                if (!container)
                                    container = window.parent

                                if (!container.printSheetByTemplate) {
                                    bw.toast('在客户端程序中才可以打印', 3000)
                                    return
                                }


                                window.parent.g_SheetsWindowForPrint = window.srcWindow
                                container.printSheetByTemplate(sheet, tmp, true, printInfo.cloudPrinters, printInfo.variables)

                                var clientVersion = 0
                                if (window.parent && window.parent.CefGlue) {
                                    clientVersion = window.parent.CefGlue.getClientVersion()
                                }

                                if (parseFloat(clientVersion) < 3.32) {
                                    $.ajax({
                                        url: '/api/Printer/PrintMark',
                                        type: 'POST',
                                        contentType: 'application/json',
                                        data: JSON.stringify({
                                            operKey: g_operKey,
                                            sheetType: 'X',
                                            sheetIDs: sheet.sheet_id,
                                            printEach: true,
                                            printSum: false
                                        }),
                                        success: function (data) {
                                            if (data.result === 'OK') {
                                            }
                                            else {

                                            }
                                        },
                                        error: function (xhr) {
                                            // console.log("返回响应信息：" + xhr.responseText)
                                        }
                                    })
                                }
                            }
                            else {
                                bw.toast(data.msg, 3000)
                            }
                        },
                        error: function (xhr) {
                            bw.toast('获取单据信息失败')
                            console.log("返回响应信息：" + xhr.responseText)
                        }
                    })




                }
                else {
                    bw.toast(data.msg, 3000)
                }
            },
            error: function (xhr) {
                bw.toast('网络连接失败')
                console.log("返回响应信息：" + xhr.responseText)
            }
        })
    })
    function handleExportDetailExcel(pritntSheetInfo) {
        // 考虑多个单据情况，每个单据信息为一个item
        let excelData = []
        // 用于自动调整列宽，多个表格，暂时默认取第一个表单相关数据，后续可以针对于条码等进行优化，提高性能
        let bodyTitleName = []
        let merges = [] // 表头合并
        // 表头 表中 表尾 进行合并
        let specialCellConfig = [] // 对特殊单元格进行配置
        var sheet = pritntSheetInfo
        var sheetStatus = "(未审核)"
        if (sheet.approve_time) sheetStatus = "(已审核)"
        const sheetTitle = "盘点单" + sheetStatus

        const otherTitle = [
            [
                { titleName: '单 号:', titleKey: 'sheet_no' },
                { titleName: '业务员:', titleKey: 'seller_name' },
                { titleName: '仓 库:', titleKey: 'branch_name' },
                { titleName: '交易日期:', titleKey: 'happen_time' },
            ]
        ]

        let excelSheetOtherTitle = handleNameAndKeyData(sheet, otherTitle)
        const excelSheetHeader = [[sheetTitle], ...excelSheetOtherTitle]

        const sheetBodyArr = [
            { titleName: '序号', titleKey: 'index', showTotal: false },   // 特殊处理
            { titleName: '商品名称', titleKey: 'item_name', showTotal: false },
            { titleName: '生产日期', titleKey: 'produce_date', showTotal: false },
            { titleName: '批次', titleKey: 'batch_no', showTotal: false },
            { titleName: '商品条码(小)', titleKey: 's_barcode', showTotal: false },
            { titleName: '商品条码(大)', titleKey: 'b_barcode', showTotal: false },
            { titleName: '单位换算', titleKey: 'unit_conv', showTotal: false },
            { titleName: '系统库存', titleKey: 'current_qty', showTotal: false },
            { titleName: '系统库存(小单位)', titleKey: 'sys_quantity', showTotal: false },
            { titleName: '占用库存', titleKey: 'sell_pend_qty', showTotal: false },
            { titleName: '盘点数量', titleKey: 'invent_qty', showTotal: true, showTotalKey: 'sum_inventory_qty' },
            { titleName: '盘盈数量', titleKey: 'profit_qty', showTotal: true, showTotalKey: 'sum_profit_qty' },
            { titleName: '盘亏数量', titleKey: 'loss_qty', showTotal: true, showTotalKey: 'sum_loss_qty' },
            { titleName: '盘盈批发金额', titleKey: 'profit_wholesale_amount', showTotal: true, showTotalKey: 'sum_profit_wholesale_amount' },
            { titleName: '盘亏批发金额', titleKey: 'loss_wholesale_amount', showTotal: true, showTotalKey: 'sum_loss_wholesale_amount' },
            { titleName: '盘盈成本金额', titleKey: 'profit_cost_amount', showTotal: true, showTotalKey: 'sum_profit_cost_amount' },
            { titleName: '盘亏成本金额', titleKey: 'loss_cost_amount', showTotal: true, showTotalKey: 'sum_loss_cost_amount' },
            { titleName: '盘盈进价金额', titleKey: 'profit_buy_amount', showTotal: true, showTotalKey: 'sum_profit_buy_amount' },
            { titleName: '盘亏进价金额', titleKey: 'loss_buy_amount', showTotal: true, showTotalKey: 'sum_loss_buy_amount' },
            { titleName: '备注', titleKey: 'remark', showTotal: false },
        ]
        let excelSheetBody = {
            bodyTitleName: [],    // 表格列表的中文名
            bodyListData: [],     // 表格列表数据部分 [{'titleKey':sheetRows[titleKey]}],[{'titleKey':sheetRows[titleKey]}]
        }
        sheet.sheetRows = sheet.SheetRows


        handleSheetBodyData(sheet, sheetBodyArr, excelSheetBody)
        const sheetFooter = [
        ]
        const excelSheetFooter = handleFooterPayWayInfo(sheet, sheetFooter)
        const dataList = handleMergeData(excelSheetHeader, excelSheetBody, excelSheetFooter)
        if (bodyTitleName.length === 0) {
            bodyTitleName = excelSheetBody.bodyTitleName
        }
        excelData.push(dataList)

        const data = excelData.reduce((previousValue, currentValue) => {
            let configObj = {}
            // 处理表头
            let start = Number(previousValue.length + 1)
            let end = start
            configObj = {
                type: 's',
                configObj: { font: { sz: 14, bold: true } },
                controlScope: 'col',  //'cell' / 'col'
                scope: [start, end]
            }
            specialCellConfig.push(configObj)
            // 处理表格
            start = Number(previousValue.length + 3)
            end = start + Number(currentValue.length - 5)
            configObj = {
                type: 's',
                configObj: {
                    border: {
                        top: { style: "thin" },
                        bottom: { style: "thin" },
                        left: { style: "thin" },
                        right: { style: "thin" },
                    }
                },
                controlScope: 'col',  //'cell' / 'col'
                scope: [start, end]
            }
            specialCellConfig.push(configObj)
            return previousValue.concat(currentValue)
        }, [])
        webExportExcel(data, "盘点单", merges, bodyTitleName, specialCellConfig)
    }
    $("#btnExport").on('click', function () {
        var res = GetSheetData();
        if (res.result === "Error") {
            bw.toast(res.msg, 5000); return;
        }
        var sheet = res.sheet;
        var idList = ["sum_profit_qty", "sum_loss_qty", "sum_profit_wholesale_amount", "sum_loss_wholesale_amount", "sum_profit_cost_amount", "sum_loss_cost_amount", "sum_profit_buy_amount", "sum_loss_buy_amount"]
        idList.forEach(function (idName) {
            var temp_val = ""
            var temp_name = "#" + idName
            if ($(temp_name)[0] && "innerText" in $(temp_name)[0]) {
                temp_val = $(temp_name)[0].innerText
            }
            sheet[idName] = temp_val
        })
        var b_qty = 0, m_qty = 0, s_qty = 0
        sheet.SheetRows.forEach(function (row) {
            b_qty += parseFloat(row.b_unit_qty) || 0
            m_qty += parseFloat(row.m_unit_qty) || 0
            s_qty += parseFloat(row.s_unit_qty) || 0
            var qty = ''
            if (row.b_unit_qty) {
                qty += row.b_unit_qty + row.b_unit_no
            }
            if (row.m_unit_qty) {
                qty += row.m_unit_qty + row.m_unit_no
            }
            if (row.s_unit_qty) {
                qty += row.s_unit_qty + row.s_unit_no
            }
            row.invent_qty = qty
        })
        var sum_qty = ""
        if (b_qty != 0) sum_qty += b_qty + '大'
        if (m_qty != 0) sum_qty += m_qty + '中'
        if (s_qty != 0) sum_qty += s_qty + '小'
        sheet.sum_inventory_qty = sum_qty
        console.log(sheet)
        handleExportDetailExcel(sheet)
    })

    window.onresize();
    window.moveOtherRows = function (rowIndex, item_id) {
        var gridRows = $('#jqxgrid').jqxGrid('getrows')
        let newRows = JSON.parse(JSON.stringify(gridRows[rowIndex - 1]))
        newRows.branch_position_name = '';
        newRows.branch_position = '0';
        newRows.produce_date = '';
        newRows.batch_no = '';
        newRows.current_qty = 0
        newRows.sys_quantity = 0
        newRows.stock_qty = 0
        newRows.batch_id = ''
        newRows.editable = true
        newRows.uid = rowIndex
        newRows.boundindex = rowIndex
        newRows.visibleindex = rowIndex
        $("#jqxgrid").jqxGrid('addrow', null, newRows, rowIndex);
    }

    $("#jqxgrid").on("rowclick", function (event) {
        var args = event.args;
        let rowIndex = args.rowindex
        var item_id = $("#jqxgrid").jqxGrid('getcellvalue', rowIndex, "item_id");
        if (args.originalEvent.button == 2 && item_id) {
            window.inventRowIndex = rowIndex
            $('#popBatchOperation').css("display", "block")
        }
    })
    $("body").keydown(function (e) {
        var cell = $('#jqxgrid').jqxGrid('getselectedcell');
        window.inventRowIndex = cell.rowindex;
        let rowIndex = window.inventRowIndex
        var item_id = $("#jqxgrid").jqxGrid('getcellvalue', rowIndex, "item_id");
        if (e.which == 187) {//+
            moveOtherRows(rowIndex + 1, item_id)
            return false
        }
    })

}


function myIsNumber(value) {
    if (value === undefined) return false
    if (value === null) return false
    value = value.toString().trim()
    if (value === '') return false
    return !isNaN(value)
}
function updateTotalAmount() {
    var rows = $('#jqxgrid').jqxGrid('getrows');
    var wholesale_amt = 0;
    var cost_amt = 0;
    var buy_amt = 0;
    for (var i = 0; i < rows.length; i++) {
        var row = rows[i];
        var amt = toMoney(row.loss_wholesale_amount) + toMoney(row.profit_wholesale_amount);
        if (parseFloat(amt)) {
            wholesale_amt += parseFloat(amt);
        }
        var cost = toMoney(row.profit_cost_amount) + toMoney(row.loss_cost_amount);
        if (parseFloat(cost)) {
            cost_amt += parseFloat(cost);
        }
        var buy = toMoney(row.profit_buy_amount) + toMoney(row.loss_buy_amount);
        if (parseFloat(buy)) {
            buy_amt += parseFloat(buy);
        }
    }
    $('#jqxgrid').jqxGrid('updategrid')
    $('#wholesale_amount').jqxInput('val', wholesale_amt);
    $('#cost_amount_avg').jqxInput('val', cost_amt);
    $('#buy_amount').jqxInput('val', buy_amt);
}
function getQtyUnit(qty, b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no) {
    let b_qty = 0, m_qty = 0, s_qty = 0
    let leftQty = qty

    let unitsQty = "";
    let absLeftQty = Math.abs(leftQty);
    let flag = leftQty < 0 ? -1 : 1;

    if (b_unit_factor) {
        b_qty = parseInt(absLeftQty / b_unit_factor);
        absLeftQty = absLeftQty % b_unit_factor;
        if (b_qty < 0.001) {
            b_qty = 0;
        }
        if (b_qty > 0) {
            b_qty *= flag;
            unitsQty += toMoney(b_qty) + b_unit_no
        }
    }
    if (m_unit_factor) {
        m_qty = parseInt(absLeftQty / m_unit_factor)
        absLeftQty = absLeftQty % m_unit_factor;
        if (m_qty < 0.001) {
            m_qty = 0;
        }
        if (m_qty > 0) {
            m_qty *= flag;
            unitsQty += toMoney(m_qty) + m_unit_no
        }
    }
    s_qty = absLeftQty
    if (s_qty < 0.001) {
        s_qty = 0
    }

    if (s_qty > 0) {
        s_qty *= flag;
        unitsQty += toMoney(s_qty) + s_unit_no;
    }

    return {
        qtyUnit: unitsQty,
        b_qty: b_qty,
        m_qty: m_qty,
        s_qty: s_qty
    };
}




