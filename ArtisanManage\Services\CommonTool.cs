using ArtisanManage.Models;
using HuaWeiObsController;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.CodeAnalysis.Elfie.Diagnostics;
using Newtonsoft.Json;
using NPOI.HPSF;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.DrawingCore;
using System.DrawingCore.Imaging;
using System.Dynamic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Security;
using System.Reflection;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Xml.Serialization;
using Newtonsoft.Json.Linq;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory.Database;
using ArtisanManage.YingJiangMallMini.Model;

namespace ArtisanManage.Services
{
    public static class CommonTool
    {
        internal const string defaultPaywayCondi = "company_id = ~COMPANY_ID and coalesce(for_pay,true)  and coalesce(status,'1')='1' and sub_type is not null ";
        internal const string restrictPayWayCondi = $@" and (s.sub_id::text IN (
            SELECT 
                json_array_elements_text(avail_pay_ways) AS individual_value 
            FROM 
                info_operator 
            WHERE  company_id = ~COMPANY_ID AND oper_id = ~OPER_ID AND restrict_pay_ways = TRUE 
            )
        OR
        (   SELECT 
                COUNT(*) 
            FROM 
                info_operator 
            WHERE  company_id = ~COMPANY_ID AND oper_id = ~OPER_ID AND restrict_pay_ways = TRUE ) = 0 
        )";
        //internal const string selectPayWay = "select s.sub_id as v,s.sub_name as l,s.py_str as z,(case when payway_type in ('YS','YF') then 't'  else 'f' end) hide from cw_subject s  left join info_pay_way p on s.sub_id=p.sub_id where p.company_id=~COMPANY_ID and (p.is_order is not true) and payway_type in ('YS','QT') order by payway_index";
        internal const string selectPayWay = @$"select s.sub_id as v,s.sub_name as l, s.py_str as z,sub_type,(case when sub_type in ('YS','YF','ZC','QTSR') then 't'  else 'f' end) hide from cw_subject s where s.{defaultPaywayCondi} and coalesce(s.is_order,false)=false and sub_type in ('YS','YF','QT','ZC','QTSR') {restrictPayWayCondi} order by order_index";
        internal const string selectPayWay_Sale = @$"select s.sub_id as v,s.sub_name as l, s.py_str as z,sub_type, (case when sub_type in ('YS','YF','ZC','QTSR') then 't'  else 'f' end) hide,q.shortcut as shortcut  from cw_subject s left join 
        info_pay_qrcode q on s.sub_id = q.sub_id and s.company_id = q.company_id where s.{defaultPaywayCondi} and
            (
                  (sub_type in ('YS','QT') and coalesce(for_pay,true ))
               or (sub_type in ('ZC')      and coalesce(for_pay,false))
            )
            and coalesce(s.is_order,false)=false {restrictPayWayCondi} order by case sub_type when 'QT' then 0 when 'YS' then 1 else 2 end, order_index";
        
        internal const string selectPayWayFeeOut = $@"select sub_id as v,sub_name as l,py_str as z,sub_type,'f' as hide from cw_subject where {defaultPaywayCondi} and sub_type IN ( 'QT','YS') and coalesce(is_order,false)=false ";
        internal const string selectPayWayFeeIn = $@"select sub_id as v,sub_name as l,py_str as z,sub_type,'f' as hide from cw_subject where {defaultPaywayCondi} and sub_type IN ( 'QT','YF') ";

        internal const string selectPayWayPrepay = $@"SELECT s.sub_id AS v,s.sub_name AS l, s.py_str AS z,sub_type FROM cw_subject s where s.{defaultPaywayCondi} and s.sub_type IN ( 'QT', 'QTSR' ) {restrictPayWayCondi} ORDER BY order_index ";
        internal const string selectPayWayPreget = $@"SELECT s.sub_id AS v,s.sub_name AS l, s.py_str AS z,sub_type FROM cw_subject s where s.{defaultPaywayCondi} and s.sub_type IN ( 'QT', 'ZC' ) {restrictPayWayCondi} ORDER BY order_index ";

        internal const string selectPayWayGetArrears = $@"select sub_id as v,sub_name as l,py_str as z,sub_type,(case when sub_type in ('YS','YF') then 't'  else 'f' end) hide  from cw_subject where {defaultPaywayCondi} and sub_type in ('QT','YS','YF') and COALESCE(is_order,false)=false {restrictPayWayCondi}";
       // internal const string selectPayWayPrepay = $@"SELECT s.sub_id AS v,s.sub_name AS l, s.py_str AS z FROM cw_subject s where s.{defaultPaywayCondi} and s.sub_type IN ( 'QT', 'QTSR' ) {restrictPayWayCondi} ORDER BY order_index ";
       // internal const string selectPayWayGetArrears = $@"select sub_id as v,sub_name as l,py_str as z,(case when sub_type in ('YS','YF') then 't'  else 'f' end) hide  from cw_subject where {defaultPaywayCondi} and sub_type in ('QT','YS','YF') and COALESCE(is_order,false)=false {restrictPayWayCondi}";
        internal const string selectPayWayCombineSheet=$@"select sub_id as v, sub_name as l from cw_subject s where {defaultPaywayCondi} and sub_type = 'ZC' {restrictPayWayCondi}";
        internal const string selectPayWay_Sale_LeftAmout = selectPayWay_Sale + " union select 0 as v,'欠款' as l";
        internal const string selectPayWay_Buy =  @$"select s.sub_id as v,s.sub_name as l, s.py_str as z,sub_type, (case when sub_type in ('YS','YF','ZC','QTSR') then 't'  else 'f' end) hide  from cw_subject s where s.{defaultPaywayCondi} and coalesce(s.is_order,false)=false and sub_type in ('YF','QT','QTSR') {restrictPayWayCondi} order by case sub_type when 'QT' then 0 when 'YF' then 1 else 2 end, order_index";
        internal const string selectPayWay_BuyReturn = @$"select s.sub_id as v,s.sub_name as l, s.py_str as z,sub_type, (case when sub_type in ('YS','YF','ZC','QTSR') then 't'  else 'f' end) hide  from cw_subject s where s.{defaultPaywayCondi} and coalesce(s.is_order,false)=false and sub_type in ('YF','QT','QTSR','ZC') {restrictPayWayCondi} order by case sub_type when 'QT' then 0 when 'YF' then 1 else 2 end, order_index";
        //internal const string selectPayWay = "SELECT s.sub_id AS v,s.sub_name AS l FROM cw_subject s where s.company_id=~COMPANY_ID and (s.is_order is not true) and sub_type in ('YS','YF','QT','ZC','QTSR')";
        internal const string SelectPayWayNormal = @$"select s.sub_id as v,s.sub_name as l, s.py_str as z,sub_type,false hide from cw_subject s where s.{defaultPaywayCondi} and sub_type ='QT' {restrictPayWayCondi} order by order_index";
        internal const string selectPayWayYF = $@"select sub_id as v,sub_name as l,py_str as z,sub_type,'f' as hide from cw_subject s where {defaultPaywayCondi} and sub_type IN ('YF') {restrictPayWayCondi}";
        internal const string selectPayWayQTSR = $@"select sub_id as v,sub_name as l,py_str as z,sub_type,'f' as hide from cw_subject s where {defaultPaywayCondi} and sub_type IN ('QTSR') {restrictPayWayCondi}";
        internal const string SelectPayWayOrderItemAdjust = $@"select sub_id v,sub_name l from cw_subject s where {defaultPaywayCondi} and sub_type ='YS' and coalesce(is_order,false)=false  {restrictPayWayCondi}";


        internal const string selectCashBankAccount = "select qrcode_id as v, account_name as l from info_pay_qrcode where company_id=~COMPANY_ID order by account_no";
        internal const string selectClasses = "select class_id as v,class_name as l,py_str as z,mother_id as pv from info_item_class  where company_id = ~COMPANY_ID and class_id<>-1 and coalesce(cls_status,'1') = '1' order by order_index,class_name,class_id";
        internal const string selectBrands = "select -1 as v,'无品牌' as l union (select brand_id as v, brand_name as l from info_item_brand where company_id = ~COMPANY_ID and coalesce(brand_status,'1')='1' and ~QUERY_CONDITION order by brand_order_index ) ";
        internal const string selectGroups = "select -1 as v,'无渠道' as l union (select group_id as v, group_name as l from info_supcust_group where company_id = ~COMPANY_ID and ~QUERY_CONDITION) ";
        internal const string selectExistBrands = "select brand_id as v, brand_name as l from info_item_brand where company_id = ~COMPANY_ID and coalesce(brand_status,'1')='1' order by brand_order_index ";
        //internal const string selectSellers = "select -1 as v,'未指定' as l union (select oper_id as v,oper_name as l,order_index from info_operator where company_id=~COMPANY_ID and is_seller and COALESCE(status,'1')='1' and ~QUERY_CONDITION order by order_index,oper_name)";
        internal const string selectSellers = "select -1 as v,'未指定' as l,-1 as order_index union (select oper_id as v,oper_name as l,order_index from info_operator where company_id=~COMPANY_ID and is_seller and COALESCE(status,'1')='1' and ~QUERY_CONDITION ) order by order_index, l";
        internal const string selectSellers_without_no_specify = "select oper_id as v,oper_name as l,order_index from info_operator where company_id=~COMPANY_ID and is_seller and COALESCE(status,'1')='1' and ~QUERY_CONDITION  order by order_index, l";
        internal const string selectSenders = "select oper_id as v,oper_name as l from info_operator where company_id=~COMPANY_ID  and is_sender  and COALESCE(status,'1')='1' order by order_index, oper_name";
         
        internal const string selectGetters = "select oper_id as v,oper_name as l from info_operator where company_id=~COMPANY_ID and COALESCE(status,'1')='1' order by order_index,oper_name";
        internal const string selectDepartments = "select depart_id as v,depart_name as l,mother_id as pv from info_department where company_id=~COMPANY_ID order by order_index,depart_name";
 

        internal const string selectBranch = "select branch_id as v,branch_name as l,py_str as z from info_branch where company_id=~COMPANY_ID  and COALESCE(status,'1')='1' order by order_index,case when branch_type='store' then 0 else 1 end";
        internal const string selectBranchPosition = "select min(branch_id) as v, branch_position_name as l from info_branch_position where company_id=~COMPANY_ID group by branch_position_name union select 0 as v,'默认库位' as l";
        internal const string selectProduceDate = "select distinct produce_date as l from info_item_batch where company_id=~COMPANY_ID order by produce_date";
        internal const string selectBatchNo = "select distinct batch_no as v,batch_no as l from info_item_batch where company_id=~COMPANY_ID and batch_no <> '' and batch_no is not null order by batch_no";
        internal const string selectStore = "select branch_id as v,branch_name as l,py_str as z from info_branch where company_id=~COMPANY_ID  and COALESCE(status,'1')='1' and coalesce(branch_type,'store')='store' order by order_index";

        internal const string selectVan = "select branch_id as v,branch_name as l,py_str as z from info_branch where company_id=~COMPANY_ID  and COALESCE(status,'1')='1' and branch_type='truck' order by order_index";

        internal const string selectItem = "select item_id as v,item_name as l,py_str as z from info_item_prop where company_id = ~COMPANY_ID and (mum_attributes is null or mum_attributes::text not like '%\"distinctStock\": true%') and (status = '1' or status is null) ";
        internal const string itemSearchFields = "['l','z','b','item_no']";
        internal const string selectItemWithBarcode = @"
SELECT item_id as v,item_name as l,py_str as z, barcode as b,item_no
FROM
(
   select info_item_prop.item_id ,item_name ,item_no, py_str, string_agg(mu.barcode,',') barcode 
   from info_item_prop 
   LEFT JOIN info_item_multi_unit mu on info_item_prop.company_id = mu.company_id and info_item_prop.item_id= mu.item_id 
   where info_item_prop.company_id = ~COMPANY_ID and (status = '1' or status is null)  
   GROUP BY info_item_prop.item_id,item_name,item_no,py_str
) t";
        internal const string selectItemWithBarcode1 = @"
SELECT item_id,item_name,py_str, barcode 
FROM
(
   select info_item_prop.item_id ,item_name ,py_str, string_agg(mu.barcode,',') barcode 
   from info_item_prop 
   LEFT JOIN info_item_multi_unit mu on info_item_prop.company_id = mu.company_id and info_item_prop.item_id= mu.item_id 
   where info_item_prop.company_id = ~COMPANY_ID and (status = '1' or status is null)  
   GROUP BY info_item_prop.item_id,item_name,py_str
) t";
        internal const string selectSupcust= "select supcust_id as v,sup_name as l,py_str as z,supcust_no as n,mobile as m,sup_alias as a from info_supcust where company_id= ~COMPANY_ID and supcust_flag in ('C','CS') and (status = '1' or status is null) ";
        internal const string selectSuppliers = "select supcust_id as v,sup_name as l,py_str as z from info_supcust where company_id= ~COMPANY_ID and supcust_flag in ('S','CS') and (status = '1' or status is null)";
        internal const string selectClients = "select supcust_id as v,sup_name as l,py_str as z,supcust_no as n from info_supcust where company_id= ~COMPANY_ID  and (status = '1' or status is null) ";
        internal const string selectFeeSubs = @"select sub_id as v,sub_name as l,py_str as z 
                                     from cw_subject 
                                     where company_id =~COMPANY_ID and sub_type = 'ZC' and sub_code>100  
                                     and sub_id not in (select case when (s.setting->>'feeOutSubForKS')::int is null then -1 else (s.setting->>'feeOutSubForKS')::int end sub_id from  company_setting s where s.company_id = ~COMPANY_ID)";

        internal static T ToTree1<T>(this List<T> nodeList,int rootId=0) where T : ITree<T>
        {
            T root = default;
            var groups = nodeList.GroupBy(x => x.Mother_Id);
            nodeList.ForEach(node =>
            {
                if (node.Mother_Id == rootId) root = node;
                node.SubNodes = groups.FirstOrDefault(x => x.Key == node.Id)?.ToList();
            });
            return root;
            //return nodeList.Find(x => x.Mother_Id == 0);
        }
        private const String host = "https://codequery.market.alicloudapi.com";
        private const String path = "/querybarcode";
        private const String method = "GET";
        private const String appcode = "61479699ae284eeab342d295b042c83a";
        public static bool CheckValidationResult(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors errors)
        {
            return true;
        }
        public static bool IsJson(string input)
        {
            input = input.Trim();
            return input.StartsWith("{") && input.EndsWith("}")
              || input.StartsWith("[") && input.EndsWith("]");
        }
       /* public static DataItem GetDataItem(string key,string sqlFld="",bool forQuery=true)
        {
            if (key == "supcust_id")
            {
                return new DataItem()
                {
                    FldArea = "divHead",
                    Title = "客户",
                    LabelFld = "sup_name",
                    Checkboxes = true,
                    ButtonUsage = "list",
                    CompareOperator = "=",
                    DropDownWidth = "280",
              
                    SqlFld = sqlFld,
                    SqlForOptions = CommonTool.selectSupcust,
                    SearchFields = "['l','z','n']",
                    ForQuery=forQuery
                };
            }
            throw new Exception("unkown data item key");
           
        }*/
        public static DataItem GetDataItem(string key, DataItemChange change)
        {
            if (key == "supcust_id")
            {
                bool QueryByLabelLikeIfIdEmpty = false;
                if (change.Checkboxes)
                {
                    QueryByLabelLikeIfIdEmpty = true;
                }
                 
                return new DataItem()
                {
                   
                    FldArea = "divHead",
                    Title = "客户",
                    LabelFld = "sup_name",
                    
                    ButtonUsage = change.ButtonUsage,
                    CompareOperator = "=",
                    DropDownWidth = "280",
                    
                    SqlForOptions = CommonTool.selectSupcust,
                    SearchFields = "['l','z','n','m','a']",
                    Checkboxes = change.Checkboxes,
					//datafields="",
					

					SqlFld = change.SqlFld,
                    ForQuery = change.ForQuery,
                    QueryByLabelLikeIfIdEmpty= QueryByLabelLikeIfIdEmpty,
                    Width =change.Width,
					SqlAreaToPlace = change.SqlAreaToPlace

				};
            }
            else if (key == "brand_id")
			{
				 

				return new DataItem()
				{

					FldArea = "divHead",  
					
					Title = "品牌",
					Checkboxes = true, 
					LabelFld = "brand_name",
					ButtonUsage = "list",
					CompareOperator = "=",
					 
					SqlForOptions = CommonTool.selectBrands,
					NullEqualValue = "-1",

					SqlFld = change.SqlFld,
					ForQuery = change.ForQuery,
					 
					Width = change.Width,
					SqlAreaToPlace = change.SqlAreaToPlace

				};
			}

			throw new Exception("unkown data item key");

        }

        public static DataItem WithTitle(this DataItem item, string title)
        {
            item.Title = title;
            return item;
        }
        public static dynamic GetItemInfoFromNetwork(string barcode)
        {
            String querys = "code=" + barcode;
            String bodys = "";
            String url = host + path;
            HttpWebRequest httpRequest = null;
            HttpWebResponse httpResponse = null;
            if (0 < querys.Length)
            {
                url = url + "?" + querys;
            }

            if (host.Contains("https://"))
            {
                ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(CheckValidationResult);
                httpRequest = (HttpWebRequest)WebRequest.CreateDefault(new Uri(url));
            }
            else
            {
                httpRequest = (HttpWebRequest)WebRequest.Create(url);
            }
            httpRequest.Method = method;
            httpRequest.Headers.Add("Authorization", "APPCODE " + appcode);
            if (0 < bodys.Length)
            {
                byte[] data = Encoding.UTF8.GetBytes(bodys);
                using (Stream stream = httpRequest.GetRequestStream())
                {
                    stream.Write(data, 0, data.Length);
                }
            }
            try
            {
                httpResponse = (HttpWebResponse)httpRequest.GetResponse();
            }
            catch (WebException ex)
            {
                httpResponse = (HttpWebResponse)ex.Response;
            }

            Stream st = httpResponse.GetResponseStream();
            StreamReader reader = new StreamReader(st, Encoding.GetEncoding("utf-8"));
            return JsonConvert.DeserializeObject(reader.ReadToEnd());

        }

        internal static void UpdateTree<T>(this T tree,Action<T,T> action) where T : ITree<T>
        {
            tree.SubNodes?.ForEach(subTree =>
            {
                action(tree,subTree);
                subTree.UpdateTree(action);
            });
        }
        public static bool IsPropertyExist(dynamic data, string propertyname)
        {
            if (data is ExpandoObject)
                return ((IDictionary<string, object>)data).ContainsKey(propertyname);
            return data.GetType().GetProperty(propertyname) != null;
        }
        public static bool IsBase64(string str)
        {
            string base64CodeRule = "^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{4}|[A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)$";
            return Regex.IsMatch(str, base64CodeRule, RegexOptions.None);
        }
        public static dynamic batchGeo1(String[] addresses)
        {
     
            /**
             * {
                "reqs":[
                     {
                       "method":"get",
                         "url":"/geocoding/v3/?address=重庆市沙坪坝区学城大道62号&ak=p9QBVLhfTeBAUnP43RDzEm1acmYPn8Bu&output=json"
                     },
                     {
                        "method":"get",
                        "url":"/geocoding/v3/?address=江苏省连云港市海州区新电光明小区&output=json&ak=p9QBVLhfTeBAUnP43RDzEm1acmYPn8Bu"
                        }
            ]
           }
             * 
             * 
             * 
             */
            HttpWebRequest webrequest = (HttpWebRequest)HttpWebRequest.Create("http://api.map.baidu.com/batch");
            webrequest.Method = "post";
            List<dynamic> reqs = new List<dynamic>();
            foreach (String address in addresses) {
                string method = "get";
                string url = @$"/geocoding/v3/?address={address}&output=json&ak=Qa6pXVRjlsnQrI8mNyNxHvTck4E3gndl";
                dynamic addressReqItem = new
                {
                    method,
                    url
                };
                reqs.Add(addressReqItem);
            }
            dynamic postData = new
            {
                reqs
            };
            webrequest.ContentType = "application/json";
            byte[] postdatabyte = Encoding.UTF8.GetBytes(Newtonsoft.Json.JsonConvert.SerializeObject(postData)) ;
            webrequest.ContentLength = postdatabyte.Length;
            using (Stream stream = webrequest.GetRequestStream())
            {
                stream.Write(postdatabyte, 0, postdatabyte.Length);
                stream.Close();
            }
               
            using (var httpWebResponse = webrequest.GetResponse())
            using (StreamReader responseStream = new StreamReader(httpWebResponse.GetResponseStream()))
            {
                String ret = responseStream.ReadToEnd();
//                dynamic result =  <T>(ret);
                return ret;
            }
        }
        public class JsonContent : StringContent
        {
            public JsonContent(object obj) :
            base(JsonConvert.SerializeObject(obj), Encoding.UTF8, "application/json")
            { }
        }
        /*
        public static async Task<dynamic> PostJsonData(string url, dynamic data)
        {
            HttpClient client = CPubVars.GetHttpClient();

            //List<dynamic> reqs = new List<dynamic>();
            HttpContent content = new JsonContent(data);
            var response = await client.PostAsync(url, content);
            var s = await response.Content.ReadAsStringAsync();
            dynamic res = JsonConvert.DeserializeObject(s);
            client.Dispose();
            return res;
        }*/
        public static async Task<dynamic> PostJsonByFactory(IHttpClientFactory factory, string url, dynamic data)
        {
            //  HttpClient client = CPubVars.GetHttpClient();
            var httpClient = factory.CreateClient("default");

           // HttpClientHandler clientHandler = new HttpClientHandler();
           // clientHandler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => { return true; };
           // HttpClient client = new HttpClient(clientHandler);
            //if (!g_bSslDifferent)
            {

               
                //  ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };
                //return client;
            }
           // else//在一台centos 7.6(www.yingjiang168.com)上，发现必须这样才可以，否则访问https报错，ssl curl组合之类的，在其他的centos7 和8上，都可以试用上面的方式
            {
             //   ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };
             //   HttpClient client = new HttpClient();
              //  return client;
            }

            //List<dynamic> reqs = new List<dynamic>();
            HttpContent content = new JsonContent(data);
            var response = await httpClient.PostAsync(url, content);
            //var response =  httpClient.SendAsync(url, content);
            response.EnsureSuccessStatusCode();
            var s = await response.Content.ReadAsStringAsync();
            dynamic res = JsonConvert.DeserializeObject(s);
           // client.Dispose();
            return res;
        }
        public static async Task<dynamic> GetJsonByFactory(IHttpClientFactory factory, string url)
        { 
            var client = factory.CreateClient("default"); 
           
            List<dynamic> reqs = new List<dynamic>();
            HttpContent content = new JsonContent(null);
            var response = await client.GetAsync(url);
            var s = await response.Content.ReadAsStringAsync();
            dynamic res = JsonConvert.DeserializeObject(s);
            client.Dispose();
            return res;
        }
        /*
        public static async Task<dynamic> GetJsonData(string url)
        {
            //HttpClientHandler clientHandler = new HttpClientHandler();
            //clientHandler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => { return true; };
            //HttpClient client = new HttpClient(clientHandler);
            //ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };

            HttpClient client = CPubVars.GetHttpClient();

            List<dynamic> reqs = new List<dynamic>();
            HttpContent content = new JsonContent(null);
            var response = await client.GetAsync(url);
            var s = await response.Content.ReadAsStringAsync();
            dynamic res = JsonConvert.DeserializeObject(s);
            client.Dispose();
            return res;
        }*/
        /*
        public static async Task<dynamic> batchGeo(String[] addresses)
        {
            string postUrl = $@"http://api.map.baidu.com/batch";

            //try
            //{
            //为防止因HTTPS证书认证失败造成API调用失败,需要先忽略证书信任问题
            HttpClient client = CPubVars.GetHttpClient();


            List<dynamic> reqs = new List<dynamic>();
            foreach (String address in addresses)
            {
                string method = "get";
                string url = @$"/geocoding/v3/?address={address}&output=json&ak=XUaaox2FCYKKhQrt8EXfS8SW2za8YbWS";//p9QBVLhfTeBAUnP43RDzEm1acmYPn8Bu
                dynamic addressReqItem = new
                {
                    method,
                    url
                };
                reqs.Add(addressReqItem);
            }
            dynamic postData = new
            {
                reqs
            }; 

            HttpContent content = new JsonContent(postData); 

            var response = await client.PostAsync(postUrl, content);
        
            var res = await response.Content.ReadAsStringAsync();

            dynamic data = JsonConvert.DeserializeObject(res);

            return data;

            //}
            //catch (Exception e)
            // {

            //}

        }*/

        public static async Task<dynamic> GetPositionByAddr(IHttpClientFactory httpClientFactory, string address)
        {
            string postUrl = $"http://api.map.baidu.com/geocoding/v3/?address={address}&output=json&ak=XUaaox2FCYKKhQrt8EXfS8SW2za8YbWS";

            //try
            //{
            //为防止因HTTPS证书认证失败造成API调用失败,需要先忽略证书信任问题
            HttpClient client = CPubVars.GetHttpClientFromFactory(httpClientFactory);
             
            var response = await client.GetAsync(postUrl);

            var res = await response.Content.ReadAsStringAsync();

            dynamic data = JsonConvert.DeserializeObject(res);

            return data;

            //}
            //catch (Exception e)
            // {

            //}

        }
        //地球半径，单位米
        private const double EARTH_RADIUS = 6378137;
        /// <summary>
        /// 计算两点位置的距离，返回两点的距离，单位 米
        /// 该公式为GOOGLE提供，误差小于0.2米
        /// </summary>
        /// <param name="lat1">第一点纬度</param>
        /// <param name="lng1">第一点经度</param>
        /// <param name="lat2">第二点纬度</param>
        /// <param name="lng2">第二点经度</param>
        /// <returns></returns>
        public static double GetDistance(double lat1, double lng1, double lat2, double lng2)
        {
            double radLat1 = Rad(lat1);
            double radLng1 = Rad(lng1);
            double radLat2 = Rad(lat2);
            double radLng2 = Rad(lng2);
            double a = radLat1 - radLat2;
            double b = radLng1 - radLng2;
            double result = 2 * Math.Asin(Math.Sqrt(Math.Pow(Math.Sin(a / 2), 2) + Math.Cos(radLat1) * Math.Cos(radLat2) * Math.Pow(Math.Sin(b / 2), 2))) * EARTH_RADIUS;
            return result;
        }

        /// <summary>
        /// 经纬度转化成弧度
        /// </summary>
        /// <param name="d"></param>
        /// <returns></returns>
        private static double Rad(double d)
        {
            return (double)d * Math.PI / 180d;
        }
        internal static List<Tree> ToTree<T>(this List<T> arr) where T:Tree
        {
            //IEnumerable<IGrouping<string, Node>> group = arr.GroupBy(x => x.Mother_Id);
            //var n = new Node { id = 0 };
            //n.ToTree(group);
            //return n.subNodes;

            Dictionary<int, List<Tree>> groups = new Dictionary<int, List<Tree>>();
            foreach (T item in arr)
            {
                //var i = Newtonsoft.Json.JsonConvert.SerializeObject(item);
                // Debug.WriteLine(i);
                if (!groups.ContainsKey(item.Mother_Id))
                {
                    groups.Add(item.Mother_Id, new List<Tree>());
                }

                if (groups.ContainsKey(item.Id))
                {
                    item.SubNodes = groups[item.Id];
                }
                else
                {
                    item.SubNodes = new List<Tree>();
                    groups.Add(item.Id, item.SubNodes);
                }
                List<Tree> jarr = groups[item.Mother_Id];
                jarr.Add(item);
            }
            return groups[0];
        }

        internal static List<TreeGetItemClass> ToTreeGetItemClass<T>(this List<T> arr) where T : TreeGetItemClass
        {
            //IEnumerable<IGrouping<string, Node>> group = arr.GroupBy(x => x.Mother_Id);
            //var n = new Node { id = 0 };
            //n.ToTree(group);
            //return n.subNodes;

            Dictionary<int, List<TreeGetItemClass>> groups = new Dictionary<int, List<TreeGetItemClass>>();
            foreach (T item in arr)
            {
                //var i = Newtonsoft.Json.JsonConvert.SerializeObject(item);
                // Debug.WriteLine(i);
                if (!groups.ContainsKey(item.Mother_Id))
                {
                    groups.Add(item.Mother_Id, new List<TreeGetItemClass>());
                }

                if (groups.ContainsKey(item.Id))
                {
                    item.SubNodes = groups[item.Id];
                }
                else
                {
                    item.SubNodes = new List<TreeGetItemClass>();
                    groups.Add(item.Id, item.SubNodes);
                }
                List<TreeGetItemClass> jarr = groups[item.Mother_Id];
                jarr.Add(item);
            }
            return groups[0];
        }

        internal static List<MallClassTree> ToMallClassTree<T>(this List<T> arr) where T:MallClassTree
        {
            Dictionary<int, List<MallClassTree>> groups = new Dictionary<int, List<MallClassTree>>();
            foreach (T item in arr)
            {
                if (!groups.ContainsKey(item.Mother_Id))
                {
                    groups.Add(item.Mother_Id, new List<MallClassTree>());
                }

                if (groups.ContainsKey(item.Id))
                {
                    item.SubNodes = groups[item.Id];
                }
                else
                {
                    item.SubNodes = new List<MallClassTree>();
                    groups.Add(item.Id, item.SubNodes);
                }
                List<MallClassTree> jarr = groups[item.Mother_Id];
                jarr.Add(item);
            }
            return groups[0];
        }
        internal static List<CompanyDepartmentTree> ToCompanyDepartmentTree<T>(this List<T> arr) where T:CompanyDepartmentTree
        {
            Dictionary<int, List<CompanyDepartmentTree>> groups = new Dictionary<int, List<CompanyDepartmentTree>>();
            foreach (T item in arr)
            {
                if (!groups.ContainsKey(item.Mother_Id))
                {
                    groups.Add(item.Mother_Id, new List<CompanyDepartmentTree>());
                }

                if (groups.ContainsKey(item.Id))
                {
                    item.SubNodes = groups[item.Id];
                }
                else
                {
                    item.SubNodes = new List<CompanyDepartmentTree>();
                    groups.Add(item.Id, item.SubNodes);
                }
                List<CompanyDepartmentTree> jarr = groups[item.Mother_Id];
                jarr.Add(item);
            }
            return groups[0];
        }
        public static List<Dictionary<string, object>> MallClassTreeToList(MallClassTree node)
        {
            List<Dictionary<string, object>> list = new List<Dictionary<string, object>>();
            if (node == null) return list;

            Dictionary<string, object> dict = new Dictionary<string, object>();
            dict.Add("id", node.Id);
            dict.Add("name", node.Name);
            dict.Add("mother_id", node.Mother_Id);
            dict.Add("class_path", node.Class_path);
            dict.Add("class_path_name", node.Class_path_name);
            dict.Add("company_Id", node.company_Id);
            dict.Add("class_image", node.Class_Image);
            dict.Add("brand_id", string.IsNullOrEmpty(node.Brand_Id) ? "" : node.Brand_Id);
            list.Add(dict);

            foreach (var subNode in node.SubNodes)
            {
                list.AddRange(MallClassTreeToList(subNode));
            }

            return list;
        }
        public static string GetTimeStamp()
        {
            TimeSpan ts = DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0);
            return Convert.ToInt64(ts.TotalMilliseconds).ToString();
        }


        /// <summary>
        /// Image 转成 base64
        /// </summary>
        /// <param name="fileFullName"></param>
        public static string ImageToBase64(this IFormFile file, string fileFullName,Boolean needPrefix = true)
        {
            try
            {
                Bitmap bmp = new Bitmap(fileFullName);
                MemoryStream ms = new MemoryStream();
                bmp.Save(ms, ImageFormat.Jpeg);
                byte[] arr = new byte[ms.Length]; ms.Position = 0;
                ms.Read(arr, 0, (int)ms.Length); ms.Close();
                if(needPrefix)
                {
                    return "data:image/jpeg;charset=utf-8;base64," + Convert.ToBase64String(arr);
                }
                else
                {
                    return Convert.ToBase64String(arr);
                }
                
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
                return null;
            }
        }

        /// <summary>
        /// pdf 转成 base64
        /// </summary>
        /// <param name="fileFullName"></param>
        public static string PdfToBase64(this IFormFile file, string fileFullName)
        {
            try
            {
                FileStream filestream = new FileStream(fileFullName, FileMode.Open);

                byte[] bt = new byte[filestream.Length];
                //调用read读取方法
                filestream.Read(bt, 0, bt.Length);
                string base64Str = "data:application/pdf;base64," + Convert.ToBase64String(bt);
                filestream.Close();
                return base64Str;
            }

            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
                return null;
            }
        }
        public static async Task<string> UploadAsync(this IFormFile file, string uploadDir, string filename = null)
        {

            if (!Directory.Exists(uploadDir))
            {
                Directory.CreateDirectory(uploadDir);
            };

            filename ??= file.FileName;
            var filePath = Path.Combine(uploadDir, filename);
            if (File.Exists(filePath)) File.Delete(filePath);
            
            //throw new Exception("Err:文件已存在！");
            using (var fileStream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(fileStream);
                fileStream.Dispose();
            }
            if (file.Length > 1024 * 1024 * 80)
            {
                File.Delete(filePath);
                return "文件不能超过80M";
            }
            return "";
               
        }

        /// <summary>
        /// 验证无效字符串(null,""或"  ") modified by jh
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static bool IsInvalid(this string str)
        {
            return string.IsNullOrWhiteSpace(str);
        }        
        /// <summary>
        /// 验证非 null,""或"  "
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static bool IsValid(this string str)
        {
            return !str.IsInvalid();
        }
 




        //检查是否数字
        public static bool IsNumeric(this string str)
        { 
            if (double.TryParse(str, out double numDouble)) {  
                return true;
            }
            else
            {
                return false;
            }
             
        }
        //检查是否为日期
        public static bool IsDate(this string str)
        {

            //yyyy-MM-dd格式
            Regex regex1 = new Regex(@"^(\d{2,4})-?\d{1,2}-?\d{1,2}$");
            //yyyy/MM/dd格式
            Regex regex2 = new Regex(@"^\d{2,4}/?\d{1,2}/?\d{1,2}$");
            //yyyy.MM.dd 格式
            Regex regex3 = new Regex(@"^\d{2,4}[.]?\d{1,2}[.]?\d{1,2}$");
            //20210903
            Regex regex4 = new Regex(@"^(20\d{2}(0?[1-9]|1[0-2])((0?[1-9])|((1|2)[0-9])|30|31))$");

            Regex regex5 = new Regex(@"^(\d{4})-(0\d{1}|1[0-2])-(0\d{1}|[12]\d{1}|3[01]) (0\d{1}|1\d{1}|2[0-3]):[0-5]\d{1}:([0-5]\d{1})$");

            if (regex1.IsMatch(str))
                return true;
            else if (regex2.IsMatch(str))
                return true;
            else if (regex3.IsMatch(str))
                return true;
            else if (regex4.IsMatch(str))
                return true;
            else if (regex5.IsMatch(str))
                return true;
            return false;
        }

        //日期纠正
        public static string CorrectDate(this string happenTime)
        {
            if (happenTime.IsValid())
            {
                if ( happenTime.IsNumeric()&&( happenTime.Substring(0, 1)=="4" || happenTime.Substring(0, 1) == "5"))
                {
                    DateTime Date = DateTime.FromOADate(double.Parse(happenTime));
                    happenTime = Date.ToString();
                }

                

                if (happenTime.Contains(".")) happenTime = happenTime.Replace(".", "-");
                if (happenTime.Contains("/")) happenTime = happenTime.Replace("/", "-");

                if (happenTime.Length == 8 && CPubVars.IsNumeric(happenTime))
                {
                    happenTime = happenTime.Substring(0, 4) + "-" + happenTime.Substring(4, 2) + "-" +
                                 happenTime.Substring(6, 2);

                }

                if (happenTime.Contains("月")) happenTime = happenTime.Replace("月", "-");

                
                if (happenTime.Contains("年")) happenTime = happenTime.Replace("年", "-");

                
                if (happenTime.Contains("日") )  happenTime = happenTime.Replace("日", " ");

                if (happenTime.Contains("号")) happenTime = happenTime.Replace("号", " ");


                if (!happenTime.Contains(":")) happenTime += " 00:00";
            }


            return happenTime;
        }

        //判断单据类型
        public static string GetSheetType(this string sheetType)
        {
            if (sheetType.IsValid())
            {
                switch (sheetType)
                {
                    case "销售":
                    case "销售单":
                        sheetType = "X";
                        break;
                    case "退货":
                    case "退货单":
                        sheetType = "T";
                        break;
                    case "销售订单":
                        sheetType = "XD";
                        break;
                    case "退货订单":
                        sheetType = "TD";
                        break;
                    case "采购单":
                        sheetType = "CG";
                        break;
                    case "采购":
                        sheetType = "CG";
                        break;
                    case "采购退货单":
                        sheetType = "CT";
                        break;
                    case "预收款单":
                        sheetType = "YS";
                        break;
                    case "定货单":
                    case "订货单":
                        sheetType = "DH";
                        break;
                    case "费用支出单":
                        sheetType = "ZC";
                        break;
                    case "其他收入单":
                        sheetType = "SR";
                        break;
                    default:
                        sheetType = "";
                        break;
                }
            }

            return sheetType;
        }


        //判断inoutflag
        public static int GetFlag(this string sheetType)
        {
            int flag = 0;
            if (sheetType.IsValid())
            {
                switch (sheetType)
                {
                    case "X":
                        flag = 1;
                        break;
                    case "T":
                        flag = -1;
                        break;
                    case "XD":
                        flag = 1;
                        break;
                    case "TD":
                        flag = -1;
                        break;
                    case "CG":
                        flag = -1;
                        break;
                    case "CT":
                        flag = 1;
                        break;
                    case "YS":
                        flag =1;
                        break;
                    case "DH":
                        flag = 1;
                        break;
                }
            }
            return flag;

        }

        //字符提取数字
        public static string GetNumber(this string str)
        {

            string result = System.Text.RegularExpressions.Regex.Replace(str, @"[^0-9]+", "");

            return result;
        }




        //字符串提取左边数字
        public static string GetNumLeft(this string str)
        {
            var nn = str.IndexOf(" ");
            if (nn != -1)
            {
                str = str.Substring(0, nn);
                return str;

            }
            else
            {
                return str;
            }
        }
        //字符串提取右边数字
        public static string GetNumRight(this string str)
        {
            var nn = str.IndexOf("已兑");
            if(nn != -1)
            {
                str = str.Substring(nn);
                str = Regex.Replace(str, @"[^\d.\d]", "");
                return str;

            }
            else
            {
                return null;
            }
        }


        public static string GetStartTime(this string str)
        {

            var time = Convert.ToDateTime(str);

           


             var time2=time.Year.ToString() +"-01-01 00:00:00";




            return time2;
        }


        public static string GetEndTime(this string str)
        {

            var time = Convert.ToDateTime(str);


           var time2 = time.Year.ToString() + "-12-31  23:59:59";



            return time2;
        }

        /// <summary>
        /// 获得32位的MD5加密
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public static string GetMD5_32(string input)
        {
            System.Security.Cryptography.MD5 md5 = System.Security.Cryptography.MD5.Create();
            byte[] data = md5.ComputeHash(System.Text.Encoding.Default.GetBytes(input));
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < data.Length; i++)
            {
                sb.Append(data[i].ToString("x2"));
            }
            return sb.ToString();
        }


        public static string Md5(string key)
        {
            //MD5类是抽象类
            MD5 md5 = MD5.Create();
            //需要将字符串转成字节数组
            byte[] buffer = Encoding.Default.GetBytes(key);
            //加密后是一个字节类型的数组，这里要注意编码UTF8/Unicode等的选择
            byte[] md5buffer = md5.ComputeHash(buffer);
            string str = null;
            // 通过使用循环，将字节类型的数组转换为字符串，此字符串是常规字符格式化所得
            foreach (byte b in md5buffer)
            {
                //得到的字符串使用十六进制类型格式。格式后的字符是小写的字母，如果使用大写（X）则格式后的字符是大写字符 
                //但是在和对方测试过程中，发现我这边的MD5加密编码，经常出现少一位或几位的问题；
                //后来分析发现是 字符串格式符的问题， X 表示大写， x 表示小写， 
                //X2和x2表示不省略首位为0的十六进制数字；
                str += b.ToString("x2");
            }
            return str;
        }
        public static string sha256(string key)
        {
            byte[] bytes = Encoding.UTF8.GetBytes(key);
            byte[] hash = SHA256Managed.Create().ComputeHash(bytes);

            StringBuilder builder = new StringBuilder();
            for (int i = 0; i < hash.Length; i++)
            {
                builder.Append(hash[i].ToString("X2"));
            }

            return builder.ToString();

        }

        public static string ToCampLower(this string str)
        {
            if (str.IsInvalid()) return str;
            if (str.Length == 1) return str.ToLower();
            return str.Substring(0,1).ToLower() + str.Substring(1,str.Length-1); 
        }
        public static string ToggleCamp(this string str)
        {
            if (str.IsInvalid()) return str;
             
            if (str.Length == 1) return str.ToggleCase();
            return str.Substring(0,1).ToggleCase() + str[1..];
        }
        public static string ToggleCase(this string str)
        {
            if (str.IsInvalid()) return str;
            if (str.IsUpperCase()) return str.ToLower();
            else return str.ToUpper();  
        }
        public static bool IsUpperCase(this string str)
        {
            if (str.IsInvalid()) return false;
            if (Regex.IsMatch(str, "[A-Z]")) return true;
            return false; 
        }
 
        public static string DefaultIfEmpty(this string str,string defaultValue)
        {
            return str.IsInvalid()? defaultValue : str;
        }



        public static string GetMd5_NOT_USED(this IFormFile file)
        {
            MD5 md5 = MD5.Create();
            using (var str = file.OpenReadStream())
            {
                byte[] bs = md5.ComputeHash(str);
                str.Close();

                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < bs.Length; i++)
                {
                    sb.Append(bs[i].ToString("x"));
                }
                return sb.ToString();
            }
            
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="thisDate"></param>
        /// <param name="day"></param>
        /// <returns></returns>
        public static DateTime GetNextWeekday(this DateTime thisDate, DayOfWeek day)
        {
            var x = day - thisDate.DayOfWeek;
            if (x <= 0) x += 7;
            return thisDate.AddDays(x);
        }
        public static DateTime GetMonthStart(this DateTime thisDate, int? month = null)
        {
            return new DateTime(thisDate.Year, month ?? thisDate.Month, 1);
        } 

        public static string ToJson(this ExpandoObject  keyValuePairs)
        {
            var kvs = keyValuePairs.Select(keyValuePair => $"\"{keyValuePair.Key}\":\"{keyValuePair.Value}\"");
            return $"{{{string.Join(',', kvs)}}}";
        }

        public static IApplicationBuilder UseToken(this IApplicationBuilder application)
        {
            return application.UseMiddleware<TokenChecker>();
        }

        #region 数据库操作
        public static async Task<string> UpdateArrearsStrategy(this CMySbCommand command, string company_id, bool bAutoCommit = true)
        {
            string exSQL = "";//负责执行插入的sql
            if(company_id == "") return "Error,方法传参缺少company_id";//虽然几率小，但是防一手

            CMySbTransaction tran = null;
            if (bAutoCommit)//bAutoCommit表示是否开启事务，调用此方法的地方已经开启了事务的时候，就置false，否则置true，保证我这个方法的操作正确
                tran = await command.Connection.BeginTransactionAsync();
            try
            {
                
                string sql = $"select * from arrears_strategy_client where company_id = {company_id}";
                var record = await CDbDealer.GetRecordsFromSQLAsync(sql, command);
                if (record.Count == 0)//欠款策略的客户表里面没有数据，就插入数据
                {
                    exSQL =@$"
INSERT INTO arrears_strategy_client (               company_id,                supcust_id,                 max_arrears,        max_arrears_days)
                             SELECT {company_id} AS company_id,i.supcust_id as supcust_id,i.max_arrears as max_arrears,NULL AS max_arrears_days FROM info_supcust i where company_id = {company_id} and i.max_arrears>0";
                    command.CommandText = exSQL;
                    await command.ExecuteNonQueryAsync();
                }
                sql = $"select * from arrears_strategy_operator where company_id = {company_id}";
                record = await CDbDealer.GetRecordsFromSQLAsync(sql, command);
                if (record.Count == 0)//欠款策略的员工表里面没有数据，就插入数据
                {
                    exSQL = @$"
INSERT INTO arrears_strategy_operator (company_id,   oper_id,                         max_arrears,        max_arrears_days)
               SELECT  {company_id} AS company_id,io.oper_id,io.seller_max_arrears AS max_arrears,NULL AS max_arrears_days FROM  info_operator io  WHERE io.company_id = {company_id} and io.seller_max_arrears>0;";
                    command.CommandText = exSQL;
                    await command.ExecuteNonQueryAsync();
                }

                if (bAutoCommit && tran != null) tran.Commit();
                return "";
            }
            catch (Exception ex)
            {
                MyLogger.LogMsg("Update Version Db Error, 迁移欠款额度失败:" + ex.Message + ex.StackTrace, company_id);//打印日志
                if (bAutoCommit && tran != null) tran.Rollback();//事务回滚
                return "Error,迁移欠款时失败";
            }
            
        }

        public static async Task<string> UpdateArrearsStrategyForBalance(this CMySbCommand command, string company_id, bool bAutoCommit = true)
        {
            if(company_id == "") return "Error,方法传参缺少company_id";//虽然几率小，但是防一手

            CMySbTransaction tran = null;
            if (bAutoCommit)//bAutoCommit表示是否开启事务，调用此方法的地方已经开启了事务的时候，就置false，否则置true，保证我这个方法的操作正确
                tran = await command.Connection.BeginTransactionAsync();
            try
            {
                string fixAuxiliaryBalance = @$"
                update arrears_balance_auxiliary abau set auxiliary_balance = arrearsOfOper.left_amount from(select left_amount, oper_id, oper_name, company_id
                    FROM (SELECT round((SUM(COALESCE(sm.total_amount, 0) - COALESCE(sm.disc_amount, 0) -
                                            COALESCE(sm.paid_amount, 0)))::numeric, 2) left_amount,

                                 oper_id,
                                 oper_name,
                                 b.company_id
                          FROM arrears_balance b
                                   LEFT JOIN
                               (
                                   (SELECT supcust_id,
                                           seller_id,
                                           money_inout_flag,
                                           total_amount * money_inout_flag    total_amount,
                                           disc_amount * money_inout_flag     disc_amount,
                                           paid_amount * money_inout_flag     paid_amount,
                                           now_pay_amount * money_inout_flag  now_pay_amount,
                                           now_disc_amount * money_inout_flag now_disc_amount,
                                           arrears_order_sheet_id
                                    FROM sheet_sale_main
                                    WHERE company_id = {company_id}
                                      and red_flag is null
                                      and approve_time is not null

                                    UNION ALL

                                    SELECT supcust_id,
                                           getter_id                          seller_id,

                                           money_inout_flag,
                                           total_amount * money_inout_flag    total_amount,
                                           disc_amount * money_inout_flag     disc_amount,
                                           paid_amount * money_inout_flag     paid_amount,
                                           now_pay_amount * money_inout_flag  now_pay_amount,
                                           now_disc_amount * money_inout_flag now_disc_amount,
                                           arrears_order_sheet_id
                                    FROM sheet_prepay
                                    WHERE company_id = {company_id}
                                      and red_flag is null
                                      and sheet_type = 'YS'
                                      and prepay_sub_id <> '-1'
                                      and approve_time is not null

                                    UNION ALL

                                    SELECT supcust_id,
                                           getter_id                          seller_id,
                                           money_inout_flag,
                                           total_amount * money_inout_flag    total_amount,
                                           disc_amount * money_inout_flag     disc_amount,
                                           paid_amount * money_inout_flag     paid_amount,
                                           now_pay_amount * money_inout_flag  now_pay_amount,
                                           now_disc_amount * money_inout_flag now_disc_amount,
                                           arrears_order_sheet_id
                                    FROM sheet_fee_out_main
                                    WHERE company_id = {company_id}
                                      and sheet_type = 'ZC'
                                      and red_flag is null
                                      and approve_time is not null) t
                                       LEFT JOIN info_operator io on io.oper_id = t.seller_id
                                   ) sm ON b.supcust_id = sm.supcust_id and b.company_id = {company_id}
                                   LEFT JOIN (select sheet_id, approve_time, red_flag, company_id
                                              FROM sheet_get_arrears_order_main
                                              WHERE company_id = {company_id}
                                                and red_flag is null) aom on sm.arrears_order_sheet_id = aom.sheet_id
                          WHERE b.company_id = {company_id}
                            and abs(b.balance) >= 0.01
                          GROUP BY oper_id, b.company_id)) arrearsOfOper where abau.company_id = arrearsOfOper.company_id and arrearsOfOper.oper_id = abau.auxiliary_id and abau.auxiliary_type = 'seller' and abau.company_id = {company_id};";


                string fixSellerMaxArrearsBalance = $@"
                    update info_supcust s set max_arrears =c.max_arrears from arrears_strategy_client c where s.company_id={company_id} and c.company_id={company_id} and s.supcust_id=c.supcust_id and c.max_arrears<>coalesce(s.max_arrears, 0);
                    update info_supcust s set max_arrears =null where s.company_id={company_id} and s.max_arrears is not null and not s.supcust_id in (select supcust_id from  arrears_strategy_client where company_id={company_id});

                    update info_operator s set seller_max_arrears =c.max_arrears from arrears_strategy_operator c where s.company_id={company_id} and c.company_id={company_id} and s.oper_id=c.oper_id and c.max_arrears<>coalesce(s.seller_max_arrears, 0);
                    update info_operator s set seller_max_arrears =null where s.company_id={company_id} and s.seller_max_arrears is not null and not s.oper_id in (select oper_id from  arrears_strategy_operator where company_id={company_id});";
                command.CommandText = fixAuxiliaryBalance + fixSellerMaxArrearsBalance;
                await command.ExecuteNonQueryAsync();
                
                if (bAutoCommit && tran != null) tran.Commit();
                return "";
            }
            catch (Exception ex)
            {
                MyLogger.LogMsg("Update Version Db Error, 更新欠款策略失败:" + ex.Message + ex.StackTrace, company_id);//打印日志
                if (bAutoCommit && tran != null) tran.Rollback();//事务回滚
                return "Error,迁移欠款时失败";
            }
            
        }
        public static async Task<string> UpdateNewField_20241209(this CMySbCommand command, string company_id, bool bAutoCommit = true)
        {
            if (company_id == "") return "Error,方法传参缺少company_id";

            CMySbTransaction tran = null;
            if (bAutoCommit)
                tran = await command.Connection.BeginTransactionAsync();
            try
            {
                string sql = @$"
WITH at AS (SELECT sheet_id, sum(unit_factor * quantity * cost_price_prop) AS cost_amount_prop, sum((unit_factor * quantity) * buy_price) AS buy_amount FROM sheet_move_detail WHERE company_id = {company_id} GROUP BY sheet_id) 
UPDATE sheet_move_main sm SET buy_amount = at.buy_amount, cost_amount_prop = at.cost_amount_prop  from at WHERE sm.company_id = {company_id} AND sm.sheet_id = at.sheet_id;";
                command.CommandText = sql;
                await command.ExecuteNonQueryAsync();

                sql = @$"
with t  as (select sm.sheet_id,sm.company_id,coalesce(som.happen_time,sm.happen_time) as time from sheet_sale_main sm left join sheet_sale_order_main som on som.company_id={company_id} and  sm.order_sheet_id=som.sheet_id
where sm.company_id={company_id} and sm.approve_time is not null ) 
update sheet_sale_main m set order_sheet_time=t.time from t where m.company_id={company_id} and m.sheet_id=t.sheet_id;
";

                command.CommandText = sql;
                await command.ExecuteNonQueryAsync();

                string startDate= CPubVars.GetDateText(DateTime.Now.AddMonths(-2));

				sql = @$"
WITH cp AS (SELECT company_id, sheet_id, item_id, sum(quantity * real_price) / sum(unit_factor * quantity) AS avg_price, CASE WHEN quantity > 0 THEN 'Positive' WHEN quantity < 0 THEN 'Negative' ELSE 'Zero' END AS quantity_sign FROM sheet_sale_detail WHERE company_id ={company_id} AND quantity <> 0 And happen_time>'{startDate}' GROUP BY company_id, sheet_id, item_id, quantity_sign ORDER BY sheet_id) 
UPDATE sheet_sale_detail sd SET item_avg_price = cp.avg_price FROM cp WHERE sd.company_id = {company_id} AND sd.sheet_id = cp.sheet_id AND sd.item_id = cp.item_id And sd.happen_time>'{startDate}'
";

                command.CommandText = sql;
                await command.ExecuteNonQueryAsync();

                if (bAutoCommit && tran != null) tran.Commit();
                return "";
            }
            catch (Exception ex)
            {
                MyLogger.LogMsg("Update Version Db Error, 刷新数据失败:" + ex.Message + ex.StackTrace, company_id);//打印日志
                if (bAutoCommit && tran != null) tran.Rollback();//事务回滚
                return "Error,刷新数据失败";
            }
        }

        public static async Task<string> UpdateOpendingStock_NowCostPrice(this CMySbCommand command, string company_id, bool bAutoCommit = true)
        {
            if (company_id == "") return "Error,方法传参缺少company_id";

            CMySbTransaction tran = null;
            if (bAutoCommit)
                tran = await command.Connection.BeginTransactionAsync();
            string sql = "";
            try
            {
                sql = @$"update sheet_stock_opening_detail set now_cost_price = cost_price_avg where now_cost_price is null and company_id = {company_id};";
                command.CommandText = sql;
                await command.ExecuteNonQueryAsync();
                if (bAutoCommit && tran != null) tran.Commit();
                return "";
            }
            catch(Exception ex)
            {
                MyLogger.LogMsg( "Update Version Db Error, 刷新数据失败:" + ex.Message + ex.StackTrace, company_id);//打印日志
                if (bAutoCommit && tran != null) tran.Rollback();//事务回滚
                return "Error,刷新数据失败";
            }
        }
        public static async Task<string> UpdateInOutSheetDetailCostPriceAvgForSUnit(this CMySbCommand command, string company_id, bool bAutoCommit = true)
        {
            if (company_id == "") return "Error,方法传参缺少company_id";

            CMySbTransaction tran = null;
            if (bAutoCommit)
                tran = await command.Connection.BeginTransactionAsync();
            string sql = "";
            try
            {
                sql = @$"update sheet_stock_in_out_detail ssiod set cost_price_avg = iip.cost_price_avg from info_item_prop iip where ssiod.item_id = iip.item_id and ssiod.company_id = {company_id};";
                command.CommandText = sql;
                await command.ExecuteNonQueryAsync();
                if (bAutoCommit && tran != null) tran.Commit();
                return "";
            }
            catch(Exception ex)
            {
                MyLogger.LogMsg( "Update Version Db Error, 刷新数据失败:" + ex.Message + ex.StackTrace, company_id);//打印日志
                if (bAutoCommit && tran != null) tran.Rollback();//事务回滚
                return "Error,刷新数据失败";
            }
        }  
		public static async Task<string> UpdateDepartmentID_20250105(this CMySbCommand command, string company_id, bool bAutoCommit = true)
		{
			if (company_id == "") return "Error,方法传参缺少company_id";

			CMySbTransaction tran = null;
			if (bAutoCommit)
				tran = await command.Connection.BeginTransactionAsync();
			try
			{
                string sql = @$"
update sheet_sale_main sm set department_id=op.depart_id 
from info_operator op 
where sm.company_id ={company_id} and op.company_id ={company_id} 
and sm.seller_id=op.oper_id 
and sm.department_id is null and op.depart_id is not null;";
                 
                command.CommandText = sql;
				await command.ExecuteNonQueryAsync();

				sql = @$"
update sheet_sale_order_main sm set department_id=op.depart_id 
from info_operator op 
where sm.company_id ={company_id} and op.company_id ={company_id} 
and sm.seller_id=op.oper_id 
and sm.department_id is null and op.depart_id is not null;";

				command.CommandText = sql;
				await command.ExecuteNonQueryAsync();

				sql = @$"
update sheet_buy_main sm set department_id=op.depart_id 
from info_operator op 
where sm.company_id ={company_id} and op.company_id ={company_id} 
and sm.seller_id=op.oper_id 
and sm.department_id is null and op.depart_id is not null;";

				command.CommandText = sql;
				await command.ExecuteNonQueryAsync();

				sql = @$"
update sheet_buy_order_main sm set department_id=op.depart_id 
from info_operator op 
where sm.company_id ={company_id} and op.company_id ={company_id} 
and sm.seller_id=op.oper_id 
and sm.department_id is null and op.depart_id is not null;";

				command.CommandText = sql;
				await command.ExecuteNonQueryAsync();

				sql = @$"
update sheet_price_rebate_main sm set department_id=op.depart_id 
from info_operator op 
where sm.company_id ={company_id} and op.company_id ={company_id}
and sm.seller_id=op.oper_id 
and sm.department_id is null and op.depart_id is not null;";

                command.CommandText = sql;
				await command.ExecuteNonQueryAsync();
                 
				if (bAutoCommit && tran != null) tran.Commit();
				return "";
			}
			catch (Exception ex)
			{
				MyLogger.LogMsg( "Update Version Db Error, 刷新数据department_id失败:" + ex.Message + ex.StackTrace, company_id);//打印日志
				if (bAutoCommit && tran != null) tran.Rollback();//事务回滚
				return "Error,刷新数据失败";
			}
		}

		public static async Task<string> UpdateArrearsDb(this CMySbCommand command, string company_id, bool bAutoCommit = true)
		{
			 
			if (company_id == "") return "Error,方法传参缺少company_id";//虽然几率小，但是防一手

			CMySbTransaction tran = null;
			if (bAutoCommit)//bAutoCommit表示是否开启事务，调用此方法的地方已经开启了事务的时候，就置false，否则置true，保证我这个方法的操作正确
				tran = await command.Connection.BeginTransactionAsync();
			try
			{

				string sql = @$"
UPDATE arrears_balance ab set first_arrears_time=sm.happen_time,first_arrears_sheet_id=sm.sheet_id,first_arrears_sheet_no=sm.sheet_no,first_arrears_sheet_type='X'
FROM
(
    SELECT sm.supcust_id,sm.sheet_id,sm.happen_time,sm.sheet_no from sheet_sale_main sm 
    INNER JOIN
    (
        SELECT supcust_id, min(happen_time) min_happen_time from sheet_sale_main where company_id={company_id} and happen_time>'{CPubVars.GetDateText(DateTime.Now.AddDays(-365))}' and sheet_type='X' and total_amount-paid_amount-disc_amount>0.02
        group by supcust_id
    ) mm on sm.supcust_id=mm.supcust_id and sm.happen_time=mm.min_happen_time 
    WHERE sm.company_id={company_id} and happen_time>'{CPubVars.GetDateText(DateTime.Now.AddDays(-365))}' and sheet_type='X' and total_amount-paid_amount-disc_amount>0.02

) sm
WHERE ab.company_id={company_id} and ab.supcust_id=sm.supcust_id;
";
                command.CommandText = sql;
                await command.ExecuteNonQueryAsync();

				sql = @$"
UPDATE sheet_sale_main sm set settle_time= happen_time
WHERE company_id={company_id} and settle_time is null and sm.happen_time>'{CPubVars.GetDateText(DateTime.Now.AddDays(-720))}' and total_amount-paid_amount-disc_amount<=0.02;
";

				command.CommandText = sql;
				await command.ExecuteNonQueryAsync();

				sql = @$"
UPDATE sheet_prepay sm set settle_time= happen_time
WHERE company_id={company_id} and settle_time is null and sm.happen_time>'{CPubVars.GetDateText(DateTime.Now.AddDays(-720))}' and total_amount-paid_amount-disc_amount<=0.02;
";

				command.CommandText = sql;
				await command.ExecuteNonQueryAsync();

				sql = @$"
UPDATE client_account_history ch set sheet_no=sm.sheet_no,settle_time=sm.settle_time
FROM sheet_sale_main sm 
WHERE ch.company_id={company_id} and sm.company_id={company_id} and ch.sheet_type in ('X','T') and ch.sheet_no is null and ch.sheet_id=sm.sheet_id and sm.happen_time>'{CPubVars.GetDateText(DateTime.Now.AddDays(-720))}' ;
";

				command.CommandText = sql;
				await command.ExecuteNonQueryAsync();

				sql = @$"
UPDATE client_account_history ch set sheet_no=sm.sheet_no,settle_time=sm.settle_time
FROM sheet_prepay sm 
WHERE ch.company_id={company_id} and sm.company_id={company_id} and ch.sheet_type in ('YS','DH') and ch.sheet_no is null and ch.sheet_id=sm.sheet_id and sm.happen_time>'{CPubVars.GetDateText(DateTime.Now.AddDays(-720))}' ;
";

				command.CommandText = sql;
				await command.ExecuteNonQueryAsync();
				if (bAutoCommit && tran != null) tran.Commit();
				return "";
			}
			catch (Exception ex)
			{
				MyLogger.LogMsg("Update Version Db Error, 迁移欠款额度失败:" + ex.Message + ex.StackTrace, company_id);//打印日志
				if (bAutoCommit && tran != null) tran.Rollback();//事务回滚
				return "更新欠款数据时失败";
			}

		}
        public static async Task<string> UpdateArrearsDb_241023(this CMySbCommand command, string company_id, bool bAutoCommit = true)
        {

            if (company_id == "") return "Error,方法传参缺少company_id";//虽然几率小，但是防一手

            CMySbTransaction tran = null;
            if (bAutoCommit)//bAutoCommit表示是否开启事务，调用此方法的地方已经开启了事务的时候，就置false，否则置true，保证我这个方法的操作正确
                tran = await command.Connection.BeginTransactionAsync();
            try
            {

   

                string sql = @$"
UPDATE sheet_sale_main sm set acct_supcust_id= coalesce(cah.supcust_id,sm.supcust_id) FROM
client_account_history cah  
where sm.company_id={company_id} and cah.company_id={company_id} and sm.sheet_id=cah.sheet_id and cah.sheet_type in ('X','T') ;
";

                command.CommandText = sql;
                await command.ExecuteNonQueryAsync();
                

                sql = @$"
UPDATE sheet_fee_out_main sm set acct_supcust_id= coalesce(cah.supcust_id,sm.supcust_id) FROM
client_account_history cah  
WHERE sm.company_id={company_id} and cah.company_id={company_id} and sm.sheet_id=cah.sheet_id and cah.sheet_type in ('ZC','SR') ;
";

                command.CommandText = sql;
                await command.ExecuteNonQueryAsync();

                sql = @$"
UPDATE sheet_buy_main sm set acct_supcust_id= coalesce(cah.supcust_id,sm.supcust_id) FROM
client_account_history cah   
WHERE sm.company_id={company_id} and cah.company_id={company_id} and sm.sheet_id=cah.sheet_id and cah.sheet_type in ('CG','CT');
";

                command.CommandText = sql;
                await command.ExecuteNonQueryAsync();

                if (bAutoCommit && tran != null) tran.Commit();
                return "";
            }
            catch (Exception ex)
            {
                MyLogger.LogMsg("Update Version Db Error, 更新单据结算单位失败:" + ex.Message + ex.StackTrace, company_id);//打印日志
                if (bAutoCommit && tran != null) tran.Rollback();//事务回滚
                return "更新单据结算单位时失败";
            }

        }

        public static async Task<string> UpdateVoData(this CMySbCommand command, string company_id, bool bAutoCommit = true)
        {
            if (company_id == "") return "Error,方法传参缺少company_id";
            CMySbTransaction tran = null;
            if (bAutoCommit)//bAutoCommit表示是否开启事务，调用此方法的地方已经开启了事务的时候，就置false，否则置true，保证我这个方法的操作正确
                tran = await command.Connection.BeginTransactionAsync();

            try
            {
                SQLQueue QQ = new SQLQueue(command);
                QQ.Enqueue("haveSheetAttr", $"select sheet_id from cw_voucher_main cvm left join cw_voucher_sheet_mapper map on cvm.company_id=map.company_id and cvm.sheet_id=map.voucher_id where cvm.company_id={company_id} and map.business_sheet_id is not null and cvm.sheet_attribute is null");//从mapper表到cvm: sheet_attribute
                QQ.Enqueue("haveDebitCredit", $"select sheet_id from cw_voucher_detail where company_id={company_id} and coalesce(debit_amount,0)=0 and coalesce(credit_amount,0)=0 and coalesce(change_amount,0)<>0");//红字凭证：cvd表将change_amount改为debit_amount和credit_amount
                dynamic haveSheetAttr = null;
                dynamic haveDebitCredit = null;
                CMySbDataReader dr = await QQ.ExecuteReaderAsync();
                while (QQ.Count > 0)
                {
                    string sqlName = QQ.Dequeue();
                    if (sqlName == "haveSheetAttr") haveSheetAttr = CDbDealer.Get1RecordFromDr(dr, false);
                    if (sqlName == "haveDebitCredit") haveDebitCredit = CDbDealer.Get1RecordFromDr(dr, false);
                }
                QQ.Clear();

                string sql = "";
                if (haveSheetAttr != null)
                {
                    sql += $@"update cw_voucher_main m set sheet_attribute=jsonb_set(COALESCE(sheet_attribute, '{{}}'::jsonb), '{{biz_info}}',tt.biz_id_nos::jsonb, true) from (
                                    select company_id,voucher_id, concat('[',string_agg(biz_id_no,','),']') as biz_id_nos from (
                                        select cvsm.company_id,cvsm.voucher_id,business_sheet_type,
                                            (
                                                case when cvsm.business_sheet_type='X' or cvsm.business_sheet_type='T' then concat('{{""biz_sheet_type"":""',cvsm.business_sheet_type,'"",""biz_sheet_id"":""',x_t.sheet_id::text,'"",""biz_sheet_no"":""',x_t.sheet_no::text,'"",""biz_make_brief"":""',replace(replace(replace(replace(replace(x_t.make_brief,E'\t',''),E'\n',''),E'\\',''),E'\""',''),E'\'',''),'""}}')
                                                when cvsm.business_sheet_type='CG' or cvsm.business_sheet_type='CT' then concat('{{""biz_sheet_type"":""',cvsm.business_sheet_type,'"",""biz_sheet_id"":""',cg_cgth.sheet_id::text,'"",""biz_sheet_no"":""',cg_cgth.sheet_no::text,'"",""biz_make_brief"":""',replace(replace(replace(replace(replace(cg_cgth.make_brief,E'\t',''),E'\n',''),E'\\',''),E'\""',''),E'\'',''),'""}}')
                                                when cvsm.business_sheet_type='SK' or cvsm.business_sheet_type='FK' then concat('{{""biz_sheet_type"":""',cvsm.business_sheet_type,'"",""biz_sheet_id"":""',sk_fk.sheet_id::text,'"",""biz_sheet_no"":""',sk_fk.sheet_no::text,'"",""biz_make_brief"":""',replace(replace(replace(replace(replace(sk_fk.make_brief,E'\t',''),E'\n',''),E'\\',''),E'\""',''),E'\'',''),'""}}')
                                                when cvsm.business_sheet_type='YS' or cvsm.business_sheet_type='YF' or cvsm.business_sheet_type='DH' then concat('{{""biz_sheet_type"":""',cvsm.business_sheet_type,'"",""biz_sheet_id"":""',ys_yf_dh.sheet_id::text,'"",""biz_sheet_no"":""',ys_yf_dh.sheet_no::text,'"",""biz_make_brief"":""',replace(replace(replace(replace(replace(ys_yf_dh.make_brief,E'\t',''),E'\n',''),E'\\',''),E'\""',''),E'\'',''),'""}}')
                                                when cvsm.business_sheet_type='DHTZ' then concat('{{""biz_sheet_type"":""',cvsm.business_sheet_type,'"",""biz_sheet_id"":""',dhtz.sheet_id::text,'"",""biz_sheet_no"":""',dhtz.sheet_no::text,'"",""biz_make_brief"":""',replace(replace(replace(replace(replace(dhtz.make_brief,E'\t',''),E'\n',''),E'\\',''),E'\""',''),E'\'',''),'""}}')
                                                when cvsm.business_sheet_type='ZC' or cvsm.business_sheet_type='SR' then concat('{{""biz_sheet_type"":""',cvsm.business_sheet_type,'"",""biz_sheet_id"":""',zc_sr.sheet_id::text,'"",""biz_sheet_no"":""',zc_sr.sheet_no::text,'"",""biz_make_brief"":""',replace(replace(replace(replace(replace(zc_sr.make_brief,E'\t',''),E'\n',''),E'\\',''),E'\""',''),E'\'',''),'""}}')
                                                when cvsm.business_sheet_type='BS' or cvsm.business_sheet_type='YK' then concat('{{""biz_sheet_type"":""',cvsm.business_sheet_type,'"",""biz_sheet_id"":""',bs_yk.sheet_id::text,'"",""biz_sheet_no"":""',bs_yk.sheet_no::text,'"",""biz_make_brief"":""',replace(replace(replace(replace(replace(bs_yk.make_brief,E'\t',''),E'\n',''),E'\\',''),E'\""',''),E'\'',''),'""}}')
                                                when cvsm.business_sheet_type='CBTJ' then concat('{{""biz_sheet_type"":""',cvsm.business_sheet_type,'"",""biz_sheet_id"":""',cbtj.sheet_id::text,'"",""biz_sheet_no"":""',cbtj.sheet_no::text,'"",""biz_make_brief"":""',replace(replace(replace(replace(replace(cbtj.make_brief,E'\t',''),E'\n',''),E'\\',''),E'\""',''),E'\'',''),'""}}')
                                                when cvsm.business_sheet_type='TR' then concat('{{""biz_sheet_type"":""',cvsm.business_sheet_type,'"",""biz_sheet_id"":""',tr.sheet_id::text,'"",""biz_sheet_no"":""',tr.sheet_no::text,'"",""biz_make_brief"":""',replace(replace(replace(replace(replace(tr.make_brief,E'\t',''),E'\n',''),E'\\',''),E'\""',''),E'\'',''),'""}}')
                                                when cvsm.business_sheet_type='RK' or cvsm.business_sheet_type='CK' then concat('{{""biz_sheet_type"":""',cvsm.business_sheet_type,'"",""biz_sheet_id"":""',sio.sheet_id::text,'"",""biz_sheet_no"":""',sio.sheet_no::text,'"",""biz_make_brief"":""',replace(replace(replace(replace(replace(sio.make_brief,E'\t',''),E'\n',''),E'\\',''),E'\""',''),E'\'',''),'""}}')
                                                when cvsm.business_sheet_type='FYFT' then concat('{{""biz_sheet_type"":""',cvsm.business_sheet_type,'"",""biz_sheet_id"":""',fa.sheet_id::text,'"",""biz_sheet_no"":""',fa.sheet_no::text,'"",""biz_make_brief"":""',replace(replace(replace(replace(replace(fa.make_brief,E'\t',''),E'\n',''),E'\\',''),E'\""',''),E'\'',''),'""}}')
                                            end ) as biz_id_no
                                        from cw_voucher_sheet_mapper cvsm
                                        left join sheet_sale_main x_t on cvsm.company_id=x_t.company_id and cvsm.business_sheet_id=x_t.sheet_id
                                        left join sheet_buy_main cg_cgth on cvsm.company_id=cg_cgth.company_id and cvsm.business_sheet_id=cg_cgth.sheet_id
                                        left join sheet_get_arrears_main sk_fk on cvsm.company_id=sk_fk.company_id and cvsm.business_sheet_id=sk_fk.sheet_id
                                        left join sheet_prepay ys_yf_dh on cvsm.company_id=ys_yf_dh.company_id and cvsm.business_sheet_id=ys_yf_dh.sheet_id
                                        left join sheet_item_ordered_adjust_main dhtz on cvsm.company_id=dhtz.company_id and cvsm.business_sheet_id=dhtz.sheet_id
                                        left join sheet_fee_out_main zc_sr on cvsm.company_id=zc_sr.company_id and cvsm.business_sheet_id=zc_sr.sheet_id
                                        left join sheet_invent_change_main bs_yk on cvsm.company_id=bs_yk.company_id and cvsm.business_sheet_id=bs_yk.sheet_id
                                        left join sheet_cost_price_adjust_main cbtj on cvsm.company_id=cbtj.company_id and cvsm.business_sheet_id=cbtj.sheet_id
                                        left join sheet_cashbank_transfer_main tr on cvsm.company_id=tr.company_id and cvsm.business_sheet_id=tr.sheet_id
                                        left join sheet_stock_in_out_main sio on cvsm.company_id=sio.company_id and cvsm.business_sheet_id=sio.sheet_id
                                        left join sheet_fee_apportion_main fa on cvsm.company_id=fa.company_id and cvsm.business_sheet_id=fa.sheet_id
                                        left join sheet_sale_fee_apportion_main sfa on cvsm.company_id=sfa.company_id and cvsm.business_sheet_id=sfa.sheet_id
                                        )t group by company_id,voucher_id, business_sheet_type
                                     ) tt
                                where m.company_id=tt.company_id and m.sheet_id=tt.voucher_id and m.company_id={company_id}; ";
                }
                if (haveDebitCredit != null)
                {
                    sql += $@"update cw_voucher_detail set debit_amount=change_amount where company_id={company_id} and change_amount>0;
                                    update cw_voucher_detail set credit_amount=-change_amount where company_id={company_id} and change_amount<0;";
                }
                if (sql != "")
                {
                    command.CommandText = sql;
                    await command.ExecuteNonQueryAsync();
                }

                if (bAutoCommit && tran != null) tran.Commit();
                return "";
            }
            catch(Exception ex)
            {
                MyLogger.LogMsg("Update Version Db Error, 迁移凭证失败:" + ex.Message + ex.StackTrace, company_id);//打印日志
                if (bAutoCommit && tran != null) tran.Rollback();//事务回滚
                return "Error,迁移凭证时失败";
            }
            
        }

        //更新陈列协议结清状况
        public static async Task<string> UpdateDisplayDb_241126(this CMySbCommand command, string company_id,
            bool bAutoCommit = true)
        {
            if (company_id == "") return "Error,方法传参缺少company_id";

            CMySbTransaction tran = null;
            if (bAutoCommit)
                tran = await command.Connection.BeginTransactionAsync();
            try
            {
                string sql = "";
                string rowMonths = "";
                for (int i = 1; i <= 12; i++)
                {
                    rowMonths +=
                        $",coalesce(month{i}_qty,0) as month{i}_qty,coalesce(month{i}_given,0) as month{i}_given";
                }
                
                sql = @$"select flow_id,d.sheet_id sheet_id {rowMonths} from display_agreement_detail d left join display_agreement_main m on d.sheet_id=m.sheet_id where d.company_id = {company_id} and red_flag is null and approve_time is not null;";
                
                List<ExpandoObject> rowFlow = await CDbDealer.GetRecordsFromSQLAsync(sql, command);
                foreach (dynamic flow in rowFlow)
                {
                    JObject jFlow = JsonConvert.DeserializeObject<JObject>(JsonConvert.SerializeObject(flow));
                    Boolean allGiven = true;
                    for (int m = 1; m <= 12; m++)
                    {
                        string sQty = jFlow[$"month{m}_qty"].ToString();
                        decimal qty = CPubVars.ToDecimal(sQty);
                        string sGiven = jFlow[$"month{m}_given"].ToString();
                        decimal given = CPubVars.ToDecimal(sGiven);
                        decimal monthLeft = qty - given;
                        if (monthLeft > 0.01m)
                        {
                            allGiven = false;
                        }
                    }

                    if (allGiven == true)
                    {
                        sql= $"update display_agreement_detail set all_given=true where company_id={company_id} and flow_id={flow.flow_id};";
                                            
                        command.CommandText = sql;
                        await command.ExecuteNonQueryAsync();
                        
                        sql = $"select flow_id,all_given from display_agreement_detail where company_id={company_id} and sheet_id = {flow.sheet_id}";
                        List<ExpandoObject> allGivens = await CDbDealer.GetRecordsFromSQLAsync(sql, command);
                        Boolean settledFlag = true;
                        foreach (dynamic allGivenJudge in allGivens)
                        {
                            if (allGivenJudge.all_given.ToLower() != "true" && allGivenJudge.flow_id != flow.flow_id)
                            {
                                settledFlag = false;
                            }
                        }

                        if (settledFlag)
                        {
                            string settleTime = CPubVars.GetDateText(DateTime.Now);
                            sql=$"update display_agreement_main set settle_time='{settleTime}' where company_id={company_id} and sheet_id={flow.sheet_id} and settle_time is null;";
                            command.CommandText = sql;
                            await command.ExecuteNonQueryAsync();
                        }
                    }
                }

                if (bAutoCommit && tran != null) tran.Commit();
                return "";
            }
            catch (Exception ex)
            {
                MyLogger.LogMsg("Update Version Db Error, 更新陈列协议结清情况失败:" + ex.Message + ex.StackTrace, company_id);//打印日志
                if (bAutoCommit && tran != null) tran.Rollback();//事务回滚
                return "更新陈列协议结清情况失败";
            }
        }


        public static async Task<string> CheckToUpdateDb(CMySbCommand cmd, string company_id,string serverID, bool bAutoCommit = true)
		{
		
			if (company_id == "") return "Error,方法传参缺少company_id";//虽然几率小，但是防一手
			decimal verDb = 0,verServer=0;
            string sql = "";

			if (serverID.IsValid())
            {
				sql = @$"
select gc.version_db,gs.version from g_company gc left join g_server gs on gs.server_id={serverID} where gc.company_id={company_id}";

			}
            else
            {
				sql = @$"
select gc.version_db,gs.version from g_company gc left join g_server gs on gs.server_id=gc.server_id where gc.company_id={company_id}";
			}
			dynamic rec = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            if (rec != null)
            {
                if (rec.version_db != "") verDb = Convert.ToDecimal(rec.version_db);
                if (rec.version != "") verServer = Convert.ToDecimal(rec.version);

            }
            else return "company_id不存在";

            verServer =Convert.ToDecimal(CPubVars.g_Version);
			if (verDb >= verServer)
            {
                return "";
            }
				CMySbTransaction tran = null;
			if (bAutoCommit)//bAutoCommit表示是否开启事务，调用此方法的地方已经开启了事务的时候，就置false，否则置true，保证我这个方法的操作正确
				tran = await cmd.Connection.BeginTransactionAsync();

            try
            {
                bool bUpdated = false;
                string msg = "";
				if (verDb < Convert.ToDecimal(9.553) && verServer>=Convert.ToDecimal(9.553))
				{
					msg = await UpdateArrearsStrategy(cmd, company_id, false);
                    if(msg=="")
				    	msg = await UpdateArrearsDb(cmd, company_id, false);
                    bUpdated = true;
				}
                if (msg == "")
                {
					if (verDb < Convert.ToDecimal(9.556) && verServer >= Convert.ToDecimal(9.556))//slave怎样测试这里：手动查g_company/server_id，更新g_server/version(条件是前面的server_id)，值为if里的数字
					{
						msg = await UpdateVoData(cmd, company_id, false);
						bUpdated = true;
					}
				}


                if (msg == "")
                {
                    if (verDb < Convert.ToDecimal(9.78) && verServer >= Convert.ToDecimal(9.78))
                    {
                        msg = await UpdateArrearsDb_241023(cmd, company_id, false);
                        bUpdated = true;
                    }
                }
                if (msg == "")
                {
                    if (verDb < Convert.ToDecimal(10.13031) && verServer >= Convert.ToDecimal(10.13031))
                    {
                        msg = await UpdateArrearsStrategyForBalance(cmd, company_id, false);
                        bUpdated = true;
                    }
                }

                if (msg == "")
                {

                    if (verDb < Convert.ToDecimal(9.88) && verServer >= Convert.ToDecimal(9.88))
                    {
                        msg = await UpdateDisplayDb_241126(cmd, company_id, false);
                        bUpdated = true;
                    }
                }
                if (msg == "")
                {
                    if (verDb < Convert.ToDecimal(9.9) && verServer >= Convert.ToDecimal(9.9))
                    {
                        msg = await UpdateNewField_20241209(cmd, company_id, false);
                        bUpdated = true;
                    }
                }
                if (msg == "")
                {
                    if (verDb < Convert.ToDecimal(9.997) && verServer >= Convert.ToDecimal(9.997))
                    {
                        msg = await UpdateDepartmentID_20250105(cmd, company_id, false);
                        bUpdated = true;
                    }
                }
				if (msg == "")
                { 
					if (verDb < Convert.ToDecimal(9.997) && verServer >= Convert.ToDecimal(9.997))
				    {
					    msg = await UpdateDepartmentID_20250105(cmd, company_id, false);
					    bUpdated = true;
				    }
				}
                if (msg == "")
                {
                    // 将期初库存单的成本价更新到新的字段（now_cost_price），但是已经出错的需要后续刷
                    if (verDb < Convert.ToDecimal(10.03) && verServer >= Convert.ToDecimal(10.03))
                    {
                        msg = await UpdateOpendingStock_NowCostPrice(cmd, company_id, false);
                        bUpdated = true;
                    }
                }
                if (msg == "")
                {
                    // 将其他出库单的成本价更新成小单位
                    if (verDb < Convert.ToDecimal(10.1303) && verServer >= Convert.ToDecimal(10.1303))
                    {
                        msg = await UpdateInOutSheetDetailCostPriceAvgForSUnit(cmd, company_id, false);
                        bUpdated = true;
                    }
                }

                //if (msg == "")
                //if (bUpdated)
                if (msg == "")
                {
                    {
                        cmd.CommandText = $"update g_company set version_db='{verServer}' where company_id={company_id}";
                        await cmd.ExecuteNonQueryAsync();
                    }
                }

                if (msg != "")
                {
					if (bAutoCommit) tran.Rollback();
                    return msg;
				}
				if (bAutoCommit)
                {
                    tran.Commit();
                }

				return "";
			}
		    catch(Exception e)
            { 
                if (bAutoCommit)
                {
                    tran.Rollback();
                }
				MyLogger.LogMsg("Update Version Db Error:" + e.Message, company_id);
                return "Error";
			}
			
		
		}

		public static async Task<List<T>> QueryAsync<T>(this CMySbCommand command, string companyID, string condition=null,string orderBy=null) where T : class, IQueryable, new()
        {
            var where = $"WHERE company_id={companyID}";
            if (condition.IsValid()) where += " AND " + condition;
            if (orderBy.IsValid()) where += " ORDER BY " + orderBy;
            var instance = new T();
            command.CommandText = instance.QuerySQL() + " " + where;
            var dr = await command.ExecuteReaderAsync();
            List<T> list = new List<T>();
            while (dr.Read())
            {
                T t = new T().ReadFromDataReader(dr.dr_sql) as T;
                list.Add(t);
            }
            dr.Close();
            return list;
        }

        public static async Task<List<T>> QueryWhereAsync<T>(this CMySbCommand command, string where) where T : class, IQueryable, new()
        {
            var instance = new T();
            command.CommandText = instance.QuerySQL() + " " + where;
            var dr = await command.ExecuteReaderAsync();
            List<T> list = new List<T>();
            
            while (dr.Read())
            {
                T t = new T().ReadFromDataReader(dr.dr_sql) as T;
                list.Add(t);
            }
            dr.Close();
           
           // string ttt = typeof(T).ToString();
            return list;
        }

        public static async Task UpdateWhereAsync<T>(this CMySbCommand command, T model, string where=null, string updateCols=null) where T : class, IModel
        {
            var type = typeof(T);
            var props = type.GetProperties();
            List<string> cols = new List<string>();
            foreach (PropertyInfo prop in props)
            {
                if(prop.GetCustomAttribute<YjColumnAttribute>() is YjColumnAttribute col && !col.IsPK)
                {
                    var field = col.DbField ?? prop.Name;
                    if (field == "company_id") continue;
                    if (updateCols?.Contains(field) ?? true)
                    {
                        var val = prop.GetValue(model);
                        if (val == "") val ="null";
                        else val = $"'{val}'";
                  
                        cols.Add($"{field}={val}");
                    }
                }
            }
            var table = type.GetCustomAttribute<YjTableAttribute>()?.TableName ?? type.Name;
            where ??= $"WHERE company_id = {model.company_Id}";
            var sql = $"UPDATE {table} SET {string.Join(',',cols)} {where}";
            command.CommandText = sql;
            await command.ExecuteNonQueryAsync();
        }

        public static async Task<int> InsertAsync<T>(this CMySbCommand command, T model) where T : class, IModel
        {
            var type = typeof(T);
            var props = type.GetProperties();
            List<string> cols = new List<string>();
            List<string> vals = new List<string>();
            string returnId = null;
            foreach (PropertyInfo prop in props)
            {
                if (prop.GetCustomAttribute<YjColumnAttribute>() is YjColumnAttribute col)
                {
                    var field = col.DbField ?? prop.Name;
                    if(col.IsSerial)
                    {
                        returnId = "returning " + field;
                        continue;
                    }
                     cols.Add(field);
                     vals.Add( $"'{prop.GetValue(model)}'");
                }
            }
            var table = type.GetCustomAttribute<YjTableAttribute>()?.TableName ?? type.Name;
            var sql = $"INSERT INTO {table} ({string.Join(',', cols)}) VALUES ({string.Join(',', vals)}) {returnId}";
            command.CommandText = sql;
            int id = (int)await command.ExecuteScalarAsync();
            return id;
        }

        public static async Task DeleteAsync<T>(this CMySbCommand command,string companyId,string condition) where T : class, IModel
        {
            var type = typeof(T);
            var table = type.GetCustomAttribute<YjTableAttribute>()?.TableName ?? type.Name;
            if (condition.IsValid()) condition = "and " + condition;
            command.CommandText = $"DELETE FROM {table} WHERE company_id = {companyId} {condition}";
            await command.ExecuteNonQueryAsync();
        }

        #endregion

        public static string ToText(this DateTime thisDate)
        {
            return thisDate.ToString("yyyy-MM-dd HH:mm:ss");
        }
        public static string ToDayStartText(this DateTime thisDate)
        {
            return thisDate.ToString("yyyy-MM-dd")+" 00:00:00";
        }
        public static string ToDayEndText(this DateTime thisDate)
        {
            return thisDate.ToString("yyyy-MM-dd")+" 23:59:59";
        }

        public static object GetDefault(this Type type)
        {
            if (type.IsValueType)
            {
                if (type == typeof(int)) return 0;
                if (type == typeof(bool)) return false;
                if (type == typeof(DateTime)) return DateTime.Now;
            }
            return null;
        }

        public static string ToDbType(this Type type)
        {
            if (type == typeof(string))
            {
                return "text";
            }
            else if (type == typeof(int))
            {
                return "int";
            }
            else if (type == typeof(bool))
            {
                return "bool";
            }
            else if (type == typeof(DateTime))
            {
                return "TimeSpan";
            }
            else if (type == typeof(float))
            {
                return "float";
            }
            else if (type.IsEnum)
            {
                return "int";
            }
            throw new Exception($"不支持的数据类型：{type.FullName}");
        }

        public static DataTable ToDataTable<T>(this IEnumerable<T> models, Action<T> action = null) where T:class,IModel
        {
            var m = models.FirstOrDefault();
            if (m == default) return null;
            Type modelType = m.GetType();
            var props = modelType.GetProperties();
            var tableName = modelType.GetCustomAttribute<YjTableAttribute>()?.TableName ?? modelType.Name;
            var table = new DataTable(tableName)
            {
                HeadRow = m.ToRow(props, true)
            };
            foreach (var model in models)
            {
                action?.Invoke(model);
                table.DataRows.Add(model?.ToRow(props));
            }
            return table;
        }

        //public static int ToInt<T>(this T _enum) where T:Enum
        //{
        //    return (int)_enum;
        //}

        public static string units_from_s_to_bms(float s_qty,float b,float m,float s,string bU,string mU,string sU)
        {
            string real_unit_qty = "";
            double b_unit_quantity;
            double s_unit_quantity;
            double m_unit_quantity;
            if (s_qty == (-0)) real_unit_qty = "0";
            else if (bU.IsInvalid()) real_unit_qty = Math.Round(s_qty,2) + sU;
            else if (bU.IsValid() && mU.IsInvalid())
            {
                b_unit_quantity = Math.Sign(s_qty) * Math.Floor(Math.Abs(s_qty) / b);
                s_unit_quantity = Math.Round(s_qty % b);
                real_unit_qty = b_unit_quantity.ToString()+ bU +s_unit_quantity.ToString() + sU;
                if (b_unit_quantity == 0 && s_unit_quantity != 0) real_unit_qty = s_unit_quantity.ToString() + sU;
                else if (b_unit_quantity != 0 && s_unit_quantity == 0)  real_unit_qty = b_unit_quantity.ToString() + bU;
            }
            else 
            {
                b_unit_quantity = Math.Sign(s_qty) * Math.Floor(Math.Abs(s_qty) / b);
                m_unit_quantity = Math.Sign(s_qty) * Math.Floor(Math.Abs(s_qty) % b / m);
                s_unit_quantity = Math.Sign(s_qty) * Math.Round(Math.Abs(s_qty) % b % m);
                real_unit_qty = b_unit_quantity.ToString() + bU + m_unit_quantity.ToString()+mU+ s_unit_quantity.ToString() + sU;
                
            }
            //real_unit_qty = real_unit_qty.Replace("-0", "0");
            return real_unit_qty;
        }
        public static string units_from_s_to_bms_old(decimal s_qty, decimal b, decimal m, decimal s, string bU, string mU, string sU)
        {
            string real_unit_qty = "";
            decimal b_unit_quantity;
            decimal s_unit_quantity;
            decimal m_unit_quantity;
            if (s_qty == (-0)) real_unit_qty = "0";
            else if (bU.IsInvalid()) real_unit_qty = Math.Round(s_qty, 2) + sU;
            else if (bU.IsValid() && mU.IsInvalid())
            {
                b_unit_quantity = Math.Sign(s_qty) * Math.Floor(Math.Abs(s_qty) / b);
                s_unit_quantity = Math.Round(s_qty % b,3);
                real_unit_qty = b_unit_quantity.ToString("0.###", CultureInfo.InvariantCulture) + bU + s_unit_quantity.ToString("0.###", CultureInfo.InvariantCulture) + sU;
                if (b_unit_quantity == 0 && s_unit_quantity != 0) real_unit_qty = s_unit_quantity.ToString("0.###", CultureInfo.InvariantCulture) + sU;
                else if (b_unit_quantity != 0 && s_unit_quantity == 0) real_unit_qty = b_unit_quantity.ToString("0.###", CultureInfo.InvariantCulture) + bU;
            }
            else
            {
                b_unit_quantity = Math.Sign(s_qty) * Math.Floor(Math.Abs(s_qty) / b);
                m_unit_quantity = Math.Sign(s_qty) * Math.Floor(Math.Abs(s_qty) % b / m);
                s_unit_quantity = Math.Sign(s_qty) * Math.Round(Math.Abs(s_qty) % b % m,3);
                real_unit_qty = b_unit_quantity.ToString("0.###", CultureInfo.InvariantCulture) + bU + m_unit_quantity.ToString("0.###", CultureInfo.InvariantCulture) + mU + s_unit_quantity.ToString("0.###", CultureInfo.InvariantCulture) + sU;

            }
            //real_unit_qty = real_unit_qty.Replace("-0", "0");
            return real_unit_qty;
        }


        public static string ToMoneyStr(this double money,bool zeroAsEmpty =true,int precision=2)
        {
            if (zeroAsEmpty && money == 0) return "";
            return money.ToMoney(precision).ToString();
        }


        public static decimal ToMoney(this double money,int precision=2)
        {
            return Math.Round(CPubVars.ToDecimal(money), precision, MidpointRounding.AwayFromZero);
        }
        public static decimal ToMoney(this decimal money, int precision = 2)
        {
            return Math.Round(money, precision, MidpointRounding.AwayFromZero);
        }


        /// <summary>
        /// 深拷贝
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="obj"></param>
        /// <returns></returns>
        public static T DeepCopy<T>(T obj)
        {
            using (var ms = new MemoryStream())
            {
                var xml = new XmlSerializer(typeof(T));             
                xml.Serialize(ms, obj);
                ms.Seek(0, SeekOrigin.Begin);
                var ret = xml.Deserialize(ms);
                ms.Close();
                return (T)ret;
            }
            
        }

        #region 工具性质的扩展方法
        // Created by InfSein, 2023

        /// <summary>
        /// 将List中每个元素都转为字符串，用自定义的连接符分割
        /// <para>类似JS数组中的Array.join方法</para>
        /// </summary>
        public static string JsJoin<T>(this List<T> list, string separator)
        {
            var result = string.Empty;
            foreach (var item in list)
            {
                if (result.Length > 0) result += separator;
                result += item.ToString();
            }
            return result;
        }
        #endregion

        /// <summary>
        /// 尝试访问匿名类型对象的属性
        /// </summary>
        public static object TryGetPropertyValue(object obj, string propName)
        {
            if (obj == null) return null;
            var prop = obj.GetType().GetProperty(propName, BindingFlags.Instance | BindingFlags.Public);
            return prop != null ? prop.GetValue(obj) : null;
        }

        /// <summary>
        /// 获取与传入销售单相关联的销售订单id
        /// </summary>
        /// <param name="saleSheetIds">销售单id，多个的话用逗号隔开</param>
        /// <returns>销售订单id，多个的话用逗号隔开</returns>
        public static async Task<CallResult> GetRelateSaleOrderSheets(
            CMySbCommand cmd, string companyId, string saleSheetIds)
        {
            if (companyId.IsInvalid()) return new("Error", "未获取到companyId", "");
            if (saleSheetIds.IsInvalid()) return new("OK", "", "");

            var sql = $@"
                SELECT
                    order_sheet_id
                FROM 
                    sheet_sale_main sm
                WHERE
                    sm.company_id = {companyId}
                    and sm.sheet_id in ({saleSheetIds});
            ";
            var orderSheetRecords = await CDbDealer.GetRecordsFromSQLAsync<RelateSaleOrderSheet>(sql, cmd);
            orderSheetRecords ??= [];

            if (orderSheetRecords.Count > 0)
            {
                var orderSheetIds = orderSheetRecords.Select(x => x.order_sheet_id).ToList();
                var orderSheetIdsStr = string.Join(",", orderSheetIds);
                return new("OK", "", orderSheetIdsStr);
            }
            else
            {
                return new("OK", "", "");
            }
        }
        private struct RelateSaleOrderSheet
        {
            public string order_sheet_id { get; set; }
        }

        public static async Task<string> ProcessAppendixPicsRetDBStr(IHttpClientFactory _httpClientFactory, List<string> appendix_pictures_base64, string companyID)
        {
            if (appendix_pictures_base64.Count == 0)
            {
                return "[]";
            }

            int uploadPicIdx = 0;
            string folderPath = $"uploads/{DateTime.Today:yyyyMM}/";
            if (!System.IO.Directory.Exists(folderPath))
            {
                System.IO.Directory.CreateDirectory(folderPath);
            }

            List<string> paths = new List<string>();
            foreach (var pic in appendix_pictures_base64)
            {
                if (HuaWeiObs.IsB64(pic))
                {
                    string appendixPicture = pic.Replace("data:image/jpeg;base64,", "").Replace("data:image/png;base64,", "");
                    uploadPicIdx++;
                    string fileName = $"Appendix_{companyID}_{Guid.NewGuid()}_{uploadPicIdx}";
                    string fileExtension = ".jpeg";
                    using (MemoryStream stream = new MemoryStream(Convert.FromBase64String(appendixPicture)))
                    {
                        string path = folderPath + fileName + fileExtension;
                        string err = await HuaWeiObs.Save(_httpClientFactory, stream, path);
                        paths.Add($"\"/{DateTime.Today:yyyyMM}/{fileName}{fileExtension}\"");
                    }
                }
                else if (pic.StartsWith(HuaWeiObs.BucketLinkHref))
                {
                    paths.Add($"\"{pic.Replace($"{HuaWeiObs.BucketLinkHref}/uploads", "")}\"");
                }
                else
                {
                    NLogger.Error("图片解析错误:不是合法的BASE64字符串，也不是OBS图像链接。详细内容：" + pic);
                    throw new Exception("图片解析错误");
                }
            }

            var showcase_pictures_indb = $"[{string.Join(',', paths)}]";
            return showcase_pictures_indb;
        }
    }
}
