﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ArtisanManage.Pages.BaseInfo
{
    public class FeeOutDetailModel : PageQueryModel
    { 
        public FeeOutDetailModel(CMySbCommand cmd) : base(Services.MenuId.feeOutDetail)
        {
            this.cmd = cmd;
            this.PageTitle = "收入支出明细表";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput",ForQuery=false, CompareOperator="=", QueryOnChange=false,Value=CPubVars.GetDateText(DateTime.Now.Date.AddDays(-30))+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead",CtrlType="jqxDateTimeInput", ForQuery=false, CompareOperator="<",Value = CPubVars.GetDateText(DateTime.Now.Date) + " 23:59",
                    JSDealItemOnSelect =@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"sup_name",new DataItem(){FldArea="divHead",Title="客户/供应商", CompareOperator="=" ,ButtonUsage="list",SqlFld="sc.supcust_id",
                    SqlForOptions="select supcust_id as v,sup_name as l,py_str as z from info_supcust where company_id= ~COMPANY_ID and (status = '1' or status is null) "  }},
                {"sheet_type",  new DataItem(){FldArea="divHead",Title="单据类型",ButtonUsage="list",LabelFld="sheet_type_name",Checkboxes = true,
                    Source = "[{v:'ZC',l:'费用支出单'},{v:'SR',l:'其他收入单'},{v:'X',l:'销售单'},{v:'T',l:'销售退货单'},{v:'CG',l:'采购单'},{v:'CT',l:'采购退货单'},{v:'SK',l:'收款单'},{v:'YS',l:'预收款单'},{v:'YF',l:'预付款单'},{v:'DH',l:'定货会'}, {v:'FK',l:'付款单'}]",CompareOperator="="}},
                {"seller_id",new DataItem(){Title="业务员",FldArea="divHead",LabelFld="seller_name",ButtonUsage="list",CompareOperator="=",SqlFld="seller_id",SqlForOptions=CommonTool.selectSellers,Checkboxes=true } },
                {"sub_type", new DataItem(){ Title="支付类型", FldArea="divHead", LabelFld="sub_type_label", ButtonUsage="list", Checkboxes = true, CompareOperator="=", Source = "[{v:'ZC',l:'费用支出'},{v:'QTSR',l:'其他收入'},{v:'QT',l:'现金银行'}]"} },
                 {"sub_id",new DataItem(){Title="科目",FldArea="divHead", LabelFld="sub_name",ButtonUsage="list",CompareOperator="=",SqlFld="t.sub_id",Checkboxes = true,
                SqlForOptions = "SELECT s.sub_id as v ,s.sub_name as l  FROM cw_subject s WHERE s.company_id =~COMPANY_ID AND sub_type IN ( 'ZC', 'QTSR', 'QT' ) ORDER BY order_index"}},
            };


            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                      ShowAggregates = true,
                      Sortable=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"sheet_id",new DataItem(){Hidden=true,HideOnLoad = true}},
                       {"sheet_no",     new DataItem(){Title="单据编号", Sortable=true,    Width="10%",Linkable = true}},
                       {"sheet_type",   new DataItem(){Title="单据类型",    Width="100",SqlFld="(CASE sheet_type WHEN 'ZC' THEN '费用支出单' WHEN 'SR' THEN '其他收入单' WHEN 'X' THEN '销售单'WHEN 'T' THEN '销售退货单' WHEN 'CG' THEN '采购单'WHEN 'CT' THEN '采购退货单'WHEN 'YF' THEN '预付款单' WHEN 'SK' THEN '收款单' WHEN 'YS' THEN '预收款单' WHEN 'YF' THEN '预付款单' WHEN 'FK' THEN '付款单' WHEN 'DH' THEN '订货会' END)"}},
                       {"sup_name",     new DataItem(){Title="客户名称",  Width="150" }},
                       {"oper_name",    new DataItem(){Title="业务员", Sortable=true,  Width="300" }},
                       {"sub_name",     new DataItem(){Title="科目",    Width="10%" }},
                       {"sub_amount",     new DataItem(){Title="金额", Sortable=true,    Width="10%" ,ShowSum=true, SqlFld="(case when sheet_type='YS' and sub_type='QTSR' then -sub_amount else sub_amount end)"}},
                       {"happen_time",     new DataItem(){Title="发生时间", Sortable=true,    Width="10%"}},
                       {"remark",     new DataItem(){Title="备注",    Width="10%" }},
                     },

                     QueryFromSQL=@"

FROM
(
    SELECT sheet_id, sheet_no , payway1_id as sub_id, supcust_id, getter_id seller_id,sheet_type,money_inout_flag*payway1_amount sub_amount,happen_time,make_brief remark     FROM  sheet_fee_out_main 
    WHERE  company_id =~COMPANY_ID AND red_flag IS NULL AND approve_time IS NOT NULL AND happen_time >= '~VAR_startDay' AND happen_time < '~VAR_endDay' 

    union all

    SELECT sheet_id, sheet_no , payway2_id as sub_id, supcust_id, getter_id seller_id,sheet_type,money_inout_flag*payway2_amount sub_amount,happen_time, make_brief remark     FROM  sheet_fee_out_main 
    WHERE  company_id =~COMPANY_ID AND red_flag IS NULL AND approve_time IS NOT NULL AND happen_time >= '~VAR_startDay' AND happen_time < '~VAR_endDay' 


    union all 

     SELECT sm.sheet_id, sm.sheet_no ,  fee_sub_id  sub_id,sm.supcust_id, getter_id seller_id,sheet_type,money_inout_flag*fee_sub_amount sub_amount,sm.happen_time,remark   FROM  sheet_fee_out_detail sd
    LEFT JOIN sheet_fee_out_main sm ON sm.sheet_id = sd.sheet_id AND sm.company_id =~COMPANY_ID
    WHERE   sm.company_id =~COMPANY_ID AND sm.red_flag IS NULL AND sm.approve_time IS NOT NULL AND sm.happen_time >= '~VAR_startDay' AND sm.happen_time < '~VAR_endDay'

    union all

    SELECT sheet_id,sheet_no,payway1_id sub_id,supcust_id,seller_id,sheet_type,money_inout_flag*payway1_amount sub_amount,happen_time,make_brief remark    FROM  sheet_sale_main
    WHERE company_id =~COMPANY_ID AND red_flag IS NULL AND approve_time IS NOT NULL AND happen_time >= '~VAR_startDay' AND happen_time < '~VAR_endDay'

    union all

    SELECT sheet_id,sheet_no,payway2_id sub_id,supcust_id,seller_id,sheet_type,money_inout_flag*payway2_amount sub_amount,happen_time,make_brief remark    FROM  sheet_sale_main
    WHERE company_id =~COMPANY_ID AND red_flag IS NULL AND approve_time IS NOT NULL AND happen_time >= '~VAR_startDay' AND happen_time < '~VAR_endDay'

    union all

    SELECT sheet_id,sheet_no,payway3_id sub_id,supcust_id,seller_id,sheet_type,money_inout_flag*payway3_amount sub_amount,happen_time,make_brief remark    FROM  sheet_sale_main
    WHERE company_id =~COMPANY_ID AND red_flag IS NULL AND approve_time IS NOT NULL AND happen_time >= '~VAR_startDay' AND happen_time < '~VAR_endDay'
	

   union all

    SELECT sheet_id,sheet_no,payway1_id sub_id,supcust_id,getter_id,sheet_type,money_inout_flag*payway1_amount sub_amount,happen_time,make_brief remark    FROM  sheet_get_arrears_main
    WHERE company_id =~COMPANY_ID AND red_flag IS NULL AND approve_time IS NOT NULL AND happen_time >= '~VAR_startDay' AND happen_time < '~VAR_endDay'
	
    union  all

    SELECT sheet_id,sheet_no,payway2_id sub_id,supcust_id,getter_id,sheet_type,money_inout_flag*payway2_amount sub_amount,happen_time,make_brief remark    FROM  sheet_get_arrears_main
    WHERE company_id =~COMPANY_ID AND red_flag IS NULL AND approve_time IS NOT NULL AND happen_time >= '~VAR_startDay' AND happen_time < '~VAR_endDay'

    union  all

    SELECT sheet_id,sheet_no,payway3_id sub_id,supcust_id,getter_id,sheet_type,money_inout_flag*payway3_amount sub_amount,happen_time,make_brief remark    FROM  sheet_get_arrears_main
    WHERE company_id =~COMPANY_ID AND red_flag IS NULL AND approve_time IS NOT NULL AND happen_time >= '~VAR_startDay' AND happen_time < '~VAR_endDay'


    union  all

    SELECT sheet_id,sheet_no ,payway1_id  sub_id,supcust_id,seller_id,sheet_type, -1 * money_inout_flag*payway1_amount sub_amount,happen_time ,make_brief remark   FROM sheet_buy_main
    WHERE company_id =~COMPANY_ID AND red_flag IS NULL AND approve_time IS NOT NULL AND happen_time >= '~VAR_startDay' AND happen_time < '~VAR_endDay'

    union  all

    SELECT sheet_id,sheet_no ,payway2_id  sub_id,supcust_id,seller_id,sheet_type, -1 * money_inout_flag*payway2_amount sub_amount,happen_time ,make_brief remark   FROM sheet_buy_main
    WHERE company_id =~COMPANY_ID AND red_flag IS NULL AND approve_time IS NOT NULL AND happen_time >= '~VAR_startDay' AND happen_time < '~VAR_endDay'

    union  all

    SELECT sheet_id,sheet_no ,payway3_id  sub_id,supcust_id,seller_id,sheet_type, -1 * money_inout_flag*payway3_amount sub_amount,happen_time ,make_brief remark   FROM sheet_buy_main
    WHERE company_id =~COMPANY_ID AND red_flag IS NULL AND approve_time IS NOT NULL AND happen_time >= '~VAR_startDay' AND happen_time < '~VAR_endDay'


    union  all

    SELECT sheet_id,sheet_no ,payway1_id  sub_id,supcust_id,getter_id seller_id,sheet_type,money_inout_flag*payway1_amount sub_amount,happen_time ,make_brief remark   FROM sheet_prepay
    WHERE company_id =~COMPANY_ID AND red_flag IS NULL AND approve_time IS NOT NULL AND happen_time >= '~VAR_startDay' AND happen_time < '~VAR_endDay'

    union  all

    SELECT sheet_id,sheet_no ,payway2_id  sub_id,supcust_id,getter_id seller_id,sheet_type,money_inout_flag*payway2_amount sub_amount,happen_time ,make_brief remark   FROM sheet_prepay
    WHERE company_id =~COMPANY_ID AND red_flag IS NULL AND approve_time IS NOT NULL AND happen_time >= '~VAR_startDay' AND happen_time < '~VAR_endDay'

    union  all

    SELECT sheet_id,sheet_no ,payway3_id  sub_id,supcust_id,getter_id seller_id,sheet_type,money_inout_flag*payway3_amount sub_amount,happen_time ,make_brief remark   FROM sheet_prepay
    WHERE company_id =~COMPANY_ID AND red_flag IS NULL AND approve_time IS NOT NULL AND happen_time >= '~VAR_startDay' AND happen_time < '~VAR_endDay'
	
) t
LEFT JOIN cw_subject cw ON cw.sub_id = t.sub_id AND cw.company_id =~COMPANY_ID
LEFT JOIN info_supcust sc ON sc.supcust_id = t.supcust_id AND sc.company_id =~COMPANY_ID 
left join info_operator o on o.oper_id=t.seller_id and o.company_id=~COMPANY_ID
where  cw.sub_type in ('QT','QTSR','ZC') and coalesce(sub_amount,0)<>0
",
                     //目前只能用交易时间查询，从现金收支表带过来的按交账时间查询还未做
                     //虽然保留了费用支出单的费用明细可用来查费用，但销售单等上面的费用支付类型还没有添加；这个表本质上只展示现金银行收支，等费用支出明细表开发完毕后，这个表上的费用支出类型明细会删除。
                     QueryOrderSQL=" order by happen_time desc"
                  }
                }
            };
        }

        public async Task OnGet()
        {
            await InitGet(cmd);
        }
        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {

            SQLVariables["startDay"] = DataItems["startDay"].Value;
            SQLVariables["endDay"] = DataItems["endDay"].Value;


        }


    }



    [Route("api/[controller]/[action]")]
    public class FeeOutDetailController : QueryController
    { 
        public FeeOutDetailController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            FeeOutDetailModel model = new FeeOutDetailModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }

        [HttpGet]
        public async Task<object> GetQueryRecords()
        {

            FeeOutDetailModel model = new FeeOutDetailModel(cmd);
            
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {

            FeeOutDetailModel model = new FeeOutDetailModel(cmd);
            
            return await model.ExportExcel(Request, cmd);
        }
    }
}
