﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using System.Linq;
using ArtisanManage.Models;
using ArtisanManage.Services;
using ArtisanManage.YingJiangBackstage.Pojo;
using ArtisanManage.YingJiangBackstage.Utils;

namespace ArtisanManage.YingJiangCommon.Dao.ItemOccupyReportDao
{
    public class ItemOccupyReportDao
    {

        public async Task<Dictionary<string, dynamic>> GetItemOccupys(dynamic parameter, CMySbCommand cmd)
        {
            string operKey = parameter.operKey;
            string type = parameter.type;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string duringSql = GetSqlByQueryType(type, parameter, companyID);
            cmd.CommandText = duringSql;
            dynamic duringList = await CDbDealer.GetRecordsFromSQLAsync(duringSql, cmd);

            List<string> regionID = ((JArray)parameter.regionID).ToObject<List<string>>();       
            string regionIDSql = regionID.Count > 0                 // 根据所选择的片区限制supcust数量
                ? $" AND region_id IN ({string.Join(",", regionID.Select(k => $"'{k}'"))})"
                : "";
            List<string> operatorID = ((JArray)parameter.operatorID).ToObject<List<string>>();
            string onlyCustomer = " AND supcust_flag ilike '%C%'";  // 限制supcust只有客户没有供应商

            string countSupcustSQL = $@"SELECT COUNT(1) as count FROM info_supcust where company_id = {companyID} {onlyCustomer} {regionIDSql}";
            dynamic countSupcustData = await CDbDealer.Get1RecordFromSQLAsync(countSupcustSQL, cmd);

            Dictionary<string, dynamic> data = new Dictionary<string, dynamic>();
            data.Add("duringList", duringList);

            data.Add("supcustCount", countSupcustData.count);
            return data;
        }
        public string GetSqlByQueryType(string type, dynamic param, string companyID)
        {
            int pageSize = param.pageSize;
            string startTime = param.startTime;
            string endTime = param.endTime;
            int pageNo = param.pageNo;
            string onlyCustomer = " AND supcust_flag ilike '%C%'";  // 限制supcust只有客户没有供应商

            List<string> filterName = ((JArray)param.filterValue).ToObject<List<string>>();  // 名称筛选
            List<string> operatorID = ((JArray)param.operatorID).ToObject<List<string>>();   // 业务员
            List<string> regionID = ((JArray)param.regionID).ToObject<List<string>>();       // 片区
            string operatorSql = operatorID.Count > 0
               ? $" AND ssm.seller_id IN ({string.Join(",", operatorID.Select(k => $"'{k}'"))})"
               : "";
            string regionIDSql = regionID.Count > 0
                ? $" AND isc.region_id IN ({string.Join(",", regionID.Select(k => $"'{k}'"))})"
                : "";
            string filterNameSql = filterName.Count > 0
                ? " AND (" + string.Join(" OR ", filterName.Select(name => $"{type}_name ILIKE '%{name}%'")) + ")"
                : "";
            string extraCondition = onlyCustomer + operatorSql + regionIDSql;

            string sqlCount = param.pageNo == 1 ? "count(1) over () as total" : "-1 as total";
            string sql = "";
            if (type == "brand")
            {
                sql = $@"    SELECT
	        brand_id as item_id,brand_name as item_name,{sqlCount},
	        (
	            SELECT 
	            count( DISTINCT isc.supcust_id)
	            FROM
		        sheet_sale_detail ssd
		        LEFT JOIN sheet_sale_main ssm ON ssd.company_id = {companyID} AND ssd.sheet_id = ssm.sheet_id  AND ssm.company_id = ssd.company_id
				LEFT JOIN info_item_prop iip ON iip.item_id = ssd.item_id  AND ssd.company_id = iip.company_id
                LEFT JOIN info_supcust isc ON isc.company_id = ssd.company_id AND isc.company_id = ssm.company_id AND isc.supcust_id = ssm.supcust_id
	            WHERE iip.item_brand = iib.brand_id AND  ssm.happen_time between '{startTime} 00:00' AND '{endTime} 23:59' AND ssm.company_id = {companyID} 
                {extraCondition}
            ) 
            FROM
	        info_item_brand iib 

            WHERE
	        iib.company_id =  {companyID} {filterNameSql}
            ORDER BY count desc
 LIMIT {pageSize} OFFSET {(pageNo - 1) * pageSize}
";
            }
            if (type == "item")
            {
                sql = $@"
            SELECT
	        item_id,barcode,item_name,{sqlCount},
	        (
	            SELECT 
	            count( DISTINCT isc.supcust_id)
	            FROM
		        sheet_sale_detail ssd
		        LEFT JOIN sheet_sale_main ssm ON ssd.company_id = {companyID} AND ssd.sheet_id = ssm.sheet_id  AND ssm.company_id = {companyID} 
                LEFT JOIN info_supcust isc ON isc.company_id = {companyID} AND ssd.company_id = {companyID} AND ssm.company_id = {companyID} AND isc.supcust_id = ssm.supcust_id
	            WHERE iip.item_id = item_id AND  ssm.happen_time between '{startTime} 00:00' AND '{endTime} 23:59' AND ssm.company_id = {companyID} 
                AND isc.company_id = {companyID}
                {extraCondition}
            ) 
            FROM
	        info_item_prop iip 
            WHERE
	        company_id = {companyID} {filterNameSql} and iip.status = 1 or null
            ORDER BY count desc
            LIMIT {pageSize} OFFSET {(pageNo - 1) * pageSize}
            
            ";
            }
            return sql;
        }

        public static async Task<Dictionary<string, dynamic>> GetCompanyInfo(dynamic parameter, CMySbCommand cmd)
        {
            string operKey = parameter.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            SQLQueue QQ = new SQLQueue(cmd);

            string sql = $@"
                        SELECT region_id, region_name
                        FROM info_region
                        WHERE company_id = {companyID}
                        order by order_index
                        ";
            QQ.Enqueue("regionList", sql);

            sql = $@"
                SELECT oper_id AS value, oper_name AS label
                FROM info_operator
                WHERE company_id = {companyID};
                ";
            QQ.Enqueue("operatorList", sql);

            dynamic infoRegion = null;
            dynamic operatorList = null;
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();

            while (QQ.Count > 0)
            {
                string tbl = QQ.Dequeue();
                if (tbl == "regionList")
                {
                    infoRegion = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (tbl == "operatorList")
                {
                    dr.Close();
                    dynamic list = await CDbDealer.GetRecordsFromSQLAsync<TreeData>(sql, cmd);
                    operatorList = TreeUtil.TreeDataToTree(list);
                }
            }

            QQ.Clear();
            Dictionary<string, dynamic> data = new Dictionary<string, dynamic>();

            data.Add("infoRegion", infoRegion);
            data.Add("operatorList", operatorList);
            return data;
        }



    }
}