using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using ArtisanManage.Models;
using ArtisanManage.Services;
using ArtisanManage.MyJXC;
using static ArtisanManage.MyJXC.SheetSale;
using Newtonsoft.Json;
using System.Dynamic;
using ArtisanManage.Pages.Sheets;
using System.IO;
using HuaWeiObsController;
using System.Net.Http;
using ArtisanManage.WebAPI.MessageOSUtil;
using ArtisanManage.YingjiangMessage.Pojo;
using ArtisanManage.YingjiangMessage.Services;
using ArtisanManage.WebAPI;
using NPOI.POIFS.Crypt.Dsig;
using MathNet.Numerics;
using Org.BouncyCastle.Asn1.X9;
using Microsoft.AspNetCore.Http;
using NPOI.XSSF.UserModel;
using static ArtisanManage.Models.PageQueryModel;
using System.Text;
using System.Xml;
using System.ComponentModel.Design;

namespace ArtisanManage.Pages
{
    public class SaleOrderSheetModel : PageSheetModel<SheetRowSaleOrder>
    { 
        
        public string SheetTitle = "";
        public bool EnablePiaoZhengTong = false;
       
        public SaleOrderSheetModel(CMySbCommand cmd):base(MenuId.sheetSaleOrder)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"sheet_no",new DataItem(){Title="单号",UseJQWidgets=false}},
                {"sheet_id",new DataItem(){Title="sheet_id", CtrlType="hidden", FldArea="divHead"}},
                {"placeholder_sheet_no",new DataItem(){Title="占位单", UseJQWidgets=false}},
                {"placeholder_sheet_id",new DataItem(){Title="placeholder_sheet_id", CtrlType="hidden", FldArea="divHead"}},
                {"red_flag",new DataItem(){Title="red_flag", CtrlType="hidden", FldArea="divHead"}}, 
                {"sheet_type",new DataItem(){Title="sheet_type", CtrlType="hidden", FldArea="divHead"}},
                {"sheetType",new DataItem(){Title="sheetType", CtrlType="hidden", FldArea="divHead"}},
                
                {"pay_bill_id",new DataItem(){FldArea="divHead", Title="关联订单", CtrlType="hidden"}},
                {"payb_status_name",new DataItem(){Title="关联订单状态",UseJQWidgets=false}},
                {"order_source",new DataItem(){Title="order_source", CtrlType="hidden", FldArea="divHead"}},
                {"order_source_name",new DataItem(){Title="来源",UseJQWidgets=false}},
                {"supcust_id",CommonTool.GetDataItem("supcust_id",new DataItemChange{ForQuery=false,AlwaysShow=true,ButtonUsage="event"}) },
                {"acct_cust_id",new DataItem(){FldArea="divHead",Title="结账单位",LabelFld="acct_cust_name",Hidden=true,HideOnLoad=true}},

                {"seller_id",new DataItem(){FldArea="divHead",Title="业务员",LabelFld="seller_name",ButtonUsage="list",SqlForOptions=CommonTool.selectSellers,AlwaysShow=true}},
                {"senders_id",new DataItem(){FldArea="divHead",Title="送货员",LabelFld="senders_name",ButtonUsage="list",SqlForOptions=CommonTool.selectSenders, Checkboxes = true} },
                {"branch_id",new DataItem(){FldArea="divHead",Restrict="sheet_xd,sheet_td", Title="仓    库",LabelFld="branch_name", ButtonUsage="list",SqlForOptions=CommonTool.selectBranch,GetOptionsOnLoad=true, AlwaysShow=true}},
                //{"oper_date",new DataItem(){title="制单日期",ctrlType="jqxDateTimeInput"}},

                {"sheet_usage",new DataItem(){  FldArea="divHead",Title="单据用途",ButtonUsage="list",Hidden=true,
					Source = "[{v:'D',l:'正常销售'},{v:'LQ',l:'处理临期'},{v:'T',l:'退货'},{v:'HH',l:'换货'},{v:'DH',l:'还定货'},{v:'J',l:'借货'},{v:'H',l:'还货'},{v:'CL',l:'陈列兑付'}]"}},

				{"happen_time",new DataItem(){FldArea="divHead",Title="交易日期",CtrlType="jqxDateTimeInput",PlaceHolder="当前时间" }},
                {"send_time",new DataItem(){FldArea="divHead",Title="送货时间",CtrlType="jqxDateTimeInput",PlaceHolder="当前时间",Hidden=true }},
                {"TempHappenTime",new DataItem(){Title="TempHappenTime", CtrlType="hidden",  FldArea="divHead"}},
                {"isRedAndChange",new DataItem(){Title="isRedAndChange",CtrlType="hidden",  FldArea="divHead"} },
                {"old_sheet_id",new DataItem(){Title="old_sheet_id",CtrlType="hidden",  FldArea="divHead"} },
                {"make_brief",new DataItem(){FldArea="divHead",Title="备    注"}},
				{"work_brief",new DataItem(){FldArea="divHead",Hidden=true, Title="工作说明"}},

                //{"useSmallUnit",new DataItem(){FldArea="divHead",Title="小单位录入",CtrlType="jqxCheckBox"}},
                {"defaultUnit",new DataItem(){Title="默认单位",FldArea="divHead", ButtonUsage="list", Source = "[{v:'b',l:'大'},{v:'s',l:'小'},{v:'m',l:'中'}]"}},

                 {"sup_addr",new DataItem(){Title="客户地址", Hidden=true, FldArea="divHead"}},
                 {"receive_addr",new DataItem(){FldArea="divHead",Width="390",Hidden=true,KeepNoValueLabel=true, Title="收货地址",LabelFld="receive_addr_desc",ButtonUsage="list",
                     SqlForOptions="select addr_id as v,addr_desc as l from info_client_address where company_id=~COMPANY_ID and client_id=~CONDI_DATA_ITEM order by addr_order",CONDI_DATA_ITEM="supcust_id"}},
                 {"supcust_no",new DataItem(){Title="客户编号", Hidden=true, FldArea="divHead"}},
                {"boss_name",new DataItem(){Title="老板姓名", CtrlType="hidden", FldArea="divHead"}},
                {"mobile",new DataItem(){Title="客户电话", Hidden=true, FldArea="divHead"}},
                {"seller_mobile",new DataItem(){Title="业务电话", CtrlType="hidden", FldArea="divHead"}},
                {"total_quantity",new DataItem(){FldArea="divTail", Hidden=true, Title="合计数量",Width="80"}},

                {"total_amount",new DataItem(){FldArea="divTail", Hidden=true, Title="合计金额",Width="80",Value="0"}},
                {"now_disc_amount",new DataItem(){FldArea="divTail",Title="优惠金额",Width="80",Value="0"}},
                {"no_disc_amount",new DataItem(){FldArea="divTail",Title="惠后合计",Width="80"}},
                {"payway1_id",new DataItem(){FldArea="divTail",HideGroup="payway1", HideClass="payway",Title="",LabelFld="payway1_name", InnerTitle="支付方式1", ClassName="itemLeft",PlaceHolder="支付方式",ButtonUsage="list",SqlForOptions=CommonTool.selectPayWay,Width="80",GetOptionsOnLoad=true,FirstOptionAsDefault=true}},
                {"payway1_type",new DataItem(){FldArea = "divTail", Title = "", InnerTitle = "支付类型1", Hidden = true, HideOnLoad = true, Width = "80"}},
                {"payway1_amount",new DataItem(){FldArea="divTail",HideGroup="payway1",HideClass="payway", Title="",ClassName="itemRight",InnerTitle="支付金额1", PlaceHolder="支付金额",Width="80"}},
                {"payway2_id",new DataItem(){FldArea="divTail",HideGroup="payway2",HideClass="payway", Title="",LabelFld="payway2_name",Hidden=true,InnerTitle="支付方式2",ClassName="titleItem",PlaceHolder="支付方式", ButtonUsage="list",SqlForOptions=CommonTool.selectPayWay,Width="80",GetOptionsOnLoad=true}},
                {"payway2_type",new DataItem(){FldArea = "divTail", Title = "", InnerTitle = "支付类型2", Hidden = true, HideOnLoad = true, Width = "80"}},
                {"payway2_amount",new DataItem(){FldArea="divTail",HideGroup="payway2",HideClass="payway", Title="",InnerTitle="支付金额2",Hidden=true, PlaceHolder="支付金额",Width="80"}},
                {"payway3_id",new DataItem(){FldArea="divTail",HideGroup="payway3",HideClass="payway", Title="",LabelFld="payway3_name",Hidden=true,InnerTitle="支付方式3",ClassName="titleItem",PlaceHolder="支付方式", ButtonUsage="list",SqlForOptions=CommonTool.selectPayWay,Width="80",GetOptionsOnLoad=true}},
                {"payway3_type",new DataItem(){FldArea = "divTail", Title = "", InnerTitle = "支付类型3", Hidden = true, HideOnLoad = true, Width = "80"}},
                {"payway3_amount",new DataItem(){FldArea="divTail",HideGroup="payway3",HideClass="payway", Title="",InnerTitle="支付金额3",Hidden=true, PlaceHolder="支付金额",Width="80"}},
                {"left_amount",new DataItem(){FldArea="divTail",Title="欠款",Width="80",Disabled=true}},
                {"total_weight",new DataItem(){FldArea="divTail",Title="重量(kg)", Width="80",Disabled=true,Hidden=true}},
                {"total_volume",new DataItem(){FldArea="divTail",Title="体积(m³)", Width="80",Disabled=true,Hidden=true}},
                {"maker_id",new DataItem(){UseJQWidgets=false, Title="制单人", Width="80",FldArea="divHead",CtrlType="hidden",}},
                {"maker_name",new DataItem(){UseJQWidgets=false, Title="制单人", Width="80"}},
                {"make_time",new DataItem(){UseJQWidgets=false, Title="审核时间"}},
                {"approver_id",new DataItem(){UseJQWidgets=false, Title="审核人", Width="80"}},
                {"approver_name",new DataItem(){UseJQWidgets=false, Title="审核人", Width="80"}},
                {"review_time",new DataItem(){UseJQWidgets=false, Title="复核时间"}},
                {"reviewer_name",new DataItem(){UseJQWidgets=false, Title="复核人", Width="80"}},
                {"visit_id",new DataItem(){FldArea="divTail",Title="拜访id",Width="80",Hidden=true, HideOnLoad=true}},

                {"approve_time",new DataItem(){UseJQWidgets=false, Title="审核时间"}},
                {"red_sheet_id",new DataItem(){Title="red_sheet_id", CtrlType="hidden", FldArea="divHead"}},
                //{"red_sheet_no",new DataItem(){UseJQWidgets=false, Title="red_sheet_no"}},
                {"sheet_id_red_me",new DataItem(){Title="sheet_id_red_me", CtrlType="hidden", FldArea="divHead"}},
                {"prepay_sub_ids",new DataItem(){Title="prepay_sub_ids", CtrlType="hidden", FldArea="divHead"}},
                {"appendix_photos",new DataItem(){Title="appendix_photos", CtrlType="hidden", FldArea="divHead"}},
                { "acct_type",new DataItem(){FldArea="divTail",Hidden=true,HideOnLoad=true}},
                { "wx_user_id",new DataItem(){FldArea="divTail",Hidden=true,HideOnLoad=true}},
                { "sheet_attr_fulldiscs",new DataItem(){FldArea="divTail",Hidden=true,HideOnLoad=true}},
                { "sheet_attr_redpacket",new DataItem(){FldArea="divTail",Hidden=true,HideOnLoad=true}},
                { "is_del",new DataItem(){FldArea="divTail",Hidden=true,HideOnLoad=true}},
                //{ "parent_sheet_id",new DataItem(){FldArea="divTail",Hidden=true,HideOnLoad=true}},
                // 会员积分
                { "relatedToVipPoint",new DataItem() { FldArea="divHead",Title="是会员积分单",Hidden=true,Disabled=true } },
                { "vipPointPendMap",new DataItem() { FldArea="divHead",Title="会员积分占用信息",Hidden=true,Disabled=true } },
               // {"branch_state",new DataItem(){title="状态",labelFld="branch_status_name",labelInDB=false,value="1",label="正常", buttonUsage="list", source = "[{v:1,l:'正常'},{v:0,l:'停用'}]"}}
            };

           // m_idFld = "sheet_id"; 
           // m_tableName = "sheet_item_master";
          //  m_selectFromSQL = "from sheet_item_master sht left join info_supcust on sht.supcust_id=info_supcust.supcust_id left join info_branch on sht.branch_id=info_branch.brand_id left join (select oper_id,oper_name as order_man_name from info_operator) tb_order_man on sht.order_man=tb_order_man.oper_id where sheet_id='~ID'";
            /*
            Grids = new Dictionary<string, FormDataGrid>()
            {
                {"gridUnit" ,new FormDataGrid(){
                   Columns = new Dictionary<string, DataItem>()
                   {
                       //{"unit_no",new DataItem(){title="单位",width="100",url="../api/ItemEdit/GetUnits"}},
                       {"unit_no",new DataItem(){title="单位",width="100",SqlForOptions="select unit_no from info_item_unit",getOptionsOnLoad=true,buttonUsage="list"}},
                       {"unit_factor",new DataItem(){title="包装率",width="80"}},
                   },
                   TableName="info_item_multi_unit",
                   IdFld="item_no",
                   SelectFromSQL="from info_item_multi_unit where item_no='~ID'"
                }}
            };*/
        }

        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            
            dynamic setting = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonCompanySetting);
            dynamic operRights = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonOperRightsOrig);
            
            if (setting != null)
            {
                /*if (setting.saleSheetDefaultUnit != null)
                {
                    DataItems["defaultUnit"].Value = setting.saleSheetDefaultUnit;
                    DataItems["defaultUnit"].Label = setting.saleSheetDefaultUnit == "s" ? "小" : setting.saleSheetDefaultUnit == "b" ? "大" : setting.saleSheetDefaultUnit == "m" ? "中" : "";
                }*/

                if (setting.saleSheetDefaultUnit != null)
                {
                    DataItems["defaultUnit"].Value = setting.saleSheetDefaultUnit;
                    DataItems["defaultUnit"].Label = setting.saleSheetDefaultUnit == "s" ? "小" : setting.saleSheetDefaultUnit == "b" ? "大" : setting.saleSheetDefaultUnit == "m" ? "中" : "";
                    //DataItems["defaultUnit"].ButtonUsage = "none";
                    //DataItems["defaultUnit"].Disabled = true;
                }

                if (setting.openTicketAccessSys!=null && setting.openTicketAccessSys.ToString().ToLower() == "true")
                {
                    EnablePiaoZhengTong = true;
                }

                if (setting.orderBranch != null && DataItems["sheet_id"].Value =="")
                {
                    if (operRights != null && operRights.delicacy != null && (operRights.delicacy.saleOrderDefaultBranch == null|| ((string)operRights.delicacy.saleOrderDefaultBranch.value).ToLower() != "lasttime")) 
                        DataItems["branch_id"].InitValue = (string)setting.orderBranch;                    
                }
                if (setting.branchForReturn != null && DataItems["sheet_id"].Value =="" && (DataItems["sheetType"].Value=="T"|| DataItems["sheetType"].Value == "TD"))
                {
                    DataItems["branch_id"].InitValue = (string)setting.branchForReturn;                    
                }
                if (setting.branchForOrderReturn != null && DataItems["sheet_id"].Value =="" && (DataItems["sheetType"].Value=="T"|| DataItems["sheetType"].Value == "TD"))
                {
                    DataItems["branch_id"].InitValue = (string)setting.branchForOrderReturn;                    
                }
                
            }

            
            if (!string.IsNullOrEmpty(DataItems["defaultUnit"].Value))
            {
                DataItems["defaultUnit"].Label = DataItems["defaultUnit"].Value == "s" ? "小" : DataItems["defaultUnit"].Value == "b" ? "大" : DataItems["defaultUnit"].Value == "m" ? "中" : "";
            }
            else if (setting != null)
            {
                if (setting.saleSheetDefaultUnit != null)
                {
                    DataItems["defaultUnit"].Value = setting.saleSheetDefaultUnit;
                    DataItems["defaultUnit"].Label = setting.saleSheetDefaultUnit == "s" ? "小" : setting.saleSheetDefaultUnit == "b" ? "大" : setting.saleSheetDefaultUnit == "m" ? "中" : "";
                }
            }

            string sqlPrepay = $"select string_agg(sub_id::text,',') sub_ids from cw_subject where company_id = {company_id} and sub_type in ('YS') and not coalesce(is_order,false)";
            dynamic recordPrepay = await CDbDealer.Get1RecordFromSQLAsync(sqlPrepay, cmd);
            if (recordPrepay != null) DataItems["prepay_sub_ids"].Value = recordPrepay.sub_ids;       
        }
        public async Task OnGet(bool forReturn)
        {
            SheetSaleOrder sheet = new SheetSaleOrder(forReturn?SHEET_RETURN.IS_RETURN:SHEET_RETURN.NOT_RETURN, LOAD_PURPOSE.SHOW);
            if (forReturn)
            {
                PageMenuID = MenuId.sheetReturnOrder;
            }
            await InitGet(cmd,sheet);

            //string items_id = "";
            //foreach (SheetRowItem sheetRow in sheet.SheetRows)
            //{
            //    if (items_id != "") items_id += ","; items_id += sheetRow.item_id;
            //}
            //await GetItemsInfoBySheetInfo(this.company_id, items_id, new { sheet.supcust_id, sheet.branch_id,branch_position="0"});
            //sheet = (SheetSale)this.Sheet;
            List<dynamic> sheetRows = JsonConvert.DeserializeObject<List<dynamic>>(JsonConvert.SerializeObject(sheet.SheetRows));
            await GetItemsInfoBySheetRowsInfo(this.company_id, sheetRows, new {  branch_id=sheet.branch_id, supcust_id = sheet.supcust_id,order_source=sheet.order_source });
             
            SheetTitle = sheet.sheet_type==SHEET_TYPE.SHEET_SALE_DD_RETURN ? "退货订单" : "销售订单";
        }
        public override async Task<JsonResult> GetItemsInfo(string companyID, string items_id, string searchStr, bool bGetAttrs, dynamic otherInfo)
        {
             
            return await SaleSheetModel.GetItemsInfo_static(cmd,companyID, items_id, searchStr, bGetAttrs, otherInfo);
        }
       /* public   async Task<JsonResult> GetItemsInfo_oldold(string companyID, string items_id, bool bGetAttrs, dynamic otherInfo)
        {
            string supcust_id = otherInfo.supcust_id;
            string branch_id = otherInfo.branch_id;

            if (supcust_id.IsInvalid()) supcust_id = "-1";
            if (branch_id.IsInvalid()) branch_id = "-1";
            var orderBy = "";
            if (items_id != "")
            {
                orderBy = $" order by position(mu.item_id::text in '{items_id}')";
            }
            else
                orderBy = $" order by mu.item_id desc";

            SQLQueue QQ = new SQLQueue(cmd);
            string sql = @$"
                            select mu.item_id,ip.item_name,mu.unit_no,other_class,ip.mum_attributes,itbs.produce_date as produce_date,batch_no,(case when rp.is_recent then rp.unit_no end) recent_unit_no,mu.retail_price,s_retail_price,unit_factor,unit_type,stock.stock_qty,ip.batch_level,oldest_stock_qty,
                                    unit_from_s_to_bms ((oldest_stock_qty::numeric),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) oldest_stock_qty_unit,
                                    unit_from_s_to_bms ((stock.stock_qty::numeric),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) stock_qty_unit,
                                    unit_from_s_to_bms ((stock.sell_pend_qty::numeric),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) sell_pend_qty_unit,
                                    unit_from_s_to_bms ((stock.usable_stock_qty::numeric),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) usable_stock_qty_unit,
                                    mu.barcode,mu.wholesale_price,mu.buy_price,ip.cost_price_avg,recent_price,coalesce(recent_orig_price,mu.wholesale_price)  recent_orig_price,item_spec,valid_days
                            from info_item_multi_unit mu
                            left join 
                            (
                                select item_id,(b->>'f1')::real as b_unit_factor,(m->>'f1')::real as m_unit_factor,(s->>'f1')::real as s_unit_factor,
                                                                                     b->>'f2' as b_unit_no,m->>'f2' as m_unit_no,s->>'f2' as s_unit_no,
                                                                                     s->>'f3' as s_retail_price 
                                from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,retail_price)) as json from info_item_multi_unit where company_id={companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s jsonb,m jsonb,b jsonb)
                            ) mu1 on mu.item_id = mu1.item_id
                            left join company_setting s on s.company_id = mu.company_id
                            left join (select item_id,produce_date from item_recent_produce_date where company_id={companyID}) rpd on mu.item_id=rpd.item_id
                            left join (select item_id,sum(COALESCE(stock_qty,0)) stock_qty,sum(COALESCE(sell_pend_qty,0)) sell_pend_qty, sum(COALESCE(stock_qty,0))-sum(COALESCE(sell_pend_qty,0)) usable_stock_qty from stock where company_id = {companyID} and branch_id={branch_id}  group by item_id,branch_id) stock on stock.item_id = mu.item_id
left join (select * from client_recent_prices where company_id = {companyID} and supcust_id = {supcust_id}) rp on rp.item_id = mu.item_id and rp.unit_no = mu.unit_no
                            left join info_item_prop ip on mu.item_id=ip.item_id  
left join (select item_id,sum(stock_qty) as oldest_stock_qty,min(produce_date) as produce_date,min(batch_no) as batch_no from stock s left join info_item_batch itb on itb.company_id = {companyID} and itb.batch_id = s.batch_id where s.company_id = {companyID} and s.branch_id={branch_id} and item_id in ({items_id}) and stock_qty >0 and s.batch_id <> 0 group by item_id) itbs on itbs.item_id = mu.item_id
                            where mu.company_id={companyID} and mu.item_id in ({items_id}) {orderBy};";
            QQ.Enqueue("items", sql);
            sql = @$" 
                    SELECT array_to_json(array_agg(row_to_json(t))) attr_options FROM
                    (
                        SELECT opt_id, opt_name, attr.attr_id FROM info_attr_opt opt left join info_attribute attr on opt.attr_id = attr.attr_id where opt.company_id ={ companyID} and not attr.spec_opt_in_item order by opt.order_index
                    ) t";
            QQ.Enqueue("attr_options", sql);

            if (bGetAttrs)
            {
                sql = $"select attr_id,attr_name,distinct_stock,order_index from info_attribute where company_id={companyID} order by order_index";
                QQ.Enqueue("attrs", sql);
            }

            List<ExpandoObject> units = null;
            List<ExpandoObject> attributes = null;
            dynamic attrOptions = null;
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                string tbl = QQ.Dequeue();
                if (tbl == "items")
                {
                    units = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (tbl == "attr_options")
                {
                    dr.Read();
                    string s = CPubVars.GetTextFromDr(dr, "attr_options");
                    if (s != "")
                    {
                        attrOptions = JsonConvert.DeserializeObject(s);
                    }
                }
                else if (tbl == "attrs")
                {
                    attributes = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();


            Dictionary<string, dynamic> items = new Dictionary<string, dynamic>();
            #region 获取价格策略
            bool doPricePlan = false;
            Dictionary<string, string> planIDs = new Dictionary<string, string>();
            if (supcust_id != "-1")
            {
                sql = $"select region_id,other_region,sup_rank,sup_group from info_supcust where company_id = {companyID} and supcust_id={supcust_id}";
                dynamic supInfo = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                var groupID = supInfo.sup_group == "" ? "null" : supInfo.sup_group;
                var otherRegion = supInfo.other_region;
                var rankID = supInfo.sup_rank == "" ? "null" : supInfo.sup_rank;
                sql = $@" select null flow_id,supcust_id,null group_id,null region_id,null rank_id,price1,price2,price3 from price_strategy_client where company_id = {companyID} and supcust_id={supcust_id}
                                union
                          select flow_id,null supcust_id,group_id,region_id,rank_id,price1,price2,price3 from price_strategy_class  where company_id={companyID} and (position(concat('/',region_id,'/') in '{otherRegion}')>0   or region_id is null) and (group_id::text = '{groupID}' or group_id is null) and (rank_id is null or rank_id::text = '{rankID}') and not (region_id is null and group_id is null and rank_id is null)  order by supcust_id,flow_id desc";
                List<ExpandoObject> supPlans = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                foreach (dynamic plan in supPlans)
                {
                    if (plan.supcust_id != "") doPricePlan = true;
                    else if (plan.supcust_id == "" && plan.flow_id != "") doPricePlan = true;

                    if (doPricePlan)
                    {
                        if (plan.price1 != "") planIDs.Add("1", plan.price1);
                        if (plan.price2 != "") planIDs.Add("2", plan.price2);
                        if (plan.price3 != "") planIDs.Add("3", plan.price3);
                        break;


                    }
                }
            }
            #endregion
            #region 使用价格策略
            Dictionary<string, dynamic> prices = new Dictionary<string, dynamic>();
            if (doPricePlan && planIDs.Count > 0)
            {
                string priceSql = "";
                var fld = ""; var orderCondi = "";
                foreach (var p in planIDs)
                {
                    if (priceSql != "") priceSql += " union ";
                    if (p.Key != null && p.Key != "")
                    {
                        fld = $"{ p.Key} as priority,'{p.Value}' plan_id,";
                        orderCondi = $" order by priority,price_item ";
                    }
                    // 查找不同方案的价格
                    priceSql += @$"select p.item_id,pi.item_id price_item,{fld}pi.class_id,s_price,m_price,b_price,class_discount
                                    from info_item_prop p 
                                    left join 
                                    (select null item_id,null s_price,null m_price,null b_price,class_id,discount class_discount from price_plan_class where plan_id::text = '{p.Value}' and company_id = {companyID} and class_id!= null
                                         union
                                     select item_id,s_price,m_price,b_price,null class_id,null class_discount from price_plan_item where company_id = {companyID} and plan_id::text = '{p.Value}' and item_id in ({items_id})
                                    ) pi on (pi.item_id = p.item_id or pi.item_id is null) and (position(concat('/',pi.class_id,'/') in other_class)>0 or pi.class_id is null)
                                where p.company_id = {companyID} and p.item_id in ({items_id}) ";

                }
                priceSql += orderCondi;
                List<ExpandoObject> plans = await CDbDealer.GetRecordsFromSQLAsync(priceSql, cmd);
                #region 修改结果集  { priority_item_id, { priority, plan_id, item_id, prSetting{price_item,class_id.....}}} ，得到prices

                foreach (dynamic plan in plans)
                {
                    dynamic pr = null;
                    if (prices.ContainsKey(plan.priority + '_' + plan.item_id)) pr = prices[plan.priority + '_' + plan.item_id];
                    else
                    {
                        pr = new ExpandoObject();
                        pr.priority = plan.priority;
                        pr.plan_id = plan.plan_id;
                        pr.item_id = plan.item_id;
                        prices.Add((string)plan.priority + '_' + plan.item_id, pr);
                        pr.priceSetting = new List<dynamic>();
                    }
                    List<dynamic> prSetting = pr.priceSetting;
                    if (plan.price_item != "" || plan.class_id != "") prSetting.Add(new { plan.price_item, plan.class_id, plan.class_discount, plan.s_price, plan.m_price, plan.b_price });
                }
                #endregion
            }
            #endregion

            #region 调价单的影响 --- 找出需要使用调价的方案的商品
            dynamic adjustItems = null;
            string adjustPlan = "";

            if (planIDs.Count > 1 && planIDs["1"] == "recent") adjustPlan = planIDs["2"]; // 对于首选方案为最近售价的价格策略  
            // 首选无价格且下一方案为最近售价 比如：  批发价 - 最近售价 - 方案一
            if (planIDs.Count > 2 && planIDs["1"] != "recent" && planIDs["2"] == "recent" && planIDs["3"] != "recent") adjustPlan = planIDs["3"];

            if (adjustPlan != "")
            {
                if (adjustPlan == "wholesale") adjustPlan = "-1";
                if (adjustPlan == "retail") adjustPlan = "0";
                if (adjustPlan == "recent") adjustPlan = "-2";

                //需要比较 client_recent_prices 表中 happen_time 与 item_price_adjust 中 adjust_time 的时间早晚
                string selAdjustSql = @$"select string_agg(a.item_id::text,',') items_id from item_price_adjust a left join client_recent_prices r on r.item_id = a.item_id
                                         where a.company_id = {companyID} and r.supcust_id = {supcust_id} and a.plan_id = {adjustPlan} and a.adjust_time>r.happen_time and a.item_id in ({items_id}); ";
                adjustItems = await CDbDealer.Get1RecordFromSQLAsync(selAdjustSql, cmd); // 找出需要使用 新价格的 商品
            }
            #endregion

            foreach (dynamic unit in units)
            {
                if (unit.unit_factor == "") continue;
                dynamic item = null;
                if (items.ContainsKey(unit.item_id))
                {
                    item = items[unit.item_id];
                }
                else
                {
                    item = new ExpandoObject();
                    item.item_id = unit.item_id;
                    item.item_name = unit.item_name;
                    item.item_spec = unit.item_spec;
                    item.valid_days = unit.valid_days;
                    item.produce_date = unit.produce_date;
                    item.stock_qty = unit.stock_qty.Replace("-0", "0");
                    item.stock_qty_unit = (unit.stock_qty_unit).Replace("-0", "0");
                    item.sell_pend_qty_unit = (unit.sell_pend_qty_unit).Replace("-0", "0");
                    item.batch_level = unit.batch_level;
                    item.usable_stock_qty_unit = (unit.usable_stock_qty_unit).Replace("-0", "0");
                    item.recent_unit_no = unit.recent_unit_no;
                    item.mum_attributes = unit.mum_attributes;
                    item.oldest_stock_qty = unit.oldest_stock_qty.Replace("-0", "0");
                    item.oldest_stock_qty_unit = (unit.oldest_stock_qty).Replace("-0", "0");
                    item.produce_date = unit.produce_date;
                    item.batch_no = unit.batch_no;
                    items.Add((string)item.item_id, item);
                    item.units = new List<dynamic>();
                }
                #region 使用价格策略
                string planPrice = "";
                foreach (var p in prices)
                {
                    if (unit.item_id == p.Value.item_id)
                    {
                        var plan = p.Value.plan_id;
                        if (plan == "") continue;
                        else if (plan == "wholesale") { planPrice = unit.wholesale_price; if (planPrice != "") break; }
                        else if (plan == "recent")
                        {
                            // 最近售价 - 批发价
                            if (adjustItems != null && adjustItems.items_id.Contains(unit.item_id)) // 如果需要改价，则取下一次的方案价格
                                continue;
                            planPrice = unit.recent_price;
                            if (planPrice != "") break;
                        }
                        else if (plan == "retail") { planPrice = unit.retail_price; if (planPrice != "") break; }
                        var otherClass = unit.other_class;
                        var selectClass = ""; var selectDisc = ""; var classIndex = -1;
                        foreach (dynamic s in p.Value.priceSetting)
                        {
                            if (s.price_item != "")
                            {
                                if (unit.unit_type == "s") planPrice = s.s_price;
                                if (unit.unit_type == "m") planPrice = s.m_price;
                                if (unit.unit_type == "b") planPrice = s.b_price;
                                if (planPrice != "") break;
                            }
                            else if (s.class_id != "")
                            {
                                var classArr = otherClass.split(otherClass, '/');
                                if (Array.IndexOf(classArr, s.class_id) > classIndex)
                                {
                                    classIndex = Array.IndexOf(classArr, s.class_id);
                                    selectClass = s.class_id;
                                    selectDisc = s.class_discount;
                                }
                            }
                            if (planPrice != "") break;
                        }
                        if (selectDisc != "" && unit.wholesale_price != "") planPrice = Convert.ToSingle(selectDisc) * Convert.ToSingle(unit.wholesale_price);
                        if (planPrice != "") break;
                    }
                }

                #endregion
                item.s_retail_price = unit.s_retail_price;
                List<dynamic> itemUnits = item.units;
                string price = "";
                if (planPrice != "") price = planPrice;
                else if (unit.recent_price != "") price = unit.recent_price;
                else if (unit.wholesale_price != "") price = unit.wholesale_price;
                itemUnits.Add(new { unit.unit_no, unit.unit_factor, unit.unit_type, price, unit.recent_orig_price, unit.recent_price, unit.wholesale_price, unit.retail_price, unit.barcode, unit.buy_price, unit.cost_price_avg });
            }
            return new JsonResult(new { result = "OK", items, attrOptions, attributes });

        }
     */
        public override async Task<JsonResult> GetItemsInfoBySheetRows(string companyID, bool bGetAttrs, List<dynamic> sheetRows,dynamic otherInfo)
        {
             return await SaleSheetModel.GetItemsInfoBySheetRows_static(cmd,companyID, bGetAttrs,sheetRows,otherInfo);
        }

    }
    [ApiController]
    [Route("api/[controller]/[action]")]
    public class SaleOrderSheetController : BaseController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        public SaleOrderSheetController(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }

        [HttpGet]
        public async Task<JsonResult> GetDataItemOptions(string operKey,string dataItemName, string flds, string value, string availValues, string condiDataItem)
        {
            var model = new SaleOrderSheetModel(cmd);
            return await PageBaseModel.GetDataItemOptionsJson(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues,condiDataItem);

        }
        /*
        [HttpGet]
        public async Task<IActionResult> GetItems(string operKey, string query)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            //var model = new SaleSheetModel(cmd);
            // string sql = $"select  distinct item_name as name,i.item_id as id,py_str as zjm,u.barcode as code from info_item_prop i left join info_item_multi_unit u on i.item_id=u.item_id where i.company_id={companyID} and u.company_id={companyID} and (item_name ilike '%{query}%' or py_str ilike '%{query}%' or u.barcode ilike '%{query}%' or item_name ilike '%{query}%') limit 30";
            string sql = $"select distinct item_name as name,i.item_id as id,py_str as zjm,string_agg(u.barcode,',') as code,item_no from info_item_prop i left join info_item_multi_unit u on i.item_id=u.item_id where i.company_id={companyID} and u.company_id={companyID} and (item_name ilike '%{query}%' or py_str ilike '%{query}%' or u.barcode ilike '%{query}%' or item_no ilike '%{query}%') and (i.status is null or i.status='1') group by item_name,i.item_id,py_str,item_no limit 30";

            dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            return new JsonResult(new {result="OK", records });
        }*/
        [HttpGet]
        public async Task<IActionResult> GetItems(string operKey, string sheetType, bool canSeePrice, string query, string supcust_id, string branch_id, string showSonItems)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            if (string.IsNullOrEmpty(branch_id))
            {
                branch_id = "0";
            }
            if (string.IsNullOrEmpty(supcust_id))
            {
                supcust_id = "0";
            }
            string priceFields = "";
            string priceLeftJoin = "";
            if (canSeePrice)
            {
                string recent_price_tb = "client_recent_prices";
                if (sheetType == "CG" || sheetType == "CT")
                {
                    recent_price_tb = "supplier_recent_prices";
                }
                priceLeftJoin = $@"left join {recent_price_tb}  cp on  cp.company_id={companyID} and  cp.supcust_id={supcust_id} and i.item_id =  cp.item_id and u.s_unit_no= cp.unit_no
left join {recent_price_tb} cpb on cpb.company_id={companyID} and cpb.supcust_id={supcust_id} and i.item_id = cpb.item_id and u.b_unit_no=cpb.unit_no
";
                priceFields = @" case when  cp.recent_price is not null then  cp.recent_price::text else u.s_wholesale_price end as s_price,
       case when cpb.recent_price is not null then cpb.recent_price::text else u.b_wholesale_price end as b_price,";
                if (sheetType == "CG" || sheetType == "CT")
                {
                    priceFields = priceFields.Replace("_wholesale_price", "_buy_price");
                }
            }

            query = query.Replace("'", "");
            string son_items_condi = "";
            if (showSonItems == "1")
            {
                son_items_condi = " and (mum_attributes is null or mum_attributes::text not like '%\"distinctStock\": true%')";
            }
            else
            {
                son_items_condi = " and son_mum_item is null";
            }
            string barcodeRight = "";
            if (query.Length >= 6)
                barcodeRight = "";

            string brcodeCondi = "";
            if (query.Length >= 3 && CPubVars.IsNumeric(query))
            {
                brcodeCondi = $" or u.s_barcode like '%{query}{barcodeRight}' or u.b_barcode like '%{query}{barcodeRight}' or u.m_barcode like '%{query}{barcodeRight}' or i.mum_attributes::text ilike '%{query}%'";

            }
            string flexStr = CPubVars.GetFlexLikeStr(query);

            string py_flexStr = CPubVars.GetPyStrFlexLikeStr(query);
            //if (query.Length >= 3) py_flexStr = flexStr;

            string sql = @$" 
select item_name as name,i.item_id as id,py_str as zjm,item_spec,mum_attributes,
      {priceFields}
       yj_get_bms_qty(stock_qty, b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no) as stock, concat(s_barcode,' ',b_barcode,' ',m_barcode) as code,item_no 
from info_item_prop i
left join stock s on i.item_id=s.item_id and s.company_id={companyID} and branch_id={branch_id}
left join 
(
    select item_id,
           s->>'f1' as s_unit_no,s->>'f3' as s_barcode, s->>'f4' as s_wholesale_price,s->>'f4' as s_buy_price,  
           m->>'f1' as m_unit_no,m->>'f3' as m_barcode, m->>'f4' as m_wholesale_price,m->>'f4' as m_buy_price,  (m->>'f2')::numeric as m_unit_factor,
           b->>'f1' as b_unit_no,b->>'f3' as b_barcode, b->>'f4' as b_wholesale_price,b->>'f4' as b_buy_price,  (b->>'f2')::numeric as b_unit_factor
 
    from crosstab('
         select item_id,unit_type,row_to_json(row(unit_no,unit_factor,barcode,wholesale_price)) as json 
         from info_item_multi_unit where company_id ={companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$)  as errr(item_id int, s jsonb,m jsonb, b jsonb)
) u 
on i.item_id=u.item_id
{priceLeftJoin}
where i.company_id={companyID} and (item_name ilike '%{flexStr}%' or py_str ilike '%{py_flexStr}%'  or item_no ilike '%{query}%' {brcodeCondi}) and COALESCE(i.status,'1')='1' {son_items_condi} order by case when stock_qty>0 then 0 else 1 end,item_name limit 100 ";

            dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            return new JsonResult(new { result = "OK", records });
        }

        [HttpGet]
        public async Task<IActionResult> GetItemInfo(string operKey, string item_id)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            // var model = new SaleSheetModel();
            string sql = $"select unit_no,unit_factor,wholesale_price as price from info_item_multi_unit where company_id={companyID} and item_id={item_id}";

            dynamic units = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            //string items=Newtonsoft.Json.JsonConvert.SerializeObject(rec); 

            return new JsonResult(new { result = "OK",item = new {item_id,units} });
        }

        [HttpGet]
        public async Task<IActionResult> GetUnits(string operKey, string item_id, string query)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string sql = $"select  distinct unit_no,unit_factor from info_item_multi_unit where company_id={companyID} and item_id={item_id} and unit_no like '%{query}%' limit 30";

            dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            //string items=Newtonsoft.Json.JsonConvert.SerializeObject(rec); 
            return new JsonResult(new { result = "OK", records });
        }

        [HttpGet]
        public async Task<IActionResult> GetItemsInfo(string operKey, string items_id, string searchStr, string supcust_id, string branch_id,string branch_position,bool bGetAttrs,string branchPositionType,bool isShowNegativeStock,bool needQueryPosition,string queryPosition)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            SaleSheetModel sheetModel = new SaleSheetModel(cmd);
            if (branch_position.IsInvalid()) branch_position = "0";
            var rr = await sheetModel.GetItemsInfo(companyID, items_id, searchStr, bGetAttrs, new { supcust_id, branch_id,branch_position,branchPositionType,isShowNegativeStock,needQueryPosition,queryPosition });
            return rr;
        }

        public class test {
            public string id = "", name = "", tt="";
        }
        [HttpGet]
        public async Task<IActionResult> SetBranchAndSellerOnSupcustUpdate(string operKey, string supcust_id)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string sql = $@"select ssom.branch_id,branch_name,ssom.seller_id,oper_name seller_name from sheet_sale_order_main ssom
                left join info_branch ib on ssom.branch_id=ib.branch_id and ssom.company_id=ib.company_id
                left join info_operator io on ssom.seller_id=io.oper_id and ssom.company_id=io.company_id
                where ssom.company_id={companyID} and supcust_id={supcust_id} and sheet_type='XD' order by make_time desc limit 1";
            List<ExpandoObject> branchAndSellerInfo = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            return new JsonResult(new { result = "OK", data = branchAndSellerInfo });
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic dSheet)  //[FromBody] dynamic sheet)
        {
            string msg = "";
            SheetSaleOrder sheet = null;
            try
            {
                sheet = CPubVars.FromJsonObj<SheetSaleOrder>(dSheet);
            }
            catch (Exception e)
            {
                string logMsg = "保存失败,请联系技术支持" + e.Message + "in SaleOrderSheet.cshtml.SaveAndApprove:" + JsonConvert.SerializeObject(dSheet);
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error(logMsg);
                MyLogger.LogMsg(logMsg,Token.CompanyID);
                msg = "审核失败,请联系技术支持";
                return new JsonResult(new { result = "Error", msg });
            }

           
            sheet.Init();
            msg = await sheet.Save(cmd);
            string result = msg== "" ? "OK": "Error";
            return new JsonResult(new { result, msg,sheet.sheet_id,sheet.sheet_no});
        }



        [HttpPost]
        public async Task<IActionResult> checkIsNeedDivideSheet([FromBody] dynamic dSheet)
        {
            string msg = "";
            SheetSaleOrder sheet = null;
            try
            {
                sheet = CPubVars.FromJsonObj<SheetSaleOrder>(dSheet);
            }
            catch (Exception e)
            {
                string logMsg = "保存失败,请联系技术支持" + e.Message + "in SaleOrderSheet.cshtml.SaveAndApprove:" + JsonConvert.SerializeObject(dSheet);
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error(logMsg);
                MyLogger.LogMsg(logMsg,Token.CompanyID);
                msg = "审核失败,请联系技术支持";
                return new JsonResult(new { result = "Error", msg });
            }
            sheet.Init();

            try
            {
                // 生成分单方案
                var divideResult = await GenerateDivideSheetPlan(sheet, cmd);

                // 检查是否有错误信息（库存不足）
                if (!string.IsNullOrEmpty(divideResult.errorMsg))
                {
                    return new JsonResult(new {
                        result = "Error",
                        msg = divideResult.errorMsg
                    });
                }

                if (divideResult.needDivide)
                {
                    // 先保存原单据,逻辑删除原单据
                    sheet.is_del = true; 
                    string originalSaveResult = await sheet.Save(cmd, false);
                    if (originalSaveResult != "")
                    {
                        return new JsonResult(new { result = "Error", msg = $"保存原单据失败: {originalSaveResult}" });
                    }

                    // 执行分单方案，传入已保存的原单据ID
                    var executeResult = await ExecuteDivideSheetPlan(sheet, divideResult.divideSheets, cmd);
                    if (executeResult.result == "Error")
                    {
                        return new JsonResult(new { result = "Error", msg = executeResult.msg });
                    }

                    return new JsonResult(new {
                        result = "OK",
                        needDivide = true,
                        msg = "订单已按库存情况自动分单",
                        sheet_id = sheet.sheet_id,
                        sheet_no = sheet.sheet_no,
                        dividedSheetCount = divideResult.divideSheets.Count,
                        dividedSheetNos = executeResult.sheetNos
                    });
                }
                else
                {
                    return new JsonResult(new {
                        result = "OK",
                        needDivide = false,
                        msg = ""
                    });
                }
            }
            catch (Exception e)
            {
                string logMsg = "分单处理失败:" + e.Message + " StackTrace:" + e.StackTrace;
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error(logMsg);
                MyLogger.LogMsg(logMsg, Token.CompanyID);
                return new JsonResult(new { result = "Error", msg = "分单处理失败,请联系技术支持" });
            }
        }

        /// <summary>
        /// 生成分单方案
        /// </summary>
        private async Task<(bool needDivide, List<SheetSaleOrder> divideSheets, string errorMsg)> GenerateDivideSheetPlan(SheetSaleOrder originalSheet, CMySbCommand cmd)
        {
            // 统计每个商品所需数量
            Dictionary<string, decimal> itemQuantity = new Dictionary<string, decimal>();
            foreach (var row in originalSheet.SheetRows)
            {
                string itemId = row.item_id;
                decimal requiredQuantity = row.quantity * row.unit_factor;
                if (itemQuantity.ContainsKey(itemId))
                {
                    itemQuantity[itemId] += requiredQuantity;
                }
                else
                {
                    itemQuantity[itemId] = requiredQuantity;
                }
            }

            // 查询每个商品在各仓库的库存数量
            Dictionary<string, Dictionary<string, decimal>> stockQuantity = new Dictionary<string, Dictionary<string, decimal>>();
            Dictionary<string, string> itemName = new Dictionary<string, string>();
            foreach (var itemId in itemQuantity.Keys)
            {
                string sql = $@"
                SELECT item_name, s.item_id, branch_id, SUM(stock_qty) as total_stock
                FROM stock s
                    LEFT JOIN info_item_prop ip on ip.item_id = s.item_id
                WHERE s.company_id = {originalSheet.company_id}
                    AND s.item_id = {itemId}
                    AND stock_qty > 0
                GROUP BY item_name, s.item_id, s.branch_id
                ORDER BY total_stock ASC"; // 优先选择库存少的仓库

                var stockRecords = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);

                if (stockRecords.Count > 0)
                {
                    stockQuantity[itemId] = new Dictionary<string, decimal>();
                    foreach (dynamic record in stockRecords)
                    {
                        string branchId = record.branch_id.ToString();
                        decimal totalStock = Convert.ToDecimal(record.total_stock);
                        stockQuantity[itemId][branchId] = totalStock;

                    }
                }

                foreach (dynamic record in stockRecords)
                {
                    itemName[record.item_id]=record.item_name;
                }
            }

            // 分析是否需要分单并生成分配方案
            Dictionary<string, List<(string branchId, decimal quantity)>> itemBranchAllocation = new Dictionary<string, List<(string, decimal)>>();
            bool needDivide = false;

            foreach (var item in itemQuantity)
            {
                string itemId = item.Key;
                decimal requiredQty = item.Value;

                if (!stockQuantity.ContainsKey(itemId) || stockQuantity[itemId].Count == 0)
                {
                    // 无库存，分配到默认仓库
                    itemBranchAllocation[itemId] = new List<(string, decimal)> { ("", requiredQty) };
                    continue;
                }

                var itemStocks = stockQuantity[itemId].ToList();

                // // 检查所有仓库的库存总和是否能满足需求
                // decimal totalStock = itemStocks.Sum(x => x.Value);
                // if (totalStock < requiredQty)
                // {
                //     // 库存不足，返回错误信息
                //     return (false, new List<SheetSaleOrder>(), $"商品 {itemName[itemId]} 库存不足，需要 {requiredQty}，总库存仅有 {totalStock}");
                // }

                if (itemStocks.Count == 1)
                {
                    // 只在一个仓库有库存，直接分配
                    itemBranchAllocation[itemId] = new List<(string, decimal)> { (itemStocks[0].Key, requiredQty) };
                }
                else
                {
                    // 多个仓库有库存，需要分单
                    needDivide = true;
                    itemBranchAllocation[itemId] = new List<(string, decimal)>();

                    // 首先检查是否有单个仓库能满足全部需求
                    var maxStockBranch = itemStocks.OrderByDescending(x => x.Value).FirstOrDefault();
                    if (maxStockBranch.Value >= requiredQty)
                    {
                        // 优先选择库存少的仓库
                        var minStockBranch = itemStocks.OrderBy(x => x.Value).FirstOrDefault(x => x.Value >= requiredQty);
                        if (minStockBranch.Key != null)
                        {
                            itemBranchAllocation[itemId].Add((minStockBranch.Key, requiredQty));
                        }
                        else
                        {
                            // 如果没找到合适的，使用库存最多的仓库
                            itemBranchAllocation[itemId].Add((maxStockBranch.Key, requiredQty));
                        }
                    }
                    else
                    {
                        // 没有单个仓库能满足全部需求，按库存数从大到小依次选择仓库
                        var sortedStocks = itemStocks.OrderByDescending(x => x.Value).ToList();
                        decimal remainingQty = requiredQty;

                        foreach (var stock in sortedStocks)
                        {
                            if (remainingQty <= 0) break;

                            if (stock.Value >= remainingQty)
                            {
                                // 当前仓库库存足够满足剩余需求
                                itemBranchAllocation[itemId].Add((stock.Key, remainingQty));
                                remainingQty = 0;
                            }
                            else
                            {
                                // 当前仓库库存不足，全部分配
                                itemBranchAllocation[itemId].Add((stock.Key, stock.Value));
                                remainingQty -= stock.Value;
                            }
                        }

                        // 如果还有剩余数量（理论上不应该发生，但为了安全起见）
                        if (remainingQty > 0)
                        {
                            var maxStock = sortedStocks.First();
                            var existingAllocation = itemBranchAllocation[itemId].FirstOrDefault(x => x.branchId == maxStock.Key);
                            if (existingAllocation.branchId != null)
                            {
                                // 更新已有分配
                                var index = itemBranchAllocation[itemId].FindIndex(x => x.branchId == maxStock.Key);
                                itemBranchAllocation[itemId][index] = (maxStock.Key, existingAllocation.quantity + remainingQty);
                            }
                            else
                            {
                                itemBranchAllocation[itemId].Add((maxStock.Key, remainingQty));
                            }
                        }
                    }
                }
            }

            if (!needDivide)
            {
                return (false, new List<SheetSaleOrder>(), "");
            }

            // 生成分单方案
            return (true, await CreateDivideSheets(originalSheet, itemBranchAllocation), "");
        }

        /// <summary>
        /// 创建分单
        /// </summary>
        private async Task<List<SheetSaleOrder>> CreateDivideSheets(SheetSaleOrder originalSheet, Dictionary<string, List<(string branchId, decimal quantity)>> itemBranchAllocation)
        {
            // 按仓库分组
            Dictionary<string, List<SheetRowSaleOrder>> branchRows = new Dictionary<string, List<SheetRowSaleOrder>>();

            foreach (var row in originalSheet.SheetRows)
            {
                string itemId = row.item_id;
                if (itemBranchAllocation.ContainsKey(itemId))
                {
                    foreach (var allocation in itemBranchAllocation[itemId])
                    {
                        string branchId = allocation.branchId;
                        decimal allocatedQty = allocation.quantity;

                        if (!branchRows.ContainsKey(branchId))
                        {
                            branchRows[branchId] = new List<SheetRowSaleOrder>();
                        }

                        // 创建新的行，按分配数量调整
                        SheetRowSaleOrder newRow = JsonConvert.DeserializeObject<SheetRowSaleOrder>(JsonConvert.SerializeObject(row));
                        newRow.quantity = allocatedQty / row.unit_factor; // 转换回显示单位
                        newRow.sub_amount = newRow.quantity * newRow.real_price;
                        newRow.branch_id = branchId;

                        branchRows[branchId].Add(newRow);
                    }
                }
                else
                {
                    // 没有库存分配信息的商品，分配到默认仓库
                    string defaultBranch = "";
                    if (!branchRows.ContainsKey(defaultBranch))
                    {
                        branchRows[defaultBranch] = new List<SheetRowSaleOrder>();
                    }
                    branchRows[defaultBranch].Add(JsonConvert.DeserializeObject<SheetRowSaleOrder>(JsonConvert.SerializeObject(row)));
                }
            }

            // 创建分单
            List<SheetSaleOrder> divideSheets = new List<SheetSaleOrder>();

            // 获取原始优惠金额数据
            decimal totalNowDiscAmount = originalSheet.now_disc_amount;
            int sheetCount = branchRows.Count;
            decimal baseNowDiscAmount = Math.Floor(totalNowDiscAmount / sheetCount);
            decimal remainingNowDiscAmount = totalNowDiscAmount - (baseNowDiscAmount * (sheetCount - 1));

            int index = 0;
            foreach (var branchGroup in branchRows)
            {
                SheetSaleOrder newSheet = JsonConvert.DeserializeObject<SheetSaleOrder>(JsonConvert.SerializeObject(originalSheet));
                // 清空原有行数据
                newSheet.SheetRows.Clear();
                decimal totalAmount = 0;
                // 将商品添加到单据商品列
                foreach (var row in branchGroup.Value)
                {
                    newSheet.SheetRows.Add(row);
                    totalAmount += row.money_amount;
                }

                // 设置新单据信息
                newSheet.sheet_id = "";
                newSheet.sheet_no = "";
                newSheet.branch_id = branchGroup.Key;

                // 设置优惠金额
                if (index == sheetCount - 1)
                {
                    newSheet.now_disc_amount = remainingNowDiscAmount;
                }
                else
                {
                    // 其他单据按基础优惠金额分配
                    newSheet.now_disc_amount = baseNowDiscAmount;
                }

                // 设置其他金额
                newSheet.total_amount = totalAmount; //合计金额
                newSheet.now_pay_amount = totalAmount - newSheet.now_disc_amount;//
                newSheet.paid_amount= totalAmount - newSheet.now_disc_amount;//支付金额
                newSheet.payway1_amount = originalSheet.payway1_amount == 0 ? 0 : totalAmount - newSheet.now_disc_amount;
                newSheet.payway2_amount = originalSheet.payway2_amount == 0 ? 0 : totalAmount - newSheet.now_disc_amount;
                newSheet.payway3_amount = originalSheet.payway3_amount == 0 ? 0 : totalAmount - newSheet.now_disc_amount;
                newSheet.real_get_amount= totalAmount - newSheet.now_disc_amount;
                newSheet.total_quantity = originalSheet.total_quantity.Split(':', 2)[0] + ":" + newSheet.total_sale_qty_unit_type;

                divideSheets.Add(newSheet);
                index++;
            }

            return divideSheets;
        }

        /// <summary>
        /// 执行分单方案
        /// </summary>
        private async Task<(string result, string msg, List<string> sheetNos)> ExecuteDivideSheetPlan(SheetSaleOrder originalSheet, List<SheetSaleOrder> divideSheets, CMySbCommand cmd)
        {
            List<string> sheetNos = new List<string>();

            try
            {
                // 保存分单
                foreach (var sheet in divideSheets)
                {
                    // 设置父单据ID
                    sheet.parent_sheet_id = originalSheet.sheet_id;
                    sheet.Init();

                    // 保存单据
                    string saveResult = await sheet.Save(cmd, false);
                    if (saveResult != "")
                    {
                        return ("Error", $"保存分单失败: {saveResult}", new List<string>());
                    }

                    sheetNos.Add(sheet.sheet_no);
                }

                return ("OK", "分单成功", sheetNos);
            }
            catch (Exception e)
            {
                return ("Error", $"执行分单方案失败: {e.Message}", new List<string>());
            }
        }

        // 测试存储方法 TODO DELETE
        [HttpPost]
        public async Task<IActionResult> SaveAsBuyOrderSheet([FromBody] SheetSaleOrder oriSheet)  //[FromBody] dynamic sheet)
        {

            // 送货员
            oriSheet.senders_id = "";
            oriSheet.senders_name = "";
            SheetBuyOrder sheet = JsonConvert.DeserializeObject<SheetBuyOrder>(JsonConvert.SerializeObject(oriSheet));
            // SheetBuyOrder sheet = new SheetBuyOrder();
            // sheet.SheetRows = oriSheet.SheetRows;
            // TODO 替换为client_id对应的company_id
            string operKey = sheet.OperKey;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            sheet.Init();
            sheet.SYNCHRONIZE_SHEETS = true;

            sheet.OperID = "1";
            string resellerInfoSql = $"select * from rs_seller where company_id={companyID} and client_id = {oriSheet.supcust_id}";
            dynamic resellerInfo = await CDbDealer.GetRecordsFromSQLAsync(resellerInfoSql, cmd);
            // TODO 客户 此处为厂家 supplier
            
            sheet.company_id = (string)resellerInfo[0].reseller_company_id;
            sheet.supcust_id = (string)resellerInfo[0].supplier_id;

            sheet.branch_id = "";

            sheet.sheet_id = "";
            sheet.sheet_no = "";
            sheet.sheet_type = SHEET_TYPE.SHEET_BUY_DD;
            sheet.maker_id = "1";
            sheet.maker_name = "";
            
            // TODO delete

            foreach (SheetRowBuyOrder row in sheet.SheetRows)
            {
                string itemId = row.item_id;
                string querySql = $"select item_id,item_class,other_class from info_item_prop where rs_mum_id = {itemId}";
                dynamic itemInfo = await CDbDealer.GetRecordsFromSQLAsync(querySql, cmd);
                row.item_id = itemInfo[0].item_id;
                row.classId = itemInfo[0].item_class;
                row.other_class = itemInfo[0].other_class;

            }
           
            
            string msg = await sheet.Save(cmd);
            string result = msg == "" ? "OK" : "Error";
            
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no });
        }

        [HttpPost]
        public async Task<IActionResult> ConvertBillToSaleOrderSheet([FromBody] dynamic data, [FromQuery] string operKey)
        {
            SheetSaleOrder sheet = new SheetSaleOrder();
            Security.GetInfoFromOperKey(operKey, out string companyID);
            sheet.Init();
            sheet.OperID = "1";
            sheet.company_id = companyID;
            sheet.supcust_id = (string)data.supcustId;
            sheet.branch_id = (string)data.branchId;
            sheet.sheet_type = SHEET_TYPE.SHEET_SALE_DD;
            sheet.sheet_id = "";
            sheet.sheet_no = "";
            sheet.maker_id = "1";
            sheet.maker_name = "";
            int inout_flag = -1;
            foreach (dynamic r in data.records)
            {
                SheetRowSaleOrder row = new SheetRowSaleOrder();
                row.item_id = r.item_id;
                row.item_name = r.item_name;
                row.unit_no = r.unit_no;
                row.unit_factor = r.unit_factor;
                

                row.quantity = r.quantity;
                row.sub_amount = r.sub_amount;
                row.real_price = r.real_price;
                sheet.SheetRows.Add(row);
                row.inout_flag = inout_flag;
                sheet.total_amount += row.sub_amount;
            }
            
            string msg = await sheet.Save(cmd);
            string result = msg == "" ? "OK" : "Error";

            if (result == "OK")
            {
                string billNo = (string)data.billNo;
                cmd.CommandText = $"INSERT INTO info_supermarket_bill ( supermarket_name, company_id, s_bill_id, sheet_no, s_bill_type) VALUES " +
                    $"('{data.supermarketName}', {companyID},'{data.billId}','{sheet.sheet_no}',{data.billType})";
                await cmd.ExecuteNonQueryAsync();
                return new JsonResult(new { result, msg, sheetNo = sheet.sheet_no });
            }
            return new JsonResult(new { result});
        }
        [HttpPost]
        public async Task<IActionResult> SaveAndApprove([FromBody] dynamic dSheet)
        {
            SheetSaleOrder sheet = null;
            string msg = "";
            try
            {
                sheet = CPubVars.FromJsonObj<SheetSaleOrder>(dSheet);
            }
            catch (Exception e)
            { 
                string logMsg= "审核失败,请联系技术支持"+ e.Message+ "in SaleOrderSheet.cshtml.SaveAndApprove:"+JsonConvert.SerializeObject(dSheet);
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error(logMsg);
                MyLogger.LogMsg(logMsg, Token.CompanyID);
                msg = "审核失败,请联系技术支持";
                return new JsonResult(new { result = "Error", msg });
            }

            sheet.Init();
            
            if (sheet.bReview)
            {
                sheet.reviewer_id = sheet.OperID;
                sheet.review_time = CPubVars.GetDateText(DateTime.Now);
            }

            if (sheet.appendixPhotos != null)
            {
                List<string> appendixBase64s = new List<string>();

                foreach (string appendixPhoto in sheet.appendixPhotos)
                {
                    appendixBase64s.Add(appendixPhoto);
                }
                sheet.appendix_photos = await ProcessAppendixPicsRetDBStr(appendixBase64s, sheet.company_id.ToString());
            }
            if (sheet.displayGiveProofs != null)
            {
                Security.GetInfoFromOperKey(sheet.OperKey, out string companyID, out string operID);
                string sheetType = "XD";
                string supcust_id = sheet.supcust_id;
                dynamic displayGiveProofs = sheet.displayGiveProofs;
                string subType  = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayFdSenderSubType.SubTypeKey;
                foreach (dynamic displayGiveProofItem in displayGiveProofs)
                {
                    string disp_sheet_id = displayGiveProofItem.disp_sheet_id;
                    string uploadsMainPath = $@"{companyID}_{sheetType}_{subType}_{operID}_{supcust_id}_{disp_sheet_id}";
                    var workContent = displayGiveProofItem.work_content;
                    string work_content = JsonConvert.SerializeObject(workContent);
                    string work_content_result = await ActionsTemplateUtils.HandleActionTemplate(work_content, uploadsMainPath, _httpClientFactory);
                    dynamic work_content_obj = JsonConvert.DeserializeObject(work_content_result);
                    displayGiveProofItem.work_content = work_content_obj;
                }
                sheet.display_give_proofs =  JsonConvert.SerializeObject(sheet.displayGiveProofs);
            }
           
            var oldSheetId = sheet.old_sheet_id;
            if (sheet.isRedAndChange)
            {
                if(sheet.old_sheet_id == "")
                {
                    msg = "修改时没有获取原单据的编号";
                }
                else
                {
                    //if (!sheet.placeholder_sheet_id.IsInvalid()) sheet.isOpenPlaceholderOrder = true;
                    msg = await sheet.RedAndChange<SheetSaleOrder>(cmd);
                }

            }
            else
            {
                msg = await sheet.SaveAndApprove(cmd);
            }
            
            // 创建消息
            if (msg == "" && sheet.displayGiveProofs != null)
            {
                dynamic displayGiveProofs = sheet.displayGiveProofs;
                Security.GetInfoFromOperKey((string)sheet.OperKey, out string companyID, out string operID);
                string subType = "";
                string subTypeName = "";
                string sheetType = sheet.SheetType;
                string order_sheet_id = sheet.order_sheet_id;
                if (sheetType.Equals("XD"))
                {
                    subType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayFdSellerSubType.SubTypeKey;
                    subTypeName = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayFdSellerSubType.SubTypeName;
                } else if (sheetType.Equals("X") && (!string.IsNullOrEmpty(order_sheet_id)))
                {
                    subType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayFdSenderSubType.SubTypeKey;
                    subTypeName = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayFdSenderSubType.SubTypeName;
                } else if (sheetType.Equals("X") && (string.IsNullOrEmpty(order_sheet_id)))
                {
                    subType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayCxGiveSubType.SubTypeKey;
                    subTypeName = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayCxGiveSubType.SubTypeName;
                }
                foreach (dynamic displayGiveProofItem in displayGiveProofs)
                {
                    // 多个单据下创建消息
                    bool needReview = displayGiveProofItem.need_review;
                    if (needReview)
                    {
                        string supName = sheet.sup_name;
                        string sheetNo = sheet.sheet_no;
                        string sheetId = sheet.sheet_id;
                        string dispSheetNo = displayGiveProofItem.disp_sheet_no;
                        string dispSheetId = displayGiveProofItem.disp_sheet_id;
                        await MessageCreateServices.CreateMessageService(new
                        {
                            operKey = sheet.OperKey,
                            createrId = operID,
                            msgClass = MessageType.ClassType.Todo,
                            msgType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageType,
                            msgSubType = subType,
                            receiverId = "",
                            msgTitle = @$"{supName} 陈列单据 {dispSheetNo} 进行了{subTypeName}，请尽快复核。来自单据{sheetNo}",
                            sheetID = sheetId,
                            sheetType,
                            dispSheetId
                        }, cmd);
                    }
                }
            }
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            else
            {
                 
            } 

            return new JsonResult(new {result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time,sheet.approve_time, sheet.review_time,sheet.placeholder_sheet_id,sheet.placeholder_sheet_no });
        }
        public async Task<string> ProcessAppendixPicsRetDBStr(List<string> appendix_pictures_base64, string companyID)
        {
            var result = await CommonTool.ProcessAppendixPicsRetDBStr(_httpClientFactory, appendix_pictures_base64, companyID);
            return result;
        }

        [HttpPost]
        public async Task<IActionResult> Review([FromBody] dynamic data)
        {           
            string sheet_id = data.sheet_id;
            string now = CPubVars.GetDateText(DateTime.Now);
            string msg = "";
            dynamic rec = await CDbDealer.Get1RecordFromSQLAsync($"select approve_time,red_flag from sheet_sale_order_main where company_id={Token.CompanyID} and sheet_id={sheet_id};",cmd);
            if (rec == null)
            {
                msg = "单据不存在";
            }
            else if(rec.red_flag!="")
            {
                msg = "被红冲单据不能复核";
            }
            else if (rec.approve_time =="")
            {
                msg = "单据未审核不能复核";
            }
            else
            {
                cmd.CommandText = $"update sheet_sale_order_main set review_time ='{now}',reviewer_id={Token.OperID} where company_id={Token.CompanyID} and sheet_id={sheet_id};";
                await cmd.ExecuteNonQueryAsync();
            }
             
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            
            return new JsonResult(new { result, msg,review_time=now});
        }

        [HttpPost]
        public async Task<IActionResult> Red([FromBody] dynamic data)
        {
            string sheet_id = data.sheet_id;
            string operKey = data.operKey;
            string redBrief = data.redBrief;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetSaleOrder sheet = new SheetSaleOrder(SHEET_RETURN.EMPTY, LOAD_PURPOSE.SHOW);
            //string placeholder_sheet_id = data.placeholder_sheet_id;
            //sheet.placeholder_sheet_id = placeholder_sheet_id;
            //if (!placeholder_sheet_id.IsInvalid()) sheet.isOpenPlaceholderOrder = true;
            string msg = "";
            string result = "";
            msg = await sheet.Red(cmd, companyID, sheet_id, operID, redBrief);
            //if (placeholder_sheet_id.IsInvalid()) msg = await sheet.Red(cmd, companyID, sheet_id, operID, redBrief);
            //else
            //{
            //    //删除销售订单表 && 红冲占位单
            //    msg = await sheet.Delete(cmd, companyID, sheet_id, operID);
            //    result = msg == "" ? "OK" : "Error";
            //    if (msg != "") result = "Error";
            //    else
            //    {
            //        SheetPlaceholderSaleOrder sheet = new SheetSaleOrder(SHEET_RETURN.EMPTY, LOAD_PURPOSE.SHOW);
            //    }
            //    msg = await sheet.Red(cmd, companyID, sheet_id, operID, redBrief);
            //}

            result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            CallResult pay_bill_refund_response = new CallResult("OK", "未成功红冲,操作取消");
            CallResult return_redpackets_result = new CallResult("OK", "未成功红冲,操作取消");
            if (result == "OK")
            {
                // 2025.06.16改造
                // 应代理商要求，在线支付单据在红冲时不再自动退款
                //pay_bill_refund_response = await PayBillController.TryRefundAssociatedBills(cmd, _httpClientFactory,
                //    companyID, sheet_id, sheet.SheetType);
                return_redpackets_result = await RedPacketController.ReturnSheetRedPackets(cmd, 
                    sheet_id, sheet.SheetType, companyID, sheet.supcust_id);
            }
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time, pay_bill_refund_response, return_redpackets_result });
        }


        [HttpPost]
        public async Task<IActionResult> Delete([FromBody] dynamic data)
        {
            string sheet_id = data.sheet_id;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetSaleOrder sheet = new SheetSaleOrder(SHEET_RETURN.EMPTY, LOAD_PURPOSE.SHOW);
            string msg = await sheet.Delete(cmd, companyID, sheet_id, operID);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            CallResult pay_bill_refund_response = new CallResult("OK", "未成功删除,操作取消");
            CallResult return_redpackets_result = new CallResult("OK", "未成功删除,操作取消");
            if (result == "OK")
            {
                //pay_bill_refund_response = await PayBillController.TryRefundAssociatedBills(cmd, _httpClientFactory,
                //    companyID, sheet_id, sheet.SheetType);
                return_redpackets_result = await RedPacketController.ReturnSheetRedPackets(cmd,
                    sheet_id, sheet.SheetType, companyID, sheet.supcust_id);
            }
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time, pay_bill_refund_response, return_redpackets_result });
        }
        [HttpPost]
        public async Task<IActionResult> AppendBrief([FromBody] dynamic data)
        {
            string sheetID = data.sheetID;
            string newBrief = data.newBrief;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetSaleOrder sheet = new SheetSaleOrder(SHEET_RETURN.EMPTY, LOAD_PURPOSE.APPROVE);

            string msg = await sheet.AppendBrief(cmd, companyID, sheetID, newBrief);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, });
        }
        [HttpGet]
        public async Task<IActionResult> GetSheetToPrint(string operKey, string sheet_id,bool smallUnitBarcode,string printTemplate)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetSaleOrder sheet = new SheetSaleOrder(LOAD_PURPOSE.SHOW);
            cmd.ActiveDatabase = "";
            await sheet.Load(cmd, companyID, sheet_id);
            sheet.LoadAttrRowsForPrint(smallUnitBarcode);
            dynamic dTemplate = null;
            if (printTemplate.IsValid())
            {
                dTemplate = JsonConvert.DeserializeObject(printTemplate);
            }
            await sheet.LoadInfoForPrint(cmd, smallUnitBarcode, true, dTemplate);
         
            string msg = "";
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, sheet });
        }


        [HttpPost]
        public async Task<IActionResult> JqxExportExcel([FromForm] IFormCollection data)
        {
            Console.WriteLine(data.ToString());
            string contentXml = data["content"];
            string result = "OK";
            string msg = "";

            #region 返回格式漂亮的xls文件，jqx版本太低不支持返回xlsx文件
            // // 设置响应头
            // Response.Clear();
            // Response.ContentType = "application/vnd.ms-excel";
            // Response.Headers.Add("Content-Disposition", $"attachment; filename=SK.xls");
            // Response.Headers.Add("Cache-Control", "must-revalidate, post-check=0, pre-check=0, private");
            // Response.Headers.Add("Expires", "0");
            // Response.Headers.Add("Pragma", "public");
            // // 写入内存流
            // 
            // using (MemoryStream memoryStream = new MemoryStream())
            // {
            //     using (StreamWriter sw = new StreamWriter(memoryStream, Encoding.UTF8, leaveOpen: true))
            //     {
            //         await sw.WriteAsync(contentXml);
            //         await sw.FlushAsync();
            //     }
            // 
            //     // 将内存流的内容异步写入响应体
            //     memoryStream.Seek(0, SeekOrigin.Begin);
            //    await memoryStream.CopyToAsync(Response.Body);
            // 
            // 
            // }
            // return new EmptyResult();
            #endregion
            #region 返回一个没有样式的xlsx文件
            // 创建一个新的 SXSSFWorkbook 实例
            using (var workbook = new XSSFWorkbook())
            {
                // 添加一个工作表
                var sheet = workbook.CreateSheet("Sheet1");

                // 将 XML 内容加载到工作表中
                using (var stream = new MemoryStream(Encoding.UTF8.GetBytes(contentXml)))
                {
                    var xmlDoc = new XmlDocument();
                    xmlDoc.Load(stream);

                    // 创建命名空间管理器
                    var nsManager = new XmlNamespaceManager(xmlDoc.NameTable);
                    nsManager.AddNamespace("ss", "urn:schemas-microsoft-com:office:spreadsheet");

                    // 使用带有命名空间的 XPath 获取所有行节点
                    var rows = xmlDoc.SelectNodes("//ss:Row", nsManager);
                    int rowIdx = 0;

                    foreach (XmlNode rowNode in rows)
                    {
                        var row = sheet.CreateRow(rowIdx++);
                        int colIdx = 0;

                        // 使用带有命名空间的 XPath 获取当前行中的所有单元格节点
                        var cells = rowNode.SelectNodes("ss:Cell", nsManager);

                        foreach (XmlNode cellNode in cells)
                        {
                            // 使用带有命名空间的 XPath 获取单元格中的数据
                            var dataNode = cellNode.SelectSingleNode("ss:Data", nsManager);
                            var value = dataNode.InnerText;

                            var cell = row.CreateCell(colIdx++);
                            cell.SetCellValue(value);
                        }
                    }
                }

                // 使用 NPOIMemoryStream 将 XSSFWorkbook 写入内存流
                byte[] fileData;
                using (var ms = new NPOIMemoryStream())
                {
                    workbook.Write(ms);
                    ms.Flush();
                    ms.Position = 0;
                    fileData = ms.ToArray();
                }

                // 返回 FileResult，提供内存流中的数据
                return File(fileData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "XSD.xlsx");
            }
            #endregion

        }

    }
}