﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace ArtisanManage.Pages.Setting
{
    public class PrintTemplateModel : PageBaseModel
    {
        public string TemplateID = "";
        public string SheetType = "";
        // public string OperKey = "";
        public PrintTemplateModel() : base(MenuId.infoPrintTemplate)
        {

        }
        public void OnGet(string sheetType, string templateID, string operKey)
        {
            if (sheetType == null || sheetType == "") throw new Exception("sheetType必须指定");
            this.TemplateID = templateID;
            this.SheetType = sheetType;
            this.OperKey = operKey;
        }
    }

    [ApiController]
    [Route("api/[controller]/[action]")]
    public class PrintTemplateController : BaseController
    { 
        public PrintTemplateController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        /// <summary>
        ///   加载模板，在新建或者打开模板时调用
        /// </summary>
        /// <param name="sheetType">单据类型 
        /// </param>
        /// <param name="templateID">单据ID,新建时为空</param>
        /// <returns>
        /// {  result, msg, 
        ///    template:模板JSON ,新建时返回默认的模板
        ///    avail_elements:可用元素JSON
        /// }
        /// </returns>       
        [HttpGet]
        public async Task<IActionResult> LoadTemplate(string sheetType, string templateID)
        {
            // create table print_template(company_id integer, template_id serial, sheet_type text, template_content json, constraint pk_print_template primary key(template_id));
            // create table print_template_avail_elements(sheet_type text primary key, avail_elements json);
            // create table print_template_choose(company_id integer, client_group_id integer, client_id integer, template_id integer, constraint pk_print_template_choose primary key(client_group_id, client_id));
            // create table print_template_shared(company_id integer, template_id serial primary key, sheet_type text, template_content json,order_index integer, author_id integer);
            if(sheetType==null || sheetType == "")
            {
                return new JsonResult(new { result="Error", msg="请指定单据类型"});
            }
            //Security.GetInfoFromOperKey(operKey, out string companyID);
            SQLQueue QQ = new SQLQueue(cmd);
            string sql = $"select avail_elements from print_template_avail_elements where sheet_type='{sheetType}';";
            QQ.Enqueue("avail_elements", sql);

            if (templateID == "" || templateID == null)
                sql = $"select template_content,sheet_type,'' as template_name from print_template_shared where sheet_type='{sheetType}' and company_id=-1 order by order_index;";
            else
                sql = $"select template_content,sheet_type,template_name from print_template where template_id='{templateID}';";

            QQ.Enqueue("print_template", sql);
            dynamic avail_elements = null, template = null;
            string s_avail_elements = null, s_template = null;
            string sheet_type = sheetType ?? "";
            string template_name = "";
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                string tbl = QQ.Dequeue();
                if (tbl == "avail_elements")
                {
                    if (dr.Read()) s_avail_elements = CPubVars.GetTextFromDr(dr, "avail_elements");
                }
                else if (tbl == "print_template")
                {
                    if (dr.Read())
                    {
                        s_template = CPubVars.GetTextFromDr(dr, "template_content"); 
                        sheet_type = CPubVars.GetTextFromDr(dr, "sheet_type");
                        template_name = CPubVars.GetTextFromDr(dr, "template_name"); 
                    }
                }
            }
            string msg = "";
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            template = Newtonsoft.Json.JsonConvert.DeserializeObject(s_template);
            avail_elements = Newtonsoft.Json.JsonConvert.DeserializeObject(s_avail_elements);

            return new JsonResult(new { result, msg, template, template_name, avail_elements, sheet_type });
        }

        /// <summary>
        ///   加载其他人分享的模板列表，可以直接应用到当前正在编辑的模板
        /// </summary>
        /// <param name="sheetType">单据类型 
        /// </param>
        /// 
        /// <returns>
        /// 
        /// {  result, msg, 
        ///    templateList:
        ///      [
        ///        {template_id,template_name,industry:应用行业,brief:说明,author_name:作者,template_content:模板内容}
        ///      ]
        /// }
        /// 
        /// </returns>       
        [HttpGet]
        public async Task<IActionResult> GetSharedTemplatesList(string sheetType)
        {
            //Security.GetInfoFromOperKey(operKey, out string companyID);
            SQLQueue QQ = new SQLQueue(cmd);
            string sql = $"select avail_elements from print_template_avail_elements where sheet_type='{sheetType}';";
            sql = $"select template_id,template_name,industry,author_name,brief,template_content from print_template_shared where sheet_type='{sheetType}' order by order_index limit 100;";
            List<System.Dynamic.ExpandoObject> templateList = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string msg = "";
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, templateList });
        }
        /// <summary>
        /// 保存模板
        /// 
        /// </summary>
        /// <param name="data">
        ///   {
        ///     operKey,
        ///     sheet_type:单据类型
        ///     template_content:模板内容
        ///     
        /// </param>
        /// <returns>{ result, msg, newTemplateID:新的模板ID}</returns>
        [HttpPost]
        public async Task<IActionResult> SaveTemplate([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            //string sheet_type = data.sheet_type;
            string template_id = data.template_id;
            string template_name = data.template_name;
            
            dynamic d_template_content = data.template_content;
            string template_content = Newtonsoft.Json.JsonConvert.SerializeObject(d_template_content);
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            CDbDealer db = new CDbDealer();
            db.AddFields(data, "sheet_type");
            db.AddField("template_name", template_name);
            template_content = template_content.Replace("'", "''");
            db.AddField("template_content", template_content);  
            db.AddField("author_id", operID);
            db.AddField("company_id", companyID);
            string sql;
            if (template_id == "" || template_id == null)
            {
                sql = db.GetInsertSQL("print_template") + " returning template_id;";
            }
            else
            {
                sql = db.GetUpdateSQL("print_template", $"template_id={template_id} and company_id={companyID};");
            }
            
            cmd.CommandText = sql;
            object ov = await cmd.ExecuteScalarAsync();
            string newTemplateID = "";
            if (ov != null && ov != DBNull.Value)
            {
                newTemplateID = ov.ToString();
            }

            string msg = "";
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, newTemplateID });
        }




        // For app invocation. do not del.
        [HttpGet]
        public async Task<IActionResult> GetCloudPrintersToUse(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var sql = @$"SELECT P.printer_id, P.printer_name, P.device_id, P.check_code, P.printer_brand
FROM info_cloud_printer P 
WHERE P.company_id = { companyID } AND P.status = 1
ORDER BY P.printer_id LIMIT 10;";

            List<System.Dynamic.ExpandoObject> printerList = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);

            string msg = "";
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, printerList });
        }

        /// <summary>
        ///   打印单据时，从服务器端拉取可用模板列表
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="sheetType">单据类型</param>
        /// <param name="clientID">客户ID</param>
        /// <returns>
        /// {  result, msg, 
        ///    templateList:模板JSON ,新建时返回默认的模板
        ///    [
        ///    template_id,template_name,template_content:模板内容
        /// 
        /// 
        ///    ]
        ///    avail_elements:可用元素JSON
        /// }
        /// </returns>       
        [HttpGet]
        public async Task<IActionResult> GetTemplateToUse(string operKey, string sheetType,string sheetUsage, string clientID)
        {
            if ((CPubVars.IsNumeric(sheetType) || sheetType.Length>=6))
            {
                sheetType = SheetBase<SheetRowBase>.StrFromSheetTypeString(sheetType);
            }
            if (sheetUsage.IsInvalid()) sheetUsage = "D";

			 

			Security.GetInfoFromOperKey(operKey, out string companyID);
            if (clientID.IsInvalid()) clientID = "-1";

            List<System.Dynamic.ExpandoObject> templateList = new List<System.Dynamic.ExpandoObject>();
            List<System.Dynamic.ExpandoObject> cloudPrinters = new List<System.Dynamic.ExpandoObject>();

            SQLQueue QQ = new SQLQueue(cmd);

 

            var sql = $@"


SELECT spec_supcust_id,spec_sup_group, template_id,template_name,template_content,avail_elements 
FROM
(
    SELECT row_number() over (partition by t.template_id) row_num
       ,coalesce(sc.supcust_id, -1) spec_supcust_id,coalesce(sg.sup_group, -1) spec_sup_group,c.client_id,
       t.template_id, t.template_name, t.template_content::text, pa.avail_elements::text
    FROM print_template t
    LEFT JOIN print_template_choose c on t.template_id = c.template_id and c.company_id = {companyID}
    LEFT JOIN 
    (
        select supcust_id, sup_group from info_supcust where supcust_id = {clientID} and company_id = {companyID}
    ) sc  on c.client_id = sc.supcust_id
    LEFT JOIN 
    (
        select supcust_id, sup_group from info_supcust where supcust_id = {clientID} and company_id = {companyID}
    ) sg on c.client_group_id = sg.sup_group
    LEFT JOIN print_template_avail_elements pa on t.sheet_type = pa.sheet_type
    WHERE t.company_id = {companyID} and t.sheet_type = '{sheetType}' and coalesce(c.sheet_usage,'D')='{sheetUsage}' 
    ORDER BY coalesce(sc.supcust_id, -1) desc, coalesce(sg.sup_group, -1) desc,coalesce(c.client_id,-1) desc,order_index
    LIMIT 100
 )  t WHERE row_num=1

";
         
            QQ.Enqueue("templ",sql);

            sql = @$"SELECT P.printer_id, P.printer_name, P.device_id, P.check_code, P.printer_brand
FROM info_cloud_printer P 
WHERE P.company_id = { companyID } AND P.status = 1
ORDER BY P.printer_id LIMIT 10;";
            QQ.Enqueue("cloudPrinters", sql);
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();

            Dictionary<string, string> variables = new Dictionary<string, string>();
            string msg = "";
           
            while (QQ.Count > 0)
            {
                string tbl=QQ.Dequeue();
                if (tbl == "templ")
                {
                    templateList =  CDbDealer.GetRecordsFromDr(dr,false);
                   if(templateList.Count==0)
                    {
                        msg = "没有设置打印模板";
                        break;
                    }
                    dynamic tmp = templateList.First();
                    if (tmp != null)
                    {
                        dynamic elements = JsonConvert.DeserializeObject(tmp.avail_elements);
                     
                        void putAreaVars(JArray eles,string prefix="")
                        {
                            for(var i=0;i< eles.Count; i++)
                            {
                                dynamic obj = eles[i];
                                string name = obj.name;
                                string title = prefix + (string) obj.title;
                                if(!variables.ContainsKey(title))
                                    variables.Add(title, name); 
                            }
                        }
                        if (elements != null)
                        {
                            if (elements.pageHead != null) putAreaVars(elements.pageHead);
                            if (elements.tableHead != null) putAreaVars(elements.tableHead);
                            if (elements.table != null) putAreaVars(elements.table, "col_");
                            if (elements.tableTail != null) putAreaVars(elements.tableTail);
                            if (elements.pageTail != null) putAreaVars(elements.pageTail);
                        }
                    }
                    foreach(dynamic t in templateList)
                    {
                        dynamic c = JsonConvert.DeserializeObject(t.template_content);
                        c.variables = JsonConvert.DeserializeObject(JsonConvert.SerializeObject(variables));
                        t.template_content = JsonConvert.SerializeObject(c);
                    }
                }
                else if(tbl== "cloudPrinters")
                {
                    cloudPrinters = CDbDealer.GetRecordsFromDr(dr, false);
                } 
            }
            QQ.Clear();
              
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, templateList, cloudPrinters });
        }

      
        
        // for web printMultiSheets invocation. do not del.
        [HttpGet]
        public async Task<IActionResult> GetTemplatesForMultiSheetsPrint(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);

            List<System.Dynamic.ExpandoObject> templateList_X = new List<System.Dynamic.ExpandoObject>();
            List<System.Dynamic.ExpandoObject> templateList_T = new List<System.Dynamic.ExpandoObject>();
            List<System.Dynamic.ExpandoObject> templateList_XS = new List<System.Dynamic.ExpandoObject>();
            List<System.Dynamic.ExpandoObject> templateList_XD = new List<System.Dynamic.ExpandoObject>();
            List<System.Dynamic.ExpandoObject> templateList_TD = new List<System.Dynamic.ExpandoObject>();
            List<System.Dynamic.ExpandoObject> templateList_DB = new List<System.Dynamic.ExpandoObject>();
            List<System.Dynamic.ExpandoObject> templateList_JH = new List<System.Dynamic.ExpandoObject>();
            List<System.Dynamic.ExpandoObject> templateList_HH = new List<System.Dynamic.ExpandoObject>();
            List<System.Dynamic.ExpandoObject> templateList_SK = new List<System.Dynamic.ExpandoObject>();

            List<System.Dynamic.ExpandoObject> templates = new List<System.Dynamic.ExpandoObject>();
            List<System.Dynamic.ExpandoObject> cloudPrinters = new List<System.Dynamic.ExpandoObject>();

            SQLQueue QQ = new SQLQueue(cmd);

            string sql;
            sql = $@"select t.template_id, t.template_name, t.sheet_type, t.template_content,pa.avail_elements from print_template t
left join print_template_choose c on t.template_id=c.template_id and c.client_id=0 and c.client_group_id=0
left join print_template_avail_elements pa on t.sheet_type=pa.sheet_type
where t.company_id={companyID} and t.sheet_type in ('X','T','X_SUM','XD','TD','DB','JH', 'HH', 'SK') order by case when c.template_id is not null then 0 else 1 end;";
            QQ.Enqueue($"templates", sql);
            
            sql = @$"SELECT P.printer_id, P.printer_name, P.device_id, P.check_code, P.printer_brand
FROM info_cloud_printer P 
WHERE P.company_id = {companyID} AND P.status = 1
ORDER BY P.printer_id LIMIT 10;";
            QQ.Enqueue("cloudPrinters", sql);
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                string tbl = QQ.Dequeue();
                if (tbl == "templates")
                {
                    templates = CDbDealer.GetRecordsFromDr(dr, false);
                    foreach(dynamic tmp in templates)
                    {
                        Dictionary<string, string> variables = new Dictionary<string, string>();
                        dynamic elements = JsonConvert.DeserializeObject(tmp.avail_elements);

                        void putAreaVars(JArray eles, string prefix = "")
                        {
                            for (var i = 0; i < eles.Count; i++)
                            {
                                dynamic obj = eles[i];
                                string name = obj.name;
                                string title = prefix + (string)obj.title;
                                if (!variables.ContainsKey(title))
                                    variables.Add(title, name);
                            }
                        }
                        if (elements != null)
                        {
                            if (elements.pageHead != null) putAreaVars(elements.pageHead);
                            if (elements.tableHead != null) putAreaVars(elements.tableHead);
                            if (elements.table != null) putAreaVars(elements.table, "col_");
                            if (elements.tableTail != null) putAreaVars(elements.tableTail);
                            if (elements.pageTail != null) putAreaVars(elements.pageTail);
                        }
                        dynamic c = JsonConvert.DeserializeObject(tmp.template_content);
                        c.variables = JsonConvert.DeserializeObject(JsonConvert.SerializeObject(variables));
                        tmp.template_content = JsonConvert.SerializeObject(c); 
                    }
                }
                else if (tbl == "cloudPrinters")
                {
                    cloudPrinters = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();

            foreach(dynamic t in templates)
            {
                if (t.sheet_type == "X")
                {
                    templateList_X.Add(t);
                }
                else if (t.sheet_type == "T")
                {
                    templateList_T.Add(t);
                }
                else if (t.sheet_type == "X_SUM")
                {
                    templateList_XS.Add(t);
                }
                else if (t.sheet_type == "XD")
                {
                    templateList_XD.Add(t);
                }
                else if (t.sheet_type == "TD")
                {
                    templateList_TD.Add(t);
                }
                else if (t.sheet_type == "DB")
                {
                    templateList_DB.Add(t);
                }
                else if (t.sheet_type == "JH")
                {
                    templateList_JH.Add(t);
                }
                else if (t.sheet_type == "HH")
                {
                    templateList_HH.Add(t);
                }
                else if (t.sheet_type == "SK")
                {
                    templateList_SK.Add(t);
                }
            }

            string msg = "";
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, templateList_X, templateList_T, templateList_XS, templateList_XD, templateList_TD, templateList_DB, templateList_JH, templateList_HH, templateList_SK, cloudPrinters });
        }
    }

}
