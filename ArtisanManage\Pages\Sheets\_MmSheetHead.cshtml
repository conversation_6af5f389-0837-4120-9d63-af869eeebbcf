@model ArtisanManage.Models.SheetPartialViewModel


<partial name="_SheetHead" model="Model" />
<script type="text/javascript">



    //判断是否是数字(包含0)

    function myIsNumber(value) {
        if (value === undefined) return false
        if (value === null) return false
        value = value.toString().trim()
        if (value === '') return false
        if (isNaN(value)) return false;  // Check if the value is NaN
        if (!isFinite(value)) return false;
        return !isNaN(value)
    }


 
    function beforeQuery(sheetRows) {
        var isOften = false
        sheetRows.forEach((row) => {
            if (row.order_sub_id || row.disp_flow_id || row.borrowed_qty) {
                isOften = true
                return;
            }
        })
        if (isOften) {
            $('#jqxgrid').jqxGrid('showcolumn', 'specific_qty_unit')
            $('#jqxgrid').jqxGrid('showcolumn', 'order_sub_name')
        }
    }
     
   
    function handlekeyboardnavigation(event) {
         
        var key = event.charCode ? event.charCode : event.keyCode ? event.keyCode : 0;
        console.log(event)
      
        var cell = $('#jqxgrid').jqxGrid('getselectedcell');
        if(!cell) return
        if (key === 13) {

           

            $("#jqxgrid").jqxGrid('endcelledit', cell.row, cell.column, false);

             addEmptyRowsAtTail(4, 20);
            var cellRow = cell.row, cellCol = cell.column

           
            if (cell.column == "item_id") {
                var item_id = $('#jqxgrid').jqxGrid('getcellvalue', cell.row, 'item_id');
                if (!item_id) {
                     $('#jqxgrid').jqxGrid('begincelledit', cell.row, cell.column);
                    return true
                }
            }
            var nextCell = getNextCellByEnterKey(cell.row, cell.column)

            $('#jqxgrid').jqxGrid('clearselection')

            $('#jqxgrid').jqxGrid('selectcell', nextCell.row, nextCell.datafield)
            if (nextCell.datafield == 'item_id')
               $('#jqxgrid').jqxGrid('ensurerowvisible', cell.row + 3)

            if (nextCell.datafield == 'item_id' ||nextCell.datafield == 'unit_no' ||nextCell.datafield == 'quantity_unit_conv'||nextCell.datafield == 'quantity'||nextCell.datafield == 'real_price'||nextCell.datafield == 'sub_amount' || nextCell.datafield == 'remark' || nextCell.datafield == 'branch_id'|| nextCell.datafield == 'branch_position' || nextCell.datafield == 'batch_id' || nextCell.datafield == 'batch_id_f') {
                 
                  
                    if (window.g_queryStatus == 'querying') {
                        var tmInterval = setInterval(function ()
                        {
                            if (window.g_queryStatus == 'done')  { 
                                clearInterval(tmInterval)
                                $('#jqxgrid').jqxGrid('begincelledit', nextCell.row, nextCell.datafield);
                            }
                        } , 30)
                    } 
                    else {
                       // setTimeout(function () {
                            $('#jqxgrid').jqxGrid('begincelledit', nextCell.row, nextCell.datafield);
                      //  }, 10);
                    }
             
            }
            return true
            if (cell.column === "item_id") {
                var item_id = $('#jqxgrid').jqxGrid('getcellvalue', cell.row, 'item_id');
                var page_grid = g_pageSetting.grid
                var produceDateShow = page_grid.find(item => {
                    if (item.datafield == "virtual_produce_date") {
                        return item.hidden
                    }
                    if (item.datafield == "produce_date") {
                        return item.hidden
                    }
                })
                if (item_id) {
                    $('#jqxgrid').jqxGrid('clearselection');

                    if (produceDateShow != undefined && !produceDateShow) {

                        if (!nextCol) nextCol = 'virtual_produce_date'
                        $('#jqxgrid').jqxGrid('selectcell', cell.row, nextCol);
                        if (nextCol == 'virtual_produce_date') {
                            setTimeout(function () {
                                $('#jqxgrid').jqxGrid('begincelledit', cell.row, 'virtual_produce_date');
                            }, 300);
                        }

                    } else {
                        if (!nextCol) nextCol = 'unit_no'
                        $('#jqxgrid').jqxGrid('selectcell', cell.row, nextCol);
                        if (nextCol == 'unit_no') {
                            setTimeout(function () {
                                $('#jqxgrid').jqxGrid('begincelledit', cell.row, 'unit_no');
                            }, 300);
                        }
                    }


                }
                else {
                    /*setTimeout(function () {
                        var a = document.activeElement;
                        if (a && a.id == 'contentjqxgrid') {
                            var input = $('#now_disc_amount').find('input')[0];
                            input.focus();
                        }
                    }, 300);*/
                }
            }
            else if (cell.column === "unit_no") {
                $('#jqxgrid').jqxGrid('clearselection');
                if (!nextCol) nextCol = 'quantity'
                $('#jqxgrid').jqxGrid('selectcell', cell.row, nextCol);
            }
            else if (cell.column === "quantity") {
                $('#jqxgrid').jqxGrid('clearselection');
                if (!nextCol) nextCol = 'real_price'
                $('#jqxgrid').jqxGrid('selectcell', cell.row, nextCol);
                var columns = $('#jqxgrid').jqxGrid('getcolumns')
                var col = columns.records.find(col => col.datafield == 'real_price')
                if (col && col.hidden) {//无查看进价权限的情况，如果价格为空，就取0
                    $('#jqxgrid').jqxGrid('clearselection');
                    $('#jqxgrid').jqxGrid('selectcell', cell.row + 1, 'item_id');
                }
            }
            else if (cell.column === "real_price") {
                $('#jqxgrid').jqxGrid('clearselection');
                if (!nextCol) nextCol = 'sub_amount'
                $('#jqxgrid').jqxGrid('selectcell', cell.row, nextCol);
            }
            else if (cell.column === "sub_amount") {
                $('#jqxgrid').jqxGrid('clearselection');
                if (!nextCol) {
                    $('#jqxgrid').jqxGrid('selectcell', cell.row + 1, 'item_id');
                    $('#jqxgrid').jqxGrid('ensurerowvisible', cell.row + 3);
                }
                else $('#jqxgrid').jqxGrid('selectcell', cell.row, nextCol);
            }
            else if (cell.column === "virtual_produce_date") {
                $('#jqxgrid').jqxGrid('clearselection');
                if (!nextCol) nextCol = 'unit_no'
                $('#jqxgrid').jqxGrid('selectcell', cell.row,nextCol);
                setTimeout(function () {
                    $('#jqxgrid').jqxGrid('begincelledit', cell.row, nextCol);
                }, 300);
            }
            else if (cell.column === "batch_id") {
                $('#jqxgrid').jqxGrid('clearselection');
                if (!nextCol) nextCol = 'quantity'
                $('#jqxgrid').jqxGrid('selectcell', cell.row, nextCol);
            }
            else if (cell.column === "batch_id_f") {
                $('#jqxgrid').jqxGrid('clearselection');
                if (!nextCol) nextCol = 'quantity'
                $('#jqxgrid').jqxGrid('selectcell', cell.row, nextCol);
            }
            else if (cell.column === "branch_id") {
                $('#jqxgrid').jqxGrid('clearselection');
                if (!nextCol) nextCol = 'branch_position'
                $('#jqxgrid').jqxGrid('selectcell', cell.row, nextCol);
            }
            else if (cell.column === "branch_position") {
                $('#jqxgrid').jqxGrid('clearselection');
                if (!nextCol) nextCol = 'unit_no'
                $('#jqxgrid').jqxGrid('selectcell', cell.row, nextCol);
            }


            return true;
        }
        else if (key === 27) {
            return true;
        } 
        else if (key >= 37 && key <= 40) {
            if (window.g_tmBeginEdit) {
                clearTimeout(window.g_tmBeginEdit)
                window.g_tmBeginEdit=0
            }
            window.g_tmBeginEdit =  setTimeout(() => {
                var sltCell = $('#jqxgrid').jqxGrid('getselectedcell');
               // if (!sltCell || sltCell.row!=cell.row || sltCell.column!=cell.column) return
                 if (sltCell.column == 'unit_no' || sltCell.column=="item_id" || sltCell.column=="quantity_unit_conv") { 
                    $('#jqxgrid').jqxGrid('begincelledit', sltCell.row, sltCell.column); 
                 }
            },100)
              
        }

    }
    /*
    function updateTheCostAmount() 
    {
        var sheetType = $('#sheetType').val()
        var rows = $('#jqxgrid').jqxGrid('getrows');
        var now_disc_amount = $('#now_disc_amount').jqxInput('val');
        var cost_amount_avg = 0;
        var cost_amount_buy = 0; 
        var cost_amount_recent = 0; 
        var cost_amount_prop = 0;
        var total_protfit_amount = 0;
        for (var i = 0; i < rows.length; i++) 
        {
            var row = rows[i]
            if (!row.item_id) continue
            if (row.cost_price_avg) {
                cost_amount_avg += parseFloat(row.cost_price_avg) * parseFloat(row.quantity)*parseFloat(row.unit_factor)
            }
            if (row.cost_price_buy) {
                cost_amount_buy += parseFloat(row.cost_price_buy) * parseFloat(row.quantity)*parseFloat(row.unit_factor)
            }
            if (row.cost_price_recent) {
                cost_amount_recent += parseFloat(row.cost_price_recent) * parseFloat(row.quantity) * parseFloat(row.unit_factor)
            }
            if (row.cost_price_prop) {
                cost_amount_prop += parseFloat(row.cost_price_prop) * parseFloat(row.quantity) * parseFloat(row.unit_factor)
            }
            if (row.sub_amount != null && row.cost_amount != null) {
                total_protfit_amount += parseFloat(row.sub_amount-row.cost_amount)
            }
        }
        total_protfit_amount += total_protfit_amount - parseFloat(now_disc_amount)
        if (sheetType == 'X' || sheetType == 'XD') {
            $('#cost_amount_avg').val(toMoney(cost_amount_avg))
            $('#cost_amount_buy').val(toMoney(cost_amount_buy))
            $('#cost_amount_recent').val(toMoney(cost_amount_recent))
            $('#cost_amount_prop').val(toMoney(cost_amount_prop))
            $('#total_profit_amount').val(toMoney(total_protfit_amount))
        }
        if (sheetType == 'T' || sheetType == 'TD') {
            $('#cost_amount_avg').val(toMoney(-cost_amount_avg))
            $('#cost_amount_buy').val(toMoney(-cost_amount_buy))
            $('#cost_amount_recent').val(toMoney(-cost_amount_recent))
            $('#cost_amount_prop').val(toMoney(-cost_amount_prop))
            $('#total_profit_amount').val(toMoney(-total_protfit_amount))
        }
    }*/


    function updateTotalAmount() {
         
        var rows = $('#jqxgrid').jqxGrid('getrows');
        var total_amt = 0;
        var order_amt = 0;
        var total_weight = 0;
        var total_volume = 0;
        var rowIndex =-1;
        var orderSubID = ''
        var orderSubName = ''
        let ks_amount = 0
        var sale_amt = 0;
        var restore_amt = 0;

        for (var i = 0; i < rows.length; i++) {
            //var sheetRow = {};// new myJXC.CSheetRow();
            //var row = $('#jqxgrid').jqxGrid('getrowdatabyid', i);
            var row = rows[i]
            if (!row.item_id) continue
            if (row.order_sub_id && row.order_sub_name&&row.order_sub_id!="-1") rowIndex++
            if (rowIndex > -1 && row.order_sub_id && row.quantity&&row.order_sub_id!="-1") { //合计该单据使用的订货款
                if (!orderSubID) {
                    orderSubID = row.order_sub_id
                    orderSubName = row.order_sub_name
                }
                else if (orderSubID && row.order_sub_id != orderSubID) bw.toast('暂不支持一张单据使用多个定货款账户，请分开使用',3000)
                order_amt += parseFloat(row.sub_amount)
            }

            //合计客损的总额
            if (row.trade_type == 'KS' && row.quantity) { 
                ks_amount += parseFloat(row.sub_amount)
            }
        
            var amt = row.sub_amount;
            if (parseFloat(amt) && row.trade_type !='KS') { //
                if (amt > 0) {
                    sale_amt += parseFloat(amt);
                } 
                else { 
                    restore_amt -= parseFloat(amt)
                }
                total_amt += parseFloat(amt);
            }
            if (row.weight) {
                total_weight+=parseFloat(row.weight)
            }
            if(row.volume){
                total_volume+=parseFloat(row.volume)
            }
        }

        var disc_amt = $('#now_disc_amount').jqxInput('val');
        if (!disc_amt) disc_amt = 0;
        var no_disc_amount = total_amt - disc_amt;
        if($('#is_imported').val()==null || $('#is_imported').val().toLowerCase()!='true'){
            $('#total_amount').jqxInput('val', toMoney(total_amt));  
            if(document.getElementById("return_amount")){              
                $('#return_amount').jqxInput('val', toMoney(restore_amt));
                $('#sale_amount').jqxInput('val', toMoney(sale_amt));
            }  
        }
        $('#no_disc_amount').jqxInput('val', toMoney(no_disc_amount));
        $('#total_weight').val(toMoney(total_weight))
        $('#total_volume').val(toMoney(total_volume))


        //处理存在交易方式为客损是，支付方式的变换
        let ksSubID 
        let ksSubName
        if (ks_amount!=0) { 
            ksSubID = window.ksSub.sub_id?  window.ksSub.sub_id :''
            ksSubName =window.ksSub.sub_name? window.ksSub.sub_name : ''
            window.hasKs = true
            if (ksSubID != '' && ksSubName != '') {
                     $('#payway1_amount').jqxInput('val', ks_amount);
                     $('#payway1_id').jqxInput('val', { value: ksSubID, label: ksSubName })
                     $('#payway1_id').jqxInput({ disabled: true })
                     $('#payway1_amount').jqxInput({ disabled: true })
                     onShowGroupBtnClicked('payway')
            }
           
        }
   


        if (myIsNumber(order_amt) && orderSubName) {
            $('#payway1_amount').jqxInput('val', toMoney(order_amt));
            $('#payway1_id').jqxInput('val', { value: orderSubID, label: orderSubName })
            window.g_orderSubID = orderSubID
            window.g_orderAmount = order_amt
            $('#payway1_id').jqxInput({ disabled: true })
            $('#payway1_amount').jqxInput({ disabled: true })
            onShowGroupBtnClicked('payway')

        } else {
            var payway1 = $('#payway1_id').jqxInput('val')
            //删除订货会或者客损行时的逻辑处理
            if ((payway1 && window.g_orderSubID && payway1.value == window.g_orderSubID) || (ks_amount == 0 && window.hasKs == true)) {
                $('#payway1_id').jqxInput({ disabled: false })
                $('#payway1_id').jqxInput('val', window.g_initialPayway)
                $('#payway1_amount').jqxInput({ disabled: false })
                onShowGroupBtnClicked('payway')
                if(window.hasKs == true)  window.hasKs = false

            }
            window.g_orderSubID = null
            window.g_orderAmount = null
        }
        //$('#payway1_amount').jqxInput('val', no_disc_amount);

        updatePayAmount();
    }
    var ignoreChange = false
    function updatePayAmount(byPaywayAmount) {

        var total_amount = $('#total_amount').jqxInput('val');
        var now_disc_amount = $('#now_disc_amount').jqxInput('val');
        var no_disc_amount = total_amount - now_disc_amount;
        ignoreChange = true;
        $('#no_disc_amount').jqxInput('val', toMoney(no_disc_amount));

        

        var payway1_amount = $('#payway1_amount').jqxInput('val');
        var payway2_amount = $('#payway2_amount').jqxInput('val');
        var payway3_amount = $('#payway3_amount').jqxInput('val');
        let sheetType = $('#sheetType').val()
        // let leftAmount = no_disc_amount - payway1_amount - payway2_amount - payway3_amount;
        // 这里leftAmount的计算时机不对 ，下面的语句应该考虑放在别的位置
        // console.log(toMoney(leftAmount))
        // if(window.onPayAmountUpdated){
        //    window.onPayAmountUpdated(leftAmount)
        // }
         
        var payway2_id = $('#payway2_id').jqxInput('val').value;
        var payway3_id = $('#payway3_id').jqxInput('val').value;

        var acct_type = $('#acct_type').jqxInput('val')

        if (g_init_payway1_amount && !g_init_left_amount) //转单的时候会在销售单中自动带出订单中的支付方式1和欠款金额
        {
            acct_type='cash'
        }
        else if (!g_init_payway1_amount && g_init_left_amount) 
        {
            acct_type='arrears'
        }
        else if(g_init_payway1_amount && g_init_left_amount)
        {
            acct_type ='both'
        }
       
        if (acct_type == 'arrears' || byPaywayAmount) {
            var left_amount = no_disc_amount - payway1_amount - payway2_amount - payway3_amount;
            $('#left_amount').jqxInput('val', toMoney(left_amount));
        }
        else if (acct_type == 'both')
        {
            if (window.g_orderSubID || window.hasKs) 
            {
                // var pay_amount = no_disc_amount - payway1_amount - left_amount;
                var left_amount = $('#left_amount').jqxInput('val');
                if (!payway2_id) $('#payway2_id').jqxInput('val', window.g_initialPayway)
                if (window.g_orderAmount) payway1_amount = toMoney(window.g_orderAmount)
                payway2_amount = toMoney(no_disc_amount - payway1_amount - left_amount)
                $('#payway2_amount').jqxInput('val', payway2_amount);
            } 
            else 
            {
                var left_amount = g_init_left_amount
                payway1_amount = toMoney(no_disc_amount - payway2_amount -payway3_amount - left_amount)
                $('#payway1_amount').jqxInput('val', payway1_amount);
            }
          
        }
        else //cash
        {
            $('#left_amount').jqxInput('val',0)
            var left_amount =  0;
            if (window.g_orderSubID || window.hasKs) {
                // var pay_amount = no_disc_amount - payway1_amount - left_amount;

                if (!payway2_id) $('#payway2_id').jqxInput('val', window.g_initialPayway)  //如果有定货会或者客损，第一个支付方式被占用，就把第一个支付方式移到第二个
                if (window.g_orderAmount) payway1_amount = toMoney(window.g_orderAmount)
                payway2_amount = toMoney(no_disc_amount - payway1_amount - left_amount)
                $('#payway2_amount').jqxInput('val', payway2_amount);
            }
            else 
            {
                //处理删除客损，支付方式金额变化
                if (window.hasKs == false) {
                    payway1_amount = toMoney(no_disc_amount)
                    $('#payway2_amount').jqxInput('val', 0);
                } 
                else 
                {
                    payway1_amount = toMoney(no_disc_amount - payway2_amount -payway3_amount - left_amount)
                }
                $('#payway1_amount').jqxInput('val', payway1_amount);
                $('#payway1_amount').trigger('input');
               
            }
        }
        // let leftAmount = no_disc_amount - payway1_amount - payway2_amount - payway3_amount;
        let leftAmount = $('#left_amount').jqxInput('val')
        console.log(toMoney(leftAmount))
        if (window.onPayAmountUpdated) {
            window.onPayAmountUpdated(leftAmount)
        }
        ignoreChange = false;
    }
    function getUnitQty(s_quantity, b_unit_no, m_unit_no, s_unit_no, b_unit_factor, m_unit_factor){
        var b_qty = 0, m_qty = 0, s_qty = 0;
        var leftQty = s_quantity
        var absLeftQty = Math.abs(leftQty)
        var flag = leftQty < 0 ? -1 : 1;
        var unitsQty =""
        if(b_unit_factor){
            b_qty = Math.floor(absLeftQty/b_unit_factor)
            absLeftQty = absLeftQty%b_unit_factor
            if (b_qty > 0) {
                b_qty *= flag
                unitsQty += b_qty + b_unit_no
            }
        }

        if (m_unit_factor) {
           m_qty = Math.floor(absLeftQty/m_unit_factor)
            absLeftQty = absLeftQty%m_unit_factor
            if (m_qty > 0) {
                m_qty *= flag
                unitsQty += m_qty + m_unit_no
            }
        }
        s_qty= absLeftQty
        if(s_qty>0){
            s_qty*=flag
            unitsQty += s_qty + s_unit_no
        }
        return unitsQty
    }
    
    function updateRowSubAmount(rowIndex) {
        var rows = $('#jqxgrid').jqxGrid('getrows')
        var row=rows[rowIndex]
        var real_price = parseFloat(row.real_price)
        var qty = parseFloat(row.quantity)
         
        var sub_amount=row.sub_amount

        if (myIsNumber(qty) && myIsNumber(real_price)) {
            sub_amount = toMoney(qty * real_price,4,2)
          
            if(!row.order_sub_id) sub_amount = toMoney(sub_amount)
           
        }
        
        if (myIsNumber(qty) && myIsNumber(row.orig_price)) {
            var orig_amount = qty * parseFloat(row.orig_price) 
            row.orig_amount = toMoney(orig_amount,2) 
        }
         
        row.sub_amount = sub_amount
        if(row.unit_weight)
            row.weight=toMoney(row.unit_weight * row.quantity,3)

       
        updateRowInfo(rowIndex)
    }

    function updateRowInfo(rowIndex) {
        var rows = $('#jqxgrid').jqxGrid('getrows')
        var row = rows[rowIndex]
        var real_price = parseFloat(row.real_price)
        var qty = parseFloat(row.quantity)
        var cost_price_avg = parseFloat(row.cost_price_avg)
        var cost_price_recent = parseFloat(row.cost_price_recent)
        var cost_price_prop = parseFloat(row.cost_price_prop)
        var cost_price_buy = parseFloat(row.cost_price_buy)
        //计算差额
        var price_diff_p = 0
        var wholesale_price = 0
        if (row.wholesale_price !== "") {
            wholesale_price = parseFloat(row.wholesale_price)
        }
        price_diff_p = (isNaN(real_price) ? 0 : real_price) - wholesale_price
        row.price_diff_p = price_diff_p
        if (row.cost_price) {
            var cost_price = parseFloat(row.cost_price)
            var cost_amount = qty * cost_price;
            row.cost_amount = toMoney(cost_amount) || 0
        }

        row.s_real_price = toMoney(real_price / row.unit_factor, 4);
        if (row.b_unit_factor > 0) {
            row.b_real_price = toMoney(real_price / row.unit_factor * row.b_unit_factor, 2);
        }

        if (row.m_unit_factor > 0) {
            row.m_real_price = toMoney(real_price / row.unit_factor * row.m_unit_factor, 2);
        }

        var sub_amount = row.sub_amount

        if (myIsNumber(qty) && myIsNumber(real_price)) {
            //sub_amount = qty * real_price
            // new_sub_amount = new_sub_amount.toFixed(2)
            if (!row.order_sub_id) sub_amount = toMoney(sub_amount)
            if (row.cost_price) {
                var new_profit = sub_amount - cost_amount
                var new_profit_rate
                if (sub_amount != 0) {
                    new_profit_rate = toMoney((new_profit * 100 / sub_amount).toFixed(2), 2)
                }


                row.profit = toMoney(new_profit)
                row.profit_rate = toMoney(new_profit_rate)
            }
        }

        if (myIsNumber(qty) && myIsNumber(row.orig_price)) {
            var orig_amount = qty * parseFloat(row.orig_price)
            row.orig_amount = toMoney(orig_amount, 2)
        }
         
         
        if (row.unit_weight)
            row.weight = toMoney(row.unit_weight * row.quantity, 3)
        if(row.unit_volume)
            row.volume=toMoney(row.unit_volume * row.quantity,3)
      
        if (!row.remark) row.remark = ''
        if (row.real_price == 0 && !row.trade_type) {
            if (row.remark.indexOf('赠品') == (-1)) {
                if (row.remark) row.remark += ','
                row.remark += '赠品'
            }
        }
        else {
            row.remark = row.remark.replace('赠品', '')
        }
    }
  
 
    function setRowOnItemSelected(rowIndex, item) {
        var supcust_id = $('#supcust_id').jqxInput('val').value
        var branch_id = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'branch_id');
        var branch_position = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'branch_position');
        if(!branch_position) branch_position = "0"
        if(!branch_id) branch_id = $('#branch_id').jqxInput('val').value
        var rows=[]
        if (Array.isArray(item)) {
            item.forEach(i => {
                var r = {item_id:i.value,item_name:i.label}
                rows.push(r)
            })            
        }
        else { 
            var row = {}
            row.item_id = item.value
            row.item_name = item.label
            rows.push(row)
        }
       
      // console.log('optionselected')
        window.g_queryStatus='querying'
        beforeQuery(rows)
        AddItemRows(rowIndex, supcust_id, branch_id,branch_position,rows)
    }


    function setStockOnbranchUpdate(rowIndex, item_id,branch_id,branch_position,batch_id,produce_date,batch_no) {
        if(!item_id) item_id = -1
        if(!branch_id) branch_id = -1
        if(!branch_position) branch_position = 0
        if(!produce_date && !batch_no) batch_id = 0 
        if(produce_date && !batch_no) batch_no = ""
        var params = { operKey: g_operKey, item_id: item_id,branch_id: branch_id,branch_position:branch_position,batch_id,produce_date,batch_no }
        $.ajax({
            url: '/api/SaleSheet/SetStockOnbranchUpdate',
            type: 'GET',
            contentType: 'application/json',
            processData: true,
            data: params,
            success: function (data) {
                let sheetType = $("#sheetType").val()
                let curGrid = '#jqxgrid'
                if(window.curGridSlt) curGrid =window.curGridSlt
                if (data.result === 'OK' && data.data.length) {
                    $(curGrid).jqxGrid('setcellvalue', rowIndex, 'stock_qty_unit', data.data[0].stock_qty_unit)
                    if("CG,CT,CD".indexOf(sheetType)==-1){
                         $(curGrid).jqxGrid('setcellvalue', rowIndex, 'sell_pend_qty_unit', data.data[0].sell_pend_qty_unit)
                         $(curGrid).jqxGrid('setcellvalue', rowIndex, 'usable_stock_qty_unit', data.data[0].usable_stock_qty_unit)
                    }
                }else{
                    $(curGrid).jqxGrid('setcellvalue', rowIndex, 'stock_qty_unit',"")
                    if("CG,CT,CD".indexOf(sheetType)==-1){
                        $(curGrid).jqxGrid('setcellvalue', rowIndex, 'sell_pend_qty_unit', "")
                        $(curGrid).jqxGrid('setcellvalue', rowIndex, 'usable_stock_qty_unit', "")
                }
            }
            }
        })
    }


 
    function cellendedit(event) {

        if (window.g_bIgnoreEndEditEvent) {
            window.g_bIgnoreEndEditEvent = false
            return
        }
        var args = event.args;
        var colName = args.datafield;

        var rowIndex = args.rowindex;
        var cellValue = args.value;
        var oldValue = args.oldvalue;
        var row = args.row;
        var branch_id = $('#branch_id').jqxInput('val')
        branch_id = branch_id.value
        // 辅助数量修改时oldvalue和cellvalue始终是""空值，在这里返回从而没法到达最末刷新语句，加上判断条件控制逻辑不在这里返回
        if (oldValue === cellValue && colName !== "quantity_unit_conv") return
        if (colName == "branch_id" && !oldValue.value && cellValue.value == branch_id) return

        var supcust_id = $('#supcust_id').jqxInput('val')
        var sheetType = $('#sheetType').val()
        supcust_id = supcust_id.value
        if (cellValue && cellValue.value != undefined) cellValue = cellValue.value

        row[colName] = cellValue
        var item_id =row.item_id
        if (!item_id && colName !=="branch_id") {
            return
        }
      
        function CheckNumberAndAlert(colTitle) {
            console.log(cellValue)
            if (!myIsNumber(cellValue)) {
                let msg = '请正确输入' + colTitle
                bw.toast(msg, 5000)
                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, colName, oldValue || '')
                return false
            }
            return true
        }
        /*function evaluate() {
            let qty = 0
            let msg = ""
            try{
                qty = math.evaluate(cellValue)
            }
            catch(e){
                msg = "请输入正确的表达式"
                bw.toast(msg, 5000)
            }
            if(!msg){
                row.quantity = qty
                cellValue = qty
                setTimeout(()=>{
                    $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, "quantity", qty)
                })
            }
            return msg
        }*/

        if (colName === 'item_id' && !(row.order_sub_id||row.disp_flow_id)) {

        }
        else if (colName === 'unit_no') {
            var sheetType = $('#sheetType').val()
            if (window.g_queriedItems && item_id) {

                var key = getQueriedItemsKeyFromRow(row)
                var item =window.g_queriedItems[key]
                if (item) {
                    item.units.forEach(function (unit) {
                        if (unit.unit_factor == 1) row.s_barcode = unit.barcode
                        if (unit.unit_no === cellValue) {
                            if(row.trade_type === 'J' || row.trade_type === 'H') unit.price = 0;
                            
                            row.real_price = toMoney(unit.price, 4)
                            var dotLen=2
                            if(unit.unit_factor==1) dotLen=2
                            if (window.g_companySetting.costPriceType == '3' && unit.buy_price) row.cost_price = toMoney(unit.buy_price,dotLen)
                            else if (window.g_companySetting.costPriceType == '2' && unit.cost_price_avg) 
                                row.cost_price = toMoney(unit.cost_price_avg*unit.unit_factor, dotLen)
                            else if (window.g_companySetting.costPriceType == '4' && unit.cost_price_recent)
                                row.cost_price = toMoney(unit.cost_price_recent * unit.unit_factor, dotLen)
                            row.cost_price_avg = toMoney(unit.cost_price_avg, 7)
                            row.cost_price_recent = toMoney(unit.cost_price_recent)
                            row.cost_price_prop = toMoney(unit.cost_price_spec)
                            row.cost_price_buy=toMoney(unit.cost_price_buy)
                            row.unit_factor = unit.unit_factor
                            row.orig_price = toMoney(unit.recent_orig_price, 4)
                            row.sys_price = toMoney(unit.price, 4)
                            row.retail_price = toMoney(unit.retail_price, 4)
                            row.last_time_price = toMoney(unit.recent_price, 4)
                            row.unit_weight = toMoney(unit.unit_weight, 3)
                            row.weight = toMoney(unit.weight, 3)
                            row.unit_volume = toMoney(unit.unit_volume, 3)
                            row.volume = toMoney(unit.volume, 3)
                            row.wholesale_price = toMoney(unit.wholesale_price,4)
                             
                            if( sheetType == 'CG'){
                                //row.cost_price_avg_unit = toMoney(item.cost_price_avg_s_unit*unit.unit_factor,2)
                                 row.cost_price_avg_unit = item.cost_price_avg_s_unit*unit.unit_factor
                            }
                           
                            //if (unit.recent_orig_price) row.orig_price = toMoney(unit.recent_orig_price, 4)
                            //if (unit.price) row.sys_price = toMoney(unit.price, 4)
                            //if (unit.retail_price !== undefined) row.retail_price = toMoney(unit.retail_price, 4)
                            //if (unit.recent_price) row.last_time_price = toMoney(unit.recent_price, 4)
                            if (row.orig_price) {
                                row.discount = toMoney(row.real_price / parseFloat(row.orig_price) * 100,1)
                                if(!myIsNumber(row.discount))row.discount=""
                            }
                            updateRowSubAmount(rowIndex)
                            row.barcode = unit.barcode
                            if(sheetType == "CG" || sheetType == "CT" || sheetType =="CD" || sheetType == "X"){
                                updateCostAmount()
                            }
                            updateTotalAmount()
                        }
                    })
                }
            }
        }
        else if (colName === 'trade_type') {
             
            if (item_id) {
                
                if (row.trade_type === 'J' && parseFloat(row.quantity) > 0 && sheetType !== 'JH') {
                    row.real_price = 0
                    row.remark = '借货'
                    row.quantity *= (-1)
                } 
                updateRowSubAmount(rowIndex)
                updateTotalAmount();
            }
        }
        else if (colName === 'quantity') {//数量
           // let msg = evaluate(cellValue, rowIndex)
           // if(msg) return
            if (!CheckNumberAndAlert("数量")) return
            if (row.trade_type === 'J') {
               // $("#jqxgrid").jqxGrid('endcelledit', rowIndex, colName, true);
                if (parseFloat(row.quantity) > 0 && sheetType !== 'JH') row.quantity *= (-1)
               let newQty=row.quantity
               setTimeout(()=>{
                     row.quantity = newQty
                     $('#jqxgrid').jqxGrid('updategrid')
               },100)
              
            }
            let qty = parseFloat(row.quantity)
            
            if (sheetType == "CG") {
                var key = getQueriedItemsKeyFromRow(row)
                var item = window.g_queriedItems[key]
                var b_unit_no="",m_unit_no="",s_unit_no="",b_unit_factor=null,m_unit_factor=null
                item.units.forEach(unit=>{
                    if (unit.unit_type == 'b') {
                        b_unit_no = unit.unit_no
                        b_unit_factor =  unit.unit_factor
                    } else if (unit.unit_type == 'm') {
                        m_unit_no = unit.unit_no
                        m_unit_factor =  unit.unit_factor
                    }else if(unit.unit_type=='s'){
                        s_unit_no = unit.unit_no
                    }
                })
                var s_qty = qty*parseFloat(row.unit_factor)
                row.unit_qty_conv = getUnitQty(s_qty,b_unit_no,m_unit_no,s_unit_no,b_unit_factor,m_unit_factor)
            }
            if (!row.real_price && myIsNumber(qty) && myIsNumber(row.sub_amount)) {
                var new_price = row.sub_amount / qty;
                row.real_price = toMoney(new_price) 
            }
            updateRowSubAmount(rowIndex)
            if(sheetType == "CG" || sheetType == "CT" || sheetType=="CD" || sheetType == "X"){
                updateCostAmount()
            }
            updateTotalAmount();
        }
        else if (colName === 'discount') { //计算折扣
            if (!CheckNumberAndAlert("折扣")) return

            var discount = parseFloat(row.discount) * 0.01
            var orig_price = parseFloat(row.orig_price)
            var qty = parseFloat(row.quantity)
            if (myIsNumber(orig_price)) {
                var new_real_price;
                    new_real_price = orig_price * discount
            }

            row.real_price = toMoney(new_real_price)
            updateRowSubAmount(rowIndex)
            if(sheetType == "CG" || sheetType == "CT" || sheetType=="CD"){
                updateCostAmount()
            }
            updateTotalAmount()

        }
        else if (colName === 'orig_price') { //原价
            if (!CheckNumberAndAlert("原价")) return
            if (event.args.oldvalue == row.orig_price) return
            row.real_price = toMoney(row.orig_price)

                if (myIsNumber(row.discount)) {
                var orig_price = parseFloat(row.orig_price)
                var discount = parseFloat(row.discount) * 0.01
                var new_real_price = (discount * orig_price).toFixed(4)
                row.real_price = toMoney(new_real_price)
            }
            /*
            let realPrice = parseFloat(row.real_price)
            let origPrice = parseFloat(row.orig_price)
            if (origPrice) {
                row.discount = toMoney(realPrice / origPrice * 100)
            }*/
            updateRowSubAmount(rowIndex)
            updateTotalAmount()
        }
        else if (colName === 's_real_price') { 
            var dotLen=2
            if(row.unit_factor==1) dotLen=4
            row.real_price = toMoney( row.s_real_price * row.unit_factor,dotLen)
            
            updateRowSubAmount(rowIndex)
            updateTotalAmount()
        }
        else if (colName === 'b_real_price') { 
              
            row.s_real_price = toMoney(row.b_real_price / row.b_unit_factor, 4)
            var dotLen = 2
            if (row.unit_factor == 1) dotLen = 4
            row.real_price = toMoney(row.s_real_price * row.unit_factor, dotLen)

            updateRowSubAmount(rowIndex)
            updateTotalAmount()
        }
        else if (colName === 'm_real_price') {
             
            row.s_real_price = toMoney(row.m_real_price / row.n_unit_factor, 4)
            var dotLen = 2
            if (row.unit_factor == 1) dotLen = 4
            row.real_price = toMoney(row.s_real_price * row.unit_factor, dotLen)


            updateRowSubAmount(rowIndex)
            updateTotalAmount()
        }
        else if (colName === 'orig_amount') { //原价金额
            if (!CheckNumberAndAlert("原价金额")) return
            let origPrice = parseFloat(row.orig_price)
            let origAmount = parseFloat(row.orig_amount)
            if (myIsNumber(row.quantity) && Number(row.quantity)) {
                row.orig_price = toMoney(origAmount / row.quantity)
                let realPrice = parseFloat(row.real_price)
                let newOrigPrice = parseFloat(row.orig_price)
                if (row.orig_price) {
                    row.discount = toMoney(realPrice / newOrigPrice * 100)
                }
            }
            //updateRowSubAmount(rowIndex)
            updateTotalAmount()
        }
        else if (colName === 'real_price') {//价格
            if (!CheckNumberAndAlert("价格")) return
            var orig_price = parseFloat(row.orig_price)
            if (row.orig_price && row.orig_price != '0' ) {
                var real_price = parseFloat(row.real_price)
                row.discount = toMoney(real_price / orig_price * 100,1)
                if (!myIsNumber(row.discount)) row.discount = ''
            }
            if(row.orig_price == '0')  row.discount=''
            updateRowSubAmount(rowIndex)
            if(sheetType == "CG" || sheetType == "CT" || sheetType=="CD" || sheetType=="X"){
                updateCostAmount()
            }
            updateTotalAmount()
        }
        else if (colName === 'sub_amount') {//金额
            if (!CheckNumberAndAlert("金额")) return

            var qty = parseFloat(row.quantity)
            if (myIsNumber(qty) && qty) {
                var sub_amount = parseFloat(toMoney(row.sub_amount))
                //客户录入单价时，比如7.326,回车自动产生金额85.17，再回车会把单价变成7.3285，客户希望不改变单价
                //因为金额小数点位数是2位，所以由于四舍五入产生的最大误差为0.005,平均到每个单价格是0.005/qty,所以这里判断在允许误差范围内则不更新单价

                var real_price_delta = 0.005/qty
                if (Math.abs(toMoney((sub_amount / qty), 4) - row.real_price) >= real_price_delta) row.real_price = toMoney((sub_amount / qty), 4)
                
                var orig_price = parseFloat(row.orig_price)
                if (myIsNumber(orig_price)) {
                    var real_price = parseFloat(row.real_price)
                    row.discount = toMoney(real_price / orig_price * 100)
                    if (!myIsNumber(row.discount)) row.discount = ''
                }
                updateRowInfo(rowIndex)
                updateTotalAmount()
            }
        }
        else if(colName === 'recent_retail_price'){
            //最近零售价
            
            if (row.recent_retail_price){
                if (!CheckNumberAndAlert("最近零售价")) return

                row.recent_retail_price = toMoney(row.recent_retail_price)
               
            }
            
        }
        else if (colName === 'virtual_produce_date') {//生产日期
            if (!isNaN(cellValue)) {
                if (cellValue.length == 6) {
                    cellValue = '20' + cellValue.substr(0, 2) + '-' + cellValue.substr(2, 2) + '-' + cellValue.substr(4, 2)
                    row[colName] = cellValue
                }
            }
        }
        else if (colName === 'branch_id') {
            //仓库变化=》库位变化=》产期变化=》库存
           row.branch_name = args.value?args.value.label:""
           let rowBranchId = cellValue
           if(!cellValue) rowBranchId = branch_id
           if (window.g_dicDefualtBranchPosition && window.g_dicDefualtBranchPosition[rowBranchId]){
               row.branch_position = window.g_dicDefualtBranchPosition[rowBranchId].branch_position
               row.branch_position_name =  window.g_dicDefualtBranchPosition[rowBranchId].branch_position_name
               window.g_queryStatus = 'querying'
               GetBatchStock([rowIndex],row.branch_position,"from")
           }else{
               window.g_queryStatus = 'querying'
               GetBranchPosition([rowIndex], rowBranchId, "from")
           }
        } else if (colName === 'branch_position') {
            row.branch_position_name = args.value?args.value.label:""
            row.branch_position = cellValue?cellValue:"0"
            window.g_queryStatus = 'querying'
            GetBatchStock([rowIndex],row.branch_position,"from")
        }
        else if (colName === 'batch_id') {
            let rowBranchId = row.branch_id
            let branchId = $("#branch_id").val()?$("#branch_id").val().value:"-1"
            rowBranchId = rowBranchId?rowBranchId:branchId
            let rowBranchPosition =row.branch_position?row.branch_position:"0"
            let key = row.item_id+"_" + rowBranchId+"_" + rowBranchPosition
            let produceDate = args.row.produce_date
            if (produceDate.length == 6) {
                produceDate = '20' + produceDate.substr(0, 2) + '-' + produceDate.substr(2, 2) + '-' + produceDate.substr(4, 2)
                row[colName] = produceDate
            }
            let batchNo = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, "batch_no")
            if(!batchNo) batchNo=""
            window.g_queryStatus = 'querying'
            setStockQty(key,rowIndex,produceDate,batchNo,"from","from")
        }
        else if (colName === 'batch_id_f') {
            let rowBranchId = row.branch_id
            let branchId = $("#branch_id").val()?$("#branch_id").val().value:"-1"
            rowBranchId = rowBranchId?rowBranchId:branchId
            let rowBranchPosition =row.branch_position?row.branch_position:"0"
            let key = row.item_id+"_" + rowBranchId+"_" + rowBranchPosition
            let batchNo = args.row.batch_no
           // row[batch_id] = cellValue
            let produceDate = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, "produce_date")
            if(!produceDate) produceDate=""
            window.g_queryStatus = 'querying'
            setStockQty(key,rowIndex,produceDate,batchNo,"from","from")
        }
        else if(colName=='allocate_amount'){
            if (!myIsNumber(cellValue)) cellValue = 0;
            cellValue = Math.round(parseFloat(cellValue) * 100) / 100;
            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, colName, cellValue);
        }
        else if(colName=='remark'){
            if(cellValue=="赠品" && oldValue!='赠品'){
                row.real_price=0;
                row.sub_amount=0;
                row.s_real_price=0;
                row.m_real_price=0;
                row.b_real_price=0;

                row.discount=''
                updateRowSubAmount(rowIndex)
                updateTotalAmount()
            }
         }

        setTimeout(() => {
            if (colName == 'allocate_amount'){
                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, colName, { value: cellValue, label: cellValue });
            }
            
            $('#jqxgrid').jqxGrid('updategrid')
            saveSheetToCach()
        },30);
        
         
    }

    function ReturnItemsRender() {
        return
        $("#jqxgrid").jqxGrid('beforeRowRender', function (divRow, rowData) {
            if(!rowData) return
            if (parseFloat(rowData.quantity) < 0) 
                divRow.style.color='#f00'                  
            else
                divRow.style.color='#000'
        })
        
    }
    
    function cellbeginedit(event) {
        console.log('cellbeginedit ' + new Date().getTime())
        return

        var args = event.args;
        var colName = args.datafield;

        var rowIndex = args.rowindex;

        /*
        setTimeout(() => {
            var cell = $("#jqxgrid").jqxGrid('getselectedcell');
            //if (cell.row != rowIndex || cell.column != colName) return
            //  window.g_editByCode = true
            //  var editable = $("#jqxgrid").jqxGrid('begincelledit', rowIndex, colName);
            //  window.g_editByCode = false
            $('#jqxgrid input').focus()
            console.log('当前为' + rowIndex)
        }, 200)*/

    }
    //检查没有商品名称但行内有数据的行
    function rowHasDataButNoItemID(row) {
        if (row.item_id) {
            return false;
        }
        if (row.sub_amount || row.real_price || row.quantity || row.orig_price) {
            return true
        }
    }
  
    function GetSheetData(isCopy=false) {
        
        var msg = "";
        var formData = getFormData();
        console.log(formData)
        var warning = "";
        if (formData.errMsg) {
            return
        }

        if (!formData.supcust_id) {
            msg = '客户/供应商未选择';
            return { result: 'Error', msg: msg }
        }
        if (!formData.seller_id) {
            if (formData.order_source) {
                formData.seller_id = -1 
            } else {
                //debugger
                var vv = $("#div_seller_id").css('display')

                if ($("#div_seller_id").css('display')!='none') {
                    msg = '业务员/经手人未选择'
                    return { result: 'Error', msg: msg } 
                }
                
            }
        }
        //if (!formData.branch_id) {
        //    msg = '请选择仓库';
        //    return { result: 'Error', msg: msg }
        //}
        var sheetType = $('#sheetType').val()
        
        if ( sheetType == 'X') {
            
            var elementShow = $("#div_senders_id").css('display')
            if (window.g_companySetting && window.g_companySetting.saleSheetNeedSender == 'True' && elementShow != 'none' && !formData.senders_id) {
                    msg = '送货员未选择'
                return { result: 'Error', msg: msg }
            }
        }

        if (sheetType == 'XD') {

            var elementShow = $("#div_senders_id").css('display')
            if (window.g_companySetting && window.g_companySetting.saleOrderSheetNeedSender == 'True' && elementShow != 'none' && !formData.senders_id) {
                msg = '送货员未选择'
                return { result: 'Error', msg: msg }
            }
        }
        
        var canSeeBuyPrice = false;
        if (window.g_operRights && window.g_operRights.delicacy
            && window.g_operRights.delicacy.seeInPrice
            && window.g_operRights.delicacy.seeInPrice.value) {
            canSeeBuyPrice = true;
        }
        var canSeeSalePrice = true;
        if (window.g_operRights.delicacy && window.g_operRights.delicacy.seeSalePrice && !window.g_operRights.delicacy.seeSalePrice.value) {
            canSeeSalePrice = false;
        }
        var canBelowMinSalePrice = false;
        if (window.g_operRights && window.g_operRights.delicacy
            && window.g_operRights.delicacy.belowMinSalePrice
            && window.g_operRights.delicacy.belowMinSalePrice.value) {
            canBelowMinSalePrice = true;
        }
         var canBelowCostPrice = true;
        if (window.g_operRights && window.g_operRights.delicacy
            && window.g_operRights.delicacy.belowCostPrice
            && window.g_operRights.delicacy.belowCostPrice.value!=undefined
            && window.g_operRights.delicacy.belowCostPrice.value.toString().toLowerCase()=='false'
        )
            
        {
            canBelowCostPrice = false;
        }
        formData.OperKey = g_operKey;
        var rows = $('#jqxgrid').jqxGrid('getrows');
        console.log(rows)
        var sheetRows = new Array;
        var existItems = {}
        var rowIndex = 0;//记录正确行的下标
        var total_sub_amount = 0; //小计的总和
        for (var i = 0; i < rows.length; i++) {
            var sheetRow = {};
            var row = $('#jqxgrid').jqxGrid('getrowdata', i);
            if (rowHasDataButNoItemID(row)) {
                msg = '第' + (i + 1) + '行存在数据但没有商品名称';
                return { result: 'Error', msg: msg }
            }
            if (myIsNumber(row.item_id)) {
                
                console.log(row)
                for (var fld in row) {
                    sheetRow[fld] = row[fld];
                }
                sheetRow.row_index = i
                /*var itemKey = sheetRow.item_id
                if(sheetRow.trade_type == "DH"){
                    itemKey = sheetRow.item_id +"_"+sheetRow.order_sub_id+"_" + sheetRow.order_price
                }*/
                if(sheetType=="TD" && Number(sheetRow.quantity)<0){
                     msg = '第' + (i+1)+ '行商品' + sheetRow.item_name + '数量为负';
                    break;
                }
                var itemKey = getQueriedItemsKeyFromRow(sheetRow)
                var item = window.g_queriedItems[itemKey]
                if (!item) {
                    msg = '第' + (i + 1) + '行商品' + sheetRow.item_name + '  需要重新选择';
                    break;
                }
                if (sheetRow.cost_price_avg === null) sheetRow.cost_price_avg = 0
                if (sheetRow.inout_flag === null) sheetRow.inout_flag = 0
                if (!sheetRow.produce_date) {
                    sheetRow.batch_id = "0"
                    sheetRow.produce_date = ''
                }
                if (!sheetRow.batch_no) sheetRow.batch_no = ''
                var rowBranchId = sheetRow.branch_id
                if (!rowBranchId) {
                    sheetRow.branch_id = ""
                    rowBranchId = formData.branch_id
                }
                if (!sheetRow.branch_position) sheetRow.branch_position = "0"
                //var itemInfo = existItems[sheetRow.item_id + sheetRow.produce_date + sheetRow.batch_no + rowBranchId + sheetRow.branch_position]
                //if (itemInfo && itemInfo.value === (sheetRow.item_id + sheetRow.produce_date + sheetRow.batch_no + rowBranchId + sheetRow.branch_position)) {
                //    msg = `第${i + 1}行 ${sheetRow.item_name}在${itemInfo.rowIndex}行已存在`
                //    break
                //}
                if (sheetRow.allocate_amount == "" || sheetRow.allocate_amount == null) sheetRow.allocate_amount = 0

                if (!rowBranchId && (!window.g_companySetting.openPlaceholderSheet || window.g_companySetting.openPlaceholderSheet == "False") && sheetType =="XD") {
                    msg = '第' + (i+1)+ '行商品' + sheetRow.item_name + '没有选择仓库';
                    break;
                }
                if (!rowBranchId && sheetType !=="XD") {
                    msg = '第' + (i+1)+ '行商品' + sheetRow.item_name + '没有选择仓库';
                    break;
                }
                 
                if (!sheetRow.unit_no) {
                    msg = '第' + (i+1)+ '行商品' + sheetRow.item_name + '没有选择单位';
                    break;
                }
                var unSame = false
                var itemUnitFactor=''
                if (item.units.length > 0) {
                    for (var j = 0; j < item.units.length; j++) {
                        var unit = item.units[j]
                        if(unit.unit_no == sheetRow.unit_no){
                            itemUnitFactor=unit.unit_factor
                            if (unit.unit_factor != sheetRow.unit_factor) {
                                unSame = true
                                break
                            }
                        }
                    }
                }
                if(unSame){
                    msg = '第' + (i + 1) + '行' + sheetRow.item_name + '的包装率错误,包装率'+ sheetRow.unit_factor + '和档案中的包装率'+ itemUnitFactor + '不匹配,请删除行重新录入商品';
                    break;
                }


                if(sheetRow.produce_date  && !isDateValid(sheetRow.produce_date)){
                        msg = '请输入第'+ (i+1)+'行商品正确的生产日期, 比如230901';
                        break;
                }
                if(!sheetRow.produce_date && sheetRow.batch_no){
                    msg = '请输入' + sheetRow.item_name + '的生产日期';
                        break;
                }
                var bHaveValidProduceDate = sheetRow.produce_date && sheetRow.produce_date != '无产期'
                var bHaveInvalidBatchlevel = !sheetRow.batch_level || sheetRow.batch_level === "0"
                if (bHaveInvalidBatchlevel && (sheetRow.batch_no || bHaveValidProduceDate)) {
                    if (isCopy){
                        sheetRow.batch_no = ""
                        sheetRow.batch_id = "0"
                        sheetRow.produce_date = "无产期"
                    }else{
                        msg = '第'+ (i+1)+'行商品没有开启严格产期/批次管理';
                        break;
                    }  
                }
                 if(sheetRow.batch_level=="1" && sheetRow.batch_no){
                     msg = '第'+ (i+1)+'行商品没有开启批次管理';
                     break;
                }
                /*
                暂时去掉，有些客户需要不严格录入产期，2024-12-16 大相要求加入校验
               
                }*/
                if (sheetRow.batch_level && !sheetRow.produce_date && (sheetType === 'X' || sheetType === 'XD' || sheetType === 'T' || sheetType === "JH" || sheetType === "HH")) {
                    msg = '请输入第' + (i + 1) + '行商品正确的生产日期, 或选择“无产期”';
                    break;
                }

                var gd = parseFloat(sheetRow.quantity);

                if (!parseFloat(sheetRow.quantity) && parseFloat(sheetRow.quantity)!= 0) {
                    msg = '请输入' + sheetRow.item_name + '的数量';
                    break;
                }
                else if (!myIsNumber(sheetRow.quantity)) {
                    msg = '第' + (i + 1) + '行' + sheetRow.item_name + '的数量输入错误，请输入正确的数量';
                    break;
                }
                if (sheetRow.trade_type === 'J' && parseFloat(sheetRow.quantity)>=0 && sheetType !== 'JH') {

                    msg = '第' + (i + 1) + '行' + sheetRow.item_name + '的借货数量，应小于0';
                    break;
                }
                if (sheetRow.trade_type === 'H' && parseFloat(sheetRow.quantity)<=0) {
                    msg = '第' + (i + 1) + '行' + sheetRow.item_name + '的还货数量，应大于0';
                    break;
                }
                if (sheetRow.order_qty && parseFloat(sheetRow.order_qty) > 0 && parseFloat(sheetRow.order_qty) - parseFloat(sheetRow.quantity*sheetRow.unit_factor) <= -0.01) {
                    msg = '第' + (i + 1) + '行' + sheetRow.item_name + '的数量大于可用还货数量';
                    break;
                }
      
                if ((sheetType == 'CG' ||sheetType == 'CT') && !canSeeBuyPrice) {
                   if (sheetRow.real_price === '') {
                       sheetRow.real_price = 0
                       sheetRow.sub_amount=0
                   }
                }
                if ((sheetType == 'X' ||sheetType == 'T') && !canSeeSalePrice) {
                   if (sheetRow.real_price === '') {
                       sheetRow.real_price = 0
                       sheetRow.sub_amount=0
                   }
                }                
                
                var v = myIsNumber(sheetRow.real_price)
                if (!myIsNumber(sheetRow.real_price)) {
                    msg = '第' + (i + 1) + '行' + sheetRow.item_name + '的价格输入错误，请输入正确的价格';
                    break;
                }
                var rrp = myIsNumber(sheetRow.recent_retail_price)
                if (!myIsNumber(sheetRow.recent_retail_price) && (sheetRow.recent_retail_price)){
                    msg = '第' + (i + 1) + '行' + sheetRow.item_name + '的最近零售价输入错误，请输入正确的零售价';
                }
                // if (!sheetRow.orig_price) {
                //     sheetRow.orig_price = row.orig_price;
                // }
                if (myIsNumber(sheetRow.orig_price)) {
                    if(!sheetRow.discount){
                        sheetRow.discount = parseFloat(sheetRow.real_price) / parseFloat(sheetRow.orig_price) * 100
                        if (!myIsNumber(sheetRow.discount)) sheetRow.discount = ''
                        sheetRow.discount = toMoney(sheetRow.discount, 1)
                    }
                  
                    sheetRow.discount_amount = toMoney((sheetRow.orig_price - sheetRow.real_price) * sheetRow.quantity)
                    sheetRow.orig_amount = toMoney(sheetRow.orig_price * sheetRow.quantity)
                }

                if ((sheetType == 'X' || sheetType == 'XD') && (sheetRow.trade_type == 'X'||sheetRow.trade_type == '')) {
                     var price = 0
                     for (var j = 0; j < item.units.length; j++) {
                          var unit = item.units[j]
                          if (unit.unit_no == sheetRow.unit_no) {
                             price = Number(unit.lowest_price)
                          }
                     }
                  if (sheetRow.real_price < price -0.01 && sheetRow.real_price > 0) {
                        if(!canBelowMinSalePrice) {
                               msg = '第' + (i + 1) + '行' + sheetRow.item_name + '的价格低于最低售价' + price + '，请输入正确的价格';
                        }else{
                               warning = '第' + (i + 1) + '行' + sheetRow.item_name + '的价格低于最低售价' + price;
                        }
                    }

                  if (parseFloat(sheetRow.cost_price) > 0 && parseFloat(sheetRow.real_price) < parseFloat(sheetRow.cost_price) && sheetRow.real_price > 0)
                    {
                         if(!canBelowCostPrice) {
                               msg = '第' + (i + 1) + '行' + sheetRow.item_name + '的价格低于成本价';
                         }else{
                               warning = '第' + (i + 1) + '行' + sheetRow.item_name + '的价格低于成本价';
                         }
                       
                    }
                        
                }
                if(sheetType === "HH") {
                    let borrow_mode = $('#borrow_mode').val();
                    if(borrow_mode.value === 'AMT') {
                        let availableAmount = toMoney($('#available_amount').val(), 4);
                        let sheet_amount = toMoney($('#total_amount').val(), 4);
                        let target_borrow_sheet_id = $('#target_borrow_sheet_id').val();
                        if(!target_borrow_sheet_id.value) {
                            msg = '请指定对应还货的借货单';
                            break;
                        }
                        if(availableAmount < sheet_amount) {
                            msg = '还货总金额大于借货单据可还金额';
                                break;
                        }
                    }
                   
                }
                // 2025.02.18 - #6455
                // 对于退货商品行，如果将它的数量修改为正数，那么trade_type自动转为X
                 if(sheetType=='X' ||sheetType=='XD'){
                    
                       if (parseFloat(sheetRow.quantity) >= 0) {
                           if (sheetRow.trade_type == 'T' || !sheetRow.trade_type) {
                                sheetRow.trade_type = 'X'
                           }
                       }
                       else if (parseFloat(sheetRow.quantity) < 0) {
                               if (sheetRow.trade_type == 'X' || !sheetRow.trade_type) {
                                sheetRow.trade_type = 'X'
                           }
                       }
                 }
              


                //if(isNaN(sheetRow.orig_price)) {
                //    msg = sheetRow.item_name + '的原价输入错误，请输入正确的原价';
                //    break;
                //}
                if (!myIsNumber(sheetRow.sub_amount)) {
                    msg = sheetRow.item_name + '的小计金额输入错误，请输入正确的小计金额';
                    break;
                }
                if (row.order_sub_id&&row.order_sub_id!="-1" && row.sub_amount != 0) {
                    if (row.order_sub_id != formData.payway1_id && row.order_sub_id != formData.payway2_id && row.order_sub_id != formData.payway3_id) {
                        msg = '支付方式中未使用' + sheetRow.item_name + '所使用的定货款账户';
                        break;
                    }
                }

                if (myIsNumber(row.sub_amount)) total_sub_amount += parseFloat(row.sub_amount)
                if (sheetRow.attr_qty && sheetRow.attr_qty.forEach) {
                    sheetRow.attr_qty = JSON.stringify(sheetRow.attr_qty)
                }
                if (sheetRow.mum_attributes) delete sheetRow.mum_attributes
                /*if (sheetRow.noStockAttrs && Object.prototype.toString.call(sheetRow.noStockAttrs) == '[object Object]') {
                    sheetRow.noStockAttrs = JSON.stringify(sheetRow.noStockAttrs)
                }*/
                //existItems[sheetRow.item_id + sheetRow.produce_date + sheetRow.batch_no + rowBranchId + sheetRow.branch_position] = {
                //    value: sheetRow.item_id + sheetRow.produce_date + sheetRow.batch_no + rowBranchId + sheetRow.branch_position,
                //    rowIndex: Number(i + 1)
                //}
                sheetRows[rowIndex++] = sheetRow;
            }
        }
        if (msg) return { result: 'Error', msg: msg }
        if (sheetRows.length == 0) {
            return { result: 'Error', msg: '请输入至少一行商品' }
        }

        if (!formData.now_disc_amount) formData.now_disc_amount = 0;
        
        msg = numberCheck(formData.now_disc_amount)
        if(msg) return { result: 'Error', msg: '优惠金额输入错误:'+ msg }

        if (formData.payway1_amount && !myIsNumber(formData.payway1_amount)) {
            msg =  '支付方式1金额输入错误'; 
        }
        if (formData.payway2_amount && !myIsNumber(formData.payway2_amount)) {
            msg =  '支付方式2金额输入错误'; 
        }
        if (formData.payway3_amount && !myIsNumber(formData.payway3_amount)) {
            msg =  '支付方式2金额输入错误'; 
        }
        if(msg) return { result: 'Error', msg: msg }

        if (!formData.payway1_amount) formData.payway1_amount = 0;
        if (!formData.payway2_amount) formData.payway2_amount = 0;
        if (!formData.payway3_amount) formData.payway3_amount = 0;
        if (!formData.other_fee1_amount) formData.other_fee1_amount = 0;
        if (!formData.other_fee2_amount) formData.other_fee2_amount = 0;
        /*改为后端校验了，前端就不校验，便于保存发现问题
         if (Math.abs(formData.total_amount - total_sub_amount >= 1)) {
            msg = `明细合计金额${total_sub_amount}与总额不等${formData.total_amount}，请重新输入任意一行商品的价格`
            return { result: 'Error', msg: msg };
        }*/
        if (parseFloat(formData.payway2_amount) != 0 && !formData.payway2_id) {
            msg = '请选择第二种支付方式';
            return { result: 'Error', msg: msg }
        }
        if (parseFloat(formData.payway3_amount) != 0 && !formData.payway3_id) {
            msg = '请选择第三种支付方式';
            return { result: 'Error', msg: msg }
        }
        if (formData.payway3_amount && !formData.payway2_amount) {
            formData.payway2_amount = formData.payway3_amount;
            formData.payway2_id = formData.payway3_id;
            formData.payway3_amount = 0;
            formData.payway3_id = '';
        }
        if (formData.payway2_amount && !formData.payway1_amount) {
            formData.payway1_amount = formData.payway2_amount;
            formData.payway1_id = formData.payway2_id;
            formData.payway2_amount = 0;
            formData.payway2_id = '';
        }
        if (window.dispAccounts) {
            window.dispAccounts.forEach(row => {
                if ((formData.payway1_id == row.sub_id && parseFloat(formData.payway1_amount || 0) > parseFloat(row.disp_amount)) || (formData.payway2_id && formData.payway2_id == row.sub_id && parseFloat(formData.payway2_amount) > parseFloat(row.disp_amount))) {
                    msg = '请勿超过' + row.sub_name + '支出账户的余额';
                    return { result: 'Error', msg: msg }
                }
            })
        }


        formData.now_pay_amount = parseFloat(formData.payway1_amount) + parseFloat(formData.payway2_amount)+ parseFloat(formData.payway3_amount);
        formData.paid_amount = formData.now_pay_amount;
        formData.disc_amount = formData.now_disc_amount;
        formData.left_amount = formData.total_amount - formData.now_pay_amount - formData.now_disc_amount

        var totalQty = $("#jqxgrid").jqxGrid('getcolumnaggregateddata', 'quantity', ['xj']).qty;
        if (formData.total_quantity != totalQty) formData.total_quantity = totalQty;

        // 附件图片处理
        debugger
        console.log('readItemImages started');
        // 这里的数据结构需要和后端处理照片的方法配合
         let images = { "photos": [] };

        if (window.appendixSrcList) {
            for (let img of window.appendixSrcList) {

                images.photos.push(img)

            }
        }

        if(images.photos.length>0){
            formData.appendix_photos =  JSON.stringify(images); //Activate infact.
            $('#appendix_photos').val(JSON.stringify(images)); // Save for possible use.
        }
      

        formData.SheetRows = sheetRows;
        if (window.g_companySetting) {
            formData.company_name = window.g_companySetting.companyName
            formData.company_tel = window.g_companySetting.contactTel
            formData.company_address = window.g_companySetting.companyAddress
        }

        /*if (window.g_companySetting && window.g_companySetting.happenTimeOnSave == 'True') {
            
            formData.TempHappenTime = 'false'
        }*/
        if (warning) return { result: 'Warning', warning: '价格警告：\n' + warning, sheet: formData}
        var result = msg ? 'Error' : "OK";
        return { result: result, msg: msg, sheet: formData };
    }
    
    function mmSheetInit(sheetType) {
        var sheet_type = sheetType;
        var clientView = 'ClientsView';
         let windowHeight = document.body.offsetHeight-50
         let windowWidth = document.body.offsetWidth-80
        if (sheetType == "CG" || sheetType == "CT" || sheetType == "CD") {
            clientView = 'SuppliersView';
        }
        if (window.g_companySetting && window.g_companySetting.happenTimeOnSave == 'True') {
            $('#TempHappenTime').val('false')
        }
        $('#supcust_id').jqxInput({
            onButtonClick: function (event) {
                $('#popClient').jqxWindow('open');
                $("#popClient").jqxWindow('setContent', `<iframe src="/BaseInfo/${clientView}?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
            }
        });
        $("#popClient").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 660, width: 1000, maxHeight:windowHeight,maxWidth:windowWidth,theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
        $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 800, width: 1200, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
        if ($("#popItemHistory").length>0) $("#popItemHistory").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 1000, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
        $('#branch_id').on('change', function (a, b) {
            var rowsData = $("#jqxgrid").jqxGrid('getrows')
            let rowindexs = []
            for (let i = 0; i < rowsData.length; i++) {
                let row = rowsData[i]
                if (!row.item_id || row.branch_id) continue
                rowindexs.push(i)
            }
            if(rowindexs.length>0){
                var branch_id = $('#branch_id').val().value
                branch_id = branch_id ? branch_id : "-1"
                GetBranchPosition(rowindexs, branch_id, "from","from")
            }
        })
        //$('#branch_id').on('change', function () {
        //    console.log("仓库改变了")
        //    var supcust_id = $('#supcust_id').jqxInput('val')
        //    var branch_id = $('#branch_id').jqxInput('val')
        //    var rows = $('#jqxgrid').jqxGrid('getrows');
        //    var sheetRows = new Array;
        //    var items_id = '' 
        //    for (var i = 0; i < rows.length; i++) {
        //        var sheetRow = {};
        //        var row = $('#jqxgrid').jqxGrid('getrowdata', i);
        //        if (myIsNumber(row.item_id)) {
        //            for (var fld in row) {
        //                sheetRow[fld] = row[fld];
        //            }
        //            sheetRows[i] = sheetRow;
        //        }
        //    }
        //    supcust_id = supcust_id.value
        //    branch_id = branch_id.value

        //    sheetRows.forEach(function (row) {
        //        if (items_id != '') items_id += ','
        //        items_id += row.item_id
        //    })
        //    var rowIndex = 0;
        //    if (branch_id && supcust_id && items_id) {
        //        $.ajax({
        //            url: '/api' + window.pageInfo.url + '/GetItemsInfo',
        //            type: 'GET',
        //            contentType: 'application/json',
        //            data: { operKey: g_operKey, items_id: items_id, supcust_id: supcust_id, branch_id: branch_id },
        //            success: function (data) {
        //                if (data.result === 'OK') {
        //                    if (!window.g_queriedItems) window.g_queriedItems = {}
                            
        //                    rows.forEach(function (row) {

        //                        var item = null
        //                        for (var item_id in data.items) {
        //                            var curItem = data.items[item_id]

        //                            if (row.item_id == curItem.item_id) {
        //                                item = curItem
        //                                var key = getQueriedItemsKeyFromRow(row)
        //                                window.g_queriedItems[key] = item
        //                            }
        //                        }
        //                        if (!item) return
        //                        let stockCol = $('#jqxgrid').jqxGrid('getcolumn', 'stock_qty_unit');
        //                            if (stockCol ) $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'stock_qty_unit', item.stock_qty_unit)
        //                        let usableStockCol = $('#jqxgrid').jqxGrid('getcolumn', 'usable_stock_qty_unit');
        //                        if (usableStockCol ) $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'usable_stock_qty_unit', item.usable_stock_qty_unit)
        //                        item.units.forEach(function(unit) {
        //                            var rowFactor = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'unit_factor');
        //                            if (unit.unit_factor == rowFactor && window.g_companySetting.rememberBuyPriceByBranch && (sheet_type == "CG" || sheet_type == "CT") ) {
        //                                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'real_price', unit.price);
        //                                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'orig_price', unit.recent_orig_price);
        //                                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'last_time_price', unit.recent_price);
        //                                var qty = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'quantity');
        //                                if(qty) {
        //                                    let sub_amount = toMoney(parseFloat(qty)*parseFloat(unit.price),2)
        //                                    $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'sub_amount', sub_amount)
        //                                }
        //                            }
        //                        });
        //                        rowIndex++
        //                    })
        //                }
        //            }
        //        })
        //    }
        //})

        /*
        var penSvg = `<svg id="penSvg" onclick="onAddBrief()" height="15" width="15" style="position:absolute;right:2px;bottom:6px;z-index:999;cursor:pointer">
                                    <use xlink:href="/images/images.svg#pen" />
                      </svg>`;
        $('#make_brief').append(penSvg)
        $('#penSvg').hide();
        */

        var approve_time = $('#approve_time').text();
        if (approve_time) {
            $('#btnApprove').attr('disabled', true);
            $("#btnSave").attr('disabled', true);
            $('#penSvg').show();
        }
        $('#no_disc_amount').jqxInput('disabled', true);

        var sheet_id = $('#sheet_id').jqxInput('val');
        var payway1_amount = $('#payway1_amount').jqxInput('val');
        var payway2_amount = $('#payway2_amount').jqxInput('val');
        var payway3_amount = $('#payway3_amount').jqxInput('val');
        var now_disc_amount = $('#now_disc_amount').jqxInput('val');
        if (!sheet_id) {
            if (payway1_amount == '0')
                $('#payway1_amount').jqxInput('val', '');
            if (payway2_amount == '0')
                $('#payway2_amount').jqxInput('val', '');
            if (payway3_amount == '0')
                $('#payway3_amount').jqxInput('val', '');
            if (now_disc_amount == '0')
                $('#now_disc_amount').jqxInput('val', '');
        }
        $('#now_disc_amount').on('input', function () {
            //if (ignoreChange) return;
            //debugger
            updatePayAmount();
        });
         $('#now_disc_amount').on('change', function () {
            //if (ignoreChange) return;
            //debugger
            updatePayAmount();
        });
        $('#no_disc_amount').on('input', function () {
            if (ignoreChange) return;
            updatePayAmount();
        });

        $('#payway1_amount').on('input', function () {
            if (ignoreChange) return;
            updatePayAmount(true);
        });
        $('#payway1_id').on('optionSelected', (a, b) => {
            
            var payway = $('#payway1_id').jqxInput('val')
            $('#payway1_type').val(payway.sub_type)
        });
        $('#payway2_id').on('optionSelected', (a, b) => {
            var payway = $('#payway2_id').jqxInput('val')
            $('#payway2_type').val(payway.sub_type)
        });
        $('#payway3_id').on('optionSelected', (a, b) => {
            var payway = $('#payway3_id').jqxInput('val')
            $('#payway3_type').val(payway.sub_type)
        });
        $('#payway1_amount').on('change', function () {
            if (ignoreChange) return;
            updatePayAmount(true);
        });

        $('#payway2_amount').on('input', function () {
            updatePayAmount(true);
        });
        $('#payway3_amount').on('input', function () {
            updatePayAmount(true);
        });
        window.g_init_payway1_amount = parseFloat($('#payway1_amount').jqxInput('val'))
        window.g_init_left_amount = parseFloat($('#left_amount').jqxInput('val'))

        if (parseFloat(payway2_amount))
            onShowGroupBtnClicked('payway');
        if (parseFloat(payway3_amount))
            onShowGroupBtnClicked('payway');

        $('#jqxgrid').on('cellclick', (event) => {
            addEmptyRowsAtTail(4, 20);
        });

        var prepay_sub_ids = $('#prepay_sub_ids').val()

        var sheetType = $('#sheetType').val()
        
        $('#payway1_id,#payway2_id,#payway3_id').jqxInput({
            beforeRenderListItem: function (payway) {

                /*if (sheetType == 'T' || sheetType == 'TD') {
                    if (payway.payway_type == 'YS')
                        return true
                }*/
                 if (payway.sub_type == 'YS' ||payway.sub_type == 'YF'){
                    var no_disc_amount = $('#no_disc_amount').val();
                   
                    // if(parseFloat(no_disc_amount)<0 || sheetType=='T'|| sheetType=='TD'|| sheetType=='CT')
                    // {
                    //         return true
                    // }
                    if (parseFloat(no_disc_amount) < 0 || sheetType == 'T' || sheetType == 'TD') {
                        return true
                    }
                    // 采购单、采购退货单、采购订单直接显示预付款
                    if (sheetType == 'CG' || sheetType == 'CT' || sheetType == 'CD') {
                        return true
                    }
                 }
                if (window.g_operRights && window.g_operRights.delicacy && window.g_operRights.delicacy.allowNegativePrepay && window.g_operRights.delicacy.allowNegativePrepay.value) {
                    return true;
                }
            
                if (prepay_sub_ids.indexOf(payway.v) != -1) {

                    var bHasBalance = false
                        
                    if (window.prepayAccounts) {
                        window.prepayAccounts.forEach((acct) => {
                            if (acct.sub_id == payway.v)
                                bHasBalance = true
                        })
                    }
                    /*
                    if (window.dispAccounts) {
                        window.dispAccounts.forEach((acct) => {
                            if (acct.sub_id == payway.v)
                                bHasBalance = true
                        })
                    }*/

                        return bHasBalance
                }
                else {
                     return true
                }
            }
        })

          var divMenu=`<div id='gridMenu'>
                    <ul>
                        <li id='copyRow'>复制</li> 
                        <li id='addOtherUnitRow'>加其他单位(+)</li>
                        <li id='hideCostPrice'>隐藏成本(F8)</li> 
                        <li id='itemHistory'>历史记录</li>
                    </ul>
                   </div>`;
                   if(window.getGridMenuHtml){
                       divMenu=window.getGridMenuHtml()

                   }
          $('body').append(divMenu);
          contextMenu = $('#gridMenu').jqxMenu({ width: 200, height: 200, autoOpenPopup: false, mode: 'popup'});

            $('#jqxgrid').on('rowclick', function (event) {
                if (event.args.rightclick) {
                    
                    // $('#jqxgrid').jqxGrid('selectrow', event.args.rowindex);
                    //var row = $('#jqxgrid').jqxGrid('getrowdata', event.args.rowindex);
                    GridData.columns.forEach(col => {
                        $('#jqxgrid').jqxGrid('selectcell', event.args.rowindex,col.datafield);
                    })

                    var scrollTop = $(window).scrollTop();
                    var scrollLeft = $(window).scrollLeft();
                    contextMenu.jqxMenu('open', parseInt(event.args.originalEvent.clientX) + 5 + scrollLeft, parseInt(event.args.originalEvent.clientY) + 5 + scrollTop);
                    return false;
                }
            });
          $('#gridMenu').on('itemclick', function (event) {
              var args = event.args;
              var menuItemID = $(args).attr('id')

              if (menuItemID == 'copyRow') {
                  funcCopyRow()
              }
              else if (menuItemID == 'copyText') {
                 document.execCommand('Copy');
              } 
              else if (menuItemID == 'addOtherUnitRow') {
                  funcAddOtherUnitRow()
              }
              else if (menuItemID == 'exchangeItem') {
                    funcExchangeItem()
              } 
              else if (menuItemID == 'hideCostPrice') {
                  hideCostPrice()
              }
              else if (menuItemID == 'changeToBorrowItem') {
                  funcChangeToBorrowItem()
              }
              else if (menuItemID == 'itemHistory') {

                  btnItemHistory_click()
              }
              else if (menuItemID == 'customerHistory') {
                  btnCustomerHistory_click()
              }

          })
 
          window.funcCopyRow = function () {
                var cell = $('#jqxgrid').jqxGrid('getselectedcell');
                var rowIndex = cell.rowindex;
                //$("#jqxgrid").jqxGrid('endcelledit', rowIndex, "quantity", false);
                var rowData = $('#jqxgrid').jqxGrid('getrowdata',rowIndex);

                var key = getQueriedItemsKeyFromRow(rowData)
                var item = window.g_queriedItems[key]
                // var nextUnit=null
                // item.units.some(unit => {
                //     if (unit.unit_no != rowData.unit_no) {
                //         nextUnit=unit
                //         return true
                //     }
                // })
                // if (!nextUnit) return
                //原逻辑为没有其他单位不能复制

                newRow = JSON.parse(JSON.stringify(rowData))

                $("#jqxgrid").jqxGrid('addrow', null, newRow, rowIndex + 1)
                $('#jqxgrid').jqxGrid('clearselection',true)
                $('#jqxgrid').jqxGrid('selectcell',rowIndex+1,'quantity')
                $('#jqxgrid').jqxGrid('focus');
            updateTotalAmount()
        }
          window.funcAddOtherUnitRow = function () {
                var cell = $('#jqxgrid').jqxGrid('getselectedcell');
                var rowIndex = cell.rowindex;
                //let rowIndex = window.SaleRowIndex
                $("#jqxgrid").jqxGrid('endcelledit', rowIndex, "quantity", false);
                var rowData = $('#jqxgrid').jqxGrid('getrowdata',rowIndex);

                var key = getQueriedItemsKeyFromRow(rowData)
                var item = window.g_queriedItems[key]
                var nextUnit=null
                item.units.some(unit => {
                    if (unit.unit_no != rowData.unit_no) {
                        nextUnit=unit
                        return true
                    }
                })
                if (!nextUnit) return

                newRow = JSON.parse(JSON.stringify(rowData))
                newRow.unit_no = nextUnit.unit_no
                newRow.unit_factor = nextUnit.unit_factor
                newRow.orig_price = nextUnit.recent_orig_price
                newRow.unit_relation1 = getRowUnitRelation(newRow)
                newRow.real_price = nextUnit.price
                if(newRow.trade_type === 'J') newRow.real_price = 0
                newRow.sys_price = nextUnit.price
                newRow.sub_amount = ''
                newRow.quantity = ''

                $("#jqxgrid").jqxGrid('addrow', null, newRow, rowIndex + 1)
                $('#jqxgrid').jqxGrid('clearselection',true)
                $('#jqxgrid').jqxGrid('selectcell',rowIndex+1,'quantity')
                $('#jqxgrid').jqxGrid('focus');
        }

              window.funcExchangeItem = function () {
                var cell = $('#jqxgrid').jqxGrid('getselectedcell');
                var rowIndex = cell.rowindex;
                //$("#jqxgrid").jqxGrid('endcelledit', rowIndex, "quantity", false);
                var rowData = $('#jqxgrid').jqxGrid('getrowdata',rowIndex);

                var key = getQueriedItemsKeyFromRow(rowData)
                var item = window.g_queriedItems[key]
                   
                newRow = JSON.parse(JSON.stringify(rowData))
                newRow.quantity=newRow.quantity*(-1)
                newRow.sub_amount=newRow.sub_amount*(-1)
                if(rowData.quantity>0){
                    rowData.trade_type='HC'
                    rowData.remark='换出'
                    newRow.trade_type='HR'
                    newRow.remark='换入'
                        
                }
                else
                {
                    rowData.trade_type='HR'
                    rowData.remark='换入'
                    newRow.trade_type='HC'
                    newRow.remark='换出'

                }

                

                $("#jqxgrid").jqxGrid('addrow', null, newRow, rowIndex + 1)
                $('#jqxgrid').jqxGrid('clearselection',true)
                $('#jqxgrid').jqxGrid('selectcell',rowIndex+1,'quantity')
                $('#jqxgrid').jqxGrid('focus');

                updateTotalAmount()
            }

            var TempHappenTime = $('#TempHappenTime').val() || ''
       
        
       
         var approve_time=$('#approve_time').val()
         if(TempHappenTime.toLowerCase()=="true" && !approve_time){
              $('#happen_time').val('')
         }
        $('#happen_time').on('change', function (event) 
        {
            var value=$('#happen_time').val()
            if(value)
                $('#TempHappenTime').val('false')
             else  $('#TempHappenTime').val('true')
 
        })
        
        window.funcChangeToBorrowItem = function() {
            if(sheetType == "JH" || sheetType == "HH") {
                return
            }
            var cell = $('#jqxgrid').jqxGrid('getselectedcell');
            var rowIndex = cell.rowindex;


            gridRows = $('#jqxgrid').jqxGrid('getrows');
            let gridRow = gridRows[rowIndex]

            gridRow.real_price = 0;
            gridRow.sub_amount = 0;
            gridRow.trade_type = 'J';
            gridRow.trade_type_name = '借货';
            if(gridRow.quantity && parseFloat(gridRow.quantity)>0 ) gridRow.quantity *= (-1)
            gridRow.remark = '借货';
            $('#jqxgrid').jqxGrid('updategrid')

            updateTotalAmount()
        }

        $('#jqxgrid').keydown(function (e) {
             var cell = $('#jqxgrid').jqxGrid('getselectedcell');
            if (cell && cell.rowindex) {
                window.SaleRowIndex = cell.rowindex;
            }
                if (e.which == 119) {//F8
                   if(window.hideCostPrice) hideCostPrice()
                }
                else if (e.which == 187) {//+
                    console.log("点击了+号")
                    funcAddOtherUnitRow()
                    return false
                }
        })
        
    }

    function mmRefreshStockQty() {
        
        console.log(window.g_queriedItems)
        if (!window.g_queriedItems) return
        var branch_id = $('#branch_id').jqxInput('val')
        //if (!branch_id) return
        var rows = $('#jqxgrid').jqxGrid('getrows');
        var rowIndex = 0;
        rows.forEach(function (row) {
            //var key = getQueriedItemsKeyFromRow(row)
            let rowBranchId = row.branch_id?row.branch_id:($("#branch_id").val()?$("#branch_id").val().value:"-1")
            let rowBranchPosition = row.branch_position?row.branch_position:"0"
            let rowBatchId = row.batch_id?row.batch_id:"0"
                let key = row.item_id + ","+rowBranchId + "_"+rowBranchPosition + "_"+ rowBatchId
            var item = g_queriedItems_bySheetRow[key]
            var column = $('#jqxgrid').jqxGrid('getcolumn', 'stock_qty_unit'); 
            if (column && item && item.stock_qty_unit) row.stock_qty_unit=item.stock_qty_unit//   $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'stock_qty_unit', item.stock_qty_unit)
            var column = $('#jqxgrid').jqxGrid('getcolumn', 'usable_stock_qty_unit'); 
            if (column && item && item.sell_pend_qty_unit) row.sell_pend_qty_unit = item.sell_pend_qty_unit//   $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'stock_qty_unit', item.stock_qty_unit)

            if (column && item && item.usable_stock_qty_unit) row.usable_stock_qty_unit = item.usable_stock_qty_unit // $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'usable_stock_qty_unit', item.usable_stock_qty_unit)
            rowIndex++
        })
       // $('#jqxgrid').jqxGrid('updategrid')
    
    }
   /* 不需要,使用已有的办法
            在 onGet方法中 
            sheet = (SheetSale)this.Sheet;//加上
            await GetItemsInfoBySheetInfo(this.company_id, items_id, new { sheet.supcust_id, sheet.branch_id });
    
    function refreshStockQty_orderToSale() {
           
        var supcust_id = $('#supcust_id').jqxInput('val')
        var branch_id = $('#branch_id').jqxInput('val')
        var rows = $('#jqxgrid').jqxGrid('getrows');
        var sheetRows = new Array;
        var items_id = ''
        for (var i = 0; i < rows.length; i++) {
            var sheetRow = {};
            var row = $('#jqxgrid').jqxGrid('getrowdata', i);
            if (myIsNumber(row.item_id)) {
                for (var fld in row) {
                    sheetRow[fld] = row[fld];
                }
                sheetRows[i] = sheetRow;
            }
        }
        supcust_id = supcust_id.value
        branch_id = branch_id.value

        sheetRows.forEach(function (row) {
            if (items_id != '') items_id += ','
            items_id += row.item_id
        })
        var rowIndex = 0;
        if (branch_id && supcust_id && items_id) {
            $.ajax({
                    url: '/api' + window.pageInfo.url + '/GetItemsInfo',
                    type: 'GET',
                    contentType: 'application/json',
                    data: { operKey: g_operKey, items_id: items_id, supcust_id: supcust_id, branch_id: branch_id },
                    success: function (data) {
                        if (data.result === 'OK') {
                            if (!window.g_queriedItems) window.g_queriedItems = {}
                            
                            rows.forEach(function (row) {

                                var item = null
                                for (var item_id in data.items) {
                                    var curItem = data.items[item_id]

                                    if (row.item_id == curItem.item_id) {
                                        item = curItem
                                        var key = getQueriedItemsKeyFromRow(row)
                                        window.g_queriedItems[key] = item
                                    }
                                }
                                if (!item) return
                                let stockCol = $('#jqxgrid').jqxGrid('getcolumn', 'stock_qty_unit');
                                    if (stockCol ) $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'stock_qty_unit', item.stock_qty_unit)
                                let usableStockCol = $('#jqxgrid').jqxGrid('getcolumn', 'usable_stock_qty_unit');
                                if (usableStockCol ) $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'usable_stock_qty_unit', item.usable_stock_qty_unit)
                                item.units.forEach(function(unit) {
                                    var rowFactor = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'unit_factor');
                                    if (unit.unit_factor == rowFactor && window.g_companySetting.rememberBuyPriceByBranch && (sheet_type == "CG" || sheet_type == "CT") ) {
                                        $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'real_price', unit.price);
                                        $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'orig_price', unit.recent_orig_price);
                                        $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'last_time_price', unit.recent_price);
                                        var qty = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'quantity');
                                        if(qty) {
                                            let sub_amount = toMoney(parseFloat(qty)*parseFloat(unit.price),2)
                                            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'sub_amount', sub_amount)
                                        }
                                    }
                                });
                                rowIndex++
                            })
                        }
                    }
                })
         }
    }*/
    function enableHideCostColumn() {
         
        var costColumns = {}
        var column = $('#jqxgrid').jqxGrid('getcolumn', 'cost_price')
        if (column) costColumns[column.datafield] = { hidden: column.hidden }
        column = $('#jqxgrid').jqxGrid('getcolumn', 'cost_amount')
        if (column) costColumns[column.datafield] = { hidden: column.hidden }
        column = $('#jqxgrid').jqxGrid('getcolumn', 'profit')
        if (column)  costColumns[column.datafield]={ hidden: column.hidden }
         column = $('#jqxgrid').jqxGrid('getcolumn', 'profit_rate')
        if (column) costColumns[column.datafield] = { hidden: column.hidden }

        function setCostColumnVisible(colName) {
            if (costColumns[colName] && !costColumns[colName].hidden) {
                var col = $('#jqxgrid').jqxGrid('getcolumn', colName)
               // col.hidden = !col.hidden
                if (col.hidden) {
                    $('#jqxgrid').jqxGrid('showcolumn',colName)
                }
                else
                    $('#jqxgrid').jqxGrid('hidecolumn', colName)

            }
        }
        window.hideCostPrice = function () {
            setCostColumnVisible('cost_price')
            setCostColumnVisible('cost_amount')
            setCostColumnVisible('profit')
            setCostColumnVisible('profit_rate')
            $('#jqxgrid').jqxGrid('refresh')
        }
    }

 
    function AutoSupply() {
        window.open("../");
    }

   
    window.addEventListener('message', function (rs) {
        var saleAndOrderRemeberSeller = true;
        if (window.g_operRights && window.g_operRights.delicacy && window.g_operRights.delicacy.saleAndOrderRemeberSeller && !window.g_operRights.delicacy.saleAndOrderRemeberSeller.value) {
            saleAndOrderRemeberSeller = false;
        }
        var orderRememberBranch = false;
        if (window.g_operRights && window.g_operRights.delicacy && window.g_operRights.delicacy.saleOrderDefaultBranch && window.g_operRights.delicacy.saleOrderDefaultBranch.value && window.g_operRights.delicacy.saleOrderDefaultBranch.value == 'lasttime') {
            orderRememberBranch = true;
        }
        var saleRememberBranch = true;
        if (window.g_operRights && window.g_operRights.delicacy && window.g_operRights.delicacy.saleRememberBranch && !window.g_operRights.delicacy.saleRememberBranch.value) {
            saleRememberBranch = false;
        }
         
        var branch_id = $('#branch_id').jqxInput('val').value
        var oldSup = $('#supcust_id').jqxInput('val')
        if (rs.data.msgHead === "ClientsView") {
            if (rs.data.action === "select") {
                let newSup = rs.data.supcust_id;
                const sup_name = rs.data.sup_name;
                const acct_type = rs.data.acct_type;
                //if(acct_type){
                    //window.g_acct_type = acct_type;
                    //$('#acct_type').jqxInput('val', acct_type);
                //}
                //debugger
                let sheetType =  $('#sheet_type').val();
                //sheetType == 'SHEET_SALE' &&
                if (oldSup.value != newSup && $('#jqxgrid').jqxGrid('getrows').filter(r => r.item_id != '' && r.item_id != undefined).length > 0) {
                    
                    $('#cancle').click(function() {
                        $('#changeClientAlertBox').hide()
                        $('#popClient').jqxWindow('close');

                    })
                    $('#confirm').click(function() {
                        
                        let refreshFlag = $("input[name='changeClient']:checked").val()
                        if (sheetType == 'SHEET_SALE') {
                            SetBranchAndSellerOnSupcustUpdate(newSup, saleRememberBranch, saleAndOrderRemeberSeller)
                        }
                        else if (sheetType == 'SHEET_SALE_DD') {
                            SetBranchAndSellerOnSupcustUpdate(newSup, orderRememberBranch, saleAndOrderRemeberSeller)
                        }
                        if (refreshFlag == 'true') {
                                    
                                     $('#supcust_id').jqxInput('val', { value: newSup, label: sup_name })
                                      if (window.getClientAccountInfo)
                                      getClientAccountInfo(newSup,true)
                                      $('#popClient').jqxWindow('close');
                                      $('#changeClientAlertBox').hide()
                                       if (oldSup && oldSup.value === newSup) return // 如果新选择客户与之前相同。返回
                                       refreshRowsBySupChange(newSup, branch_id)
                        } else {
                                
                                    let n = GetFirstEmptyRowIndex()
                                    if (n === 0) return
                                    let hasOrderItem = false
                                    const gridRows = $('#jqxgrid').jqxGrid('getrows');
                                    let index = 0
                                    var rows = []
                                    gridRows.forEach((row) => {
                                        if(row.order_sub_id) hasOrderItem=true
                                        if (!row.item_id || row.order_sub_id || row.disp_flow_id) return 
                                        rows[index] = row
                                        index++
                                    }) // 删除定货会商品陈列商品
                               
                                    let addrows = []
                                    for (let i = 0; i < 20; i++) {
                                        addrows.push({});
                                    }
                                    $('#jqxgrid').jqxGrid('clear');
                                    $('#jqxgrid').jqxGrid('addrow', null, rows)
                                    $('#jqxgrid').jqxGrid('addrow', null, addrows) 
                                     updateTotalAmount()
                                   
                                
                            if (hasOrderItem) {
                                     let payway2_id = $('#payway2_id').jqxInput('val') 
                                    let payway2_amount = $('#payway2_amount').jqxInput('val');
                                    let payway3_amount = $('#payway3_amount').jqxInput('val');
                                    var sumAmount = Number(payway2_amount) + Number(payway3_amount)
                                     $('#payway1_id').jqxInput('val', { value:payway2_id.value , label: payway2_id.label }) 
                                     $('#payway1_amount').jqxInput('val', sumAmount);
                                    
                                    $('#payway2_id').jqxInput('val', { value: '', label: '' }) 
                                    $('#payway2_amount').jqxInput('val', '');
                                    $('#payway3_id').jqxInput('val', { value: '', label: '' }) 
                                    $('#payway3_amount').jqxInput('val', '');
                                
                            }
                                
                                    $('#supcust_id').jqxInput('val', { value: newSup, label: sup_name })
                                    $('#changeClientAlertBox').hide()
                                    $('#popClient').jqxWindow('close');
                        }
                        $('#confirm').off('click')
                        //bw.toast("这是一个一闪而过的话", 3000);
                    })

                    $('#changeClientAlertBox').show()

                } else {
                          $('#supcust_id').jqxInput('val', { value: newSup, label: sup_name })
                          $('#supcust_id').jqxInput('val', { value: newSup, label: sup_name })
                    if (sheetType == 'SHEET_SALE') {
                        SetBranchAndSellerOnSupcustUpdate(newSup, saleRememberBranch, saleAndOrderRemeberSeller)
                    }
                    else if (sheetType == 'SHEET_SALE_DD') {
                        SetBranchAndSellerOnSupcustUpdate(newSup, orderRememberBranch, saleAndOrderRemeberSeller)
                    }
                           if (window.getClientAccountInfo)
                            getClientAccountInfo(newSup,true)
                            $('#popClient').jqxWindow('close');

                            if (oldSup && oldSup.value === newSup) return // 如果新选择客户与之前相同。返回

                            refreshRowsBySupChange(newSup, branch_id)
                }

          

            }

        }
        else if (rs.data.msgHead === "ItemsView") {
            if (rs.data.action === "selectMulti") {
                //debugger
                var supcust_id = $('#supcust_id').jqxInput('val').value
                var cell = $('#jqxgrid').jqxGrid('getselectedcell')
                var rowIndex = -1
                if(cell)
                   rowIndex=cell.rowindex;
                $("#jqxgrid").jqxGrid('endcelledit', rowIndex, "item_id", false)

                $('#popItem').jqxWindow('close')
                AddItemRows(rowIndex, supcust_id, branch_id,"0",rs.data.checkedRows)
                beforeQuery(rs.data.checkedRows)
            }
            else if (rs.data.action === "update") {
                $('#gridItems').jqxGrid('setcellvalue', RowIndex, "n", rs.data.record.sup_name);
            }
            $('#popClient').jqxWindow('close');
        }
        else if (rs.data.msgHead === "SuppliersView") {
            if (rs.data.action === "select") {
                // let supcust_id = rs.data.supcust_id;
                let newSup = rs.data.supcust_id;
                let sup_name = rs.data.sup_name;
               // this.window.g_acct_type = rs.data.acct_type;
                $('#supcust_id').jqxInput('val', { value: newSup, label: sup_name });
                if (window.getClientAccountInfo)
                    getClientAccountInfo(newSup)
                $('#popClient').jqxWindow('close')

                if (oldSup && oldSup === newSup) return // 如果新选择客户与之前相同。返回

                refreshRowsBySupChange(newSup, branch_id)

            }
        }


    })


    //开单时选择客户后，刷新商品行
    function refreshRowsBySupChange(newSup,branch_id) {
        let n = GetFirstEmptyRowIndex()
        if (n === 0) return
        // 更新客户后，如果商品行有数据，需要刷新商品行信息，更新价格，删除定货会商品陈列商品
        const gridRows = $('#jqxgrid').jqxGrid('getrows');
        let index = 0
        var rows = []
        gridRows.forEach((row) => {
            if (!row.item_id || row.order_sub_id || row.disp_flow_id) return
            rows[index] = row
            index++
        }) // 删除定货会商品陈列商品
        //window.g_orderSubID = ''
        //window.g_orderAmount = ''
        let addrows = []
        for (let i = 0; i < 20; i++) {
            addrows.push({});
        }
        $('#jqxgrid').jqxGrid('clear');//清除表格

        $('#jqxgrid').jqxGrid('addrow', null, addrows) // 添加空行
        
        console.log(rows)

        AddItemRows(0, newSup, branch_id,"0",rows) // 刷新商品行，更新价格
        updateTotalAmount()

        $('#payway1_id').jqxInput('val', window.g_initialPayway) // 更新支付方式1
        console.log('支付方式：', window.g_initialPayway)

        $('#payway2_id').jqxInput('val', { value: '', label: '' }) // 支付方式二清空
        $('#payway2_amount').jqxInput('val', '');
        $('#payway3_id').jqxInput('val', { value: '', label: '' }) // 支付方式三清空
        $('#payway3_amount').jqxInput('val', '');
    }


    // 复制到其他单据，商品行内容获取
    function GetRowsByCopyFromSheets(params) {
        console.log("GetRowsByCopyFromSheets",params)
        let index = 0
        var rows = []
        params.SheetRows.forEach((row) => {
            if (!row.item_id || row.order_sub_id || row.disp_flow_id) return
            rows[index] = row
            index++
        }) // 删除定货会商品陈列商品

        AddItemRows(0, params.supcust_id, params.branch_id,"0", rows, true)

        $('#no_disc_amount').jqxInput('val', toDecimal(params.no_disc_amount));
        $('#now_disc_amount').jqxInput('val', toDecimal(params.now_disc_amount));
        $('#total_amount').jqxInput('val', toDecimal(params.total_amount));
    }

    //显示销售单欠款和预收款
    function getClientAccountInfo(supcust_id,selectSup=false) {
        //debugger
        var sheet_type = $('#sheet_type').val()
        window.mysupcust_id = supcust_id;
        var sheetType=$('#sheetType').val()
        var sheetName = "SaleSheet"
        var bForBuySheet=false
        if (',CG,CT,CD,CTD,'.indexOf(',' + sheetType + ',') >=0) {
            sheetName = "BuySheet"
            bForBuySheet=true
        }

        $('#div_get_account').empty()
        
        $.ajax({
            url: `/api/${sheetName}/GetClientAccountInfo`,
            type: 'GET',
            contentType: 'application/json',
            data: {
                operKey: g_operKey,
                supcust_id: supcust_id,
                sheet_type:sheet_type,
                    selectSup: selectSup
            },
            success: function (data) {
                //debugger
                if (data.result === 'OK') {
                    var arrears = "";
                    var prepay = "";
                    
                    $('#supcust_no').val(data.supcust_no)
                    $('#mobile').val(data.mobile)
                    $('#sup_addr').val(data.sup_addr)
                    $('#boss_name').val(data.boss_name)
                   
                    //$('#seller_id').val({value:data.charge_seller_id,label:charge_seller_name})
                    
                    if (data.charge_seller) {
                        var seller_id = $('#seller_id').val()
                        if(!( seller_id && seller_id.value ))
                           $('#seller_id').val({ value: data.charge_seller, label: data.charge_seller_name })
                    }
                    
                    
                    
                    if (data.acct_type) {
                        var acct_type = data.acct_type;
                        var s=''
                        if (data.acct_way_name) {
                            s=data.acct_way_name
                        }
                        else s = acct_type == "pay" ? "现结" : acct_type == "arrears" ? "欠款" : ""
                        if ($('#acct_type_way').length>0)
                           $('#acct_type_way').jqxInput('val', s);
                        if ($('#acct_type').length > 0)
                            $('#acct_type').jqxInput('val', acct_type);
                    }
                    if (data.mobile) { 
                        if ($('#mobile').length > 0) {
                            $('#mobile').jqxInput('val',data.mobile)
                        } 
                    }
                            if (data.supcust_no ) {
                                if ($('#supcust_no ').length > 0) {
                                        $('#supcust_no ').jqxInput('val',data.supcust_no )
                            }
                        }

                    if (data.addr1 && selectSup) {
                        if ($('#receive_addr').length>0){
                            $('#receive_addr').val({ value: data.addr1.v, label: data.addr1.l })
                        }

                        if (data.addr1 && selectSup) {
                            $('#receiver_mobile').val(data.addr1.p)
                            
                         }

                    }
                       
                    if (data.acct_cust_id) {
                        $('#acct_supcust_id').val(data.acct_cust_id)
                        //$('#acct_cust_id').jqxInput('val', { value: data.acctInfo.acct_cust_id, label: data.acctInfo.acct_cust_name });
                        if(data.acct_cust_name)$('#div_get_account').append(`<label style="padding:4px;color:#f66;font-size:14px">结算单位：${data.acct_cust_name} </label>`)
                    }

                    /*if(data.acctInfo) {
                        $('#acct_supcust_id').val(data.acctInfo.acct_cust_id)
                        //$('#acct_cust_id').jqxInput('val', { value: data.acctInfo.acct_cust_id, label: data.acctInfo.acct_cust_name });
                        if(data.acctInfo.acct_cust_name)$('#div_get_account').append(`<label style="padding:4px;color:#f66;font-size:14px">结算单位：${data.acctInfo.acct_cust_name} </label>`)
                    }*/
                    if (data.disp) {
                        var dispLabel = "";

                        window.dispAccounts = data.disp
                        if (data.disp.length > 0) {
                            for (var i = 0; i < data.disp.length; i++) {
                                var sub_name = data.disp[i].sub_name
                                var disp_amount = data.disp[i].disp_amount
                                dispLabel += `<label id='DispTip' style="padding:4px;color:#f66;font-size:14px;" title = "本月前(含本月)的陈列剩余费用">${sub_name}:${disp_amount}</label>`
                            }
                        }
                        if (dispLabel) $('#div_get_account').append(dispLabel) 

                    }
                    //window.parent.newTabPage("历史交账单", `WorkFlow/CheckAccount/CheckSheetsSheetHistory?sheetNo=${sheet_no}&startDay=${startDay}`)
                    if (data.arrears.length > 0) {
                            var arrear = parseFloat(data.arrears[0].balance);
                            if (bForBuySheet) {
                               arrears = '应付款:' + toMoney(-arrear) + ';  ' 
                            }
                            else {
                               arrears = '应收款:' + toMoney(arrear) + ';  '
                            }
                    }
                    
                    window.prepayAccounts = data.prepay
                    if (data.prepay.length > 0) {
                        for (var i = 0; i < data.prepay.length; i++) {
                            var sub_id = data.prepay[i].sub_id
                            var sub_name = data.prepay[i].sub_name
                            var prepay_balance = data.prepay[i].balance
                            if (!sub_name && prepay_balance) sub_name = '预收款'
                            prepay += sub_name + ': ' + toMoney(prepay_balance) + '; ' + ' '
                            //$(`li[data-value=${sub_id}]`).show()

                        }
                    }

                    $labelArrearsInfo = $('<label class="arrears_label" style="cursor:pointer;padding-top:4px;height:22px;color:#f66;font-size:14px;border-bottom:1px #f66 solid;margin-left:4px;"></label>')
                    $labelPrepayInfo = $('<label style="padding:4px;color:#f66;font-size:14px"></label>')

                    if (arrears) {
                        $('#div_get_account').append($labelArrearsInfo)
                        $labelArrearsInfo.text(arrears)
                        console.log($labelArrearsInfo)
                        $('#div_get_account').on("click",'.arrears_label',()=>{
                            const supcust_label = $('#supcust_id').jqxInput('val')
                            if (bForBuySheet) {
                                window.parent.newTabPage("付款单", `Sheets/GetArrearsSheet?forPayOrGet=true&sup_name=${supcust_label.label}&supcust_id=${supcust_label.value}`,window)
                            }
                            else {
                                window.parent.newTabPage("收款单", `Sheets/GetArrearsSheet?forPayOrGet=false&sup_name=${supcust_label.label}&supcust_id=${supcust_label.value}`,window)
                            }
                           
                        })
                    }
                    if (prepay) {
                        $('#div_get_account').append($labelPrepayInfo)
                        $labelPrepayInfo.text(prepay)
                    }
                    $(window).trigger("resize")
                    //btnMoreAcctInfo
                    if ($('#div_get_account').width() > 198) {
                        $('#btnMoreAcctInfo').show()
                        var popWidth=$('#div_get_account').width()+30
                        var popHeight = $('#div_get_account').height() + 50

                        $('#btnMoreAcctInfo').click(() => {
                            
                            if ($('#div-whole-acct-info').length == 0) {
                                var html = $('#div_get_account').html()
                                var divWhole = `
                                            <div id='div-whole-acct-info' style="z-index:********;padding:10px;background:#fff;border:1px #ccc solid;border-radius:6px;position:fixed;width:500px;height:300px;">
                                                <div style="height:20px;"><img id="img-close-acct-info" style="float:right;cursor:pointer;" src="/jqwidgets/jqwidgets/styles/images/close.png" /></div>
                                            ${html}

                                        </div>`
                                $('body').append(divWhole)
                                $('#div-whole-acct-info').mouseleave(() => {

                                   // $('#div-whole-acct-info').animate({ height: "30px", width: "200px", top: $('#div_get_account').offset().top, left: $('#div_get_account').offset().left }, 100, () => {
                                  //      $('#div-whole-acct-info').hide()
                                   // })


                                })
                                $('#img-close-acct-info').click(() => {
                                      $('#div-whole-acct-info').animate({ height: "30px", width: "200px", top: $('#div_get_account').offset().top, left: $('#div_get_account').offset().left }, 100, () => {
                                         $('#div-whole-acct-info').hide()
                                     })

                                })
                                
                            }
                            $('#div-whole-acct-info').css('height','auto')
                            $('#div-whole-acct-info').width(popWidth)
                          
                            popHeight = $('#div-whole-acct-info').height() + 40
                            $('#div-whole-acct-info').show()
                            $('#div-whole-acct-info').css('height', '40px')
                            $('#div-whole-acct-info').offset({ left: $('#div_get_account').offset().left, top: $('#div_get_account').offset().top })

                            var destTop = $('#btnMoreAcctInfo').offset().top - popHeight -50;
                            var destLeft = $('#btnMoreAcctInfo').offset().left - popWidth;

                            $('#div-whole-acct-info').animate({ height: popHeight, width: popWidth, top: destTop, left: destLeft })
                            
                        })
                    }
                }
                else {
                    bw.toast(data.msg, 3000);
                }
            },
            error: function (xhr) {
                console.log("返回响应信息：" + xhr.responseText);
            }
        });
    }



    function routeConvert(srcRoute) {

    }

    //function AddItemRows_old(rowIndex,supcust_id,branch_id,rows) {
    //    //var item_id = obj.item_id;
    //    var items_id=''
    //    rows.forEach(function (row) {
    //        if(items_id!='') items_id += ','
    //        items_id+=row.item_id
    //    })
    //   // var supcust_id = obj.supcust_id;
    //    //var branch_id = obj.branch_id;
    //    $.ajax({
    //        url: '/api' + window.pageInfo.url + '/GetItemsInfo',
    //        type: 'GET',
    //        contentType: 'application/json',
    //        data: { operKey: g_operKey, items_id: items_id, supcust_id: supcust_id, branch_id: branch_id },
    //        success: function (data) {
    //            if (data.result === 'OK') {
    //                if (supcust_id) $('#supcust_id').jqxInput({ disabled: true })
    //                if (!window.g_queriedItems) window.g_queriedItems = {}
    //                var gridRows = $('#jqxgrid').jqxGrid('getrows');
    //                for (var item_id in data.items) {
    //                    var item = data.items[item_id]
    //                    var order_price = 0;
    //                    var order_unit_factor = 0;
    //                    var order_qty = 0;
    //                    var order_qty_unit = '';
    //                    var order_sub_name = '';
    //                    var order_sub_id = '';

    //                    var bigUnitNo = '', unit_factor = '', barcode = '', price = '', retail_price = '', orig_price = '';
    //                    var unitType = 'b'
    //                    window.g_queriedItems[item.item_id] = item
    //                    rows.forEach(function (row) {

    //                        if ($('#useSmallUnit').length == 1) {
    //                            var useSmallUnit = $('#useSmallUnit').jqxCheckBox('checked')
    //                            if (useSmallUnit) unitType = 's'
    //                        }

    //                        if (row.item_id == item.item_id) {
    //                            if (row.order_sub_id)
    //                                window.g_queriedItems[item.item_id + '_' + row.order_sub_id + '_' + row.order_price] = item


    //                            if (row.order_price) {
    //                                order_price = row.order_price
    //                                order_unit_factor = row.order_unit_factor
    //                                order_qty = row.order_qty
    //                                order_qty_unit = row.order_qty_unit
    //                                order_sub_id = row.order_sub_id
    //                                order_sub_name = row.order_sub_name
    //                            }

    //                        }
    //                    })
    //                    var order_s_unit_price = 0;
    //                    if (myIsNumber(order_price)) {
    //                        order_s_unit_price = order_price / order_unit_factor
    //                    }
    //                    item.units.forEach((unit) => {
    //                       // if(window.g_companySetting.show)
    //                        if (order_sub_name) unit.price = order_s_unit_price * unit.unit_factor
    //                        if (unit.unit_type == 's') gridRows[rowIndex].s_barcode = unit.barcode
    //                        if (unit.unit_type == unitType || item.units.length == 1 ) {
    //                            bigUnitNo = unit.unit_no
    //                            unit_factor = unit.unit_factor

    //                            barcode = unit.barcode
    //                            retail_price = unit.retail_price
    //                            price = unit.price
    //                            orig_price = unit.recent_orig_price
    //                        }
    //                    })

    //                    price = toMoney(price)
    //                    order_price = toMoney(order_price)


    //                    $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'item_id', { value: item.item_id, label: item.item_name })
    //                    if ($('#jqxgrid').jqxGrid('getcolumn', 'item_spec'))
    //                        if (item.item_spec) $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'item_spec', item.item_spec)
    //                    if ($('#jqxgrid').jqxGrid('getcolumn', 'valid_days'))
    //                        if (item.valid_days) $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'valid_days', item.valid_days)
    //                    var column = $('#jqxgrid').jqxGrid('getcolumn', 'stock_qty_unit');

    //                    if (column && item.stock_qty_unit) $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'stock_qty_unit', item.stock_qty_unit)

    //                    $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'unit_no', bigUnitNo)
    //                    $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'unit_factor', unit_factor)
    //                    if($('#jqxgrid').jqxGrid('getcolumn','orig_price'))
    //                       $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'orig_price', orig_price)
    //                    if($('#jqxgrid').jqxGrid('getcolumn','sys_price'))
    //                       $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'sys_price', price)


    //                    $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'real_price', price)
    //                    //$('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'retail_price', retail_price)

    //                    gridRows[rowIndex].s_retail_price=item.s_retail_price
    //                    if($('#jqxgrid').jqxGrid('getcolumn','barcode'))
    //                        $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'barcode', barcode)
    //                    if ($('#jqxgrid').jqxGrid('getcolumn', 'order_sub_name')) $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'order_sub_name', order_sub_name)
    //                    if ($('#jqxgrid').jqxGrid('getcolumn', 'order_sub_id')) $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'order_sub_id', order_sub_id)
    //                    if ($('#jqxgrid').jqxGrid('getcolumn', 'order_qty')) $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'order_qty', order_qty)
    //                    if ($('#jqxgrid').jqxGrid('getcolumn', 'order_qty_unit')) $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'order_qty_unit', order_qty_unit)
    //                    if ($('#jqxgrid').jqxGrid('getcolumn', 'b_order_price')) $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'b_order_price', order_price)
    //                    //if ($('#jqxgrid').jqxGrid('getcolumn', 'cost_price_buy')) $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'cost_price_buy', cost_price_buy)

    //                    rowIndex++
    //                }
    //            }
    //        }
    //    })
    //}
    function GetFirstEmptyRowIndex() {
        var rows = $('#jqxgrid').jqxGrid('getrows')
        var lastRowIndex = -1;
        var i = 0;
        rows.forEach(row => {
            if (row.item_id) {
                lastRowIndex=i
            }
            i++
        })
        return lastRowIndex+1
    }


    /*
    function AddItemRows_using(rowIndex, supcust_id, branch_id, rows, bUsePriceInRows) { // bUsePriceInRows 复制单据时会使用
        //var item_id = obj.item_id;

        var items_id = ''
        if (rowIndex == -1) {
            rowIndex = GetFirstEmptyRowIndex()
        }
        rows.forEach(function (row) {
            if (items_id != '') items_id += ','
            items_id += row.item_id
        })

        var newRows=[]
        rows.forEach(row => {

            var subRow=null
            if (row.b_qty) {
                subRow = JSON.parse(JSON.stringify(row))
                subRow.quantity = row.b_qty
                subRow.unit_no = row.b_unit_no
                subRow.unit_factor = row.b_unit_factor
                newRows.push(subRow)
            }
            if (row.m_qty) {
                subRow = JSON.parse(JSON.stringify(row))
                subRow.quantity = row.m_qty
                subRow.unit_no = row.m_unit_no
                subRow.unit_factor = row.m_unit_factor
                newRows.push(subRow)
            }
             if (row.s_qty) {
                subRow = JSON.parse(JSON.stringify(row))
                subRow.quantity = row.s_qty
                subRow.unit_no = row.s_unit_no
                subRow.unit_factor = row.s_unit_factor
                newRows.push(subRow)
            }
            if (!subRow) {
                newRows.push(row)
            }

        })
        rows=newRows

        // var supcust_id = obj.supcust_id;
        //var branch_id = obj.branch_id;


        $.ajax({
            url: '/api' + window.pageInfo.url + '/GetItemsInfo',
            type: 'GET',
            contentType: 'application/json',
            data: { operKey: g_operKey, items_id: items_id, supcust_id: supcust_id, branch_id: branch_id,bGetAttrs:!window.attributes},
            success: function (data) {
                if (data.result === 'OK') {
                    if (supcust_id) $('#supcust_id').jqxInput({ disabled: true })
                    if (!window.g_queriedItems) window.g_queriedItems = {}
                    var gridRows = $('#jqxgrid').jqxGrid('getrows')
                    var nAddRows = rowIndex + rows.length + 10 - gridRows.length
                    if (nAddRows>0)
                        addEmptyRows(nAddRows)
 
                    if(data.attrOptions) window.attrOptions = data.attrOptions
                    if(data.attributes) window.attributes = data.attributes
 

                    rows.forEach(function (row) {

                        var item = null
                        var order_price = 0, order_unit_factor = 0, order_qty = 0,specific_qty_unit = '',order_sub_name = '',order_sub_id = '';
                        var disp_flow_id = '', disp_sheet_id = '', disp_month_id = '',disp_unit_no='',disp_unit_factor='';
                        var bigUnitNo = '', unit_factor = '', barcode = '', price = '', orig_price = '', retail_price = '', cost_price = '', remark = '', s_retail_price = '',last_time_price='';

                        var unitType = 'b'
                        var unit_no = '',quantity='',sub_amount='';
                        for (var item_id in data.items) {
                            var curItem = data.items[item_id]
                            if ($('#useSmallUnit').length == 1) {
                                var useSmallUnit = $('#useSmallUnit').jqxCheckBox('checked')
                                if (useSmallUnit) unitType = 's'
                            }

                            if (row.item_id == curItem.item_id) {
                                item = curItem
                               // window.g_queriedItems[item.item_id] = item
                                var key=getQueriedItemsKeyFromRow(row)
                                window.g_queriedItems[key] = item


                                if (row.order_price) {
                                    order_price = row.order_price
                                    order_unit_factor = row.order_unit_factor
                                    order_qty = row.order_qty
                                    specific_qty_unit = row.order_qty_unit
                                    order_sub_id = row.order_sub_id
                                    order_sub_name = row.order_sub_name
                                    remark = '还定货'
                                }

                                if (row.disp_flow_id) {
                                    disp_flow_id = row.disp_flow_id
                                    disp_sheet_id = row.disp_sheet_id;
                                    specific_qty_unit = row.disp_left_qty + row.disp_unit_no;
                                    disp_month_id = row.disp_month_id;
                                    disp_unit_no = row.disp_unit_no;
                                    remark=row.disp_month+'月陈列'
                                }
                            }
                        }
                        if (!item) return
                        
                        var order_s_unit_price = 0;
                        if (myIsNumber(order_price)) {
                            order_s_unit_price = order_price / order_unit_factor
                        }


                        item.units.forEach((unit) => {
                            // if(window.g_companySetting.show)
                            if (order_sub_name) unit.price = order_s_unit_price * unit.unit_factor
                            if (unit.unit_type == 's')  gridRows[rowIndex].s_barcode = unit.barcode

                                if (disp_unit_no == unit.unit_no) {
                                    disp_unit_factor = unit.unit_factor
                                    unitType=unit.unit_type
                                }
                            if (unit.unit_type == 's') s_retail_price=unit.retail_price


                            if (item.units.length == 1 || unit.unit_type == unitType || unit.unit_no==row.unit_no) {
                                unit_no = unit.unit_no
                                unit_factor = unit.unit_factor
                                barcode = unit.barcode
                                retail_price = unit.retail_price
                                price = unit.price
                                if (window.g_companySetting.costPriceType == '3' && unit.buy_price) {
                                    cost_price = toMoney(unit.buy_price,2)
                                }
                                else if (window.g_companySetting.costPriceType == '2' && unit.cost_price_avg) {
                                    cost_price =  toMoney(unit.cost_price_avg*unit.unit_factor, 2)
                                }

                                if (unit.recent_price) last_time_price = unit.recent_price
                                if (unit.recent_orig_price) orig_price = unit.recent_orig_price
                                if (disp_unit_no) {
                                    unit_no = disp_unit_no;
                                    unit_factor = disp_unit_factor;
                                    price = 0
                                }
                            }


                        })
                        if (bUsePriceInRows) {
                            price = row.real_price
                            unit_no = row.unit_no
                            unit_factor = row.unit_factor
                            quantity = row.quantity
                            //sub_amount = row.sub_amount
                            orig_price = row.orig_price
                        }
                        else price = toMoney(price, 4)


                        if (row.quantity) quantity = row.quantity

                        order_price = toMoney(order_price,4)


                        $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'item_id', { value: item.item_id, label: item.item_name })



                       if ($('#jqxgrid').jqxGrid('getcolumn', 'item_spec'))
                            if (item.item_spec) $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'item_spec', item.item_spec)
                        if ($('#jqxgrid').jqxGrid('getcolumn', 'valid_days'))
                            if (item.valid_days) $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'valid_days', item.valid_days)
                        if ($('#jqxgrid').jqxGrid('getcolumn', 'produce_date'))
                            if (item.produce_date) $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'produce_date', item.produce_date)
                        var column = $('#jqxgrid').jqxGrid('getcolumn', 'stock_qty_unit');

                        if (column && item.stock_qty_unit) $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'stock_qty_unit', item.stock_qty_unit)

                        $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'unit_no', unit_no)
                        $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'unit_factor', unit_factor)
                        if (quantity) {
                            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'quantity', quantity)
                        }

                        if (remark)
                            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'remark', remark)

                        if ($('#jqxgrid').jqxGrid('getcolumn', 'orig_price'))
                            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'orig_price', orig_price)
                        if ($('#jqxgrid').jqxGrid('getcolumn', 'sys_price'))
                            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'sys_price', price)
                        $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'real_price', price)
                        if ($('#jqxgrid').jqxGrid('getcolumn', 'discount') && orig_price)
                            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'discount', toMoney(price / orig_price * 100))

                        var qty = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'quantity');
                        if(qty) {
                            sub_amount = toMoney(parseFloat(qty)*price,2)
                            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'sub_amount', sub_amount)
                        }
                        debugger
                        if (item.mum_attributes) {
                            var curRow = gridRows[rowIndex];
                            curRow.mum_attributes = JSON.parse(item.mum_attributes)
                            curRow.mum_attributes.forEach(a => {
                                var b = window.attributes.find(b => b.attr_id == a.attrID)
                                if (b) {
                                   a.order_index=b.order_index
                                } 
                            })
                           curRow.mum_attributes.sort((a,b)=>parseFloat(a.order_index||100)-parseFloat(b.order_index||100))
                        }
                         

                        if ($('#jqxgrid').jqxGrid('getcolumn', 'last_time_price'))
                            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'last_time_price', last_time_price)
                        if ($('#jqxgrid').jqxGrid('getcolumn', 'retail_price'))
                            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'retail_price', retail_price)
                        if ($('#jqxgrid').jqxGrid('getcolumn', 's_retail_price'))
                            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 's_retail_price', s_retail_price)
                        // gridRows[rowIndex].s_retail_price = item.s_retail_price
                        if ($('#jqxgrid').jqxGrid('getcolumn', 'barcode'))
                            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'barcode', barcode)
                        if ($('#jqxgrid').jqxGrid('getcolumn', 'order_sub_name')) $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'order_sub_name', order_sub_name)
                        if ($('#jqxgrid').jqxGrid('getcolumn', 'order_sub_id')) $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'order_sub_id', order_sub_id)
                        if ($('#jqxgrid').jqxGrid('getcolumn', 'order_qty')) $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'order_qty', order_qty)
                        if ($('#jqxgrid').jqxGrid('getcolumn', 'specific_qty_unit')) $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'specific_qty_unit', specific_qty_unit)
                        if ($('#jqxgrid').jqxGrid('getcolumn', 'order_price')) $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'order_price', order_price)
                        if ($('#jqxgrid').jqxGrid('getcolumn', 'disp_flow_id')) $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'disp_flow_id', disp_flow_id)
                        if ($('#jqxgrid').jqxGrid('getcolumn', 'disp_sheet_id')) $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'disp_sheet_id', disp_sheet_id)
                        if ($('#jqxgrid').jqxGrid('getcolumn', 'disp_month_id')) $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'disp_month_id', disp_month_id)
                        if ($('#jqxgrid').jqxGrid('getcolumn', 'cost_price')) $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'cost_price', cost_price)

                        //if ($('#jqxgrid').jqxGrid('getcolumn', 'cost_price_buy')) $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'cost_price_buy', cost_price_buy)
                       
                        
                        rowIndex++
                    })
                    updateTotalAmount()
                    //rows.refresh()
                }
            }
        })
    }
    */

  
    function AddItemRows(rowIndex, supcust_id, branch_id,branch_position, rows, bUsePriceInRows) { // bUsePriceInRows 复制单据时会使用
        console.log(window.g_companySetting)
        console.log(window.g_operRights)
        let sheetType = $('#sheetType').val()
        if(!branch_position) branch_position ="0"
        let hasTradeTypeWithKS = false
        let hasOrderItem = false
        const itemsRows = $('#jqxgrid').jqxGrid('getrows')
        for (var i = 0; i < rows.length; i++) {
            for (let itemsRow of itemsRows) {
                if (itemsRow.item_name === rows[i].item_name && itemsRow.boundindex != rowIndex) {
                    msg = '第' + (itemsRow.boundindex + 1) + '行与' + (rowIndex + 1 + i) + '行商品' + rows[i].item_name + '重复';
                    bw.toast(msg, 5000)
                }
            }
            if (rows[i].order_sub_id && rows[i].order_sub_id != '') {
                hasOrderItem = true
            }
        }

        if (',X,XD,T,TD,'.indexOf(sheetType) >0 )
        { 
            //处理订货会与客损不能同时存在
           if (hasOrderItem) { 
                itemsRows.forEach(itemRow => { 
                    if (itemRow.trade_type == 'KS') {
                        hasTradeTypeWithKS = true
                    }
                })
            }

            if(hasOrderItem && hasTradeTypeWithKS){
                bw.toast('不能同时开定货会和客损商品',3000)
                return
            }
        }
        var items_id = ''
        if (rowIndex == -1) {
            rowIndex = GetFirstEmptyRowIndex()
        }
        rows.forEach(function (row) {
            if (items_id != '') items_id += ','
            items_id += row.item_id
        })
        if (!items_id) {
             bw.toast("请至少勾选一个商品")
             return
        }
        var newRows = []
        rows.forEach(row => {

            var subRow = null
            if (row.b_qty) {
                subRow = JSON.parse(JSON.stringify(row))
                subRow.quantity = row.b_qty
                subRow.unit_no = row.b_unit_no
                subRow.unit_factor = row.b_unit_factor
                subRow.unit_relation1 = getRowUnitRelation(row)
                newRows.push(subRow)
            }

            if (row.m_qty) {
                subRow = JSON.parse(JSON.stringify(row))
                subRow.quantity = row.m_qty
                subRow.unit_no = row.m_unit_no
                subRow.unit_factor = row.m_unit_factor
                subRow.unit_relation1 = getRowUnitRelation(row)
                newRows.push(subRow)
            }

            if (row.s_qty) {
                subRow = JSON.parse(JSON.stringify(row))
                subRow.quantity = row.s_qty
                subRow.unit_no = row.s_unit_no
                subRow.unit_factor = row.s_unit_factor
                subRow.unit_relation1 = getRowUnitRelation(row)
                newRows.push(subRow)
            }

            if (!subRow) {
                newRows.push(row)
            }

        })
        rows = newRows
        console.log(rows)

        // var supcust_id = obj.supcust_id;
        //var branch_id = obj.branch_id;
        let branchID = $("#branch_id").val().value
        var params = { operKey: g_operKey, items_id: items_id, supcust_id: supcust_id, branch_id: branch_id, branch_position: branch_position, bGetAttrs: !window.attributes }
        params.defaultBranchPositionType =  null
 
        params.defaultBranchPositionID = "0"
        if (window.g_dicDefualtBranchPosition && window.g_dicDefualtBranchPosition[branchID]) {
            params.defaultBranchPositionID = window.g_dicDefualtBranchPosition[branchID].branch_position
        }
        else {
            if(sheetType == "T" || sheetType =="TD"){
                params.defaultBranchPositionType = window.g_companySetting.backBranchPositionType
            }
            else{
                params.defaultBranchPositionType = window.g_companySetting.defaultBranchPositionType
            }
        }
        
        
        let isShowNoStock = false
        if (window.g_companySetting && window.g_companySetting.showNegativeStock && window.g_companySetting.showNegativeStock == "True") {
            isShowNoStock = true
        }
        params.isShowNegativeStock = isShowNoStock
        let isShowProduceDate = false
        let isShowBatchNo = false
        let isShowBranchPosition = false
        var pageUrl = window.pageInfo.url
        pageUrl=pageUrl.replace('Order','')
        $.ajax({
            url: '/api' + pageUrl + '/GetItemsInfo_Post',
            type: 'POST',
            contentType: 'application/json',
            processData: true,
            data:JSON.stringify(params),
            success: function (data) {
                if (data.result === 'OK') {
                    if(!window.g_dicDefualtBranchPosition) window.g_dicDefualtBranchPosition = {}
                    if(data.defaultBranchPosition) window.g_dicDefualtBranchPosition[branchID] = data.defaultBranchPosition
                    let rowindexs = []
                    if (!window.g_queriedItems) window.g_queriedItems = {}
                    var gridRows = $('#jqxgrid').jqxGrid('getrows');
                    
                    var nAddRows = rowIndex + rows.length + 10 - gridRows.length

                    if (rowIndex == -1) {
                        if (nAddRows > 0) { 
                            addEmptyRows(nAddRows)
                        }
                    }
                    else {
                           addEmptyRows(rows.length-1, rowIndex) 
                       //addEmptyRows(rows.length-1, rowIndex + rows.length) 
                    }

                    if(data.attrOptions) window.attrOptions = data.attrOptions
                    if(data.attributes) window.attributes = data.attributes

                    //gridRows = $('#jqxgrid').jqxGrid('getrows');
                    rows.forEach(function (row) {
                        
                        var item = null
                        var weight = "";
                        var s_unit_no = "",m_unit_no="",b_unit_no="";
                        var order_price = 0, order_unit_factor = 0, order_qty = 0, specific_qty_unit = '', order_sub_name = '', order_sub_id = '',order_flow_id='',order_item_sheets_id="",order_item_sheets_no = "";
                        var disp_flow_id = '', disp_sheet_id = '', disp_month_id = '', disp_unit_no = '', disp_unit_factor = '';
                        var unit_factor = '', barcode = '', price = '', orig_price = '', retail_price = '', wholesale_price='', cost_price_buy = '', cost_price_recent = '', cost_price_prop = '', cost_price_avg = '', cost_price = '', unit_weight = '', unit_volume = '', volume = '', remark = '', s_retail_price = '', last_time_price = '', cost_price_avg_unit = '', recent_retail_price = '';
                        let trade_type = '',trade_type_name = '';
                        var unitType = 'b'
                         if ($('#defaultUnit').length == 1) {
                            var defaultUnit = $('#defaultUnit').jqxInput('val')
                            if (defaultUnit && defaultUnit.value) unitType = defaultUnit.value
                        }
                        //2024-03-26 所有单据都可以复制备注
                        //if (sheetType === "CG"){
                        //    remark = row.remark
                        //}
                        remark = row.remark


                        var unit_no = '', quantity = '', sub_amount = '';
                        var branch_id = "",branch_position="";
                        for (var item_id in data.items) {
                            var curItem = data.items[item_id]                       
                            if (row.item_id == curItem.item_id) {
                                item =JSON.parse( JSON.stringify(curItem))
                     
                                var key = getQueriedItemsKeyFromRow(row)
                                window.g_queriedItems[key] = item

                                if ((row.order_price !== undefined && row.order_price !== '' && row.order_price !== null) && row.order_sub_id) {
                                    order_price = row.order_price
                                    order_unit_factor = row.order_unit_factor
                                    order_qty = row.order_qty
                                    specific_qty_unit = row.order_qty_unit
                                    order_sub_id = row.order_sub_id 
                                    order_flow_id = row.order_flow_id
                                    order_sub_name = row.order_sub_name
                                    order_item_sheets_id = row.order_item_sheets_id
                                    order_item_sheets_no = row.order_item_sheets_no

                                    remark = '还定货'
                                    trade_type='DH'
                                }

                                if (row.disp_flow_id) {
                                    disp_flow_id = row.disp_flow_id
                                    disp_sheet_id = row.disp_sheet_id;
                                    specific_qty_unit = row.disp_left_qty + row.disp_unit_no;
                                    disp_month_id = row.disp_month_id;
                                    disp_unit_no = row.disp_unit_no;
                                    remark = row.disp_month + '月陈列'
                                    if (row.fee_sub_name) remark +='('+row.fee_sub_name+')'
                                    trade_type = 'CL'

                                }

                                if (row.borrowed_qty) {
                                    specific_qty_unit = row.order_qty_unit;
                                    order_qty = row.borrowed_qty
                                    if ($('#sheet_type').val() !== "SHEET_BORROW_ITEM") {
                                        trade_type = 'H';
                                        trade_type_name = '还货'
                                        remark = '还货'
                                    }
                                    
                                }
                            }
                        }
                        if (!item) return
                        var order_s_unit_price = 0;
                        if (myIsNumber(order_price)) {
                            order_s_unit_price = order_price / order_unit_factor
                        }

                        var gridRow = gridRows[rowIndex]
                        var has_m_unit = false
                        gridRow.s_unit_factor = 1
                        item.units.forEach((unit) => { 
                            if (unit.unit_type == 's') { 
                                gridRow.s_barcode = unit.barcode
                                gridRow.s_weight = unit.unit_weight
                                gridRow.s_volume = unit.unit_volume
                            }
                            else if (unit.unit_type == 'm') { 
                                has_m_unit=true
                                gridRow.m_unit_factor =parseFloat(unit.unit_factor)
                                gridRow.m_weight = unit.unit_weight
                                gridRow.m_volume = unit.unit_volume
                            }
                            else if (unit.unit_type == 'b') {
                                gridRow.b_unit_factor = parseFloat(unit.unit_factor)
                                gridRow.b_weight = unit.unit_weight
                                gridRow.b_volume = unit.unit_volume
                            }
                        })
                        
                        item.units.forEach((unit) => {
                            // if(window.g_companySetting.show)
                            if (order_sub_name) unit.price = order_s_unit_price * unit.unit_factor
                            if (trade_type=="CL"||trade_type=="H") unit.price = 0
                           

                            if (disp_unit_no == unit.unit_no) {
                                disp_unit_factor = unit.unit_factor
                                unitType = unit.unit_type
                            }
                            if (unit.unit_type == 's') {
                                s_retail_price = unit.retail_price                               
                            }
                            var bMet=false
                            if (row.quantity) {
                                if (row.unit_no == unit.unit_no) bMet=true
                            }
                            else if (item.units.length == 1 || unit.unit_type == unitType
                                || (unit.unit_type == 's' && unitType=='m' && !has_m_unit)//当中单位为默认单位时,如果没有中单位, 使用小单位
                            ) {
                                bMet=true
                            }

                            if (bMet){
                                if (sheetType === "CG") { 
                                     cost_price_avg_unit= item.cost_price_avg_s_unit*unit.unit_factor
                                    //wholesale_price = item.wholesale_price 
                                }
                                wholesale_price = unit.wholesale_price
                                unit_no = unit.unit_no
                                unit_factor = unit.unit_factor
                                barcode = unit.barcode
                                retail_price = unit.retail_price
                                price = unit.price
                                unit_weight = unit.unit_weight
                                weight = unit.weight
                                unit_volume = unit.unit_volume
                                volume = unit.volume
                                var dotLen = 7
                                cost_price_buy = toMoney(unit.cost_price_buy, dotLen)
                                cost_price_recent = toMoney(unit.cost_price_recent, dotLen)
                                cost_price_prop = toMoney(unit.cost_price_spec, dotLen)
                                cost_price_avg = toMoney(unit.cost_price_avg, dotLen)
                                if(unit_factor==1) dotLen=7
                                if (window.g_companySetting.costPriceType == '3' && unit.buy_price) {
                                    cost_price = toMoney(unit.buy_price,dotLen)
                                }
                                else if (window.g_companySetting.costPriceType == '2' && unit.cost_price_avg) {
                                    cost_price = toMoney(unit.cost_price_avg * unit.unit_factor, dotLen)
                                }
                                else if (window.g_companySetting.costPriceType == '4' && unit.cost_price_avg) {
                                    cost_price = toMoney(unit.cost_price_recent * unit.unit_factor, dotLen)
                                }
                                else if (window.g_companySetting.costPriceType == '1' && unit.cost_price_spec) {
                                    cost_price = toMoney(unit.cost_price_spec * unit.unit_factor, dotLen)
                                }
                                if (unit.recent_price) last_time_price = unit.recent_price
                                if (unit.recent_orig_price) orig_price = unit.recent_orig_price
                                if (unit.recent_retail_price) recent_retail_price = unit.recent_retail_price
                                if (disp_unit_no) {
                                    unit_no = disp_unit_no;
                                    unit_factor = disp_unit_factor;
                                    price = 0
                                }
                            }
                            if(unit.unit_type=="s") s_unit_no = unit.unit_no
                            if(unit.unit_type=="m") m_unit_no = unit.unit_no
                            if(unit.unit_type=="b") b_unit_no = unit.unit_no
                            //return bMet
                        })
                        if (bUsePriceInRows) {
                            price = row.real_price
                            unit_no = row.unit_no
                            unit_factor = row.unit_factor
                            quantity = row.quantity
                            //sub_amount = row.sub_amount
                            orig_price = row.orig_price
                        }
                        else price = toMoney(price, 4)
  
                         
                        var columns = $('#jqxgrid').jqxGrid('getcolumns')
                        var col=columns.records.find(col=>col.datafield=='real_price')
                        if (col && col.hidden) {//无查看进价权限的情况，如果价格为空，就取0
                            if (!price) price = 0
                        }

                        if (row.quantity) quantity = row.quantity

                        order_price = toMoney(order_price, 4)
                        
                      
                        gridRow.item_id = item.item_id
                        gridRow.item_name = item.item_name
                        if (item.son_mum_item) gridRow.son_mum_item = item.son_mum_item
                        gridRow.mum_attributes = item.mum_attributes 
                        if(gridRow.mum_attributes) gridRow.mum_attributes=JSON.parse(gridRow.mum_attributes)
                        gridRow.avail_attr_combine = item.avail_attr_combine
                        if(gridRow.avail_attr_combine) gridRow.avail_attr_combine=JSON.parse(gridRow.mum_attributes)
                        

                        if (',X,T,XD,TD,'.indexOf(',' + sheetType + ',') == -1) {
                            var attrs = gridRow.mum_attributes
                            for (var i = attrs.length - 1; i >= 0; i--) {
                                var attr = attrs[i]
                                if (!attr.distinctStock) {
                                    attrs.splice(i,1)
                                }
                            }
                        }
                        if(row.attr_qty) gridRow.attr_qty = row.attr_qty
                        if(row.other_class) gridRow.other_class= row.other_class
                        gridRow.item_no = item.item_no
                        gridRow.item_spec = item.item_spec
                        gridRow.item_alias = item.item_alias
                        gridRow.item_remark = item.item_remark
                        gridRow.item_class = item.item_class
                        gridRow.class_name = item.class_name

                        let isLoadVirtualProduceDate = true
                        if (window.g_companySetting && window.g_companySetting.loadVirtualProduceDate && window.g_companySetting.loadVirtualProduceDate == "False") {
                            isLoadVirtualProduceDate = false
                        }
                        if(isLoadVirtualProduceDate){
                            // 如果商品没有启用严格生产日期，尝试使用上一行的虚拟产期
                            if (!item.batch_level || item.batch_level === "0") {
                                // 获取上一行的虚拟产期
                                if (rowIndex > 0) {
                                    var prevRowVirtualProduceDate = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex - 1, 'virtual_produce_date');
                                    gridRow.virtual_produce_date = prevRowVirtualProduceDate || item.virtual_produce_date || "";
                                } else {
                                    gridRow.virtual_produce_date = item.virtual_produce_date || "";
                                }
                            } else {
                                gridRow.virtual_produce_date = item.virtual_produce_date || "";
                            }
                        }else{
                            gridRow.virtual_produce_date = ""
                        }
                        gridRow.location = item.location
                        gridRow.valid_days = item.valid_days+item.valid_day_type
                        gridRow.valid_day_type = item.valid_day_type
                        gridRow.supplier_name = item.supplier_name
                        gridRow.supplier_id = item.supplier_id
                        gridRow.manufactor_id = item.manufactor_id
                        gridRow.manufactor_name = item.manufactor_name
                        gridRow.produce_date = ""
                        gridRow.batch_no = ""
                        gridRow.stock_qty_unit = item.stock_qty_unit
                        gridRow.sell_pend_qty_unit = item.sell_pend_qty_unit
                        gridRow.usable_stock_qty_unit = item.usable_stock_qty_unit
                        gridRow.stock_qty = item.stock_qty
                        gridRow.batch_level = item.batch_level
                        if (item.batch_level == "1" || item.batch_level == "2") {
                            isShowProduceDate = true
                        }
                        if (item.batch_level == "2") {
                            isShowBatchNo = true
                        }
                        gridRow.unit_no = unit_no
                        gridRow.unit_factor = unit_factor
                        gridRow.unit_relation1 = getRowUnitRelation(gridRow)
                        gridRow.quantity = quantity
                        gridRow.remark = remark
                        if(!orig_price) orig_price = price;
                        gridRow.orig_price = orig_price
                        gridRow.sys_price = price
                        gridRow.real_price = price
                       
                        gridRow.wholesale_price = wholesale_price
                        if (orig_price) {
                            gridRow.discount = toMoney(price / orig_price * 100,1)
                            if (!myIsNumber(gridRow.discount)) gridRow.discount = ''
                        }
                        gridRow.real_price = price
                        var qty = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'quantity');
                        if(qty) gridRow.sub_amount = toMoney(parseFloat(qty) * price, 2)
                        gridRow.last_time_price = last_time_price
                        gridRow.retail_price = retail_price
                        gridRow.s_retail_price = s_retail_price
                        gridRow.recent_retail_price = recent_retail_price
                        gridRow.barcode = barcode
                                          
                        gridRow.unit_weight = unit_weight
                        gridRow.weight = weight
                        gridRow.unit_volume = unit_volume
                        gridRow.volume = volume
                        gridRow.order_sub_name = order_sub_name
                        gridRow.order_sub_id = order_sub_id
                        gridRow.order_flow_id= order_flow_id
                        gridRow.order_item_sheets_id=order_item_sheets_id
                        gridRow.order_item_sheets_no=order_item_sheets_no
                        gridRow.order_qty = order_qty
                        gridRow.specific_qty_unit = specific_qty_unit
                        gridRow.order_price = order_price
                        gridRow.disp_flow_id = disp_flow_id
                        gridRow.disp_sheet_id = disp_sheet_id
                        gridRow.disp_month_id = disp_month_id
                        gridRow.cost_price = cost_price
                        gridRow.cost_price_avg = cost_price_avg
                        gridRow.cost_price_buy = cost_price_buy
                        gridRow.cost_price_recent = cost_price_recent
                        gridRow.cost_price_prop = cost_price_prop
                        gridRow.trade_type = trade_type
                        gridRow.trade_type_name = trade_type_name
                        gridRow.other_class = item.other_class
                        gridRow.brand_id = item.brand_id
                        gridRow.brand_name = item.brand_name
                        gridRow.s_unit_no = s_unit_no
                        gridRow.m_unit_no = m_unit_no
                        gridRow.b_unit_no = b_unit_no
                        gridRow.branch_position = window.g_dicDefualtBranchPosition && window.g_dicDefualtBranchPosition[branchID] ? window.g_dicDefualtBranchPosition[branchID].branch_position : "0"
                        gridRow.branch_position_name = window.g_dicDefualtBranchPosition && window.g_dicDefualtBranchPosition[branchID] ? window.g_dicDefualtBranchPosition[branchID].branch_position_name : ""
                        if (gridRow.branch_position_name) {
                            isShowBranchPosition = true
                        }
                        let branchId = branchID ? branchID : "-1"
                        let keyForBatch = item.item_id +"_" + branchId + "_" + gridRow.branch_position
                        if (!window.itemsBatchStockTotal) window.itemsBatchStockTotal = {}
                        if (!window.itemsBatchStockForShow) window.itemsBatchStockForShow = {}
                        window.itemsBatchStockTotal[keyForBatch] = data.batchStockTotal && data.batchStockTotal[keyForBatch] ? data.batchStockTotal[keyForBatch]:[]
                        window.itemsBatchStockForShow[keyForBatch] = data.batchStockForShow&&data.batchStockForShow[keyForBatch]?data.batchStockForShow[keyForBatch]:[]
                        if ((gridRow.branch_position != "0" || gridRow.batch_level)) {
                            if (sheetType === "X" || sheetType === "XD" || sheetType === "T" || sheetType === "JH" || sheetType === "HH") {
                                 
                                let batchStockForShow = window.itemsBatchStockForShow[keyForBatch]?JSON.parse(JSON.stringify(window.itemsBatchStockForShow[keyForBatch])):[]
                                batchStockForShow = batchStockForShow.filter(e=>e.batch_id !=="0")
                                batchStockForShow.sort((pre, next) => {
                                    return Date.parse(pre.produce_date) - Date.parse(next.produce_date)
                                })
                                batchStockForShow.sort((pre, next) => {
                                    if (pre.batch_no > next.batch_no) return 1
                                })
                                    if ((batchStockForShow.length === 0 || (batchStockForShow.length !== 0 && (sheetType === "T" || sheetType === "JH"))) && window.getSettingValue('showNoProduceDate').toLowerCase() === 'true') {
                                    // 开启设置项，退货单和借货单自动带出无产期，不带出最早产期
                                    gridRow.produce_date = "无产期"
                                } else {
                                    gridRow.produce_date = batchStockForShow.length ? batchStockForShow[0].produce_date : ''
                                }
                                gridRow.batch_no = batchStockForShow.length ? batchStockForShow[0].batch_no : ''
                                gridRow.batch_id = batchStockForShow.length ? batchStockForShow[0].batch_id : '0'
                            }
                            let batchStockToTal =  window.itemsBatchStockTotal[keyForBatch]?JSON.parse(JSON.stringify(window.itemsBatchStockTotal[keyForBatch])):[]
                            let flag = batchStockToTal.some(e=>{
                                if(e.batch_no == gridRow.batch_no && e.produce_date == gridRow.produce_date){
                                    gridRow.stock_qty_unit = e.stock_qty_unit
                                    gridRow.stock_qty = e.stock_qty
                                    if("X,XT,XD,CG".indexOf(sheetType)>-1){
                                        gridRow.sell_pend_qty_unit = e.sell_pend_qty_unit
                                        gridRow.usable_stock_qty_unit = e.usable_stock_qty_unit
                                    }
                                    return true
                                }
                            })
                            if(!flag){
                                gridRow.stock_qty_unit = ""
                                gridRow.stock_qty = "0"
                                if("X,XT,XD,CG".indexOf(sheetType)>-1)
                                {
                                    gridRow.sell_pend_qty_unit = ""
                                    gridRow.usable_stock_qty_unit = ""
                                }
                            }
                        }
                        if (requestString('copy')) {
                            // 复制单据保留原来的生产日期,视情况看要不要取消
                            debugger
                            gridRow.produce_date = row.produce_date
                        }
                        rowindexs.push(rowIndex)
                        if (sheetType === "CG") {
                            gridRow.cost_price_avg_unit = cost_price_avg_unit
                        }
                        if (window.g_operRights.delicacy.saleItemDefQty) {
                            if ((sheetType == "X" || sheetType == "T" || sheetType == "XD" || sheetType == "TD") && window.g_operRights.delicacy.saleItemDefQty.value && gridRow.mum_attributes === "") {
                                if(!gridRow.quantity) gridRow.quantity = window.g_operRights.delicacy.saleItemDefQty.value
                               
                            }
                        }
                        updateRowSubAmount(rowIndex)
                        rowIndex++
                    })
                    if(isShowProduceDate)  $('#jqxgrid').jqxGrid('showcolumn', 'produce_date');
                    if(isShowBatchNo)  $('#jqxgrid').jqxGrid('showcolumn', 'batch_no');
                    if(isShowBranchPosition)  $('#jqxgrid').jqxGrid('showcolumn', 'branch_position_name');
                    if(sheetType=="CG" ||sheetType =="CT" ||sheetType == "CD"){
                        if( $("#other_fee1_amount>input").val() ||  $("#other_fee2_amount>input").val()){
                            updateCostAmount()
                        }
                    }                   
                    updateTotalAmount()
                    $('#jqxgrid').jqxGrid('updategrid')
                    
                    window.g_queryStatus='done'
                }
            },
            error: function (xhr) {
                window.g_queryStatus='error'
            }
        })
        
    }
    var unitRights = @Html.Raw(Model.JsonOperRights)
    $(function () {
        if (unitRights) {
            Object.keys(unitRights).forEach(key => {
                if (!unitRights[key]) {
                    var id = '#btn' + key.replace(key[0], key[0].toUpperCase())
                    console.log(key, id, $(id))
                    $(id).hide()
                }
            })
        }
    })


    //function getBranchPositionForReturn(rowindexs,branch_id,backBranchPositionType) {
    //    var rowsData = $("#jqxgrid").jqxGrid('getrows')
    //    $.ajax({
    //        url: '/api/SaleSheet/GetBranchPositionForReturn',
    //        type: 'GET',
    //        contentType: 'application/json',
    //        processData: true,
    //        data: {
    //            operKey: g_operKey,
    //            branchId: branch_id,
    //            backBranchPositionType: backBranchPositionType
    //        },
    //        success: function (data) {
    //            if (data.result == "OK") {
    //                rowindexs.forEach(i => {
    //                    rowsData[i].branch_position = data.records?data.records.branch_position:""
    //                    rowsData[i].branch_position_name = data.records?data.records.branch_position_name:""
    //                    $('#jqxgrid').jqxGrid('setcellvalue', i, 'branch_position_name',  rowsData[i].branch_position_name);
    //                })
                   
    //            }
    //        }
    //    })
    //}
    function UploadSheetToPiaoZhengTong() {//同步销售单或销售订单到安徽票证通系统
        function loginPiaoZhengTong( callBack) {
            var companySetting = window.g_companySetting
            const data = {
                ticketAccessSysAccount: companySetting.ticketAccessSysAccount,
                ticketAccessSysPwd: companySetting.ticketAccessSysPwd,
                ticketAccessSysKey: companySetting.ticketAccessSysKey,
                operKey:window.g_operKey
            }
            $.ajax({
                dataType: "json", //返回的数据类型
                contentType: 'application/json',
                url: `/api/SaleSheet/LoginPiaoZhengTong`,
                data: JSON.stringify(data),
                type: "POST"
            }).then(res => {
                callBack(res)
            })
        }
                function uploadSupcust(token,sheetDataResp,nextfn){

                         console.log(sheetDataResp)
                    //(string)data.token, (string)data.oentityCode, (string)data.bossName, (string)data.mobile, (string)data.supName, (string)data.address
                    var uploadSupcustData = {
                        token: token,
                        oentityCode: sheetDataResp.license_no ? sheetDataResp.license_no : sheetDataResp.supcust_id.padEnd(15,'1'),
                        bossName: sheetDataResp.boss_name.padEnd(2,"*"),
                        mobile: sheetDataResp.mobile,
                        supcust_no: sheetDataResp.supcust_no,
                        supName: sheetDataResp.sup_name,
                        address: sheetDataResp.sup_addr ? sheetDataResp.sup_addr:"暂无地址"
                    }
                    $.ajax({
                        dataType: "json", //返回的数据类型getSheetToPrint
                        contentType: 'application/json',
                        url: `/api/ClientEdit/SaveSupcustPiaoZhengTong?operKey=${g_operKey}`,
                        data: JSON.stringify(uploadSupcustData),
                        type: "POST"
                    }).then(res => {
                        console.log(res)
                        console.log("上传成功")
                        nextfn()
                    })

        
                   
        }
        Date.prototype.format =  function (fmt) {  
        var o = {
            "M+":  this.getMonth() + 1,  // 月份 
            "d+":  this.getDate(),  // 日 
            "h+":  this.getHours(),  // 小时 
            "m+":  this.getMinutes(),  // 分 
            "s+":  this.getSeconds(),  // 秒 
            "q+": Math.floor(( this.getMonth() + 3) / 3),  // 季度 
            "S":  this.getMilliseconds()  // 毫秒 
        };
        if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, ( this.getFullYear() + "").substr(4 - RegExp.$1.length));
        for ( var k  in o)
        if ( new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
         return fmt;
        }
        function getCurSavedSheetInfo(sheet_id, cbSuccess, cbFail) {
            if (!sheet_id) {
                bw.toast("该销售单未生成，请点击保存再进行操作")
                return
            }
            var sheetType=$('#sheetType').val()
            if (sheetType == 'XD' || sheetType == 'TD') {
                sheetType = 'SaleOrderSheet'
            }
            else if (sheetType == 'X' || sheetType == 'T') {
                sheetType = 'SaleSheet'
            }
            $.ajax({
                url: `/api/${sheetType}/GetSheetToPrint`,
                type: 'GET',
                contentType: 'application/json',
                data: {
                    operKey: g_operKey,
                    sheet_id: sheet_id,
                    smallUnitBarcode: true
                },
                success: function (data) {
                    if (data.result === 'OK') {
                        var sheet = data.sheet
                        cbSuccess(sheet)
                    }
                    else {
                        bw.toast(data.msg, 3000)
                    }
                },
                error: function (xhr) {
                    console.log("返回响应信息：" + xhr.responseText)
                }
            })
        }

        var sheetID = $("#sheet_id").val()
        if (!sheetID) {
            bw.toast("请先保存单据")
            return
        }
        loginPiaoZhengTong( res => {
            if (res.data.status != 0) {
                bw.toast("票证通登录失败:" + res.data.errorMsg)
            }
            else {
                console.log("[票证通]登录成功")
                getCurSavedSheetInfo(sheetID,  (sheetDataResp) => {
                    if (res.result === "Error") {
                        bw.toast(res.msg, 5000); return;
                    }
            
                   /**
                    if (!sheetDataResp.license_no) {
                        bw.toast(`该客户没有设置营业执照编号`)
                        return
                    }*/
                    //sheetDataResp.sheetRows.forEach((row,index) => {
                    //    if (!row.produce_date) {
                    //        bw.toast(`第${index}行${row.item_name}请输入生产日期`)
                  //          return
                  //      }
                  //  })
                    uploadSupcust(res.data.result.token,sheetDataResp,()=>{
                        console.log("上传单据")
                                var uploadData = {}
                    uploadData.token = res.data.result.token
                    uploadData.type = 1
                    uploadData.remark=sheetDataResp.make_brief
                    uploadData.oentityCode = sheetDataResp.license_no ? sheetDataResp.license_no : sheetDataResp.supcust_id.padEnd(15,'1')
                    var ticketAccessRows = []
                    sheetDataResp.sheetRows.forEach(sheetRow => {
                            console.log(sheetRow)
                        //[{ "procode": "7506856061721067", "proname": "干枣", "proSpec": "1*500g", "validdate": "240", "validdateUnit": "0", "proUnit": "包", "protype": "1.", "inspCert": null, "proIpc": "20180725", "price": "8", "proCount": "4", "total": "32.00" }]
                        var ticketAccessRow = {}
                        ticketAccessRow.procode = sheetRow.barcode
                        ticketAccessRow.proname = sheetRow.item_name
                        ticketAccessRow.validdate = sheetRow._valid_days
                        ticketAccessRow.validdateUnit = 0
                        ticketAccessRow.proSpec = sheetRow.item_spec
                        ticketAccessRow.proUnit = sheetRow.unit_no
                        const realProduceDate=sheetRow.produce_date?sheetRow.produce_date:sheetRow.virtual_produce_date
                        ticketAccessRow.proIpc = realProduceDate?realProduceDate:new Date().format("yyyyMMdd")
                        ticketAccessRow.proCount = sheetRow.quantity
                        ticketAccessRow.price = sheetRow.real_price
                        ticketAccessRow.total = sheetRow.sub_amount
                        ticketAccessRow.protype = "1."
                        ticketAccessRows.push(ticketAccessRow)
                    })
                    uploadData.detail = ticketAccessRows
                    $.ajax({
                        dataType: "json", //返回的数据类型getSheetToPrint
                        contentType: 'application/json',
                        url: `/api/SaleSheet/UploadSheetToPiaoZhengTong?operKey=${g_operKey}`,
                        data: JSON.stringify(uploadData),
                        type: "POST"
                    }).then(res => {

                        if (res.resp.status == "0") {
                            bw.toast("上传成功")
                        }
                        else {
                            bw.toast("同步失败:" + res.resp.errorMsg)
                        }
                    })
                    })
            

                });

            }
        })
    }

    function initeditor_real_price(row, cellvalue, editor, celltext, pressedkey) {
            // set the editor's current value. The callback is called each time the editor is displayed.
            var inputElement = editor.find('div')[0];
            $(inputElement).jqxInput('clearOptions');
            var inputField = editor.find('input');
            if (pressedkey) {
                inputField.val(pressedkey);
                inputField.jqxInput('selectLast');
            }
            else {
                inputField.val({ value: cellvalue, label: celltext});
                inputField[0].value = celltext||'';
                inputField.jqxInput('selectAll');
            }
            $(inputElement).jqxInput('suggest',true);
    }

    function createeditor_real_price(row, cellvalue, editor, cellText, width, height) {
        var element = $('<div id="txtRealPrice"></div >');
        editor.append(element);
        var inputElement = editor.find('div')[0];

        var dataFields = new Array(
            { datafield: "price_type", text: "类型", width: 100 },
            { datafield: "show_price", text: "价格", width: 140 },            
            { datafield: "price", text: "单价", width: 60 }
        )

        var gridSlt='#jqxgrid'
        if (window.curGridSlt) gridSlt=window.curGridSlt           
            $(inputElement).jqxInput({
                placeHolder: "", height: height, width: width,
                borderShape: "none",
                buttonUsage: 'list',
                showHeader: true,
                searchable: false,
                buttonsHtml:`<div style=""><button onmousedown="btnItemHistory_click()">历史记录</button>  </div>`,
                dropDownHeight: 250,
                displayMember: "price",
                valueMember: "price",
                dataFields: dataFields,
                searchFields: [],
                maxRecords: 4,
                url: '',
                /*source: [{ unit_no: 'P', unit_factor: 1 }, { unit_no: 'x', unit_factor: 10 }],*/
                source: function (query, response) {
                    //  debugger;
                    var item = query;//.split(/,\s/).pop();
                    // update the search query.
                    // inputElement.jqxInput({ query: item });
                    var cell = $(gridSlt).jqxGrid('getselectedcell')
                    var rowindex = cell.rowindex
                    let sheetRows = $(gridSlt).jqxGrid('getrows')
                    var sheetRow = sheetRows[rowindex]
                    //var item_id = $('#jqxgrid').jqxGrid('getcellvalue', rowindex, "item_id");
                    //let key = sheetRow.item_id
                    //if (sheetRow.order_sub_id && (sheetRow.order_price).toString()) key = sheetRow.item_id + '_' + sheetRow.order_sub_id + '_' + sheetRow.order_price
                    var key = getQueriedItemsKeyFromRow(sheetRow)
                    if (window.g_queriedItems && key) {
                        var item = window.g_queriedItems[key];
                        var prices=[]
                        if (item) {
                            var canSeeInPrice=false
                            if (window.g_operRights && window.g_operRights.delicacy && window.g_operRights.delicacy.seeInPrice) {
                                if (window.g_operRights.delicacy.seeInPrice.value) {
                                    canSeeInPrice=true
                                }
                            }

                            var wholesale_price = '', buy_price = '', retail_price = '', recent_price = '',cost_price='',unit_cost_price=''
                            var unit_wholesale_price = '', unit_buy_price = '', unit_retail_price = '', unit_recent_price=''
                           
                            var recent_time = ''
                            var planPrices = []
                            var curUnitPrice=''
                            item.units.forEach(unit => {
                                if (unit.recent_price) {
                                    recent_price += `${unit.recent_price}/${unit.unit_no}`
                                    recent_time = unit.recent_time
                                    if (unit.unit_no == sheetRow.unit_no)  
                                        unit_recent_price=unit.recent_price 
                                }
                                if (unit.wholesale_price) {
                                    wholesale_price += `${unit.wholesale_price}/${unit.unit_no}`
                                    if (unit.unit_no == sheetRow.unit_no)
                                        unit_wholesale_price=unit.wholesale_price
                                }
                                if (canSeeInPrice) {

                                    if(unit.buy_price){
                                        buy_price += `${unit.buy_price}/${unit.unit_no}`
                                        if (unit.unit_no == sheetRow.unit_no)
                                            unit_buy_price=unit.buy_price
                                    }
                                   


                                    var costPriceType=''
                                    var cur_cost_price=''                                  
                                    if(window.g_companySetting) costPriceType= window.g_companySetting.costPriceType


                                    if (costPriceType== '3' && unit.buy_price) cur_cost_price = toMoney(unit.buy_price,2)
                                    else if (costPriceType == '2' && unit.cost_price_avg)
                                        cur_cost_price = toMoney(unit.cost_price_avg*unit.unit_factor, 2)
                                    else if (costPriceType == '4' && unit.cost_price_recent)
                                            cur_cost_price = toMoney(unit.cost_price_recent * unit.unit_factor, 2)
                                    else if (costPriceType == '1' && unit.cost_price_spec)
                                            cur_cost_price = toMoney(unit.cost_price_spec * unit.unit_factor, 2)

                                    if(cur_cost_price){
                                        cost_price += `${cur_cost_price}/${unit.unit_no}`
                                        if (unit.unit_no == sheetRow.unit_no)
                                                unit_cost_price=cur_cost_price
                                    }
                                }



                                if ( unit.retail_price) {
                                    retail_price += `${unit.retail_price}/${unit.unit_no}`
                                    if (unit.unit_no == sheetRow.unit_no)
                                        unit_retail_price=unit.retail_price
                                }
                                if (unit.planPrices) {
                                    for (var plan_name in unit.planPrices) {
                                        var unit_price =  unit.planPrices[plan_name]
                                        var unitPlan =  planPrices[plan_name]
                                        if (!unitPlan) unitPlan = {price_type:plan_name,show_price:'',price:''}
                                         
                                        unitPlan.show_price +=  `${unit_price}/${unit.unit_no}` 
                                        if(sheetRow.unit_no==unit.unit_no) unitPlan.price=unit_price
                                        planPrices[plan_name] = unitPlan 
                                    }
                                }
                                 
                                
                                


                            })
                            var price
                            if (recent_price) {
                                price = { price_type: recent_time,price:unit_recent_price, show_price: recent_price }
                                prices.push(price)
                            }
                            if (wholesale_price) {
                                price = { price_type: '批发价',price:unit_wholesale_price, show_price: wholesale_price }
                                prices.push(price)
                            }
                            if (retail_price) {
                                price = { price_type: '零售价',price:unit_retail_price, show_price: retail_price }
                                prices.push(price)
                            }
                            if (buy_price) {
                                price = { price_type: '进价', price:unit_buy_price, show_price: buy_price }
                                prices.push(price)
                            }
                               if (cost_price) {
                                    price = { price_type: '成本价', price:unit_cost_price, show_price: cost_price }
                                    prices.push(price)
                                }
                                
                            for (var plan_name in planPrices) {
                                var plan_price = planPrices[plan_name]
                                price = { price_type: plan_name,show_price:plan_price.show_price, price: plan_price.price, }
                                prices.push(price)
                            }
                            response(prices,null,true)
                        }
                    }
                },
                renderer: function (itemValue, inputValue, way) {
                   // if (way == 'keyboard') return inputValue
                   // else return itemValue
                       return itemValue;
                   // if(itemValue) return itemValue;
                   // else return inputValue
                }
            })         
    }
     
    function init_recent_price_editor(row, cellvalue, editor, celltext, pressedkey) {
        var inputElement = editor.find('div')[0];
        $(inputElement).jqxInput('clearOptions');
        var inputField = editor.find('input');
        if (pressedkey) {
            inputField.val(pressedkey);
            inputField.jqxInput('selectLast');
        }
        else {
            inputField.val({ value: cellvalue, label: celltext });
            inputField[0].value = celltext || '';
            inputField.jqxInput('selectAll');
        }
        $(inputElement).jqxInput('suggest', true);
    }

    function create_recent_retail_price_editor(row, cellvalue, editor, cellText, width, height) {
        var element = $('<div id="txtRecentRetailPrice"></div>')
        editor.append(element)

        var sheetType = ''
        if ($('#sheetType').length > 0) {
            sheetType = $('#sheetType').val()
        }

        var inputElement = editor.find('div')[0];

        var dataFields = [

            { datafield: 'type', text: "类别", width: 80 },
            { datafield: 'price', text: "价格", width: 80 },
        ]
        var gridSlt = '#jqxgrid'
        if (window.curGridSlt) gridSlt = window.curGridSlt
        $(inputElement).jqxInput({
            placeHolder: "", height: height, width: width,
            borderShape: 'none',
            buttonUsage: 'list',
            /*       checkboxes: true,*/
            showHeader: true,
            dropDownHeight: 180,
            displayMember: 'price',
            valueMember: 'price',
            dataFields: dataFields,
            searchFields: [],
            searchMode: 'none',
            maxRecords: 3,
            // getSmiliarItems方法增加单位信息等
            source: function (query, response) {
                // debugger

                var cell = $(gridSlt).jqxGrid('getselectedcell');
                var rowindex = cell.rowindex
                let sheetRows = $(gridSlt).jqxGrid('getrows')
                var sheetRow = sheetRows[rowindex]
                var key = getQueriedItemsKeyFromRow(sheetRow)
                var source = []
                if (window.g_queriedItems && key) {
                    // 加入该单位的零售价
                    var item = window.g_queriedItems[key];

                    if (item) {
                        /*                    var canSeeInPrice = false
                                            if (window.g_operRights && window.g_operRights.delicacy && window.g_operRights.delicacy.seeInPrice) {
                                                if (window.g_operRights.delicacy.seeInPrice.value) {
                                                    canSeeInPrice = true
                                                }
                                            }*/

                        item.units.forEach(unit => {
                            if (unit.retail_price) {
                                if (unit.unit_no == sheetRow.unit_no) {
                                    source.push({ 'type': '单位零售价', 'price': unit.retail_price })
                                }
                            }
                        })
                    }
                }
                // console.log(cell)
                var itemId = $(gridSlt).jqxGrid('getcellvalue', cell.rowindex, "item_id");
                var unitNo = $(gridSlt).jqxGrid('getcellvalue', cell.rowindex, "unit_no");
                var supcustId = $('#supcust_id').val().value;
                // console.log(barcode, itemName)
                $.ajax({
                    url: '/api/SaleSheet/GetRecentRetailPrice',
                    type: 'GET',
                    contentType: 'application/json',
                    data: {
                        operKey: g_operKey,
                        item_id: itemId,
                        unit_no: unitNo,
                        supcust_id: supcustId
                    },
                    success: function (data) {
                        if (data.result === 'OK') {
                            console.log("match success");

                            source.push({
                                'type': '最近零售价',
                                'price': data.record.recent_retail_price
                            })

                        } else {
                            console.log("match fail");

                        }
                        response(source);
                    }
                })

            },

            // ??
            renderer: function (itemValue, inputValue, way) {
                if (way == 'keyboard') return inputValue
                else return itemValue
            }

        })


    }


    function btnItemHistory_click() {
       // $('#popItemHistory').jqxWindow('open')
        var sheetType = ''
        if ($('#sheetType').length>0) {
            sheetType = $('#sheetType').val()
        }

        var cell = $('#jqxgrid').jqxGrid('getselectedcell');
        var rowIndex = cell.rowindex;
        var row = $('#jqxgrid').jqxGrid('getrowdata', rowIndex);
        
         

        
        var supcust = $('#supcust_id').val();
        if (!row.item_id || !supcust || !supcust.value) {
            return
        }
        //debugger
        var startDay = new Date()
        startDay.setMonth(startDay.getMonth()-3)
        startDay = getDateText(startDay)
        //var endDay = new Date()
        
      //  endDay = getDateText(endDay)
         
        var url = ''
         var title = '销售明细表';
        if (sheetType && ',X,T,XD,TD,'.indexOf(',' + sheetType + ',') >= 0) {
            url = `/Report/SaleItemHistory?operKey=${window.g_operKey}&forItemHistory=1&supcust_id=${supcust.value}&sup_name=${supcust.label}&item_id=${row.item_id}&item_name=${row.item_name}&startDay=${startDay}`;

        }
        else if (sheetType && ',CG,CT,CD,'.indexOf(',' + sheetType + ',') >= 0) {
            url = `/Report/BuysDetail?operKey=${window.g_operKey}&c=1&supcust_id=${supcust.value}&sup_name=${supcust.label}&item_id=${row.item_id}&item_name=${row.item_name}&startDay=${startDay}`;
            title = '采购明细表'
        }
        $('#popItemHistory').jqxWindow('open')
        $("#popItemHistory").jqxWindow('setContent', `<iframe src="${url}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);


        

       // window.parent.newTabPage(title, `${url}`,window);
         

    }

    function btnCustomerHistory_click() {
        // 获取当前单据的客户信息
        var supcust = $('#supcust_id').val();

        if (!supcust || !supcust.value) {
            bw.toast('请先选择客户');
            return;
        }

        // 计算6个月前的日期作为开始时间
     
        var startDay = new Date()
        startDay.setMonth(startDay.getMonth()-6)
        startDay = getDateText(startDay)
        
        // 构建URL参数，传递客户ID、客户名称和开始时间
        var params = `supcust_id=${supcust.value}`;
        if (supcust.label && supcust.label !== '') {
            params += `&sup_name=${encodeURIComponent(supcust.label)}`;
        }
        params += `&startDay=${encodeURIComponent(startDay)}`;

        // 根据当前页面类型确定跳转目标
        var currentUrl = window.location.pathname.toLowerCase();
        var targetUrl = '';
        var tabTitle = '';

        if (currentUrl.includes('saleordersheet')) {
            // 销售订单开单页面 -> 跳转到销售订单历史页面
            targetUrl = `Sheets/SaleOrderSheetView?${params}`;
            tabTitle = '销售订单历史';
        } else {
            // 销售单开单页面 -> 跳转到销售单历史页面
            targetUrl = `Sheets/SaleSheetView?${params}`;
            tabTitle = '销售单历史';
        }

        // 调试信息
        console.log('跳转参数:', {
            currentUrl: currentUrl,
            supcust_id: supcust.value,
            sup_name: supcust.label,
            startDay: startDay,
            targetUrl: targetUrl,
            tabTitle: tabTitle
        });

        // 跳转到对应的历史页面
        window.parent.newTabPage(tabTitle, targetUrl, window);
    }

    function initeditor_trade_type(row, cellvalue, editor, celltext, pressedkey) {
        var inputElement = editor.find('div')[0];
        $(inputElement).jqxInput('clearOptions');
        var inputField = editor.find('input');
        if (pressedkey) {
            inputField.val(pressedkey);
            inputField.jqxInput('selectLast');
        }
        else {
            inputField.val({ value: cellvalue, label: celltext});
            inputField[0].value = celltext||'';
            inputField.jqxInput('selectAll');
        }
        $(inputElement).jqxInput('suggest',true);
    }

    function createeditor_trade_type(row, cellvalue, editor, cellText, width, height) {
            
                var element = $('<div id="txtUnitNo"></div >');
                editor.append(element);
                var inputElement = editor.find('div')[0];

                var dataFields = new Array({ datafield: "trade_type", text: "", width: 50, hidden:true },
                    { datafield: "trade_type_name", text: "", width: 50 }
                );

                $(inputElement).jqxInput({
                    placeHolder: "交易方式", height: height, width: width,
                    borderShape: "none",
                    buttonUsage: 'list',
                    showHeader: true,
                    dropDownHeight: 160,
                    displayMember: "trade_type_name",
                    valueMember: "trade_type",
                    dataFields: dataFields,
                    searchFields: [],
                    maxRecords: 4,
                    url: '',
                    source: [{ trade_type: 'J', trade_type_name: '借货' }, {trade_type: 'KS', trade_type_name: '客损'}],
                    //source: function (query, response) {
                    //    //  debugger;
                    //    var item = query;//.split(/,\s/).pop();
                    //    // update the search query.
                    //    // inputElement.jqxInput({ query: item });
                    //    var cell = $('#jqxgrid').jqxGrid('getselectedcell');
                    //    var rowindex = cell.rowindex;
                    //    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', rowindex, "item_id");
                    //    if (window.g_queriedItems && item_id) {
                    //        var item = window.g_queriedItems[item_id];
                    //        if (item) {
                    //            var units = JSON.parse(JSON.stringify(item.units))
                    //            if ($('#useSmallUnit').length == 1) {
                    //               var useSmallUnit = $('#useSmallUnit').jqxCheckBox('checked')
                    //               if (useSmallUnit) {
                    //                   units.reverse()
                    //               }
                    //            }
                    //            response(units,null,true)
                    //        }
                    //    }
                    //},
                    renderer: function (itemValue, inputValue) {
                        // debugger;
                        var terms = inputValue.split(/,\s*/);
                        // remove the current input
                        terms.pop();
                        // add the selected item
                        terms.push(itemValue);
                        // add placeholder to get the comma-and-space at the end

                        // terms.push("");
                        //var value = terms.join(", ");
                        //return terms;
                        ;
                        return itemValue;
                    }
                });
                $(inputElement).on('optionSelected',
                function (a, b) {
                       
                    var value = $(inputElement).val();
                    var trade_type = value.value;

                    var name = '';
                    if (value.name)
                        name = value.name;
                    var cell = $('#jqxgrid').jqxGrid('getselectedcell');
                    var rowIndex = cell.rowindex;
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, "item_id");
                    if (item_id) {
                        if(trade_type == 'J') {
                            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, "real_price", '0');
                            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, "remark", '借货');
                            var rowData=$('#jqxgrid').jqxGrid('getrowdata',cell.rowindex)
                            cellendedit({ args: {datafield:'trade_type',rowindex:cell.rowindex,row:rowData,value:value,oldvalue:''} }) 
                        }
                        if (trade_type == 'KS') { 
                            let ksSubId=''
                            //找出用于客损的sub_id和sub_name
                            if(window.g_companySetting.feeOutSubForKS && window.g_companySetting.feeOutSubForKS !='')  ksSubId = window.g_companySetting.feeOutSubForKS
                            if (ksSubId != '') { 
                                     $.ajax({
                                     url: `/api/SaleSheet/GetSubNameByKs`,
                                     type: 'POST',
                                     contentType: 'application/json',
                                     data: JSON.stringify({ operKey: g_operKey, subId:ksSubId }),
                                     success: function(data) {
                                            window.ksSub =data
                         //开客损商品要校验是否已有订货会商品       
                              var hasOrderSubId = false
                              var rows = $('#jqxgrid').jqxGrid('getrows');
                            for (var i = 0; i < rows.length; i++) { 
                                if (rows[i].order_sub_id && rows[i].order_sub_id != '') { 
                                    hasOrderSubId = true
                                    break
                                }
                            }
                            if (hasOrderSubId) {
                                bw.toast('不能同时开定货会和客损商品',3000)
                                var id = $('#jqxgrid').jqxGrid('getrowid', rowIndex);
                                 $('#jqxgrid').jqxGrid('deleterow', id); 
                                 return
                            }
                              $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, "real_price", '0');
                              $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, "remark", '客损');
                              var rowData=$('#jqxgrid').jqxGrid('getrowdata',cell.rowindex)
                              cellendedit({ args: {datafield:'trade_type',rowindex:cell.rowindex,row:rowData,value:value,oldvalue:''} }) 
                                }
                            })
                            }
                      
                        }
                                
                    }
               })
        }


function onMouseEnterRealPrice(event, rowIndex,sysPrice) {
    if (sysPrice) {
        if (window.tmShowSysPrice) {
            clearTimeout(window.tmShowSysPrice)
            window.tmShowSysPrice = 0
        }
        window.tmShowSysPrice = setTimeout(() => {
            var pop = `<div id="popShowSysPrice"  style="z-index:********9;display:none;position:absolute; background-color:#eef8ee;border-color:#dde8dd;border-width:1px;border-style:solid;border-radius:6px;font-size:14px;text-align:center;line-height:40px"></div> `
            var popDiv = $('#popShowSysPrice');
            if (popDiv.length == 0) {
                $('body').append(pop) 
                popDiv = $('#popShowSysPrice');
            }
           
            popDiv.empty()
            popDiv.text('带出价:'+sysPrice)
            
            var tg = event.target
            var cell = $(tg).parent()
            var mw = 10
            var mh = 10
            var width = tg.offsetWidth + mw * 2;
            var height = tg.offsetHeight + mh
            var left = cell.offset().left-mw;
            var top = cell.offset().top - height-4;
           
            popDiv.css('left', left)
            popDiv.css('top', top)
            popDiv.css('width', width) 
            popDiv.css('height', height) 
            popDiv.show(0)
        }, 300)


    }
   
}

function onMouseLeaveRealPrice() {
    if (window.tmShowSysPrice) {
        clearTimeout(window.tmShowSysPrice)
        window.tmShowSysPrice=0
    }
    $('#popShowSysPrice').hide(0)


  }

function onEnterKeyDown1(ele) {
    if (!(ele.parentNode && ele.parentNode.id == 'barcode_input')) return 
    var searchStr = ele.value
    if (!searchStr) return
    window.g_searchStr=searchStr
    var params= { operKey: g_operKey, items_id: '', searchStr:searchStr, supcust_id: supcust_id, branch_id: branch_id, bGetAttrs:!window.attributes}
        $.ajax({
            url: '/api' + window.pageInfo.url + '/GetItemsInfo',
            type: 'GET',
            contentType: 'application/json',
            processData: true,
            data: params,
            success: function (data) {
                if (data.result === 'OK') {
                    if (data.items.length == 1) {

                    }
                    var tbl = '<table>'
                    window.g_itemsToSelect=data.items
                    for (var i = 0; i < data.items.length; i++) {
                        var item = data.items[i]
                        var tr =`<tr data-item_id=${item.item_id} onclick="onItemRowSelected(${item.item_id})"><td>${item.item_name}</td><td>${item.s_barcode}</td><td>${item.usable_stock_qty_unit}</td></tr>`
                        trs+=tr
                    }
                    tbl+='</table>'
                    $('#divItemsToSelect').html(tbl)

                    window.onItemRowSelected = function (item_id) {
                        var item = window.g_itemsToSelect.find(item => item.item_id == item_id)
                        var str = window.g_searchStr
                        var specUnitNo = ''
                        if (str.length >= 6 && item.b_barcode.indexOf(str) >= 0) {
                            specUnitNo = item.b_unit_no
                        }
                        else if (str.length >= 6 && item.m_barcode.indexOf(str) >= 0) {
                            specUnitNo = item.m_unit_no
                        }
                        else if (str.length >= 6 && item.s_barcode.indexOf(str) >= 0) {
                            specUnitNo = item.s_unit_no
                        }
                       // item
                       // addItemRowToGrid
                    }
                    window.addItemRowToGrid=function(rowIndex,rows,items,autoAddQty,specUnitNo){
                        if (!window.g_queriedItems) window.g_queriedItems = {}
                        var gridRows = $('#jqxgrid').jqxGrid('getrows');
                        var nAddRows = rowIndex + rows.length + 10 - gridRows.length

                        if (rowIndex == -1) {
                            if (nAddRows > 0) { 
                                addEmptyRows(nAddRows)
                            }
                        }
                        else{
                           addEmptyRows(rows.length-1, rowIndex) 
                        }

                        if(data.attrOptions) window.attrOptions = data.attrOptions
                        if(data.attributes) window.attributes = data.attributes

                        gridRows = $('#jqxgrid').jqxGrid('getrows');
                        rows.forEach(function (row) {

                            var item = null
                            var order_price = 0, order_unit_factor = 0, order_qty = 0, specific_qty_unit = '', order_sub_name = '', order_sub_id = '',order_flow_id='',order_item_sheets_id='',order_item_sheets_no='';
                            var disp_flow_id = '', disp_sheet_id = '', disp_month_id = '', disp_unit_no = '', disp_unit_factor = '';
                            var unit_factor = '', barcode = '', price = '', orig_price = '', retail_price = '', cost_price_recent = '', cost_price_prop = '', cost_price_avg = '', cost_price = '', unit_weight = '',unit_volume='', remark = '', s_retail_price = '', last_time_price = '';
                            let trade_type = '',trade_type_name = '';
                            var unitType = 'b'
                             if ($('#defaultUnit').length == 1) {
                                var defaultUnit = $('#defaultUnit').jqxInput('val')
                                if (defaultUnit && defaultUnit.value) unitType = defaultUnit.value
                            }
                            if (sheetType === "CG"){
                                remark = row.remark
                            }
                        

                            var unit_no = '', quantity = '', sub_amount = '';
                            for (var item_id in data.items) {
                                var curItem = data.items[item_id]                       
                                if (row.item_id == curItem.item_id) {
                                    item =JSON.parse( JSON.stringify(curItem))
                     
                                    var key = getQueriedItemsKeyFromRow(row)
                                    window.g_queriedItems[key] = item

                                    if (row.order_price) {
                                        order_price = row.order_price
                                        order_unit_factor = row.order_unit_factor
                                        order_qty = row.order_qty
                                        specific_qty_unit = row.order_qty_unit
                                        order_sub_id = row.order_sub_id
                                        order_flow_id = row.order_flow_id
                                        order_sub_name = row.order_sub_name
                                        order_item_sheets_id = row.order_item_sheets_id
                                        order_item_sheets_no = row.order_item_sheets_no

                                        remark = '定货'
                                        trade_type='DH'
                                    }

                                    if (row.disp_flow_id) {
                                        disp_flow_id = row.disp_flow_id
                                        disp_sheet_id = row.disp_sheet_id;
                                        specific_qty_unit = row.disp_left_qty + row.disp_unit_no;
                                        disp_month_id = row.disp_month_id;
                                        disp_unit_no = row.disp_unit_no;
                                        remark = row.disp_month + '月陈列'
                                        if (row.fee_sub_name) remark +='('+row.fee_sub_name+')'
                                        trade_type = 'CL'

                                    }

                                    if (row.borrowed_qty) {
                                        specific_qty_unit = row.order_qty_unit;
                                        order_qty = row.borrowed_qty
                                        if ($('#sheet_type').val() !== "SHEET_BORROW_ITEM") {
                                            trade_type = 'H';
                                            trade_type_name = '还货'
                                            remark = '还货'
                                        }
                                    }
                                }
                            }
                            if (!item) return
                            var order_s_unit_price = 0;
                            if (myIsNumber(order_price)) {
                                order_s_unit_price = order_price / order_unit_factor
                            }

                            var gridRow = gridRows[rowIndex]
                            var has_m_unit = false
                            item.units.forEach((unit) => {
                                if (unit.unit_type == 's') { 
                                    gridRow.s_barcode = unit.barcode
                                }
                                if (unit.unit_type == 'm') { 
                                    has_m_unit=true
                                }
                            })

                            item.units.forEach((unit) => {
                                // if(window.g_companySetting.show)
                                if (order_sub_name) unit.price = order_s_unit_price * unit.unit_factor
                                if (trade_type=="CL"||trade_type=="H") unit.price = 0
                           

                                if (disp_unit_no == unit.unit_no) {
                                    disp_unit_factor = unit.unit_factor
                                    unitType = unit.unit_type
                                }
                                if (unit.unit_type == 's') {
                                    s_retail_price = unit.retail_price                               
                                }
                                var bMet=false
                                if (row.quantity) {
                                    if (row.unit_no == unit.unit_no) bMet=true
                                }
                                else if (item.units.length == 1 || unit.unit_type == unitType
                                    || (unit.unit_type == 's' && unitType=='m' && !has_m_unit)//当中单位为默认单位时,如果没有中单位, 使用小单位
                                ) {
                                    bMet=true
                                }

                                if (bMet){
                                    unit_no = unit.unit_no
                                    unit_factor = unit.unit_factor
                                    barcode = unit.barcode
                                    retail_price = unit.retail_price
                                    price = unit.price
                                    unit_weight=unit.unit_weight
                                    unit_volume = unit.unit_volume
                                    var dotLen=2
                                    if(unit_factor==1) dotLen=7
                                    if (window.g_companySetting.costPriceType == '3' && unit.buy_price) {
                                        cost_price = toMoney(unit.buy_price,dotLen)
                                    }
                                    else if (window.g_companySetting.costPriceType == '2' && unit.cost_price_avg) {
                                        cost_price = toMoney(unit.cost_price_avg * unit.unit_factor, dotLen)
                                    }
                                    else if (window.g_companySetting.costPriceType == '4' && unit.cost_price_recent) {
                                        cost_price = toMoney(unit.cost_price_recent * unit.unit_factor, dotLen)
                                    }

                                    if (unit.recent_price) last_time_price = unit.recent_price
                                    if (unit.recent_orig_price) orig_price = unit.recent_orig_price
                                    if (disp_unit_no) {
                                        unit_no = disp_unit_no;
                                        unit_factor = disp_unit_factor;
                                        price = 0
                                    }
                                }
                                //return bMet
                            })
                            if (bUsePriceInRows) {
                                price = row.real_price
                                unit_no = row.unit_no
                                unit_factor = row.unit_factor
                                quantity = row.quantity
                                //sub_amount = row.sub_amount
                                orig_price = row.orig_price
                            }
                            else price = toMoney(price, 4)
  
                         
                            var columns = $('#jqxgrid').jqxGrid('getcolumns')
                            var col=columns.records.find(col=>col.datafield=='real_price')
                            if (col && col.hidden) {//无查看进价权限的情况，如果价格为空，就取0
                                if (!price) price = 0
                            }

                            if (row.quantity) quantity = row.quantity

                            order_price = toMoney(order_price, 4)
                        
                      
                            gridRow.item_id = item.item_id
                            gridRow.item_name = item.item_name
                            gridRow.mum_attributes = item.mum_attributes
                            if(gridRow.mum_attributes) gridRow.mum_attributes=JSON.parse(gridRow.mum_attributes)
                            if (',X,T,XD,TD,'.indexOf(',' + sheetType + ',') == -1) {
                                var attrs = gridRow.mum_attributes
                                for (var i = attrs.length - 1; i >= 0; i--) {
                                    var attr = attrs[i]
                                    if (!attr.distinctStock) {
                                        attrs.splice(i,1)
                                    }
                                }
                            }
                            if(row.attr_qty) gridRow.attr_qty = row.attr_qty
                            if(row.other_class) gridRow.other_class= row.other_class
                            gridRow.item_spec = item.item_spec
                            gridRow.valid_days = item.valid_days
                            gridRow.supplier_name = item.supplier_name
                            gridRow.manufactor_name = item.manufactor_name
                            gridRow.virtual_produce_date = item.produce_date
                            gridRow.stock_qty_unit = item.stock_qty_unit
                            gridRow.sell_pend_qty_unit = item.sell_pend_qty_unit
                            gridRow.usable_stock_qty_unit = item.usable_stock_qty_unit
                      
                        
                            gridRow.unit_no = unit_no
                            gridRow.unit_factor = unit_factor
                            gridRow.unit_relation1 = getRowUnitRelation(gridRow)
                            gridRow.quantity = quantity
                            gridRow.remark = remark
                            if(!orig_price) orig_price = price;
                            gridRow.orig_price = orig_price
                            gridRow.sys_price = price
                            gridRow.real_price = price
                            
                            
                            if (orig_price) {
                                gridRow.discount = toMoney(price / orig_price * 100,1)
                                if (!myIsNumber(gridRow.discount)) gridRow.discount = ''
                            }
                          
                            var qty = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'quantity');
                            if(qty) gridRow.sub_amount = toMoney(parseFloat(qty) * price, 2)
                            gridRow.last_time_price = last_time_price
                            gridRow.retail_price = retail_price
                            gridRow.s_retail_price = s_retail_price
                            gridRow.barcode = barcode
                                          
                            gridRow.unit_weight = unit_weight
                            gridRow.unit_volume = unit_volume
                            gridRow.order_sub_name = order_sub_name
                            gridRow.order_sub_id = order_sub_id
                            gridRow.order_flow_id = order_flow_id
                            gridRow.order_item_sheets_id=order_item_sheets_id
                            gridRow.order_item_sheets_no=order_item_sheets_no
                            gridRow.order_qty = order_qty
                            gridRow.specific_qty_unit = specific_qty_unit
                            gridRow.order_price = order_price
                            gridRow.disp_flow_id = disp_flow_id
                            gridRow.disp_sheet_id = disp_sheet_id
                            gridRow.disp_month_id = disp_month_id
                            gridRow.cost_price = cost_price
                            gridRow.cost_price_avg = cost_price_avg
                            gridRow.buy_price = buy_price
                            gridRow.recent_price = recent_price
                            gridRow.trade_type = trade_type
                            gridRow.trade_type_name = trade_type_name
                            gridRow.other_class = item.other_class
                            gridRow.brand_id = item.brand_id
                            gridRow.brand_name = item.brand_name
                            updateRowSubAmount(rowIndex)
                            //if ($('#jqxgrid').jqxGrid('getcolumn', 'cost_price_buy')) $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'cost_price_buy', cost_price_buy)
                            // row.refresh()
                      
                            rowIndex++
                        })
                        $('#jqxgrid').jqxGrid('updategrid')
                        let newrows =  $('#jqxgrid').jqxGrid('getrows');
                    
                        console.log(newrows)
                        if(sheetType=="CG" || sheetType == "CD"){
                            updateCostAmount()
                        }
                        updateTotalAmount()
                

                    }
                    
                }
            }
        })

}

    function aggregatesrenderer_sale_sub_amount(aggregates, column, element, summaryData) {
        var renderstring = "<div class='jqx-widget-content style='float: left; width: 100%; height: 100%; '>";
        var sale_amount = parseFloat($('#sale_amount').jqxInput('val'))
        var return_amount =parseFloat($('#return_amount').jqxInput('val'))
        $.each(aggregates, function (key, value) {
            var txt=value
            if (return_amount > 0) {
                txt = `${value}=${sale_amount}-${return_amount}`
            }
            renderstring += '<div style="position: relative; margin: 6px; text-align: right; overflow: hidden;">' + txt + '</div>';
        });
        renderstring += "</div>";
        return renderstring;
    }

        function aggregatesrenderer_sub_amount_profit_rate(aggregates, column, element, summaryData) {
            var renderstring = "<div class='jqx-widget-content' style='text-align: right; padding: 5px;'>";
          
            var sum=0
            var rows = $('#jqxgrid').jqxGrid('getrows');
            var count=0
            rows.forEach(row => {
               if (row.profit_rate) {
                       sum += Number(row.profit_rate);
                   count++
               }
             })
             var avg=''
             if(count>0)
               avg=toMoney(sum/count)
          
            renderstring += avg;

            renderstring += "</div>";
            return renderstring;
        }
</script>
