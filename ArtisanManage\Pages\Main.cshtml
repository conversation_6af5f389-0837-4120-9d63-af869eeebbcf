@page
@model ArtisanManage.Pages.MainModel
@{
    Layout = null;
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;" />
    @* <meta content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"> *@
    @* 移动端访问网页缩放设置 *@
    <meta content="user-scalable =yes" name="viewport">
    <meta name="referrer" content="no-referrer-when-downgrade">
    <title>营匠</title>
    <link href="~/MiniJsLib/MiniJsLibPC.css?v=@Html.Raw(Model.Version)" rel="stylesheet" />
  
    <link rel="stylesheet" href="~/jqwidgets/jqwidgets/styles/jqx.base.css?v=@Html.Raw(Model.Version)" type="text/css" />
    <link rel="stylesheet" href="~/jqwidgets/jqwidgets/styles/jqx.mytab.css" media="screen" type="text/css" />
      <link rel="stylesheet" href="~/css/main.css?v=@Html.Raw(Model.Version)" />
    <link rel="stylesheet" href="~/jqwidgets/jqwidgets/styles/jqx.shinyblack.css" media="screen" type="text/css" />
    <script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxcore.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxsplitter.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxmytabs.js?v=@Html.Raw(Model.Version)"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxpopover.js?v=@Html.Raw(Model.Version)"></script>
 
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxmenu.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxwindow.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdatetimeinput.js?v=@Html.Raw(Model.Version)"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxcalendar.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/globalization/globalize.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/globalization/globalize.culture.zh-CN.js"></script>
    <script src="~/MiniJsLib/MiniJsLibPC.js?v=@Html.Raw(Model.Version)"></script>

    <script src="~/js/Vue.js"></script> 

    <link rel="stylesheet" href="~/MiniJsLib/jquery.dialog.css">
    <link href="~/lib/element-ui/theme/index.css" rel="stylesheet" />

    <script src="~/lib/element-ui/index.min.js"></script>
    <script src="~/MiniJsLib/jquery.dialog.js"></script>

    <script type="text/javascript">
        window.g_operKey ='@Html.Raw(Model.OperKey)';
        window.bizStartDate = '@Html.Raw(Model.BizStartPeriod)';
         
        var curMenu = null, zTree_Menu = null;
        var divCurDivMenu = null;

        function closeAndRelease(frame, tabIndex) {
            // 2022.12.14 GC by InfSein
               
            if (frame) {
                var iframe = frame.contentWindow;
                var win = iframe.window;
                try {
                    if (win.destroyWindow) {
                        win.destroyWindow()
                        if (window.g_SheetsWindowForPrint == win) {
                            window.g_SheetsWindowForPrint==null
                        }
                    }
                }
                catch (e) {}
                
                for (var k in win) {
                    var tp = typeof win[k]

                    if ((typeof win[k]) == "object ") {

                        try {
                            win[k] = null
                        }
                        catch (e) {
                            debugger
                        }
                            
                    }
                }
                win = null

                frame.src = 'about:blank';
                frame.style.visibility='hidden'
               //frame.src = '';
                setTimeout(() => {
                     
                    iframe.document.write('');
                    iframe.document.clear();
                    iframe.close();
                       
              
                    var par=$(frame).parent()
                    $(frame).remove();
                    par.empty()
                },50);
                    $("#jqxTabs").jqxTabs('removeAt', tabIndex)
                  
            }
            else {
                console.warn('closeAndRelease is called but no frame given.')
            }
        }

        function bodyScale_notused() {//会导致popover组件漂移
            var devicewidth = document.documentElement.clientWidth;//获取当前分辨率下的可是区域宽度
            var scale = devicewidth / 1920; // 分母——设计稿的尺寸
            if(scale+0.2<=1){
              document.body.style.zoom = scale+0.2;//放大缩小相应倍数
            }
            console.log("scale",scale)
        }
        $(document).ready(function () {  
            //console.log("eeeeee",window.frames[])
            //bodyScale();
            $("#jqxTabs").jqxTabs({
                theme: 'mytab', height: '100%', width: '100%', showCloseButtons: true,scrollPosition:"both",
                onCloseButtonClick: function (tabIndex) {

                    var content = $('#jqxTabs').jqxTabs('getContentAt', tabIndex);
                   
                
                 
                    var frame = content.childNodes[0];
                   // var w = frame.contentWindow;
                    try {
                        if (w.getNotSavedMsg && w.getNotSavedMsg()) {
                            var msg = w.getNotSavedMsg()
                            if (msg === true) {
                                msg = "尚未保存,确定退出吗?"
                            }
                            jConfirm(msg, function () {
                                closeAndRelease(frame, tabIndex);
                                //$("#jqxTabs").jqxTabs('removeAt', tabIndex)
                            }, "");
                        }
                        else {
                            closeAndRelease(frame, tabIndex);
                            //$("#jqxTabs").jqxTabs('removeAt', tabIndex)
                        }
                    }
                    catch (e) {
                          closeAndRelease(frame, tabIndex);
                          //$("#jqxTabs").jqxTabs('removeAt', tabIndex)
                    }
                    


                }
            });
            $("#jqxTabs").jqxTabs('hideCloseButtonAt',0)
            //$("#div_menuInfo").jqxPopover({ autoClose: true, position: "right", title: "", showCloseButton: false, selector: $("#mnuInfo"),width: 800 });
           // $("#div_mnuSale").jqxPopover({ autoClose: true, position: "right", title: "", showCloseButton: false, selector: $("#mnuSale") });
            //$("#div_mnuBuy").jqxPopover({ autoClose: true, position: "right", title: "", showCloseButton: false, selector: $("#mnuBuy") });
            $("#div_headimage").jqxPopover({ autoClose: true, position: "bottom", title: "", offset: { left: 0, top: -8 }, showCloseButton: false, selector: $("#headimage") });
            $("#div_help").jqxPopover({ autoClose: true, position: "bottom", title: "", offset: { left: 0, top: -8 }, showCloseButton: false, selector: $("#help") });
            $("#div_service").jqxPopover({ autoClose: true, position: "bottom", title: "", offset: { left: 0, top: -8 }, showCloseButton: false, selector: $("#service") });
            $("#div_close_tabs").jqxPopover({ autoClose: true, position: "bottom", title: "", offset: { left: -30, top: -3 }, arrowOffsetValue: 20,showCloseButton: false, selector: $("#close_tabs") });
            $("#popAIservice").jqxWindow({ isModal: true, modalOpacity: 0.3, height: '800px', width: '1000px', theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
            $("#service").on('click', function () {
                $('#popAIservice').jqxWindow('open');
                var cs_url = "https://"+`s12.yingjiang.co/ai_customer/index.html#/?operKey=${g_operKey}&userType=customer`
                //var cs_url = "http://"+`*************:1111/#/?operKey=${g_operKey}&userType=customer`
                $("#popAIservice").jqxWindow('setContent', `<iframe src="${cs_url}" width="100%" height="100%" scrolling="no" frameborder="no"
                        sandbox="allow-scripts allow-top-navigation allow-same-origin"
                ></iframe>`);
            });
            $(".pop").each((i, pop) => {
                var $this = $(pop);
                var target = $this.data('target') || $this.parent();
                $this.jqxPopover({ autoClose: true,closeOnClick:true, animationOpenDelay: 0, animationCloseDelay: 0, position: "right", offset: {left:-5,top:0}, title: "", showCloseButton: false, selector:target });
            });

            $('.menu').on('click', function () {
                var tabTitle = $(this).data("title") || $(this).text();
                var url = $(this).data('url');
                var page = `page=${this.id}`;
                url += url.includes('?') ? '&' : '?';
                url += page;
                newTabPage(tabTitle, url,window);
            });
            
            $('.mainMenu').hover(function (e) {//监听.mainmenu鼠标悬停事件
                var rr = $(this).find('div:nth-child(2)');
                rr.css({ 'opacity': '1', 'transition': '1s' });
                rr = $(this).find('svg');
                rr.css({ 'fill': '#fff', 'transition': '1s' });


            }, function () {
                var rr = $(this).find('div:nth-child(2)');
                    rr.css({ 'opacity': '0.6', 'transition': '1s' });
                     rr = $(this).find('svg');
                rr.css({ 'fill': '#b1b1b1', 'transition': '1s' });
            })

        window.curTabIndex = -1;
        window.preTabIndex = -1;
        $('#jqxTabs').on('add', function (event) {
            var len = $('#jqxTabs').jqxTabs('length');
            window.preTabIndex = window.curTabIndex;
            window.curTabIndex = len - 1;

        });

        $('#jqxTabs').on('removed', function (event) {
            var item = event.args.item;
            for (var i = window.arrTabs.length - 1; i >= 0; i--) {
                 var tab = window.arrTabs[i];
                 if (tab.item == item) {
                     window.arrTabs.splice(i, 1);
                 }
                 else if (tab.item > item) {
                     tab.item--;
                 }
            }

            if (window.arrTabs.length > 0) {
                tab = window.arrTabs[window.arrTabs.length - 1];
                setTimeout(function () {
                     $('#jqxTabs').jqxTabs('select',tab.item);
                }, 300);
            }
        });

        $('#jqxTabs').on('selected', function (event) {
              var item = event.args.item;
              window.preTabIndex = window.curTabIndex;
              window.curTabIndex = item;
              if (!window.arrTabs) window.arrTabs = [];
              for (var i = window.arrTabs.length - 1; i >= 0; i--) {
                  var tab = window.arrTabs[i];
                  if (tab.item == item) {
                      window.arrTabs.splice(i, 1);
                  }
              }
              window.arrTabs.push({ item: item });


              var content = $('#jqxTabs').jqxTabs('getContentAt', window.curTabIndex);
              var frame = content.childNodes[0]
              if(!frame.contentWindow)  frame = content.childNodes[1]
              var curWindow = frame.contentWindow
              
              if (curWindow.g_bRefreshOnTabClicked && curWindow.QueryData) {
                  curWindow.QueryData()
              }
              else if (curWindow.ensureDeleteGridRows) {
                  curWindow.ensureDeleteGridRows()
              }
              else if(window.gridRowIDsToDelete) {
                  window.gridRowIDsToDelete = null
              }
            })


            if(window.CefGlue) {
                window.g_clientVersion = window.CefGlue.getClientVersion()
                $('#divClientVersion').text('c' + window.g_clientVersion)
                $('#divClientVersion').css('display','inline')
            }
             
           
            if (window.preTabIndex>=0) {
                var content = $('#jqxTabs').jqxTabs('getContentAt', window.preTabIndex);
                var frame = content.childNodes[0]
                var preWindow = frame.contentWindow
                content = $('#jqxTabs').jqxTabs('getContentAt', window.curTabIndex);
                frame = content.childNodes[0]
                var curWindow = frame.contentWindow
                if (preWindow.g_bRelatedReport_sale && window.g_bRelatedReport_sale) {
                    var queryItems = preWindow.funGetQueryValues()
                    for (var n in queryItems) {
                        var l = queryItems[v]
                    
                    }
                    curWindow.$('n').jqxInput('val', v)
                    curWindow.queryData()
                }
            }

            $('#jqxwin_bizperiod').on('close', function (event) {
                console.log("You closed a window of bizperiod");
                let start_period = '@Html.Raw(Model.BizStartPeriod)';
                if (start_period == '' && window.bizStartDate=='') {
                    $('#jqxwin_bizperiod').jqxWindow('open');
                }
            });
        })

        window.onload=function(){
            if(window.bizStartDate==''){
                $("#jqxwin_bizperiod").jqxWindow({ height: 300, width: 500, theme: 'summer', isModal: true, showCloseButton: false });
                $("#bizperiod_input").jqxDateTimeInput({ width: '250px', height: '25px', culture: 'zh-CN', formatString: 'yyyy-MM-dd' });
                $('#jqxwin_bizperiod').jqxWindow('open');
            }
        }

            function chooseBizStartDate(){
            let startDate = $('#bizperiod_input').jqxDateTimeInput('getDate').format('yyyy-MM-dd');
            console.log(startDate);

            jConfirm(`确认以${startDate}作为系统启用日期？`, function () {
                $.ajax({
                    url: "/AppApi/Main/SaveBizStartDate?operKey=@Model.OperKey",
                    method: "POST",
                    contentType: "application/json;charset=UTF-8",
                    data: JSON.stringify({
                        operKey: "@Model.OperKey",
                        period: startDate
                    }),
                    success: (res) => {
                        if(res.result=='OK'){
                            window.bizStartDate = startDate;
                            $('#jqxwin_bizperiod').jqxWindow('close');
                        }else{
                            bw.toast(res.msg);
                        }
                    },
                    error: (res) => {
                        bw.toast('网络错误');
                    }
                });
                
            }, "");
        }

        Date.prototype.format = function (fmt) {
            var o = {
                "M+": this.getMonth() + 1,  // 月份
                "d+": this.getDate(),  // 日
                "h+": this.getHours(),  // 小时
                "m+": this.getMinutes(),  // 分
                "s+": this.getSeconds(),  // 秒
                "q+": Math.floor((this.getMonth() + 3) / 3),  // 季度
                "S": this.getMilliseconds()  // 毫秒
            };
            if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
            for (var k in o)
                if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
            return fmt;
        }

        function setExpireMsg(msg) {
           
            if (msg === '账号已过期,请及时续费') {
                var countdown = 5
                $('#btncfm').on('click', function() {
                    $(this).attr('data-flag', 0)
                    $(this).disabled = true
                })
                var timer = setInterval(function() {

                    $('#expireTimewarning span').text('账号已过期,请及时续费:' + countdown + '秒后将返回登录界面！')
                    $('#expireTimewarning').show()

                    countdown--
                    if (countdown < 0 || $('#btncfm').attr('data-flag') == 0) {
                        clearInterval(timer)
                        location.replace('Login?')
                    }
                }, 1000)
            } else if(msg) {
                      $('div .expireTime').text(msg)
                     $('div .expireTime').show()            
            }
            //if (msg) {
            //    document.querySelector('div.expireTime').innerText = `${msg}`;
            //    document.querySelector('div.expireTime').style.display = 'block';       
            //}
        }
        //function handleChange() {
        //    debugger
        //    var rsId = document.getElementById("sellersSelect").value
        //    var rsSellers = window.rsSellers
        //    for (var i = 0; i < rsSellers.length;i++){
        //        var rs = rsSellers[i]
        //        if (rs.company_id == rsId) {
        //            var loginUrl = window.location.href
        //            var arr = loginUrl.split("?")
        //            var serverUrl = arr[0]
        //            window.location.href = `${serverUrl}?operKey=${rs.rsOperKey}`
        //        }
        //    }
        //}
        //function addSellers(data){
        //    if(data.length>0){
        //        document.getElementById("sellersSelect").style.display = "block"
        //        window.rsSellers = data
        //    }
                
        //    var selectElement = document.getElementById("sellersSelect");
        //    debugger
        //    selectElement.innerHTML = "";
        //    for (var i = 0; i < data.length; i++) {
        //        var optionElement = document.createElement("option");
        //        optionElement.value = data[i].company_id;
        //        optionElement.textContent = data[i].company_name;
        //        selectElement.appendChild(optionElement);
        //    }

        //}

        function getAllTabWindows() {
            var len = $('#jqxTabs').jqxTabs('length');
            var arr=[]
            for (let i = 0; i < len; i++) {
                var content = $('#jqxTabs').jqxTabs('getContentAt', i);
                var frame = content.childNodes[0]
                if(!frame.contentWindow)  frame = content.childNodes[1]
                arr.push(frame)
            }
            return arr
        }
        function selectTabPage(title,baseUrl,url,queryItemStr,window) {
            debugger
            var len = $('#jqxTabs').jqxTabs('length');
            var index =-1
            for (var i = 0; i < len;i++){
                var text = $('#jqxTabs').jqxTabs('getTitleAt',i)
                var content =  $('#jqxTabs').jqxTabs('getContentAt', i);
                var frame = content.childNodes[0];
                if (frame.src) {
                    var src = frame.src
                    if (src.indexOf(baseUrl) > 0){
                        console.log(src.indexOf('showTabs'))
                        if (src.indexOf('ArrearsBillView') > 0 && !(src.indexOf('showTabs') > 0)) {
                            continue
                        }
                        index = i
                        break
                    }
                }
                var w = frame.contentWindow;
            }
            if (index != -1) {
                $('#jqxTabs').jqxTabs('select',index)
            }else{
                newTabPage(title, url, window);
            }
        }
        function newTabPage(title, url,srcWindow,params) { 
            if(url==window.g_currentUrl){
                 //bw.toast("不要点击太快哦~")
                 return 
            }
            window.g_currentUrl=url 
         
             if( window.g_tmClearUrl ){
                clearTimeout( window.g_tmClearUrl)
                  window.g_tmClearUrl =0
            }

            window.g_tmClearUrl = setTimeout(() => { 
                window.g_currentUrl=''
            },1000)

            var devicewidth = document.documentElement.clientWidth;//获取当前分辨率下的可是区域宽度
            var scale = devicewidth / 1920; // 分母——设计稿的尺寸
            
            title = title.replace('查 ', '查');
            url = encodeURI(url)

            if (url.indexOf('?')>0) url += "&"; else url += "?";
            
            if (url.indexOf('operKey') < 0) {
                url += `operKey=${g_operKey}`
            }
            var tm=new Date().getTime()
            url+=`&s_tm=${tm}`
             
            //$('#jqxTabs').jqxTabs('addLast', title, `<div style="position:relative;width:100%;height:100%;margin-left:0; margin-right:0"><iframe src="${url}operKey=${g_operKey}" name="mainFrame" frameborder=0 scrolling="auto" style="width:110%;height:110%;position:absolute;top:-52px;left:-73px; margin-left:0; margin-right:0;transform:scale(${scale + 0.2})"  marginwidth="0" marginheight="0" bordercolor="#eeeeee" class="frmContext"></iframe></div>`);
         
            
            $('#jqxTabs').jqxTabs('addLast', title, `<iframe src="${url}" name="mainFrame" frameborder=0 scrolling="auto" style="width:100%;height:100%;margin-left:0; margin-right:0;"  marginwidth="0" marginheight="0" bordercolor="#eeeeee" class="frmContext" referrerpolicy="no-referrer-when-downgrade"></iframe>`);

            var len = $('#jqxTabs').jqxTabs('length');
            var content = $('#jqxTabs').jqxTabs('getContentAt', len - 1);
            var frame = content.childNodes[0];
            var w = frame.contentWindow;

            if (srcWindow) {
                w.srcWindow = srcWindow;
            }
            if (params) {
                w.paramsFromSrcWindow = params;
            }
            var jqxTabs = $('#jqxTabs')

            var tabs = jqxTabs.find("li.jqx-tabs-title")
            var lastTab = tabs[tabs.length - 1]

            var closeBtn = $(lastTab).find('.jqx-tabs-close-button')
            if (closeBtn.length > 0) {
                closeBtn[0].onclick = function (e) {
                     e.preventDefault()
                    e.stopPropagation()
                    try {
                        if (w.isNotSaved && w.isNotSaved()) {
                            e.preventDefault()
                            e.stopPropagation()
                        }
                    }
                    catch (e) {}
                    

                }

            }


        }
        function closeTab(win) {
            var len = $('#jqxTabs').jqxTabs('length');
            for (var i = 0; i < len; i++) {
                var content = $('#jqxTabs').jqxTabs('getContentAt', i);
                var frame = content.childNodes[0];
                var w = frame.contentWindow;
                if (w == win) {
                    closeAndRelease(frame,i)
                     //$('#jqxTabs').jqxTabs('removeAt',i);
                     break;
                }
            }
        }


        function showdiv() {
            document.getElementById("bg").style.display = "block";
            document.getElementById("show").style.display = "block";

        }
        function hidediv() {
            document.getElementById("bg").style.display = 'none';
            document.getElementById("show").style.display = 'none';
        }
        function hideWechatBindDiv() {
            document.getElementById("bg").style.display = 'none';
            document.getElementById("bindWechat").style.display = 'none';
            //bindWechat
        }
        //var screenw = screen.width;
        //switch (screenw) {
        //    case 1920:
        //        $('#headimage').attr('margin-right', 230);
        //        break;
        //    case 1536:
        //        $('#headimage').attr('data-height', 373);
        //        break;
        //}

    </script>
    <style>
        * {
            -webkit-user-drag: none;
            user-select: none;
        }
        .close_btn{
            height:25px;
            line-height:25px;
            padding:5px;
        }
        .close_btn:hover{
            background-color:#f0f9f0;
        }
        #customSelect li {
    padding: 5px;
    cursor: pointer;
}

#customSelect li:hover {
    background-color: #3461af;
}

        #bizperiod_footer button{
            transition:all 0.5s;
        }

        #bizperiod_footer button:hover {
                background-color: WhiteSmoke !important;
        }

        .xdsoft_overlay_lock_offset{
            margin-right:0 !important;
        }
</style>
</head>

<body style="margin-top: 0px;margin-left: 0px;margin-right: 0px;">
    <div class="outer" style="">
        <div class="A" style="" ondblclick="top_DblClick(event)" onmousemove="top_MouseMove(event)" onmousedown="top_MouseDown(event)"  onmouseup="top_MouseUp(event)">
            <div style="vertical-align: top; user-select: none;" class="auto-style1">

                <img draggable="false" src="@Html.Raw(Model.LogoUrl)" style="float: left; margin-left:20px; margin-top:10px;width:48px;" alt="" />

            </div>
           
            <div style="height: 100px;text-align:center;" draggable="false">
               
                <div style="float:right;background-color:#585858;width:30px;height:30px;border-radius:20px;margin-right:230px;margin-top:10px;" id="headimage">
                    <svg height="15" width="15" style="margin:5px 0px -5px auto;cursor:pointer" fill="#dddddd">
                        <use xlink:href="/images/images.svg#head" />
                    </svg>
                </div>
                <div id="div_headimage" style="position:absolute;display:none; width:60px;height:300px;">
                    <div style="width:150px;height:200px;position:relative;">
                        <ul style="margin-left:-20px;line-height: 25px;font-size:15px">
                            <li style="list-style:none;">
                                <a style="color:#337ecc" href="@Startup.Localhost/UpdatePassword?operKey=@Model.OperKey">修改密码</a>
                            </li>
                            <li id="bindLink">
                                <a style="color:#337ecc" onclick="bindWeChat()">绑定微信</a>
                            </li>
                            <li><a style="color:#337ecc" href="@Startup.Localhost/Login">退出</a></li>

                        </ul>
                        <div id="companyName" style="position:absolute;bottom:10px;width:100%;text-align:center;">
                           <div>@Html.Raw(Model.CompanyName)</div> 
                           <div>@Html.Raw(Model.OperName)</div> 
                        </div>
                         
                    </div>

                </div>

               

                <div id="help" style="float:right;line-height:30px;text-align:center;color:#fff; background-color:#585858;width:30px;height:30px;border-radius:20px;margin-right:30px;margin-top:10px;">
                    <svg height="15" width="15" style="margin:7px auto 0px auto; cursor:pointer" fill="#dddddd">
                        <use xlink:href="/images/images.svg#help" />
                    </svg>
                </div>
                <div id="div_help" style="position:absolute;display:none;">
                    <div style="width:150px;height:200px;display:flex;flex-direction:column;justify-content:space-between;align-items:center;">
                        <ul style="line-height: 25px;font-size:15px;padding:0px;">
                            <li style="list-style:none;">
                                <a id="btnshow" value="Show" onclick="showdiv();">
                                    App下载
                                </a>
                            </li>
                            <li>
                                <a id="btnshow" href="https://yingjiang.obs.myhuaweicloud.com/download/YingJiangClientSetup.exe" style="text-decoration: none;color: inherit">客户端下载</a>
                            </li>
                            <li onclick="remoteAssistance()"><a style="cursor:pointer;">申请远程协助</a></li>
                            <li><a style="cursor:pointer;">服务热线：</a></li>
                            <li><a style="cursor:pointer;">025-52812165</a></li>

                        </ul>
                       <div style="text-align:center;">
                           <label>版本: </label>
                           <label style="margin-right:5px;display:none;" id="divClientVersion"></label>
                           <label>@Html.Raw("s"+Model.Version)</label>
                       </div>
                       
                    </div>
                </div>
                <div id="service" style="float:right;line-height:30px;text-align:center;color:#fff; background-color:#585858;width:30px;height:30px;border-radius:20px;margin-right:30px;margin-top:10px; cursor:pointer;">
                    <svg height="15" width="15" style="margin:7px auto 0px auto; cursor:pointer" fill="#dddddd">
                        <use xlink:href="/images/images.svg#smile" />
                    </svg>
                </div>
                <div id="div_service" style="position:absolute;display:none;">
                    <div style="width:60px;height:18px;display:flex;flex-direction:column;justify-content:space-between;align-items:center;">
                        <div style="text-align:center;">
                            <label>智能客服 </label>
                        </div>

                    </div>
                </div>
                <div id="progress" style="display:none;float:right;border-radius:5px; width:100px;height:30px;margin-top:10px;margin-right:30px;">
                    <div style="background:#eee;border-radius:5px; width:100%;height:20px;">
                        <div id="progress-value" style="background:#99eeaa;border-radius:5px; width:10%;height:20px;"></div>
                    </div>
                    <div id="progress-type" style="width:100%;height:10px;color:#fff;font-size:10px;">
                       
                    </div>
                </div>
                <div id="popAIservice" style="display:none;">
                    <div id="serviceCaption" style="height:20px;background-color:#fff; text-align:center;"><span style="font-size:15px;">智能客服</span></div>
                    <div style="overflow:hidden;"> </div>
                </div>
                <!--<div style="color:#fff;font-size:20px;background-image:url(images/yingjiang.png);background-size:auto;position:fixed;left:calc(100vw /2 - 200px);top:0px; width:270px;height:30px;margin-top:15px;">

                </div>-->
                @*<div id="rsSellers"  style="display:block;float:right;line-height:40px;text-align:center;color:#ffffff; background-color:#585858;height:40px;border-radius:20px;margin-right:30px;margin-top:10px;padding-left:10px;padding-right:10px">*@
                    @*<select style="display:none;float:right;line-height:40px;text-align:center;color:#ffffff; background-color:#585858;height:40px;border-radius:20px;margin-right:30px;margin-top:10px;padding-left:10px;padding-right:10px" id="sellersSelect" onchange="handleChange()"></select>*@
@*                </div>*@

<div id="customSelect" style="display: none;float:right; line-height:40px; text-align:center; color:#ffffff; background-color:#585858; height:40px; border-radius:20px; margin-right:30px; margin-top:10px; padding-left:10px; padding-right:10px; position: relative;">
    <input type="text" id="searchBox" placeholder="搜索..." style="width: 100%;font-size:15px; box-sizing: border-box; border: none; padding: 5px;background-color: #585858;    color: #f0f9f0;text-align: center;">
     <button id="clearButton" style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); border: none; background: transparent; cursor: pointer;">×</button>
    <ul id="dropdown" style="display: none; list-style: none; padding: 0; margin: 0; position: absolute; width: 100%; z-index: 1000; max-height: 200px; overflow-y: auto; background-color: #585858; border-radius: 0 0 20px 20px;">
        <!-- 列表项将通过JavaScript动态添加 -->
    </ul>
</div>


                
                <div id="expireTime" class="expireTime" style="display:none;float:right;line-height:40px;text-align:center;color:#FF0000; background-color:#585858;height:40px;border-radius:20px;margin-right:30px;margin-top:10px;padding-left:10px;padding-right:10px"></div>
                <div id="expireTimewarning" style=" position:absolute;width:460px;height:110px;border:1px solid #eeeeee;background:#ffffff; border-radius:10px;left:50%;margin-left:-200px;margin-top:11px;display:none;padding-top:20px; z-index:10000">
                    <span ></span>
                   
                        <button id="btncfm" data-flag="1" style="display:block;margin-top:40px;margin-left:180px;border:1px ;border-radius:10px;height: 35px;cursor:pointer">好的，知道了</button>
                   
                    
                </div>
                <div   class="words_wrapper" style="position:fixed;left:calc(100vw /2 - 200px);top:0px; width:270px;height:25px;margin-top:10px;">
                    @if (Model.WordsImgUrl != "")
                    {
                        <img style="object-fit: contain; user-select: none;" height="25px" width="150px" src="@Html.Raw(Model.WordsImgUrl)" />
                    }
                   
                </div>

                @if (Model.SettingBrief != "")
                {
                    <div id="debugInfo" style="float:right;line-height:30px;text-align:center;color:@Html.Raw(Model.BriefColor); background-color:#585858;width:120px;height:30px;border-radius:15px;margin-right:30px;margin-top:10px;">
                        @Html.Raw(Model.SettingBrief)
                    </div>
                }
                <div id="close_tabs" style="position:absolute;top:63px;right:15px;z-index:10;height:25px;width:30px;line-height:25px;border-radius:5px 5px 0 0;">
                    <svg height="15" width="15" style="margin:0 auto;" fill="#aaaa">
                        <use xlink:href="images/images.svg#arrow" />
                    </svg>
                </div>
                <div id="div_close_tabs" style="position:absolute;display:none; width:100px;height:80px;">
                    <div style="width:100px;height:80px;position:relative;">
                        <ul style="margin-left:-45px;line-height: 25px;font-size:15px;">
                            <li onclick="closeAllTabs()" class="close_btn" style="list-style:none;cursor:pointer;">
                                <svg height="15" width="15" style="margin:0 auto;" fill="#333">
                                    <use xlink:href="images/images.svg#closePage" />
                                </svg>
                                关闭全部</li>
                            <li onclick="closeAllTabs(true)" class="close_btn" style="list-style:none;cursor:pointer;">
                                <svg height="15" width="15" style="margin:0 auto;" fill="#333">
                                    <use xlink:href="images/images.svg#closePage" />
                                </svg>
                                关闭其他页</li>
                        </ul>
                    </div>

                </div>

            </div>
        </div>

        <div class="B" >

            <!-- <div id="splitter"  style="margin-top: 0px;margin-left:0px;">style="background:#00f;" -->

            <div id="jqxNavigationBar" style="float:left;width:83px;height:100%;margin-top:30px; color:#fff;position:relative;">
                <div style="height:100%; text-align:center; padding-top:30px;">

                    <style>

                        .menuPage {
                            overflow: hidden;
                            padding-top: 16px;
                        }

                        .jqx-popover {
                            border-color: #e2e2e2;
                            border-radius: 20px;
                            box-shadow: 20px 20px 90px 20px rgba(0, 0, 0, 0.2);
                        }

                        .menuPage > div:nth-child(odd) {
                            float: left;
                            width: 180px;
                            margin: 0px;
                            padding-right: 10px;
                            padding-left: 10px;
                            padding-bottom: 10px;
                        }

                        .menuPage > div > div:first-child {
                            width: 100%;
                            text-align: center;
                            height: 30px;
                        }

                            .menuPage > div > div:first-child > span {
                                font-weight: bold;
                            }

                        .menuPage > div > div:not(:first-child) {
                            height: 36px;
                            margin-left: 0;
                            padding-left: 0;
                        }

                            .menuPage > div > div:not(:first-child):hover {
                                background-color: #ddd;
                            }

                            .menuPage > div > div:not(:first-child) > div:first-child {
                                margin-left: 6px;
                                height: 32px;
                                line-height: 32px;
                                float: left;
                                cursor: pointer;
                                vertical-align: central;
                            }

                            .menuPage > div > div:not(:first-child) > div:nth-child(even) {
                                margin-right: 10px;
                                height: 26px;
                                width: 43px;
                                line-height: 26px;
                                cursor: pointer;
                                float: right;
                                border-width: 0px;
                                border-top-width: 1px;
                                border: solid;
                                border-radius: 13px;
                                border-color: #dd8888;
                                border-style: solid;
                                /* background-color: #eeeeee;*/
                                text-align: center;
                                margin: 2px;
                            }

                        .menuPage > div:nth-child(even) {
                            position: relative;
                            float: left;
                            width: 2px;
                            margin-top: 0px;
                        }

                            .menuPage > div:nth-child(even) > div {
                                position: absolute;
                                margin-top: 50px;
                                top: 0;
                                width: 1px;
                                margin: 0px;
                                height: 1000px;
                                background-image: linear-gradient(to bottom, #aaa 0%, #aaa 10%, transparent 30%);
                                background-size: 1px 7px;
                                background-repeat: repeat-y;
                            }

                        .menuPage > div:last-child {
                            display: none;
                        }

                        .jqx-window-content{
                            padding:0;
                        }
                    </style>

                    @{
                        foreach (var Menu in Model.Menus)
                        {
                                <div class="@Menu.Class left-menu" style="margin-bottom:18px;">
                                    <div>
                                        <svg style="height: 28px; width: 28px;" fill="#b1b1b1">
                                            <use xlink:href="@Menu.Url" />
                                        </svg>
                                    </div>
                                    <div style="opacity:0.6;font-size:15px;">@Menu.Title</div>
                                    <div class="pop">
                                        <div class="menuPage">
                                            @foreach (var menuGroup in Menu.SubNodes)
                                        {
                                                    <div style="width:200px;font-size:16px;margin-left:10px;">
                                                        <div style="text-align:left;margin-left:6px;"><b>@menuGroup.Key</b></div>
                                                        @foreach (var subMenu in menuGroup.Value)
                                                    {
                                                            <div style="display:flex;justify-content:space-between;">
                                                                <div style="flex:auto;" class="menu" id="@subMenu.Id" data-url="@subMenu.Url">@subMenu.Title</div>
                                                                @{if (subMenu.View)
                                                            {
                                                                                                                                                                                                                                                                                            <div id="@subMenu.Id" class="menu" style="border-width:1px;margin-right:15px;" data-title='@("查" + subMenu.Title)' data-url="@subMenu.ViewUrl">查</div>
                                                            }
                                                                }
                                                                @{if (subMenu.OrderTable)
                                                            {
                                                                                                                                                                                                                                                                                            <div id="@subMenu.Id" class="menu" style="border-width:1px;margin-right:15px;" data-title='@(subMenu.Title.Replace("销售","订单"))' data-url="@subMenu.OrderUrl">订</div>
                                                            }
                                                                }
                                                            </div>
                                                    }
                                                    </div>
                                                    <div><div></div></div>
                                         }
                                            </div>
                                        </div>
                                    </div>
                        }

                    }
                </div>
            </div>
            <div style="margin-top: 0px; margin:0; margin-left:70px;background-color:#808080;height:100%;overflow:hidden;">
                <div class="jqx-hideborder jqx-hidescrollbars" style="width:100%;height:100%; margin-top: 0px; overflow:hidden;" id="jqxTabs">
                    <ul style="margin-left: 0px;margin-top: 0px;">
                        <li>工作台</li>
                    </ul>
                    <div id="content3" style="overflow:hidden;position:relative;z-index:10;">
                        <iframe src="WorkTab?operKey=@Model.OperKey" name="mainFrame" frameborder=0 scrolling="auto" style="width:100%;height:100%; margin-left:0; margin-right:0;" marginwidth="0" marginheight="0" bordercolor="#eeeeee" id="frmContext"></iframe>
                    </div>
                </div>
                <div id="bg">

                </div>
                <div id="show">
                    <div class="showbar">
                        <i style="text-align: center;color: rgb(41,48,54);"></i>App下载
                        <span id="btnclose" onclick="hidediv();"></span>
                    </div>
                    <div id="picture">
                        <img src="./images/yingjiang_app_qrcode.png" />
                    </div>
                </div>
                <div id="bindWechat">
                    <div class="showbar">
                        <i style="text-align: center;color: rgb(41,48,54);"></i>绑定微信
                        <span id="btnclose" onclick="hideWechatBindDiv();"></span>
                    </div>
                    <div id="verifycode_container">
                        <div id="verifycode_input">
                            
                        </div>
                        <div id="verifycode_tip">请输入手机短信四位验证码</div>
                    </div>
                    <div id="qr_picture">
                    </div>
                </div>
            </div>
            <!-- </div>-->
        </div>
    </div>

    <div id='jqxwin_bizperiod' style="display:none;">
        <div>业务开账</div>@*window-head*@
        <div style="margin-top:7%;margin-left:10%;">
            @*window-body*@
            <div id='bizperiod_body'>
                <div style="font-size:16px; margin:5px 0 5px 0;">选择系统启用时间：</div>
                <div style="font-size:14px; margin:5px 40px 5px 10px; color: gray;">[ 注意：1. 该启用时间将作为业务系统的期初时间，业务期初余额录入/查询以此时间为准；2. 开单时间将在该时间之后（导入除外） ]</div>
                <input id="bizperiod_input" style="margin:5px 0 0 20px;"/>
            </div>
            <div id="bizperiod_footer" style="font-size:16px;position:absolute;bottom:30px;margin-left:160px;">
                <button onclick="chooseBizStartDate()" style="width:70px;height:35px;background-color:#fff;border:1px solid darkgrey;border-radius:5px;cursor:pointer;">确定</button>
            </div>
        </div>
    </div>
    <script>
        $('body').on('contextmenu', function () {
            return false;
        });
        var divMenu = `<div id='sysMenu'>
                        <ul>
                            <li onclick="refreshPage()">刷新(ctrl+F5)</li>

                        </ul>
                       </div>`;
        $('body').append(divMenu);
        window.contextMenu = $('#sysMenu').jqxMenu({ width: 200, height: 58, autoOpenPopup: false, mode: 'popup' });
        $('body').on('mouseup', function (e) {
            var dd = 1;
            if (e.button == 2) {
                contextMenu.jqxMenu('open', parseInt(e.clientX) + 5, parseInt(e.clientY) + 5);
                return false;

            }
        })
        function refreshPage() {
            location.reload();
        }
        function renderInputDivTpl(valueArr,maxLength) {
            var inputDivsWithValue = valueArr.map(e => {
                return `
                    <div style='border-radius:10px;font-size:24px;border:4px solid #999;text-align:center;width:60px;height:60px;margin-left:10px;line-height:60px;'>${e}</div>
                `
            })
            var inputDivsWithEmpty = []
            for (var i = 0; i < maxLength - valueArr.length; i++) {
                inputDivsWithEmpty.push(`
                   <div style='border-radius:10px;border:4px solid #f0f0f0;text-align:center;width:60px;height:60px;margin-left:10px;line-height:60px;'></div>
                `)
            }
            return inputDivsWithValue.join("")+inputDivsWithEmpty.join("")
        }

        function bindWeChat() {
            $.ajax({
              url:"/AppApi/Main/SendBindWechatSms?operKey=@Model.OperKey",
              method:"POST",
              data:{
                   operKey:"@Model.OperKey"
              },
              success: (res) => {
                  console.log(res)
                  $("#bindWechat").css("display","block")
                  $("#bg").css("display","block")
                  var verifyCode = ""
                  var tpl = renderInputDivTpl([],4)
                  $("#verifycode_input").html(tpl)
                  document.addEventListener("keydown", (e) => {
                      console.log(e)
                      if (e.keyCode == 8) {
                          verifyCode = verifyCode.split("").splice(0, verifyCode.length - 1).join("")
                      } else { 
                         verifyCode += e.key
                      }
                      if (verifyCode.length == 4) {
                           checkBindVerifyCode(verifyCode,verifyPass => {
                               if (verifyPass) {
                                   showQRCode()
                               } else {
                                   verifyCode = ""
                                   var tpl = renderInputDivTpl([],4)
                                   $("#verifycode_input").html(tpl)
                                   $("#verifycode_tip").html( "验证不通过,请重新输入")
                                   $("#verifycode_tip").css("color","#c40000")
                               }
                           })
                      }
                      if (verifyCode.length > 4) {
                          return
                      }
                      var inputValueArray = verifyCode.split("")
                      tpl = renderInputDivTpl(inputValueArray,4)
                      $("#verifycode_input").html(tpl)
                  })
                    //$("#qr_picture").html(`<img src='https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=${res.data}' height='80'/>`)
                }
            })
        }
        function checkBindVerifyCode(verifyCode,cb) {
           $("#bg").css("display","block")
            $.ajax({
              url:"/AppApi/Main/CheckVerifyCode?operKey=@Model.OperKey&&verifyCode="+verifyCode,
              method:"GET",
              success: (res) => {
                    console.log(res)
                    cb(res.data)
                    //$("#qr_picture").html(`<img src='https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=${res.data}' height='80'/>`)
                }
            })
        }

      function showQRCode() {
       $.ajax({
              url:"/AppApi/Main/GetBindQR?operKey=@Model.OperKey",
              method:"GET",
              success: (res) => {
                    console.log(res)
                    $("#verifycode_container").css("display","none")
                    $("#qr_picture").html(`<img src='https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=${res.data}' height='80'/>`)
                }
            })
        }
        function closeAllTabs(flag){
            let selectedIndex = $("#jqxTabs").jqxTabs("selectedItem");
            //console.log("index",selectedIndex);
            let count = $("#jqxTabs").jqxTabs("length");
            let arr=getAllTabWindows()
            console.log("count",count)
            for(let i=count-1;i>=0;i--){
                if(flag){
                    if(i!==0&&i!==selectedIndex){
                        let content = $('#jqxTabs').jqxTabs('getContentAt', i);
                        //let frame = content.childNodes[0];
                        let frame = arr[i]
                        closeAndRelease(frame, i);
                    }
                }else{
                if(i!==0){
                let content = $('#jqxTabs').jqxTabs('getContentAt', i);
                //let frame = content.childNodes[0];
                let frame=arr[i]
                    closeAndRelease(frame, i);
                }
                }
            }
        }
        function getCloudPrinters() {
            $.ajax({
                url: '/api/PrintTemplate/GetCloudPrintersToUse',
                type: 'GET',
                contentType: 'application/json',
                data: {
                    operKey: g_operKey
                },
                success: function(data) {
                    if (window.CefGlue && window.CefGlue.setFunctionResult) {
                        var result = JSON.stringify(data.printerList);
                        window.CefGlue.setFunctionResult(result);
                    }
                },
                error: function (xhr) {
                    console.log("获取云打印机列表失败：" + xhr.responseText);
                    if (window.CefGlue && window.CefGlue.setFunctionResult) {
                        window.CefGlue.setFunctionResult(null);
                    }
                }
            });
        }
         function logBeforePrint(sheetsInfo) {
            sheetsInfo.operKey = g_operKey
            $.ajax({
                url: "api/Printer/LogBeforePrint", //url地址
                dataType: "json", //返回的数据类型
                contentType: 'application/json',
                type: "post", //发起请求的方式
                data: JSON.stringify(sheetsInfo),
                timeout: 8000,//设置超时时间为8s
                success: function (res) {
                    if (window.CefGlue && window.CefGlue.setFunctionResult && sheetsInfo.funcID) {
                        var result = JSON.stringify(res)   
                        window.CefGlue.setFunctionResult(sheetsInfo.funcID, result) 
                    }
                },
                error: function (xhr) {
                    if (window.CefGlue) {
                        var res = { result: 'Error', msg: "网络错误:" + xhr.responseText }
                        var result = JSON.stringify(res)   
                        window.CefGlue.setFunctionResult(sheetsInfo.funcID, result)
                    }
                     
                }
            });
        }
        function markSheetPrint(sheetsInfo) {
            sheetsInfo.operKey = g_operKey
        
            $.ajax({
                url: "api/Printer/PrintMark", //url地址
                dataType: "json", //返回的数据类型
                contentType: 'application/json',
                type: "post", //发起请求的方式
                data: JSON.stringify(sheetsInfo),
                timeout: 8000,//设置超时时间为8s
                success: function (res) {
                    if (window.CefGlue && window.CefGlue.setFunctionResult && sheetsInfo.funcID) {
                        var result = JSON.stringify(res)   
                        window.CefGlue.setFunctionResult(sheetsInfo.funcID, result)

                    }
                    //var tabMum = $('.jqx-tabs-content-mytab')
                  
                    var win = window.g_SheetsWindowForPrint
                    if (win && win.refreshPrintCount) {
                        win.refreshPrintCount(sheetsInfo.sheetIDs, sheetsInfo.printEach, sheetsInfo.printSum, sheetsInfo.printOpenStock)
                    } 

                },
                error: function (xhr) {
                    if (window.CefGlue) {
                        var res = { result: 'Error', msg: "网络错误:" + xhr.responseText }
                        var result = JSON.stringify(res)   
                        window.CefGlue.setFunctionResult(sheetsInfo.funcID, result)
                    }
                    //console.log("返回响应信息：" + xhr.responseText);
                }
            });
        }
        function CloudPrint(sheetsInfo) {
            sheetsInfo.operKey = g_operKey

            $.ajax({
                url: "https://coolies.yingjiang.co/AppApi/CloudPrint/PrintSheetWithTemplate", //url地址
                //url: "http://127.0.0.1:8082/AppApi/CloudPrint/PrintSheetWithTemplate", //url地址
                dataType: "json", //返回的数据类型
                contentType: 'application/json',
                type: "post", //发起请求的方式
                data: JSON.stringify(sheetsInfo),
                timeout: 8000,//设置超时时间为8s
                success: function (res) {
                    if (window.CefGlue && window.CefGlue.setFunctionResult && sheetsInfo.funcID) {
                        var result = JSON.stringify(res)
                        window.CefGlue.setFunctionResult(sheetsInfo.funcID, result)

                    }
                    //var tabMum = $('.jqx-tabs-content-mytab')

                     

                },
                error: function (xhr,ex,ex2,ex3,ex4) {
                    if (window.CefGlue) {
                        var res = { result: 'Error', msg: "网络错误:" + xhr.responseText }
                        var result = JSON.stringify(res)
                        window.CefGlue.setFunctionResult(sheetsInfo.funcID, result)
                    }
                    //console.log("返回响应信息：" + xhr.responseText);
                }
            });
        }

        function refreshSheetPrintCount(sheetsInfo) {
            //win.refreshPrintCount(sheetsInfo.sheetIDs, sheetsInfo.printEach, sheetsInfo.printSum, sheetsInfo.printOpenStock)
            //return
             if (window.CefGlue && window.CefGlue.setFunctionResult && sheetsInfo && sheetsInfo.funcID) {
                var result = 'OK'
                window.CefGlue.setFunctionResult(sheetsInfo.funcID, result)

            }
            setTimeout(() => {
                var win = window.g_SheetsWindowForPrint
                if (win && win.refreshPrintCount) {
                    win.refreshPrintCount(sheetsInfo.sheetIDs, sheetsInfo.printEach, sheetsInfo.printSum, sheetsInfo.printOpenStock)
                    //win.updateGridRow(sheetsInfo.sheetIDs, sheetsInfo.printEach, sheetsInfo.printSum, sheetsInfo.printOpenStock)
                     
                }
            },1000)
            
           
        }
         function refreshSheetPrintCount1(win) {
            //win.refreshPrintCount( )
           // return
             if (window.CefGlue && window.CefGlue.setFunctionResult && sheetsInfo && sheetsInfo.funcID) {
                var result = 'OK'
                window.CefGlue.setFunctionResult(sheetsInfo.funcID, result)

            }
            setTimeout(() => {
                var win = window.g_SheetsWindowForPrint
                if (win && win.refreshPrintCount) {
                    //win.refreshPrintCount(sheetsInfo.sheetIDs, sheetsInfo.printEach, sheetsInfo.printSum, sheetsInfo.printOpenStock)
                    win.updateGridRow(sheetsInfo.sheetIDs, sheetsInfo.printEach, sheetsInfo.printSum, sheetsInfo.printOpenStock)
                     
                }
            },1000)
            
           
        }
        function logPrintResult(sheetsInfo) {
            sheetsInfo.operKey = g_operKey
            $.ajax({
                url: "api/Printer/LogPrintResult", //url地址
                dataType: "json", //返回的数据类型
                contentType: 'application/json',
                type: "post", //发起请求的方式
                timeout: 8000,//设置超时时间为8s
                data: JSON.stringify(sheetsInfo),
                success: function (res) {
                    if (window.CefGlue && window.CefGlue.setFunctionResult && sheetsInfo.funcID) {
                        var result = JSON.stringify(res)   
                        window.CefGlue.setFunctionResult(sheetsInfo.funcID, result) 
                    } 
                },
                error: function (xhr) {
                    if (window.CefGlue) {
                        var res = { result: 'Error', msg: "网络错误:" + xhr.responseText }
                        var result = JSON.stringify(res)   
                        window.CefGlue.setFunctionResult(sheetsInfo.funcID, result)
                    }
                    //console.log("返回响应信息：" + xhr.responseText);
                }
            });
        }


        var downX = -1, downY = -1;
        function top_MouseDown(e) {
            downX = e.screenX
            downY = e.screenY
            if (window.CefGlue && window.CefGlue.topMouseDown) {
                g_readyForMouseMove = true
                preX = e.screenX
                preY = e.screenY
                window.CefGlue.topMouseDown(window.screen.width, downX, downY)
                
            }
        }
        
        var preX = -1, preY = -1;
        function top_MouseMove(e) {
            if (downX == -1) return   
            if (!(window.CefGlue && window.CefGlue.topMouseMove)) return            
            window.CefGlue.topMouseMove(e.screenX - downX, e.screenY - downY)
            preX = e.screenX
            preY = e.screenY
        } 
        function top_MouseUp(e) {
            downX = -1
            downY =-1

        }

        function top_DblClick(e) {
            if (!(window.CefGlue && window.CefGlue.maxWindow)) return
            window.CefGlue.maxWindow()
        }
         function remoteAssistance() {
            if (window.CefGlue) {
                if (!window.CefGlue.remoteAssistance) {
                    bw.toast("客户端版本低了，请升级")
                    return
                }
                window.CefGlue.remoteAssistance()
               
            }
            else {
                bw.toast("使用客户端才可以申请远程协助")
            }

        }
function addSellers(data) {
    if (data.length > 0) {
        document.getElementById("customSelect").style.display = "block"
        window.rsSellers = data;
        var dropdown = document.getElementById("dropdown");
        var searchBox = document.getElementById("searchBox");
        dropdown.innerHTML = ""; // 清空现有选项

        data.forEach(function(item, index) {
            var li = document.createElement("li");
            li.textContent = item.company_name;
            li.setAttribute('data-id', item.company_id);
            li.onclick = function() {
                searchBox.value = this.textContent;
                dropdown.style.display = "none";
                // 这里可以加入选中项的处理逻辑
            };
            dropdown.appendChild(li);

            // 如果是第一个元素，则设置为搜索框的默认值
            if (index === 0) {
                searchBox.value = item.company_name;
                window.originSeller = searchBox.value
            }
        });
    }
}

document.getElementById("searchBox").addEventListener("input", function() {
    var searchTerm = this.value.toLowerCase();
    var dropdown = document.getElementById("dropdown");
    var items = dropdown.getElementsByTagName("li");
    for (var i = 0; i < items.length; i++) {
        var itemText = items[i].textContent.toLowerCase();
        if (itemText.includes(searchTerm)) {
            items[i].style.display = "";
        } else {
            items[i].style.display = "none";
        }
    }
});

document.getElementById("searchBox").addEventListener("focus", function() {
    document.getElementById("dropdown").style.display = "block";
});

document.addEventListener("click", function(event) {
    var isClickInsideElement = document.getElementById("customSelect").contains(event.target);
    if (!isClickInsideElement) {
        document.getElementById("dropdown").style.display = "none";
    }
});
// 使用事件委托来处理 li 的点击事件
document.getElementById("dropdown").addEventListener('click', function(event) {
    if (event.target.tagName === 'LI') {
        if (window.originSeller === event.target.textContent) {
            return;
        }
        window.originSeller =event.target.textContent
        var itemId = event.target.dataset.id;
        console.log("选中的项的 ID: " + itemId);
        document.getElementById("searchBox").value = event.target.textContent;
        document.getElementById("dropdown").style.display = "none";
        var rsSellers = window.rsSellers
        for (var i = 0; i < rsSellers.length;i++){
            var rs = rsSellers[i]
            if (rs.company_id == itemId) {
                var loginUrl = window.location.href
                var arr = loginUrl.split("?")
                var serverUrl = arr[0]
                window.location.href = `${serverUrl}?operKey=${rs.rsOperKey}`
            }
        }
    }
});

document.getElementById("clearButton").addEventListener("click", function() {
    document.getElementById("searchBox").value = '';
    // 可以选择是否隐藏下拉菜单
    document.getElementById("dropdown").style.display = 'none';
});
        window.showProgress = function (quickOrSlow) {
            if (window.tmShowProgress) {
                clearInterval(window.tmShowProgress)
                window.tmShowProgress=0
            }
            var interval = 20000
            if(quickOrSlow) interval=3000
            window.tmShowProgress = setInterval(() => {
                $.ajax({
                    url: "/AppApi/Main/GetProgressToShow?operKey=@Model.OperKey",
                    method: "GET",
                    contentType: "application/json;charset=UTF-8",
                    data: {

                    },
                    success: (res) => {
                        if (res.result == 'OK') {

                            if (res.progress) {
                                var pg = res.progress
                                var arr = pg.split('|')
                                var title = arr[0]
                                var value = arr[1]
                                $('#progress-value').css('width', (parseFloat(value) * 100).toString() + '%')
                                $('#progress-type').text(title)

                                if(!$('#progress').is(":visible")){
                                     $('#progress').show()
                                     window.showProgress(true)
                                }
                            }
                            else {
                                if($('#progress').is(":visible")){
                                     $('#progress').hide()
                                     window.showProgress(false)
                                }
                            }


                        } else {
                            //bw.toast(res.msg);
                        }
                    },
                    error: (res) => {
                        // bw.toast('网络错误');
                    }
                });



            }, interval)
        }
     // window.showProgress(false)
        var pageStyle = @Html.Raw(Model.PageStyle.IsValid() ? Model.PageStyle:"null");
        if(pageStyle){
            if(pageStyle.backColor){
                 $('.outer,.A,#jqxNavigationBar').css('background-color',pageStyle.backColor) 
                 if (window.CefGlue) 
                    window.CefGlue.setBackgroundColor(pageStyle.backColor)
            }
            if(pageStyle.textColor){
                $('outer,A,jqxNavigationBar').css('background-color', pageStyle.textColor)
            }
     
            
        }

    </script>
</body>
</html>