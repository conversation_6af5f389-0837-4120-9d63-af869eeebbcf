@model ArtisanManage.Models.SheetPartialViewModel

<link rel="stylesheet" href="~/css/DataForm.css?v=@Html.Raw(Model.Version)" type="text/css" />
<link rel="stylesheet" href="~/jqwidgets/jqwidgets/styles/jqx.base.css?v=@Html.Raw(Model.Version)" type="text/css" />
<script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxcore.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdata.js?v=@Html.Raw(Model.Version)"></script>
<!-- <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdata.export.js"></script> -->
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxbuttons.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxmyinput.js?v=@Html.Raw(Model.Version)"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxtree.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdropdowntree.js?v=@Html.Raw(Model.Version)"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxscrollbar.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxmenu.js"></script>

<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.js?v=@Html.Raw(Model.Version)"></script>

<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxmygrid.edit.js?v=@Html.Raw(Model.Version)"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.selection.js?v=@Html.Raw(Model.Version)"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.columnsresize.js"></script>
<!-- <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.export.js"></script> -->

<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.pager.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdropdownlist.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.sort.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.aggregates.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxlistbox.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxwindow.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdatetimeinput.js?v=@Html.Raw(Model.Version)"></script>

<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxcalendar.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxtooltip.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxcheckbox.js"></script>

<script type="text/javascript" src="~/jqwidgets/jqwidgets/globalization/globalize.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/globalization/globalize.culture.zh-CN.js"></script>

<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxnotification.js"></script>
<!--<script type="text/javascript" src="~/js/math.min.js"></script>
    -->
<link rel="stylesheet" href="~/MiniJsLib/jquery.dialog.css?v=@Html.Raw(Model.Version)">
<script src="~/MiniJsLib/jquery.dialog.js?v=@Html.Raw(Model.Version)"></script>


<link rel="stylesheet" href="~/MiniJsLib/MiniJsLibPC.css?v=@Html.Raw(Model.Version)">
<script src="~/MiniJsLib/MiniJsLibPC.js?v=@Html.Raw(Model.Version)"></script>

<style type="text/css">

    .row-oper {
        fill: #d0d0d0;
        cursor: pointer;
    }

        .row-oper:hover {
            fill: #777777;
        }

    .jqx-fill-state-hover {
        -moz-box-sizing: content-box;
        box-sizing: content-box;
        border-color: #999;
        background: #f0f9f0;
    }

    .jqx-grid-cell-hover a {
        display: block;
    }

    .jqx-grid-cell-pinned {
        background-color: #fbfbfb;
    }

    .rowBtn {
        display: none;
    }

    #cmbPayWay1, #cmbPayWay2, #cmbPayWay3 {
        right: 1px;
        left: auto;
        width: 70px;
    }

    .makeInfo {
        width: 430px;
    }

        .makeInfo > div {
            float: left;
            height: 18px;
            font-size: 12px;
            color: #555;
        }

            .makeInfo > div > div {
                float: left;
                height: 18px;
                position: relative;
            }

                .makeInfo > div > div:first-child {
                    text-align: right;
                    width: 50px;
                }

                .makeInfo > div > div:last-child {
                    text-align: left;
                    width: 160px;
                }

                .makeInfo > div > div > span {
                    bottom: 1px;
                    left: 1px;
                    top: auto;
                }

                .makeInfo > div > div > label {
                    bottom: 1px;
                    right: 1px;
                }

    #getOrder {
        color: blue;
        cursor: pointer;
    }

    #divButtons button {
        margin-right: 30px;
        margin-top: 20px;
    }

    #divTail {
        margin-top: 10px;
    }

    .select-table {
        border-spacing: 0px;
        border-collapse: collapse;
        border: none;
    }

        .select-table tr:hover {
            background: #def;
        }

        .select-table tr {
            cursor: pointer;
            height: 30px;
            line-height: 30px;
        }

        .select-table td {
            border-style: solid;
            border-width: 0.5px;
            border-spacing: 0px;
            border-color: #e0e0e0;
            padding-left: 5px;
            border-left: none;
            border-right: none;
            border-top: none;
        }

    .choosetemplate {
        display: none;
        position: absolute;
        top: 748px;
        left: 980px;
        width: 100px;
        height: 25px;
        text-align: center;
        font-size: 14px;
        overflow: hidden;
        border: #e2e2e2 1px solid;
    }

    #topCoverDiv {
        opacity: 0.4;
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0px;
        background-color: #000;
        z-index: 100;
        text-align: center;
    }

    #dia {
        background: rgba(255,255,255,1);
        z-index: 200;
        position: absolute;
        top: 50%;
        left: 50%;
        width: 300px;
        height: 250px;
        margin: -200px 0 0 -250px;
        border-radius: 10px;
        border-top-width: 10px;
        padding: 10px 10px 10px 10px;
    }

    .show_close {
        height: 20px;
        width: 20px;
        margin-left: 480px;
        cursor: pointer;
        display: inline-block;
        float: right;
    }

    ::-webkit-scrollbar {
        width: 16px;
        height: 16px;
        background-color: #000;
    }

    ::-webkit-scrollbar-track {
        background-color: #f4f4f4;
    }

    ::-webkit-scrollbar-thumb {
        border-radius: 7px;
        -webkit-box-shadow: inset 0 0 0px rgba(0, 0, 0, 0.3);
        background-color: #00000015;
    }

    ::-webkit-scrollbar-corner {
        background-color: black;
    }

</style>
<script>
    //for print template chooser
    function myIsNumber(value) {
        if (value === undefined || value === null || value === '') return false;
        const num = Number(value);
        return !isNaN(num) && isFinite(num);
    }

    function isDateValid(date) {
        if (date === '无产期') return true
        let produceDate = new Date(date)
        console.log("检查日期格式", isNaN(produceDate))
        //var reg = /^((\d{2}(([02468][048])|([13579][26]))[\-\/\s]?((((0?[13578])|(1[02]))[\-\/\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\-\/\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\-\/\s]?((0?[1-9])|([1-2][0-9])))))|(\d{2}(([02468][1235679])|([13579][01345789]))[\-\/\s]?((((0?[13578])|(1[02]))[\-\/\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\-\/\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\-\/\s]?((0?[1-9])|(1[0-9])|(2[0-8]))))))(\s((([0-1][0-9])|(2?[0-3]))\:([0-5]?[0-9])((\s)|(\:([0-5]?[0-9])))))?$/;
        var reg2 = /^(\d{4})-(\d{2})-(\d{2})$/;

        return reg2.test(date) && (!isNaN(produceDate))
    }
    //封装一个promise用于各种单据在审核前，进行相应仓库的商品有没有进行过盘点
    function checkHasInventory(sheet, url) {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: `/api/${url}/CheckHasInventoryBeforeApprove`,
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(sheet),
                success: function (data) {
                    resolve(data)
                },
                error: function (error) {
                    reject(error)
                }
            })

        }
        )

    }




    function choosetemplate() {
        var maskBg = document.getElementById('topCoverDiv');
        var dia = document.getElementById('dia');
        maskBg.style.display = (maskBg.style.display == 'none') ? 'block' : 'none';
        dia.style.display = (dia.style.display == 'none') ? 'block' : 'none';
    }

    function getPrintTemplate() {
        $('.choosetemplate').css('display', 'block');
        //maskBg.style.display('block');
    }

    function hidePrintTemplate() {
        $('.choosetemplate').css('display', 'none');
        //var maskBg = document.getElementsByClassName('choosetemplate');
        //maskBg.style.display('none');
    }

    function onBtnAttrClick(event, rowIndex, unitType) {
        //
        event.stopPropagation()
        let gridSlt = "#jqxgrid"
        if (window.curGridSlt) gridSlt = window.curGridSlt
        var sheetRows = $(gridSlt).jqxGrid('getrows')

        //   var source = $("#jqxgrid").jqxGrid('getsource')
        //   var sheetRows = source._source.localdata

        var sheetRow = sheetRows[rowIndex]
        //var src= window.source
        if (sheetRow.mum_attributes) {
            if (!sheetRow.mum_attributes.forEach) {
                sheetRow.mum_attributes = JSON.parse(sheetRow.mum_attributes)
            }
        }


        if (sheetRow.attr_qty) {
            if (!sheetRow.attr_qty.forEach) {
                sheetRow.attr_qty = JSON.parse(sheetRow.attr_qty)
            }
        }


        var attrs = sheetRow.mum_attributes
        var sheetType = $('#sheetType').val()
        if (',X,T,XD,TD,'.indexOf(',' + sheetType + ',') == -1) {
            for (var i = attrs.length - 1; i >= 0; i--) {
                var a = attrs[i]
                if (!a.distinctStock) {
                    attrs.splice(i, 1)
                }
            }
        }
        const attrsOrigin = JSON.parse(JSON.stringify(attrs))
        attrs.forEach(attr => {
            if (!attr.specOptInItem || attr.specOptInItem == "False") {
                if (window.attrOptions) {
                    attr.options = []   // 此写法不知为何要清空mum_attributes中的options， 可能是由于设计缺陷-_-!，导致options二维的清空不存条码所以需要重新置空，但由导致下方 attr.options.push丢失条码，页面无法直接渲染
                    attrOptions.forEach(opt => {
                        //
                        if (opt.attr_id == attr.attrID)
                            attr.options.push({ optID: opt.opt_id, optName: opt.opt_name,sBarcode:'',bBarcode:'',mBarcode:''})  // 增加sBarcode:'',bBarcode:'',mBarcode:''
                    })
                }
            }
        })
        // 写法优雅绝了
        attrs.forEach(attr => {
            const matchingAttrOrigin = attrsOrigin.find(attrOrigin => Number(attr.attrID) === Number(attrOrigin.attrID));
            if (matchingAttrOrigin) {
                attr.options.forEach(attrOpt => {
                    const matchingAttrOriginOpt = matchingAttrOrigin.options.find(attrOriginOpt => Number(attrOpt.optID) === Number(attrOriginOpt.optID));
                    if (matchingAttrOriginOpt) {
                        attrOpt.bBarcode = attrOpt.bBarcode || matchingAttrOriginOpt.bBarcode || '';
                        attrOpt.mBarcode = attrOpt.mBarcode || matchingAttrOriginOpt.mBarcode || '';
                        attrOpt.sBarcode = attrOpt.sBarcode || matchingAttrOriginOpt.sBarcode || '';
                    }
                });
            }
        });
        var attr_qty = sheetRow.attr_qty
        var columns = [
            {
                text: '', sortable: false, filterable: false, editable: false, pinned: true,
                groupable: false, draggable: false, resizable: false,
                datafield: '', columntype: 'number', width: 20,
                cellsrenderer: commonPinCellsRenderer
            }
        ]

        attrs.forEach(attr => {
            var col = {
                text: attr.attrName, datafield: 'optName_' + attr.attrID, width: '270'
            }
            columns.push(col)
            if (attr.options) {
                //attr.options.forEach(opt)
            }
        })

        columns.push({ text: '数量', datafield: 'qty', width: '70' })
        columns.push({ text: '库存', datafield: 'stock_qty_unit', width: '70' })
        /*  getCombineRows  递归函数 根据商品的具有的属性获取所有的选项组合 比如颜色尺码组合有
         红      S
         绿      L

        得到 红S   红L   绿S  绿L


          attrs: 商品档案里定义的属性组合，来自info_item_prop表的mum_attributes属性
          [
          { attrID:5, attrName:'颜色',distinctStock:true,distinctStockEditable:false,specOptInItem:false, options:[{optID:1,optName:'红色',barcode:'69788877'},{optID:2,optName:'绿色',barcode:'5655566'}]},
          { attrID:6, attrName:'尺码',distinctStock:true,distinctStockEditable:false,specOptInItem:false, options:[{optID:5,optName:'S', barcode:'69788877'},{optID:6,optName:'L', barcode:'5655566'}]},
          ]
          attrIndex: 递归遍历时递增
          optIndex: 递归遍历时递增

    结果   lstRows: 获得的属性组合行
              [
                { optID_5: 1, optName_5:"红色", optID_6: 5,optName_6: "S",qty: "10"},
                { optID_5: 1, optName_5:"红色", optID_6: 6,optName_6: "L",qty: "10"},

              ]

              [
                { optID_7: 1, optName_7:"葡萄", qty: "10"},
                { optID_7: 1, optName_7:"苹果", qty: "10"},

              ]
          row: 一行属性组合,在递归函数之间传递,第一次调用不传row

         */
        function getCombineRows(attrs,availAttrCombine, attrIndex, optIndex, lstRows, row) {
            //
            if (!row) {
                //第一次调用时不传row
                row = { qty: '' }
                lstRows.push(row)
            }
            var attr = attrs[attrIndex]
            var opt = attr.options[optIndex]

            //将当前遍历到的属性选项的ID和Name赋值到row
            //比如第一次调用时赋值 红
            row['optID_' + attr.attrID] = opt.optID
            row['optName_' + attr.attrID] = opt.optName

            if(availAttrCombine)
            {
                var combine = availAttrCombine.find(cmb => Number(cmb.son_options_id) === Number(opt.optID))

                if (combine) {
                    row.sBarcode = combine.sBarcode || ''
                    row.bBarcode = combine.bBarcode || ''
                    row.mBarcode = combine.mBarcode || ''
                }
            }

            if(!row.sBarcode && !row.bBarcode && !row.mBarcode){
                row.sBarcode = opt.sBarcode || ''
                row.bBarcode = opt.bBarcode || ''
                row.mBarcode = opt.mBarcode || ''
            }




            if (opt.sPrice) row.sPrice = opt.sPrice


            //遍历后面的属性，比如尺码，这里optIndex为0所以只取属性的第一个选项

            if (attrIndex < attrs.length - 1) {
                //例如当前optName是 红,下一个遍历的是 S，如果当前是绿，下一个也是S，
                //B:
                getCombineRows(attrs,availAttrCombine, attrIndex + 1, 0, lstRows, row)
            }

            //C:  当前属性当前选项 和 后面属性所有选项组合行都获取完了，继续遍历 当前属性的下一个选项
            //例:
            //  1.如果上一个optName是红, 当前optName是S, 将当前组合行红S clone后新增一个组合行，将尺码变为L
            //  2.如当前optName是红，到这里时红色和后面属性的所有组合都已经完成了,将会继续遍历绿色
            //  例1代码是在例2代码前运行的,因为 B 处的递归将会把红后面的组合都处理好了，再回到这里
            if (optIndex < attr.options.length - 1) {
                //选项下移一位就就意味着一个新的组合行出现了，比如当前是 红S
                row = JSON.parse(JSON.stringify(row))
                lstRows.push(row)
                getCombineRows(attrs, availAttrCombine, attrIndex, optIndex + 1, lstRows, row)
            }
        }



        function getRowOptionName(row) {
            var arr = []
            var kf = 'optName_'
            for (var k in row) {
                if (k.indexOf(kf) == 0) {
                    var v = row[k]
                    arr.push(v)
                }
            }
            arr.sort()
            return arr.join('_')
        }
        function getRowOptionId(row) {
            var arr = []
            var kf = 'optID_'
            for (var k in row) {
                if (k.indexOf(kf) == 0) {
                    var attrID = k.replace('optID_', '')
                    var attrs = sheetRow.mum_attributes
                    var attr = attrs.find(a => a.attrID == attrID)
                    if (attr.distinctStock) {
                        var v = row[k]
                        arr.push(v)
                    }
                }
            }
            arr.sort()
            return arr.join('_')
        }

        var lstRows = []

        getCombineRows(attrs,sheetRow.availAttrCombine, 0, 0, lstRows)

        var branch_id = ''
        if ($("#model_name").length) {
            console.log($("#model_name"))
            branch_id = "null"
        }else if(sheetType =="BC"){
            branch_id = "-1"
        } else {
            if ($('#branch_id').length)
                branch_id = $('#branch_id').jqxInput('val')
            else if ($('#from_branch_id').length)
                branch_id = $('#from_branch_id').jqxInput('val')
            if (!branch_id) {
                bw.toast('请选择仓库')
                return
            }
            branch_id = branch_id.value
        }


        $.ajax({
            url: '/api/SaleSheet/GetItemsForAttrRows',
            type: 'GET',
            contentType: 'application/json',
            data: { operKey: g_operKey, item_id: sheetRow.item_id, branch_id: branch_id },
            success: function (res) {
                if (res.result === 'OK') {
                    var infoRows = res.data
                    var sheetType = $('#sheetType').val()
                    lstRows.forEach((son, index) => {
                        son.son_options_id = getRowOptionId(son)
                        var info = infoRows.find(r => r.son_options_id == son.son_options_id)
                        if (info) {
                            son.son_item_id = info.son_item_id
                            son.son_item_name = info.son_item_name
                            son.stock_qty_unit = info.stock_qty_unit
                            son.stock_qty = info.stock_qty
                            son.produce_date = info.produce_date
                            son.remember_price = info.remember_price
                            if (sheetType == 'PD') {
                                son.buy_price = info.s_buy_price
                                son.wholesale_price = info.s_wholesale_price
                                son.cost_price_avg = info.s_cost_price
                            }
                        }
                        else
                        {
                            son.stock_qty_unit = null
                        }
                    })
                    loadAttrQtyForm()
                }
            }
        })

        function loadAttrQtyForm() {

            if (attr_qty) {
                lstRows.forEach(row => {
                    attr_qty.forEach(rowQ => {
                        var met = true
                        for (var k in row) {
                            if (k.indexOf('optID_') == 0) {
                                if (rowQ[k] != row[k]) {
                                    met = false
                                }
                            }
                        }
                        if (met) {
                            row.qty = rowQ.qty
                        }
                    })
                    //if (r['optID_' + attr.attrID] = opt.optID)
                })
            }

            var sourceAttrs =
            {
                localdata: lstRows,
                unboundmode: true
                // totalrecords: 10,
            };
            var divAttr = `
                 <div id="gridAttrsWrapper" style="position:absolute;background:#e8e8e8;border-radius:10px;z-index:999999;">
                    <svg  onclick="onAttrClose()" height="15" width="15" style="position:absolute;right:4px;top:4px;cursor:pointer">
                       <use xlink:href="/images/images.svg#close" />
                    </svg>
                 <div id="gridAttrs" style="margin-top:30px;margin-left:10px;">

                 </div>
                   <button onclick="onAttrOK()" style="width:70px;height:30px;position:absolute;right:30%;bottom:5px;cursor:pointer">
                   <svg  height="15" width="15" style="">
                       <use xlink:href="/images/images.svg#check" />
                    </svg></button>
                 </div>
                `


            var gridWidth = 30
            columns.forEach(col => {
                gridWidth += parseInt(col.width)
            })
            if ($('#gridAttrsWrapper').length == 0)
                $('body').append(divAttr)
            var x = $(event.target).offset().left - gridWidth / 2
            var y = $(event.target).offset().top
            var docHeight = $('body').height()

            if (y + 300 < docHeight) {
                $('#gridAttrsWrapper').css({ "left": x, "top": y + 20, width: gridWidth + 20, height: "0px", display: "block" })
                $('#gridAttrsWrapper').animate({ height: 300 + 'px' }, 200)
            }
            else {
                $('#gridAttrsWrapper').css({ "left": x, "top": y - 2, width: gridWidth + 20, height: "0px", display: "block" })
                $('#gridAttrsWrapper').animate({ height: 300 + 'px', top: y - 2 - 300 + 'px' }, 200)
            }



            var dataAdapter = new $.jqx.dataAdapter(sourceAttrs);

            $("#gridAttrs").jqxGrid(
                {
                    source: dataAdapter,
                    width: gridWidth,
                    height: 225,
                    showaggregates: false,
                    showstatusbar: false,
                    statusbarheight: 25,
                    pageable: false,
                    // autoheight: true,
                    sortable: false,
                    editable: true,
                    columnsresize: true,
                    editmode: 'selectedcell',// 'selectedcell',
                    selectionmode: 'click',//'singlecell',// 'multiplecellsadvanced',
                    hoverrow: true,
                    // cellhover: cellhover,
                    // handlekeyboardnavigation: handlekeyboardnavigation,
                    columns: columns
                })
        }

        window.onAttrClose = function () {
            //
            $('#gridAttrsWrapper').animate({ height: 0 + 'px' }, 200, function () {
                $('#gridAttrsWrapper').css('display', 'none')
            })
        }

        window.onAttrOK = function () {


            var attrs = sheetRow.mum_attributes
            console.log(attrs)
            var distinctStockCount = 0
            if (attrs) {
                attrs.forEach(attr => {
                    if (attr.distinctStock) {
                        distinctStockCount++
                    }
                })
            }

            /*
            if (0 < distinctStockCount  && distinctStockCount< attrs.length) {
                bw.toast('多个属性必须同时核算或不核算库存')
                return
            }*/

            if (distinctStockCount > 0) {
                addRowsForAttr_distinctStock(rowIndex)
            }
            else {
                setQuantityForAttr_notDistinctStock(rowIndex)
            }

            /*
            var rows = $("#gridAttrs").jqxGrid('getrows')
            var useRows = []
            var qty = 0
            rows.forEach(row => {
                var attrQty = parseFloat(row.qty)
                if (attrQty) {
                    useRows.push(row)
                    qty += attrQty
                }
            })

            sheetRow.attr_qty = useRows
            sheetRow.quantity = qty
            cellendedit({ args: { datafield: 'quantity', rowindex: rowIndex, value: qty }})
            $("#jqxgrid").jqxGrid('refresh')
            $('#gridAttrsWrapper').animate({height:0+'px'},200, function () {
                $('#gridAttrsWrapper').css('display', 'none')

            })

            */
           if(window.onAttrRowsAdded){
                window.onAttrRowsAdded()
           }

        }

        function cloneObj(obj) {
            var newObj = {}
            for (var k in obj) {
                newObj[k] = obj[k]
            }
            return newObj
        }
        function setQuantityForAttr_notDistinctStock(rowIndex) {
            var rows = $("#gridAttrs").jqxGrid('getrows')
            //
            var useRows = []
            var qty = 0
            rows.forEach(row => {
                var attrQty = parseFloat(row.qty)
                if (row.uniqueid) delete row.uniqueid
                if (row.uid) delete row.uid
                if (row.boundindex) delete row.boundindex
                if (row.visibleindex) delete row.visibleindex
                if (attrQty) {
                    useRows.push(row)
                    qty += attrQty
                }
            })
            qty = toMoney(qty, 3)
            sheetRow.attr_qty = useRows
            sheetRow.quantity = qty

            updateRowSubAmount(rowIndex)
            updateTotalAmount()
            let gridSlt = "#jqxgrid"
            if (window.curGridSlt) gridSlt = window.curGridSlt
            //cellendedit({ args: { datafield: 'quantity', rowindex: rowIndex, value: qty }})
            $(gridSlt).jqxGrid('updategrid')
            $('#gridAttrsWrapper').animate({ height: 0 + 'px' }, 200, function () {
                $('#gridAttrsWrapper').css('display', 'none')

            })
        }
        function addRowsForAttr_distinctStock(rowIndex) {
            //var sheetRows = source.localdata
            let gridSlt = "#jqxgrid"
            if (window.curGridSlt) gridSlt = window.curGridSlt
            var sheetRows = $(gridSlt).jqxGrid('getrows')
            var sheetRow = sheetRows[rowIndex]
            var attrs = sheetRow.mum_attributes
            var rows = $("#gridAttrs").jqxGrid('getrows')
            //
            console.log(rows)
            var useRows = []
            var qty = 0
            var rowsNoItemID = []
            rows.forEach(row => {
                var attrQty = parseFloat(row.qty)
                if (attrQty) {
                    useRows.push(row)
                    if (!row.son_item_id) {
                        rowsNoItemID.push(row)
                    }
                    qty += attrQty
                }
            })
            /*
              useRows:[
                { optID_5: 1, optName_5:"红色", optID_6: 5,optName_6: "S",qty: "10"},
                { optID_5: 1, optName_5:"红色", optID_6: 6,optName_6: "L",qty: "10"}
              ]
              CreateItemsForAttrRows return:
              {
                 result:'OK', sonRows:[{item_id,item_name,qty,stock_qty_unit}]
              }
             */
            if (rowsNoItemID.length > 0) {
                $.ajax({
                    url: '/api/SaleSheet/CreateItemsForAttrRows',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ operKey: g_operKey, item_id: sheetRow.item_id, attrRows: rowsNoItemID, attrs: sheetRow.mum_attributes }),
                    success: function (data) {
                        if (data.result === 'OK') {
                            if (!window.g_queriedItems) window.g_queriedItems = {}
                            var sonRows = data.sonRows
                            sonRows.forEach(son => {
                                // var row = useRows.find(r => r.son_options_id == son.son_options_id)
                                useRows.forEach(row => {

                                    if (row.son_options_id == son.son_options_id) {
                                        row.son_item_id = son.son_item_id
                                        row.son_item_name = son.son_item_name
                                        row.stock_qty=son.stock_qty
                                        row.sell_pend_qty=son.sell_pend_qty
                                    }
                                })
                                //
                            })
                            replaceAttrMumRowWithSons(useRows, rowIndex)
                        }
                    }
                })
            }
            else {
                replaceAttrMumRowWithSons(useRows, rowIndex)
            }

        }
        function replaceAttrMumRowWithSons(useRows, rowIndex) {
            //
            var addRows = {}
            var sheetType = $('#sheetType').val()
            var remember_price =""
            //
            useRows.forEach((son, index) => {
                var attrSheetRow = addRows[son.son_item_id]
                if (!attrSheetRow) {
                    var attrSheetRow = JSON.parse(JSON.stringify(sheetRow))
                    attrSheetRow.item_id = son.son_item_id
                    attrSheetRow.item_name = son.son_item_name
                    attrSheetRow.son_mum_item = sheetRow.item_id
                    attrSheetRow.s_barcode = son.sBarcode
                    attrSheetRow.remember_price = son.remember_price
                    remember_price = son.remember_price
                    //var unitType = 'b'
                    //if ($('#defaultUnit').length == 1) {
                    //    var defaultUnit = $('#defaultUnit').jqxInput('val')
                    //    if (defaultUnit && defaultUnit.value) unitType = defaultUnit.value
                    //}
                    console.log(sheetRow)
                    if (sheetRow.unit_factor == "1") attrSheetRow.barcode = son.sBarcode
                    //attrSheetRow.produce_date = son.produce_date
                    if (sheetType == 'PD') {
                        attrSheetRow.buy_price = son.buy_price
                        attrSheetRow.cost_price_avg = son.cost_price_avg
                        attrSheetRow.wholesale_price = son.wholesale_price
                    }

                    if (son.stock_qty_unit) {
                        if (sheetType == 'PD') {
                            attrSheetRow.current_qty = son.stock_qty_unit
                            attrSheetRow.stock_qty = son.stock_qty

                        }
                        else {
                            attrSheetRow.stock_qty_unit = son.stock_qty_unit
                        }
                    }
                    if (sheetType == 'PD') {
                        if (unitType == "s") {
                            attrSheetRow.s_unit_qty = parseFloat(son.qty)
                            onCellEndEdit(rowIndex, "s_unit_qty", undefined, false)
                        }
                        else if (unitType == "b") {
                            attrSheetRow.b_unit_qty = parseFloat(son.qty)
                            onCellEndEdit(rowIndex, "b_unit_qty", undefined, false)
                        }
                        else if (unitType == "m") {
                            attrSheetRow.m_unit_qty = parseFloat(son.qty)
                            onCellEndEdit(rowIndex, "m_unit_qty", undefined, false)
                        }
                    }
                    else
                        attrSheetRow.quantity = parseFloat(son.qty)
                        //attrSheetRow.stock_qty = parseFloat(son.stock_qty)
                        //attrSheetRow.sell_pend_qty = parseFloat(son.sell_pend_qty)
                        //attrSheetRow.stock_qty = son.stock_qty !== undefined && !isNaN(parseFloat(son.stock_qty)) ? parseFloat(son.stock_qty) : 0;
                        attrSheetRow.stock_qty = parseFloat(son.stock_qty) ||'';

                        attrSheetRow.sell_pend_qty =  parseFloat(son.sell_pend_qty) ||'';
                        attrSheetRow.stock_qty_unit = attrSheetRow.stock_qty.toString() + attrSheetRow.s_unit_no;
                        attrSheetRow.usable_stock_qty_unit = (attrSheetRow.stock_qty - attrSheetRow.sell_pend_qty).toString() + attrSheetRow.s_unit_no;
                        attrSheetRow.attr_qty = []
                        addRows[son.son_item_id] = attrSheetRow
                }
                else {
                    attrSheetRow.quantity += parseFloat(son.qty)
                }

                var noStockRow = {}
                var aq = { qty: son.qty }
                if (son.sBarcode) aq.sBarcode = son.sBarcode
                if (son.bBarcode) aq.bBarcode = son.bBarcode

                for (var k in son) {
                    if (k.indexOf('optID_') == 0 || k.indexOf('optName_') == 0) {
                        var attrID = k.replace('optID_', '').replace('optName_', '')
                        var attr = sheetRow.mum_attributes.find(attr => attr.attrID == attrID)
                        if (!attr.distinctStock) {
                            //delete son[k]
                            aq[k] = son[k]
                        }
                    }
                }
                attrSheetRow.attr_qty.push(aq)

            })
            let gridSlt = "#jqxgrid"
            if (window.curGridSlt) gridSlt = window.curGridSlt
            var id = $(gridSlt).jqxGrid('getrowid', rowIndex)
            $(gridSlt).jqxGrid('deleterow', id)
            var rows =[]
            for (var id in addRows) {
                var attrSheetRow = addRows[id]
                rows.push(attrSheetRow)
                attrSheetRow.sub_amount = attrSheetRow.real_price * attrSheetRow.quantity
                if (',X,T,XD,TD,'.indexOf(',' + sheetType + ',') >= 0) {
                    var attrs = attrSheetRow.mum_attributes
                    for (var i = attrs.length - 1; i >= 0; i--) {
                        var attr = attrs[i]
                        if (attr.distinctStock) {
                            attrs.splice(i, 1)
                        }
                    }
                }
                else {
                    attrSheetRow.mum_attributes = null
                }

            }

            if (',CG,CT,X,T,XD,TD,'.indexOf(',' + sheetType + ',') >= 0&&remember_price && remember_price.toLowerCase() == "true") {
                var supcust_id = $('#supcust_id').jqxInput('val').value
                var branch_id = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'branch_id');
                var branch_position = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'branch_position');
                if (!branch_position) branch_position = "0"
                if (!branch_id) branch_id = $('#branch_id').jqxInput('val').value
                addEmptyRows(1, rowIndex)
                AddItemRows(rowIndex, supcust_id, branch_id, branch_position, rows)
            } else {
                var index = 0
                for (var i in addRows) {
                    var attrSheetRow = addRows[i]
                    if (sheetType == 'JH' || sheetType == 'HH') {
                        attrSheetRow.real_price = 0
                        attrSheetRow.trade_type = 'J'
                        attrSheetRow.trade_type_name = '借货'
                        if (sheetType == 'HH') {
                            attrSheetRow.trade_type = 'H'
                            attrSheetRow.trade_type_name = '还货'
                        }
                    }
                    $(gridSlt).jqxGrid('addrow', null, attrSheetRow, rowIndex + index);

                    if (sheetType == 'PD') {
                        onCellEndEdit(rowIndex + index, unitType + "_unit_qty", undefined, false)
                    }
                    else
                        updateRowSubAmount(rowIndex + index)

                    //update g_queriedItems
                    //var key = sheetRow.item_id
                    //if (sheetRow.order_sub_id && (sheetRow.order_price).toString()) key = sheetRow.item_id + '_' + sheetRow.order_sub_id + '_' + sheetRow.order_price
                    var key = getQueriedItemsKeyFromRow(sheetRow)
                    var item = window.g_queriedItems[key]
                    if (item) {
                        var sonItem = cloneObj(item)
                        sonItem.item_id = attrSheetRow.item_id
                        sonItem.item_name = attrSheetRow.item_name
                        //var sonKey = sonItem.item_id
                        //if (sheetRow.order_sub_id && (sheetRow.order_price).toString()) sonKey = sonKey + '_' + sheetRow.order_sub_id + '_' + sheetRow.order_price
                        var sonSheetRow = JSON.parse(JSON.stringify(sheetRow))
                        sonSheetRow.item_id = sonItem.item_id
                        var sonKey = getQueriedItemsKeyFromRow(sonSheetRow)
                        window.g_queriedItems[sonKey] = sonItem
                    }
                    index++
                }
                updateTotalAmount()
            }
            //$("#jqxgrid").jqxGrid('refresh')

            //hide the attribute quantity input dialog
            $('#gridAttrsWrapper').animate({ height: 0 + 'px' }, 200, function () {
                $('#gridAttrsWrapper').css('display', 'none')

            })
        }



        //return '<span style="margin: 4px; float: ' + columnproperties.cellsalign + ';color:#000;">' + value + '</span>';


    }

</script>
<script>
    window.ksSub = ''   //用于选客损交易方式后从后端接受公司设置的客损科目
    window.g_operKey = '@Html.Raw(Model.OperKey)';
    window.g_operName = '@Html.Raw(Model.OperName)';
    window.g_company_id = '@Html.Raw(Model.company_id)';
    window.pageInfo = {
        url: '/@Model.PageName',
    };
    window.g_pageType='sheet'
    @Html.Raw(Model.m_saveCloseScript)

</script>

<partial name="_PageCustomHead" model="Model" />

<script type="text/javascript">

    @Html.Raw(Model.GetFormDataScript)  //this offer function getFormData()

        $(document).ready(function () {
            //
            console.log(window.g_queriedItems)
            // ;
            var printTemplateChooser = $(`<div id="topCoverDiv" style="display: none;z-index:2000;">
                </div>
                <div id="dia" style="display: none;z-index:2001;">
                    <div style="z-index: 19891015;">
                        <div style="background:#fff;text-align:center;" move="ok">

                            <img src="/PrintTemplate/img/close.svg" class="show_close" onclick="choosetemplate()" />
                            <span style="font-size: 15px; margin-top: 15px;">模板列表</span>

                        </div>
                        <div style="margin-left:20px;margin-top:10px;margin-bottom:10px;line-height:20px;display:flex;align-items:center;"> <input type="checkbox" id="ckPrintSmallBarcode" style="width:20px;height:20px;"/><label for="ckPrintSmallBarcode">打印小单位条码</label> </div>
                        <div id="template-list" style="text-align: left; padding: 5px; overflow: auto; height: 200px;">
                        </div>
                    </div>
                </div>`)

            $('body').append(printTemplateChooser)


            var popBrief = `
                <div id="popBrief" style="display:none">
                    <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;">
                        <span style="font-size:20px;" id="appendBriefTitle">追加备注</span>
                    </div>
                    <div style="overflow:hidden;cursor:text">
                        <div style="height:80%;outline:none">
                            <div style="padding:3px; cursor:text;height:80%;outline:none;margin:20px 30px;border: 1px solid #c5c5c5;" id="newBrief" contenteditable></div>
                        </div>
                        <div style="text-align:center;margin-top:-20px"><button id="btnSaveBrief" onclick="btnSaveBrief_Clicked();" type="button">确定</button></div>
                    </div>

                </div>`

            $('body').append(popBrief)

            $("#popBrief").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 300, width: 450, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

            window.onAddBrief = function (forRed, callBack) {
                //
                if (forRed) {
                    if (forRed === 'CX')
                        $('#appendBriefTitle').text('请输入终止备注')
                    else
                        $('#appendBriefTitle').text('请输入红冲备注')
                    $('#btnSaveBrief').removeAttr('onclick')
                }
                else $('#appendBriefTitle').text('追加备注')
                window.g_bForRed = forRed
                window.g_addBriefCallBack = callBack
                $('#popBrief').jqxWindow('open')
                $('#newBrief').empty()
                setTimeout(() => {
                    $('#newBrief').focus()
                }, 300)
            }

            window.btnSaveBrief_Clicked = function () {
                var newBrief = $("#newBrief").text()
                if (!newBrief.trim()) {
                    $('#popBrief').shake(2, 10, 400)
                    return
                }
                if (window.g_bForRed) {
                    if (window.g_bForRed === 'CX') newBrief = '终止原因:' + newBrief
                    else newBrief = '红冲原因:' + newBrief

                }
                var sheetID = $('#sheet_id').val()
                var oldBrief = $('#make_brief').val()


                $('#popBrief').jqxWindow('close');
                $.ajax({
                    url: '/api' + window.pageInfo.url + '/AppendBrief',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        operKey: g_operKey,
                        sheetID: sheetID,
                        newBrief: newBrief.trim()
                    }),
                    success: function (data) {
                        if (data.result === 'OK') {
                            $('#make_brief').find('input').val(oldBrief + ' ' + newBrief);
                            if (g_bForRed) {
                                if (window.g_addBriefCallBack) {
                                    window.g_addBriefCallBack()
                                }
                            }
                            else
                                bw.toast('修改成功', 3000);
                        }
                        else {
                            bw.toast(data.msg, 3000);
                        }

                    },
                    error: function (xhr) {

                        console.log("返回响应信息：" + xhr.responseText);
                    }
                })
            }

               var popInvoice = `
        <div id="popInvoice" style="display:none">
            <div id="clientCaption" style="height:30px;background-color:#fff;text-align:center;">
                <span style="font-size:20px;" id="appendInvoiceTitle">添加发票号</span>
            </div>
            <div style="overflow:hidden;cursor:text">
                <div style="height:80%;outline:none">
                    <div style="padding:3px;cursor:text;height:80%;outline:none;margin:20px 30px;border:1px solid #c5c5c5;" id="newInvoice" contenteditable></div>
                </div>
                <div style="text-align:center;margin-top:-20px"><button id="btnSaveInvoice" onclick="btnSaveInvoice_Clicked();" type="button">确定</button></div>
            </div>
        </div>`;

    $('body').append(popInvoice);

    $("#popInvoice").jqxWindow({
        isModal: true,
        modalOpacity: 0.3,
        height: 300,
        width: 450,
        theme: 'summer',
        autoOpen: false,
        showCloseButton: true,
        closeButtonSize: 32,
        showAnimationDuration: 500,
        closeAnimationDuration: 500,
        animationType: 'fade'
    });

    window.onAddInvoice = function (forRed, callBack) {
        //
        if (forRed) {
            if (forRed === 'CX')
                $('#appendInvoiceTitle').text('请输入终止发票号')
            else
                $('#appendInvoiceTitle').text('请输入红冲发票号')
            $('#btnSaveInvoice').removeAttr('onclick')
        }
        else $('#appendInvoiceTitle').text('添加发票号')
        window.g_bForRed = forRed
        window.g_addInvoiceCallBack = callBack
        $('#popInvoice').jqxWindow('open')
        $('#newInvoice').empty()
        setTimeout(() => {
            $('#newInvoice').focus()
        }, 300)
    }

    window.btnSaveInvoice_Clicked = function () {
        var newInvoice = $("#newInvoice").text()
        if (!newInvoice.trim()) {
            $('#popInvoice').shake(2, 10, 400)
            return
        }
        if (window.g_bForRed) {
            if (window.g_bForRed === 'CX') newInvoice = '终止原因:' + newInvoice
            else newInvoice = '红冲原因:' + newInvoice
        }
        var sheetID = $('#sheet_id').val()
        var oldInvoice = $('#invoice_no').val()

        $('#popInvoice').jqxWindow('close');
        $.ajax({
            url: '/api' + window.pageInfo.url + '/AppendInvoice',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                operKey: g_operKey,
                sheetID: sheetID,
                newInvoice: newInvoice.trim()
            }),
            success: function (data) {
                if (data.result === 'OK') {
                    $('#invoice_no').find('input').val(oldInvoice + ' ' + newInvoice);
                    if (g_bForRed) {
                        if (window.g_addInvoiceCallBack) {
                            window.g_addInvoiceCallBack()
                        }
                    }
                    else
                        bw.toast('添加成功', 3000);
                }
                else {
                    bw.toast(data.msg, 3000);
                }
            },
            error: function (xhr) {
                console.log("返回响应信息：" + xhr.responseText);
            }
        })
    }

            $("#btnCopy").on('click', btnCopySheet_click)
            $("#btnRedAndChange").on('click', function () {
                var isRedAndChange = $('#isRedAndChange').val()
                var red_flag = $('#red_flag').val()
                if (red_flag) return
                $('#lblSheetTitle').css('color', '#000')
                let title = $('#lblSheetTitle').text();
                let newTitleArr = title.split('(')
                if (newTitleArr.length >= 0) {
                    $('#lblSheetTitle').text(newTitleArr[0])
                }
                var old_sheet_id = $('#sheet_id').val()
                console.log("旧单")
                console.log(old_sheet_id)
                $('#old_sheet_id').val(old_sheet_id)
                $('#sheet_id').val('')

                if (window.appendixApproved) {
                    window.appendixApproved = false
                }

            $('#review_time').text('')
            $('#reviewer_name').text('')
            //$('#order_sheet_id').val('')
            //$('#order_sheet_id').text('')
            //$('#order_sheet_no').val('')
            //$('#order_sheet_no').text('')

                $('#approve_time').val('')
                $('#approve_time').text('')
                $('#approver_id').val('')
                $('#approver_id').text('')
                $('#approver_name').val('')
                $('#approver_name').text('')
                $('#make_time').val('')
                $('#make_time').text('')
                $('#maker_name').val('')
                $('#maker_name').text('')
                var make_brief = $('#make_brief').jqxInput('val')
                var arr = make_brief.split('红冲原因')
                if (arr.length >= 0) make_brief = arr[0]
                console.log(make_brief)
                $('#make_brief').jqxInput('val', make_brief)
                $('#red_flag').val('')
                try {

                    $('#payway1_id').jqxInput({ disabled: false })
                    $('#payway1_amount').jqxInput({ disabled: false })
                    window.g_init_payway1_amount = parseFloat($('#payway1_amount').jqxInput('val'))
                    window.g_init_left_amount = parseFloat($('#left_amount').jqxInput('val'))
                }
                catch (e) { }
                $('#isRedAndChange').val('true')
                updateSheetState()
                $("#btnSave").attr('disabled', true)
            })
            $("#btnRed").on('click', function () {
                var sheet_id = $('#sheet_id').val()
                var sheetType = $('#sheetType').val()
                if(sheetType =="HDK"){
                    var installment_no = $('#installment_no').val()
                }
                var placeholder_sheet_id=""
                console.log(sheetType)
                var confirm_append = '';
                if (sheetType == "XD") placeholder_sheet_id = $('#placeholder_sheet_id').val()
                else if (sheetType == 'CG') {
                    let feeSheetInfo = $('#feeSheetInfo').val() ? JSON.parse($('#feeSheetInfo').val()) : '';
                    if (feeSheetInfo && Object.keys(feeSheetInfo).length > 0) {
                        confirm_append = '红冲采购单 将同时红冲其分摊单和分摊单新创建的费用支出单，';
                    }
                }
                else if (sheetType == 'X') {
                    let saleFeeSheetInfo = $('#saleFeeSheetInfo').val() ? JSON.parse($('#saleFeeSheetInfo').val()) : '';
                    if (saleFeeSheetInfo && Object.keys(saleFeeSheetInfo).length > 0) {
                        confirm_append = '红冲销售单 将同时红冲其分摊单和分摊单新创建的费用支出单，';
                    }
                }
                else if (sheetType == 'FYFT' || sheetType == 'XSFY') confirm_append = '红冲分摊单 将同时红冲其新创建的费用支出单，';
                else if (sheetType == 'ZC') {
                    let incomeSheetInfo = $('#incomeSheetInfo').val() ? JSON.parse($('#incomeSheetInfo').val()) : '';
                    if (incomeSheetInfo && Object.keys(incomeSheetInfo).length > 0) {
                        confirm_append = '红冲费用单 将同时红冲代厂家支付关联收入单，';
                    }
                }
                jConfirm(`${confirm_append}确定要红冲吗？`, function () {
                    var previousPage = window.srcWindow ? window.srcWindow.app : null;
                    var sheets = ""
                    if (previousPage) {
                        sheets = previousPage.sheets;
                        if (sheets) {
                            sheets = getPreviousSheetsList(sheets);
                        }
                    }
                    console.log(sheets)
                    console.log(window.pageInfo.url)
                    //var sheetType = $('#sheetType').val()
                    $("#btnRed").attr('disabled', true)
                    $('#btnRedAndChange').attr('disabled', true);
                    onAddBrief(true)
                    //onAddInvoice(true)
                    $("#btnRed").attr('disabled', false);
                    $('#btnSaveBrief').off('click')
                    $('#btnSaveBrief').on('click', function () {
                        var newBrief = $("#newBrief").text()
                        if (!newBrief.trim()) {
                            $('#popBrief').shake(2, 10, 400)
                            return
                        } else {
                            $.ajax({
                                url: '/api' + window.pageInfo.url + '/Red',
                                type: 'POST',
                                contentType: 'application/json',
                                data: JSON.stringify({ operKey: g_operKey, sheetType:sheetType,installment_no: installment_no,sheet_id: sheet_id, placeholder_sheet_id: placeholder_sheet_id, redBrief: newBrief, sheet_happen_time: $("#happen_time").val() }),
                                success: function (data) {
                                    if (data.result === 'OK') {
                                        btnSaveBrief_Clicked()
                                        $('#sheet_no_red_me').text('')
                                        $('#red_flag').val('1')
                                        updateSheetState()
                                        bw.toast('红冲成功', 3000)
                                        var sheetID = $('#sheet_id').val()
                                        for (var tempi = 0; tempi < sheets.length; tempi++) {
                                            if (sheets[tempi].sheet_id == sheetID) {
                                                sheets.splice(tempi, 1)
                                                window.g_temp_index = tempi
                                                break
                                            }
                                        }

                                        if (srcWindow && srcWindow.updateGridRow)
                                            srcWindow.updateGridRow(sheetID, { sheet_status: '已红冲' })

                                        assignSheets(sheets)
                                        // previousPage.refsh()

                                    }
                                    else {
                                        $("#btnRed").attr('disabled', false)
                                        $('#btnRedAndChange').attr('disabled', false);
                                        bw.toast(data.msg, 3000)
                                    }
                                },
                                error: function (xhr) {
                                    console.log("返回响应信息：" + xhr.responseText);
                                }
                            })
                        }

                    })

                }, "")
            })

            @Html.Raw(Model.m_showFormScript)

                window.g_queriedItems =@Html.Raw(Model.ItemsInfoJson)
                let newqueriedItems = {}
                for(let k in window.g_queriedItems){
                    let newKey = k.split(",")[0]
                    newqueriedItems[newKey] = window.g_queriedItems[k]
                }
                window.g_queriedItems_bySheetRow = JSON.parse(JSON.stringify(window.g_queriedItems))
                window.g_queriedItems = newqueriedItems

                //
            let SheetRowsJson = JSON.parse(JSON.stringify(@Html.Raw(Model.SheetRowsJson)))
            sheetRowsDH = SheetRowsJson.filter(row=>row.order_sub_id)
            sheetRowsDH.some(row=>{
                let key = row.item_id
                //let newKey = row.item_id + "_" + row.order_sub_id + "_" + row.order_price
                var newKey = getQueriedItemsKeyFromRow(row)
                if(window.g_queriedItems[key] && !window.g_queriedItems[newKey]){
                    let newQueriedItem = JSON.parse(JSON.stringify(window.g_queriedItems[key]))
                    newQueriedItem.units.forEach(e=>{
                        if(e.unit_type=="s") e.price = toMoney(row.order_price /row.unit_factor)
                        if(e.unit_type=="m") e.price = toMoney(row.order_price /row.unit_factor * e.unit_factor)
                        if (e.unit_type == "b") e.price = toMoney(row.order_price / row.unit_factor * e.unit_factor)
                    })
                    window.g_queriedItems[newKey] = newQueriedItem
                }
            })

            if ($('#payway1_id').length > 0) window.g_initialPayway = $('#payway1_id').jqxInput('val')//一定要放在onPageReady前面，因为onPageReady中会用到g_initialPayway
            onPageReady(@Html.Raw(Model.SheetRowsJson));
            if ($('#jqxgrid').length > 0) {
                $('#jqxgrid')[0].onmouseleave = () => {
                    window.operRowID = -1
                    removeRowOperatorFunc()
                }
            }
            if ($('#jqxgridB').length > 0) {
                $('#jqxgridB')[0].onmouseleave = () => {
                    window.operRowID = -1
                    removeRowOperatorFunc()
                }
            }



            $("#btnClose").on('click', function () {
                window.parent.closeTab(window);
            })


            $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                return false;
            })
            if ($('#make_brief').length > 0) {
                var penSvg = `<svg id="penSvg" onclick="onAddBrief()" height="15" width="15" style="position:absolute;right:2px;bottom:6px;z-index:999;cursor:pointer">
                                            <use xlink:href="/images/images.svg#pen" />
                              </svg>`;
                $('#make_brief').append(penSvg)
                $('#penSvg').hide();
                var approve_time = $('#approve_time').text();
                if (approve_time) {
                    $('#penSvg').show();
                }
            }

            if ($('#invoice_no').length > 0) {
                var penSvg = `<svg id="penSvg" onclick="onAddInvoice()" height="15" width="15" style="position:absolute;right:2px;bottom:6px;z-index:999;cursor:pointer">
                                            <use xlink:href="/images/images.svg#pen" />
                              </svg>`;
                $('#invoice_no').append(penSvg)
                $('#penSvg').hide();
                var approve_time = $('#approve_time').text();
                if (approve_time) {
                    $('#penSvg').show();
                }
            }



            var unitRights = @Html.Raw(Model.JsonOperRights);
            $(function () {
                if (unitRights) {
                    Object.keys(unitRights).forEach(key => {
                        if (!unitRights[key]) {
                            var id = '#btn' + key.replace(key[0], key[0].toUpperCase());
                            $(id).hide();
                        }
                    })
                }
            })

            if ($('#barcode_input').length) {
                $('#barcode_input').on()
            }
            if (!$('#sheet_id').val() && !$('#order_sheet_id').val()) {
                loadCachedSheet()
            }
        })

    function showLongMsg(msg, cbContinue) {
        if ($("#popMessage").length == 0) {
            var div = `
    <div id="popMessage" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">导入结果</span></div>
        <div   style="overflow-y:scroll;">
             <div id="divMessage"> </div>
             <div><button id="btn-pop-msg-ok">确定</button> </div>
        </div>
    </div>`
            $('body').append(div)
            $("#popMessage").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 500, width: 450, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

        }

        $('#divMessage').html(msg)
        $('#popMessage').jqxWindow('open')
        if (cbContinue) {
            $("#btn-pop-msg-ok").click(() => {
                closeLongMsg()
                cbContinue()
            })
        }
        else {
            $("#btn-pop-msg-ok").click(function () {
                closeLongMsg()
                $("#btnApprove").attr('disabled', false)
            })
        }

    }
    function closeLongMsg(msg, cbContinue) {
        if ($("#popMessage").length > 0)
            $("#popMessage").jqxWindow('close')
    }

    function loadCachedSheet() {
        if (requestString('copy')) return
        var sheetType = $('#sheetType').val()
        if (!sheetType) return
        var cachedSheets = localStorage.getItem(`cachedSheets_${g_company_id}_${sheetType}`);

        cachedSheets = JSON.parse(cachedSheets)
        if (!cachedSheets || cachedSheets.length == 0) return
        var html = ''
        for (var i = 0; i <= cachedSheets.length - 1; i++) {
            var sht = cachedSheets[i]
            if (sht.sheetType == sheetType) {
                var mark = ''
                if (sht.sup_name) {
                    mark = sht.sup_name
                }
                else if (sht.branch_id) {
                    mark = sht.branch_name
                }
                else if (sht.to_branch_name) {
                    mark = sht.from_branch_name + '-->' + sht.to_branch_name
                }
                var arr = window.parent.getAllTabWindows()
                var bSheetOpen = false
                arr.forEach(w => {
                    if (w != window) {
                        if (w.firstCachTime == sht.firstCachTime) {
                            bSheetOpen = true
                        }
                    }
                })

                if (bSheetOpen) {
                    // cachedSheets.splice(i, 1)
                    continue
                }



                html += `
    <tr index="${i}" firstCachTime="${sht.firstCachTime}"><td style="width:250px;">${mark}</td><td style="width:200px;">${sht.lastCachTime}</td><td class="delete-btn" style="width:70px;color:#ff8888;cursor:pointer;text-align:center;">删除</td></tr>
    `

            }
        }
        if (html == '') return
        html = `
    <div id="popSelectCachedSheet" style="display:none ">
        <div style="height:40px;background-color:#fff; text-align:center;width:100%; display: flex; justify-content: center;align-items:center" class="title-wrapper">
           <div>有未保存的单据</div>

        </div>

    <div>

        <div style="height:160px;padding-left:10px;">
            <div style="height:200px;overflow:auto;">
                   <table class="select-table" style="margin-top:10px;">
                    ${html}
                </table>
            </div>
            <div style="margin-top: 10px; display: flex; justify-content: center; width: 100%">
                 <div style="position:absolute;left:10px;width:150px; height:20px;color:#bbb;font-size:12px; text-align:center;display: flex;">
                            缓存最长10天, 最多10张
                 </div>
                <button id="btn-new-sheet" >关闭</button>

            </div>

        </div>

    </div>



    </div>
    `
        if ($('#popSelectCachedSheet').length == 0) {
            $('body').append(html)
            $('#popSelectCachedSheet tr').on('click', e => {
                var target = e.currentTarget
                var index = parseInt($(target).attr('index'))
                var sheet = cachedSheets[index]
                window.g_queriedItems = sheet.g_queriedItems// @Html.Raw(Model.ItemsInfoJson)
                window.firstCachTime = sheet.firstCachTime
                setFormDataItems(sheet)
               //
                var i = 0
                sheet.sheetRows.forEach((row) => {
                    row.row_index = i
                    if(row.inout_flag ==null) row.inout_flag = 0
                    i++
                })
                loadSheetData(sheet.sheetRows)
                $("#popSelectCachedSheet").jqxWindow('close')
            })
            $('#popSelectCachedSheet .delete-btn').on('click', e => {
                e.stopPropagation()

                jConfirm('确定删除吗', () => {
                    var target = e.currentTarget.parentNode
                    var index = parseInt($(target).attr('index'))
                    var sheet = cachedSheets[index]
                    cachedSheets.splice(index, 1)
                    localStorage.setItem(`cachedSheets_${g_company_id}_${sheetType}`, JSON.stringify(cachedSheets));
                    $(target).remove()
                })
            })

            $('#popSelectCachedSheet #btn-new-sheet').on('click', e => {
                $("#popSelectCachedSheet").jqxWindow('close')
            })
            $("#popSelectCachedSheet").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 300, width: 440, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

        }
        $("#popSelectCachedSheet").jqxWindow('open')
    }

    function saveSheetToCach() {
        var sheet = getFormData()
        if(sheet.isRedAndChange&&sheet.isRedAndChange.toLowerCase() == "true")return
        sheet.sheetRows = $('#jqxgrid').jqxGrid('getrows')
        sheet.sheetRows = JSON.parse(JSON.stringify(sheet.sheetRows))
        console.log(sheet.sheetRows)
        for (var i = sheet.sheetRows.length - 1; i >= 0; i--) {
            var row = sheet.sheetRows[i]
            if (!row.item_id && !row.mm_sheet_id) sheet.sheetRows.splice(i, 1)
        }
        sheet.g_queriedItems = window.g_queriedItems
        var sheetType = $('#sheetType').val()
        var cachedSheets = localStorage.getItem(`cachedSheets_${g_company_id}_${sheetType}`)
        if (!cachedSheets) {
            cachedSheets = []
        }
        else {
            cachedSheets = JSON.parse(cachedSheets)
        }

        for (var i = cachedSheets.length - 1; i >= 0; i--) {
            var sht=cachedSheets[i]
            var cachTime = new Date(sht.firstCachTime)
            var timeDiff = Math.abs(new Date() - cachTime);
            var daysDiff = timeDiff / (24 * 60 * 60 * 1000);
            if (daysDiff > 10 || i>10) {
                cachedSheets.splice(i,1)
            }
        }


        var existIndex = -1
        if (window.firstCachTime) {
            cachedSheets.forEach((sht, index) => {
                if (sht.firstCachTime == window.firstCachTime) {
                    existIndex = index
                }
            })
        }
        else {
            window.firstCachTime = new Date().toString()

        }
        window.lastCachTime = getDateTimeText(new Date())
        sheet.lastCachTime = window.lastCachTime
        sheet.firstCachTime = window.firstCachTime
        if (existIndex != -1) {
            cachedSheets.splice(existIndex, 1)
        }

        cachedSheets.splice(0, 0, sheet)
        cachedSheets = JSON.stringify(cachedSheets)
        localStorage.setItem(`cachedSheets_${g_company_id}_${sheetType}`, cachedSheets)
    }

    function removeSheetFromCach() {
        if (window.firstCachTime) {
            var sheetType = $('#sheetType').val()
            var cachedSheets = localStorage.getItem(`cachedSheets_${g_company_id}_${sheetType}`)
            if (cachedSheets) cachedSheets = JSON.parse(cachedSheets)
            if (!cachedSheets) return
            for (var i = cachedSheets.length - 1; i >= 0; i--) {
                var sht = cachedSheets[i]
                if (sht.firstCachTime == window.firstCachTime) {
                    cachedSheets.splice(i, 1)
                }
            }
            localStorage.setItem(`cachedSheets_${g_company_id}_${sheetType}`, JSON.stringify(cachedSheets))
            window.firstCachTime = ''
        }
    }


    function onRowRemove(rowIndex) {
        let gridSlt = "#jqxgrid"
        if (window.curGridSlt) gridSlt = window.curGridSlt
        var id = $(gridSlt).jqxGrid('getrowid', rowIndex);
        $(gridSlt).jqxGrid('deleterow', id);
        updateTotalAmount()
        var sheetType = $('#sheetType').val()
        if(sheetType=="CG" ||sheetType =="CT" ||sheetType == "CD"){
            updateCostAmount()
        }
        window.operRowID = -1;
    }
    function getPreviousSheetsList(sheets) {
        var sheetType = $('#sheetType').val()
        if (sheetType == 'X' || sheetType == 'T') return sheets.saleSheets;
        //if(sheetType == '')
        if (sheetType == 'ZC') return sheets.feeOutSheets;
        if (sheetType == 'SR') return sheets.incomeSheets;
        if (sheetType == 'SK') return sheets.getArrearSheets;
        if (sheetType == 'YS') return sheets.pregetSheets;
        if (sheetType == 'YF') return sheets.prepaySheets;
    }

    function assignSheets(sheets) {
        var sheetType = $('#sheetType').val()
        if (sheetType == 'X' || sheetType == 'T') window.srcWindow.app.saleSheets = sheets;
        if (sheetType == 'ZC') window.srcWindow.app.feeOutSheets = sheets;
        if (sheetType == 'SR') window.srcWindow.app.incomeSheets = sheets;
        if (sheetType == 'SK') window.srcWindow.app.getArrearSheets = sheets;
        if (sheetType == 'YS') window.srcWindow.app.pregetSheets = sheets;
        if (sheetType == 'YF') window.srcWindow.app.prepaySheets = sheets;
    }
    function onRowAdd(rowIndex) {
        var gridSlt = '#jqxgrid'
        if (window.curGridSlt) gridSlt = window.curGridSlt
        var row = {};
        GridData.columns.forEach(function (col) {
            if (col.datafield) row[col.datafield] = "";
            if (col.displayfield) row[col.displayfield] = "";
        })
        $(gridSlt).jqxGrid('addrow', null, row, rowIndex);
    }


    var removeRowOperatorFunc = function () {
        var gridSlt = '#jqxgrid'
        if (window.curGridSlt) gridSlt = window.curGridSlt
        var displayRows = $(gridSlt).jqxGrid('getvisiblerows');
        var allRows = $(gridSlt).jqxGrid('getrows');

        var arr = $('.row_operator')
        for (var i = 0; i < arr.length; i++) {

            var row_operator = arr[i]
            if (row_operator.parentNode) {
                // if (row_operator.parentNode.rowIndex != cellhtmlElement.rowIndex || row_operator.parentNode.rowIndex == undefined) {
                var row = row_operator.parentNode.parentNode
                var id = row.id
                id = id.replace('row', '')
                id = id.replace('jqxgridB', '')
                id = id.replace('jqxgrid', '')
                var curRow = displayRows[id]
                var showIndex = -1
                for (var j = 0; j < allRows.length; j++) {
                    var r = allRows[j]

                    if (r === curRow) {
                        showIndex = j
                    }
                }

                if (showIndex != window.operRowID) {
                    //  console.log('设置了setPinCell:', showIndex)
                    var html = "<div style='height:100%;display:flex; justify-content:center;align-items:center;'>" + (showIndex + 1) + "</div>";
                   // console.log('reseting rowindex:' + showIndex + 1)
                    row_operator.parentNode.innerHTML = html;// row_operator.parentNode.normalInnerHTML;

                }

            }
        }
    }


    function cellhover(cellhtmlElement, x, y, rowIndex, curGrid) {
       //  if (curGrid)
       //     window.curGridSlt = curGrid
        if (cellhtmlElement) {
            if (cellhtmlElement.className.indexOf('pinned') >= 0 && cellhtmlElement.style.left == "0px") {
                var approve_time = $('#approve_time').text()
                if (approve_time) return

                var gridSlt = '#jqxgrid'

                if (window.curGridSlt) gridSlt = window.curGridSlt

                if (curGrid) gridSlt = curGrid

                var editCell = $(gridSlt).jqxGrid('geteditcell');
                if (editCell) {
                    if (x <= 50) {
                      //  $(gridSlt).jqxGrid('endcelledit');
                    }

                }
                removeRowOperatorFunc()

                if (cellhtmlElement.innerHTML.indexOf('row_operator') == -1) {
                    if (!(window.g_dicNotDeleteRows && window.g_dicNotDeleteRows[rowIndex])) {

                        cellhtmlElement.innerHTML = `<div class="row_operator" style="height:100%;width:100%;display:flex;justify-content:space-around;align-items:center;">
                                <svg onmousedown="onRowAdd(${rowIndex})" class="row-oper" height="15" width="15" style="">
                                    <use xlink:href="/images/images.svg#add" />
                                </svg>
                                <svg onmousedown="onRowRemove(${rowIndex})" class="row-oper" height="15" width="15" style="">
                                    <use xlink:href="/images/images.svg#remove" />
                                </svg>
                            </div>`;

                        window.operHTML = cellhtmlElement.innerHTML;
                        window.operRowID = rowIndex;
                        //console.log('new rowIndex:',rowIndex)
                    }

                }
            }
        }
    }


    function initeditor_unit_no(row, cellvalue, editor, celltext, pressedkey) {
        console.log("initeditor_unit_no")
        // set the editor's current value. The callback is called each time the editor is displayed.
        var inputElement = editor.find('div')[0];
        $(inputElement).jqxInput('clearOptions');
        var inputField = editor.find('input');
        if (pressedkey) {
            inputField.val(pressedkey);
            inputField.jqxInput('selectLast');
        }
        else {
            inputField.val({ value: cellvalue, label: celltext });
            inputField[0].value = celltext || '';
            inputField.jqxInput('selectAll');
        }
        $(inputElement).jqxInput('suggest', true);
        setTimeout(() => {
             var gridSlt = '#jqxgrid'
            if (window.curGridSlt) gridSlt = window.curGridSlt
            var cell = $(gridSlt).jqxGrid('getselectedcell');
            if (cell.row != row || cell.column != 'unit_no') {
               $(inputElement).jqxInput('close');
            }
        },100)
    }

    function createeditor_unit_no(row, cellvalue, editor, cellText, width, height) {
        console.log("createeditor_unit_no")
        var element = $('<div id="txtUnitNo"></div >');
        editor.append(element);
        var inputElement = editor.find('div')[0];
        console.log(inputElement)

        var dataFields = new Array({ datafield: "unit_no", text: "单位", width: 50 },
            { datafield: "unit_factor", text: "包装率", width: 50 }
        );
        var gridSlt = '#jqxgrid'
        if (window.curGridSlt) gridSlt = window.curGridSlt

        $(inputElement).jqxInput({
            placeHolder: "", height: height, width: width,
            borderShape: "none",
            buttonUsage: 'list',
            showHeader: true,
            dropDownHeight: 160,
            displayMember: "unit_no",
            valueMember: "unit_no",
            dataFields: dataFields,
            searchFields: [],
            maxRecords: 4,
            url: '',
            /*source: [{ unit_no: 'P', unit_factor: 1 }, { unit_no: 'x', unit_factor: 10 }],*/
            source: function (query, response) {
                //  ;
                var item = query;//.split(/,\s/).pop();
                // update the search query.
                // inputElement.jqxInput({ query: item });
                var cell = $(gridSlt).jqxGrid('getselectedcell');
                var rowindex = cell.rowindex;
                let sheetRows = $(gridSlt).jqxGrid('getrows')
                var sheetRow = sheetRows[rowindex]
                //var item_id = $('#jqxgrid').jqxGrid('getcellvalue', rowindex, "item_id");
               // let key = sheetRow.item_id
                //if (sheetRow.order_sub_id && (sheetRow.order_price).toString()) key = sheetRow.item_id + '_' + sheetRow.order_sub_id + '_' + sheetRow.order_price
                var key = getQueriedItemsKeyFromRow(sheetRow)
                if (window.g_queriedItems && key) {
                    var item = window.g_queriedItems[key];
                    if (item) {
                        var units = JSON.parse(JSON.stringify(item.units))
                        if ($('#defaultUnit').length == 1) {
                            var defaultUnit = $('#defaultUnit').jqxInput('val')
                            if (defaultUnit && defaultUnit.value == 's') {
                                units.reverse()
                            }
                            else if (defaultUnit && defaultUnit.value == 'm') {
                                var mUnitIndex = -1;
                                for (var i = 0; i < units.length; i++) {
                                    var u = units[i]
                                    if (u.unit_type == 'm') {
                                        mUnitIndex = i
                                    }
                                }

                                if (mUnitIndex >= 0) {
                                    var mUnit = units.splice(mUnitIndex, 1)
                                    units.splice(0, 0, mUnit[0])
                                }
                                else//默认单位为中单位，但是没有中单位时，调整小单位为列表第一个
                                    units.reverse()
                            }
                        }
                        response(units, null, true)
                    }
                }
            },
            renderer: function (itemValue, inputValue) {
                // ;
                var terms = inputValue.split(/,\s*/);
                // remove the current input
                terms.pop();
                // add the selected item
                terms.push(itemValue);
                // add placeholder to get the comma-and-space at the end

                // terms.push("");
                //var value = terms.join(", ");
                //return terms;
                return itemValue;
            }
        });
        $(inputElement).on('optionSelected',
            function (a, b) {
                //  ;
                var value = $(inputElement).val();
                var unit_no = value.value;

                var name = '';
                if (value.name)
                    name = value.name;
                var cell = $(gridSlt).jqxGrid('getselectedcell');
                var rowIndex = cell.rowindex;
                let sheetRows = $(gridSlt).jqxGrid('getrows')
                var sheetRow = sheetRows[rowIndex]
                //let key = sheetRow.item_id
                //if (sheetRow.order_sub_id && (sheetRow.order_price).toString()) key = sheetRow.item_id + '_' + sheetRow.order_sub_id + '_' + sheetRow.order_price
                var key = getQueriedItemsKeyFromRow(sheetRow)
                if (window.g_queriedItems && key) {
                    var item = window.g_queriedItems[key];
                    if (item) {
                        item.units.forEach(function (unit) {
                            if (unit.unit_no === unit_no) {
                                $(gridSlt).jqxGrid('setcellvalue', rowIndex, "unit_factor", unit.unit_factor);
                                $(gridSlt).jqxGrid('setcellvalue', rowIndex, "barcode", unit.barcode);
                            }
                        })
                        var rowData = $(gridSlt).jqxGrid('getrowdata', cell.rowindex)
                        cellendedit({ args: { datafield: 'unit_no', rowindex: cell.rowindex, row: rowData, value: value, oldvalue: '' } })
                    }
                }

            })
    }
    function initeditor_produce_date(row, cellvalue, editor, celltext, pressedkey) {

        var inputField = editor.find('input');

        if (pressedkey) {

            var container = window.parent.CefGlue
            var simulateInput = null
            if (container) simulateInput = container.simulateInput
            if (!simulateInput) {
                inputField.val(pressedkey);
                inputField.jqxInput('selectLast');
            }
            else {
                inputField.focus()
                setTimeout(() => {
                    inputField.val('')
                    simulateInput(pressedkey)
                    console.log('keycode:' + pressedkey)

                }, 30)
            }
        }
        else {
            inputField.val({ value: cellvalue, label: celltext });
            inputField[0].value = celltext || '';
            inputField.jqxInput('selectAll');

        }
    }
    function createeditor_produce_date(row, cellvalue, editor, cellText, width, height) {
        var gridSlt = '#jqxgrid'
        if (window.curGridSlt) gridSlt = window.curGridSlt
        var element = $('<div id="produceDate"></div>');
        editor.append(element);
        var inputElement = editor.find('div')[0];
        var sheetType =$('#sheetType').val();
        var batchStock = []
        var rowIndex =-1
        let itemId = $(gridSlt).jqxGrid('getcellvalue', rowIndex, "item_id");
        let rowBranchId = "-1"
        let rowFromBranchId = "-1"
        let rowToBranchId = "-1"
        let rowBranchPosition = "0"
        let rowFromBranchPosition = "0"
        let rowToBranchPosition = "0"
        let key = ""
        let fromKey = ""
        let toKey = ""
        let sheetRows = $(gridSlt).jqxGrid('getrows')
        var sheetRow = sheetRows[rowIndex]
        if (sheetType === "DB") {
            var dataFields = [
                { datafield: "produce_date", text: "生产日期", width: 120 },
                { datafield: "batch_no", text: "批次", width: 150 },
                { datafield: "from_stock_qty_unit", text: "出仓实际库存", width: 100 },
                { datafield: "from_usable_stock_qty_unit", text: "出仓可用库存", width: 100 },
                { datafield: "to_stock_qty_unit", text: "入仓实际库存", width: 100 }]
        }
        else {
            var dataFields = [
                { datafield: "produce_date", text: "生产日期", width: 120 },
                { datafield: "batch_no", text: "批次", width: 150 },
                { datafield: "stock_qty_unit", text: "库存", width: 80 },
                { datafield: "usable_stock_qty_unit", text: "可用库存", width: 80 },
            ]
        }
        // 2025.04.02 - #7489
        // 生产日期弹窗在某些客户的显示器上会超出屏幕显示范围
        // 所以弹窗的高度从[固定350]修改为[默认250,显示器高度足够时才为350]
        let dropdownHeight = 250
        const windowHeight = $(window).height()
        if (windowHeight > 800) {
            // 正常1080P显示器的高度为704,2K为808
            // 经测试，即使是标准1080P，设为350的话也会有部分商品行的弹窗溢出容器
            console.log('wow,your screen is so big')
            dropdownHeight = 350
        }
        let inputOptions = {
            placeHolder: "230101", height: height, width: width,
            borderShape: "none",
            buttonUsage: 'list',
            showHeader: true,
            dropDownHeight: dropdownHeight,
            keepNoValueLabel:true,
            displayMember: "produce_date",
            valueMember: "batch_id",
            dataFields: dataFields,
            searchFields: ["produce_date","zjm"],
            searchMode: 'none',
            maxRecords: 10,
            source: function (query, response) {
                if (window.curGridSlt) gridSlt = window.curGridSlt
                sheetType = $('#sheetType').val();
                batchStock = []
                rowIndex = $(gridSlt).jqxGrid('getselectedcell').rowindex;
                itemId = $(gridSlt).jqxGrid('getcellvalue', rowIndex, "item_id");
                rowBranchId = "-1"
                rowFromBranchId = "-1"
                rowToBranchId = "-1"
                rowBranchPosition = "0"
                rowFromBranchPosition = "0"
                rowToBranchPosition = "0"
                key = ""
                fromKey = ""
                toKey = ""
                sheetRows = $(gridSlt).jqxGrid('getrows')
                sheetRow = sheetRows[rowIndex]
                if(sheetType =="DB"){
                    rowFromBranchId = $("#from_branch_id").val()?$("#from_branch_id").val().value:"-1";
                    rowFromBranchPosition = sheetRow.from_branch_position?sheetRow.from_branch_position:"0"
                    fromKey = itemId +"_" + rowFromBranchId +"_" + rowFromBranchPosition
                    rowToBranchId = $("#to_branch_id").val()?$("#to_branch_id").val().value:"-1";
                    rowToBranchPosition = sheetRow.to_branch_position?sheetRow.to_branch_position:"0"
                    toKey = itemId + "_" + rowToBranchId + "_" + rowToBranchPosition
                }
                else if(sheetType =="ZZ" || sheetType =="CF"){
                    rowBranchPosition = sheetRow.branch_position?sheetRow.branch_position:"0"
                    if(gridSlt=="#jqxgrid"){
                        rowBranchId = $("#from_branch_id").val()?$("#from_branch_id").val().value:"-1";
                    }else{
                        rowBranchId = $("#to_branch_id").val()?$("#to_branch_id").val().value:"-1";
                    }
                    key = itemId + "_" + rowBranchId + "_" + rowBranchPosition
                }
                else{
                    rowBranchId = $(gridSlt).jqxGrid('getcellvalue', rowIndex, "branch_id")
                    let branchId = $("#branch_id").val()? $("#branch_id").val().value:"-1"
                    rowBranchId = rowBranchId?rowBranchId:branchId
                    rowBranchPosition = sheetRow.branch_position?sheetRow.branch_position:"0"
                    key = itemId+ "_"  + rowBranchId+ "_"  + rowBranchPosition
                }
                let isShowNoStock = false
                if (window.g_companySetting && window.g_companySetting.showNegativeStock && window.g_companySetting.showNegativeStock == "True") {
                    isShowNoStock = true
                }
                if(window.itemsBatchStockTotal && (window.itemsBatchStockTotal[key] || (window.itemsBatchStockTotal[fromKey] && window.itemsBatchStockTotal[toKey]))){
                    if(sheetType == "DB"){
                        batchStock = []
                        let fromBatchStock = window.itemsBatchStockForShow[fromKey]?window.itemsBatchStockForShow[fromKey]:[]
                        let toBatchStock = window.itemsBatchStockForShow[toKey]?window.itemsBatchStockForShow[toKey]:[]
                        if(isShowNoStock){
                            fromBatchStock =  window.itemsBatchStockTotal[fromKey]?window.itemsBatchStockTotal[fromKey]:[]
                            toBatchStock =  window.itemsBatchStockTotal[toKey]?window.itemsBatchStockTotal[toKey]:[]
                        }
                        fromBatchStock.forEach(f=>{
                            let bs = JSON.parse(JSON.stringify(f))
                            bs.from_stock_qty_unit = f.stock_qty_unit
                            bs.from_usable_stock_qty_unit = f.usable_stock_qty_unit
                            bs.to_stock_qty_unit = 0
                            toBatchStock.some(t=>{
                                if(f.batch_id == t.batch_id){
                                    bs.to_stock_qty_unit = t.stock_qty_unit
                                    return true
                                }
                            })
                            batchStock.push(bs)
                        })
                        addEmptyProduceDate("DB", batchStock, sheetRow)
                    }else{
                        batchStock =  window.itemsBatchStockForShow[key]?window.itemsBatchStockForShow[key]:[]
                        if(isShowNoStock){
                            batchStock =  window.itemsBatchStockTotal[key]?window.itemsBatchStockTotal[key]:[]
                        }
                        addEmptyProduceDate(sheetType, batchStock, sheetRow)
                    }
                    response(batchStock)
                }
                else{
                    let branchs_id = ""
                    let branchs_position = ""
                    if(sheetType == "DB"){
                        let fromBranchId = $('#from_branch_id').val()?$('#from_branch_id').val().value:"-1"
                        let toBranchId = $('#to_branch_id').val()?$('#to_branch_id').val().value:"-1"
                        branchs_id = fromBranchId +"," + toBranchId
                        let from_branch_position = sheetRow.from_branch_position?sheetRow.from_branch_position:"0"
                        let to_branch_position = sheetRow.to_branch_position ? sheetRow.to_branch_position : "0"
                        branchs_position = from_branch_position + "," + to_branch_position
                    }
                    else if(sheetType == "ZZ" || sheetType =="CF"){
                        branchs_position =  sheetRow.branch_position?sheetRow.branch_position:"0"
                        if(gridSlt =="#jqxgrid"){
                             branchs_id =  $('#from_branch_id').val()?$('#from_branch_id').val().value:"-1"
                        }
                        else{
                            branchs_id = $('#to_branch_id').val()?$('#to_branch_id').val().value:"-1"
                        }
                    }
                    else{
                        branchs_id = sheetRow.branch_id
                        if(!branchs_id) branchs_id = $('#branch_id').val()?$('#branch_id').val().value:"-1"
                        branchs_position = sheetRow.branch_position?sheetRow.branch_position:"0"

                    }
                    $.ajax({
                        url: '/api/SaleSheet/GetBatchStock',
                        type: 'GET',
                        contentType: 'application/json',
                        data: {
                            operKey: g_operKey,
                            items_id:itemId,
                            branch_id: branchs_id,
                            branch_position:branchs_position,
                            isShowNegativeStock:isShowNoStock
                        },
                        success: function (data) {
                            if (data.result == "OK") {

                                if (!window.itemsBatchStockTotal) window.itemsBatchStockTotal = {}
                                if (!window.itemsBatchStockForShow) window.itemsBatchStockForShow = {}
                                if(sheetType == "DB"){
                                    batchStock = []
                                    window.itemsBatchStockTotal[fromKey] = data.batchStockTotal&&data.batchStockTotal[fromKey] ? data.batchStockTotal[fromKey] : []
                                    window.itemsBatchStockTotal[toKey] = data.batchStockTotal&&data.batchStockTotal[toKey] ? data.batchStockTotal[toKey] : []
                                    window.itemsBatchStockForShow[fromKey] = data.batchStockForShow&&data.batchStockForShow[fromKey] ? data.batchStockForShow[fromKey] : []
                                    window.itemsBatchStockForShow[toKey] = data.batchStockForShow&&data.batchStockForShow[toKey] ? data.batchStockForShow[toKey] : []
                                    let fromBatchStock = window.itemsBatchStockForShow[fromKey]?window.itemsBatchStockForShow[fromKey]:[]
                                    let toBatchStock = window.itemsBatchStockForShow[toKey]?window.itemsBatchStockForShow[toKey]:[]
                                    if(isShowNoStock){
                                        fromBatchStock =  window.itemsBatchStockTotal[fromKey]?window.itemsBatchStockTotal[fromKey]:[]
                                        toBatchStock =  window.itemsBatchStockTotal[toKey]?window.itemsBatchStockTotal[toKey]:[]
                                    }
                                    fromBatchStock.forEach(f=>{
                                        let bs = JSON.parse(JSON.stringify(f))
                                        bs.from_stock_qty_unit = f.stock_qty_unit
                                        bs.from_usable_stock_qty_unit = f.usable_stock_qty_unit
                                        bs.to_stock_qty_unit = 0
                                        toBatchStock.some(t=>{
                                            if(f.batch_id == t.batch_id){
                                                bs.to_stock_qty_unit = t.stock_qty_unit
                                                return true
                                            }
                                        })
                                        batchStock.push(bs)
                                    })
                                    addEmptyProduceDate("DB", batchStock, sheetRow)
                                }else{
                                    window.itemsBatchStockTotal[key] = data.batchStockTotal&&data.batchStockTotal[key] ? data.batchStockTotal[key] : []
                                    window.itemsBatchStockForShow[[key]] = data.batchStockForShow&&data.batchStockForShow[key] ? data.batchStockForShow[key] : []
                                    batchStock =  window.itemsBatchStockForShow[key]?window.itemsBatchStockForShow[key]:[]
                                    if(isShowNoStock){
                                        batchStock =  window.itemsBatchStockTotal[key]?window.itemsBatchStockTotal[key]:[]
                                    }
                                    addEmptyProduceDate(sheetType, batchStock, sheetRow)
                                }
                                response(batchStock)
                            }
                        }
                    })
                }
            },
            renderer: function (itemValue, inputValue) {
                var terms = inputValue.split(/,\s*/);
                let curIndex = $(gridSlt).jqxGrid('getselectedcell').rowindex;
                let curValue = $(gridSlt).jqxGrid('getcellvalue', curIndex, "produce_date");
                curValue=curValue?curValue:""
                if (inputValue && inputValue.length == 6) {
                    inputValue = '20' + inputValue.substr(0, 2) + '-' + inputValue.substr(2, 2) + '-' + inputValue.substr(4, 2)
                }
                const date = new Date(inputValue);
                var inputIsDate = !isNaN(date.getTime()) && inputValue.length==10
                if (!itemValue && !inputIsDate) {
                    return ''
                }

                if (!itemValue ) {
                    return inputValue
                }
                if (inputIsDate &&  itemValue.indexOf(inputValue) !==-1) {
                    return inputValue
                }
                else return itemValue
                // if(inputValue == curValue && curValue!=itemValue){
                //     terms.pop();
                //     terms.push(itemValue);
                //     terms.push("");
                // }
                // var value = terms.join(", ");
                // return value;
            }
        }
        $(inputElement).jqxInput(inputOptions)

        $(inputElement).on('change', function (event) {

            var value = $(inputElement).val();
            let produceDateInput = value.label?value.label:""
            let batchNoInput = ""
            if(value.value){//选择下拉选项
                batchNoInput = value.batch_no?value.batch_no:""
                //$(gridSlt).jqxGrid('showcolumn', 'batch_no');
            }else{//
                batchNoInput = $(gridSlt).jqxGrid('getcellvalue', rowIndex, "batch_no");
                batchNoInput = batchNoInput?batchNoInput:""
            }
            if (produceDateInput && produceDateInput.length == 6) {
                produceDateInput = '20' + produceDateInput.substr(0, 2) + '-' + produceDateInput.substr(2, 2) + '-' + produceDateInput.substr(4, 2)
            }
            let selectedValue = null
            var matchedItem = batchStock.some(item => {
                if(item.produce_date == produceDateInput && item.batch_no == batchNoInput){
                    selectedValue = item
                    return true
                }
            });
            console.log(matchedItem)
            if(!matchedItem){
                sheetRow.produce_date = produceDateInput
            }
            else{
                sheetRow.produce_date = selectedValue.produce_date
                sheetRow.batch_no = selectedValue.batch_no
            }
           // setTimeout(() => {
                $(gridSlt).jqxGrid('endcelledit', rowIndex, "batch_id", false);
                $(gridSlt).jqxGrid('updategrid');
           //},100)


            //$(gridSlt).jqxGrid('endcelledit', rowIndex, "batch_id_f", false);
        })
    }
     function addEmptyProduceDate(sheetType, batchStock, sheetRow) {
        debugger
        let emptyItem = batchStock.find(item => item.batch_id === "0")
        let branch_id = ""
        let branch_position = ""
        let branch_position_name = ""
        if (emptyItem) {
            emptyItem.produce_date = "无产期"
        } else {
            let newBatch = {}
            if (sheetType === "DB") {
                let fromBranchId = $('#from_branch_id').val() ? $('#from_branch_id').val().value : "-1"
                let toBranchId = $('#to_branch_id').val() ? $('#to_branch_id').val().value : "-1"
                branch_id = fromBranchId + "," + toBranchId
                let from_branch_position = sheetRow.from_branch_position ? sheetRow.from_branch_position : "0"
                let to_branch_position = sheetRow.to_branch_position ? sheetRow.to_branch_position : "0"
                branch_position = from_branch_position + "," + to_branch_position
                newBatch = {
                    batch_id: '0',
                    batch_no: '',
                    produce_date: '无产期',
                    zjm: '',
                    from_sell_pend_qty: 0,
                    from_stock_qty: 0,
                    from_sell_pend_qty_unit: '0',  // 根据需要设置
                    from_stock_qty_unit: '0',  // 根据需要设置
                    from_usable_stock_qty_unit: '0', // 根据需要设置
                    to_sell_pend_qty: 0,  // 根据需要设置
                    to_stock_qty: 0,
                    to_stock_qty_unit: '0',
                    to_sell_pend_qty_unit: '0',
                    to_usable_stock_qty_unit: '0'
                };
            } else if (sheetType === "X" || sheetType === "T" || sheetType === "PD"|| sheetType === "JH" || sheetType === "HH" || sheetType === "XD") {
                branch_id = sheetRow.branch_id
                if (!branch_id) branch_id = $('#branch_id').val() ? $('#branch_id').val().value : "-1"
                branch_position = sheetRow.branch_position ? sheetRow.branch_position : "0"
                newBatch = {
                    batch_id: '0',
                    batch_id_f: '0',
                    batch_no: '',
                    produce_date: '无产期',
                    zjm: '',
                    stock_qty: 0,
                    sell_pend_qty: 0,
                    usable_stock_qty: 0,
                    branch_id: branch_id,  // 根据需要设置
                    branch_position: branch_position,
                    branch_position_name: branch_position_name,
                    stock_qty_unit: '0',  // 根据需要设置
                    sell_pend_qty_unit: '0',  // 根据需要设置
                    usable_stock_qty_unit: '0'  // 根据需要设置
                };
            } else {
                return
            }

            batchStock.push(newBatch)

        }
        batchStock.sort((a, b) => parseInt(a.batch_id) - parseInt(b.batch_id));
        console.log(batchStock)
    }
    function initeditor_batch_no(row, cellvalue, editor, celltext, pressedkey) {
        console.log(editor)
        var inputElement = editor.find('div')[0];
        $(inputElement).jqxInput('clearOptions');
        var inputField = editor.find('input');
        if (pressedkey) {
            inputField.val(pressedkey);
            inputField.jqxInput('selectLast');
        }
        else {
            inputField.val({ value: cellvalue, label: celltext });
            inputField[0].value = celltext || '';
            inputField.jqxInput('selectAll');
        }
        $(inputElement).jqxInput('suggest', true);
    }

    function createeditor_batch_no(row, cellvalue, editor, cellText, width, height) {

        var gridSlt = '#jqxgrid'
        if (window.curGridSlt) gridSlt = window.curGridSlt
        var element = $('<div id="batchNo"></div>');
        editor.append(element);
        var inputElement = editor.find('div')[0];
        var sheetType = $('#sheetType').val();
        var rowIndex = $(gridSlt).jqxGrid('getselectedcell').rowindex;
        var batchStock = []
        let itemId = $(gridSlt).jqxGrid('getcellvalue', rowIndex, "item_id");
        let rowBranchId = "-1"
        let rowFromBranchId = "-1"
        let rowToBranchId = "-1"
        let rowBranchPosition = "0"
        let rowFromBranchPosition = "0"
        let rowToBranchPosition = "0"
        let key = ""
        let fromKey = ""
        let toKey = ""
        let sheetRows = $(gridSlt).jqxGrid('getrows')
        var sheetRow = sheetRows[rowIndex]
        if (sheetType === "DB") {
            var dataFields = [
                { datafield: "batch_no", text: "批次", width: 250 },
                { datafield: "produce_date", text: "生产日期", width: 120 },
                { datafield: "from_stock_qty_unit", text: "出仓实际库存", width: 80 },
                { datafield: "from_usable_stock_qty_unit", text: "出仓可用库存", width: 80 },
                { datafield: "to_stock_qty_unit", text: "入仓实际库存", width: 80 }]
        }
        else {
            var dataFields = [
                { datafield: "batch_no", text: "批次", width: 250 },
                { datafield: "produce_date", text: "生产日期", width: 120 },
                { datafield: "stock_qty_unit", text: "库存", width: 80 },
                { datafield: "usable_stock_qty_unit", text: "可用库存", width: 80 },
            ]
        }
        $(inputElement).jqxInput({
            placeHolder: "助记码/名称/编号", height: height, width: width,
            borderShape: "none",
            buttonUsage: 'list',
            showHeader: true,
            dropDownHeight: 350,
            displayMember: "batch_no",
            valueMember:"batch_id_f",
            dataFields: dataFields,
            searchFields: ["batch_no"],
            searchMode: 'none',
            opened:false,
            maxRecords: 4,
            url: '',
            source: function (query, response) {
                if (window.curGridSlt) gridSlt = window.curGridSlt
                sheetType = $('#sheetType').val();
                batchStock = []
                rowIndex = $(gridSlt).jqxGrid('getselectedcell').rowindex;
                itemId = $(gridSlt).jqxGrid('getcellvalue', rowIndex, "item_id");
                rowBranchId = "-1"
                rowFromBranchId = "-1"
                rowToBranchId = "-1"
                rowBranchPosition = "0"
                rowFromBranchPosition = "0"
                rowToBranchPosition = "0"
                key = ""
                fromKey = ""
                toKey = ""
                sheetRows = $(gridSlt).jqxGrid('getrows')
                sheetRow = sheetRows[rowIndex]
                if (sheetType == "DB") {
                    rowFromBranchId = $("#from_branch_id").val() ? $("#from_branch_id").val().value : "-1";
                    rowFromBranchPosition = sheetRow.from_branch_position ? sheetRow.from_branch_position : "0"
                    // fromKey = itemId + rowFromBranchId + rowFromBranchPosition
                    fromKey = `${itemId}_${rowFromBranchId}_${rowFromBranchPosition}`
                    rowToBranchId = $("#to_branch_id").val() ? $("#to_branch_id").val().value : "-1";
                    rowToBranchPosition = sheetRow.to_branch_position ? sheetRow.to_branch_position : "0"
                    // toKey = itemId + rowToBranchId + rowToBranchPosition
                    toKey = `${itemId}_${rowToBranchId}_${rowToBranchPosition}`
                }
                else if (sheetType == "ZZ" || sheetType == "CF") {
                    rowBranchPosition = sheetRow.branch_position ? sheetRow.branch_position : "0"
                    if (gridSlt = "#jqxgrid") {
                        rowBranchId = $("#from_branch_id").val() ? $("#from_branch_id").val().value : "-1";
                    } else {
                        rowBranchId = $("#to_branch_id").val() ? $("#to_branch_id").val().value : "-1";
                    }
                    // key = itemId + rowBranchId + rowBranchPosition
                    key = `${itemId}_${rowBranchId}_${rowBranchPosition}`
                }
                else {
                    rowBranchId = $(gridSlt).jqxGrid('getcellvalue', rowIndex, "branch_id")
                    let branchId = $("#branch_id").val() ? $("#branch_id").val().value : "-1"
                    rowBranchId = rowBranchId ? rowBranchId : branchId
                    rowBranchPosition = sheetRow.branch_position ? sheetRow.branch_position : "0"
                    // key = itemId + rowBranchId + rowBranchPosition
                    key = `${itemId}_${rowBranchId}_${rowBranchPosition}`
                }
                let isShowNoStock = false
                if (window.g_companySetting && window.g_companySetting.showNegativeStock && window.g_companySetting.showNegativeStock == "True") {
                    isShowNoStock = true
                }
                if (window.itemsBatchStockTotal && (window.itemsBatchStockTotal[key] || (window.itemsBatchStockTotal[fromKey] && window.itemsBatchStockTotal[toKey]))) {
                    if (sheetType == "DB") {
                        batchStock = []
                        let fromBatchStock = window.itemsBatchStockForShow[fromKey] ? window.itemsBatchStockForShow[fromKey] : []
                        let toBatchStock = window.itemsBatchStockForShow[toKey] ? window.itemsBatchStockForShow[toKey] : []
                        if (isShowNoStock) {
                            fromBatchStock = window.itemsBatchStockTotal[fromKey] ? window.itemsBatchStockTotal[fromKey] : []
                            toBatchStock = window.itemsBatchStockTotal[toKey] ? window.itemsBatchStockTotal[toKey] : []
                        }
                        fromBatchStock.forEach(f => {
                            let bs = JSON.parse(JSON.stringify(f))
                            bs.from_stock_qty_unit = f.stock_qty_unit
                            bs.from_usable_stock_qty_unit = f.usable_stock_qty_unit
                            bs.to_stock_qty_unit = 0
                            toBatchStock.some(t => {
                                if (f.batch_id == t.batch_id) {
                                    bs.to_stock_qty_unit = t.stock_qty_unit
                                    return true
                                }
                            })
                            batchStock.push(bs)
                        })
                    } else {
                        batchStock = window.itemsBatchStockForShow[key] ? window.itemsBatchStockForShow[key] : []
                        if (isShowNoStock) {
                            batchStock = window.itemsBatchStockTotal[key] ? window.itemsBatchStockTotal[key] : []
                        }
                    }
                    response(batchStock)
                }
                else {
                    let branchs_id = ""
                    let branchs_position = ""
                    if (sheetType == "DB") {
                        let fromBranchId = $('#from_branch_id').val() ? $('#from_branch_id').val().value : "-1"
                        let toBranchId = $('#to_branch_id').val() ? $('#to_branch_id').val().value : "-1"
                        branchs_id = fromBranchId + "," + toBranchId
                        let from_branch_position = sheetRow.from_branch_position ? sheetRow.from_branch_position : "0"
                        let to_branch_position = sheetRow.to_branch_position ? sheetRow.to_branch_position : "0"
                        branchs_position = from_branch_position + "," + to_branch_position
                    }
                    else if (sheetType == "ZZ" || sheetType == "CF") {
                        branchs_position = sheetRow.branch_position ? sheetRow.branch_position : "0"
                        if (gridSlt == "#jqxgrid") {
                            branchs_id = $('#from_branch_id').val() ? $('#from_branch_id').val().value : "-1"
                        }
                        else {
                            branchs_id = $('#to_branch_id').val() ? $('#to_branch_id').val().value : "-1"
                        }
                    }
                    else {
                        branchs_id = sheetRow.branch_id
                        if (!branchs_id) branchs_id = $('#branch_id').val() ? $('#branch_id').val().value : "-1"
                        branchs_position = sheetRow.branch_position ? sheetRow.branch_position : "0"

                    }
                    $.ajax({
                        url: '/api/SaleSheet/GetBatchStock',
                        type: 'GET',
                        contentType: 'application/json',
                        data: {
                            operKey: g_operKey,
                            items_id: itemId,
                            branch_id: branchs_id,
                            branch_position: branchs_position,
                            isShowNegativeStock: isShowNoStock
                        },
                        success: function (data) {
                            if (data.result == "OK") {

                                if (!window.itemsBatchStockTotal) window.itemsBatchStockTotal = {}
                                if (!window.itemsBatchStockForShow) window.itemsBatchStockForShow = {}
                                if (sheetType == "DB") {
                                    batchStock = []
                                    window.itemsBatchStockTotal[fromKey] = data.batchStockTotal && data.batchStockTotal[fromKey] ? data.batchStockTotal[fromKey] : []
                                    window.itemsBatchStockTotal[toKey] = data.batchStockTotal && data.batchStockTotal[toKey] ? data.batchStockTotal[toKey] : []
                                    window.itemsBatchStockForShow[fromKey] = data.batchStockForShow && data.batchStockForShow[fromKey] ? data.batchStockForShow[fromKey] : []
                                    window.itemsBatchStockForShow[toKey] = data.batchStockForShow && data.batchStockForShow[toKey] ? data.batchStockForShow[toKey] : []
                                    let fromBatchStock = window.itemsBatchStockForShow[fromKey] ? window.itemsBatchStockForShow[fromKey] : []
                                    let toBatchStock = window.itemsBatchStockForShow[toKey] ? window.itemsBatchStockForShow[toKey] : []
                                    if (isShowNoStock) {
                                        fromBatchStock = window.itemsBatchStockTotal[fromKey] ? window.itemsBatchStockTotal[fromKey] : []
                                        toBatchStock = window.itemsBatchStockTotal[toKey] ? window.itemsBatchStockTotal[toKey] : []
                                    }
                                    fromBatchStock.forEach(f => {
                                        let bs = JSON.parse(JSON.stringify(f))
                                        bs.from_stock_qty_unit = f.stock_qty_unit
                                        bs.from_usable_stock_qty_unit = f.usable_stock_qty_unit
                                        bs.to_stock_qty_unit = 0
                                        toBatchStock.some(t => {
                                            if (f.batch_id == t.batch_id) {
                                                bs.to_stock_qty_unit = t.stock_qty_unit
                                                return true
                                            }
                                        })
                                        batchStock.push(bs)
                                    })
                                } else {
                                    window.itemsBatchStockTotal[key] = data.batchStockTotal && data.batchStockTotal[key] ? data.batchStockTotal[key] : []
                                    window.itemsBatchStockForShow[[key]] = data.batchStockForShow && data.batchStockForShow[key] ? data.batchStockForShow[key] : []
                                    batchStock = window.itemsBatchStockForShow[key] ? window.itemsBatchStockForShow[key] : []
                                    if (isShowNoStock) {
                                        batchStock = window.itemsBatchStockTotal[key] ? window.itemsBatchStockTotal[key] : []
                                    }
                                }
                                response(batchStock)
                            }
                        }
                    })
                }
            },
            renderer: function (itemValue, inputValue) {
                var terms = inputValue.split(/,\s*/);
                let curIndex = $(gridSlt).jqxGrid('getselectedcell').rowindex;
                let curValue = $(gridSlt).jqxGrid('getcellvalue', curIndex, "batch_no");
                curValue=curValue?curValue:""
                if(inputValue == curValue && curValue!=itemValue){
                    terms.pop();
                    terms.push(itemValue);
                    terms.push("");
                }
                var value = terms.join(", ");
                return value;
            }
        });
        $(inputElement).on('change', function (event) {
            var value = $(inputElement).val();
            let batchNoInput = value.label?value.label:""
            let produceDateInput = ""
            console.log(value)
            if(value.value){
                produceDateInput = value.produce_date
                //$(gridSlt).jqxGrid('showcolumn', 'produce_date');
            }else{
                produceDateInput = $(gridSlt).jqxGrid('getcellvalue', rowIndex, "produce_date");
            }
            let selectedValue = null
            var matchedItem = batchStock.some(item => {
                if(item.produce_date === produceDateInput && item.batch_no === batchNoInput){
                    selectedValue = item
                    return true
                }
            });
            if(!matchedItem){
                sheetRow.batch_no = batchNoInput
            }
            else{
                sheetRow.batch_no = selectedValue.batch_no
                sheetRow.produce_date = selectedValue.produce_date
            }
            $(gridSlt).jqxGrid('endcelledit', rowIndex, "batch_id_f", false);
        })
    }
    function money_renderer(row, columnfield, value, defaulthtml, columnproperties) {
        return '<div style="margin-right:4px; height:100%;display:flex;align-items:center;justify-content:flex-end;">' + toMoneyText(value,2,2) + '</div>';
    }
    function price_renderer(row, columnfield, value, defaulthtml, columnproperties) {
        return '<div style="margin-right:4px; height:100%;display:flex;align-items:center;justify-content:flex-end;">' + toMoneyText(value,4,2) + '</div>';
    }
    function cost_price_renderer(row, columnfield, value, defaulthtml, columnproperties) {
        return '<div style="margin-right:4px; height:100%;display:flex;align-items:center;justify-content:flex-end;">' + toMoneyText(value,4,4) + '</div>';
    }
    function cellsrenderer_produce_date(row, columnfield, value, defaulthtml, columnproperties) {
        var gridSlt = '#jqxgrid'
        if (window.curGridSlt) gridSlt = window.curGridSlt

        var rowData = $(gridSlt).jqxGrid('getrows')
        rowData = rowData[row]
        var attrSvg = '<div></div>'
        if (rowData.batch_stock) {
            var attrs = rowData.batch_stock
            if (!attrs.forEach) attrs = JSON.parse(attrs)

            if (attrs.length > 1) {
                attrSvg = `<svg id="svgFeather" height="15" width="15" style="cursor:pointer">
                                    <use xlink:href="/images/images.svg#feather" />
                                </svg>`;
            }

        }
        if (value === "") {
            var s = `<div style="margin: 4px;height:calc(100% - 8px);display:flex;justify-content:space-between;align-items:center;">
                    ${attrSvg}
                    <div>${value}<div></div>`;
        } else {
            var s = `<div style="margin: 4px;height:calc(100% - 8px);display:flex;justify-content:space-between;align-items:center;">
                   ${value}</div>`;
        }

        return s;
    }

    function cellsrenderer_quantity(row, columnfield, value, defaulthtml, columnproperties) {
        var gridSlt = '#jqxgrid'
        if (window.curGridSlt) gridSlt = window.curGridSlt
        var rowData = $(gridSlt).jqxGrid('getrows')
        rowData = rowData[row]
        var attrSvg = '<div></div>'

        if (rowData && rowData.mum_attributes) {
            var attrs = rowData.mum_attributes
            if (!attrs.forEach) attrs = JSON.parse(attrs)

            if (attrs.length) {
                attrSvg = `<svg id="svgFeather" onmousedown="onBtnAttrClick(event,${row})" height="15" width="15" style="cursor:pointer">
                                <use xlink:href="/images/images.svg#feather" />
                            </svg>`;
            }

        }
        var color = ""
        if (value == 0) {
            color="color:#f00;"
        }
        if(value != "" && $('#sheetType').val() === 'JH') {
            value = Math.abs(value)
            color=""
        }
        var s = `<div style="margin: 4px;height:calc(100% - 8px);display:flex;justify-content:space-between;align-items:center;${color}">
                ${attrSvg}
                <div>${value}<div></div>`;
        return s;
    }

    window.getUnitQtyOfSheetRow = function (row) {
        let b_qty = 0,
            m_qty = 0,
            s_qty = 0
        let leftQty = row.quantity * row.unit_factor
        let unitsQty = ""
        let absLeftQty = Math.abs(leftQty)
        let flag = leftQty < 0 ? -1 : 1

        if (window.g_queriedItems && row.item_id) {
            var key = getQueriedItemsKeyFromRow(row)
            var item = window.g_queriedItems[key];
            if (item) {
                item.units.forEach(unit => {
                    if (unit.unit_type == 'b') {
                        row.b_unit_no = unit.unit_no
                        row.b_unit_factor = unit.unit_factor
                    }
                    else if (unit.unit_type == 'm') {
                        row.m_unit_no = unit.unit_no
                        row.m_unit_factor = unit.unit_factor
                    }
                    else if (unit.unit_type == 's') {
                        row.s_unit_no = unit.unit_no
                        row.s_unit_factor = unit.unit_factor
                    }
                })


            }
        }


        if (row.b_unit_factor) {
            b_qty = parseInt(absLeftQty / row.b_unit_factor)
            absLeftQty = absLeftQty % row.b_unit_factor
            if (b_qty < 0.001) {
                b_qty = 0
            }

            if (b_qty > 0) {
                b_qty *= flag

                unitsQty += toMoney(b_qty) + row.b_unit_no
            }
        }
        if (row.m_unit_factor) {
            m_qty = parseInt(absLeftQty / row.m_unit_factor)
            absLeftQty = absLeftQty % row.m_unit_factor
            if (m_qty < 0.001) {
                m_qty = 0
            }
            if (m_qty > 0) {
                m_qty *= flag
                unitsQty += toMoney(m_qty) + row.m_unit_no
            }
        }
        s_qty = absLeftQty
        if (s_qty < 0.001) {
            s_qty = 0
        }
        if (s_qty > 0) {
            s_qty *= flag
            unitsQty += toMoney(s_qty) + row.s_unit_no
        }

        row.b_quantity = b_qty
        row.m_quantity = m_qty
        row.s_quantity = s_qty

        return unitsQty
    }
    function cellsrenderer_multi_qty(row, column, value, p4, p5, rowData) {
        if (!rowData) return

        var s = getUnitQtyOfSheetRow(rowData)

        var html = `<div style="height:100%;display:flex;align-items:center;justify-content:flex-end;"><label style="margin-right:3px;">${s}</label></div>`

        //var html = `<div style="height:100%;display:flex;align-items:center;justify-content:flex-end;"><label>${value}</label><label style="margin-right:3px;">${unit_no || ''}</label></div>`
        return html;
    }

    function createeditor_multi_qty(row, cellvalue, editor, cellText, width, height) {
        var rowData = $('#jqxgrid').jqxGrid('getrowdata', row)
        if (!rowData) return

        var s = ''
        //if (rowData.b_unit_no) {
        //<label>${rowData.b_unit_no}
        //placeholder="${rowData.b_unit_no}"
        s += `<input id='b_qty'  onkeydown="onQtyInputKeyDown(event)" style="width:25px;height:35px;border:none;outline:none;" autocomplete="off" /> <label id="b_qty_lbl" style="font-size:14px;"></label>`
        //  }
        // if (rowData.m_unit_no) {
        s += `<input id='m_qty'  onkeydown="onQtyInputKeyDown(event)" style="width:25px;height:35px;border:none;outline:none;margin-left:2px;" autocomplete="off"/><label id="m_qty_lbl"></label>`
        // }
        s += `<input id='s_qty' style="width:25px;height:35px;border:none;outline:none;margin-left:2px;" autocomplete="off"/><label id="s_qty_lbl" style="font-size:14px;"></label>`
        s = `<div style="text-align:right;font-size:9px;width:100%;display:flex;justify-content:flex-end;align-items:center;font-size:14px;">
                        ${s}
                   </div>`
        var element = $(s);
        editor.append(element);
    }
    function onQtyInputKeyDown(event) {

        if (event.keyCode != 13) return
        var input = event.target
        var nextInput = null
        if (input.id == 'b_qty') {
            nextInput = $(input.parentNode).find('#m_qty:visible')
        }
        if (!nextInput || nextInput.length == 0) {
            nextInput = $(input.parentNode).find('#s_qty')
        }
        if (nextInput && nextInput.length) {
            nextInput.focus()
            event.stopPropagation()
        }
    }
    function initeditor_multi_qty(row, cellvalue, editor, celltext, pressedkey) {
        var rowData = $('#jqxgrid').jqxGrid('getrowdata', row)
        if (!rowData) return
        var b_input = editor.find('#b_qty');
        var m_input = editor.find('#m_qty');
        var s_input = editor.find('#s_qty');
        var b_input_lbl = editor.find('#b_qty_lbl')
        var m_input_lbl = editor.find('#m_qty_lbl')
        var s_input_lbl = editor.find('#s_qty_lbl')

        var attrs = rowData.mum_attributes
        if (attrs && attrs.length === undefined) attrs = JSON.parse(attrs)
        var stockAttr = attrs ? attrs.find(attr => attr.distinctStock) : null

        getUnitQtyOfSheetRow(rowData)

        b_input.css('display', rowData.b_unit_no ? 'block' : 'none')
        m_input.css('display', rowData.m_unit_no ? 'block' : 'none')
        s_input.css('display', rowData.s_unit_no ? 'block' : 'none')

        b_input_lbl.css('display', rowData.b_unit_no ? 'block' : 'none')
        m_input_lbl.css('display', rowData.m_unit_no ? 'block' : 'none')
        s_input_lbl.css('display', rowData.s_unit_no ? 'block' : 'none')

        //b_input.attr('placeholder', rowData.b_unit_no)
        //m_input.attr('placeholder', rowData.m_unit_no)
        //s_input.attr('placeholder', rowData.s_unit_no)

        b_input.val(rowData.b_quantity || '');
        m_input.val(rowData.m_quantity || '');
        s_input.val(rowData.s_quantity || '');

        b_input_lbl.text(rowData.b_unit_no || '')
        m_input_lbl.text(rowData.m_unit_no || '')
        s_input_lbl.text(rowData.s_unit_no || '')

        if (pressedkey) {
            if (b_input && rowData.b_unit_no) {
                b_input.val(pressedkey);
            }
            else {
                s_input.val(pressedkey);
            }
        }


        setTimeout(function () {
            if (b_input.length && rowData.b_unit_no) b_input.focus()
            else s_input.focus()
        }, 100)
    }

    function geteditorvalue_quantity(row, cellvalue, editor) {
        var qty = editor.val();
        var regex = /^[0-9+\-*/.()]+$/;
        if(!regex.test(qty))
           return qty;
        let result = eval(qty);
        return result
    }

    function geteditorvalue_multi_qty(row, cellvalue, editor) {
        var rowData = $('#jqxgrid').jqxGrid('getrowdata', row)
        if (!rowData) return
        getUnitQtyOfSheetRow(rowData)

        var b_input = editor.find('#b_qty');
        var m_input = editor.find('#m_qty');
        var s_input = editor.find('#s_qty');
        var unitCount=0
        if (b_input) rowData.b_quantity = b_input.val()
        if (m_input) rowData.m_quantity = m_input.val()
        if (s_input) rowData.s_quantity = s_input.val()
        var sQty = 0
        if (rowData.b_quantity) {
            sQty += parseFloat(rowData.b_quantity * rowData.b_unit_factor)
            unitCount++
        }
        if (rowData.m_quantity) {
            sQty += parseFloat(rowData.m_quantity * rowData.m_unit_factor)
            unitCount++
        }
        if (rowData.s_quantity) {
            sQty += parseFloat(rowData.s_quantity)
            unitCount++
        }
        var curQty = parseFloat(rowData.quantity) * parseFloat(rowData.unit_factor)
        var swithToSmallUnit=false
        if (unitCount > 1) {

            if (curQty != sQty) {
                swithToSmallUnit=true
            }
        }
        else if(curQty!=sQty) {
            var leftQty = sQty % rowData.unit_factor
            if (leftQty > 0) {
                swithToSmallUnit=true

            }
            else {
                rowData.quantity=sQty / rowData.unit_factor
            }
        }
        if (swithToSmallUnit) {
            rowData.quantity = sQty


            if (rowData.real_price) rowData.real_price = toMoney(rowData.real_price / rowData.unit_factor, 4)
            if (rowData.orig_price) rowData.orig_price = toMoney(rowData.orig_price / rowData.unit_factor, 4)
            if (rowData.sys_price) rowData.sys_price = toMoney(rowData.sys_price / rowData.unit_factor, 4)
            if (rowData.wholesale_price) rowData.wholesale_price = toMoney(rowData.wholesale_price / rowData.unit_factor, 4)
            if (rowData.cost_price) rowData.cost_price = toMoney(rowData.cost_price / rowData.unit_factor, 4)
            rowData.unit_no = rowData.s_unit_no
            rowData.unit_factor = 1
        }

        if (window.updateRowSubAmount){
            window.updateRowSubAmount(row)
            window.updateRowSubAmount(row)
            updateTotalAmount()
        }
        //$('#jqxgrid').jqxGrid('updategrid')

        return '';
    }


    function cellsrenderer_multi_price(row, column, value, p4, p5, rowData) {
        if (!rowData) return

        getUnitQtyOfSheetRow(rowData)
        var unitPrice = rowData.real_price / rowData.unit_factor
        var s = ''
        if (rowData.b_unit_no) {
            s = toMoney(rowData.b_unit_factor * unit_price, 2) + '/' + rowData.b_unit_no
        }

        if (rowData.m_unit_no) {
            if (s) s += ' '
            s = toMoney(rowData.m_unit_factor * unit_price, 2) + '/' + rowData.m_unit_no
        }

        if (s) s += ' '
        s = toMoney(unit_price, 4) + '/' + rowData.s_unit_no

        var html = `<div style="height:100%;display:flex;align-items:center;justify-content:flex-end;"><label style="margin-right:3px;">${s}</label></div>`

        //var html = `<div style="height:100%;display:flex;align-items:center;justify-content:flex-end;"><label>${value}</label><label style="margin-right:3px;">${unit_no || ''}</label></div>`
        return html;
    }

    function createeditor_multi_price(row, cellvalue, editor, cellText, width, height) {
        var rowData = $('#jqxgrid').jqxGrid('getrowdata', row)
        if (!rowData) return

        var s = ''
        //if (rowData.b_unit_no) {
        //<label>${rowData.b_unit_no}
        //placeholder="${rowData.b_unit_no}"
        s += `<input id='b_price'  onkeydown="onQtyInputKeyDown_price(event)" style="width:25px;height:35px;border:none;outline:none;" autocomplete="off" /> <label id="b_price_lbl"></label>`
        //  }
        // if (rowData.m_unit_no) {
        s += `<input id='m_price'  onkeydown="onQtyInputKeyDown_price(event)" style="width:25px;height:35px;border:none;outline:none;margin-left:2px;" autocomplete="off"/><label id="m_price_lbl"></label>`
        // }
        s += `<input id='s_price' style="width:25px;height:35px;border:none;outline:none;margin-left:2px;" autocomplete="off"/><label id="s_price_lbl"></label>`
        s = `<div style="text-align:right;font-size:9px;width:100%;display:flex;justify-content:flex-end;align-items:center;">
                                ${s}
                           </div>`
        var element = $(s);
        editor.append(element);
    }
    function onQtyInputKeyDown_price(event) {

        if (event.keyCode != 13) return
        var input = event.target
        var nextInput = null
        if (input.id == 'b_qty') {
            nextInput = $(input.parentNode).find('#m_qty:visible')
        }
        if (nextInput.length == 0) {
            nextInput = $(input.parentNode).find('#s_qty')
        }
        if (nextInput.length) {
            nextInput.focus()
            event.stopPropagation()
        }
    }
    function initeditor_multi_price(row, cellvalue, editor, celltext, pressedkey) {
        var rowData = $('#jqxgrid').jqxGrid('getrowdata', row)
        if (!rowData) return

        getUnitQtyOfSheetRow(rowData)
        var unitPrice = rowData.real_price / rowData.unit_factor
        var b_price = '', m_price = '', s_price = unitPrice
        if (rowData.b_unit_no) {
            b_price = toMoney(rowData.b_unit_factor * unit_price, 2)
        }

        if (rowData.m_unit_no) {
            m_price = toMoney(rowData.m_unit_factor * unit_price, 2)
        }

        var b_input = editor.find('#b_price');
        var m_input = editor.find('#m_price');
        var s_input = editor.find('#s_price');
        var b_input_lbl = editor.find('#b_price_lbl')
        var m_input_lbl = editor.find('#m_price_lbl')
        var s_input_lbl = editor.find('#s_price_lbl')

        var attrs = rowData.mum_attributes
        if (attrs) attrs = JSON.parse(attrs)
        var stockAttr = attrs ? attrs.find(attr => attr.distinctStock) : null

        getUnitQtyOfSheetRow(rowData)

        b_input.css('display', rowData.b_unit_no ? 'block' : 'none')
        m_input.css('display', rowData.m_unit_no ? 'block' : 'none')
        s_input.css('display', rowData.s_unit_no ? 'block' : 'none')

        b_input_lbl.css('display', rowData.b_unit_no ? 'block' : 'none')
        m_input_lbl.css('display', rowData.m_unit_no ? 'block' : 'none')
        s_input_lbl.css('display', rowData.s_unit_no ? 'block' : 'none')

        //b_input.attr('placeholder', rowData.b_unit_no)
        //m_input.attr('placeholder', rowData.m_unit_no)
        //s_input.attr('placeholder', rowData.s_unit_no)

        b_input.val(b_price || '')
        m_input.val(m_price || '')
        s_input.val(s_price || '')

        b_input_lbl.text('/' + rowData.b_unit_no || '')
        m_input_lbl.text('/' + rowData.m_unit_no || '')
        s_input_lbl.text('/' + rowData.s_unit_no || '')

        if (pressedkey) {
            if (b_input && rowData.b_unit_no) {
                b_input.val(pressedkey)
            }
            else {
                s_input.val(pressedkey)
            }
        }


        setTimeout(function () {
            if (b_input.length && rowData.b_unit_no) b_input.focus()
            else s_input.focus()
        }, 100)
    }
    function geteditorvalue_multi_price(row, cellvalue, editor) {
        var rowData = $('#jqxgrid').jqxGrid('getrowdata', row)
        if (!rowData) return
        getUnitQtyOfSheetRow(rowData)

        var b_input = editor.find('#b_price');
        var m_input = editor.find('#m_price');
        var s_input = editor.find('#s_price');
        if (b_input) rowData.b_quantity = b_input.val()
        if (m_input) rowData.m_quantity = m_input.val()
        if (s_input) rowData.s_quantity = s_input.val()
        var sQty = 0
        if (rowData.b_quantity) {
            sQty += parseFloat(rowData.b_quantity * rowData.b_unit_factor)
        }
        if (rowData.m_quantity) {
            sQty += parseFloat(rowData.m_quantity * rowData.m_unit_factor)
        }
        if (rowData.s_quantity) {
            sQty += parseFloat(rowData.s_quantity)
        }

        rowData.quantity = sQty
        rowData.unit_no = rowData.s_unit_no
        rowData.unit_factor = 1
        if (window.updateRowSubAmount) {
            window.updateRowSubAmount(row)
        }
        //  $('#jqxgrid').jqxGrid('updategrid')

        return '';
    }
    function getQtyUnits(qtyUnits, sheetRow) {// item_id, qty, unit_no) {
        if (!sheetRow.quantity) return qtyUnits
        var unitType = '', unitTypeLabel = '小';
        var key = getQueriedItemsKeyFromRow(sheetRow)
        var item = window.g_queriedItems[key]
        if (item) {
            item.units.forEach(function (unit) {
                if (unit.unit_no == sheetRow.unit_no) unitType = unit.unit_type;
            })
        }
        if (unitType == 'm') unitTypeLabel = '中';
        else if (unitType == 'b') unitTypeLabel = '大';
        var n = qtyUnits.indexOf(unitTypeLabel);
        if (n > 0) {
            var unitQty = 0;
            var replacePart = "";
            for (i = n - 1; i >= 0; i--) {
                var tmp = parseFloat(qtyUnits.substring(i, n));
                if (tmp.toString() != "NaN") {
                    unitQty = tmp;
                    replacePart = qtyUnits.substring(i, n)
                }
                else break;
            }

            qtyUnits = qtyUnits.replace(replacePart + unitTypeLabel, toMoney(parseFloat(unitQty) + parseFloat(sheetRow.quantity)) + unitTypeLabel)
        }
        else {
            var bLeft = false
            var bMid = false
            var hasSmallUnit = qtyUnits.indexOf('小') !== -1
            var hasBigUnit = qtyUnits.indexOf('大') !== -1
            if (unitType == 'b') {
                bLeft = true
            }
            else if (unitType == 'm') {
                if (hasSmallUnit && !hasBigUnit) {
                    bLeft = true
                }
                else if (hasSmallUnit && hasBigUnit) {
                    bMid = true
                }
            }
            sheetRow.quantity = $('#sheetType').val() === 'JH' ? Math.abs(sheetRow.quantity) : sheetRow.quantity

            if (bMid) {
                var arr = qtyUnits.split('大')
                qtyUnits = arr[0] + "大" + toMoney(sheetRow.quantity).toString() + unitTypeLabel + arr[1]
            }
            else if (bLeft) {
                qtyUnits = toMoney(sheetRow.quantity).toString() + unitTypeLabel + qtyUnits
            }
            else
                qtyUnits = qtyUnits + toMoney(sheetRow.quantity).toString() + unitTypeLabel
        }
        return qtyUnits
    }


    var aggregates_quantity = [{
        'qty':
            function (aggregatedValue, currentValue, column, sheetRow) {
                //var item_id = sheetRow.item_id
                //var unit_no = sheetRow.unit_no;
                var qty = sheetRow.quantity;
                if (!aggregatedValue) aggregatedValue = ""

                var sheetType = ''
                if ($('#sheetType').length > 0) {
                    sheetType = $('#sheetType').val()
                }
                if (sheetType == 'X' || sheetType == 'XD') {
                    var saleQtyUnits = '', returnQtyUnits = '';

                    var arr = aggregatedValue.split('退:')
                    if (arr.length == 2) {
                        saleQtyUnits = arr[0].trim()
                        returnQtyUnits = arr[1]
                    }
                    else saleQtyUnits = aggregatedValue
                    saleQtyUnits = saleQtyUnits.replace('销:', '')
                    if (qty < 0) {
                        qty = qty * (-1)
                        returnQtyUnits = getQtyUnits(returnQtyUnits, sheetRow)// item_id, qty, unit_no)
                    }
                    else {
                        saleQtyUnits = getQtyUnits(saleQtyUnits, sheetRow)//item_id, qty, unit_no)
                    }
                    if (returnQtyUnits) {
                        returnQtyUnits = '退:' + returnQtyUnits
                        saleQtyUnits = '销:' + saleQtyUnits
                        aggregatedValue = saleQtyUnits + ' ' + returnQtyUnits
                    }
                    else aggregatedValue = saleQtyUnits
                }
                else {
                    aggregatedValue = getQtyUnits(aggregatedValue,sheetRow)// item_id, qty, unit_no)
                }

                return aggregatedValue;
            }
    }];
    function aggregatesrenderer_quantity(aggregates, column, element, summaryData) {
        var renderstring = "<div class='jqx-widget-content style='float: left; width: 100%; height: 100%; '>";
        $.each(aggregates, function (key, value) {
            renderstring += '<div style="position: relative; margin: 6px; text-align: right; overflow: hidden;">' + value + '</div>';
        });
        renderstring += "</div>";
        return renderstring;
    }
    var aggregates_sub_amount = [{
        'xj':
            function (aggregatedValue, currentValue) {
                return toMoney(Number(aggregatedValue) + (Number(currentValue) || 0));
            }
    }];

    function aggregatesrenderer_sub_amount(aggregates, column, element, summaryData) {
        var renderstring = "<div class='jqx-widget-content style='float: left; width: 100%; height: 100%; '>";
        $.each(aggregates, function (key, value) {
            renderstring += '<div style="position: relative; margin: 6px; text-align: right; overflow: hidden;">' + value + '</div>';
        });
        renderstring += "</div>";
        return renderstring;
    }

     function createeditor_branch_name(row, cellvalue, editor, cellText, width, height) {

        var element = $('<div id="txtBranchName"></div>')
        editor.append(element)


        var inputElement = editor.find('div')[0];

        var sheetType = ''
        if ($('#sheetType').length > 0) {
            sheetType = $('#sheetType').val()
        }
        //todo:判断仓库权限

        var dataFields = [
            { datafield: "l", text: "仓库名称", width: width }]
        $(inputElement).jqxInput({
            placeHolder: "助记码/名称/编号", height: height, width: width,
            borderShape: "none",
            buttonUsage: 'list',
            checkboxes: false,
            showHeader: true,
            dropDownHeight: 260,
            displayMember: "l",
            valueMember: "v",
            dataFields: dataFields,
            searchFields: ["z", "l"],
            searchMode: 'none',
            maxRecords: 9,
            source: function (query, response) {
                if(window.branchList&&window.branchList.length>0){
                    response(window.branchList)
                }
                else{
                    $.ajax({
                        url: '/api/SaleSheet/GetBranchAndPositionInfo',
                        type: 'GET',
                        contentType: 'application/json',
                        data: { operKey: g_operKey, dataItemName: "branch_id" },
                        success: function (data) {
                            if (data.result === 'OK') {
                                window.branchList = data.records
                                response(data.records)
                            } else {
                                bw.toast(data.msg)
                            }
                        }
                    })
                }
            },
            renderer: function (itemValue, inputValue) {
                var terms = inputValue.split(/,\s*/);
                let curIndex = $("#jqxgrid").jqxGrid('getselectedcell').rowindex;
                let curValue = $("#jqxgrid").jqxGrid('getcellvalue', curIndex, "branch_name");
                curValue=curValue?curValue:""
                if(inputValue == curValue && curValue!=itemValue){
                    terms.pop();
                    terms.push(itemValue);
                    terms.push("");
                }
                var value = terms.join(", ");
                return value;
            },
        })
    }

    function initeditor_branch_name(row, cellvalue, editor, celltext, pressedkey) {

        var inputField = editor.find('input');

        if (pressedkey) {

            var container = window.parent.CefGlue
            var simulateInput = null
            if (container) simulateInput = container.simulateInput
            if (!simulateInput) {
                inputField.val(pressedkey);
                inputField.jqxInput('selectLast');
            }
            else {
                inputField.focus()
                setTimeout(() => {
                    inputField.val('')
                    simulateInput(pressedkey)
                    console.log('keycode:' + pressedkey)

                }, 30)
            }


        }
        else {
            inputField.val({ value: cellvalue, label: celltext });
            inputField[0].value = celltext || '';
            inputField.jqxInput('selectAll');

        }
    }

    function createeditor_branch_position_name(row, cellvalue, editor, cellText,width,height,colName) {

        var element = $('<div id="txtBranchPositionName"></div>')
        editor.append(element)


        var inputElement = editor.find('div')[0];

        var sheetType = ''
        if ($('#sheetType').length > 0) {
            sheetType = $('#sheetType').val()
        }
        //todo:判断仓库权限

        var dataFields = [
            { datafield: "l", text: "库位", width: 100 },
            { datafield: "t", text: "库位类型", width: 100 },
        ]
        $(inputElement).jqxInput({
            placeHolder: "助记码/名称/编号", height: height, width: width,
            borderShape: "none",
            buttonUsage: 'list',
            checkboxes: false,
            showHeader: true,
            dropDownHeight: 260,
            displayMember: "l",
            valueMember: "v",
            dataFields: dataFields,
            searchFields: ["z", "v","t"],
            searchMode: 'none',
            maxRecords: 9,
            source: function (query, response) {
                sheetType = $('#sheetType').val()
                let gridSlt = "#jqxgrid"
                if (window.curGridSlt) gridSlt = window.curGridSlt
                var branch_id =""
                rowIndex = $(gridSlt).jqxGrid('getselectedcell').rowindex;
                if(sheetType == "DB"){
                    if(editor[0].id.indexOf("from_branch_position")>-1){
                        branch_id =  $(gridSlt).jqxGrid('getcellvalue', rowIndex, 'from_branch_id');
                        if(!branch_id) branch_id = $("#from_branch_id").val().value
                    }else{
                         branch_id =  $(gridSlt).jqxGrid('getcellvalue', rowIndex, 'to_branch_id');
                        if(!branch_id) branch_id = $("#to_branch_id").val().value
                    }
                }else if(sheetType == "ZZ" || sheetType == "CF"){
                    if (gridSlt=="#jqxgrid") {
                        branch_id = $("#from_branch_id").val().value
                    } else {
                        branch_id = $("#to_branch_id").val().value
                    }
                }
                else{
                     branch_id =  $(gridSlt).jqxGrid('getcellvalue', rowIndex, 'branch_id');
                     if(!branch_id) branch_id = $("#branch_id").val().value
                }
                if(window.branchPositionList && window.branchPositionList[branch_id]){
                    console.log(window.branchPositionList[branch_id])
                    response(window.branchPositionList[branch_id])
                }else{
                    $.ajax({
                        url: '/api/SaleSheet/GetBranchAndPositionInfo',
                        type: 'GET',
                        contentType: 'application/json',
                        data: { operKey: g_operKey, dataItemName: "branch_position" ,branchId:branch_id},
                        success: function (data) {
                            if (data.result === 'OK') {
                                if(!window.branchPositionList) window.branchPositionList={}
                                if(window.branchPositionList && !window.branchPositionList[branch_id]) window.branchPositionList[branch_id] = data.records
                                response(window.branchPositionList[branch_id])
                            }else{
                                bw.toast(data.msg)
                            }
                        }
                    })
                }
            },
            renderer: function (itemValue, inputValue) {
                var terms = inputValue.split(/,\s*/);
                var gridSlt = '#jqxgrid'
                if (window.curGridSlt) gridSlt = window.curGridSlt
                console.log(gridSlt)
                let curIndex = $(gridSlt).jqxGrid('getselectedcell').rowindex;
                if(!colName) colName = "branch_position_name"
                let curValue = $(gridSlt).jqxGrid('getcellvalue', curIndex, colName);
                curValue=curValue?curValue:""
                if(inputValue == curValue && curValue!=itemValue){
                    terms.pop();
                    terms.push(itemValue);
                    terms.push("");
                }
                var value = terms.join(", ");
                return value;
            },
        })

        //$(inputElement).on('change',
        //    function (a,b) {
        //        var value = $(inputElement).val();
        //        console.log(value)
        //        var gridSlt = '#jqxgrid'
        //        if (window.curGridSlt) gridSlt = window.curGridSlt
        //        let colNameValue = ""
        //        if(!colName) {
        //            colName = "branch_position_name"
        //            colNameValue = ""
        //        }
        //        $(gridSlt).jqxGrid('setcellvalue', rowIndex,colName,value?value.value:"");
        //    }
        //)
            }

    function initeditor_branch_position_name(row, cellvalue, editor, celltext, pressedkey) {

        var inputField = editor.find('input');

        if (pressedkey) {

            var container = window.parent.CefGlue
            var simulateInput = null
            if (container) simulateInput = container.simulateInput
            if (!simulateInput) {
                inputField.val(pressedkey);
                inputField.jqxInput('selectLast');
            }
            else {
                inputField.focus()
                setTimeout(() => {
                    inputField.val('')
                    simulateInput(pressedkey)
                    console.log('keycode:' + pressedkey)

                }, 30)
            }


        }
        else {
            inputField.val({ value: cellvalue, label: celltext });
            inputField[0].value = celltext || '';
            inputField.jqxInput('selectAll');

        }
    }

    function createeditor_item_name(row, cellvalue, editor, cellText, width, height) {

        var element = $('<div id="txtItemName"></div>')
        editor.append(element)


        var inputElement = editor.find('div')[0];

        var sheetType = ''
        if ($('#sheetType').length > 0) {
            sheetType = $('#sheetType').val()
        }

            var canSeePrice=false
            if (window.g_operRights && window.g_operRights.delicacy && window.g_operRights.delicacy.seeInPrice) {
                if (',CG,CT,CD,ZZ,CF'.indexOf(','+sheetType+',')>=0){
                    if (window.g_operRights.delicacy.seeInPrice.value) {
                        canSeePrice=true
                    }
                }
                if (',X,T,XD,TD,'.indexOf(',' + sheetType + ',') >= 0) {
                    canSeePrice=true
                }
            }
            var dataFields = [
                { datafield: "name", text: "商品名称", width: 360 },
                { datafield: "item_spec", text: "规格", width: 100 },
                { datafield: "code", text: "条码", width: 140 },
                { datafield: "stock", text: "库存", width: 100 }
            ]

            if (canSeePrice){
                dataFields.push({ datafield: "b_price", text: "大单价", width: 50 })
                dataFields.push({ datafield: "s_price", text: "小单价", width: 50 })
            }
            var gridSlt = '#jqxgrid'
            if (window.curGridSlt) gridSlt = window.curGridSlt
            $(inputElement).jqxInput({
                placeHolder: "助记码/名称/编号", height: height, width: width,
                borderShape: "none",
                buttonUsage: 'event',
                checkboxes:true,
                showHeader: true,
                dropDownHeight: 260,
                displayMember: "name",
                valueMember: "id",
                dataFields: dataFields,
                searchFields: ["zjm", "name", "code", "item_no","mum_attributes"],
                maxRecords: 9,
                source: function (query, response) {
                    var branch_id = ''
                    var branch_position=""
                    if ($('#branch_id').length) {
                        branch_id=$('#branch_id').jqxInput('val')
                    }
                    else if ($('#from_branch_id').length) {
                        branch_id=$('#from_branch_id').jqxInput('val')
                    }
                    if (branch_id) branch_id = branch_id.value
                    var cell = $(gridSlt).jqxGrid('getselectedcell');
                    let rowBranchId = $(gridSlt).jqxGrid('getcellvalue',cell.rowindex, "branch_id");
                    if (rowBranchId) branch_id = rowBranchId
                    var showSonItems = ''
                    //

                    if (',DH,CL,'.indexOf(','+sheetType+',')==-1 && window.g_companySetting && window.g_companySetting.showSonItems == 'True') {
                       showSonItems='1'
                    }

    // 新增：判断是否精确查找
    //exactItemNameSearch由exactItemNameSearch决定,exactItemNameSearch在公司设置保存时改变
    var exactItemNameSearch = (window.g_companySetting && window.g_companySetting.exactItemNameSearch && window.g_companySetting.exactItemNameSearch.toLowerCase() === 'true') ? 1 : 0;
    var postData = { operKey: g_operKey, sheetType: sheetType, canSeePrice: canSeePrice, query: query, branch_id: branch_id, showSonItems: showSonItems };
    if (exactItemNameSearch) {
        postData.exactItemNameSearch = 1;
    }
    $.ajax({
        url: '/api/SaleSheet/GetItems',
        type: 'GET',
        contentType: 'application/json',
        data: postData,
        success: function (data) {
            if (data.result === 'OK') {
                console.log("精确查找时，只保留名称完全匹配的项");
                // 精确查找时，只保留名称完全匹配的项
                //if (exactItemNameSearch) {
                //    var filtered = data.records.filter(function(item){
                //        return item.name && item.name.indexOf(query) !== -1;
                //    });
                //    response(filtered);
                //} else {
                    response(data.records)
                //}
            }
        }
    })

            },
            renderer: function (itemValue, inputValue) {
                // ;
                var terms = inputValue.split(/,\s*/);
                // remove the current input
                terms.pop();
                // add the selected item
                terms.push(itemValue);
                // add placeholder to get the comma-and-space at the end
                // terms.push("");
                //var value = terms.join(", ");
                //return terms;
                return itemValue;
            },
            onButtonClick: function () {
                window.curRowIndex = row;
                $('#popItem').jqxWindow('open');
                var params = ''

                var branch_id = ''
                if ($('#branch_id').length > 0) {
                    branch_id = $('#branch_id').jqxInput('val')
                }
                else if ($('#from_branch_id').length > 0) {
                    branch_id = $('#from_branch_id').jqxInput('val')
                }
                if (branch_id && branch_id.value) branch_id = branch_id.value
                var cell = $(gridSlt).jqxGrid('getselectedcell');
                var rowindex = cell.rowindex;
                let rowBranchId = $(gridSlt).jqxGrid('getcellvalue',rowindex, "branch_id");
                if (rowBranchId) branch_id = rowBranchId

                if (branch_id && parseInt(branch_id)) params += `&branch_id=${branch_id}`

                if ($('#supcust_id').length > 0) {
                    var supcust_id = $('#supcust_id').jqxInput('val').value
                    if (supcust_id) {
                        params += `&supcust_id=${supcust_id}`
                    }
                }

                var sheetType = ''
                if ($('#sheetType').length > 0) {
                    sheetType = $('#sheetType').val()
                    if (sheetType) {
                        params += `&sheetType=${sheetType}`
                    }
                }
                var showSonItems = ''
                if (sheetType && ',PD,YK,BS,'.indexOf(',' + sheetType + ',') >= 0) {
                    //   showSonItems='&showSonItems=1'
                }
                let displayFlag='';
                if(sheetType=='CX'){
                    displayFlag='forDisplay=1&';
                }
                $("#popItem").jqxWindow('setContent', `<iframe src="/BaseInfo/ItemsView?forSelect=1&${displayFlag}operKey=${g_operKey}${showSonItems}&item_class=${3}&class_name=${'rrrr'}&${params}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);

            }
        })

        $(inputElement).on('optionSelected',
            function (a, b) {

                var value = $(inputElement).val();
                //var value = a.args.item
                var id = '';
                var gridSlt = '#jqxgrid'
                if (window.curGridSlt) gridSlt = window.curGridSlt
                var cell = $(gridSlt).jqxGrid('getselectedcell');
                $(gridSlt).jqxGrid('endcelledit', cell.row, cell.column, false);
                if (window.setRowOnItemSelected) {
                    window.setRowOnItemSelected(cell.rowindex, value)
                }

            })

    }

    function initeditor_item_name(row, cellvalue, editor, celltext, pressedkey) {

        var inputField = editor.find('input');

        if (pressedkey) {

            var container = window.parent.CefGlue
            var simulateInput = null
            if (container) simulateInput = container.simulateInput
            if (!simulateInput) {
                inputField.val(pressedkey);
                inputField.jqxInput('selectLast');
            }
            else {
                inputField.focus()
                setTimeout(() => {
                    inputField.val('')
                    simulateInput(pressedkey)
                    console.log('keycode:' + pressedkey)

                }, 30)
            }


        }
        else {
            inputField.val({ value: cellvalue, label: celltext });
            inputField[0].value = celltext || '';
            inputField.jqxInput('selectAll');

        }
    }


    function simulateKeyPress(character) {
        // 方法1
        jQuery.event.trigger({
            type: 'keypress',
            which: character.charCodeAt(0)
        })
        /*
        // 方法2
        var event = jQuery.Event( "logged" );
        event.user = "foo";
        event.pass = "bar";
        $( "body" ).trigger( event );

        // 方法3
        $( "body" ).trigger({
            type:"logged",
            user:"foo",
            pass:"bar"
        }); */
    }

    window.onresize = function () {

        var h1 = document.all.divTitle.offsetHeight;
        // var h2 = document.all.divHead.offsetHeight;// $("#divHead").height();
        var h2 = $("#divHead").height();

        var h3 = $("#divTail").height();
        var h4;
        //console.log("document.body.offsetHeight",document.body.offsetHeight)
        if (document.body.offsetHeight < 600) {
            $("#divButtons button").css('margin-top', '10px')
            $("#divButtons button").css('margin-bottom', '5px')
            h4 = document.all.divButtons.offsetHeight + 15;
        } else {
            //$("#divButtons").css('padding-bottom','10px')
            $("#divButtons button").css('margin-top', '5px')
            h4 = document.all.divButtons.offsetHeight + 5;
        }
        //console.log("document.all.divButtons.offsetHeight",h4)
        //var h5 = $(window).height();
        var h6 = window.innerHeight;
        var windowWidth = window.innerWidth;
        //var h7 = document.documentElement.clientHeight;

        var h = h6 - h1 - h2 - h3 - h4 - 5;
        if($('#jqxgrid').length > 0){
            var rowsheight = $('#jqxgrid').jqxGrid('rowsheight');
            var headerheight = $('#jqxgrid').jqxGrid('columnsheight');
            var statusbarheight = $('#jqxgrid').jqxGrid('statusbarheight');
            var max_rows = Math.floor((h - headerheight - statusbarheight) / rowsheight);
            var now_rows = $('#jqxgrid').jqxGrid('getrows') ? $('#jqxgrid').jqxGrid('getrows').length : 0;
            var rowsAreaHeight = max_rows * rowsheight
            if (document.body.offsetHeight < 600) {
                h = rowsAreaHeight + headerheight + statusbarheight + 2 - 5;
            } else {
                h = rowsAreaHeight + headerheight + statusbarheight + 2 - 20;
            }
            $("#jqxgrid").jqxGrid('verticalscrollbarlargestep', rowsAreaHeight - 20)
            $("#jqxgrid").jqxGrid(
                {
                    height: h,
                    width: windowWidth - 20
                });
            var fill_rows = max_rows - now_rows - 1;
            if (fill_rows > 0) {
                var rows = new Array(); var i;
                for (i = 0; i < fill_rows; i++) {
                    var row = {};
                    row["mum_attributes"] = null;
                    var GridData = window.GridData
                    if (GridData && GridData.columns) {
                        GridData.columns.forEach(function (col) {
                            if (col.datafield) row[col.datafield] = "";
                            if (col.displayfield) row[col.displayfield] = "";
                        })
                    }
                    rows.push(row);
                }
                $('#jqxgrid').jqxGrid('addrow', null, rows);
            }
        }


    }

    function loadSheetRows(sheetRows) {

        if (sheetRows) {
            var sheetType = $("#sheetType").val()
            var rowCount = 20;
            $('#jqxgrid').jqxGrid('clear');
            //$('#div-grid-top-left').on('click', (event) => {
            //    showCustomizeGridForm(event)
            //})
            // $('#svgLeftTop').on('click', (event) => {
            //    showCustomizeGridForm(event)
           // })
            let showProduceDate = false
            let showBatchNo = false
            let showBranchPosition = false
            let showFromBranchPosition = false
            let showToBranchPosition = false
            let showBranchID = false

            for (var i = 0, sheetRowslength = sheetRows.length; i < sheetRowslength; i++) {
                var row = sheetRows[i]
                if(!showProduceDate && row.produce_date) showProduceDate = true
                if(!showBatchNo && row.batch_no) showBatchNo = true
                if(!showBranchPosition && row.branch_position_name) showBranchPosition = true
                if(!showFromBranchPosition && row.from_branch_position_name) showFromBranchPosition = true
                if(!showToBranchPosition && row.to_branch_position_name) showToBranchPosition = true
                if(!showBranchID && row.branch_id) showBranchID = true
                if (row.orig_price && !row.discount) {
                    row.discount = (row.real_price / row.orig_price * 100).toFixed(1)
                    if (!myIsNumber(row.discount)) row.discount = ""
                }
                if (row.orig_price === 0 && row.real_price === 0) row.discount = 100
                /*
                if (window.g_companySetting.sheetShowBarcodeStyle == '2' ) {
                   row.barcode=row.s_barcode
                }
                 else if (window.g_companySetting.sheetShowBarcodeStyle == '1') {
                     if (row.unit_factor == 1)
                        row.barcode = row.s_barcode
                     else
                        row.barcode = row.b_barcode
                 }*/
            }

            rowCount = sheetRows.length + 20
            for (var i = sheetRows.length; i < rowCount; i++) {
                var row = {};
                GridData.columns.forEach(function (col) {
                    if (col.datafield) row[col.datafield] = "";
                    if (col.displayfield) row[col.displayfield] = "";
                })
                sheetRows[i] = row;
            }
            $('#jqxgrid').jqxGrid('addrow', null, sheetRows);

            $('#jqxgrid').jqxGrid('beforeRowRender', function (divRow, rowData) {
                if (!rowData || !divRow || !divRow.style) return
                if (parseFloat(rowData.quantity) < 0)
                    divRow.style.color = '#f00'
                else
                    divRow.style.color = '#000'
            })

            setTimeout(() => {
                $('#jqxgrid').jqxGrid('scrollto', 0, 0);
               // $('.iconscontainer').hide() //这个div会覆盖左上角小齿轮，导致无法点击
            }, 300)
            if(showProduceDate){
                $('#jqxgrid').jqxGrid('showcolumn', 'produce_date')
            }
            if(showBatchNo){
                $('#jqxgrid').jqxGrid('showcolumn', 'batch_no')
            }
            if (showBranchID) {
                $('#jqxgrid').jqxGrid('showcolumn', 'branch_id')
            }
            if(showFromBranchPosition){
                $('#jqxgrid').jqxGrid('showcolumn', 'from_branch_position')
            }
            if(showToBranchPosition){
                $('#jqxgrid').jqxGrid('showcolumn', 'to_branch_position')
            }if(showBranchPosition){
                $('#jqxgrid').jqxGrid('showcolumn', 'branch_position')
            }
        }
        updateSheetState();

    }
    function updateSheetState() {
        var red_flag = $('#red_flag').val();
        var sheet_id = $('#sheet_id').val();
        var sheet_type = $('#sheet_type').val();
        var sheetType = $('#sheetType').val();
        var approve_time = $('#approve_time').text();
        var review_time = $('#review_time').text();
        if (!sheet_id) {
            $('#btnDelete').css('display', 'inline-block');
            $('#btnDelete').attr('disabled', true);
            $('#btnEdit').attr('disabled', true);
        }
        if (sheet_id && approve_time == "") {
            $('#btnDelete').css('display', 'inline-block');
            $('#btnDelete').attr('disabled', false);
        }

        //var disabled = (!sheet_id) || red_flag == '1' || red_flag == '2'

        var printStatus = window.getRightValue('delicacy.sheetStatusForPrint.value')
        if (sheet_type == 'SHEET_MOVE_STORE') {
            printStatus = window.getRightValue('delicacy.moveSheetStatusForPrint.value')

        }
        if (printStatus == "true" || printStatus == "all") printStatus = "saved"
        var canPrint = (printStatus == "approved" && red_flag == "" && approve_time) ||
            (printStatus == "saved" && red_flag == "" && sheet_id) || !printStatus

        $('#btnPrint').attr('disabled', !canPrint);
        $('#choosetemplate').attr('disabled', !canPrint);

        var approve_time = $('#approve_time').text();

        var rightPrefix=''
        var sheetType = $("#sheetType").val()
        if(sheetType=='X') rightPrefix='sale.sheetSale'
        else if (sheetType == 'T') rightPrefix = 'sale.sheetReturn'
        else if (sheetType == 'XD') rightPrefix = 'sale.sheetSaleOrder'
        else if (sheetType == 'TD') rightPrefix = 'sale.sheetReturnOrder'
        else if (sheetType == 'TJ') rightPrefix = 'sale.sheetPriceAdjust'
        else if (sheetType == 'JH') rightPrefix = 'sale.sheetBorrowItem'
        else if (sheetType == 'HH') rightPrefix = 'sale.sheetBorrowItem'
        else if (sheetType == 'DH') rightPrefix = 'sale.orderItemSheet'
        else if (sheetType == 'DHTZ') rightPrefix = 'sale.orderItemAdjustSheet'
        else if (sheetType == 'CX') rightPrefix = 'sale.sheetDisplayAgreement'
        else if (sheetType == 'XSFY') rightPrefix = 'sale.sheetSaleFeeApportion'


        else if (sheetType == 'CG') rightPrefix = 'buy.sheetBuy'
        else if (sheetType == 'CT') rightPrefix = 'buy.sheetBuyReturn'
        else if (sheetType == 'CD') rightPrefix = 'buy.sheetBuyOrder'
        else if (sheetType == 'CGTJ') rightPrefix = 'buy.sheetBuyPriceAdjust'
        else if (sheetType == 'FYFT') rightPrefix = 'buy.sheetFeeApportion'

        else if (sheetType == 'DB') rightPrefix = 'stock.sheetMove'
        else if (sheetType == 'YK') rightPrefix = 'stock.sheetInventChange'
        else if (sheetType == 'BS') rightPrefix = 'stock.sheetInventReduce'
        else if (sheetType == 'PD') rightPrefix = 'stock.sheetInvent'
        else if (sheetType == 'ZZ') rightPrefix = 'stock.sheetCombine'
        else if (sheetType == 'CF') rightPrefix = 'stock.sheetSplit'
        else if (sheetType == 'RK') rightPrefix = 'stock.sheetStockIn'
        else if (sheetType == 'CK') rightPrefix = 'stock.sheetStockOut'
        else if (sheetType == 'QCKC') rightPrefix = 'stock.sheetOpeningStock'
        else if (sheetType == 'CBTJ') rightPrefix = 'stock.sheetCostPriceAdjust'

        else if (sheetType == 'ZC') rightPrefix = 'money.sheetFeeOut'
        else if (sheetType == 'SR') rightPrefix = 'money.sheetFeeIn'
        else if (sheetType == 'SK') rightPrefix = 'money.sheetGetArrears'
        else if (sheetType == 'FK') rightPrefix = 'money.sheetPayArrears'
        else if (sheetType == 'YS') rightPrefix = 'money.sheetPreget'
        else if (sheetType == 'YF') rightPrefix = 'money.sheetPrepay'
        else if (sheetType == 'DZ') rightPrefix = 'money.sheetGetArrearsOrder'
        else if (sheetType == 'TR') rightPrefix = 'money.sheetCashBankTransfer'
        else if (sheetType == 'QTF') rightPrefix = 'money.arrearsBill'
        else if (sheetType == 'QTS') rightPrefix = 'money.arrearsBill'
        else if (sheetType == 'BC') rightPrefix = 'sale.sheetPriceRebate'
        else if (sheetType == 'BT') rightPrefix = 'buy.sheetPriceAllowance'


        var canSave = window.getRightValue(rightPrefix+'.make').toLowerCase()=="true"
        var canApprove = window.getRightValue(rightPrefix+'.approve').toLowerCase()=="true"
        var canReview = window.getRightValue(rightPrefix + '.review').toLowerCase()=="true"
        var canDelete = window.getRightValue(rightPrefix + '.delete').toLowerCase()=="true"
        var canRed = window.getRightValue(rightPrefix + '.red').toLowerCase()=="true"
        var canPrint = window.getRightValue(rightPrefix + '.print').toLowerCase() == "true"
        var canExport = window.getRightValue(rightPrefix + '.export').toLowerCase() == "true"

        if (rightPrefix){
            if (!canSave) $('#btnSave').css('display', 'none');
            if (!canApprove) $('#btnApprove').css('display', 'none');
            if (!canReview) $('#btnReview').css('display', 'none');
            if (!canDelete) $('#btnDelete').css('display', 'none');
            if (!canRed) $('#btnRed').css('display', 'none');
            if (!canRed) $('#btnRedAndChange').css('display', 'none');
            if (!canExport) {
                $('#btnExport').css('display', 'none');
                $('#ImportExportContainer').css('height', '60px');
            };
        }


        $('#btnCopy').attr('disabled', !sheet_id);
        $('#btnReview').attr('disabled', true);
        console.log(approve_time)

        if (red_flag == '1') {
            $('#imgState').attr('src', '/images/reded.ico');
            $('#imgState').css('display', 'inline-block');
            $('#btnRed').css('display', 'none');
            $('#btnRedAndChange').attr('disabled', true);
            setPageReadonly(true)
        }
        else if (red_flag == '2') {
            $('#lblSheetTitle').css('color', '#f00');
            var title = $('#lblSheetTitle').text();
            $('#lblSheetTitle').text(title + '(红字单)');
            $('#btnRed').css('display', 'none');
            $('#btnRedAndChange').attr('disabled', true);
            setPageReadonly(true)
        }
        else if (approve_time != "") {
            $('#imgState').attr('src', '/images/approved.ico');
            $('#imgState').css('display', 'inline-block');

            if (canRed) {
                $('#btnRed').css('display', 'inline-block');
                 $('#btnRed').attr('disabled', false);
            }

            $('#btnApprove').attr('disabled', true);
            $('#btnRedAndChange').attr('disabled', false);
            if (review_time == "")
                $('#btnReview').attr('disabled', false);
            $('#btnSave').attr('disabled', true);
            $('#btnDelete').css('display', 'none');

            $('#isRedAndChange').val('false')
            setPageReadonly(true)
        }
        else {
            if (review_time == "")
                $('#btnReview').attr('disabled', false);
            $('#imgState').css('display', 'none');
            $('#btnRed').css('display', 'none');
            $('#btnApprove').attr('disabled', false);
            $('#btnSave').attr('disabled', false);

            $('#btnRed').attr('disabled', true);
            $('#btnRedAndChange').attr('disabled', true);
            $('#btnRed').css('display', 'none');
            if (canDelete) {
                $('#btnDelete').css('display', 'inline-block');
                $('#btnDelete').attr('disabled', sheet_id ? false : true)
            }
            if (sheetType === "XD" || sheetType === "TD") {
                var is_del_order = $('#is_del').jqxInput('val');    //查看销售订单是否已经删除
                if (is_del_order.toLowerCase() === "true") {
                    $('#btnApprove').attr('disabled', true);
                    $("#btnSave").attr('disabled', true);
                    $("#btnReview").attr('disabled', true);
                    $("#btnRedAndChange").attr('disabled', true);
                    $("#btnPrint").attr('disabled', true);
                    $("#choosetemplate").attr('disabled', true);
                    $("#btnDelete").attr('disabled', true);
                    $('#popoverMore button:contains("导入")').attr('disabled', true);
                }
            }
            setPageReadonly(false)
        }
        let sheetNo =  $('#sheet_no').val()
        if(sheetNo && sheetType == "ZWD"){
            $('#btnApprove').attr('disabled', false);
        }
        if(window.onSheetPageUpdated){
            window.onSheetPageUpdated()
        }

    };

    function addEmptyRows(needAddRowNums, rowIndex) {
        let gridSlt = "#jqxgrid"
        if (window.curGridSlt) gridSlt = window.curGridSlt
        var rows = new Array();
        for (let index = 0; index < needAddRowNums; index++) {
            rows.push({});
        }
        if (rowIndex >= 0)
            $(gridSlt).jqxGrid('addrow', null, rows, rowIndex)
        else
            $(gridSlt).jqxGrid('addrow', null, rows)

    }

    /*
    * minEmptyRows      : 检测行数
    * needAddRowNums    : 需要添加的行数
    * gridID : 单grid页面默认divID为jqxgrid，多grid页面需要传入这个参数
    */
    function addEmptyRowsAtTail(minEmptyRows, needAddRowNums, gridID = 'jqxgrid') {
        var cell = $(`#${gridID}`).jqxGrid('getselectedcell');
        if (cell) {
            var total_rows = $(`#${gridID}`).jqxGrid('getrows').length;
            if (cell.row >= total_rows - minEmptyRows) {
                var rows = new Array();
                for (let index = 0; index < needAddRowNums; index++) {
                    rows.push({});
                }
                $(`#${gridID}`).jqxGrid('addrow', null, rows);
                $(`#${gridID}`).jqxGrid('selectcell', cell.row, cell.datafield)

            }
        }
    }

    function copyToSheets(e) {
        var arr = [];
        var checkeds = $('input[name="copyToSheets"]:checked')

        for (var i = 0; i < checkeds.length; i++) {
            var checked = checkeds[i]
            var row = {}
            row.id = checkeds[i].id;
            row.url = checkeds[i].value;
            row.title = document.querySelector(`label[for="${row.id}"]`).innerHTML;
            arr.push(row)
        }
        return arr;
    }
    function copyToSheet(a) {

        var sheet = {}
        sheet.id = a.id;
        sheet.url = a.getAttribute('data-url');
        sheet.title = a.innerHTML;

        return sheet;
    }
    function btnCopySheet_click() {
        $('#lblSheetTitle').css('color', '#000')
        let title = $('#lblSheetTitle').text();
        let newTitleArr = title.split('(')
        if (newTitleArr.length >= 0) {
            $('#lblSheetTitle').text(newTitleArr[0])
        }
        $('#sheet_id').val('')
         $('#sheet_no').val('')

        $('#review_time').text('')
        $('#reviewer_name').text('')

        var red_flag = $('#red_flag').val()
        var sheetType = $("#sheetType").val()
        if (!red_flag) {

            $('#order_sheet_id').val('')
            $('#order_sheet_id').text('')
            $('#order_sheet_no').val('')
            $('#order_sheet_no').text('')
            $('#sheet_no').val('')
            $('#sheet_no').text('')
        }
        if (sheetType == "XD" || sheetType == "TD") {   //销售订单假删除（复制单据时候要将删除状态设为false）
            $('#placeholder_sheet_id').val('')
            $('#placeholder_sheet_id').text('')
             $('#placeholder_sheet_no').val('')
            $('#placeholder_sheet_no').text('')
            $('#is_del').val('false')
            var a = $('#is_del').val()
        }
        if(sheetType=="CG"){
            let feeSheetInfo = $('#feeSheetInfo').val() ? JSON.parse($('#feeSheetInfo').val()) : '';
            if (feeSheetInfo && Object.keys(feeSheetInfo).length > 0) {
                feeSheetInfo.fee_apportion_sheet_id = '';
                feeSheetInfo.fee_apportion_sheet_no = '';
                for (let i = 0; i < feeSheetInfo.sheetRowFeeAppoList.length;i++){
                    feeSheetInfo.sheetRowFeeAppoList[i].fee_sheet_id = '';
                    feeSheetInfo.sheetRowFeeAppoList[i].fee_sheet_no = '';
                }
                $('#feeSheetInfo').val(JSON.stringify(feeSheetInfo));
            }
        }
        if (sheetType == "X") {
            let saleFeeSheetInfo = $('#saleFeeSheetInfo').val() ? JSON.parse($('#saleFeeSheetInfo').val()) : '';
            if (saleFeeSheetInfo && Object.keys(saleFeeSheetInfo).length > 0) {
                saleFeeSheetInfo.sale_fee_apportion_sheet_id = '';
                saleFeeSheetInfo.sale_fee_apportion_sheet_no = '';
                for (let i = 0; i < saleFeeSheetInfo.sheetRowSaleFeeAppoList.length; i++) {
                    saleFeeSheetInfo.sheetRowSaleFeeAppoList[i].fee_sheet_id = '';
                    saleFeeSheetInfo.sheetRowSaleFeeAppoList[i].fee_sheet_no = '';
                }
                $('#saleFeeSheetInfo').val(JSON.stringify(saleFeeSheetInfo));
            }
        }
        if (red_flag != 1 && sheetType != "QCKC") { $('#happen_time').jqxDateTimeInput('val', ''); }

        $('#approve_time').val('')
        $('#approve_time').text('')
        $('#approver_id').val('')
        $('#approver_id').text('')
        $('#approver_name').val('')
        $('#approver_name').text('')
        $('#make_time').val('')
        $('#make_time').text('')
        $('#maker_name').val('')
        $('#maker_name').text('')
        $('#red_sheet_id').text('')
        $('#red_sheet_id').val('')
        var make_brief = $('#make_brief').jqxInput('val')
        var arr = make_brief.split('红冲原因')
        if (arr.length >= 0) make_brief = arr[0]
        console.log(make_brief)
        $('#make_brief').jqxInput('val', make_brief)
        // 不清除的话复制单据不能同步
        $('#order_source').val('')
        $('#red_flag').val('')
        if (window.onCopySheet) {
            window.onCopySheet()
        }

        try {
            $('#payway1_id').jqxInput({ disabled: false })
            $('#payway1_amount').jqxInput({ disabled: false })
            window.g_init_payway1_amount = parseFloat($('#payway1_amount').jqxInput('val'))
            window.g_init_left_amount = parseFloat($('#left_amount').jqxInput('val'))
        }
        catch (e) { }

        clearInvalidProduceDate()

        updateSheetState()
    }
    function clearInvalidProduceDate() {
        var rows = $('#jqxgrid').jqxGrid('getrows');
        for (var i = 0; i < rows.length; i++) {
            var row = rows[i]
            var bHaveInvalidBatchlevel = !row.batch_level || row.batch_level === "0"
            var bHaveValidProduceDate = row.produce_date && row.produce_date != '无产期'
            if (bHaveInvalidBatchlevel && (row.batch_no || bHaveValidProduceDate)) {
                row.batch_no = ""
                row.batch_id = "0"
                row.produce_date = ""
            }
        }
    }
    function btnCopySheetHead_click() {
        btnCopySheet_click()
        $('#jqxgrid').jqxGrid('clear')
        addEmptyRows(20)

        $('#total_amount').jqxInput('val', '0');
        $('#now_disc_amount').jqxInput('val', '');
        $('#no_disc_amount').jqxInput('val', '0');

        $('#payway1_id').jqxInput('val', { value: '', label:  '' });
        $('#payway1_amount').jqxInput('val', '');

        $('#payway2_id').jqxInput('val', { value: '', label:  '' });
        $('#payway2_amount').jqxInput('val', '');

        $('#payway3_id').jqxInput('val', { value: '', label: '' });
        $('#payway3_amount').jqxInput('val', '');
        $('#left_amount').jqxInput('val','0');
        window.g_init_payway1_amount = parseFloat($('#payway1_amount').jqxInput('val'))
        window.g_init_left_amount = parseFloat($('#left_amount').jqxInput('val'))
    }
    function btnNewSheet_click() {
        resetFormData()
        $('#jqxgrid').jqxGrid('clear')
        addEmptyRows(20)
    }

    function requestString(name) {

        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");

        var r = location.search.substr(1).match(reg);

        return r ? decodeURI(r[2]) : null;
    }


    var unitRights = @Html.Raw(Model.JsonOperRights);
    $(function () {
        if (unitRights) {
            Object.keys(unitRights).forEach(key => {
                if (!unitRights[key]) {
                    var id = '#btn' + key.replace(key[0], key[0].toUpperCase());
                    $(id).hide();
                }
            });
        }

    })
    window.g_companySetting = @Html.Raw(Model.JsonCompanySetting);
    window.g_operRights = @Html.Raw(Model.JsonOperRightsOrig);
    window.g_optionsRemembered = @Html.Raw(Model.JsonOptionsRemembered);
    window.CoolieServerUri = '@Html.Raw(Model.CoolieServerUri)';
    function getQueriedItemsKeyFromRow(sheetRow) {
        let key = ""
        if (sheetRow.order_sub_id)
            key = sheetRow.item_id + '_' + sheetRow.order_sub_id + '_' + toMoney(sheetRow.order_price, 4)
        else {
            //let rowBranchId = sheetRow.branch_id?sheetRow.branch_id:($("#branch_id").val()?$("#branch_id").val().value:"-1")
            //let rowBranchPosition = sheetRow.branch_position?sheetRow.branch_position:"0"
            //let rowBatchId = sheetRow.batch_id?sheetRow.batch_id:"0"
            //key = sheetRow.item_id + rowBranchId + rowBranchPosition + rowBatchId
            key = sheetRow.item_id
        }
        if(!window.curGridSlt) window.curGridSlt = '#jqxgrid'
        if(window.curGridSlt!='#jqxgrid')
           key+= window.curGridSlt
        return key

    }
    function getRowUnitRelation(row) {
        var b_unit_no = '', m_unit_no = '', s_unit_no = '', b_unit_factor = '', m_unit_factor = '', s_unit_factor = ''
        var key = getQueriedItemsKeyFromRow(row)
        //if (row.sheet_type !== "SHEET_COMBINE_ITEMS" && row.sheet_type !== "SHEET_SPLIT_ITEMS") {
            var item = window.g_queriedItems[key]
       // } else {
        //    var item = window.g_queriedItems[key + window.curGridSlt]
        //}
        if (item) {
            item.units.forEach(function (unit) {
                if (unit.unit_type == 'b') {
                    b_unit_no = unit.unit_no
                    b_unit_factor = unit.unit_factor
                }
                else if (unit.unit_type == 'm') {
                    m_unit_no = unit.unit_no
                    m_unit_factor = unit.unit_factor
                }
                else if (unit.unit_type == 's') {
                    s_unit_no = unit.unit_no
                    s_unit_factor = unit.unit_factor
                }
            })
        }

        if (b_unit_no) {
            if (m_unit_no) {
                var bm = parseInt(b_unit_factor / m_unit_factor)
                return `1${b_unit_no}=${bm}${m_unit_no}=${b_unit_factor}${s_unit_no}`
            }
            else {
                return `1${b_unit_no}=${b_unit_factor}${s_unit_no}`
            }
        }
    }


    function funcSortByColumn(column, direction, sortColumns) {
        let gridSlt = "#jqxgrid"
        if (window.curGridSlt) gridSlt = window.curGridSlt
        var sheetRows = $(gridSlt).jqxGrid('getboundrows')
        var source = $(gridSlt).jqxGrid('getsource')
        var flag = 1
        if (direction === 'descending') flag = -1
        sheetRows.sort((a, b) => {
            var n1 = a.item_name
            var n2 = b.item_name
            var n1Empty = n1 == undefined || n1 == ''
            var n2Empty = n2 == undefined || n2 == ''

            if (n1Empty && !n2Empty) {
                return 1
            }
            else if (!n1Empty && n2Empty) {
                return -1
            }
            else if (n1Empty && n2Empty) {
                return 0
            }

            n1 = a[column]
            n2 = b[column]
            if (!isNaN(n1) && !isNaN(n2)) {
                if (n1 > n2) return flag;
                else if (n1 < n2) return -flag;
                else return 0;
            }
            n1 = n1.toString()
            n2 = n2.toString()
            var h1 = n1.substr(0, 1)
            var h2 = n2.substr(0, 1)

            var reg = /^[a-zA-Z]+$/
            var eng1 = reg.test(h1)
            var eng2 = reg.test(h2)

            reg = /^[0-9]+$/
            var num1 = reg.test(h1)
            var num2 = reg.test(h2)
            var f1 = 0
            if (!eng1 && !num1) f1 = 2
            else if (eng1) f1 = 1


            var f2 = 0
            if (!eng2 && !num2) f2 = 2
            else if (eng2) f2 = 1
            if (eng2) f2 = 1

            var c = 0

            if (f1 != f2) {
                c = f1 - f2
            }
            else {
                c = n1.localeCompare(n2, 'zh-cn')
            }
            c *= flag
            return c
        })
        source._source.localdata = sheetRows;// $("#jqxgrid").jqxGrid('getrows')
        $(gridSlt).jqxGrid('updatebounddata')
        return true;
    }

    function getNextCellByEnterKey(rowIndex,colName) {
        let gridSlt = "grid"
        if(window.curGridSlt=="#jqxgridB") gridSlt = "gridB"
        var setColumns =  window.g_pageSetting ? window.g_pageSetting[gridSlt] :null
        console.log(setColumns)

        var bMet = false
        var nextColumn = ''

        var firstStopCol = ''
        if (setColumns) {
           setColumns.some(c => {
            if (c.enterstop && !c.hidden) {
                if(!firstStopCol) firstStopCol=c.datafield
            }
          })
        }

        if (!firstStopCol) {
            var defaultColumns = [
                { datafield: 'item_id', enterstop: true },
                { datafield: 'virtual_produce_date', enterstop: true },
                { datafield: 'unit_no', enterstop: true },
                { datafield: 'quantity', enterstop: true },
                { datafield: 'real_price', enterstop: true },
                { datafield: 'sub_amount', enterstop: true },
                { datafield: 'fee_sub_id', enterstop: true },
            ]
            var newColumns=[]
            defaultColumns.forEach(c => {
               var setCol = setColumns.find(setCol=>setCol.datafield==c.datafield && !setCol.hidden)
                if (setCol) {
                    newColumns.push(c)
                }
            })

            setColumns=newColumns
            firstStopCol = setColumns[0].datafield
        }

        setColumns.some(c => {
            if (bMet && c.enterstop) {
                nextColumn = c.datafield
                return true
            }
            else if (c.datafield == colName) {
                bMet=true
            }
        })

        if (!nextColumn) {
            nextColumn = firstStopCol
            rowIndex++
        }
        return { datafield: nextColumn, row: rowIndex }

    }
    function setBatch(rowindexs,jqxGridFlag,positionFlag) {
        let curGrid = '#jqxgrid'
        if (jqxGridFlag == "to") {
            curGrid = '#jqxgridB'
        }
        var rowsData = $(curGrid).jqxGrid('getrows')
        let sheetType =$("#sheetType").val()
        let branchID ="-1"
        if(sheetType =="DB"){
            if(positionFlag=="to"){
                branchID = $("#to_branch_id").val().value
            }else{
                branchID = $("#from_branch_id").val().value
            }

        }else if(sheetType =="ZZ"|| sheetType =="CF"){
            if(jqxGridFlag=="to"){
                branchID = $("#from_branch_id").val().value
            }else{
                branchID = $("#to_branch_id").val().value
            }

        }else{
            branchID = $("#branch_id").val().value ? $("#branch_id").val().value : "-1"
        }
        rowindexs.forEach(rowIndex=>{
            let row = rowsData[rowIndex]
            let rowBranchID = row.branch_id ? row.branch_id : branchID
            let rowBranchPosition = "0"
            if(sheetType !="DB"){
                rowBranchPosition = row.branch_position?row.branch_position:"0"
            }else{
                if(positionFlag=="to"){
                    rowBranchPosition = row.to_branch_position ? row.to_branch_position : "0"
                }else{
                    rowBranchPosition = row.from_branch_position?row.from_branch_position:"0"
                }
            }
            let key = row.item_id +"_" + rowBranchID + "_" + rowBranchPosition
            if(key){
                if(sheetType =="X" || sheetType =="XD" || sheetType =="JH" || sheetType =="HH" || (sheetType =="DB" && positionFlag!="to")){
                    let batchStockForShow = window.itemsBatchStockForShow && window.itemsBatchStockForShow[key]?JSON.parse(JSON.stringify(window.itemsBatchStockForShow[key])):[]
                    batchStockForShow = batchStockForShow.filter(e=>e.batch_id !=="0")
                    batchStockForShow.sort((pre, next) => {
                        return Date.parse(pre.produce_date) - Date.parse(next.produce_date)
                    })
                    batchStockForShow.sort((pre, next) => {
                        if (pre.batch_no > next.batch_no) return 1
                    })
                    if ((batchStockForShow.length === 0 || (batchStockForShow.length !== 0 && sheetType === "JH")) &&  window.getSettingValue('showNoProduceDate').toLowerCase() === 'true') {
                        row.produce_date = "无产期"
                    } else {
                        row.produce_date = batchStockForShow.length ? batchStockForShow[0].produce_date : ''
                    }
                    // row.produce_date = batchStockForShow.length ? batchStockForShow[0].produce_date : ''
                    row.batch_no = batchStockForShow.length ? batchStockForShow[0].batch_no : ''
                    row.batch_id = batchStockForShow.length ? batchStockForShow[0].batch_id : '0'
                }
                setStockQty(key,rowIndex, row.produce_date,row.batch_no,jqxGridFlag,positionFlag)

            }
        })
    }
    function setStockQty(key,rowIndex,produce_date,batch_no,jqxGridFlag,positionFlag){
        //
        let sheetType = $("#sheetType").val()
        console.log(sheetType)
        let curGrid = '#jqxgrid'
        if (jqxGridFlag == "to") {
            curGrid = '#jqxgridB'
        }
        var rowsData = $(curGrid).jqxGrid('getrows')
        batch_no = batch_no?batch_no:""
        produce_date = produce_date?produce_date:""
        let batchStockToTal = window.itemsBatchStockTotal && window.itemsBatchStockTotal[key]?JSON.parse(JSON.stringify(window.itemsBatchStockTotal[key])):[]
        let flag = batchStockToTal.some(e=>{
            if (e.batch_no == batch_no && (e.produce_date === produce_date ||
                (["", "无产期"].includes(e.produce_date) && ["", "无产期"].includes(produce_date)))) {
                    // 添加这个条件因为"无产期" 和 "" 等价，不添加的话有些情况会导致产期选择后清空调拨单出入仓库存
                if(sheetType == "DB"){
                    if(positionFlag=="to"){
                        rowsData[rowIndex].to_branch_qty_unit = e.stock_qty_unit
                    }else{
                        rowsData[rowIndex].from_branch_qty_unit = e.stock_qty_unit
                    }
                }else if (sheetType == "PD") {
                    rowsData[rowIndex].current_qty = e.stock_qty_unit
                    rowsData[rowIndex].sys_quantity = e.stock_qty
                    rowsData[rowIndex].stock_qty = e.stock_qty
                }else {
                    rowsData[rowIndex].stock_qty_unit = e.stock_qty_unit
                    rowsData[rowIndex].usable_stock_qty_unit = e.usable_stock_qty_unit
                    if("X,XT,XD,ZZ,CF".indexOf(sheetType)>-1){
                        rowsData[rowIndex].sell_pend_qty_unit = e.sell_pend_qty_unit
                    }
                }
                return true
            }
        })
        if(!flag){
            if (sheetType == "PD"){
                rowsData[rowIndex].current_qty = ""
                rowsData[rowIndex].sys_quantity ="0"
                rowsData[rowIndex].stock_qty ="0"
            }else if(sheetType == "DB"){
                if(positionFlag=="to"){
                    rowsData[rowIndex].to_branch_qty_unit = ""
                }else{
                    rowsData[rowIndex].from_branch_qty_unit = ""
                }
            }
            else
            {
                rowsData[rowIndex].stock_qty_unit = "0"
                rowsData[rowIndex].usable_stock_qty_unit = "0"
                if("X,XT,XD,ZZ,CF".indexOf(sheetType)>-1)
                {
                    rowsData[rowIndex].sell_pend_qty_unit = ""
                    // if(rowsData[rowIndex].stock_qty_unit=="")
                    // rowsData[rowIndex].stock_qty_unit = "0"
                    // if (rowsData[rowIndex].usable_stock_qty_unit == "")
                    //     rowsData[rowIndex].usable_stock_qty_unit = "0"
                }
            }
        }
        $(curGrid).jqxGrid('updategrid')
        window.g_queryStatus='done'
    }

    function GetBranchPosition(rowindexs, branch_id, jqxGridFlag = "from",positionFlag) {
        let curGrid = '#jqxgrid'
        if (jqxGridFlag == "to") {
            curGrid = '#jqxgridB'
        }
        let sheetType = $('#sheetType').val()
        let branchPositionType =  null
        if(sheetType == "T" || sheetType =="TD"){
            branchPositionType = window.g_companySetting.backBranchPositionType
        }else{
            branchPositionType = window.g_companySetting.defaultBranchPositionType
        }
        let isShowNoStock = false
        if (window.g_companySetting && window.g_companySetting.showNegativeStock && window.g_companySetting.showNegativeStock == "True") {
            isShowNoStock = true
        }

        let rowsData = $(curGrid).jqxGrid('getrows')
        let items_id = ""
        if(!branchPositionType){//未设置默认库位类型
            rowindexs.forEach(i => {
                if(sheetType == "DB"){
                    if(positionFlag=="to"){
                        rowsData[i].to_branch_position = "0"
                        rowsData[i].to_branch_position_name = ""
                    }
                    else{
                        rowsData[i].from_branch_position = "0"
                        rowsData[i].from_branch_position_name = ""
                    }
                }
                else
                {
                    rowsData[i].branch_position = "0"
                    rowsData[i].branch_position_name =  ""
                }
                // let key = rowsData[i].item_id + branch_id + "0"
                let key = `${rowsData[i].item_id}_${branch_id}_0`
                if(!window.itemsBatchStockTotal || !window.itemsBatchStockTotal[key]){
                    if(items_id) items_id +=","
                    items_id += rowsData[i].item_id
                }
            })

            if(items_id=="") {
                setBatch(rowindexs, jqxGridFlag,positionFlag)
                return
            }

            $.ajax({
                url: '/api/SaleSheet/GetBatchStock',
                type: 'GET',
                contentType: 'application/json',
                data: {
                    operKey: g_operKey,
                    items_id,
                    branch_id: branch_id,
                    branch_position: "0",
                    isShowNegativeStock:isShowNoStock
                },
                success: function (data) {
                    if (data.result == "OK") {
                        if(!window.itemsBatchStockTotal) window.itemsBatchStockTotal = {}
                        if(!window.itemsBatchStockForShow) window.itemsBatchStockForShow = {}
                        rowindexs.forEach(i => {
                            // let key = rowsData[i].item_id + branch_id +"0"
                            let key = `${rowsData[i].item_id}_${branch_id}_0`
                            window.itemsBatchStockTotal[key] = data.batchStockTotal[key]?data.batchStockTotal[key]:[]
                            window.itemsBatchStockForShow[[key]] = data.batchStockForShow[key]?data.batchStockForShow[key]:[]
                        })
                        setBatch(rowindexs, jqxGridFlag,positionFlag)
                    }
                }
            })
        }
        else{
            rowindexs.forEach(i => {
                if(window.g_dicDefualtBranchPosition && window.g_dicDefualtBranchPosition[branch_id]){
                    // let key = rowsData[i].item_id + branch_id + window.g_dicDefualtBranchPosition[branch_id]
                    let key = `${rowsData[i].item_id}_${branch_id}_${window.g_dicDefualtBranchPosition[branch_id]}`
                    if(!window.itemsBatchStockTotal || !window.itemsBatchStockTotal[key]){
                        if(items_id) items_id +=","
                        items_id += rowsData[i].item_id
                    }
                }else{
                    if(items_id) items_id +=","
                    items_id += rowsData[i].item_id
                }
            })
            if (items_id == "") {
                setBatch(rowindexs, jqxGridFlag, positionFlag)
                return
            }
            $.ajax({
                url: '/api/SaleSheet/GetBranchPosition',
                type: 'GET',
                contentType: 'application/json',
                data: {
                    operKey: g_operKey,
                    items_id:items_id,
                    branch_id: branch_id,
                    branchPositionType: branchPositionType,
                    isShowNegativeStock:isShowNoStock
                },
                success: function (data) {
                    if (data.result == "OK") {
                        let position = data.branchPosition
                        rowindexs.forEach(i => {
                            if(!window.g_dicDefualtBranchPosition) window.g_dicDefualtBranchPosition = {}
                            if(position && window.g_dicDefualtBranchPosition && !window.g_dicDefualtBranchPosition[branch_id]) window.g_dicDefualtBranchPosition[branch_id] = position
                            if(sheetType == "DB"){
                                if(positionFlag=="to"){
                                    rowsData[i].to_branch_position = position&&position.branch_position?position.branch_position:"0"
                                    rowsData[i].to_branch_position_name = position && position.branch_position_name ? position.branch_position_name : ""
                                }
                                else{
                                    rowsData[i].from_branch_position = position&&position.branch_position?position.branch_position:"0"
                                    rowsData[i].from_branch_position_name = position && position.branch_position_name ? position.branch_position_name : ""
                                }
                            }else{
                                rowsData[i].branch_position = position&&position.branch_position?position.branch_position:"0"
                                rowsData[i].branch_position_name =  position&&position.branch_position_name?position.branch_position_name:""
                            }
                        })
                        if (!window.itemsBatchStockTotal) window.itemsBatchStockTotal = {}
                        if (!window.itemsBatchStockForShow) window.itemsBatchStockForShow = {}
                        rowindexs.forEach(i => {
                            let bp = position&&position.branch_position?position.branch_position:"0"
                            let key = `${rowsData[i].item_id}_${branch_id}_${bp}`
                            window.itemsBatchStockTotal[key] = data.batchStockTotal[key] ? data.batchStockTotal[key] : []
                            window.itemsBatchStockForShow[[key]] = data.batchStockForShow[key] ? data.batchStockForShow[key] : []
                        })
                        setBatch(rowindexs,jqxGridFlag,positionFlag)
                    }
                }
            })
        }
    }
    function GetBatchStock(rowindexs, branch_position,jqxGridFlag = "from",positionFlag){
        let curGrid = '#jqxgrid'
        if (jqxGridFlag == "to") {
            curGrid = '#jqxgridB'
        }
        let branch_id ="-1"
        let sheetType = $('#sheetType').val()
        if(sheetType == "ZZ" || sheetType=="CF"){
            if(curGrid == '#jqxgrid'){
                branch_id =$("#from_branch_id").val().value
            }
            else{
                branch_id =$("#to_branch_id").val().value
            }

        }else if(sheetType == "DB"){
            if(positionFlag=="to"){
                branch_id =$("#to_branch_id").val().value
            }else{
                branch_id =$("#from_branch_id").val().value
            }
        }
        else{
            branch_id =$("#branch_id").val().value
            if(rowindexs.length == 1) {
                let rowBranchId = $(curGrid).jqxGrid('getcellvalue', rowindexs[0], 'branch_id')
                branch_id = rowBranchId?rowBranchId:branch_id
            }
        }
        var rowsData = $(curGrid).jqxGrid('getrows')
        let items_id = ""
        rowindexs.forEach(i => {
            // let key = rowsData[i].item_id + branch_id + branch_position
            let key = `${rowsData[i].item_id}_${branch_id}_${branch_position}`
            if(!window.itemsBatchStockTotal || !window.itemsBatchStockTotal[key]){
                if(items_id) items_id +=","
                items_id += rowsData[i].item_id
            }
        })
        if(items_id=="") {
            setBatch(rowindexs, jqxGridFlag,positionFlag)
            return
        }

        let isShowNoStock = false
        if (window.g_companySetting && window.g_companySetting.showNegativeStock && window.g_companySetting.showNegativeStock == "True") {
            isShowNoStock = true
        }
        branch_position = branch_position?branch_position:"0"
        $.ajax({
                url: '/api/SaleSheet/GetBatchStock',
                type: 'GET',
                contentType: 'application/json',
                data: {
                    operKey: g_operKey,
                    items_id,
                    branch_id: branch_id,
                    branch_position:branch_position,
                    isShowNegativeStock:isShowNoStock
                },
                success: function (data) {
                    if (data.result == "OK") {
                        if (!window.itemsBatchStockTotal) window.itemsBatchStockTotal = {}
                        if (!window.itemsBatchStockForShow) window.itemsBatchStockForShow = {}
                        rowindexs.forEach(i => {
                            // let key = rowsData[i].item_id + branch_id + branch_position
                            let key = `${rowsData[i].item_id}_${branch_id}_${branch_position}`
                        window.itemsBatchStockTotal[key] = data.batchStockTotal[key] ? data.batchStockTotal[key] : []
                            window.itemsBatchStockForShow[[key]] = data.batchStockForShow[key] ? data.batchStockForShow[key] : []
                        })
                        setBatch(rowindexs, jqxGridFlag)
                    }
                }
        })
    }

    document.addEventListener('keydown', function(event) {
        if (event.ctrlKey) {
            switch (event.key) {
                case 's':
                    // 阻止默认的保存行为


                    event.preventDefault();
                    console.log('Ctrl + S 被按下');
                    // 在这里添加保存的逻辑
                    var button = document.getElementById('btnSave');
                    button.click();
                    break;
                case 'p':
                    // 阻止默认的打印行为

                    event.preventDefault();
                    console.log('Ctrl + P 被按下');
                    // 在这里添加打印的逻辑
                    var button = document.getElementById('btnPrint');
                    button.click();
                    break;
            }
        }
    });

    function getNextCellByEnterKey(rowIndex, colName) {
        let gridSlt = "grid"
        if (window.curGridSlt == "#jqxgridB") gridSlt = "gridB"
        var setColumns = window.g_pageSetting ? window.g_pageSetting[gridSlt] : null
        console.log(setColumns)

        var bMet = false
        var nextColumn = ''

        var firstStopCol = ''
        if (setColumns) {
            setColumns.some(c => {
                if (c.enterstop && !c.hidden) {
                    if (!firstStopCol) firstStopCol = c.datafield
                }
            })
        }

        if (!firstStopCol) {
            var defaultColumns = [
                { datafield: 'item_id', enterstop: true },
                { datafield: 'virtual_produce_date', enterstop: true },
                { datafield: 'unit_no', enterstop: true },
                { datafield: 'quantity', enterstop: true },
                { datafield: 'real_price', enterstop: true },
                { datafield: 'sub_amount', enterstop: true },
                { datafield: 'fee_sub_id', enterstop: true },
            ]
            var newColumns = []
            defaultColumns.forEach(c => {
                var setCol = setColumns.find(setCol => setCol.datafield == c.datafield && !setCol.hidden)
                if (setCol) {
                    newColumns.push(c)
                }
            })

            setColumns = newColumns
            firstStopCol = setColumns[0].datafield
        }

        setColumns.some(c => {
            if (bMet && c.enterstop) {
                nextColumn = c.datafield
                return true
            }
            else if (c.datafield == colName) {
                bMet = true
            }
        })

        if (!nextColumn) {
            nextColumn = firstStopCol
            rowIndex++
        }
        return { datafield: nextColumn, row: rowIndex }

    }
</script>
