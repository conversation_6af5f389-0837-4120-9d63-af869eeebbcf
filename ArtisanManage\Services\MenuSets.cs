using ArtisanManage.Pages;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using static ArtisanManage.Services.EnumExtAttribute;

namespace ArtisanManage.Services
{

    public class Rights : Dictionary<MenuId, UnitRights>
    {
        public static Rights FromJson(JObject json)
        {
            var rights = new Rights();
            IEnumerable<JProperty> properties = json.Properties();
            foreach (JProperty k in properties)
            {
                string key = (string)k.Name;
                try
                {
                MenuId enumKey = (MenuId)Enum.Parse(typeof(MenuId), key);                                    
                JObject value = (JObject)json[key];
                UnitRights unitRights = UnitRights.FromJson(value);
                rights.Add(enumKey, unitRights);                    
                }
                catch (Exception) { }
            }
            return rights;
        }

        public static Rights Create(out Dictionary<MenuId, Store> stores)
        {
            var type = typeof(MenuId);
            MenuId[] ids = (MenuId[])type.GetEnumValues();
            stores = new Dictionary<MenuId, Store>();
            var rights = new Rights();

            foreach (MenuId id in ids)
            {
                if ((int)id < 20 && (int)id > 0)
                {
                    var attr = type.GetField(id.ToString()).GetCustomAttributes(false)?[0];
                    if (attr is EnumExtAttribute attribute)
                    {
                        var ts = attribute.BaseRights.Select(X =>
                        {
                            var att = X.GetType().GetField(X.ToString()).GetCustomAttributes(false);
                            if (att[0] is EnumExtAttribute a)
                            {
                                return a.Display;
                            }
                            return id.ToString();
                        });
                        rights.Add(id, new UnitRights());
                        Store store = new Store
                        {
                            display = attribute.Display,
                            titles = ts.ToList(),
                            subRightsDisplay = new Dictionary<MenuId, string>(),
                            subRightsTips = new Dictionary<MenuId, string>()
                        };
                        stores.Add(id, store);
                    }
                }
                else if ((int)id > 99)
                {
                    try {  
                        var attr = type.GetField(id.ToString()).GetCustomAttributes(false)?[0];
                        if (attr is EnumExtAttribute attribute)
                        {
                            string defaultValue = attribute.DefaultValue;
                            if (attribute.BaseRightType == BASE_RIGHT_TYPE.CHECK)
                            {
                                if (defaultValue == "") defaultValue = "false";
                            }
                            var baseRights = BaseRights.Create(attribute.BaseRights, defaultValue);
                            baseRights.BaseRightType = attribute.BaseRightType.ToString();

                            if (attribute.OptionsJson != "")
                                baseRights.OptionsJson = JsonConvert.DeserializeObject<Dictionary<string, string>>(attribute.OptionsJson);
                            rights[attribute.Group].Add(id, baseRights);
                            stores[attribute.Group].subRightsDisplay.Add(id, attribute.Display);
                            stores[attribute.Group].subRightsTips.Add(id, attribute.Tip);

                            stores[attribute.Group].subRightsOptionJson.Add(id, baseRights.OptionsJson);
                            stores[attribute.Group].subRightsBaseRightType.Add(id, baseRights.BaseRightType.ToString());

                        }
                    }
                    catch(Exception ee)
					{

					}
                }
            }
            return rights;
        }

        public List<Menu> FilterMenus_old()
        {
            if (Count == 0) return null;
            var file = System.IO.File.ReadAllTextAsync(Environment.CurrentDirectory + "/JsonFiles/menu.json", Encoding.UTF8).Result;
            var menus = JsonConvert.DeserializeObject<List<Menu>>(file);
            foreach (var unitRights in this)
            {
                if (unitRights.Key == MenuId.delicacy) continue;
                foreach (var unitRight in unitRights.Value)
                {
                    menus.RemoveAll(menu => {//menu中是我们要的导航栏内容
                        foreach (var group in menu.SubNodes)
                        {
                            group.Value.RemoveAll(x =>
                            {
                                try
                                {
                                    dynamic test = unitRight.Value[MenuId.see];
                                }
                                catch (Exception e)
                                {
                                    Console.WriteLine(e);
                                }
                                if (x.Id != unitRight.Key) return false;
                                if (!unitRight.Value.ContainsKey(MenuId.see)) return false;

                                if (unitRight.Value[MenuId.see].ToLower() == "true")
                                {
                                    x.UnitRight = unitRight;
                                    if (x.Id == MenuId.salesDetaill)
                                    {

                                    }
                                    return false;
                                }
                                return true;
                            });
                            if (group.Value.Count == 0) menu.SubNodes.Remove(group.Key);
                        }
                        return menu.SubNodes.Count == 0;
                    });

                }
            }
            return menus;
        }

        public List<Menu> FilterMenus()
        {
            if (Count == 0) return null;
            var file = System.IO.File.ReadAllTextAsync(Environment.CurrentDirectory + "/JsonFiles/menu.json", Encoding.UTF8).Result;
            var menus = JsonConvert.DeserializeObject<List<Menu>>(file);

            menus.RemoveAll(menu => {//menu中是我们要的导航栏内容
                foreach (var group in menu.SubNodes)
                {
                    group.Value.RemoveAll(x =>
                    {
                         
                        foreach (var unitRights in this)
                        {
                            if (unitRights.Key == MenuId.delicacy) continue;
                            foreach (var unitRight in unitRights.Value)
                            { 
                                //dynamic test = unitRight.Value[MenuId.see];

                                //if (x.Id != unitRight.Key)  return false;
                                if (x.Id == unitRight.Key)
                                {
                                    if (!unitRight.Value.ContainsKey(MenuId.see))
                                        return false; 
                                    if (unitRight.Value[MenuId.see].ToLower() == "true")
                                    {
                                        x.UnitRight = unitRight;
                                        return false;
                                    }
                                }
                                    
                               
                            }
                        }
                        return true;

                    });
                    if (group.Value.Count == 0) menu.SubNodes.Remove(group.Key);
                }
                return menu.SubNodes.Count == 0;
            });


            
            return menus;
        }
    }

    public class UnitRights : Dictionary<MenuId, BaseRights>
    {
        public static UnitRights FromJson(JObject json)
        {
            var unitRights = new UnitRights();
            IEnumerable<JProperty> properties = json.Properties();
            foreach (JProperty k in properties)
            {
                string key = k.Name;
                try
                {
                    MenuId enumKey = (MenuId)Enum.Parse(typeof(MenuId), key);
                    JObject value = (JObject)k.Value;
                    BaseRights baseRight = BaseRights.FromJson(value);
                    unitRights.Add(enumKey, baseRight);
                }
                catch (Exception) { }
            }
            return unitRights;
        }
    }
    public class Store
    {
        public string display { get; set; }
        public List<string> titles { get; set; }
        public Dictionary<MenuId, string> subRightsDisplay { get; set; }
        public Dictionary<MenuId, string> subRightsTips { get; set; }
        public Dictionary<MenuId, Dictionary<string, string>> subRightsOptionJson = new Dictionary<MenuId, Dictionary<string, string>>();
        public Dictionary<MenuId, string> subRightsBaseRightType = new Dictionary<MenuId, string>();
    }

    public class BaseRights : Dictionary<MenuId, string>
    {
        public static BaseRights FromJson(JObject json)
        {
            var baseRight = new BaseRights();
            IEnumerable<JProperty> properties = json.Properties();
            foreach (JProperty k in properties)
            {
                string keyName = (string)k.Name;
                try
                {
                    MenuId enumKey = (MenuId)Enum.Parse(typeof(MenuId), keyName);
                    string value = k.Value.ToString();// == "true";
                    if (value == "True" || value == "False") value = value.ToLower();
                    baseRight.Add(enumKey, value);
                   //baseRight.Add(enumKey, k.Value.ToString());
                }
                catch (Exception) { }
            }
            return baseRight;
        }
        public static BaseRights Create(MenuId[] menuIds = null, string defaultValue = "")
        {
            var baseRight = new BaseRights();
            //baseRight.Add(MenuId.see, false);
            //baseRight.Add(MenuId.make, false);
            //baseRight.Add(MenuId.print, false);
            //baseRight.Add(MenuId.export, false);
            //baseRight.Add(MenuId.approve, false);
            // if(defaultValue=="")
            //   defaultValue = false.ToString().ToLower();
            if (menuIds != null)
                foreach (MenuId menuId in menuIds)
                {
                    
                    baseRight.Add(menuId, defaultValue);
                }
            return baseRight;
        }
        public string BaseRightType { get; set; } = "CHECK";
        public string value { get; set; } = "";
        public Dictionary<string, string> OptionsJson { get; set; } = new Dictionary<string, string>();
    }

    public enum MenuId
    {
        /// <summary>
        /// mainMenu
        /// </summary>
        Empty = 0,
        [EnumExt("销售", baseRights: new MenuId[] { see, make, approve, red, delete, print, export, review })]
        sale,
        [EnumExt("访销流程", baseRights: new MenuId[] { see, make, approve, red, delete, print, export})]
        orderFlow,
        [EnumExt("采购", baseRights: new MenuId[] { see, make, approve, red, delete, print, export, review })]
        buy,
        [EnumExt("库存", baseRights: new MenuId[] { see, make, approve, red, delete, print, export, review })]
        stock ,
        [EnumExt("资金", baseRights: new MenuId[] { see, make, approve, red, delete, print, export, review })]
        money,
        [EnumExt("财务", baseRights: new MenuId[] { see, make, approve, red, delete, print, export, review })]
        accounting,
        [EnumExt("报表", baseRights: new MenuId[] { see, print, export })]
        report,
        [EnumExt("档案", baseRights: new MenuId[] { see, edit, delete, print, export,create,approve })]
        info,
        [EnumExt("设置", baseRights: new MenuId[] { see, edit })]
        setting,
        [EnumExt("精细化", baseRights: new MenuId[] { })]
        delicacy,

        [EnumExt("财务报表", baseRights: new MenuId[] { see, print, export })]
        cwReport ,
        [EnumExt("财务单据", baseRights: new MenuId[] { see, make, approve, red, delete, print, export, review })]
        cwSheet,
        [EnumExt("财务操作", baseRights: new MenuId[] { see, operate })]
        cwOperate,

        [EnumExt("查看")]
        see = 30,
        [EnumExt("新建")]
        create,
        [EnumExt("制单")]
        make,
        [EnumExt("打印")]
        print,
        [EnumExt("导出")]
        export,
        [EnumExt("编辑")]
        edit,
        [EnumExt("红冲")]
        red,
        [EnumExt("取值")]
        value,
        //在此处添加新项
        [EnumExt("删除")]
        delete,

        [EnumExt("审核")]
        approve,
        [EnumExt("复核")]
        review, 
        [EnumExt("操作")]
        operate,
        //-------------------------------------------
        /// <summary>
        /// subMenu
        /// </summary>


        //销售  
        #region 销售 基本单据
        [EnumExt("销售订单", sale, baseRights: new MenuId[] { see, make, approve, red, delete, print, export, review })]
        sheetSaleOrder=100,
        [EnumExt("退货订单", sale, baseRights: new MenuId[] { see, make, approve, red, delete, print, export, review })]
        sheetReturnOrder,
        [EnumExt("销售单", sale, baseRights: new MenuId[] { see, make, approve, red, delete, print, export, review })]
        sheetSale,
        [EnumExt("补差单", sale, baseRights: new MenuId[] { see, make, approve, red, delete, print, export, review })]
        sheetPriceRebate,
        [EnumExt("退货单", sale, baseRights: new MenuId[] { see, make, approve, red, delete, print, export, review })]
        sheetReturn,
        [EnumExt("借货单", sale, baseRights: new MenuId[] { see, make, approve, red, delete, print, export, review })]
        sheetBorrowItem,
        [EnumExt("还货单", sale, baseRights: new MenuId[] { see, make, approve, red, delete, print, export, review })]
        sheetReturnItem,
        [EnumExt("调价单", sale, baseRights: new MenuId[] { see, make, approve, red, print, export })]
        sheetPriceAdjust,

        [EnumExt("拜访门店", sale, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        sheetVisit,

        [EnumExt("连锁超市订单", sale, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        sheetSupermarket,
        [EnumExt("占位单", sale, baseRights: new MenuId[] { see, make, approve, red, delete, print, export, review })]
        sheetPlaceholderOrder,
        [EnumExt("销售费用分摊单", sale, baseRights: new MenuId[] { see, make, approve, red, delete, print, export, review })]
        sheetSaleFeeApportion,

        [EnumExt("标签打印", sale, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        sheetLabelPrint,
        #endregion

        #region 销售 业务流程

        [EnumExt("交账单", sale, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        sheetCheckSheets ,
        [EnumExt("访单管理", sale, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        orderManage,

        [EnumExt("定货会", sale, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        orderItemSheet,
        [EnumExt("定货会调整单", sale, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        orderItemAdjustSheet,

        [EnumExt("陈列协议", sale, baseRights: new MenuId[] { see, make, approve, red, delete, print, export, review })]
        sheetDisplayAgreement, 

        [EnumExt("促销活动", sale, baseRights: new MenuId[] { see, make, approve, red, delete, print })]
        promotionView,
        [EnumExt("特价申请单", sale, baseRights: new MenuId[] { see, make, approve, red, delete, print })]
        sheetSpecialPrice,
        [EnumExt("红包", sale, baseRights: new MenuId[] { see, make, approve, red, delete, print })]
        redPacket,
        #endregion

        #region 销售 访单流程

        [EnumExt("打单", orderFlow, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        orderPrint ,
        [EnumExt("复核", orderFlow, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        orderReview,
        [EnumExt("装车", orderFlow, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        assignVan ,
        [EnumExt("装车回撤", orderFlow, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        retreatFromVan,
        [EnumExt("发货", orderFlow, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        deliverItems ,
        [EnumExt("转单(送货签收)", orderFlow, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        orderToSale ,
        [EnumExt("回库", orderFlow, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        backBranch,

        #endregion



        //采购
        #region 采购

        [EnumExt("采购单", buy, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        sheetBuy,

        [EnumExt("采购退货单", buy, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        sheetBuyReturn,

        [EnumExt("采购订单", buy, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        sheetBuyOrder,
        [EnumExt("采购调价单", buy, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        sheetBuyPriceAdjust,
        [EnumExt("采购费用分摊单", buy, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        sheetFeeApportion,
        [EnumExt("补贴单", buy, baseRights: new MenuId[] { see, make, approve, red, delete, print, export, review })]
        sheetPriceAllowance,

        #endregion



        //仓库
        #region 仓库
        [EnumExt("调拨单", stock, baseRights: new MenuId[] { see, make, approve, red, delete, print, export,review })]
        sheetMove,
        [EnumExt("盘点盈亏单", stock, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        sheetInventChange,
        [EnumExt("期初库存单", stock, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        sheetOpeningStock,
        [EnumExt("报损单", stock, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        sheetInventReduce,
        [EnumExt("盘点单", stock, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        sheetInvent,
        [EnumExt("门店库存上报单", stock, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        sheetStoreStock,
        [EnumExt("组装单", stock, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        sheetCombine,
        [EnumExt("拆分单", stock, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        sheetSplit,

        [EnumExt("成本调价单", stock, baseRights: new MenuId[] { see, make, approve, red, print, export })]
        sheetCostPriceAdjust,
        [EnumExt("其他入库单", stock, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        sheetStockIn,
        [EnumExt("其他出库单", stock, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        sheetStockOut,


        #endregion


        #region 资金单据

        [EnumExt("转账单", money, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        sheetCashBankTransfer,
        [EnumExt("现金银行期初余额", money, baseRights: new MenuId[] { see, make, print, export })]
        cashBankOpeningBalance,
        [EnumExt("现金银行余额表", money, baseRights: new MenuId[] { see, print, export })]
        cashBankBalance,
        [EnumExt("现金银行明细账", money, baseRights: new MenuId[] { see, print, export })]
        cashBankDetail,
        [EnumExt("贷款单", money, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        sheetLoan,
        [EnumExt("还贷款计划", money, baseRights: new MenuId[] { see, print, export })]
        repayPlan,
        [EnumExt("还贷款单", money, baseRights: new MenuId[] { see, make, approve, red, delete, print, export, review })]
        sheetRepay,
        [EnumExt("收款单", money, baseRights: new MenuId[] { see, make, approve, red, delete, print, export,review })]
        sheetGetArrears,
        [EnumExt("批量收款单", money, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        sheetGetBulkArrears,
        [EnumExt("对账单", money, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        sheetGetArrearsOrder,
        [EnumExt("批量对账单", money, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        sheetGetBulkArrearsOrder, 
        [EnumExt("付款单", money, baseRights: new MenuId[] { see, make, approve, red, delete, print, export, review })]
        sheetPayArrears,
        [EnumExt("预收款单", money, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        sheetPreget,
        [EnumExt("预付款单", money, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        sheetPrepay,
        [EnumExt("费用支出", money, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        sheetFeeOut,
        [EnumExt("其他收入", money, baseRights: new MenuId[] { see, make, approve, red, delete, print, export })]
        sheetFeeIn,
        [EnumExt("欠条管理", money, baseRights: new MenuId[] { see, make, approve, red, delete, print, export})]
        arrearsBill,
        
        // [EnumExt("查欠条发放单", money, baseRights: new MenuId[] { see, make, approve })]
        // sheetGrantArrearsView, 
        // [EnumExt("查欠条回收单", money, baseRights: new MenuId[] { see, make, approve })]
        // sheetRevokeArrearsView,
        #endregion

        #region 标准财务

        [EnumExt("凭证", cwSheet, baseRights: new MenuId[] { see, make, delete, approve, red, print, export })]
        sheetVoucher,
        [EnumExt("科目期初", cwSheet, baseRights: new MenuId[] { see, make, print, export })]
        openingBalance,

        [EnumExt("批量生成凭证", cwOperate, baseRights: new MenuId[] { see, operate })]
        sheetVoucherLists,
        [EnumExt("期末结转", cwOperate, baseRights: new MenuId[] { see, operate })]
        ClosingCarryForward,
        [EnumExt("财务开账", cwOperate, baseRights: new MenuId[] { see, operate })]
        openingAccount,
        /*[EnumExt("辅助核算", cwOperate, baseRights: new MenuId[] { see, operate })]
        assistAccounting,*/

        [EnumExt("资产管理", cwSheet, baseRights: new MenuId[] { see, make, delete, approve, print, export })]
        sheetPPE,
        [EnumExt("资产类别", cwSheet, baseRights: new MenuId[] { see, make, delete })]
        sheetPPEType,

        [EnumExt("资产负债表", cwReport, baseRights: new MenuId[] { see, print, export })]
        balanceSheet,
        [EnumExt("利润表", cwReport, baseRights: new MenuId[] { see, print, export })]
        profitSheet,
        [EnumExt("科目余额表", cwReport, baseRights: new MenuId[] { see, print, export })]
        accountBalance,
        [EnumExt("明细账", cwReport, baseRights: new MenuId[] { see, print, export })]
        detailBill,
        [EnumExt("核算项目余额表", cwReport, baseRights: new MenuId[] { see, print, export })]
        assistBalance,
        [EnumExt("核算项目明细账", cwReport, baseRights: new MenuId[] { see, print, export })]
        assistDetail,
        #endregion

        #region 销售报表
        [EnumExt("销售汇总 (按业务员)/销售排行", report, baseRights: new MenuId[] { see, print, export })]
        salesSummaryBySeller,
        [EnumExt("销售汇总 (按商品)/热销商品", report, baseRights: new MenuId[] { see, print, export })]
        salesSummaryByItem,
        [EnumExt("销售汇总 (按客户)/客户排行", report, baseRights: new MenuId[] { see, print, export })]
        salesSummaryByClient = 508,
        [EnumExt("销售汇总 (按片区)/片区", report, baseRights: new MenuId[] { see, print, export })]
        salesSummaryByRegion,
        [EnumExt("销售汇总 (按渠道)/渠道", report, baseRights: new MenuId[] { see, print, export })]
        salesSummaryByGroup,
        [EnumExt("销售汇总 (按送货员)/客户排行", report, baseRights: new MenuId[] { see, print, export })]
        salesSummaryBySender,
        [EnumExt("销售汇总 (按送货员/品)", report, baseRights: new MenuId[] { see, print, export })]
        salesSummaryBySenderAndItem,
        [EnumExt("销售汇总 (按客/品)", report, baseRights: new MenuId[] { see, print, export })]
        salesSummaryByClientAndItem,
        [EnumExt("销售汇总 (按业/品)", report, baseRights: new MenuId[] { see, print, export })]
        salesSummaryBySellerAndItem,
        [EnumExt("销售汇总 (按品牌)/品牌汇总", report, baseRights: new MenuId[] { see, print, export })]
        salesSummaryByBrand,
        [EnumExt("销量走势图/销量走势", report, baseRights: new MenuId[] { see, print, export })]
        salesTrend,
        [EnumExt("销售明细", report, baseRights: new MenuId[] { see, print, export })]
        salesDetaill,
        [EnumExt("交账汇总", report, baseRights: new MenuId[] { see, print, export })]
        checkSheetBySeller,
        [EnumExt("借还货汇总", report, baseRights: new MenuId[] { see, print, export })]
        borrowedView,
        [EnumExt("借还货明细", report, baseRights: new MenuId[] { see, print, export })]
        borrowItem,
        [EnumExt("客户借货余额", report, baseRights: new MenuId[] { see, print, export })]
        borrowBalance,
        [EnumExt("客户借货历史", report, baseRights: new MenuId[] { see, print, export })]
        borrowHistory,
        [EnumExt("借货数据迁移助手", report, baseRights: new MenuId[] { see, print, export })]
        borrowMigration,
        [EnumExt("定货商品汇总", report, baseRights: new MenuId[] { see, print, export })]
        itemsOrderedSummary,
        [EnumExt("定货商品变化明细", report, baseRights: new MenuId[] { see, print, export })]
        itemsOrderedSummaryByItem,
        [EnumExt("定货调整记录", report, baseRights: new MenuId[] { see, print, export })]
        itemsOrderedAdjust,
        [EnumExt("订单核销明细表", report, baseRights: new MenuId[] { see, print, export })]
        orderToSaleDifferencel,


        //[EnumExt("定货明细表", report, baseRights: new MenuId[] { see, print, export })]
        //itemsOrderedDetail,

        //在此处添加新项


        [EnumExt("陈列协议明细", report, baseRights: new MenuId[] { see, print, export })]
        displayAgreementDetail,
        [EnumExt("陈列协议变化(金额)", report, baseRights: new MenuId[] { see, print, export })]
        displayMoneyDetail,
        [EnumExt("陈列协议全览", report, baseRights: new MenuId[] { see, print, export })]
        displaySheetBrowser,
        [EnumExt("装车汇总(商品)", report, baseRights: new MenuId[] { see, print, export })]
        AssignSummaryByItem,
        [EnumExt("新客分析", report, baseRights: new MenuId[] { see, print, export })]
        createSupcustAnalysis,
        #endregion


        #region 仓库报表

        [EnumExt("库存表", report, baseRights: new MenuId[] { see, print, export })]
        viewStock,
        [EnumExt("库存变化表", report, baseRights: new MenuId[] { see, print, export })]
        stockChangeSum,
        [EnumExt("库存变化明细表", report, baseRights: new MenuId[] { see, print, export })]
        stocksChangeByOrder,
        [EnumExt("期初库存表", report, baseRights: new MenuId[] { see, print, export })]
        openingStockReport,
        [EnumExt("调拨汇总", report, baseRights: new MenuId[] { see, print, export })]
        moveSummary,
        [EnumExt("调拨明细表", report, baseRights: new MenuId[] { see, print, export })]
        movesDetail,
        [EnumExt("盘点汇总", report, baseRights: new MenuId[] { see, print, export })]
        inventorySummary,
        [EnumExt("盘点明细", report, baseRights: new MenuId[] { see, print, export })]
        inventoryDetail,
        [EnumExt("报损明细", report, baseRights: new MenuId[] { see, print, export })]
        breakageDetail,
        [EnumExt("库存预警表", report, baseRights: new MenuId[] { see, print, export })]
        stockAlert,
        [EnumExt("库存预警设置", report, baseRights: new MenuId[] { see, print, export })]
        stockAlertDetail,
        [EnumExt("临期预警表", report, baseRights: new MenuId[] { see, print, export })]
        approachingAlert,
        [EnumExt("承包利润表(公司)", report, baseRights: new MenuId[] { see, print, export })]
        contractProfitCompany,
        [EnumExt("承包利润表(业务员)", report, baseRights: new MenuId[] { see, print, export })]
        contractProfitSeller,
        [EnumExt("门店库存上报表", report, baseRights: new MenuId[] { see, print, export })]
        storeStockReport,
        #endregion




        //报表
        #region 采购报表
        [EnumExt("采购汇总(商品)", report, baseRights: new MenuId[] { see, print, export })]
        buysSummaryByItem,
        [EnumExt("采购明细表", report, baseRights: new MenuId[] { see, print, export })]
        buysDetail,
        #endregion

        #region 资金报表
        [EnumExt("客户应收款", report, baseRights: new MenuId[] { see, print, export })]
        arrearsBlance,
        [EnumExt("预收款余额", report, baseRights: new MenuId[] { see, print, export })]
        pregetBalance,
        [EnumExt("收款单明细", report, baseRights: new MenuId[] { see, print, export })]
        arrearsSheetDetail,
        [EnumExt("客户往来账/往来账", report, baseRights: new MenuId[] { see, print, export })]
        clientBusinessHistory,
        [EnumExt("客户往来账汇总表", report, baseRights: new MenuId[] { see, print, export })]
        accountChangeSum,

        [EnumExt("供应商应付款", report, baseRights: new MenuId[] { see, print, export })]
        supplierArrears,
        [EnumExt("预付款余额", money, baseRights: new MenuId[] { see, print, export })]
        prepayBalance,
        [EnumExt("供应商往来账", report, baseRights: new MenuId[] { see, print, export })]
        supplierBusinessHistory,
        [EnumExt("供应商往来汇总表", report, baseRights: new MenuId[] { see, print, export })]
        supplierAccountChangeSum,

        [EnumExt("经营利润表", report, baseRights: new MenuId[] { see, print, export })]
        businessProfit,
        [EnumExt("费用合计表", report, baseRights: new MenuId[] { see, print, export })]
        feeOutSummary,
        [EnumExt("现金收支表", report, baseRights: new MenuId[] { see, print, export })]
        cashInOut,

        [EnumExt("收入支出明细表", report, baseRights: new MenuId[] { see, print, export })]
        feeOutDetail,
        [EnumExt("客户应收款明细", report, baseRights: new MenuId[] { see, print, export })]
        arrearsBlanceDetail,

        [EnumExt("银行支付对账表", report, baseRights: new MenuId[] { see, print, export })]
        payBillTradesCheck,

        #endregion

        #region 员工报表
        [EnumExt("业务员外勤轨迹", report, baseRights: new MenuId[] { see, print, export })]
        visitPath,
        [EnumExt("业务员外勤轨迹", report, baseRights: new MenuId[] { see, print, export })]
        visitPathGIS,
        [EnumExt("业务员拜访记录", report, baseRights: new MenuId[] { see, print, export })]
        visitRecord,
        [EnumExt("提成表", report, new MenuId[] { see, print, export })]
        commission,
        [EnumExt("业务员排行榜", report, baseRights: new MenuId[] { see, print, export })]
        sellerRank,
        [EnumExt("拜访汇总", report, baseRights: new MenuId[] { see, print, export })]
        visitSummaryBySeller,
        [EnumExt("客户分布", report, baseRights: new MenuId[] { see, print, export })]
        supcustDistribution,
        [EnumExt("请假审批", report, baseRights: new MenuId[] { see, print, export })]
        attendanceLeaveApprove,
        [EnumExt("考勤报表", report, baseRights: new MenuId[] { see, print, export })]
        AttendanceReport,
        [EnumExt("考勤报表（总）", report, baseRights: new MenuId[] { see, print, export })]
        AttendanceReportAll,
        #endregion

        #region 会员积分
        [EnumExt("会员积分汇总表", report, baseRights: new MenuId[] { see, print, export })]
        vipPointsSummary,
        [EnumExt("会员积分明细表", report, baseRights: new MenuId[] { see, print, export })]
        vipPointsDetail, 
        #endregion

        #region 其他报表
        [EnumExt("客户活跃度(流失预警)", report, baseRights: new MenuId[] { see, print, export })]
        customerLiveness,
        [EnumExt("客户价值分析", report, baseRights: new MenuId[] { see, print, export })]
        clientValue,
        [EnumExt("新增客户报表", report, baseRights: new MenuId[] { see, print, export })]
        clientAdd,
        [EnumExt("经营历程", report, baseRights: new MenuId[] { see, print, export })]
        businessHistoryLists,
        [EnumExt("客户流失预警", report, baseRights: new MenuId[] { see, print, export })]
        clientLossWarning,
        [EnumExt("单品铺市率", report, baseRights: new MenuId[] { see, print, export })]
        itemsOccupyReport,
        [EnumExt("品牌铺市率", report, baseRights: new MenuId[] { see, print, export })]
        brandsOccupyReport,
        [EnumExt("档案变化表", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        docChangeLog,

        #endregion



        //档案
        #region 商品档案
        [EnumExt("商品", info, baseRights: new MenuId[] { see, edit, delete, print, export,create, approve },"true")]
        infoItem ,
        [EnumExt("单位", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        infoUnit,
        [EnumExt("品牌", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        infoBrand,
        [EnumExt("条码秤组", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        infoBarcodeScale,

        [EnumExt("价格方案", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        infoPrice,
        [EnumExt("价格策略", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        priceStrategy,
        [EnumExt("欠款策略", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        arrearsStrategy,
        [EnumExt("上次售价", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        preSalePrice,
        [EnumExt("组装拆分模型", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        combineTemp,

        #endregion

        #region 客户档案
        [EnumExt("客户", info, baseRights: new MenuId[] { see, edit, delete, print, export,create,approve },"true")]
        infoClient,
        [EnumExt("渠道", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        infoGroup,
        [EnumExt("等级", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        infoRank,
        [EnumExt("供应商", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        infoSupplier,
        [EnumExt("费用单位", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        infoFeeUnit,
        [EnumExt("结算方式", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        infoAcctWay ,
        [EnumExt("片区", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        infoRegion,
        #endregion

        #region 员工档案
        [EnumExt("员工", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        infoOperator,
        [EnumExt("角色", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        infoRole,
        [EnumExt("提成方案", report, new MenuId[] { see, edit, print, delete, export })]
        commissionPlan,
        [EnumExt("行程", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        visitSchedule,
        [EnumExt("日程", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        visitDay,
        [EnumExt("考勤设置", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        attendanceSetting,
        [EnumExt("考勤规范", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        visitStandard,
        [EnumExt("提成策略", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        commissionStrategiesView,
        #endregion

        #region 其他档案
        [EnumExt("仓库", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        infoBranch,
        [EnumExt("库位", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        infoBranchPosition,
        [EnumExt("库位类型", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        infoBranchPositionType,
        [EnumExt("账户管理", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        infoPayQrCode,
        [EnumExt("借贷款单位档案", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        infoLoanPartner,
        [EnumExt("客户限定商品", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        infoClientItemCode,
        [EnumExt("客品匹配表", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        infoClientItemMatch,
        #endregion



        //设置
        #region 设置   
        [EnumExt("公司设置", setting, baseRights: new MenuId[] { see })]
        companySetting,
        [EnumExt("导入信息", setting, baseRights: new MenuId[] { see })]
        infoImport,
        [EnumExt("业务结账", setting, baseRights: new MenuId[] { see, edit })]
        bizMonthClose,
        [EnumExt("打印模板", setting, baseRights: new MenuId[] { see, edit })]
        infoPrintTemplate,
		[EnumExt("小票打印模板", setting, baseRights: new MenuId[] { see, edit })]
		infoReceiptTemplate,
		[EnumExt("云打印机", setting, baseRights: new MenuId[] { see, edit })]
        setCloudPrinter,
        [EnumExt("会计科目", info, baseRights: new MenuId[] { see, edit, print, export })]
        paywaysView,
        [EnumExt("备注信息", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        briefsView,
        [EnumExt("电商对接", setting, baseRights: new MenuId[] { see })]
        emartSetting,
        [EnumExt("电商商品设置", setting, baseRights: new MenuId[] { see, edit })]
        emartItemSet,
        [EnumExt("支付方式", info, baseRights: new MenuId[] { see, edit, print, export })]
        paywaysQuickView,
        [EnumExt("员工消息订阅", setting, baseRights: new MenuId[] { see })]
        msgSubscribeCompany,
        [EnumExt("分销商管理", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        rsSeller,
        [EnumExt("分销方案", info, baseRights: new MenuId[] { see, edit, delete, print, export })]
        rsPlan,
        #endregion

        #region 商城
        [EnumExt("商城配置", setting, baseRights: new MenuId[] { see })]
        mallSetting ,
        [EnumExt("上架管理", setting, baseRights: new MenuId[] { see })]
        onsaleTemplate ,
        [EnumExt("装修模板", setting, baseRights: new MenuId[] { see })]
        mainpageMaintenance,
        [EnumExt("商城用户", setting, baseRights: new MenuId[] { see })]
        mallUser,
        [EnumExt("商城公告", setting, baseRights: new MenuId[] { see, edit, delete, })]
        mallNotice,
        #endregion
        
        #region 会员积分
        [EnumExt("会员等级", setting, baseRights: new MenuId[] { see, edit, delete,create })]
        vipLevel ,
        [EnumExt("会员方案", setting, baseRights: new MenuId[] { see, edit, delete,create })]
        vipPlan,
        [EnumExt("会员策略", setting, baseRights: new MenuId[] { see, edit })]
        vipStrategy,
        #endregion



        #region 精细化管理【不和菜单关联】
        [EnumExt("查看进价", delicacy, baseRights: new MenuId[] { value },"false")]
        seeInPrice,
        [EnumExt("查看利润", delicacy, baseRights: new MenuId[] { value }, "false")]
        seeProfit,
        [EnumExt("调拨单多次保存", delicacy, baseRights: new MenuId[] { value }, "false")]
        moveShtMultiSave,
        [EnumExt("查看售价", delicacy, baseRights: new MenuId[] { value }, "true")]
        seeSalePrice,
        [EnumExt("单据查看范围", delicacy, baseRights: new MenuId[] { value }, EnumExtAttribute.BASE_RIGHT_TYPE.SELECT, "{'self':'查看自己单据','department':'查看部门单据','all':'查看所有单据'}", "all")]
        sheetViewRange,
        [EnumExt("装车时单据查看范围", delicacy, baseRights: new MenuId[] { value }, EnumExtAttribute.BASE_RIGHT_TYPE.SELECT, "{'default':'默认','self':'查看自己单据','department':'查看部门单据','all':'查看所有单据'}", "default")]
        sheetViewRangeOnAssignVan,
		//[EnumExt("送货签收时单据查看范围", delicacy, baseRights: new MenuId[] { value }, EnumExtAttribute.BASE_RIGHT_TYPE.SELECT, "{'default':'默认','self':'查看自己单据','department':'查看部门单据','all':'查看所有单据'}", "default")]
		//sheetViewRangeOnOrderToSheet, 
		[EnumExt("查看单据时, 如指定客户可以看到所有单据", delicacy, baseRights: new MenuId[] { value }, "true")]
        notLimitViewRangeOnClient,
        [EnumExt("销售订单选客户时默认仓库", delicacy, baseRights: new MenuId[] { value }, EnumExtAttribute.BASE_RIGHT_TYPE.SELECT, "{'setting':'公司设置默认仓库','lasttime':'该客户上次开单仓库'}", "setting")]
        saleOrderDefaultBranch,
        [EnumExt("销售单选客户时记忆上次开单仓库", delicacy, baseRights: new MenuId[] { value }, "true")]
        saleRememberBranch,
        [EnumExt("销售单销售订单选客户时记忆上次开单业务员", delicacy, baseRights: new MenuId[] { value }, "true")]
        saleAndOrderRemeberSeller,
        [EnumExt("允许查看小程序单据", delicacy, baseRights: new MenuId[] { value }, "false")]
        canSeeMallSheet,
        [EnumExt("调拨单查看范围", delicacy, baseRights: new MenuId[] { value }, EnumExtAttribute.BASE_RIGHT_TYPE.SELECT, "{'self':'查看自己单据','department':'查看部门单据','all':'查看所有单据'}", "all")]
        sheetDBViewRange,
        [EnumExt("应收款展示范围", delicacy, baseRights: new MenuId[] { value }, EnumExtAttribute.BASE_RIGHT_TYPE.SELECT, "{'self':'查看自己产生欠款','department':'查看部门产生的欠款','all':'查看所有欠款'}", "all")]
        supcustArrearsRange,
        [EnumExt("装车时车辆库存不超库存预警上限", delicacy, baseRights: new MenuId[] { value }, EnumExtAttribute.BASE_RIGHT_TYPE.SELECT, "{'true':'是','false':'否'}", "false")]
        limitMaxVanStockOnMove,
        [EnumExt("上报行走轨迹", delicacy, baseRights: new MenuId[] { value }, "false")]
        reportTrail,
        [EnumExt("允许负库存出货", delicacy, baseRights: new MenuId[] { value }, "true")]
        allowNegativeStock,
        [EnumExt("无库存换货", delicacy, baseRights: new MenuId[] { value }, "true")]
        allowNoStockHH,
        [EnumExt("销售/收款单可以打印的状态", delicacy, baseRights: new MenuId[] { value }, EnumExtAttribute.BASE_RIGHT_TYPE.SELECT, "{'saved':'保存或审核后','approved':'审核后','all':'所有状态'}", "all")]
        sheetStatusForPrint,

        [EnumExt("调拨单可以打印的状态", delicacy, baseRights: new MenuId[] { value }, EnumExtAttribute.BASE_RIGHT_TYPE.SELECT, "{'saved':'保存或审核后','approved':'审核后','all':'所有状态'}", "saved")]
        moveSheetStatusForPrint,

        [EnumExt("红冲单据限制", delicacy, baseRights: new MenuId[] { value }, EnumExtAttribute.BASE_RIGHT_TYPE.SELECT, "{'1days':'当天','2days':'近2天','3days':'近3天','7days':'近7天','30days':'近30天','60days':'近60天','90days':'近90天','180days':'近180天','360days':'近360天','720days':'近720天','1080days':'近1080天'}", "3days")]
        redSheetDayLimit,

        [EnumExt("无备注红冲(冲改)单据", delicacy, baseRights: new MenuId[] { value }, "false")]
        redSheetNoBrief,
        [EnumExt("不拜访可开销售订单", delicacy, baseRights: new MenuId[] { value }, "true")]
        allowNoVisitOrder,
        [EnumExt("不拜访可开销售单", delicacy, baseRights: new MenuId[] { value }, "false")]
        allowNoVisitSale,
        [EnumExt("交账后当日开单", delicacy, baseRights: new MenuId[] { value }, EnumExtAttribute.BASE_RIGHT_TYPE.SELECT, "{'allow':'允许','notAllow':'不允许'}", "allow")]
        newSheetAfterChecked,
        [EnumExt("已开单商品允许更改包装率和单位", delicacy, baseRights: new MenuId[] { value }, EnumExtAttribute.BASE_RIGHT_TYPE.SELECT, "{'allow':'允许','notAllow':'不允许'}", "notAllow")]
        changeUnitfactorAfterNewSheet,
        [EnumExt("单据打印后允许红冲/冲改单据", delicacy, baseRights: new MenuId[] { value }, EnumExtAttribute.BASE_RIGHT_TYPE.SELECT, "{'allow':'允许','notAllow':'不允许'}", "allow")]
        allowRedSheetAfterPrint,
        [EnumExt("单据复核后允许红冲/冲改单据", delicacy, baseRights: new MenuId[] { value }, EnumExtAttribute.BASE_RIGHT_TYPE.SELECT, "{'allow':'允许','notAllow':'不允许'}", "allow")]
        allowRedSheetAfterReview,
        [EnumExt("销售单改价", delicacy, baseRights: new MenuId[] { value }, "true")]
        allowChangeSaleSheetPrice,
        
        [EnumExt("退货单改价", delicacy, baseRights: new MenuId[] { value }, "true")]
        allowChangeReturnSheetPrice,

        [EnumExt("低于最低售价销售", delicacy, baseRights: new MenuId[] { value }, "false")]
        belowMinSalePrice, 
		[EnumExt("低于成本价销售", delicacy, baseRights: new MenuId[] { value }, "true")]
		belowCostPrice,
		 
		[EnumExt("允许欠款", delicacy, baseRights: new MenuId[] { value }, "true")]
        allowSheetArrears,
        [EnumExt("允许优惠", delicacy, baseRights: new MenuId[] { value }, "true")]
        allowSheetDiscAmount,
        [EnumExt("预收预付款可负", delicacy, baseRights: new MenuId[] { value }, "false")]
        allowNegativePrepay,
        [EnumExt("提前兑付陈列费用", delicacy, baseRights: new MenuId[] { value }, "false")]
        allowAdvanceDisplayFee,
        [EnumExt("允许查看库存金额", delicacy, baseRights: new MenuId[] { value }, "true")]
        allowViewStockAmount,
        [EnumExt("允许登录电脑端", delicacy, baseRights: new MenuId[] { value }, "true")]
        allowLoginWeb,
        [EnumExt("允许登录APP端", delicacy, baseRights: new MenuId[] { value }, "true")]
        allowLoginApp,
        [EnumExt("APP开单显示折扣", delicacy, baseRights: new MenuId[] { value }, "false")]
        appShowDiscount,
        [EnumExt("APP开单使用折扣计算", delicacy, baseRights: new MenuId[] { value }, "false")]
        appCalcDiscount,
        [EnumExt("APP开单可输送货时间", delicacy, baseRights: new MenuId[] { value }, "false")]
        appInputSendTime,
        [EnumExt("APP开单可选业务员", delicacy, baseRights: new MenuId[] { value }, "false")]
        appSelectSeller,
        [EnumExt("APP开单可选送货员", delicacy, baseRights: new MenuId[] { value }, "false")]
        appSelectSender,
        [EnumExt("APP装车可选送货员", delicacy, baseRights: new MenuId[] { value }, "true")]
        appAssignSelectSender,
        [EnumExt("APP开单送货员为制单人", delicacy, baseRights: new MenuId[] { value }, "false")]
        appOperIsDefSender,
        [EnumExt("APP开单可选收款人", delicacy, baseRights: new MenuId[] { value }, "false")]
        appSelectGetter,
        [EnumExt("可使用支出类账户支付", delicacy, baseRights: new MenuId[] { value }, "false")]
        payWithFeeOutAcct,

        [EnumExt("允许开零售单", delicacy, baseRights: new MenuId[] { value }, "false")]
        allowRetailSheet,
        [EnumExt("参与考勤", delicacy, baseRights: new MenuId[] { value }, "false")]
        joinAttendance,
        [EnumExt("允许跳点拜访", delicacy, baseRights: new MenuId[] { value }, "true")]
        allowSkipVisit,
        [EnumExt("APP允许搭赠", delicacy, baseRights: new MenuId[] { value }, "true")]
        allowSaleAsGift,

        [EnumExt("最大售价差", delicacy, baseRights: new MenuId[] { value }, EnumExtAttribute.BASE_RIGHT_TYPE.INPUT, "", "", "百分比或金额,例:5%或3")]
        maxSaleDiscPrice,
        [EnumExt("最大销售优惠额", delicacy, baseRights: new MenuId[] { value }, EnumExtAttribute.BASE_RIGHT_TYPE.INPUT, "", "", "百分比或金额,例:5%或3")]
        maxSaleDiscAmt,
        [EnumExt("承包业务", delicacy, baseRights: new MenuId[] { value }, "false")]
        isContractSeller,
        [EnumExt("承包价改价", delicacy, baseRights: new MenuId[] { value }, "true")]
        changeContractPrice,
        [EnumExt("允许跨部门兑付陈列", delicacy, baseRights: new MenuId[] { value }, "false")]
        giveDisplayCrossDept,
        [EnumExt("可送货签收未装车订单", delicacy, baseRights: new MenuId[] { value }, "false")]
        noVanOrderToSale,
        
        [EnumExt("允许开单负优惠", delicacy, baseRights: new MenuId[] { value }, "true")]
        negativeDiscAmt,
        [EnumExt("允许开单负欠款", delicacy, baseRights: new MenuId[] { value }, "true")]
        negativeLeftAmt,
        [EnumExt("允许开单负支付", delicacy, baseRights: new MenuId[] { value }, "true")]
        negativePayAmt,
        [EnumExt("开单商品默认数量", delicacy, baseRights: new MenuId[] { value }, EnumExtAttribute.BASE_RIGHT_TYPE.INPUT,"","", "")]
        saleItemDefQty,
        [EnumExt("智能拍单", delicacy, baseRights: new MenuId[] { value }, "false")]
        allowMobilePicImport,
        [EnumExt("拜访门店必须打开定位", delicacy, baseRights: new MenuId[] { value }, EnumExtAttribute.BASE_RIGHT_TYPE.SELECT, "{'true':'是','false':'否'}", "true")]
        appVisitNeedPosition,
        [EnumExt("开单时必须打开定位", delicacy, baseRights: new MenuId[] { value }, EnumExtAttribute.BASE_RIGHT_TYPE.SELECT, "{'true':'是','false':'否'}", "false")]
        appSheetNeedPosition,
        [EnumExt("客户档案审核后才能开单", delicacy, baseRights: new MenuId[] { value }, EnumExtAttribute.BASE_RIGHT_TYPE.SELECT, "{'true':'是','false':'否'}", "false")]
        appSheetNeedClientApproved,
        [EnumExt("单据明细行自由输入备注", delicacy, baseRights: new MenuId[] { value }, EnumExtAttribute.BASE_RIGHT_TYPE.SELECT, "{'true':'是','false':'否'}", "true")]
        appSheetRowRemarkFreeInput,
        [EnumExt("手机端销售(订)单打印即审核", delicacy, baseRights: new MenuId[] { value }, EnumExtAttribute.BASE_RIGHT_TYPE.SELECT, "{'true':'是','false':'否'}", "false")]
        appSalePrintIsApprove,
        //2024.9.25 增加app端修改促销组合单价权限控制 
        [EnumExt("手机端允许操作员修改促销组合单价", delicacy, baseRights: new MenuId[] { value }, EnumExtAttribute.BASE_RIGHT_TYPE.SELECT, "{'true':'是','false':'否'}", "false")]
        appPromotionPriceChangeApprove,
        [EnumExt("查看商城公码（批发版）", delicacy, baseRights: new MenuId[] { value }, "true")]
        shareMallCode_p,
        [EnumExt("查看商城公码（零售版）", delicacy, baseRights: new MenuId[] { value }, "true")]
        shareMallCode_l,
        //2024.12.18 增加app端销售单、销售订单开单的时候开借货商品与还货商品权限控制
        [EnumExt("允许销售单开借货、还货", delicacy, baseRights: new MenuId[] { value }, "true")]
        allowBorrowOnSale,
		[EnumExt("送货抢单", delicacy, baseRights: new MenuId[] { value }, "false")]
		senderGrabOrder,
        [EnumExt("销售订单审核后才能抢单", delicacy, baseRights: new MenuId[] { value }, "false")]
		allowGrabAfterSaleOrderApproved,

		#endregion

		/*
        [EnumExt("进价", setting, baseRights: new MenuId[] { see, edit })]
        infoImport,
        [EnumExt("批发价", setting, baseRights: new MenuId[] { see, edit })]
        infoImport,

        [EnumExt("别人单据", setting, baseRights: new MenuId[] { see, make, approve})]
        infoImport,*/





		//[EnumExt("费用支出", report)]
		//sheetFeeOut,

		//[EnumExt("收款对账", money)]
		//a,//收款对账
		//[EnumExt("打印模板", report)]
		//o,//打印模板
		//[EnumExt("打印控制", report)]
		//p,//打印控制
		//[EnumExt("可定制工作台", report)]
		//q,//可定制工作台
		//[EnumExt("简易官网", report)]
		//r,//简易官网
		//[EnumExt("登录页面", report)]
		//s,//登录页面

	}

	[AttributeUsage(AttributeTargets.Field)]
    public class EnumExtAttribute : Attribute//main.cshtml.cs引用了这个类
    {

        public string Display { get; set; }
        public MenuId Group { get; set; }
        public MenuId[] BaseRights { get; set; }

        public string DefaultValue = "";
        public string Tip = "";

        public BASE_RIGHT_TYPE BaseRightType = BASE_RIGHT_TYPE.CHECK;
        public string OptionsJson = "";
        public enum BASE_RIGHT_TYPE
        {
            CHECK, INPUT, SELECT
        }
        public EnumExtAttribute(string name, MenuId group = default, MenuId[] baseRights = null, string defaultValue = "")
        {
            Display = name;
            Group = group;
            BaseRights = baseRights;
            DefaultValue = defaultValue;
        }

        public EnumExtAttribute(string name, MenuId group, MenuId[] baseRights, BASE_RIGHT_TYPE baseRightType, string optionsJson, string defaultValue = "", string tip = "")
        {
            Display = name;
            Group = group;
            BaseRights = baseRights;
            BaseRightType = baseRightType;
            OptionsJson = optionsJson;
            DefaultValue = defaultValue;
            Tip = tip;
        }

    }

}
