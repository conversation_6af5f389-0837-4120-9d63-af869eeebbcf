﻿using ArtisanManage.Models;
using ArtisanManage.Pages.WeChat.SheetPages;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.CodeAnalysis.Elfie.Diagnostics;
using Microsoft.CodeAnalysis.Operations;
using myJXC;
using Newtonsoft.Json;
using NPOI.HSSF.Record;
using NPOI.POIFS.Crypt.Dsig;
using NPOI.SS.Formula.Functions;
using NPOI.SS.UserModel;
using Quartz.Util;
using Renci.SshNet;
using Swashbuckle.AspNetCore.SwaggerGen;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Dynamic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using static NPOI.HSSF.Util.HSSFColor;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace ArtisanManage.MyJXC
{
    public class SheetRowAssignVan:SheetRowBase
    {
        public SheetRowAssignVan()
        {

        }
        //[SaveToDB(false)][FromFld(false)] public override string remark { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override int inout_flag { get; set; } = 0;
        [SaveToDB(false)][FromFld(false)] public override int row_index { get; set; } = 0;

        [FromFld("op_id")]public override string sheet_id { get; set; }
        [SaveToDB(false)][FromFld(false)] public string sheet_no { get; set; }
        [SaveToDB][FromFld] public string item_id { get; set; }

        [FromFld("ip.item_name")]public string item_name { get; set; }
        [SaveToDB][FromFld] public string unit_no { get; set; }
           
        [SaveToDB][FromFld] public decimal unit_factor { get; set; } = 1;

        [SaveToDB][FromFld] public decimal quantity { get; set; }
        [SaveToDB(false)][FromFld] public decimal retreat_qty { get; set; }


        [SaveToDB][FromFld] public decimal sheet_order_quantity { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string produce_date { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string batch_no { get; set; } = "";
        [SaveToDB][FromFld] public string batch_id { get; set; } = "0";
        [SaveToDB][FromFld] public string branch_position { get; set; } = "0";
        [FromFld(LOAD_PURPOSE.SHOW)] public string branch_position_name { get; set; } = "";
        [SaveToDB][FromFld("COALESCE(t.branch_id,omtvm.from_branch)")] public string branch_id { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string branch_name { get; set; } = "";
        public string from_branch_position { get; set; } = "0";
        public string to_branch_position { get; set; }= "0";

        [SaveToDB][FromFld] public string sale_order_sheet_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)]public string brand_name { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)]public string class1_name { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)]public string class_name { get; set; }
    }
    public class SheetAssignVan : SheetBase<SheetRowAssignVan>
    {
        [SaveToDB("op_id")][IDField][FromFld("op_id")] public override string sheet_id { get; set; }
        [SaveToDB("op_no")][FromFld("case when t.op_no is  null then 'ZC'||t.op_id::text else t.op_no end")] public override string sheet_no { get; set; } = "";
        [SaveToDB][FromFld] public string op_type { get; set; }
        [SaveToDB][FromFld] public string oper_id { get; set; }
        [FromFld("op.oper_name")] public string oper_name { get; set; }
        [SaveToDB][FromFld] public string move_sheet_id { get; set; }
        [SaveToDB][FromFld] public string senders_id { get; set; }
        [SaveToDB][FromFld] public string senders_name { get; set; }
        [SaveToDB(false)][FromFld(false)] public override string red_sheet_id { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string maker_id { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string make_time { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string make_brief { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string approve_brief { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string submit_time { get; set; } = "";
        [SaveToDB(false)][FromFld("case when od.retreats_id is null then null else true end")] public  string have_retreat { get; set; } = "";
        [FromFld("od.no_retreat_order_sheet_id")] public string no_retreat_order_sheet_id { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string maker_name { get; set; } = "";

        [FromFld("otd.sale_order_sheets_id")] public string sale_order_sheets_id { get; set; }
        public string sale_order_sheets_no { get; set; }
        [FromFld("sm.sheet_no")] public string move_sheet_no { get; set; }
        [SaveToDB][FromFld] public string from_branch { get; set; }
        [FromFld("v.branch_name")] public string to_van_name { get; set; }
        [SaveToDB][FromFld] public string to_van { get; set; }
        [FromFld("f.branch_name")] public string from_branch_name { get; set; }
        public string order_status { get; set; }
        //public string sale_order_branch_id { get; set; }
        [FromFld("otd.move_sheet_ids")] public string move_sheet_ids { get; set; } = "";

        [SaveToDB][FromFld("case when t.approver_id is not null then t.approver_id else sm.approver_id end")] public override string approver_id { get; set; } = "";
        [FromFld("case when t.approver_id is not null then  approver.approver_name else mover.approver_name end")] public override string approver_name { get; set; } = "";
        [SaveToDB][FromFld("to_char( case when t.approve_time is not null then t.approve_time else sm.approve_time end , 'yyyy-MM-dd hh24:mi:ss' ) as approve_time")] public override string approve_time { get; set; } = "";

        [SaveToDB][FromFld("case when t.move_stock is not null then t.move_stock::text else otd.move_stock::text end")] public string move_stock { get; set; }
        public string sheetsInfo { get; set; } = "";
        public string newSheetsInfo { get; set; } = "";
        //public bool hasChanged { get; set; } = false;
        public List<SheetRowAssignVan> SheetRowsForDetail = new List<SheetRowAssignVan>();
        private void ConstructFun()
        {
			MainLeftJoin = @" 
                    LEFT JOIN info_branch f ON T.from_branch = f.branch_id AND f.company_id = ~COMPANY_ID
	                LEFT JOIN info_branch v ON T.to_van = v.branch_id AND v.company_id = ~COMPANY_ID
                    LEFT JOIN ( SELECT oper_id, oper_name  FROM info_operator WHERE company_id = ~COMPANY_ID ) op on op.oper_id = t.oper_id
	                LEFT JOIN ( SELECT oper_id, oper_name AS approver_name FROM info_operator WHERE company_id = ~COMPANY_ID ) approver ON T.approver_id = approver.oper_id 
	                LEFT JOIN 
                    (
                            SELECT op_id , string_agg(DISTINCT so.move_stock::text,',') move_stock,string_agg(sale_order_sheet_id::text,',') sale_order_sheets_id ,jsonb_agg(DISTINCT jsonb_build_object(COALESCE(sale_order_row_branch_id::text,'-1'),move_sheet_id::text||'_'||smm.sheet_no::text))   move_sheet_ids
                            FROM op_move_to_van_detail d 
                            left join sheet_move_main smm on smm.company_id =~COMPANY_ID  and smm.sheet_id = d.move_sheet_id
                            LEFT JOIN sheet_status_order so on so.company_id = d.company_id and d.sale_order_sheet_id = so.sheet_id
                            WHERE d.company_id = ~COMPANY_ID GROUP BY op_id
                    ) otd on otd.op_id = t.op_id
	                LEFT JOIN sheet_move_main sm on sm.company_id = ~COMPANY_ID and sm.sheet_id = t.move_sheet_id
                    LEFT JOIN (  SELECT op_id, string_agg(retreat_id::text,',') retreats_id,string_agg(case when retreat_id is null then sale_order_sheet_id::text else null end,','  ) no_retreat_order_sheet_id FROM op_move_to_van_detail WHERE company_id = ~COMPANY_ID  GROUP BY op_id) od on od.op_id = t.op_id
	                LEFT JOIN ( SELECT oper_id, oper_name AS approver_name FROM info_operator WHERE company_id = ~COMPANY_ID ) mover ON sm.approver_id = mover.oper_id 
                ";
			DetailLeftJoin = $@" left join info_item_prop ip on ip.company_id= t.company_id and ip.item_id = t.item_id
left join info_item_brand ib on ip.item_brand=ib.brand_id and ib.company_id=~company_id
left join (select class_id classId,class_name,order_index as class_order_index,general_class as gen_class2 from info_item_class where company_id =~company_id) ic on ip.item_class=ic.classId 
left join (select class_id classId1,class_name as class1_name,order_index as class1_order_index, general_class as gen_class1 from info_item_class where company_id =~company_id) ic1 on text_to_int(split_part(ip.other_class,'/', 3)) = ic1.classId1
left join (select batch_id,COALESCE(batch_no,'') as batch_no,SUBSTRING(COALESCE(produce_date::text,''),1,10) as produce_date from info_item_batch where company_id= ~COMPANY_ID) itb on itb.batch_id = t.batch_id
left join (select from_branch,op_id from op_move_to_van_main where company_id = ~COMPANY_ID) omtvm on omtvm.op_id = t.op_id
left join info_branch ibb on ibb.branch_id =COALESCE( t.branch_id,omtvm.from_branch)
left join info_branch_position ibp on ibp.branch_id =ibb.branch_id and ibp.branch_position = COALESCE(t.branch_position,0)";
		}
        public SheetAssignVan(LOAD_PURPOSE loadPurpose) : base("op_move_to_van_main", "op_move_to_van_row", loadPurpose)
        {
            sheet_type = SHEET_TYPE.SHEET_ASSIGN_VAN;
            ConstructFun();

		}

		public SheetAssignVan() : base("op_move_to_van_main", "op_move_to_van_row", LOAD_PURPOSE.SHOW)
		{
			sheet_type = SHEET_TYPE.SHEET_ASSIGN_VAN;
			ConstructFun();
		}

		public override string GetOtherSaveSQL()
        {
            string sqlDetail = "";
            var strOpId = "@op_id";
            if (sheet_id != "")
            {
                sqlDetail += $@"DELETE FROM op_move_to_van_detail where company_id = '{company_id}' and op_id = {sheet_id}; ";
                strOpId = sheet_id;
            }
            Dictionary<string, string> branchsId = new Dictionary<string, string>();
            foreach (SheetRowAssignVan row in SheetRows)
            {
                if (row.sale_order_sheet_id == "-1")
                {
                    string branchId = row.branch_id;
                    if (!branchsId.ContainsKey(branchId)) branchsId.Add(branchId, branchId);
                }
            }
            if (move_sheet_ids == "")
            {
                sqlDetail = sale_order_sheets_id.Split(',').Aggregate("", (current, sheetId) => current + $@"insert into op_move_to_van_detail (op_id,oper_id,company_id, sale_order_sheet_id)
                           values ('{strOpId}', '{OperID}', '{company_id}', '{sheetId}');");
                if (sheet_id != "")
                {
                    sqlDetail = sale_order_sheets_id.Split(',').Aggregate("", (current, sheetId) => current +
                                $@"update op_move_to_van_detail set move_sheet_id = null where company_id = '{company_id}' and sale_order_sheet_id = '{sheetId}' and op_id = {sheet_id};");
                }
                if(branchsId.Count > 0)
                {
                     sqlDetail += $@"insert into op_move_to_van_detail (op_id,oper_id,company_id,sale_order_sheet_id)
                    values ('{strOpId}', '{OperID}', '{company_id}', '-1');";
                }
            }
            else
            {
                if (!(move_sheet_ids.StartsWith("[") && move_sheet_ids.EndsWith("]")))       // move_sheet_ids这里前后端的数据给的不一样，做个判断，不然下面序列化有问题
                {
                    move_sheet_ids ="["+ move_sheet_ids + "]";
                }
                List<Dictionary<string, string>> lstMoveSheetIDsAndBranchIDs = JsonConvert.DeserializeObject<List<Dictionary<string, string>>>(move_sheet_ids);
                if(branchsId.Count > 0)
                {
                    foreach (KeyValuePair<string, string> kv in branchsId)
                    {
                        foreach(var moveSheet in lstMoveSheetIDsAndBranchIDs)
                        {
                            if (moveSheet.ContainsKey(kv.Key))
                            {
                                string moveSheetId = moveSheet[kv.Key].Split("_")[0];
                                sqlDetail += $@"insert into op_move_to_van_detail (op_id,oper_id,company_id,  move_sheet_id, sale_order_sheet_id,sale_order_row_branch_id)
                            values ('{strOpId}', '{OperID}', '{company_id}', '{moveSheetId}', '-1','{kv.Key}');";
                            }
                        }
                        
                    }
                }
                List<dynamic> newOrderSheetsInfo = JsonConvert.DeserializeObject<List<dynamic>>(newSheetsInfo);
                foreach(dynamic sheet in newOrderSheetsInfo)
                {
                    string saleOrderSheetId = sheet.sale_order_sheet_id;
                    string sheetId =  saleOrderSheetId.IsInvalid()? sheet.sheet_id : saleOrderSheetId;
                    if(sheet.sheetType == "TD")
                    {
                         sqlDetail += $@"insert into op_move_to_van_detail (op_id,oper_id,company_id, sale_order_sheet_id)
                               values ('{strOpId}', '{OperID}', '{company_id}', '{sheetId}');";

                    }
                    else
                    {
                        Dictionary<string, string> branchIds = new Dictionary<string, string>();
                        if (sheet.sheetRows == null)
                        {
                            sheet.sheetRows = JsonConvert.DeserializeObject<dynamic>(JsonConvert.SerializeObject(sheet.SheetRows));
                        }
                        foreach (dynamic sheetRow in sheet.sheetRows)
                        {
                            if(sheetRow.quantity > 0)
                            {
                                string branchId = sheetRow.branch_id;
                                string key = branchId.IsInvalid() ? sheet.branch_id : branchId;
                                if (!branchIds.ContainsKey(key)) branchIds.Add(key, key);
                            }
                        }
                        if (branchIds.Count > 0)
                        {
                            foreach (KeyValuePair<string, string> kv in branchIds)
                            {
                                foreach (var moveSheet in lstMoveSheetIDsAndBranchIDs)
                                {
                                    if (moveSheet.ContainsKey(kv.Key))
                                    {
                                        string moveSheetId = moveSheet[kv.Key].Split("_")[0];
                                        sqlDetail += $@"insert into op_move_to_van_detail (op_id,oper_id,company_id,  move_sheet_id, sale_order_sheet_id,sale_order_row_branch_id)
                                            values ('{strOpId}', '{OperID}', '{company_id}', '{moveSheetId}', '{sheetId}','{kv.Key}');";
                                    }
                                }
                                //if (moveSheetIDsAndBranchIDs.ContainsKey(kv.Key))
                                //{
                                //    string moveSheetId = moveSheetIDsAndBranchIDs[kv.Key].Split("_")[0];
                                //    sqlDetail += $@"insert into op_move_to_van_detail (op_id,oper_id,company_id,  move_sheet_id, sale_order_sheet_id,sale_order_row_branch_id)
                                //    values ('{strOpId}', '{OperID}', '{company_id}', '{moveSheetId}', '{sheetId}','{kv.Key}');";
                                //}
                            }
                        }
                        else
                        {
                            sqlDetail += $@"insert into op_move_to_van_detail (op_id,oper_id,company_id, sale_order_sheet_id)
                                    values ('{strOpId}', '{OperID}', '{company_id}','{sheetId}');";
                        }
                    }
                }
            }
            
            string sqlOrderStatus = sale_order_sheets_id.Split(',').Aggregate("", (current, sheetId) => current + $@"
                                       insert into sheet_status_order 
                        (company_id,    sheet_id, order_status,  senders_id,                 senders_name,                     van_id, move_stock,assign_van_id) 
                values ('{company_id}', '{sheetId}', '{order_status}', '{senders_id}', '{senders_name}', {to_van},                 {move_stock},'{strOpId}')
                on conflict(sheet_id) do update set order_status = '{order_status}', senders_id = '{senders_id}', senders_name = '{senders_name}',assign_van_id ='{strOpId}', van_id = {to_van},move_stock={move_stock};");
            if(red_flag != "")
            {
                sqlOrderStatus = "";
            }
            return sqlDetail + sqlOrderStatus;
        }
        public override string GetSaveSQL(bool bForApproveOrRed, out string err)
        {
            err = "";
            string sql = base.GetSaveSQL(bForApproveOrRed, out err);
            //if(!bForApproveOrRed) sql = sql.Replace("'zc'", "'pzc'");
            //var strOpId = "@op_id";
            //if (sheet_id != "") strOpId = sheet_id;
            //string sqlOrderStatus = sale_order_sheets_id.Split(',').Aggregate("", (current, sheetId) => current + $@"
            //                           insert into sheet_status_order 
            //        (company_id,    sheet_id, order_status,  senders_id,                 senders_name,                     van_id, sheet_print_count, sum_print_count, open_stock_print_count,move_stock,assign_van_id) 
            //values ('{company_id}', '{sheetId}', 'pzc', '{senders_id}', '{senders_name}', {to_van}, '0',                 '0',             '0',                {move_stock},'{strOpId}')
            //on conflict(sheet_id) do update set order_status = 'pzc', senders_id = '{senders_id}', senders_name = '{senders_name}',assign_van_id ='{strOpId}', van_id = {to_van},move_stock={move_stock};");
            //sql = sql + sqlOrderStatus;
            return sql;
        }
        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            base.GetInfoForApprove_SetQQ(QQ);
            string sql = "";
            if (sale_order_sheets_id != "")
            {
                sql = $@"SELECT sm.sheet_no,sso.* FROM sheet_status_order sso 
LEFT JOIN sheet_sale_order_main sm on sso.company_id = sm.company_id and sso.sheet_id = sm.sheet_id 
 where sso.company_id = {company_id} and sm.sheet_id in({sale_order_sheets_id});";
                QQ.Enqueue("order_sheets", sql);
            }


        }

        public async Task<string> GetAssignVanOpSheet(CMySbCommand cmd,string  sheet_id)
        {
            string op_id = sheet_id;

            string sheetIDs = sale_order_sheets_id;
            string saleOrderSheetIDs = "";
            string placeholderOrderSheetIDs = "";
            string otherSheetID = "";
            if (sheetIDs != "")
            {
                List<string> sheetIdList = sheetIDs.Split(",").ToList();
                foreach (string sheetId in sheetIdList)
                {
                    if (sheetId == "-1")
                    {
                        otherSheetID = "-1";
                        break;
                    }
                }
                string hasPlaceholderSql = $@"select m.sheet_id as sheet_id,sp.sheet_id as placeorder_sheet_id from sheet_sale_order_main m
left join sheet_placeholder_order_main sp on sp.company_id = m.company_id and sp.sale_order_sheet_id = m.sheet_id
where m.company_id = {company_id} and m.sheet_id in ({sheetIDs})  order by position(m.sheet_id::text in '{sheetIDs}')";
                var records = await CDbDealer.GetRecordsFromSQLAsync(hasPlaceholderSql, cmd);
                foreach (dynamic rec in records)
                {
                    string placeholderSheetId = rec.placeorder_sheet_id;
                    string saleOrderSheetId = rec.sheet_id;
                    if (placeholderSheetId.IsValid())
                    {
                        if (placeholderOrderSheetIDs != "") placeholderOrderSheetIDs += ",";
                        placeholderOrderSheetIDs += placeholderSheetId;
                    }
                    else
                    {
                        if (saleOrderSheetIDs != "") saleOrderSheetIDs += ",";
                        saleOrderSheetIDs += saleOrderSheetId;
                    }
                }
            }
            List<SheetSaleOrder> sumSheets = new List<SheetSaleOrder>();
            List<dynamic> sheetGroup1 = new List<dynamic>();
            if (saleOrderSheetIDs != "")
            {
                SheetSaleOrder.GetSheetsUsage usage = new SheetSaleOrder.GetSheetsUsage();
                usage.GetSumSheet = true;
                usage.GetEachSheet = true;
                usage.SplitUnitRows = false;
                //usage.SplitUnitRows = true;
                usage.OptionToRemember = "";
                SheetSaleOrder.GetSheetsResult res = await SheetSaleOrder.GetItemSheets<SheetSaleOrder, SheetRowSaleOrder>(cmd, OperKey, saleOrderSheetIDs, usage, true, "0", "", "");
                sumSheets.Add(res.sheetGroup[0].sheets[0]);

                foreach (dynamic resRow in res.sheetGroup[1].sheets)
                {
                    sheetGroup1.Add(resRow);
                }
            }
            if (placeholderOrderSheetIDs != "")
            {
                SheetPlaceholderOrder.GetSheetsUsage placeholderUsage = new SheetPlaceholderOrder.GetSheetsUsage();
                placeholderUsage.GetSumSheet = true;
                placeholderUsage.GetEachSheet = true;
                placeholderUsage.SplitUnitRows = false;
                //placeholderUsage.SplitUnitRows = true;
                placeholderUsage.OptionToRemember = "";
                SheetPlaceholderOrder.GetSheetsResult placeholderRes = await SheetPlaceholderOrder.GetItemSheets<SheetPlaceholderOrder, SheetRowPlaceholderOrder>(cmd, OperKey, placeholderOrderSheetIDs, placeholderUsage, true, "0", "", "");
                SheetSaleOrder sheetSaleOrderSheet = JsonConvert.DeserializeObject<SheetSaleOrder>(JsonConvert.SerializeObject(placeholderRes.sheetGroup[0].sheets[0]));
                sumSheets.Add(sheetSaleOrderSheet);
                foreach (dynamic resRow in placeholderRes.sheetGroup[1].sheets)
                {
                    sheetGroup1.Add(resRow);
                }
            }

            SheetSaleOrder sumSheet = SheetSaleOrder.GetSumSheet<SheetSaleOrder, SheetRowSaleOrder>(sumSheets, false, true, false);//group by item_id、unit_no、branch_id、branch_position、batch_id 

            if (otherSheetID != "")
            {
                string otherRowSql = $@"SELECT * FROM op_move_to_van_row ovr
left join info_branch ib on ib.company_id = {company_id} and ib.branch_id = ovr.branch_id
left join info_branch_position ibp on ibp.company_id = {company_id} and ibp.branch_position = ovr.branch_position
left join info_item_batch itb on itb.company_id = {company_id} and itb.batch_id = ovr.batch_id
left join info_item_prop ip on ip.company_id = {company_id} and ip.item_id = ovr.item_id
WHERE ovr.company_id = {company_id} and op_id ={op_id} and sheet_order_quantity = 0;";
                dynamic otherRows = await CDbDealer.GetRecordsFromSQLAsync(otherRowSql, cmd);
                SheetRowSaleOrder copyRow = sumSheet.SheetRows[0];
                foreach (dynamic row in otherRows)
                {
                    string produceDate = (string)row.produce_date;
                    SheetRowSaleOrder newRow = JsonConvert.DeserializeObject<SheetRowSaleOrder>(JsonConvert.SerializeObject(copyRow));
                    newRow.sheet_id = "-1";
                    newRow.item_name = row.item_name;
                    newRow.unit_no = row.unit_no;
                    newRow.unit_factor = CPubVars.ToDecimal(row.unit_factor);
                    newRow.branch_id = row.branch_id;
                    newRow.branch_name = row.branch_name;
                    newRow.branch_position = row.branch_position;
                    newRow.branch_position_name = row.branch_position_name;
                    newRow.produce_date = produceDate == "" ? "" : produceDate.Substring(0, 10);
                    newRow.batch_no = row.batch_no;
                    newRow.quantity = CPubVars.ToDecimal(row.quantity);
                    sumSheet.SheetRows.Add(newRow);
                }
            }
            Dictionary<string, dynamic> changedItem = new Dictionary<string, dynamic>();
            Dictionary<string, SheetRowAssignVan> AssignSheetItemDetail = new Dictionary<string, SheetRowAssignVan>();
            List<SheetRowAssignVan> AssignItems = new List<SheetRowAssignVan>();//储存多装
            List<dynamic> newSheetGroup1 = new List<dynamic>();
            List<dynamic> sumSheetSheetRows = new List<dynamic>();
            if (changedItem.Count > 0)
            {
                foreach (dynamic sheet in sheetGroup1)
                {
                    dynamic newSheet = null;
                    string saleOrderSheetId = sheet.sheet_id;
                    if (sheet.SheetType == "ZWD")
                    {
                        newSheet = JsonConvert.DeserializeObject<SheetPlaceholderOrder>(JsonConvert.SerializeObject(sheet));
                        saleOrderSheetId = newSheet.sale_order_sheet_id;
                    }
                    else newSheet = JsonConvert.DeserializeObject<SheetSaleOrder>(JsonConvert.SerializeObject(sheet));
                    if (newSheet.SheetType == "XD" || newSheet.SheetType == "ZWD")
                    {
                        List<dynamic> newSheetRows = new List<dynamic>();
                        foreach (dynamic nsr in newSheet.SheetRows)
                        {
                            dynamic sheetRow = JsonConvert.DeserializeObject<dynamic>(JsonConvert.SerializeObject(nsr));
                            if (CPubVars.ToDecimal(sheetRow.quantity) * CPubVars.ToDecimal(sheetRow.inout_flag) < 0)
                            {
                                //string sheetItemKey = saleOrderSheetId + sheetRow.item_id + sheetRow.unit_no;
                                string sheetItemKey = saleOrderSheetId + sheetRow.item_id;
                                //string noSheetItemKey = "-1" + sheetRow.item_id + sheetRow.unit_no;
                                string noSheetItemKey = "-1" + sheetRow.item_id;
                                if (changedItem.ContainsKey(noSheetItemKey))
                                {
                                    changedItem[noSheetItemKey] = sheetRow;
                                }
                                if (changedItem.ContainsKey(sheetItemKey))
                                {
                                    changedItem[sheetItemKey] = sheetRow;
                                    string branchId = sheetRow.branch_id;
                                    branchId = branchId.IsInvalid() ? newSheet.branch_id : branchId;
                                    //string sheetItemDetailKey = saleOrderSheetId + sheetRow.item_id + sheetRow.unit_no + branchId + sheetRow.branch_position + sheetRow.batch_id ;
                                    string sheetItemDetailKey = saleOrderSheetId + sheetRow.item_id + branchId + sheetRow.branch_position + sheetRow.batch_id;
                                    if (AssignSheetItemDetail.ContainsKey(sheetItemDetailKey))
                                    {
                                        sheetRow.quantity = AssignSheetItemDetail[sheetItemDetailKey].quantity;
                                        sheetRow.sub_amount = CPubVars.ToDecimal(sheetRow.quantity) * CPubVars.ToDecimal(sheetRow.real_price);
                                        AssignSheetItemDetail.Remove(sheetItemDetailKey);
                                        newSheetRows.Add(sheetRow);
                                    }
                                    else//原订单商品行被删除了
                                    {
                                        changedItem[sheetItemKey] = sheetRow;
                                    }
                                }
                                else newSheetRows.Add(sheetRow);
                            }
                            else newSheetRows.Add(sheetRow);
                        }
                        if (newSheet.SheetType == "ZWD")
                        {
                            newSheet.SheetRows = JsonConvert.DeserializeObject<List<SheetRowPlaceholderOrder>>(JsonConvert.SerializeObject(newSheetRows));
                        }
                        else
                        {

                            newSheet.SheetRows = JsonConvert.DeserializeObject<List<SheetRowSaleOrder>>(JsonConvert.SerializeObject(newSheetRows));
                        }
                    }
                    newSheetGroup1.Add(newSheet);
                }
                Dictionary<string, dynamic> newSheetRowsDic = new Dictionary<string, dynamic>();
                foreach (dynamic newSheet in newSheetGroup1)//更新sumSheets
                {
                    if (newSheet.SheetType == "XD" || newSheet.SheetType == "ZWD")
                    {
                        foreach (dynamic sheetRow in newSheet.SheetRows)
                        {
                            if (CPubVars.ToDecimal(sheetRow.quantity) * CPubVars.ToDecimal(sheetRow.inout_flag) < 0)
                            {
                                dynamic sheetRowNew = JsonConvert.DeserializeObject<dynamic>(JsonConvert.SerializeObject(sheetRow));
                                //string key = sheetRowNew.item_id + sheetRowNew.unit_no + "_" + sheetRowNew.branch_id + sheetRowNew.branch_position + sheetRowNew.batch_id;
                                string key = sheetRowNew.item_id + "_" + sheetRowNew.branch_id + sheetRowNew.branch_position + sheetRowNew.batch_id;
                                if (!newSheetRowsDic.ContainsKey(key)) newSheetRowsDic.Add(key, sheetRowNew);//订单数在前端处理
                                else
                                {
                                    newSheetRowsDic[key].quantity = CPubVars.ToDecimal(newSheetRowsDic[key].quantity) + CPubVars.ToDecimal(sheetRowNew.quantity);
                                }
                            }
                        }
                    }
                }
                sumSheetSheetRows = newSheetRowsDic.Values.ToList();
            }
            else
            {
                foreach (dynamic sheet in sheetGroup1)
                {
                    if (sheet.SheetType == "ZWD")
                    {
                        newSheetGroup1.Add(JsonConvert.DeserializeObject<SheetPlaceholderOrder>(JsonConvert.SerializeObject(sheet)));
                    }
                    else
                    {
                        newSheetGroup1.Add(JsonConvert.DeserializeObject<SheetSaleOrder>(JsonConvert.SerializeObject(sheet)));
                    }
                }
                sumSheetSheetRows = JsonConvert.DeserializeObject<List<dynamic>>(JsonConvert.SerializeObject(sumSheet.SheetRows));
            }
            foreach (SheetRowAssignVan row in AssignItems)
            {
                //string noSheetItemKey = "-1" + row.item_id + row.unit_no;
                string noSheetItemKey = "-1" + row.item_id;
                if (changedItem.ContainsKey(noSheetItemKey))
                {
                    dynamic newSheetRow = JsonConvert.DeserializeObject<dynamic>(JsonConvert.SerializeObject(changedItem[noSheetItemKey]));
                    newSheetRow.sheet_id = row.sale_order_sheet_id;
                    newSheetRow.quantity = row.quantity;
                    newSheetRow.branch_id = row.branch_id;
                    newSheetRow.branch_name = row.branch_name;
                    newSheetRow.branch_position = row.branch_position;
                    newSheetRow.branch_position_name = row.branch_position_name;
                    newSheetRow.batch_id = row.batch_id;
                    newSheetRow.produce_date = row.produce_date;
                    newSheetRow.batch_no = row.batch_no;
                    newSheetRow.sub_amount = CPubVars.ToDecimal(newSheetRow.quantity) * CPubVars.ToDecimal(newSheetRow.real_price);
                    sumSheetSheetRows.Add(newSheetRow);
                }

            }
            sumSheet.SheetRows = JsonConvert.DeserializeObject<List<SheetRowSaleOrder>>(JsonConvert.SerializeObject(sumSheetSheetRows));
            return JsonConvert.SerializeObject(newSheetGroup1);
        }

        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            base.GetInfoForSave_ReadData(dr, sqlName, bForRed);
        }
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;

            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);
            if(sqlName == "order_sheets")
            {
                dynamic rec = CDbDealer.GetRecordsFromDr(dr,false);
                if(rec.Count > 0)
                {
                    foreach(dynamic re in rec)
                    {
                        if (re.order_status == "zd")
                        {
                            info.ErrMsg = $"{re.sheet_no}订单已转单,无法装车";
                        }
                    }
                }
            }
            
            
        }
        protected override async Task<string> CheckSheetValid(CMySbCommand cmd)
        {
            var check =await base.CheckSheetValid(cmd);
            if (check != "OK") return check;
            return "OK";
        }
        protected override async Task<string> CheckForRed(CMySbCommand cmd)
        {
            if (newSheetsInfo.IsInvalid() && sheet_id.IsValid())
            {
                newSheetsInfo = await GetAssignVanOpSheet(cmd, sheet_id);
            }
            return "";
        }
        protected override async Task<CInfoForApproveBase> GetInfoForApprove(CMySbCommand cmd)
        {

            SQLQueue QQ = new SQLQueue(cmd);
            if (sheet_id != "")
            {
                if (!FIXING_ARREARS)
                {
                    string check_sql = $"select approve_time from {MainTable} where op_id={sheet_id} and company_id = {company_id}";
                    QQ.Enqueue("check_sheet", check_sql);
                }
            }

            GetInfoForApprove_SetQQ(QQ);
            string errMsg = "";
            if (QQ.Count > 0)
            {
                CMySbDataReader dr = await QQ.ExecuteReaderAsync();
                try
                {
                    while (QQ.Count > 0)
                    {
                        string tbl = QQ.Dequeue();
                        if (tbl == "check_sheet")
                        {
                            dynamic checkSheet = CDbDealer.Get1RecordFromDr(dr, false);
                            if (checkSheet != null && checkSheet.approve_time != "")
                            {
                                errMsg = "单据已审核过,不能再次审核";
                                break;
                            }
                        }
                        else
                        {
                            GetInfoForApprove_ReadData(dr, tbl,false);
                            if (InfoForApprove != null && InfoForApprove.ErrMsg != "")
                            {
                                errMsg = InfoForApprove.ErrMsg;
                                break;
                            }
                        }
                    }
                }
                catch (Exception e)
                {
                    errMsg = "读取数据失败";
                    MyLogger.LogMsg($"in GetInfoForApprove. error:${e.Message}, ${e.StackTrace}", company_id, "approve");
                }
                QQ.Clear();
            }
            if (InfoForApprove == null) InfoForApprove = new CInfoForApproveBase();
            InfoForApprove.ErrMsg = errMsg;
            return InfoForApprove;
        }
        protected override string GetApproveSQL(CInfoForApproveBase info)
        {
            string sqlOrderStock = "";
            if (move_stock.ToLower() == "true"&&red_flag.IsInvalid())
            {
                foreach (var row in SheetRows)
                {
                    var orderBranch = from_branch;
                    var order_qty = row.sheet_order_quantity * row.unit_factor;
                    if (row.batch_id.IsInvalid()) row.batch_id = "0";
                    if (row.branch_id.IsInvalid()) row.branch_id = from_branch;
                    if (row.branch_id != to_van) orderBranch = row.branch_id;
                    if (row.branch_position.IsInvalid()) row.branch_position = "0";
                    sqlOrderStock += $@"update stock set sell_pend_qty=sell_pend_qty-({order_qty}) where company_id={company_id} and branch_id={orderBranch} and branch_position={row.branch_position} and item_id={row.item_id} and batch_id ={row.batch_id};";
                }
            }
            

//            string sqlOrderStatus = sale_order_sheets_id.Split(',').Aggregate("", (current, sheetId) => current + $@"
//                           insert into sheet_status_order 
//        (company_id,    sheet_id, order_status,  senders_id,                 senders_name,                     van_id, sheet_print_count, sum_print_count, open_stock_print_count,move_stock,assign_van_id) 
//values ('{company_id}', '{sheetId}', 'zc', '{senders_id}', '{senders_name}', {to_van}, '0',                 '0',             '0',                {move_stock},'{sheet_id}')
//on conflict(sheet_id) do update set order_status = 'zc', senders_id = '{senders_id}', senders_name = '{senders_name}', van_id = {to_van},move_stock={move_stock},assign_van_id='{sheet_id}';");
            string sqlSetting = $"update company_setting set setting=setting::jsonb || '{{\"orderMoveStock\":{move_stock.ToLower()} }}'::jsonb where company_id={company_id} and (setting->>'orderMoveStock'<>'{move_stock}' or setting->'orderMoveStock' is null);";
            //string sql = sqlOrderStock + sqlOrderStatus + sqlSetting;
            //string sql = sqlOrderStock + sqlOrderStatus;
            string sql = sqlOrderStock +  sqlSetting;
            return sql;
        }
		public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info)
		{
			await base.OnSheetIDGot(cmd, sheetID, info);

            /*
            var itemsID = "";
            foreach(var row in this.SheetRows)
            {
                if (itemsID != "") itemsID += ",";
                itemsID += row.item_id;
            }
            string sql = @$"
select item_id,item_name,sell_pend_qty from stock s where s.company_id={this.company_id} and item_id in ({itemsID}) and sell_pend_qty<0";
            var records= await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string itemsName = "";
            foreach(dynamic rec in records)
            {
                if (itemsName != "") itemsName += ",";
                itemsName += rec.item_name;
            }
            if (itemsName != "")
            {
                info.ErrMsg = $"{itemsName}等商品出现负占用库存";
            }*/

		}

		protected class CInfoForApprove : CInfoForApproveBase
        {
            public string ArrearBalance = "", PrepayBalance = "";
            public List<SheetRowInventory> SheetRows = null;
        }
        public async Task<string> Delete(CMySbCommand cmd, string companyID, string op_id,bool bAutoCommit = true)
        {
            cmd.ActiveDatabase = "";
            CMySbTransaction tran = null;
            if(bAutoCommit) tran = cmd.Connection.BeginTransaction();
            if(op_id=="") return "没有获取装车单号";
            string sql = @$"
select m.op_type, m.move_sheet_id,sale_order_sheet_id,m.red_flag,mm.red_flag move_flag,sm.sheet_id sale_sheet_id,om.sheet_no order_sheet_no,sm.sheet_no sale_sheet_no,so.receipt_status,m.happen_time,so.order_status,dm.sheet_id delivery_id,so.assign_van_id,ovm.op_type assign_type,m.approve_time,so.print_time
from op_move_to_van_detail d
left join op_move_to_van_main m on d.company_id=m.company_id and d.op_id=m.op_id
left join sheet_sale_order_main om on d.company_id=om.company_id and d.sale_order_sheet_id=om.sheet_id
left join sheet_sale_main sm on om.company_id=sm.company_id and om.sheet_id=sm.order_sheet_id and sm.approve_time is not null and sm.red_flag is null
left join sheet_status_order so on d.company_id=so.company_id and d.sale_order_sheet_id=so.sheet_id
LEFT JOIN op_move_to_van_main ovm on ovm.company_id = so.company_id and ovm.op_id = so.assign_van_id 
LEFT JOIN sheet_move_main mm on m.company_id = mm.company_id and m.move_sheet_id=mm.sheet_id
LEFT JOIN sheet_delivery_detail dd on dd.company_id = d.company_id and dd.op_id = d.op_id
LEFT JOIN sheet_delivery_main dm on dm.company_id = d.company_id and dm.sheet_id = dd.sheet_id and dm.red_flag is null 
where d.company_id={companyID} and d.op_id={op_id};";
            string err = "";
            List<System.Dynamic.ExpandoObject> lstOrderSheet = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            foreach (dynamic sht in lstOrderSheet)
            {
                if (sht.sale_order_sheet_id == "-1") continue;
                if (sht.approve_time!="") err =  "装车单已审核无法删除";
                if(sht.move_sheet_id !="") err = "装车单已生成调拨单无法删除";
                if (sht.assign_van_id != op_id)
                {
                    if (sht.assign_van_id == "")  err =  $"订单{sht.order_sheet_no}已经回撤,无法删除";
                    if (sht.assign_type == "v2v")  err =  $"订单{sht.order_sheet_no}已经换车,无法删除";
                    if (sht.assign_type == "2v")  err =  $"订单{sht.order_sheet_no}已经重新装车,无法删除";
                }
            }
            string deletSql = $@"DELETE FROM op_move_to_van_main WHERE company_id = {companyID} and op_id = {op_id};
                                 DELETE FROM op_move_to_van_detail WHERE company_id = {companyID} and op_id = {op_id};
                                 DELETE FROM op_move_to_van_row WHERE company_id = {companyID} and op_id = {op_id};";
            string updateStatusOrder = "";
            foreach(dynamic sht in lstOrderSheet)
            {
                var op_type = "xd";
                if (sht.print_time != "") op_type = "dd";
                updateStatusOrder += $@"UPDATE sheet_status_order set senders_id = null , senders_name = null , order_status ='{op_type}',van_id =null ,move_stock = null ,assign_van_id = null   WHERE company_id = {companyID} and sheet_id = {sht.sale_order_sheet_id};";
            }
            if(err == "")
            {
                cmd.CommandText = sql;
                cmd.CommandText = deletSql + updateStatusOrder;
                await cmd.ExecuteScalarAsync();
                if (bAutoCommit && tran != null) tran.Commit();
            }
            else
            {
                if (bAutoCommit && tran != null) tran.Rollback();
            }
            return err;
            
        }
		protected override async Task<string> BeforeRed(CMySbCommand cmd, string sheetID, string rederID, string redBrief, CInfoForApproveBase info)
		{
			string err = ""; 
            string checkBeforeRed = @$"
select m.op_type, d.move_sheet_id,sale_order_sheet_id,m.red_flag,mm.red_flag move_flag,sm.sheet_id sale_sheet_id,om.sheet_no order_sheet_no,sm.sheet_no sale_sheet_no,so.receipt_status,m.happen_time,so.order_status,dm.sheet_id delivery_id,(CASE WHEN sale_order_sheet_id = -1 then -1 else so.assign_van_id end) as assign_van_id,ovm.op_type assign_type
from op_move_to_van_detail d
left join op_move_to_van_main m on d.company_id=m.company_id and d.op_id=m.op_id
left join sheet_sale_order_main om on d.company_id=om.company_id and d.sale_order_sheet_id=om.sheet_id
left join sheet_sale_main sm on om.company_id=sm.company_id and om.sheet_id=sm.order_sheet_id and sm.approve_time is not null and sm.red_flag is null
left join sheet_status_order so on d.company_id=so.company_id and d.sale_order_sheet_id=so.sheet_id
LEFT JOIN op_move_to_van_main ovm on ovm.company_id = so.company_id and ovm.op_id = so.assign_van_id 
LEFT JOIN sheet_move_main mm on m.company_id = mm.company_id and d.move_sheet_id=mm.sheet_id
LEFT JOIN sheet_delivery_detail dd on dd.company_id = d.company_id and dd.op_id = d.op_id
LEFT JOIN sheet_delivery_main dm on dm.company_id = d.company_id and dm.sheet_id = dd.sheet_id and dm.red_flag is null 
where d.company_id={this.company_id} and d.op_id={sheetID};";
            List<System.Dynamic.ExpandoObject> lstOrderSheet = await CDbDealer.GetRecordsFromSQLAsync(checkBeforeRed, cmd);
            List<string> moveSheetIds = new List<string>();
            foreach (dynamic sht in lstOrderSheet)
            {
                if (!string.IsNullOrEmpty(sht.move_sheet_id) && sht.move_sheet_id != "-1" && !moveSheetIds.Contains(sht.move_sheet_id)) moveSheetIds.Add(sht.move_sheet_id);
                op_type = sht.op_type;
                happen_time = sht.happen_time;
                string sale_sheet_id = sht.sale_sheet_id;
                string receipt_status = sht.receipt_status;
                if (sale_sheet_id != "")
                {
                    err = $"订单{sht.order_sheet_no}已转单,无法修改装车单";
                    break;
                }

                if (sht.assign_van_id != "-1" && sht.assign_van_id != red_sheet_id)
                {
                    if (sht.assign_van_id == "" && op_type != "retreat") err = $"装车单{sheet_no}中订单{sht.order_sheet_no}已经回撤,请先红冲回撤单";
                    if (sht.assign_type == "v2v") err = $"装车单{sheet_no}中订单{sht.order_sheet_no}已经换车,无法红冲";
                    if (sht.assign_type == "2v") err = $"装车单{sheet_no}中订单{sht.order_sheet_no}已经重新装车,无法红冲";
                    if (err != "") break;
                }
                if (sht.move_flag != "")
                {
                    err = "调拨单已红冲，无法撤销装车";
                    break;
                }
            }

			string sql = "";
			//if (move_stock == "true" && moveSheetIds.Count != 0)
			if (move_stock == "true"&& err=="")
            {
                if (moveSheetIds.Count != 0)
                {
                    SheetMove moveSheet = new SheetMove(LOAD_PURPOSE.SHOW);
                    moveSheet.isRedAndChange = isRedAndChange;
                    foreach (string moveSheetId in moveSheetIds)
                    {
                        err = await moveSheet.Red(cmd, this.company_id, moveSheetId, rederID, redBrief, false);
                    }
                }
                
                if (err == "")
                {
                    foreach (var row in SheetRows)
                    {
                        var order_qty = row.sheet_order_quantity * row.unit_factor;
                        sql += $"update stock set sell_pend_qty=sell_pend_qty+({order_qty}) where company_id={this.company_id} and branch_id={row.branch_id} and item_id={row.item_id} and batch_id = {row.batch_id} and branch_position = {row.branch_position};";
                    }
                }

            }

            if (err == "")
            {
     
               // sql += $"update op_move_to_van_main set red_flag='1' where company_id={this.company_id} and op_id={sheet_id};";
                sql += $"update sheet_status_order set order_status=case when sheet_print_count>0 then 'dd' else 'xd' end , senders_id  = null , senders_name = null , van_id = null , move_stock = null,assign_van_id = null where company_id={this.company_id} and sheet_id in ({sale_order_sheets_id});";
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync(); 
            }
            return err;
        }
   
        public override string GetSheetCharactor()
        {
            string res = this.company_id + "_" + this.OperID + "_" + this.from_branch + "_" + this.to_van + "_" + this.move_stock;
            foreach (var row in SheetRows)
            {
                res += row.item_id + "_" + row.quantity;
            }
            return res;
        }
        public Dictionary<string,Dictionary<string, dynamic>> GetSheetRowsInfo(List<dynamic> orderSheets)
        {
            Dictionary<string, Dictionary<string, dynamic>> dic = new Dictionary<string, Dictionary<string, dynamic>>();
            foreach(dynamic sheet in orderSheets)
            {
                if(sheet.sheetType == "XD" || sheet.sheetType == "ZWD")
                {
                    string saleOrderSheetId = sheet.sale_order_sheet_id;
                    string sheetKey = saleOrderSheetId.IsInvalid() ? sheet.sheet_id : saleOrderSheetId;
                    dic[sheetKey] = new Dictionary<string, dynamic>();
                    foreach(dynamic sheetRow in sheet.sheetRows)
                    {
                        if(CPubVars.ToDecimal(sheetRow.inout_flag) * CPubVars.ToDecimal(sheetRow.quantity) < 0)
                        {
                            dynamic newSheetRow = JsonConvert.DeserializeObject<dynamic>(JsonConvert.SerializeObject(sheetRow));
                            newSheetRow.sheetType = sheet.sheetType;
                            string branchId = newSheetRow.branch_id;
                            string rowBranchId = branchId.IsInvalid() ? sheet.branch_id : newSheetRow.branch_id;
                            string itemKey = newSheetRow.item_id + newSheetRow.unit_no + rowBranchId + newSheetRow.branch_position + newSheetRow.produce_date + newSheetRow.batch_no;
                            if (dic[sheetKey].ContainsKey(itemKey))
                            {
                                dic[sheetKey][itemKey].quantity = Convert.ToString(CPubVars.ToDecimal(dic[sheetKey][itemKey].quantity) + CPubVars.ToDecimal(newSheetRow.quantity));
                            }
                            else
                            {
                                dic[sheetKey][itemKey] = newSheetRow;
                            }
                        }
                    }
                }
                
            }
            return dic;
        }
		public override async Task<string> OnSheetBeforeSave(CMySbCommand cmd, CInfoForApproveBase info)
		{ 
           
            string msg = await CheckBatch(cmd,false);
            if (msg != "") return msg;

            //List<dynamic> orderSheets = JsonConvert.DeserializeObject<List<dynamic>>(sheetsInfo);
            List<dynamic> orderSheets = JsonConvert.DeserializeObject<List<dynamic>>(newSheetsInfo);
            Dictionary<string,Dictionary<string, dynamic>> orderSheetsDic = GetSheetRowsInfo(orderSheets);
            foreach(SheetRowAssignVan sheetRow in this.SheetRows)//更新sheet_order_quantity
            {
                if (sheetRow.sale_order_sheet_id == "-1")
                {
                    sheetRow.sheet_order_quantity = 0;
                }
                else
                {
                    string sheetKey = sheetRow.sale_order_sheet_id;
                    string itemKey = sheetRow.item_id + sheetRow.unit_no + sheetRow.branch_id + sheetRow.branch_position + sheetRow.produce_date + sheetRow.batch_no;
                    if (orderSheetsDic[sheetKey].ContainsKey(itemKey))
                    {
                        if((orderSheetsDic[sheetKey][itemKey].sheetType == "XD" || orderSheetsDic[sheetKey][itemKey].sheetType == "ZWD") 
                        && CPubVars.ToDecimal(orderSheetsDic[sheetKey][itemKey].quantity) *  CPubVars.ToDecimal(orderSheetsDic[sheetKey][itemKey].inout_flag) < 0
                        )
                        {
                            sheetRow.sheet_order_quantity = CPubVars.ToDecimal(orderSheetsDic[sheetKey][itemKey].quantity);
                        }
                    }
                    else{
                        sheetRow.sheet_order_quantity = 0;
                    }
                }
            }
            
            return msg;
        }
        public override async Task<string> OnSheetBeforeApprove(CMySbCommand cmd,  CInfoForApproveBase info)
        {
            string err = ""; 
      
            string saleOrderSheetsId = "";
            List<dynamic> orderSheets = JsonConvert.DeserializeObject<List<dynamic>>(sheetsInfo);
            List<dynamic> newOrderSheets = JsonConvert.DeserializeObject<List<dynamic>>(newSheetsInfo);
            Dictionary<string,Dictionary<string, dynamic>> orderSheetsDic = GetSheetRowsInfo(orderSheets);
            Dictionary<string,Dictionary<string, dynamic>> newOrderSheetsDic = GetSheetRowsInfo(newOrderSheets);
            foreach(SheetRowAssignVan sheetRow in this.SheetRows)
            {
                if (sheetRow.sale_order_sheet_id == "-1")
                {
                    sheetRow.sheet_order_quantity = 0;
                }
                else
                {
                    //sheetRow.sheet_order_quantity = sheetRow.quantity;
                    string sheetKey = sheetRow.sale_order_sheet_id;
                    string itemKey = sheetRow.item_id + sheetRow.unit_no + sheetRow.branch_id + sheetRow.branch_position + sheetRow.produce_date + sheetRow.batch_no;
                    if (newOrderSheetsDic[sheetKey].ContainsKey(itemKey))
                    {
                        if ((newOrderSheetsDic[sheetKey][itemKey].sheetType == "XD" || newOrderSheetsDic[sheetKey][itemKey].sheetType == "ZWD")
                        && CPubVars.ToDecimal(newOrderSheetsDic[sheetKey][itemKey].quantity) * CPubVars.ToDecimal(newOrderSheetsDic[sheetKey][itemKey].inout_flag) < 0
                        )
                        {
                            sheetRow.sheet_order_quantity = CPubVars.ToDecimal(newOrderSheetsDic[sheetKey][itemKey].quantity);
                        }
                    }
                    else
                    {
                        sheetRow.sheet_order_quantity = 0;
                    }

                }
                    
            }
            Dictionary<string, bool> changedSheet = new Dictionary<string, bool>();
            foreach(KeyValuePair<string, Dictionary<string, dynamic>> kv in newOrderSheetsDic)
            {
                string sheetKey = kv.Key;
                foreach(KeyValuePair<string,dynamic> kv2 in newOrderSheetsDic[sheetKey])
                {
                    string itemKey = kv2.Key;
                    if (orderSheetsDic[sheetKey].ContainsKey(itemKey)){
                        if (orderSheetsDic[sheetKey][itemKey].quantity != newOrderSheetsDic[sheetKey][itemKey].quantity)
                        {
                            changedSheet[sheetKey] = true;
                            break;
                        }
                    }
                    else
                    {
                        changedSheet[sheetKey] = true;
                        break;
                    }
                }
            }
            foreach (dynamic sheet in newOrderSheets)
            {
                string saleOrderSheetId = sheet.sale_order_sheet_id;
                string sheetKey = saleOrderSheetId.IsInvalid() ? sheet.sheet_id : sheet.sale_order_sheet_id;
                string oldSheetId = sheet.sheet_id;
                if (saleOrderSheetId.IsValid())
                {
                    oldSheetId = saleOrderSheetId;
                }
                SheetSaleOrder saleSheet = new SheetSaleOrder(SHEET_RETURN.NOT_RETURN, LOAD_PURPOSE.SHOW);
                await saleSheet.Load(cmd, company_id, oldSheetId);
                if (changedSheet.ContainsKey(sheetKey) && changedSheet[sheetKey]&&saleSheet.placeholder_sheet_id.IsValid())
                {
                    List<SheetRowPlaceholderOrder> paceholderSheetRows = JsonConvert.DeserializeObject<List<SheetRowPlaceholderOrder>>(JsonConvert.SerializeObject(sheet.sheetRows));//新占位单sheetRows
           
                    SheetPlaceholderOrder plSheet = JsonConvert.DeserializeObject<SheetPlaceholderOrder>(JsonConvert.SerializeObject(saleSheet));
                    plSheet.SheetRows = paceholderSheetRows;
                    plSheet.company_id = company_id;
                    plSheet.OperID = oper_id;
                    plSheet.approver_id = "";
                    plSheet.approve_time = "";
                    plSheet.happen_time = "";
                    plSheet.sheet_id = "";
                    plSheet.sheet_no = "";
                    plSheet.SheetType = "ZWD";
                    saleSheet.company_id = company_id;
                    saleSheet.OperID = oper_id;
                    saleSheet.OperKey = OperKey;
                    saleSheet.approver_id = "";
                    saleSheet.approve_time = "";
                    saleSheet.sheet_id = "";
                    saleSheet.placeholderOrderSheet = plSheet;
                    saleSheet.old_sheet_id = oldSheetId;
                    err = await saleSheet.RedAndChange<SheetSaleOrder>(cmd, false);
                    if (err == "")
                    {
                        if(sheet.sheetType == "ZWD")
                        {
                            sheet.sale_order_sheet_id = saleSheet.sheet_id;
                            sheet.sheet_id = saleSheet.placeholder_sheet_id;
                        }
                        else
                        {
                            sheet.sheet_id = saleSheet.sheet_id;
                        }
                        this.sheet_id = "";
                        SheetRows.ForEach(t =>
                        {
                            if (t.sale_order_sheet_id == oldSheetId)
                            {
                                t.sale_order_sheet_id = saleSheet.sheet_id;
                            }
                        });
                        if (saleOrderSheetsId == "") saleOrderSheetsId += saleSheet.sheet_id;
                        else saleOrderSheetsId += "," + saleSheet.sheet_id;
                    }
                }
                else
                {
                    string sheetId =saleOrderSheetId.IsInvalid() ? sheet.sheet_id : sheet.sale_order_sheet_id;
                    if (saleOrderSheetsId == "") saleOrderSheetsId += sheetId;
                    else saleOrderSheetsId += "," + sheetId;
                    SheetRows.ForEach(t =>
                    {
                        if (t.sale_order_sheet_id == sheet.sheet_id.ToString())
                        {
                            //t.sheet_id = sheetId;
                            t.sale_order_sheet_id = sheetId;
                        }
                    });
                }
            }
            newSheetsInfo = JsonConvert.SerializeObject(newOrderSheets);
            sale_order_sheets_id = saleOrderSheetsId;
            if (err == "")
            {
                List<SheetMove> sheetMoves = new List<SheetMove>();
                Dictionary<string, List<SheetRowAssignVan>> sheetAssignVanRows = new Dictionary<string, List<SheetRowAssignVan>>();
                if (move_stock.ToLower() == "true" && SheetRows.Count() > 0) //分仓=》多个调拨单
                {
                    foreach (SheetRowAssignVan row in SheetRows)
                    {
                        SheetRowAssignVan newRow = JsonConvert.DeserializeObject<SheetRowAssignVan>(JsonConvert.SerializeObject(row));
                        if (newRow.quantity == 0) continue;
                        string rowFromBranchPosition = newRow.branch_position;
                        newRow.from_branch_position = rowFromBranchPosition;
                        newRow.to_branch_position = "0";
                        if (sheetAssignVanRows.ContainsKey(newRow.branch_id))
                        {
                            int findI = sheetAssignVanRows[newRow.branch_id].FindIndex(t => t.from_branch_position == rowFromBranchPosition && t.batch_id == newRow.batch_id && t.item_id == newRow.item_id && t.unit_no == newRow.unit_no);
                            if (findI > -1) sheetAssignVanRows[newRow.branch_id][findI].quantity += newRow.quantity;
                            else sheetAssignVanRows[newRow.branch_id].Add(newRow);
                        }
                        else
                        {
                            sheetAssignVanRows.Add(newRow.branch_id, new List<SheetRowAssignVan>() { newRow });
                        }
                    }
                    foreach (KeyValuePair<string, List<SheetRowAssignVan>> kv in sheetAssignVanRows)
                    {
                        if (kv.Key == to_van) continue;
                        var sheetmove = new SheetMove(LOAD_PURPOSE.SHOW);
                        sheetmove.from_branch_id = kv.Key;
                        sheetmove.to_branch_id = to_van;
                        sheetmove.SheetRows = JsonConvert.DeserializeObject<List<SheetRowMove>>(JsonConvert.SerializeObject(kv.Value));
                        sheetmove.company_id = company_id;
                        sheetmove.happen_time = CPubVars.GetDateText(DateTime.Now);
                        sheetmove.maker_id = OperID;
                        sheetmove.make_time = CPubVars.GetDateText(DateTime.Now);
                        sheetmove.submit_time = CPubVars.GetDateText(DateTime.Now);
                        sheetmove.OperKey = OperKey;
                        sheetmove.OperID = OperID;
                        sheetmove.SaleOrderSheetIDs = sale_order_sheets_id;
                        sheetmove.SaleOrderSheetNos = sale_order_sheets_no;
                        sheetmove.assign_van = Assign_Van.ASSIGN_VAN;
                        sheetmove.sheet_id = move_sheet_id == "null" ? "" : move_sheet_id;
                        sheetmove.sheet_no = move_sheet_no == "" ? "" : move_sheet_no;
                        sheetMoves.Add(sheetmove);
                    }
                }
                Dictionary<string, string> moveSheetIDsAndBranchIDs = new Dictionary<string, string>();
                foreach (SheetMove sheetmove in sheetMoves)
                {
                    err = await sheetmove.SaveAndApprove(cmd, false);
                    if (err == "")
                    {
                        string moveSheetIdNoStr = sheetmove.sheet_id + "_" + sheetmove.sheet_no;
                        moveSheetIDsAndBranchIDs.Add(sheetmove.from_branch_id,moveSheetIdNoStr);
                       
                        move_sheet_id = sheetmove.sheet_id;
                        move_sheet_no = sheetmove.sheet_no;
                    }
                }
                if (sheetMoves.Count > 0) { move_sheet_ids = JsonConvert.SerializeObject(moveSheetIDsAndBranchIDs); }
            }
            return err;
        }

        public override async Task LoadInfoForPrint(CMySbCommand cmd, bool smallUnitBarcode, bool bLoadCompanySetting = true, dynamic printTemplate = null)
        {
            await base.LoadInfoForPrint(cmd, smallUnitBarcode, bLoadCompanySetting); 
            
        }

        //protected override async Task<string> CheckSheetRowValid(CMySbCommand cmd)
        //{
        //    foreach (dynamic row in this.SheetRows)
        //    {
        //        string branchID = row.branch_id;
        //        string branchName = row.branch_name;
        //        if (branchID.IsInvalid())
        //        {
        //            branchID = branch_id;
        //            branchName = branch_name;
        //        }
        //        if (row.GetType().GetProperty("branch_position") == null||row.branch_position == "0" || string.IsNullOrWhiteSpace(row.branch_position)) continue;
        //        //if (row.GetType().GetProperty("branch_position") == null || row.branch_position == "0" || row.branch_position == null || row.branch_position == "") continue;
        //        dynamic record = await CDbDealer.Get1RecordFromSQLAsync($"select flow_id from info_branch_position where company_id = {company_id} and branch_position = {row.branch_position} and branch_id = {branchID};", cmd);
        //        if (record == null)
        //        {
        //            return $"{branchName}不存在库位：{row.branch_position_name}";
        //        }
        //    }
        //    return "OK";

        //}
        protected dynamic GetNewSheets(dynamic sheetRows)
        {
            Dictionary<string, List<dynamic>> sheets = new Dictionary<string, List<dynamic>>();
            //List<dynamic> newSheetRows = new List<dynamic>();
            //List<>
            foreach (dynamic sheetRow in sheetRows)
            {
                sheetRow.sheet_order_quantity = sheetRow.quantity;
                if (sheets.ContainsKey(sheetRow.sheet_no.ToString() + "," + sheetRow.sheet_id.ToString()))
                {
                    sheets[sheetRow.sheet_no.ToString() + "," + sheetRow.sheet_id.ToString()].Add(sheetRow);
                }
                else
                {
                    sheets.Add(sheetRow.sheet_no.ToString() + "," + sheetRow.sheet_id.ToString(), new List<dynamic>() { sheetRow });
                }
            }
            return sheets;
        }
        public async Task<string> CheckBatch(CMySbCommand cmd ,bool bAutoCommit = true)
        {
            string msg = "";
            string insertSql = "";
            string insertValue = "";
            string selectSql = "";
            string selectValue = "";
            Dictionary<string, dynamic> batchDic = new Dictionary<string, dynamic>();
            Dictionary<string, string> sheetRowBatch = new Dictionary<string, string>();
            try
            {

                foreach (SheetRowAssignVan row in SheetRows)
                {
                    if (row.produce_date.IsInvalid())
                    {
                        continue;
                    }
                    string key = row.produce_date + row.batch_no;
                    if (!batchDic.ContainsKey(key))
                    {
                        batchDic[key] = new { produce_date = row.produce_date, batch_no = row.batch_no };
                        if(selectValue!="")  selectValue += ",";
                        selectValue += $@"('{row.produce_date}','{row.batch_no}')";
                    }
                }
                if (selectValue != "") selectSql = $@"select * from info_item_batch where company_id = {company_id} and (substring(produce_date::text,1,10),batch_no) in ({selectValue});";
                if (selectSql != "")
                {
                    List<ExpandoObject> selectRec = await CDbDealer.GetRecordsFromSQLAsync(selectSql, cmd);
                    foreach (dynamic row in selectRec)
                    {
                        string produceDate = row.produce_date;
                        string batchNo = row.batch_no;
                        produceDate = produceDate.Substring(0, 10);
                        string key = produceDate + batchNo;
                        sheetRowBatch.Add(key, row.batch_id);
                        if (batchDic.ContainsKey(key)) batchDic.Remove(key);
                    }
                    foreach(KeyValuePair<string,dynamic> kv in batchDic)
                    {
                        string produceDate = kv.Value.produce_date;
                        string batchNo = kv.Value.batch_no;
                        if(insertValue!="")  insertValue += ",";
                        insertValue += $@"({company_id},'{produceDate}','{batchNo}')";
                    }
                    if (insertValue!="")//INSERT INTO
                    {
                        insertSql += $"insert into info_item_batch (company_id,produce_date,batch_no) values {insertValue} on CONFLICT(company_id,produce_date,batch_no) DO NOTHING RETURNING batch_id,produce_date,batch_no;";
                        List<ExpandoObject> insertRec = await CDbDealer.GetRecordsFromSQLAsync(insertSql, cmd);
                        foreach (dynamic row in insertRec)
                        {
                            string produceDate = row.produce_date;
                            string batchNo = row.batch_no;
                            produceDate = produceDate.Substring(0, 10);
                            string key = produceDate + batchNo;
                            sheetRowBatch.Add(key, row.batch_id);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                msg = "生产日期/批次错误";
                MyLogger.LogMsg(msg + e.Message + e.StackTrace + "SQL:" + cmd.CommandText, company_id, "produce_date");
            }
            if (msg == "")
            {
                foreach (SheetRowAssignVan row in SheetRows)
                {
                    if (row.produce_date.IsInvalid())
                    {
                        row.batch_id = "0";
                        continue;
                    }
                    string key = row.produce_date + row.batch_no;
                    if (sheetRowBatch.ContainsKey(key))
                    {
                        row.batch_id = sheetRowBatch[key];
                    }
                    else
                    {
                        msg = "生产日期/批次错误";
                    }
                }
            }
            return msg;
        }
    }
}
