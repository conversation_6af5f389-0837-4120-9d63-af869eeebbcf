using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using NPOI.SS.Formula.Functions;
using System.Net;
using System.IO;
using System.Text;
using ArtisanManage.MyJXC;
using NPOI.Util;
using Microsoft.IdentityModel.Tokens;

namespace ArtisanManage.AppController
{

    /// <summary>
    /// 一、销量走势 
    /// 二、业务员排行
    /// 三、品牌销量汇总
    /// 四、客户销量汇总
    /// 五、商品库存表（待定）
    /// 六、预收款报表 根据（时间，业务员，预收款方）
    /// 七、热销商品排行
    /// </summary>



    [Route("AppApi/[controller]/[action]")]
    public class ReportController : QueryController
    {
        public ReportController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        public async Task<JsonResult> sendNotify()
        {
            string result = "";
            string url = "https://api.xmpush.xiaomi.com/v2/message/all";
            HttpWebRequest req = (HttpWebRequest)WebRequest.Create(url);
            req.Method = "POST";
            req.ContentType = "application/x-www-form-urlencoded";
            #region 添加Post 参数
            StringBuilder builder = new StringBuilder();
            int i = 0;
            Dictionary<string, string> dic = new Dictionary<string, string>();
            dic.Add("description", "notification_description");
            dic.Add("payload", "营匠APP，助您发大财");
            dic.Add("restricted_package_name", "com.yingjiang.app");
            dic.Add("title", "营匠APP");
            dic.Add("notify_type", "2");
            dic.Add("time_to_live", "1000");

            foreach (var item in dic)
            {
                if (i > 0)
                    builder.Append("&");
                builder.AppendFormat("{0}={1}", item.Key, item.Value);
                i++;
            }
            byte[] data = Encoding.UTF8.GetBytes(builder.ToString());
            req.ContentLength = data.Length;
            using (Stream reqStream = req.GetRequestStream())
            {
                reqStream.Write(data, 0, data.Length);
                reqStream.Close();
            }
            #endregion
            HttpWebResponse resp = (HttpWebResponse)req.GetResponse();
            using (Stream stream = resp.GetResponseStream())
            {
                //获取响应内容
                using (StreamReader reader = new StreamReader(stream, Encoding.UTF8))
                {
                    result = reader.ReadToEnd();
                }
            }

            return Json(new { result });
        }
        /// <summary>
        /// 销量走势
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="dateType">year:查半年，month:查30天</param>
        /// <returns></returns>
        /// 利润=收入-成本（含赠）-销售优惠
        [HttpGet]
        public async Task<JsonResult> GetTrends(string operKey, string viewRange, string sellerID, string dateType, int startRow, int pageSize, string startTime, string endTime, string costPriceType, string departID, string regionID, string clientID = "")
        {
            string departCondi = "";
            string departLeftJoinCondi = "";
            string regionCondi = "";
            string regionLeftJoinCondi = "";
            string clientCondi = "";
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            cmd.CommandText = $@"select cost_price_type from company_setting where company_id={companyID};";
            var dr = await cmd.ExecuteReaderAsync();
            if (costPriceType.IsInvalid())
            {
                while (dr.Read())
                {
                    costPriceType = CPubVars.GetTextFromDr(dr, "cost_price_type");
                }
            }
            dr.Close();
            if (costPriceType.IsInvalid()) costPriceType = "3";
            //var dateFmt = "MM-dd";
            //var serialDate = $"full join (select to_char(b,'MM-DD') as t,to_char(b,'YYYY-MM-DD') as ty from generate_series(to_timestamp('{startTime}','YYYY-MM-DD'),to_timestamp('{endTime}','YYYY-MM-DD'),'1 days') as b) b on a.interval = b.t";
            var dateFmt = "YYYY-MM-DD";
            var serialDate = $"full join (select to_char(b,'YYYY-MM-DD') as t,to_char(b,'YYYY-MM-DD') as ty from generate_series(to_timestamp('{startTime}','YYYY-MM-DD'),to_timestamp('{endTime}','YYYY-MM-DD'),'1 days') as b) b on a.interval = b.t";
            var interval = " COALESCE(to_char(a.interval::date, 'MMDD') , to_char(b.t::date, 'MMDD')) as interval";
            var intervalWithYear = " COALESCE(a.interval, b.t) as intervalWithYear";
            if (dateType == "year")
            {
                dateFmt = "YY-MM";
                DateTime dtStart = Convert.ToDateTime(startTime);
                startTime = dtStart.Year + "-" + dtStart.Month + "-" + "1";

                serialDate = $"full join (select to_char(b,'YY-MM') as t,to_char(b,'YY-MM') as ty from generate_series(to_timestamp('{startTime}','YY-MM'),to_timestamp('{endTime}','YY-MM'),'1 months') as b) b on a.interval = b.t";
                intervalWithYear = " COALESCE(a.interval , b.t) as intervalWithYear";
                interval = " COALESCE(a.interval , b.t) as interval";
            }

            var condi = $"where d.company_id = {companyID}  and approve_time is not null and red_flag is null and (trade_type NOT IN ( 'J', 'H' ) or trade_type is null)  ";
            if (startTime.IsValid() && endTime.IsValid()) condi += $" and (m.happen_time >= '{CPubVars.GetDateText(startTime)}' and m.happen_time <= '{CPubVars.GetDateText(endTime)}') and (d.happen_time >= '{CPubVars.GetDateText(startTime)}' and d.happen_time <= '{CPubVars.GetDateText(endTime)}')";
            var todayCondi = $"where d.company_id = {companyID} and approve_time is not null and red_flag is null  ";
            string sToday = CPubVars.GetDateText(DateTime.Now.Date);

            todayCondi += $" and m.happen_time >=  '{CPubVars.GetDateText(sToday)}' and d.happen_time >=  '{CPubVars.GetDateText(sToday)}'";
            /*if (viewRange == "self")
            {
                condi += $" and seller_id={operID} ";
                todayCondi += $" and seller_id={operID} "; 
            }*/

            if (sellerID.IsValid())
            {
                condi += $" and seller_id={sellerID} ";
                todayCondi += $" and seller_id={sellerID} ";
            }
            if (departID.IsValid())
            {
                departLeftJoinCondi = "left join info_operator io on m.company_id=io.company_id and m.seller_id=io.oper_id  ";
                departCondi = $" and io.depart_path like '%/{departID}/%' ";
            }
            if (regionID.IsValid())
            {
                regionLeftJoinCondi = "left join info_supcust isp on m.company_id=isp.company_id and m.supcust_id=isp.supcust_id  ";
                regionCondi = $" and isp.other_region like '%/{regionID}/%' ";
            }
            if (clientID.IsValid())
            {
                clientCondi = $" and m.supcust_id = '{clientID}' ";
            }
			string sumFlds = "";
			string costFld = @$"
case when {costPriceType} = 1 then round(cast(spec_profit as   numeric),2) 
     when {costPriceType} = 2 then round(cast(avg_profit as    numeric),2)
     when {costPriceType} = 4 then round(cast(recent_profit as numeric),2) 
                              else round(cast(buy_profit as    numeric),2) 
end";
            if (startRow == 0)
            {
                sumFlds = @$"
,count(*) over()
,round((sum(X_amount) over())::numeric,2) as saleTotal
,round((sum(T_amount) over())::numeric,2) as returnTotal
,round((sum(saleSum) over())::numeric,2)  as total
,round((sum({costFld}) over() )::numeric,2) profitTotal

";
            }

				var sql_noLimit = $@"
SELECT {interval}, {intervalWithYear},COALESCE(X_amount,0) X_amount,COALESCE(T_amount,0) T_amount,COALESCE(saleSum,0) saleSum,
    COALESCE({costFld},0) profit {sumFlds}
FROM 
(
    SELECT round(sum(X_amount)::numeric,2) X_amount,round(sum(T_amount)::numeric,2) T_amount,round(sum(X_amount+T_amount)::numeric,2) as saleSum,to_char(happen_time,'{dateFmt}') as interval,
           round((sum(total)-sum(disc)-sum(spec_cost))::numeric,2) spec_profit,round((sum(total)-sum(disc)-sum(avg_cost))::numeric,2) avg_profit,round((sum(total)-sum(disc)-sum(buy_cost))::numeric,2) buy_profit,round((sum(total) - sum(disc) - sum(recent_cost))::numeric, 2)  recent_profit
    FROM 
    (
        SELECT d.sheet_id,m.happen_time,
			    sum(case when quantity*inout_flag<=0 then sub_amount*inout_flag*(-1) else 0 end) as X_amount,
			    sum(case when quantity*inout_flag>0 then sub_amount*inout_flag*(-1) else 0 end) as T_amount, 
                sum(case when d.row_index=1 then COALESCE(now_disc_amount,0)*inout_flag*(-1) else 0 end) disc,
			    sum(sub_amount*inout_flag*(-1)) total,
                sum(quantity*inout_flag*(-1)*unit_factor*COALESCE(d.cost_price_avg,0)) avg_cost,
                sum(quantity*inout_flag*(-1)*unit_factor*COALESCE(p.cost_price_spec,0)) spec_cost,
                sum(quantity*inout_flag*(-1)*unit_factor*COALESCE(d.cost_price_buy,0)) buy_cost,
                sum(quantity*inout_flag*(-1)*unit_factor*COALESCE(d.cost_price_recent,0)) recent_cost
        FROM sheet_sale_detail d 
            left join sheet_sale_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID}
		    left join info_item_prop p on p.item_id = d.item_id and d.company_id = {companyID}
            {departLeftJoinCondi} {regionLeftJoinCondi}
            {condi} {departCondi} {regionCondi} {clientCondi}
        GROUP BY d.sheet_id,m.happen_time  
    ) d 
    GROUP BY interval ORDER BY interval DESC
) a 
            {serialDate} order by b.ty desc";

            var sql = sql_noLimit + $" limit {pageSize} offset {startRow} ";
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("data", sql);
/*
			sql = $@"
SELECT round(sum(X_amount)::numeric,2) as saleTotal,round(sum(T_amount)::numeric,2) as returnTotal,round(sum(saleSum)::numeric,2) total,round(sum(profit)::numeric,2) profitTotal 
FROM 
(
     SELECT X_amount,T_amount,saleSum,(case when {costPriceType} = 1 then spec_profit when {costPriceType}=2 then avg_profit else buy_profit end) profit
     FROM 
     (
        SELECT round(sum(X_amount)::numeric,2) X_amount,round(sum(T_amount)::numeric,2) T_amount,round(sum(X_amount+T_amount)::numeric,2) as saleSum,
                                        round((sum(total)-sum(disc)-sum(spec_cost))::numeric,2) spec_profit,round((sum(total)-sum(disc)-sum(avg_cost))::numeric,2) avg_profit,round((sum(total)-sum(disc)-sum(buy_cost))::numeric,2) buy_profit
         FROM 
            ( 
             SELECT d.sheet_id,
                    sum(case when quantity*inout_flag<=0 then sub_amount*inout_flag*(-1) else 0 end) as X_amount,
			        sum(case when quantity*inout_flag>0 then sub_amount*inout_flag*(-1) else 0 end) as T_amount, 
                    sum(case when d.row_index=1 then COALESCE(now_disc_amount,0)*inout_flag*(-1) else 0 end) disc,
		            sum(sub_amount*inout_flag*(-1)) total,
                sum(quantity*inout_flag*(-1)*unit_factor*COALESCE(d.cost_price_avg,0)) avg_cost,
                sum(quantity*inout_flag*(-1)*unit_factor*COALESCE(p.cost_price_spec,0)) spec_cost,
                sum(quantity*inout_flag*(-1)*unit_factor*COALESCE(d.cost_price_buy,0)) buy_cost          
             FROM sheet_sale_detail d left join sheet_sale_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID} 
                      left join info_item_prop p on p.item_id = d.item_id
             {departLeftJoinCondi} {regionLeftJoinCondi}
			 {condi} {departCondi} {regionCondi} {clientCondi} 
             GROUP BY d.sheet_id  ) d  ) a ) tt ;";
			QQ.Enqueue("total", sql);
                */

			sql = $@"
SELECT round(sum(X_amount)::numeric,2) as saleTotal,round(sum(T_amount)::numeric,2) as returnTotal,round(sum(saleSum)::numeric,2) total,round(sum(profit)::numeric,2) profitTotal 
FROM 
    (SELECT X_amount,T_amount,saleSum,{costFld} profit
     FROM 
        (
         SELECT round(sum(X_amount)::numeric,2) X_amount,round(sum(T_amount)::numeric,2) T_amount,round(sum(X_amount+T_amount)::numeric,2) as saleSum,
                                        round((sum(total)-sum(disc)-sum(spec_cost))::numeric,2) spec_profit,round((sum(total)-sum(disc)-sum(avg_cost))::numeric,2) avg_profit,round((sum(total)-sum(disc)-sum(buy_cost))::numeric,2) buy_profit, round((sum(total) - sum(disc) - sum(recent_cost))::numeric, 2)  recent_profit
         FROM 
         (
             SELECT d.sheet_id,
				    sum(case when quantity*inout_flag<=0 then sub_amount*inout_flag*(-1) else 0 end) as X_amount,
				    sum(case when quantity*inout_flag>0 then sub_amount*inout_flag*(-1) else 0 end) as T_amount, 
                    sum(case when d.row_index=1 then COALESCE(now_disc_amount,0)*inout_flag*(-1) else 0 end) disc,
				    sum(sub_amount*inout_flag*(-1)) total,
                sum(quantity*inout_flag*(-1)*unit_factor*COALESCE(d.cost_price_avg,0)) avg_cost,
                sum(quantity*inout_flag*(-1)*unit_factor*COALESCE(p.cost_price_spec,0)) spec_cost,
                sum(quantity*inout_flag*(-1)*unit_factor*COALESCE(d.cost_price_buy,0)) buy_cost,
                sum(quantity*inout_flag*(-1)*unit_factor*COALESCE(d.cost_price_recent,0)) recent_cost
			 FROM sheet_sale_detail d 
                    left join sheet_sale_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID} 
                    left join info_item_prop p on p.item_id = d.item_id   
                    {departLeftJoinCondi} 
                    {regionLeftJoinCondi}
                    {todayCondi} 
                    {departCondi} {regionCondi} {clientCondi}  
             GROUP BY d.sheet_id  ) d  ) a ) tt";
            QQ.Enqueue("todayTotal", sql);



           // sql = @$"SELECT count(interval) as count FROM ({sql_noLimit}) t ";
           // QQ.Enqueue("count", sql);
            List<ExpandoObject> records = null;
			dynamic total = null;
			ExpandoObject todayTotal = null;
            dr = await QQ.ExecuteReaderAsync();
            var count = "0";
	
			while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    records = CDbDealer.GetRecordsFromDr(dr, false);
						if (startRow == 0)
						{

							if (records.Count > 0)
							{
								dynamic rec = records[0];
							    total = new
								{
									rec.saletotal,
									rec.returntotal,
									rec.total,
									rec.profittotal
									 
								};
								 
								count = rec.count;

							}
							else
							{
							    total = new
								{
									saleTotal = 0,
									returnTotal = 0,
									total = 0,
									profitTotal = 0								
								};
								 
							}
						}
					}
                else if (sqlName == "todayTotal")
                {
                    todayTotal = CDbDealer.Get1RecordFromDr(dr, false);
                }
                else if (sqlName == "total")
                {
                  //  total = CDbDealer.Get1RecordFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                  //  dr.Read();
                   // count = CPubVars.GetTextFromDr(dr, "count");
                }
            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, records, todayTotal, total, count });
        }


        /// <summary>
        /// 业务员排行
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="startRow"></param>
        /// <param name="pageSize"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="sellerID"></param>
        /// <param name="costPriceType"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> GetSellerRank(string operKey, int startRow, int pageSize, string startTime, string endTime, string sellerID, string classID, string itemsID,string brandID, string viewRange, string costPriceType, string orderType, string groupID, string queryBySalesOrderChecked, string branchID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);

            cmd.CommandText = $@"select cost_price_type from company_setting where company_id={companyID};";
            var dr = await cmd.ExecuteReaderAsync();
            if (costPriceType.IsInvalid())
            {
                if (dr.Read())
                {
                    costPriceType = CPubVars.GetTextFromDr(dr, "cost_price_type");
                }
            }
            dr.Close();
            if (costPriceType.IsInvalid()) costPriceType = "3";


            var depart_id = "";
            if (sellerID.IsValid() && (viewRange == "department" || viewRange == "self"))
            {
                cmd.CommandText = $@"select depart_id from info_operator where company_id={companyID} and  oper_id={sellerID};";
                dr = await cmd.ExecuteReaderAsync();
                if (dr.Read())
                {
                    depart_id = CPubVars.GetTextFromDr(dr, "depart_id");
                }
                dr.Close();
            }

            
            var condi = $" sm.company_id = {companyID}  and approve_time is not null and red_flag is null and sm.seller_id is not null ";

            if (startTime.IsValid() && endTime.IsValid())
            {
                condi += $" and sm.happen_time >= '{startTime}' and sm.happen_time <= '{endTime}'";
            }
            if (!string.IsNullOrEmpty(itemsID))
            {
                condi += $" and d.item_id in ({itemsID}) ";
            }
            if (!string.IsNullOrEmpty(classID))
            {
                string clsCondi = "";
                foreach (string c in classID.Split(','))
                {
                    if (clsCondi != "") clsCondi += " or ";
                    clsCondi += $"p.other_class like '%{c}%'";
                }
                clsCondi = "(" + clsCondi + ")";
                condi += $" and {clsCondi}";
            }

            if (!string.IsNullOrEmpty(brandID))
            {
                condi += $" and p.item_brand = {brandID} ";
            }
            if (!string.IsNullOrEmpty(groupID))
            {
                condi += $" and sup.sup_group = {groupID} ";
            }
            if (branchID.IsValid())
            {
                condi += $" and sm.branch_id = {branchID} ";
            }

            // if (sellerID.IsValid()&& viewRange == "self") condi += $" and sm.seller_id = {sellerID} ";

            var sellercondi = "";
            int parsedDepartId;
            if (int.TryParse(depart_id, out parsedDepartId) && viewRange != "all")
            {
                sellercondi += $" and (depart_path like '%/{parsedDepartId}/%' OR depart_id = {parsedDepartId}) ";
            }



            var orderSql = "";
            if (orderType.IsValid()) orderSql = $" order by {orderType} desc";
            string sumFlds = "";
            string costFld = @$"
case when {costPriceType} = 1 then round(cast(spec_profit as   numeric),2) 
     when {costPriceType} = 2 then round(cast(avg_profit as    numeric),2)
     when {costPriceType} = 4 then round(cast(recent_profit as numeric),2) 
                              else round(cast(buy_profit as    numeric),2) 
end";
            if (startRow == 0)
            {
                sumFlds = @$"
,count(*) over()
,round((sum(X_amount) over())::numeric,2) as saleTotal
,round((sum(T_amount) over())::numeric,2) as returnTotal
,round((sum(saleSum) over())::numeric,2)  as total
,round((sum({costFld}) over() )::numeric,2) profitTotal
,round((sum(saleNum) over())::numeric,2) saleNumTotal
";

			}
            string detailTableName = queryBySalesOrderChecked == "true" ? "sheet_sale_order_detail" : "sheet_sale_detail";
            string mainTableName = queryBySalesOrderChecked == "true" ? "sheet_sale_order_main" : "sheet_sale_main";
            
            var sqlNoLimit = @$"
SELECT seller_id,oper_name,X_amount,T_amount,saleSum,saleNum,b_qty,m_qty,s_qty,x_b_qty,x_m_qty,x_s_qty,t_b_qty,t_m_qty,t_s_qty,
    {costFld} profit {sumFlds}
FROM 
(
    SELECT seller_id,oper_name,round(sum(x_amount)::numeric,2) x_amount,round(sum(t_amount)::numeric,2) t_amount,round(sum(x_amount+t_amount)::numeric,2) as saleSum,sum(saleNum) as saleNum,
        round(sum(b_qty)::numeric,2) b_qty,
        round(sum(m_qty)::numeric,2) m_qty,
        round(sum(s_qty)::numeric,2) s_qty,
        round( SUM ( x_b_qty ) :: NUMERIC, 2 ) x_b_qty,
        round( SUM ( x_m_qty ) :: NUMERIC, 2 ) x_m_qty,
        round( SUM ( x_s_qty ) :: NUMERIC, 2 ) x_s_qty,
        round( SUM ( t_b_qty ) :: NUMERIC, 2 ) t_b_qty,
        round( SUM ( t_m_qty ) :: NUMERIC, 2 ) t_m_qty,
        round( SUM ( t_s_qty ) :: NUMERIC, 2 ) t_s_qty,
        round((sum(total)-sum(disc)-sum(spec_cost))::numeric,2) spec_profit,round((sum(total)-sum(disc)-sum(avg_cost))::numeric,2) avg_profit, round((sum(total)-sum(disc)-sum(buy_cost))::numeric,2) buy_profit,round((sum(total) - sum(disc) - sum(recent_cost))::numeric, 2)  recent_profit
    FROM 
    (
        SELECT sm.seller_id,oper_name,d.item_id,sum(case when quantity*inout_flag<=0 then sub_amount*inout_flag*(-1) else 0 end) as x_amount,sum(quantity) saleNum,
            sum(case when quantity*inout_flag>0 then sub_amount*inout_flag*(-1) else 0 end) as t_amount,
            yj_get_unit_qty ('b',sum(quantity*unit_factor*inout_flag*(-1))::numeric,b_unit_factor,m_unit_factor,false) b_qty,
			yj_get_unit_qty ('m',sum(quantity*unit_factor*inout_flag*(-1))::numeric,b_unit_factor,m_unit_factor,false) m_qty,
			yj_get_unit_qty ('s',sum(quantity*unit_factor*inout_flag*(-1))::numeric,b_unit_factor,m_unit_factor,false) s_qty,
	        yj_get_unit_qty ( 'b', SUM (case when sheet_type ='X' then quantity* unit_factor else 0 end ) :: NUMERIC, b_unit_factor, m_unit_factor ,false) x_b_qty,
	        yj_get_unit_qty ( 'm', SUM (case when sheet_type ='X' then quantity* unit_factor else 0 end ) :: NUMERIC, b_unit_factor, m_unit_factor ,false) x_m_qty,
	        yj_get_unit_qty ( 's', SUM (case when sheet_type ='X' then quantity* unit_factor else 0 end ) :: NUMERIC, b_unit_factor, m_unit_factor ,false) x_s_qty, 
	        yj_get_unit_qty ( 'b', SUM (case when sheet_type ='T' then quantity* unit_factor else 0 end ) :: NUMERIC, b_unit_factor, m_unit_factor ,false) t_b_qty,
	        yj_get_unit_qty ( 'm', SUM (case when sheet_type ='T' then quantity* unit_factor else 0 end ) :: NUMERIC, b_unit_factor, m_unit_factor ,false) t_m_qty,
	        yj_get_unit_qty ( 's', SUM (case when sheet_type ='T' then quantity* unit_factor else 0 end ) :: NUMERIC, b_unit_factor, m_unit_factor ,false) t_s_qty, 
            sum(sub_amount*inout_flag*(-1)) total,sum(quantity*inout_flag*(-1)*unit_factor*COALESCE(d.cost_price_avg,0)) avg_cost,
            sum(quantity*inout_flag*(-1)*unit_factor*COALESCE(p.cost_price_spec,0)) spec_cost,
            sum(quantity*inout_flag*(-1)*COALESCE(d.cost_price_buy,0)*unit_factor) buy_cost,
            sum(quantity * inout_flag * (-1) * COALESCE(d.cost_price_recent, 0) * unit_factor) recent_cost,
            sum(case when d.row_index=1 then COALESCE(disc_amount,0)*inout_flag*(-1) else 0 end ) disc
        FROM 
        (
            select * from {detailTableName} where company_id = {companyID} and happen_time >= '{startTime}' and happen_time <= '{endTime}' and coalesce(trade_type,'X') NOT IN ( 'J', 'H' ) 
        ) d
        LEFT JOIN {mainTableName} sm on d.sheet_id =sm.sheet_id and sm.company_id = {companyID}
        LEFT JOIN info_item_prop p on d.item_id = p.item_id and p.company_id = {companyID}
        LEFT JOIN info_supcust sup on sm.supcust_id = sup.supcust_id and sup.company_id = {companyID}
        LEFT JOIN 
        ( 
           SELECT us.item_id,us.unit_no s_unit_no, ub.unit_no b_unit_no,   ub.unit_factor b_unit_factor, um.unit_no m_unit_no,   um.unit_factor m_unit_factor
           FROM      info_item_multi_unit us
           LEFT JOIN info_item_multi_unit ub on us.item_id=ub.item_id and ub.unit_type='b' and ub.company_id={companyID}
           LEFT JOIN info_item_multi_unit um on us.item_id=um.item_id and um.unit_type='m' and um.company_id={companyID}
           WHERE us.company_id={companyID} and us.unit_type='s'
        ) mu on mu.item_id = d.item_id
        LEFT JOIN info_operator o on sm.seller_id = o.oper_id AND  o.company_id = {companyID}
        WHERE {condi} {sellercondi} 
        GROUP BY sm.seller_id,oper_name,d.item_id,b_unit_factor,m_unit_factor
    ) t 
    group by oper_name,seller_id
) 
t";

            SQLQueue QQ = new SQLQueue(cmd);
            string sql = $"{sqlNoLimit} {orderSql} limit {pageSize} offset {startRow} ";
            QQ.Enqueue("data", sql);
            if (startRow == 0)
            {
               // sql = @$"select count(*),round(sum(X_amount)::numeric,2) as saleTotal,round(sum(T_amount)::numeric,2) as returnTotal,round(sum(saleSum)::numeric,2) as total,round(sum(profit)::numeric,2) profitTotal,round(sum(saleNum)::numeric,2) saleNum from 
               //     ( {sqlNoLimit} ) t ";
                //QQ.Enqueue("total", sql);
            }

            List<ExpandoObject> records = null;
            List<dynamic> total = new List<dynamic>();
            dr = await QQ.ExecuteReaderAsync();
            var count = "0";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    records = CDbDealer.GetRecordsFromDr(dr, false);
                    if (startRow == 0)
                    {
						 
                        if (records.Count > 0) {
                            dynamic rec = records[0];
                            dynamic d = new
                            {
                                rec.saletotal,
								rec.returntotal,
								rec.total,
								rec.profittotal,
                                rec.salenumtotal
							};
                            total.Add(d);
                            count = rec.count;

						}
                        else
                        {
							dynamic d = new
							{
								saleTotal=0,
								returnTotal=0,
								total=0,
								profitTotal=0,
								saleNum=0
							};
							total.Add(d);
						}
						

					}
                }
                else if (sqlName == "total")
                {
                   // total = CDbDealer.GetRecordsFromDr(dr, false);
                   // count = ((dynamic)total[0]).count;
                }
            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, records, total, count });
        }

        /// <summary>
        /// 品牌销量汇总
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="sellerID"></param>
        /// <param name="startRow"></param>
        /// <param name="pageSize"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="costPriceType"></param>
        /// <param name="orderType"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> GetAmountByBrand(string operKey, string sellerID, int startRow, int pageSize, string startTime, string endTime, string costPriceType, string orderType, string departID, string clientID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);

            string departLeftJoinCondi = "";


            cmd.CommandText = $@"select cost_price_type from company_setting where company_id={companyID};";
            var dr = await cmd.ExecuteReaderAsync();
            if (costPriceType.IsInvalid())
            {
                while (dr.Read())
                {
                    costPriceType = CPubVars.GetTextFromDr(dr, "cost_price_type");
                }
            }

            if (costPriceType.IsInvalid()) costPriceType = "3";

            dr.Close();
            var condi = $" and approve_time is not null and red_flag is null and (trade_type NOT IN ( 'J', 'H' ) or trade_type is null) ";
            if (sellerID.IsValid()) condi += $" and seller_id = {sellerID} ";
            if (clientID.IsValid()) condi += $" and sm.supcust_id = {clientID} ";
            if (departID.IsValid())
            {
                departLeftJoinCondi = "left join info_operator io on sm.company_id=io.company_id and sm.seller_id=io.oper_id  ";
                condi += $" and io.depart_path like '%/{departID}/%' ";
            }
            if (startTime.IsValid() && endTime.IsValid())
                condi += $" and d.happen_time >= '{startTime}' and d.happen_time <= '{endTime}' and sm.happen_time >= '{startTime}' and sm.happen_time <= '{endTime}'";

            var sql_nolimit = @$"
SELECT brand_id,brand_name,X_amount,T_amount,saleSum,saleNum,b_qty,m_qty,s_qty,
	x_b_qty, x_m_qty, x_s_qty,
	t_b_qty, t_m_qty, t_s_qty,
    (case when {costPriceType} = 1 then round(cast(spec_profit as numeric),2) 
    when {costPriceType} = 2 then round(cast(avg_profit as numeric),2) else round(cast(buy_profit as numeric),2) end) profit 
FROM 
(
    SELECT brand_id,brand_name,round(sum(x_amount)::numeric,2) x_amount,round(sum(t_amount)::numeric,2) t_amount,round(sum(x_amount+t_amount)::numeric,2) as saleSum,sum(saleNum) as saleNum,
    round(sum(b_qty)::numeric,2) b_qty,round(sum(m_qty)::numeric,2) m_qty,round(sum(s_qty)::numeric,2) s_qty,
	round( SUM ( x_b_qty ) :: NUMERIC, 2 ) x_b_qty,
	round( SUM ( x_m_qty ) :: NUMERIC, 2 ) x_m_qty,
	round( SUM ( x_s_qty ) :: NUMERIC, 2 ) x_s_qty,
	round( SUM ( t_b_qty ) :: NUMERIC, 2 ) t_b_qty,
	round( SUM ( t_m_qty ) :: NUMERIC, 2 ) t_m_qty,
	round( SUM ( t_s_qty ) :: NUMERIC, 2 ) t_s_qty,
    round((sum(total)-sum(spec_cost))::numeric,2) spec_profit,round((sum(total)-sum(avg_cost))::numeric,2) avg_profit, round((sum(total)-sum(buy_cost))::numeric,2) buy_profit
    FROM 
    (
        SELECT brand_id,brand_name,d.item_id,sum(case when quantity*inout_flag<=0 then sub_amount*inout_flag*(-1) else 0 end) as x_amount,sum(quantity) saleNum,
            sum(case when quantity*inout_flag>0 then sub_amount*inout_flag*(-1) else 0 end) as t_amount,
            yj_get_unit_qty ('b',sum(quantity*unit_factor*inout_flag*(-1))::numeric,b_unit_factor,m_unit_factor,false) b_qty,
	        yj_get_unit_qty ('m',sum(quantity*unit_factor*inout_flag*(-1))::numeric,b_unit_factor,m_unit_factor,false) m_qty,
	        yj_get_unit_qty ( 's', SUM ( quantity * unit_factor * inout_flag * ( - 1 ) ) :: NUMERIC, b_unit_factor, m_unit_factor ,false) s_qty,
            yj_get_unit_qty ( 'b', SUM (case when sheet_type ='X' then quantity* unit_factor else 0 end ) :: NUMERIC, b_unit_factor, m_unit_factor ,false) x_b_qty,
            yj_get_unit_qty ( 'm', SUM (case when sheet_type ='X' then quantity* unit_factor else 0 end ) :: NUMERIC, b_unit_factor, m_unit_factor ,false) x_m_qty,
            yj_get_unit_qty ( 's', SUM (case when sheet_type ='X' then quantity* unit_factor else 0 end ) :: NUMERIC, b_unit_factor, m_unit_factor ,false) x_s_qty, 
            yj_get_unit_qty ( 'b', SUM (case when sheet_type ='T' then quantity* unit_factor else 0 end ) :: NUMERIC, b_unit_factor, m_unit_factor ,false) t_b_qty,
            yj_get_unit_qty ( 'm', SUM (case when sheet_type ='T' then quantity* unit_factor else 0 end ) :: NUMERIC, b_unit_factor, m_unit_factor ,false) t_m_qty,
	        yj_get_unit_qty ( 's', SUM ( CASE WHEN sheet_type = 'T' THEN quantity * unit_factor ELSE 0 END ) :: NUMERIC, b_unit_factor, m_unit_factor ,false) t_s_qty,
            sum(sub_amount*inout_flag*(-1)) total,sum(quantity*inout_flag*(-1)*unit_factor*COALESCE(d.cost_price_avg,0)) avg_cost,
            sum(quantity*inout_flag*(-1)*unit_factor*COALESCE(p.cost_price_spec,0)) spec_cost,
            sum(quantity*inout_flag*(-1)*COALESCE(d.cost_price_buy,0)*unit_factor) buy_cost
        FROM sheet_sale_detail d 
        LEFT JOIN sheet_sale_main sm on d.sheet_id =sm.sheet_id and sm.company_id = {companyID}
        LEFT JOIN info_item_prop p on d.item_id = p.item_id and p.company_id = {companyID}
        {departLeftJoinCondi}
        LEFT JOIN 
        (
            SELECT item_id,(b->>'f1')::real as b_unit_factor,(m->>'f1')::real as m_unit_factor,b->>'f2' as b_unit_no,m->>'f2' as m_unit_no,s->>'f2' as s_unit_no from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no)) as json from info_item_multi_unit where company_id= {companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
                as errr(item_id int, s jsonb,m jsonb,b jsonb) 
        ) mu on mu.item_id = d.item_id
        LEFT JOIN info_item_brand ib on ib.brand_id = p.item_brand and ib.company_id = {companyID} 
        WHERE d.company_id = {companyID} {condi}
        GROUP BY brand_id,brand_name,d.item_id,b_unit_factor,m_unit_factor,d.quantity,d.inout_flag
    ) t
    group by brand_id,brand_name
) t ";

            var sql = sql_nolimit + $@" order by {orderType} desc limit {pageSize} offset {startRow};";
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("data", sql);

            if (startRow == 0)
            {
                sql = @$"select count(*) count,round(sum(X_amount)::numeric,2) as saleTotal,round(sum(T_amount)::numeric,2) as returnTotal,round(sum(saleSum)::numeric,2) total,round(sum(profit)::numeric,2) profitTotal,round(sum(saleNum)::numeric,2) saleNum FROM 
                             ( {sql_nolimit} ) tt  ";
                QQ.Enqueue("total", sql);
            }

            List<ExpandoObject> records = null;
            List<ExpandoObject> total = null;
            dr = await QQ.ExecuteReaderAsync();
            var count = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    records = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "total")
                {
                    total = CDbDealer.GetRecordsFromDr(dr, false);
                    count = ((dynamic)total[0]).count;
                }
            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, records, total, count });
        }

        ///<summary>
        ///热销商品排行
        /// </summary>
        [HttpGet]
        public async Task<JsonResult> GetAmountByProduct(string operKey, string sheetType, string sellerID, string brandID, string supcustID, int startRow,
            int pageSize, string remark, string startTime, string endTime, string costPriceType, string orderType, string classID, string itemsID, string departID, string groupID, string branchID)
        {
            string departLeftJoinCondi = "";
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string mainTable = "sheet_sale_main";
            string detailTable = "sheet_sale_detail";
            if (sheetType == "XD")
            {
                mainTable = "sheet_sale_order_main";
                detailTable = "sheet_sale_order_detail";
            }

            cmd.CommandText = $@"select cost_price_type from company_setting where company_id={companyID};";
            var dr = await cmd.ExecuteReaderAsync();
            if (costPriceType.IsInvalid())
            {
                while (dr.Read())
                {
                    costPriceType = CPubVars.GetTextFromDr(dr, "cost_price_type");
                }
            }

            if (costPriceType.IsInvalid()) costPriceType = "4";

            dr.Close();
            var mainCondi = $" and approve_time is not null and red_flag is null";
            if (sellerID.IsValid()) mainCondi += $" and seller_id = {sellerID} ";
            if (supcustID.IsValid()) mainCondi += $" and sm.supcust_id = {supcustID} ";

            string detailCondi = $"";

            if (brandID.IsValid()) detailCondi += $" and p.item_brand = {brandID} ";


            if (!string.IsNullOrEmpty(itemsID))
            {
                detailCondi += $" and p.item_id in ({itemsID}) ";
            }
            if (!string.IsNullOrEmpty(classID))
            {
                string clsCondi = "";
                foreach (string c in classID.Split(','))
                {
                    if (clsCondi != "") clsCondi += " or ";
                    clsCondi += $"p.other_class like '%{c}%'";
                }
                clsCondi = "(" + clsCondi + ")";
                detailCondi += $" and {clsCondi}";
            }
            if (departID.IsValid())
            {
                departLeftJoinCondi = "left join info_operator io on sm.company_id=io.company_id and sm.seller_id=io.oper_id  ";
                mainCondi += $" and io.depart_path like '%/{departID}/%' ";
            }
            if (!string.IsNullOrEmpty(groupID))
            {
                departLeftJoinCondi += " left join info_supcust sup on sm.company_id = sup.company_id and sm.supcust_id = sup.supcust_id ";
                mainCondi += $" and sup.sup_group = {groupID} ";
            }
            if (branchID.IsValid())
            {
                mainCondi += $" and sm.branch_id = {branchID} ";
            }
            string timeCondi = $" and  happen_time >= '{startTime}' and  happen_time <= '{endTime}'";
            string remarkCondi = string.IsNullOrEmpty(remark) ? $@"" : @$" and d.remark like '%{remark}%' ";
            var sql_nolimit = @$"
SELECT item_id,item_name,item_brand,X_amount,abs(T_amount) AS T_amount,saleSum,saleNum,
        b_qty,b_unit_no,m_qty,m_unit_no,s_unit_no,s_qty,s_unit_no as unit_no,qty_unit,
        x_qty_unit,xz_qty_unit,t_qty_unit,tz_qty_unit,x_b_qty,x_m_qty,x_s_qty,x_b_qty,x_m_qty,x_s_qty,t_b_qty,t_m_qty,t_s_qty,
        xz_b_qty,xz_m_qty,xz_s_qty,tz_b_qty,tz_m_qty,tz_s_qty,
        (CASE WHEN {costPriceType} = 1 THEN round( CAST ( spec_profit AS NUMERIC ), 2 ) 
        WHEN {costPriceType} = 2 THEN round( CAST ( avg_profit AS NUMERIC ), 2 ) 
        WHEN {costPriceType} = 4 THEN round(CAST(recent_profit AS NUMERIC), 2)
                    ELSE round( CAST ( buy_profit AS NUMERIC ), 2 ) END ) profit 
FROM 
(
    SELECT item_id,item_name,item_brand,qty saleNum,b_unit_no,m_unit_no,s_unit_no,
        yj_get_unit_qty ('b',qty::numeric,b_unit_factor,m_unit_factor,false) b_qty,
        yj_get_unit_qty ('m',qty::numeric,b_unit_factor,m_unit_factor,false) m_qty,
        yj_get_unit_qty ('s',qty::numeric,b_unit_factor,m_unit_factor,false) s_qty,
        unit_from_s_to_bms (qty::numeric,b_unit_factor,m_unit_factor,1,b_unit_no,m_unit_no,s_unit_no) qty_unit,
        unit_from_s_to_bms ( x_qty :: NUMERIC, b_unit_factor, m_unit_factor, 1, b_unit_no, m_unit_no, s_unit_no ) x_qty_unit,
        unit_from_s_to_bms ( xz_qty :: NUMERIC, b_unit_factor, m_unit_factor, 1, b_unit_no, m_unit_no, s_unit_no ) xz_qty_unit,
        unit_from_s_to_bms ( t_qty :: NUMERIC, b_unit_factor, m_unit_factor, 1, b_unit_no, m_unit_no, s_unit_no ) t_qty_unit,
        unit_from_s_to_bms ( tz_qty :: NUMERIC, b_unit_factor, m_unit_factor, 1, b_unit_no, m_unit_no, s_unit_no ) tz_qty_unit,

	    yj_get_unit_qty ( 'b', x_qty :: NUMERIC, b_unit_factor, m_unit_factor ,false) x_b_qty,
	    yj_get_unit_qty ( 'm', x_qty :: NUMERIC, b_unit_factor, m_unit_factor ,false) x_m_qty,
	    yj_get_unit_qty ( 's', x_qty :: NUMERIC, b_unit_factor, m_unit_factor ,false) x_s_qty, 
	    yj_get_unit_qty ( 'b', t_qty :: NUMERIC, b_unit_factor, m_unit_factor ,false) t_b_qty,
	    yj_get_unit_qty ( 'm', t_qty :: NUMERIC, b_unit_factor, m_unit_factor ,false) t_m_qty,
	    yj_get_unit_qty ( 's', t_qty :: NUMERIC, b_unit_factor, m_unit_factor ,false) t_s_qty, 

	    yj_get_unit_qty ( 'b', xz_qty :: NUMERIC, b_unit_factor, m_unit_factor ,false) xz_b_qty,
	    yj_get_unit_qty ( 'm', xz_qty :: NUMERIC, b_unit_factor, m_unit_factor ,false) xz_m_qty,
	    yj_get_unit_qty ( 's', xz_qty :: NUMERIC, b_unit_factor, m_unit_factor ,false) xz_s_qty, 
	    yj_get_unit_qty ( 'b', tz_qty :: NUMERIC, b_unit_factor, m_unit_factor ,false) tz_b_qty,
	    yj_get_unit_qty ( 'm', tz_qty :: NUMERIC, b_unit_factor, m_unit_factor ,false) tz_m_qty,
	    yj_get_unit_qty ( 's', tz_qty :: NUMERIC, b_unit_factor, m_unit_factor ,false) tz_s_qty, 
        round(x_amount::numeric,2) x_amount,round(t_amount::numeric, 2) t_amount,
	    round((x_amount + t_amount)::NUMERIC, 2) AS saleSum,
        round((total-spec_cost) :: NUMERIC, 2) spec_profit,
	    round((total-avg_cost)::numeric, 2 ) avg_profit,
        round((total-buy_cost)::NUMERIC, 2 ) buy_profit, 
        round((total-recent_cost)::NUMERIC, 2 ) recent_profit 
    FROM 
    (
        SELECT d.item_id,item_name,item_brand,b_unit_no,m_unit_no,s_unit_no,b_unit_factor,m_unit_factor,
            SUM (quantity*d.unit_factor*inout_flag*(-1)) as qty,
	        SUM( case when d.sub_amount<>0  and d.quantity*d.inout_flag<0 then   d.quantity*d.unit_factor else 0 end) AS x_qty,
	        SUM(case when d.sub_amount=0 and d.quantity*d.inout_flag<0 then d.quantity*d.unit_factor else 0 end) AS xz_qty,
	        SUM(case when d.sub_amount<>0  and d.quantity*d.inout_flag>0 then d.quantity*d.unit_factor else 0 end) AS t_qty,
	        SUM(case when d.sub_amount=0 and d.quantity*d.inout_flag>0 then d.quantity*d.unit_factor else 0 end) AS tz_qty,
            SUM ( CASE WHEN quantity * inout_flag <= 0 THEN sub_amount * inout_flag * ( - 1 ) ELSE 0 END ) AS X_amount,
			SUM ( CASE WHEN quantity * inout_flag > 0 THEN sub_amount * inout_flag * ( - 1 ) ELSE 0 END ) AS T_amount,
			SUM ( sub_amount * inout_flag * ( - 1 ) ) total,
            SUM ( quantity * inout_flag * ( - 1 ) * unit_factor * COALESCE ( d.cost_price_avg, 0 ) ) avg_cost,
			SUM ( quantity * inout_flag * ( - 1 ) * unit_factor * COALESCE ( d.cost_price_spec, 0 ) ) spec_cost,
            SUM ( quantity * inout_flag * ( - 1 ) * COALESCE ( d.cost_price_buy, 0 )*unit_factor ) buy_cost,
            SUM ( quantity * inout_flag * ( - 1 ) * COALESCE ( cost_price_recent, 0 )*unit_factor ) recent_cost
            FROM 
        (
             SELECT d.row_index,d.item_id,sheet_id,quantity,unit_factor,inout_flag,sub_amount,d.cost_price_avg,cost_price_prop cost_price_spec,d.cost_price_recent cost_price_recent,cost_price_buy,item_name,item_brand
             FROM {detailTable} d
             LEFT JOIN info_item_prop P ON d.item_id = P.item_id and p.company_id = {companyID} 
             WHERE d.company_id={companyID} {remarkCondi} and (trade_type NOT IN('J', 'H') or trade_type is null) {timeCondi} {detailCondi}
        ) d
	    inner JOIN
        (
            select * from {mainTable} sm 
            {departLeftJoinCondi}
            where sm.company_id = {companyID} {timeCondi} {mainCondi}
        ) sm ON d.sheet_id = sm.sheet_id
	   
        LEFT JOIN
        (
            select item_id,(b->>'f1')::real as b_unit_factor,(m->>'f1')::real as m_unit_factor,b->>'f2' as b_unit_no,m->>'f2' as m_unit_no,s->>'f2' as s_unit_no from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no)) as json 
             from info_item_multi_unit where company_id= {companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
             as errr(item_id int, s jsonb,m jsonb,b jsonb) 
        ) mu on mu.item_id = d.item_id
        GROUP BY d.item_id,item_name,item_brand,b_unit_no,m_unit_no,s_unit_no,b_unit_factor,m_unit_factor
    ) t 
) t";

            var sql = sql_nolimit + $@" order by {orderType} desc  limit {pageSize} offset {startRow};";
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("data", sql);
            sql = @$"select count(*) as count,round(sum(X_amount)::numeric,2) as saleTotal,round(sum(T_amount)::numeric,2) as returnTotal,round(sum(saleSum)::numeric,2) total,round(sum(profit)::numeric,2) profitTotal,round(sum(saleNum)::numeric,2) saleNum FROM 
                             ( {sql_nolimit} ) tt  ";
            if (startRow == 0)
            {
                QQ.Enqueue("total", sql);
            }

            List<ExpandoObject> records = null;
            List<ExpandoObject> total = null;
            dr = await QQ.ExecuteReaderAsync();
            var count = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    records = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "total")
                {
                    total = CDbDealer.GetRecordsFromDr(dr, false);
                    count = ((dynamic)total[0]).count;
                }
            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, records, total, count });
        }

        [HttpGet]
        public async Task<JsonResult> GetAmountByProduct_old(string operKey, string sheetType, string sellerID, string brandID, string supcustID, int startRow,
            int pageSize, string startTime, string endTime, string costPriceType, string orderType, string classID, string itemsID, string departID)
        {
            string departLeftJoinCondi = "";
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string mainTable = "sheet_sale_main";
            string detailTable = "sheet_sale_detail";
            if (sheetType == "XD")
            {
                mainTable = "sheet_sale_order_main";
                detailTable = "sheet_sale_order_detail";
            }

            cmd.CommandText = $@"select cost_price_type from company_setting where company_id={companyID};";
            var dr = await cmd.ExecuteReaderAsync();
            if (costPriceType.IsInvalid())
            {
                while (dr.Read())
                {
                    costPriceType = CPubVars.GetTextFromDr(dr, "cost_price_type");
                }
            }

            if (costPriceType.IsInvalid()) costPriceType = "4";

            dr.Close();
            var condi = $"sm.company_id = {companyID}  and approve_time is not null and red_flag is null and d.item_id is not null and (trade_type NOT IN ( 'J', 'H' ) or trade_type is null) ";
            if (sellerID.IsValid()) condi += $" and seller_id = {sellerID} ";
            if (brandID.IsValid()) condi += $" and p.item_brand = {brandID} ";
            if (supcustID.IsValid()) condi += $" and sm.supcust_id = {supcustID} ";
            //if (itemIds.IsValid()) condi += $"and p.item_id in ({itemIds})";

            if (!string.IsNullOrEmpty(itemsID))
            {
                condi += $" and p.item_id in ({itemsID}) ";
            }
            if (!string.IsNullOrEmpty(classID))
            {
                string clsCondi = "";
                foreach (string c in classID.Split(','))
                {
                    if (clsCondi != "") clsCondi += " or ";
                    clsCondi += $"p.other_class like '%{c}%'";
                }
                clsCondi = "(" + clsCondi + ")";
                condi += $" and {clsCondi}";
            }
            if (departID.IsValid())
            {
                departLeftJoinCondi = "left join info_operator io on sm.company_id=io.company_id and sm.seller_id=io.oper_id  ";
                condi += $" and io.depart_path like '%/{departID}/%' ";
            }


            if (startTime.IsValid() && endTime.IsValid())
                condi += $" and d.happen_time >= '{startTime}' and d.happen_time <= '{endTime}' and sm.happen_time >= '{startTime}' and sm.happen_time <= '{endTime}'";
            var sql_nolimit = @$"
SELECT item_id,item_name,item_brand,X_amount,abs(T_amount) AS T_amount,saleSum,saleNum,
        b_qty,b_unit_no,m_qty,m_unit_no,s_unit_no,s_qty,s_unit_no as unit_no,qty_unit,
        x_qty_unit,xz_qty_unit,t_qty_unit,tz_qty_unit,x_b_qty,x_m_qty,x_s_qty,x_b_qty,x_m_qty,x_s_qty,t_b_qty,t_m_qty,t_s_qty,
        xz_b_qty,xz_m_qty,xz_s_qty,tz_b_qty,tz_m_qty,tz_s_qty,
        (CASE WHEN {costPriceType} = 1 THEN round( CAST ( spec_profit AS NUMERIC ), 2 ) 
        WHEN {costPriceType} = 2 THEN round( CAST ( avg_profit AS NUMERIC ), 2 ) 
                    ELSE round( CAST ( buy_profit AS NUMERIC ), 2 ) END ) profit 
FROM 
(
    SELECT item_id,item_name,item_brand,qty saleNum,b_unit_no,m_unit_no,s_unit_no,
        yj_get_unit_qty ('b',qty::numeric,b_unit_factor,m_unit_factor,false) b_qty,
        yj_get_unit_qty ('m',qty::numeric,b_unit_factor,m_unit_factor,false) m_qty,
        yj_get_unit_qty ('s',qty::numeric,b_unit_factor,m_unit_factor,false) s_qty,
        unit_from_s_to_bms (qty::numeric,b_unit_factor,m_unit_factor,1,b_unit_no,m_unit_no,s_unit_no) qty_unit,
        unit_from_s_to_bms ( x_qty :: NUMERIC, b_unit_factor, m_unit_factor, 1, b_unit_no, m_unit_no, s_unit_no ) x_qty_unit,
        unit_from_s_to_bms ( xz_qty :: NUMERIC, b_unit_factor, m_unit_factor, 1, b_unit_no, m_unit_no, s_unit_no ) xz_qty_unit,
        unit_from_s_to_bms ( t_qty :: NUMERIC, b_unit_factor, m_unit_factor, 1, b_unit_no, m_unit_no, s_unit_no ) t_qty_unit,
        unit_from_s_to_bms ( tz_qty :: NUMERIC, b_unit_factor, m_unit_factor, 1, b_unit_no, m_unit_no, s_unit_no ) tz_qty_unit,

	    yj_get_unit_qty ( 'b', x_qty :: NUMERIC, b_unit_factor, m_unit_factor ,false) x_b_qty,
	    yj_get_unit_qty ( 'm', x_qty :: NUMERIC, b_unit_factor, m_unit_factor ,false) x_m_qty,
	    yj_get_unit_qty ( 's', x_qty :: NUMERIC, b_unit_factor, m_unit_factor ,false) x_s_qty, 
	    yj_get_unit_qty ( 'b', t_qty :: NUMERIC, b_unit_factor, m_unit_factor ,false) t_b_qty,
	    yj_get_unit_qty ( 'm', t_qty :: NUMERIC, b_unit_factor, m_unit_factor ,false) t_m_qty,
	    yj_get_unit_qty ( 's', t_qty :: NUMERIC, b_unit_factor, m_unit_factor ,false) t_s_qty, 

	    yj_get_unit_qty ( 'b', xz_qty :: NUMERIC, b_unit_factor, m_unit_factor ,false) xz_b_qty,
	    yj_get_unit_qty ( 'm', xz_qty :: NUMERIC, b_unit_factor, m_unit_factor ,false) xz_m_qty,
	    yj_get_unit_qty ( 's', xz_qty :: NUMERIC, b_unit_factor, m_unit_factor ,false) xz_s_qty, 
	    yj_get_unit_qty ( 'b', tz_qty :: NUMERIC, b_unit_factor, m_unit_factor ,false) tz_b_qty,
	    yj_get_unit_qty ( 'm', tz_qty :: NUMERIC, b_unit_factor, m_unit_factor ,false) tz_m_qty,
	    yj_get_unit_qty ( 's', tz_qty :: NUMERIC, b_unit_factor, m_unit_factor ,false) tz_s_qty, 
        round(x_amount::numeric,2) x_amount,round(t_amount::numeric, 2) t_amount,
	    round((x_amount + t_amount)::NUMERIC, 2) AS saleSum,
        round((total-spec_cost) :: NUMERIC, 2) spec_profit,
	    round((total-avg_cost)::numeric, 2 ) avg_profit,
        round((total-buy_cost)::NUMERIC, 2 ) buy_profit 
    FROM 
    (
        SELECT d.item_id,item_name,item_brand,b_unit_no,m_unit_no,s_unit_no,b_unit_factor,m_unit_factor,
            SUM (quantity*d.unit_factor*inout_flag*(-1)) as qty,
	        SUM( case when d.sub_amount<>0  and d.quantity*d.inout_flag<0 then   d.quantity*d.unit_factor else 0 end) AS x_qty,
	        SUM(case when d.sub_amount=0 and d.quantity*d.inout_flag<0 then d.quantity*d.unit_factor else 0 end) AS xz_qty,
	        SUM(case when d.sub_amount<>0  and d.quantity*d.inout_flag>0 then d.quantity*d.unit_factor else 0 end) AS t_qty,
	        SUM(case when d.sub_amount=0 and d.quantity*d.inout_flag>0 then d.quantity*d.unit_factor else 0 end) AS tz_qty,
            SUM ( CASE WHEN quantity * inout_flag <= 0 THEN sub_amount * inout_flag * ( - 1 ) ELSE 0 END ) AS X_amount,
			SUM ( CASE WHEN quantity * inout_flag > 0 THEN sub_amount * inout_flag * ( - 1 ) ELSE 0 END ) AS T_amount,
			SUM ( sub_amount * inout_flag * ( - 1 ) ) total,
            SUM ( quantity * inout_flag * ( - 1 ) * unit_factor * COALESCE ( d.cost_price_avg, 0 ) ) avg_cost,
			SUM ( quantity * inout_flag * ( - 1 ) * unit_factor * COALESCE ( d.cost_price_spec, 0 ) ) spec_cost,
            SUM ( quantity * inout_flag * ( - 1 ) * COALESCE ( d.cost_price_buy, 0 )*unit_factor ) buy_cost
        FROM {detailTable} d
	    LEFT JOIN {mainTable} sm ON d.sheet_id = sm.sheet_id and sm.company_id = {companyID}
	    LEFT JOIN info_item_prop P ON d.item_id = P.item_id and p.company_id = {companyID}
        {departLeftJoinCondi}
        LEFT JOIN
        (
            select item_id,(b->>'f1')::real as b_unit_factor,(m->>'f1')::real as m_unit_factor,b->>'f2' as b_unit_no,m->>'f2' as m_unit_no,s->>'f2' as s_unit_no from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no)) as json 
             from info_item_multi_unit where company_id= {companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
             as errr(item_id int, s jsonb,m jsonb,b jsonb) 
        ) mu on mu.item_id = d.item_id
        WHERE {condi} GROUP BY d.item_id,item_name,item_brand,b_unit_no,m_unit_no,s_unit_no,b_unit_factor,m_unit_factor
    ) t 
) t";

            var sql = sql_nolimit + $@" order by {orderType} desc  limit {pageSize} offset {startRow};";
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("data", sql);
            sql = @$"select count(*) as count,round(sum(X_amount)::numeric,2) as saleTotal,round(sum(T_amount)::numeric,2) as returnTotal,round(sum(saleSum)::numeric,2) total,round(sum(profit)::numeric,2) profitTotal,round(sum(saleNum)::numeric,2) saleNum FROM 
                             ( {sql_nolimit} ) tt  ";
            if (startRow == 0)
            {
                QQ.Enqueue("total", sql);
            }

            List<ExpandoObject> records = null;
            List<ExpandoObject> total = null;
            dr = await QQ.ExecuteReaderAsync();
            var count = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    records = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "total")
                {
                    total = CDbDealer.GetRecordsFromDr(dr, false);
                    count = ((dynamic)total[0]).count;
                }
            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, records, total, count });
        }

        ///<summary>
        ///热订商品排行
        /// </summary>
        [HttpGet]
        public async Task<JsonResult> GetSaleOrderAmountByProduct(string operKey, string sellerID, string brandID, string supcustID, int startRow,
            int pageSize, string remark, string startTime, string endTime, string classID, string costPriceType, string orderType, string itemsID, string departID, string groupID, string branchID)
        {
            return await GetAmountByProduct(operKey, "XD", sellerID, brandID, supcustID, startRow, pageSize, remark, startTime, endTime, costPriceType, orderType, classID, itemsID, departID, groupID, branchID);
        }
        /// <summary>
        /// 客户销量汇总
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="startRow"></param>
        /// <param name="pageSize"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="costPriceType"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> GetClientSaleSum_old(string operKey, string sellerID, string startRow, string pageSize, string startTime, string endTime, string costPriceType, string orderType, string regionID, string classID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);

            //  if (pageSize == 400) pageSize = 430;


            cmd.CommandText = $@"select cost_price_type from company_setting where company_id={companyID};";
            var dr = await cmd.ExecuteReaderAsync();
            if (costPriceType.IsInvalid())
            {
                while (dr.Read())
                {
                    costPriceType = CPubVars.GetTextFromDr(dr, "cost_price_type");
                }
            }
            dr.Close();

            if (costPriceType.IsInvalid()) costPriceType = "3";
            var condi = $"where sm.company_id = {companyID}  and approve_time is not null and red_flag is null and (trade_type NOT IN ( 'J', 'H' ) or trade_type is null) ";
            if (sellerID.IsValid()) condi += $" and seller_id = {sellerID} ";
            if (classID.IsValid()) condi += $" AND p.class_id like '%{classID}%'";
            if (startTime.IsValid() && endTime.IsValid()) condi += $" and sm.happen_time >= '{startTime}' and sm.happen_time <= '{endTime}'";
            if (regionID.IsValid()) condi += $"and (region_id = {regionID} or other_region like '%{regionID}%')";
            var sql_nolimit = @$"
SELECT supcust_id,sup_name,X_amount,T_amount,saleSum,saleNum,b_qty,m_qty,s_qty,x_b_qty,x_m_qty,x_s_qty,t_b_qty,t_m_qty,t_s_qty,
 (case when {costPriceType} = 1 then round(cast(spec_profit as numeric),2)  when {costPriceType} = 2 then round(cast(avg_profit as numeric),2) else round(cast(buy_profit as numeric),2) end) profit 
FROM 
(
    select supcust_id,sup_name,round(sum(x_amount)::numeric,2) x_amount,round(sum(t_amount)::numeric,2) t_amount,round(sum(x_amount+t_amount)::numeric,2) as saleSum,sum(saleNum) as saleNum,
        round(sum(b_qty)::numeric,2) b_qty,
        round(sum(m_qty)::numeric,2) m_qty,
        round(sum(s_qty)::numeric,2) s_qty,
		round( SUM ( x_b_qty ) :: NUMERIC, 2 ) x_b_qty,
		round( SUM ( x_m_qty ) :: NUMERIC, 2 ) x_m_qty,
		round( SUM ( x_s_qty ) :: NUMERIC, 2 ) x_s_qty,
		round( SUM ( t_b_qty ) :: NUMERIC, 2 ) t_b_qty,
		round( SUM ( t_m_qty ) :: NUMERIC, 2 ) t_m_qty,
		round( SUM ( t_s_qty ) :: NUMERIC, 2 ) t_s_qty,
        round((sum(total)-sum(spec_cost))::numeric,2) spec_profit,
        round((sum(total)-sum(avg_cost))::numeric,2) avg_profit,
        round((sum(total)-sum(buy_cost))::numeric,2) buy_profit
             from (
                   select sm.supcust_id,sup_name,d.item_id,sum(case when quantity*inout_flag<=0 then sub_amount*inout_flag*(-1) else 0 end) as x_amount,sum(quantity) saleNum,
                           sum(case when quantity*inout_flag>0 then sub_amount*inout_flag*(-1) else 0 end) as t_amount,
                            yj_get_unit_qty ('b',sum(quantity*unit_factor*inout_flag*(-1))::numeric,b_unit_factor,m_unit_factor,false) b_qty,
						    yj_get_unit_qty ('m',sum(quantity*unit_factor*inout_flag*(-1))::numeric,b_unit_factor,m_unit_factor,false) m_qty,
						    yj_get_unit_qty ('s',sum(quantity*unit_factor*inout_flag*(-1))::numeric,b_unit_factor,m_unit_factor,false) s_qty,
	                        yj_get_unit_qty ( 'b', (case when sheet_type ='X' then SUM ( quantity* unit_factor  ) else 0 end) :: NUMERIC, b_unit_factor, m_unit_factor ,false) x_b_qty,
	                        yj_get_unit_qty ( 'm', (case when sheet_type ='X' then SUM ( quantity* unit_factor  ) else 0 end) :: NUMERIC, b_unit_factor, m_unit_factor ,false) x_m_qty,
	                        yj_get_unit_qty ( 's', (case when sheet_type ='X' then SUM ( quantity* unit_factor  ) else 0 end) :: NUMERIC, b_unit_factor, m_unit_factor ,false) x_s_qty, 
	                        yj_get_unit_qty ( 'b', (case when sheet_type ='T' then SUM ( quantity* unit_factor  ) else 0 end) :: NUMERIC, b_unit_factor, m_unit_factor ,false) t_b_qty,
	                        yj_get_unit_qty ( 'm', (case when sheet_type ='T' then SUM ( quantity* unit_factor  ) else 0 end) :: NUMERIC, b_unit_factor, m_unit_factor ,false) t_m_qty,
	                        yj_get_unit_qty ( 's', (case when sheet_type ='T' then SUM ( quantity* unit_factor  ) else 0 end) :: NUMERIC, b_unit_factor, m_unit_factor ,false) t_s_qty, 
                            sum(sub_amount*inout_flag*(-1)) total,sum(quantity*inout_flag*(-1)*unit_factor*COALESCE(d.cost_price_avg,0)) avg_cost,
                            sum(quantity*inout_flag*(-1)*unit_factor*COALESCE(p.cost_price_spec,0)) spec_cost,
                            sum(quantity*inout_flag*(-1)*COALESCE(d.cost_price_buy,0)*unit_factor) buy_cost
                                 from sheet_sale_main sm 
                                    LEFT JOIN sheet_sale_detail d on sm.sheet_id =d.sheet_id and sm.company_id = d.company_id and d.company_id = {companyID}
                                    LEFT JOIN info_item_prop p on d.item_id = p.item_id and d.company_id = p.company_id and p.company_id = {companyID}
                                   
                                    left join (select item_id,(b->>'f1')::real as b_unit_factor,(m->>'f1')::real as m_unit_factor,b->>'f2' as b_unit_no,m->>'f2' as m_unit_no,s->>'f2' as s_unit_no from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no)) as json from info_item_multi_unit where company_id= {companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
                                    as errr(item_id int, s jsonb,m jsonb,b jsonb) ) mu on mu.item_id = d.item_id 
                                    LEFT JOIN info_supcust as isu on isu.supcust_id = sm.supcust_id and sm.company_id = isu.company_id and isu.company_id = {companyID} {condi} and isu.company_id = {companyID}
                                     GROUP BY sup_name,sm.supcust_id,d.item_id,b_unit_factor,m_unit_factor,sheet_type) t 
                                        group by sup_name,supcust_id) t order by {orderType} desc ";


            var sql = sql_nolimit + $@" limit {pageSize} offset {startRow};";
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("records", sql);
            sql = @$"SELECT round(sum(X_amount)::numeric,2) as saleTotal,round(sum(T_amount)::numeric,2) as returnTotal,round(sum(saleSum)::numeric,2) total,round(sum(profit)::numeric,2) profitTotal,round(sum(saleNum)::numeric,2) saleNum FROM 
                      ( {sql_nolimit} ) tt    ";
            QQ.Enqueue("total", sql);
            sql = $@"SELECT count(*) as count from ( {sql_nolimit} ) tt  ";
            QQ.Enqueue("count", sql);
            List<ExpandoObject> records = null;
            List<ExpandoObject> total = null;
            dr = await QQ.ExecuteReaderAsync();
            var count = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "records")
                {
                    records = await CDbDealer.GetRecordsFromDrAsync(dr, false);
                }
                else if (sqlName == "total")
                {
                    total = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    count = CPubVars.GetTextFromDr(dr, "count");
                }
            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, records, total, count });
        }

        [HttpGet]
        public async Task<JsonResult> GetClientSaleSum(string operKey, string sellerID, string startRow, int pageSize, string startTime, string endTime, string costPriceType, string orderType, string regionID, string departID, string clientID, string itemsID, string classID, string branchID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string departCondi = "";
            string departLeftJoinCondi = "";
            if (departID.IsValid())
            {
                departLeftJoinCondi = "left join info_operator io on sm.company_id=io.company_id and sm.seller_id=io.oper_id  ";
                departCondi = $" and io.depart_path like '%/{departID}/%' ";
            }
            if (pageSize > 500) pageSize = 500;
            //  if (pageSize == 400) pageSize = 430;
            //int dbPageSize = pageSize + 40;

            cmd.CommandText = $@"select cost_price_type from company_setting where company_id={companyID};";
            var dr = await cmd.ExecuteReaderAsync();
            if (costPriceType.IsInvalid())
            {
                while (dr.Read())
                {
                    costPriceType = CPubVars.GetTextFromDr(dr, "cost_price_type");
                }
            }
            dr.Close();

            if (costPriceType.IsInvalid()) costPriceType = "3";
            if(orderType== "saleNum")
            {
                orderType = "coalesce(b_qty,-100000) desc, coalesce(m_qty,-100000) desc, coalesce(s_qty,-100000) desc";
            }
            else
            {
                orderType += " desc";
            }
            var condi = $"where sm.company_id = {companyID}  and approve_time is not null and red_flag is null and (trade_type NOT IN ( 'J', 'H' ) or trade_type is null) ";
            if (sellerID.IsValid()) condi += $" and seller_id = {sellerID} ";
            if (startTime.IsValid() && endTime.IsValid()) condi += $" and sm.happen_time >= '{startTime}' and sm.happen_time <= '{endTime}'  and d.happen_time >= '{startTime}' and d.happen_time <= '{endTime}'";
            if (regionID.IsValid()) condi += $"and (region_id = {regionID} or other_region like '%{regionID}%')";
            if (clientID.IsValid()) condi += $"and sm.supcust_id = {clientID}";
            if (branchID.IsValid()) condi += $"and sm.branch_id = {branchID}";
			
            if (!string.IsNullOrEmpty(itemsID))
			{
				condi += $" and d.item_id in ({itemsID}) ";
			}

			if (!string.IsNullOrEmpty(classID))
			{
				string clsCondi = "";
				foreach (string c in classID.Split(','))
				{
					if (clsCondi != "") clsCondi += " or ";
					clsCondi += $"p.other_class like '%{c}%'";
				}
				clsCondi = "(" + clsCondi + ")";
				condi += $" and {clsCondi}";
			}

			var sql_nolimit = @$"
SELECT t.supcust_id,sup_name,X_amount,T_amount,saleSum,saleNum,b_qty,m_qty,s_qty,x_b_qty,x_m_qty,x_s_qty,t_b_qty,t_m_qty,t_s_qty,
 (case when {costPriceType} = 1 then round(cast(spec_profit as numeric),2)  when {costPriceType} = 2 then round(cast(avg_profit as numeric),2) WHEN {costPriceType} = 4 THEN round(CAST(recent_profit AS NUMERIC), 2) else round(cast(buy_profit as numeric),2) end) profit 
FROM 
(
    select supcust_id,round(sum(x_amount)::numeric,2) x_amount,round(sum(t_amount)::numeric,2) t_amount,round(sum(x_amount+t_amount)::numeric,2) as saleSum,sum(saleNum) as saleNum,
        round(sum(b_qty)::numeric,2) b_qty,
        round(sum(m_qty)::numeric,2) m_qty,
        round(sum(s_qty)::numeric,2) s_qty,
		round( SUM ( x_b_qty ) :: NUMERIC, 2 ) x_b_qty,
		round( SUM ( x_m_qty ) :: NUMERIC, 2 ) x_m_qty,
		round( SUM ( x_s_qty ) :: NUMERIC, 2 ) x_s_qty,
		round( SUM ( t_b_qty ) :: NUMERIC, 2 ) t_b_qty,
		round( SUM ( t_m_qty ) :: NUMERIC, 2 ) t_m_qty,
		round( SUM ( t_s_qty ) :: NUMERIC, 2 ) t_s_qty,
        round((sum(total)-sum(disc)-sum(spec_cost))::numeric,2) spec_profit,
        round((sum(total)-sum(disc)-sum(avg_cost))::numeric,2) avg_profit,
        round((sum(total)-sum(disc)-sum(buy_cost))::numeric,2) buy_profit,
        round((sum(total) - sum(disc) - sum(recent_cost))::numeric, 2)  recent_profit
    from
    (
        select sm.supcust_id,d.item_id,sum(case when quantity*inout_flag<=0 then sub_amount*inout_flag*(-1) else 0 end) as x_amount,sum(quantity) saleNum,
             sum(case when quantity*inout_flag>0 then sub_amount*inout_flag*(-1) else 0 end) as t_amount,

                (sum(quantity*unit_factor*inout_flag*(-1))::numeric/b_unit_factor)::integer as b_qty,
                (sum(quantity*unit_factor*inout_flag*(-1))::numeric%b_unit_factor::numeric/m_unit_factor)::integer as m_qty,
                sum(quantity*unit_factor*inout_flag*(-1))::numeric % coalesce(b_unit_factor,10000)::numeric % coalesce(m_unit_factor,10000)::numeric as s_qty,

               
                (sum(case when sheet_type ='X' then quantity* unit_factor else 0 end)::numeric/b_unit_factor)::integer as x_b_qty,
                (sum(case when sheet_type ='X' then quantity* unit_factor else 0 end)::numeric%b_unit_factor::numeric/m_unit_factor)::integer as x_m_qty,
                 sum(case when sheet_type ='X' then quantity* unit_factor else 0 end)::numeric % coalesce(b_unit_factor,10000)::numeric % coalesce(m_unit_factor,10000)::numeric as x_s_qty,

                (sum(case when sheet_type ='T' then quantity* unit_factor else 0 end)::numeric/b_unit_factor)::integer as t_b_qty,
                (sum(case when sheet_type ='T' then quantity* unit_factor else 0 end)::numeric%b_unit_factor::numeric/m_unit_factor)::integer as t_m_qty,
                 sum(case when sheet_type ='T' then quantity* unit_factor else 0 end)::numeric % coalesce(b_unit_factor,10000)::numeric % coalesce(m_unit_factor,10000)::numeric as t_s_qty,

                sum(sub_amount*inout_flag*(-1)) total,sum(quantity*inout_flag*(-1)*unit_factor*COALESCE(d.cost_price_avg,0)) avg_cost,
                sum(quantity*inout_flag*(-1)*unit_factor*COALESCE(p.cost_price_spec,0)) spec_cost,
                sum(quantity*inout_flag*(-1)*COALESCE(d.cost_price_buy,0)*unit_factor) buy_cost,
                sum(quantity * inout_flag * (-1) * COALESCE(d.cost_price_recent, 0) * unit_factor) recent_cost,
				sum(case when d.row_index=1 then COALESCE(disc_amount,0)*inout_flag*(-1) else 0 end ) disc
        from sheet_sale_main sm 
        LEFT JOIN sheet_sale_detail d on sm.sheet_id =d.sheet_id and d.company_id = {companyID}
        LEFT JOIN info_item_prop p on d.item_id = p.item_id and p.company_id = {companyID}
        LEFT JOIN info_supcust sc on sm.supcust_id=sc.supcust_id and sc.company_id={companyID}
        {departLeftJoinCondi}
        left join 
        (
            select item_id,(b->>'f1')::real as b_unit_factor,(m->>'f1')::real as m_unit_factor,b->>'f2' as b_unit_no,m->>'f2' as m_unit_no,s->>'f2' as s_unit_no from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no)) as json from info_item_multi_unit where company_id= {companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
            as errr(item_id int, s jsonb,m jsonb,b jsonb) 
        ) mu on mu.item_id = d.item_id
        {condi} {departCondi}
        GROUP BY sm.supcust_id,d.item_id,b_unit_factor,m_unit_factor,sheet_type
    ) t 
    group by supcust_id
) t 
LEFT JOIN info_supcust isu on t.supcust_id=isu.supcust_id and isu.company_id = {companyID}
order by {orderType} ";

            var sql = sql_nolimit + $@" limit {pageSize} offset {startRow};";
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("records", sql);
            sql = @$"SELECT count(*) as count,round(sum(X_amount)::numeric,2) as saleTotal,round(sum(T_amount)::numeric,2) as returnTotal,round(sum(saleSum)::numeric,2) total,round(sum(profit)::numeric,2) profitTotal,round(sum(saleNum)::numeric,2) saleNum FROM 
                      ( {sql_nolimit} ) tt    ";
            QQ.Enqueue("total", sql);
            // sql = $@"SELECT count(*) as count from ( {sql_nolimit} ) tt  ";
            //    QQ.Enqueue("count", sql);
            List<ExpandoObject> records = null;
            List<ExpandoObject> total = null;
            dr = await QQ.ExecuteReaderAsync();
            var count = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "records")
                {
                    records = await CDbDealer.GetRecordsFromDrAsync(dr, false);
                }
                else if (sqlName == "total")
                {
                    total = CDbDealer.GetRecordsFromDr(dr, false);
                    if (total != null)
                    {
                        count = ((dynamic)total[0]).count;
                    }
                }

            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, records, total, count });
        }


        /// <summary>
        /// 库存表
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="searchStr"></param>
        /// <param name="classID"></param>
        /// <param name="branchID"></param>
        /// <param name="showZeroStock"></param>
        /// <param name="showExpiredItems"></param>
        /// <param name="startRow"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>


        [HttpGet]
        public async Task<JsonResult> GetStockReport(string operKey, string searchStr, string classID, string branchID, string branchPositionName, bool showZeroStock, bool showPositionStock, bool showBatchStock, bool showExpiredItems, int startRow, int pageSize, string costPriceType, string sortType)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            CMySbDataReader dr;
            string recentPriceTime = "";
            if (costPriceType.IsInvalid())
            {
                dynamic setting = await CDbDealer.Get1RecordFromSQLAsync($"select cost_price_type, setting->>'recentPriceTime' as recentpricetime from company_setting where company_id={companyID};", cmd);
                if (setting != null)
                {
                    costPriceType = setting.cost_price_type;
                    recentPriceTime = setting.recentpricetime;
                }
            }

            if (costPriceType.IsInvalid()) costPriceType = "3";

            string condi = $"where ip.company_id = {companyID} ";
            string condiStock = "";
            if (searchStr.IsValid())
            {
                string b = "%";
                if (searchStr.Length >= 6) b = "";
                condi += $" and (ip.item_name ilike '%{searchStr}%' or ip.py_str ilike '%{searchStr}%' or ip.py_str1 ilike '%{searchStr}%' or ip.item_no ilike '%{searchStr}%' or s_barcode like '%{searchStr}{b}' or b_barcode like '%{searchStr}{b}' or m_barcode like '%{searchStr}{b}' or ip.mum_attributes::text ilike '%{searchStr}%')";
            }
            //condi += $" and (ip.item_name ilike '%{searchStr}%' or ip.item_no ilike '%{searchStr}%' or ip.py_str ilike '%{searchStr}%' or s_barcode like '%{searchStr}%' or b_barcode like '%{searchStr}%' or m_barcode like '%{searchStr}%') ";
            //if (searchStr != null) condi += $" and (ip.item_name ilike '%{searchStr}%' or ip.item_no ilike '%{searchStr}%' or ip.py_str ilike '%{searchStr}%') ";

            /*        if (branchID != null) condiStock = $" and branch_id = {branchID} ";*/
            if (branchID != null)
            {
                string[] branchIdArr = branchID.Split(",");
                string branchIdCondi = "";
                foreach (var branchId in branchIdArr)
                {
                    if (branchIdCondi != "") branchIdCondi += " or ";
                    branchIdCondi += $" s.branch_id = {branchId} ";
                }
                if (branchIdCondi != "")
                {
                    branchIdCondi = " and (" + branchIdCondi + ")";
                }
                condiStock += branchIdCondi;
            }
            if (branchPositionName != null)
            {
                string[] branchPositionArr = branchPositionName.Split(",");
                string branchPositionCondi = "";
                foreach (var branchposition in branchPositionArr)
                {
                    if (branchPositionCondi != "") branchPositionCondi += " or ";
                    if (branchposition == "默认库位")
                    {
                        branchPositionCondi += $" s.branch_position = 0 ";
                    }
                    else
                    {
                        branchPositionCondi += $" branch_position_name = '{branchposition}' ";
                    }
                }
                if (branchPositionCondi != "")
                {
                    branchPositionCondi = " and (" + branchPositionCondi + ")";
                }
                condiStock += branchPositionCondi;
            }

            if (classID != null) condi += $" and ip.other_class like '%/{classID}/%' ";
            //if (!showZeroStock && !showExpiredItems)
            //{
            //    condi += $" and (stock_qty <> 0) ";
            //}
            //if (showZeroStock && !showExpiredItems) condi += $" and ((ip.status=1 or ip.status is null) or (ip.status = 0 and stock_qty<>0))";
            //if (showExpiredItems && !showZeroStock) condi += $" and ((stock_qty<>0) or (ip.status=0 and stock_qty=0))";
            if (!showZeroStock)
            {
                condi += $" and (stock_qty <> 0) ";
            }
            string groupBySql = "group by item_id";
            string fields = "";
            string showFields = "";
            if (showPositionStock)
            {
                fields += ",s.branch_id,branch_name,COALESCE(s.branch_position,0) as branch_position,COALESCE(branch_position_name,'') as branch_position_name";
                groupBySql += $" ,branch_name,s.branch_id,s.branch_position,branch_position_name ";
                showFields += ",branch_id,branch_name,branch_position,branch_position_name";
            }
            if (showBatchStock)
            {
                fields += ",s.batch_id,COALESCE(batch_no,'') as batch_no,SUBSTRING(COALESCE(produce_date::text,''),1,10) as produce_date";
                groupBySql += $" ,s.batch_id,batch_no,produce_date";
                showFields += ",batch_no,batch_id,produce_date";
            }
            if (!showExpiredItems)
            {
                condi += $" and (ip.status=1 or ip.status is null)";
            }
            string itemSql = $@"
select ip.item_id,item_name,item_class,class_name,status,yj_get_unit_rates(b_unit_factor::float4,m_unit_factor::float4) as unit_rates,ic.order_index,stock_qty,avail_stock_qty,b_unit_factor,
(COALESCE(ip.cost_price_avg,0)*COALESCE(stock.stock_qty,0)) avg_cost,
(COALESCE(ip.cost_price_spec,0)*COALESCE(stock.stock_qty,0)) spec_cost,
(COALESCE(s_buy_price::numeric,0)*COALESCE(stock.stock_qty,0)) buy_cost,
(COALESCE((ip.cost_price_recent::jsonb->'avg{recentPriceTime}')::numeric,0)*COALESCE(stock.stock_qty,0)) recent_cost,
round((case when b_wholesale_price is not null and stock_qty is not null and stock_qty <> 0  then b_wholesale_price::numeric *( stock_qty/b_unit_factor ::numeric ) else (COALESCE(s_wholesale_price::numeric,0)*COALESCE(stock.stock_qty,0))   end)::numeric,2)       wholesale_amount,
round((case when b_contract_price is not null and stock_qty is not null and stock_qty <> 0  then b_contract_price::numeric *( stock_qty/b_unit_factor ::numeric ) else (COALESCE
  (s_contract_price::numeric,0)*COALESCE(stock.stock_qty,0))   end)::numeric,2)       contract_amount,
(sign(COALESCE(stock.stock_qty,0))*floor(COALESCE(abs(stock.stock_qty),0) / (b_unit_factor)::numeric)) b_stock,b_unit_no,
    (CASE WHEN m_unit_factor is null THEN null ELSE sign(COALESCE(stock.stock_qty,0))*floor((COALESCE(abs(stock.stock_qty),0)%(b_unit_factor)::numeric)/(m_unit_factor)::numeric) END) as m_stock,m_unit_no,
    (CASE WHEN b_unit_factor is NOT NULL AND m_unit_factor is NOT NULL THEN sign(stock.stock_qty)*floor(COALESCE(abs(stock.stock_qty),0)%(b_unit_factor)::numeric%(m_unit_factor)::numeric)
			WHEN b_unit_factor is NOT NULL AND m_unit_factor is NULL THEN round(COALESCE(stock.stock_qty,0)%(b_unit_factor)::numeric,3) 
			WHEN b_unit_factor is NULL AND m_unit_factor is NULL  THEN round(COALESCE(stock.stock_qty,0),3) END) s_stock,s_unit_no,
    (sign(COALESCE(stock.avail_stock_qty,0))*floor(COALESCE(abs(stock.avail_stock_qty),0) / (b_unit_factor)::numeric)) avail_b_stock,
    (CASE WHEN m_unit_factor is null THEN null ELSE sign(COALESCE(stock.avail_stock_qty,0))*floor((COALESCE(abs(stock.avail_stock_qty),0)%(b_unit_factor)::numeric)/(m_unit_factor)::numeric) END) as avail_m_stock,
    (CASE WHEN b_unit_factor is NOT NULL AND m_unit_factor is NOT NULL THEN sign(stock.avail_stock_qty)*floor(COALESCE(abs(stock.avail_stock_qty),0)%(b_unit_factor)::numeric%(m_unit_factor)::numeric)
			WHEN b_unit_factor is NOT NULL AND m_unit_factor is NULL THEN round(COALESCE(stock.avail_stock_qty,0)%(b_unit_factor)::numeric,3) 
			WHEN b_unit_factor is NULL AND m_unit_factor is NULL  THEN round(COALESCE(stock.avail_stock_qty,0),3) END) avail_s_stock {showFields}

from info_item_prop ip
LEFT JOIN 
(
   select item_id,s->>'f1' s_unit_factor,s->>'f2' s_unit_no,s->>'f3' s_barcode, s->>'f4' s_buy_price,s->>'f5' s_wholesale_price,s->>'f6' s_contract_price,
                  m->>'f1' m_unit_factor,m->>'f2' m_unit_no,m->>'f3' m_barcode,m->>'f4' m_buy_price,m->>'f5' m_wholesale_price,m->>'f6' m_contract_price,
                  b->>'f1' b_unit_factor,b->>'f2' b_unit_no,b->>'f3' b_barcode, b->>'f4' b_buy_price,b->>'f5' b_wholesale_price,b->>'f6' b_contract_price
    from 
    ( 
       select item_id,b,m,s 
       from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,barcode,buy_price,wholesale_price,contract_price)) as json from info_item_multi_unit where company_id= {companyID}  order by item_id',$$values ('s'::text), ('m'::text),('b'::text)$$) 
       as errr(item_id int, s jsonb,m jsonb,b jsonb) 
    ) m 
) t on ip.item_id=t.item_id 
left join  
(
    select item_id,sum(stock_qty) as stock_qty,sum(stock_qty-COALESCE(sell_pend_qty,0)) as avail_stock_qty {fields} from stock s
left join info_branch ib on ib.branch_id = s.branch_id and ib.company_id = {companyID}
left join info_branch_position ibp on ibp.branch_id = s.branch_id and ibp.branch_position = s.branch_position and ibp.company_id = {companyID}
left join info_item_batch itb on itb.company_id = {companyID} and itb.batch_id = s.batch_id
where s.company_id = {companyID} {condiStock} {groupBySql} 
) stock on stock.item_id = ip.item_id 
LEFT JOIN 
(
   select * from info_item_class where company_id = {companyID} 
) as ic on ip.item_class = ic.class_id {condi}";
            string orderBy = "order by order_index,class_name,class_id,item_name";
            if (sortType == "order_index")
            {
                orderBy = "order by order_index asc";
            }
            if (sortType == "item_name")
            {
                orderBy = "order by ip.py_str asc";
            }

            SQLQueue QQ = new SQLQueue(cmd);
            var sql = $@"
select item_id,item_name,item_class,class_name,status,order_index,unit_rates,b_unit_no,m_unit_no,s_unit_no,stock_qty,avail_stock_qty,b_unit_factor, b_stock,m_stock,s_stock,avail_b_stock,avail_m_stock,avail_s_stock, concat(case when b_stock<>0 then b_stock::text else '' end,case when b_stock<>0 then b_unit_no else '' end,case when m_stock<>0 then m_stock::text else '' end,case when m_stock<>0 then m_unit_no else '' end,case when s_stock<>0 then s_stock::text else '' end,case when s_stock<>0 then s_unit_no else '' end) qty,
concat(case when avail_b_stock<>0 then avail_b_stock::text else '' end,case when avail_b_stock<>0 then b_unit_no else '' end,case when avail_m_stock<>0 then avail_m_stock::text else '' end,case when avail_m_stock<>0 then m_unit_no else '' end,case when avail_s_stock<>0 then avail_s_stock::text else '' end,case when avail_s_stock<>0 then s_unit_no else '' end) avail_qty,
(case when {costPriceType} = 1 then round(cast(spec_cost as numeric),2) 
when {costPriceType} = 2 then round(cast(avg_cost as numeric),2) else round(cast(buy_cost as numeric),2) end) costs,wholesale_amount,contract_amount {showFields}
from ( {itemSql} {orderBy} limit {pageSize} offset {startRow} ) t";
            QQ.Enqueue("data", sql);
            if (startRow == 0)
            {
                sql = $@"
select item_class,class_name,order_index,COALESCE(sum(b_stock),0) b_total_stock,COALESCE(sum(m_stock),0) m_total_stock,COALESCE(sum(s_stock),0) s_total_stock,
(case when {costPriceType} = 1 then round(cast(sum(spec_cost) as numeric),2) 
        when {costPriceType} = 2 then round(cast(sum(avg_cost) as numeric),2) else round(cast(sum(buy_cost) as numeric),2) end) class_costs, round(cast(sum(wholesale_amount) as numeric),2) class_wholesale,
round(cast(sum(contract_amount) as numeric),2) class_contract
from ( {itemSql} ) dd group by item_class,class_name,order_index order by order_index,class_name,item_class";
                QQ.Enqueue("class_total", sql);
                sql = $@"
select count(*) rows_count,concat(COALESCE(sum(b_stock),0),'大',COALESCE(sum(m_stock),0),'中',COALESCE(sum(s_stock),0),'小') total, 
concat(COALESCE(sum(avail_b_stock),0),'大',COALESCE(sum(avail_m_stock),0),'中',COALESCE(sum(avail_s_stock),0),'小') avail_total, 
sum(stock_qty) total_stock_qty,sum(stock_qty) total_avail_stock_qty,
COALESCE(sum(b_stock),0) b_total_stock,COALESCE(sum(m_stock),0) m_total_stock,COALESCE(sum(s_stock),0) s_total_stock,
(case when {costPriceType} = 1 then round(cast(sum(spec_cost) as numeric),2) 
    when {costPriceType} = 2 then round(cast(sum(avg_cost) as numeric),2)
    when {costPriceType} = 4 then round(cast(sum(recent_cost) as numeric),2) 
else round(cast(sum(buy_cost) as numeric),2) end) costs_total,  round(cast(sum(wholesale_amount) as numeric),2) wholesale_total,round(cast(sum(contract_amount) as numeric),2) contract_total
from( {itemSql} ) dd ";
                QQ.Enqueue("total", sql);
            }


            /*if (startRow == 0)
            {
                sql = @$"
select count(ip.item_name) count from info_item_prop ip
LEFT JOIN 
(
    select item_id,s->f3 s_barcode,m->f3 m_barcode,b->f3 b_barcode from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,barcode)) as json from info_item_multi_unit where company_id= {companyID} order by item_id',$$values ('s'::text), ('m'::text),('b'::text)$$) 
            as errr(item_id int, s jsonb,m jsonb,b jsonb) 
) t on ip.item_id=t.item_id 
left join
(
    select item_id,sum(stock_qty) as stock_qty from stock where company_id = {companyID} {condiStock}  group by item_id 
) stock on stock.item_id = ip.item_id 
LEFT JOIN 
(
    select * from info_item_class where company_id = {companyID}
) as ic on ip.item_class = ic.class_id {condi}";
                QQ.Enqueue("count", sql);

            }*/
            List<ExpandoObject> records = null;
            List<ExpandoObject> class_total = null;
            dr = await QQ.ExecuteReaderAsync();
            var count = "";
            var total = "";
            string total_stock_qty = "", total_avail_stock_qty = "";


            var total_cost = "";
            var wholesale_total = "";
            var contract_total = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    records = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "class_total")
                {
                    class_total = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "total")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                    total_stock_qty = CPubVars.GetTextFromDr(dr, "total_stock_qty");
                    total_avail_stock_qty = CPubVars.GetTextFromDr(dr, "total_avail_stock_qty");
                    count = CPubVars.GetTextFromDr(dr, "rows_count");

                    total_cost = CPubVars.GetTextFromDr(dr, "costs_total");
                    wholesale_total = CPubVars.GetTextFromDr(dr, "wholesale_total");
                    contract_total = CPubVars.GetTextFromDr(dr, "contract_total");
                }
                /*else if (sqlName == "count")
                {
                    dr.Read();
                    count = CPubVars.GetTextFromDr(dr, "count");
                }*/
            }
            QQ.Clear();
            /*
            //List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string total = "";
            Dictionary<string, double> units = new Dictionary<string, double>();
            void addUnit(string unit,string qty)
            {
                if (qty == "") return;
                double nQty = Convert.ToDouble(qty);
                if (!units.ContainsKey(unit))
                {
                    units.Add(unit, nQty);
                }
                else
                {
                    units[unit] += nQty;
                }
            }
            List<dynamic> records = new List<dynamic>();
            
            foreach(dynamic record in data)
            {
                string b = record.b;
                string bu = record.bu;
                string m = record.m;
                string mu = record.mu;
                string s = record.s;
                string su = record.su;
                string qty = "";
                if (b != "") qty += b + bu;
                if (m != "") qty += m + mu;
                if (s != "") qty += s + su;
                record.qty = qty; 
             
                if(records.Count < pageSize)
                {
                    records.Add(new { record.item_name, qty });
                } 
             
                if (startRow == 0)
                {
                    if (b != "" && b != "0")
                        addUnit(bu, b);
                    else if (m != "" && m != "0")
                        addUnit(mu, m);
                    else if (s != "" && s != "0") addUnit(su, s);
                }
               
            }
            if (startRow == 0)
            {
                foreach (var k in units)
                {
                    total += CPubVars.FormatMoney(k.Value, 2) + k.Key;
                }
            }
               */
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, records, class_total, total, total_stock_qty, total_avail_stock_qty, total_cost, wholesale_total, contract_total, count });
        }




        /// <summary>
        /// 预收款报表 根据（时间，业务员，预收款方）
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="dateType"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="sellerID"></param>
        /// <param name="subID"></param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> GetPrepay(string operKey, string dateType, string startDate, string endDate, string sellerID, string subID, int pageSize, int startRow, string custID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string condi = "";
            if (!sellerID.IsNullOrEmpty()) condi += $"and ah.seller_id={sellerID} ";
            if (!custID.IsNullOrEmpty()) condi += $"and ah.supcust_id={custID} ";
            if (subID != null) condi += $"and ah.sub_id = {subID}";
            var today = DateTime.Today.ToText();
            int weekDay = (short)DateTime.Today.DayOfWeek;
            var month = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1).ToText();
            var monday = DateTime.Today.AddDays(1 - weekDay).ToText();

            switch (dateType)
            {
                case "month":
                    condi += $"and ah.happen_time >= '{month}'";
                    break;
                case "week":
                    condi += $"and ah.happen_time >= '{monday}'";
                    break;
                case "today":
                    condi += $"and ah.happen_time >= '{today}' ";
                    break;
                case "appointDay":
                    if (startDate.IsValid() && endDate.IsValid())
                        condi += $"and ah.happen_time >= '{startDate}' and ah.happen_time <= '{endDate}'";
                    break;

            }
            var sql_noLimit = $@"select ah.supcust_id,isu.sup_name,pw.sub_name,sum(case when ah.change_amount>0 THEN ah.change_amount else 0 end) as plus_amount,
            sum(case when ah.change_amount<0 THEN ah.change_amount else 0 end) as minus_amount,pb.balance as now_balance from
(
     select ah.*,
    ( case when sm.seller_id is not null then sm.seller_id when pp.seller_id is not null then pp.seller_id when gam.seller_id is not null then gam.seller_id when ad.seller_id is not null then ad.seller_id when ad_pw1.seller_id is not null then ad_pw1.seller_id when ad_pw2.seller_id is not null then ad_pw2.seller_id when fm.seller_id is not null then fm.seller_id else null end
    )  seller_id 
    from client_account_history as ah
     LEFT JOIN ( select sheet_id, seller_id,sheet_type from sheet_sale_main where company_id = {companyID}  ) sm ON sm.sheet_id = ah.sheet_id and sm.sheet_type = ah.sheet_type
    LEFT JOIN ( select sheet_id,getter_id as seller_id,sheet_type from sheet_prepay where company_id = {companyID} ) pp ON pp.sheet_id = ah.sheet_id and pp.sheet_type = ah.sheet_type
    LEFT JOIN ( select sheet_id,getter_id as seller_id,sheet_type from sheet_get_arrears_main where company_id = {companyID} ) gam ON gam.sheet_id = ah.sheet_id and 'SK' = ah.sheet_type
    LEFT JOIN ( select sheet_id,seller_id,sheet_type from sheet_item_ordered_adjust_main where company_id = {companyID} ) ad on ad.sheet_id=ah.sheet_id and ad.sheet_type = substr(ah.sheet_type,1,4)
    LEFT JOIN ( select sheet_id,seller_id,sheet_type from sheet_item_ordered_adjust_main where company_id ={companyID} and coalesce(payway1_amount,0)<>0 ) ad_pw1 on ad_pw1.sheet_id=ah.sheet_id and ad_pw1.sheet_type = substr(ah.sheet_type,1,4)
    LEFT JOIN ( select sheet_id,seller_id,sheet_type from sheet_item_ordered_adjust_main where company_id = {companyID} and coalesce(payway2_amount,0)<>0 ) ad_pw2 on ad_pw2.sheet_id=ah.sheet_id and ad_pw2.sheet_type = substr(ah.sheet_type,1,4)
    LEFT JOIN ( select sheet_id,getter_id as seller_id,sheet_type from sheet_fee_out_main where company_id = {companyID} ) fm on fm.sheet_id=ah.sheet_id and fm.sheet_type = ah.sheet_type
) ah
     left join info_supcust as isu on ah.supcust_id = isu.supcust_id and isu.company_id ={companyID}
     left join cw_subject as pw on pw.sub_id = ah.sub_id and pw.company_id ={companyID}
     left join prepay_balance as pb on pb.supcust_id = ah.supcust_id and pb.sub_id = ah.sub_id and pb.company_id ={companyID}
     where ah.company_id = {companyID} and ah.sub_type = 'YS' and ah.red_flag is null {condi}  group by ah.supcust_id,isu.sup_name,pw.sub_name,pb.balance order by isu.sup_name";
            var sql = sql_noLimit + $" limit {pageSize} offset {startRow}";
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("data", sql);
            sql = @$"select count(*) count from ({sql_noLimit}) tt ";
            QQ.Enqueue("count", sql);
            sql = $@"select sum(plus_amount) as totalPlus,sum(minus_amount) as totalMinus,sum(now_balance) as total from 
                     ({sql_noLimit}) t ";
            QQ.Enqueue("total", sql);
            List<ExpandoObject> records = null;
            List<ExpandoObject> total = null;
            var dr = await QQ.ExecuteReaderAsync();
            var count = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    records = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    count = CPubVars.GetTextFromDr(dr, "count");
                }
                else if (sqlName == "total")
                {
                    total = CDbDealer.GetRecordsFromDr(dr, false);

                }
            }
            QQ.Clear();
            var data = records.GroupBy(x => (x as dynamic).supcust_id).Select(x =>
            {
                var dy = x as dynamic;
                decimal plus_amount = 0;
                decimal minus_amount = 0;
                decimal balance_total = 0;
                var subNodes = x.ToList();
                subNodes.ForEach(y =>
                {
                    plus_amount += decimal.TryParse((y as dynamic).plus_amount, out decimal plusMoney) ? plusMoney : 0;
                    minus_amount += decimal.TryParse((y as dynamic).minus_amount, out decimal minusMoney) ? minusMoney : 0;
                    balance_total += decimal.TryParse((y as dynamic).now_balance, out decimal money) ? money : 0;
                });
                return new
                {
                    clientName = (x.FirstOrDefault() as dynamic)?.sup_name ?? "",
                    plus_amount,
                    minus_amount,
                    balance_total,
                    subNodes,
                };
            });

            return Json(new { result = "OK", msg = "", data, total, count });

        }
        [HttpGet]
        public async Task<JsonResult> GetRealTime(string operKey, int pageSize, int startRow, string regionID, string sort,bool isShowAllClient)
        {

            Security.GetInfoFromOperKey(operKey, out string companyID);
            SQLQueue QQ = new SQLQueue(cmd);
            var regioncondition = "";
            var sortCondition = "";
            if (regionID != null)
            {
                regioncondition = $"AND other_region ilike '%{regionID}%' ";
            }
            if (sort.IsValid())
            {
                string[] paramSortKVs = sort.Split("$");
                List<string> sortCmds = new List<string>();
                foreach (var kv in paramSortKVs)
                {
                    string[] arr = kv.Split("#");
                    string orderKey = arr[0];
                    string orderType = arr[1];
                    switch (orderKey)
                    {
                        case "unSaleDays":
                            // lastsale desc
                            // -1未销售 此处取999999用于排序
                            sortCmds.Add("CASE WHEN lastsale = -1 THEN 999999 ELSE lastsale END" + " " + orderType);
                            break;
                        case "unVisitDays":
                            // lastsale desc
                            sortCmds.Add("CASE WHEN lastvisit = -1 THEN 999999 ELSE lastvisit END" + " " + orderType);
                            break;
                    }
                }
                if (sortCmds.Count > 0)
                {
                    sortCondition += "ORDER BY " + string.Join(",", sortCmds);
                }
            }
            else
            {
                sortCondition += "ORDER BY CASE WHEN lastsale = -1 THEN 999999 ELSE lastsale END desc,CASE WHEN lastvisit = -1 THEN 999999 ELSE lastvisit END desc";
            }

            var showCondition = "";
            if(!isShowAllClient)
            {
                showCondition = "where (lastsale <> -1 or lastvisit <> -1)";
            } 

            var sql = $@"   select *  from (

SELECT
    sup_name,
	coalesce(case when (realtime ->> 'lastSaleTime'<>null or realtime ->> 'lastSaleTime'<>'' ) then  (date_part( 'day', current_date - CAST ( realtime ->> 'lastSaleTime' AS TIMESTAMP ) )) else -1 end,-1) as lastsale ,
	coalesce(case when (realtime ->> 'lastVisitTime'<>null or realtime ->> 'lastVisitTime'<>'' ) then  (date_part( 'day', current_date - CAST ( realtime ->> 'lastVisitTime' AS TIMESTAMP ) )) else -1 end,-1) as lastvisit 
FROM
	info_supcust sc
	left join realtime_supcust rs  ON rs.supcust_id = sc.supcust_id and sc.company_id= {companyID}
	where sc.company_id= {companyID} and sc.status <>'0' {regioncondition}    )t  {showCondition}  {sortCondition}  limit {pageSize} offset {startRow} ";
            QQ.Enqueue("data", sql);


            List<ExpandoObject> records = null;
            var counts = "";

            var dr = await QQ.ExecuteReaderAsync();

            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    records = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();

            counts = records.Count.ToString();

            return Json(new { result = "OK", msg = "", records, counts });
        }



        [HttpGet]
        public async Task<JsonResult> GetAccountHistory(string startTime, string endTime, string supcustID, string subType, string prepaySubId, string sellerID, string senderID, string operKey, int pageSize, int startRow)
        {

            Security.GetInfoFromOperKey(operKey, out string companyID);
            SQLQueue QQ = new SQLQueue(cmd);
            var timecondition = "";
            var prepayCondi = "";
            var senderIDdition = "";
            var sellerIDdition = "";
            var subTypeCondi = "";
            string clientCondi = "";

            if (!string.IsNullOrWhiteSpace(startTime) && !string.IsNullOrWhiteSpace(endTime))
            {
                timecondition = $"AND happen_time >= '{startTime}' AND happen_time <= '{endTime}' ";
            }




            if (!string.IsNullOrWhiteSpace(supcustID)) clientCondi += $" and (supcust_id = '{supcustID}' )";

            if (!string.IsNullOrWhiteSpace(subType)) subTypeCondi += $" and (sub_type= '{subType}' )";

            if (!string.IsNullOrWhiteSpace(prepaySubId)) prepayCondi += $" and (sub_id= '{prepaySubId}' )";

            if (!string.IsNullOrEmpty(senderID)) senderIDdition = $" and senders_id LIKE '%{senderID}%'";



            if (!string.IsNullOrEmpty(sellerID)) sellerIDdition = "and  seller_id =" + sellerID;



            string sumFields = "";
            if (startRow == 0)
            {
                sumFields = $@",count(0) over () as record_count,
                sum(change_amount) over() as sum_change
";
            }





            var sql = $@"
SELECT
	sheet_no,sheet_id,happen_time,approve_time,change_amount,now_balance,sheet_type,sub_name
    {sumFields}
FROM
(
	SELECT
	cah.* ,
	(
		CASE WHEN sm.seller_id IS NOT NULL THEN sm.seller_id WHEN pp.seller_id IS NOT NULL THEN pp.seller_id WHEN gam.seller_id IS NOT NULL THEN gam.seller_id  WHEN ad.seller_id IS NOT NULL THEN ad.seller_id WHEN fm.seller_id IS NOT NULL THEN fm.seller_id ELSE NULL END 
	) seller_id,
	(
			CASE WHEN sm.sheet_no IS NOT NULL THEN sm.sheet_no WHEN pp.sheet_no IS NOT NULL THEN pp.sheet_no  WHEN gam.sheet_no IS NOT NULL THEN gam.sheet_no WHEN ad.sheet_no IS NOT NULL THEN ad.sheet_no WHEN fm.sheet_no IS NOT NULL THEN fm.sheet_no ELSE NULL END 
    ) sheet_no, sm.senders_id 
	FROM
	(
        SELECT sheet_id,sheet_no ah_sheet_no,happen_time,approve_time,change_amount,now_balance,sheet_type,sub_id FROM client_account_history WHERE company_id = {companyID} and red_flag is null    {timecondition}  {subTypeCondi} {clientCondi}  {prepayCondi}  order by happen_time desc
    ) cah
    LEFT JOIN 
    ( 
        SELECT sheet_id, sheet_no, seller_id, senders_id, sheet_type FROM sheet_sale_main WHERE company_id = {companyID}
    ) sm ON sm.sheet_id = cah.sheet_id AND sm.sheet_type = cah.sheet_type 
    LEFT JOIN
    ( 
       SELECT sheet_id, sheet_no, getter_id AS seller_id, '' senders_id, sheet_type FROM sheet_prepay WHERE company_id = {companyID} 
    ) pp ON pp.sheet_id = cah.sheet_id AND pp.sheet_type = cah.sheet_type
	LEFT JOIN 
    (
        SELECT sheet_id, sheet_no, getter_id AS seller_id, '' senders_id, sheet_type FROM sheet_get_arrears_main WHERE company_id = {companyID} 
    ) gam ON gam.sheet_id = cah.sheet_id AND 'SK' = cah.sheet_type
	LEFT JOIN 
    ( 
            SELECT sheet_id, sheet_no, seller_id, '' senders_id, sheet_type FROM sheet_item_ordered_adjust_main WHERE company_id = {companyID} 
    ) ad ON ad.sheet_id = cah.sheet_id AND ad.sheet_type = cah.sheet_type
	LEFT JOIN
    (
        SELECT sheet_id, sheet_no, getter_id AS seller_id, '' senders_id, sheet_type FROM sheet_fee_out_main WHERE company_id = {companyID} 
    ) fm ON fm.sheet_id = cah.sheet_id AND fm.sheet_type = cah.sheet_type 
) cah
LEFT JOIN 
( 
    SELECT oper_id, oper_name AS seller_name FROM info_operator WHERE company_id = {companyID} 
) seller ON seller.oper_id = cah.seller_id
LEFT JOIN cw_subject cw on cw.sub_id=cah.sub_id and cw.company_id = {companyID}
WHERE true {sellerIDdition}  {senderIDdition} ORDER BY approve_time

  ";
            QQ.Enqueue("data", sql);


            if (pageSize == 0) pageSize = 1000;
            string sqlLimit = $"{sql} limit {pageSize} offset {startRow}";



            List<ExpandoObject> data = null;
            var dr = await QQ.ExecuteReaderAsync();

            string sum_change = "";
            //string sum_arrears_reduce = "";
            //string sum_prepay_add = "";
            //string sum_prepay_reduce = "";
            int recordCount = -1;

            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                    if (startRow == 0 && data.Count > 0)
                    {
                        dynamic first = data.First();
                        recordCount = Convert.ToInt32(first.record_count);
                        sum_change = first.sum_change;
                        //sum_arrears_reduce = first.sum_arrears_reduce;
                        //sum_prepay_add = first.sum_prepay_add;
                        //sum_prepay_reduce = first.sum_prepay_reduce;

                    }
                }

            }
            QQ.Clear();

            string result = "OK";
            string msg = "";



            return Json(new { result, msg, data, sum_change, recordCount });

        }

        [HttpGet]
        public async Task<JsonResult> GetBranchList(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string sql = $"SELECT branch_id, branch_name FROM info_branch WHERE company_id = {companyID} AND status = 1 ORDER BY branch_id";
            var data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            return Json(new { result = "OK", data });
        }
    }

}
