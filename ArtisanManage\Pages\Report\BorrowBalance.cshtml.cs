using ArtisanManage.Models;
using ArtisanManage.Pages.BaseInfo;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using static ArtisanManage.Models.PageQueryModel;

namespace ArtisanManage.Pages.BaseInfo
{
    public class BorrowBalanceModel : PageQueryModel
    {
        public BorrowBalanceModel(CMySbCommand cmd) : base(MenuId.borrowBalance)
        {
            this.cmd = cmd;
            this.PageTitle = "借货单余额查询";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea = "divHead",CtrlType="jqxDateTimeInput",ForQuery=false, SqlFld="bm.happen_time", CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date.AddDays(-30))+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期", FldArea = "divHead", CtrlType="jqxDateTimeInput",ForQuery=false, SqlFld="bm.happen_time",   CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }"
                }},
                {"supcust_id",CommonTool.GetDataItem("supcust_id",new DataItemChange{Checkboxes = true,SqlFld = "bsb.supcust_id",ForQuery=true,}) },
                {"seller_id",new DataItem(){FldArea="divHead",Title="业务员",Checkboxes=true, LabelFld="seller_name",ButtonUsage="list",SqlForOptions=CommonTool.selectSellers,CompareOperator="="}},
                //{"seller_id",new DataItem(){Title="业务员",LabelFld="seller_name",ButtonUsage="list",SqlForOptions=CommonTool.selectSellers,ForQuery=false}},
                //{"department_id",new DataItem(){Title="部门",ForQuery=true, LabelFld="department_name", CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=false,DropDownWidth="150",
                    //SqlForOptions=CommonTool.selectDepartments
                //}},
                //{"balance_min",new DataItem(){Title="余额≥",ForQuery=true,Width="80",PlaceHolder="最小余额"}},
                //{"balance_max",new DataItem(){Title="余额≤",ForQuery=true,Width="80",PlaceHolder="最大余额"}},
            };

            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                    Columns = new Dictionary<string, DataItem>()
                    {
                        {"borrow_sheet_id",new DataItem(){Title="借货单ID",Width="100",SqlFld="bsb.borrow_sheet_id",Hidden=true}},
                        {"sheet_no",new DataItem(){Title="借货单号",Width="150",SqlFld="bm.sheet_no",Linkable=true}},
                        {"sup_name",new DataItem(){Title="客户名称",Width="200",SqlFld="sc.sup_name"}},
                        {"seller_name", new DataItem(){Title="业务员",  Width="80",Sortable=true}},
                        {"borrow_mode_name",new DataItem(){Title="借货模式",Width="100",SqlFld="(case bsb.borrow_mode when 'QTY' then '按数量' when 'AMT' then '按金额' else '按数量' end)", Hidden = true}},
                        {"borrow_time",new DataItem(){Title="借货时间",Width="150",SqlFld="bsb.borrow_time"}},
                        {"total_amount",new DataItem(){Title="借货金额",Width="120",SqlFld="bsb.total_amount",ShowSum=true}},
                        {"returned_amount",new DataItem(){Title="已还金额",Width="120",SqlFld="bsb.returned_amount",ShowSum=true}},
                        {"balance_amount",new DataItem(){Title="剩余金额",Width="120",SqlFld="bsb.balance_amount",ShowSum=true}},
                        {"last_return_time",new DataItem(){Title="最后还货",Width="150",SqlFld="bsb.last_return_time"}},
                        //{"seller_name",new DataItem(){Title="业务员",Width="100",SqlFld="op.oper_name"}},
                        //{"mobile",new DataItem(){Title="联系电话",Width="120",SqlFld="sc.mobile"}},
                    },




            QueryFromSQL = @"
from borrow_sheet_balance bsb
inner join borrow_item_main bm on bsb.company_id = bm.company_id and bsb.borrow_sheet_id = bm.sheet_id
left join info_supcust sc on bsb.company_id = sc.company_id and bsb.supcust_id = sc.supcust_id
LEFT JOIN (select oper_id,
                           oper_name   as seller_name,
                           depart_id   as department_id_s,
                           depart_path as department_path_s,
                           depart_path as depart_path_s
                    from info_operator
                    where company_id = ~COMPANY_ID) seller on bm.seller_id = seller.oper_id
where bsb.company_id = ~COMPANY_ID and bm.happen_time > '~VAR_startDay' and bm.happen_time <= '~VAR_endDay'
  and bsb.status = 'ACTIVE'
    
  and bsb.balance_amount > 0
",
            QueryOrderSQL = "order by bsb.borrow_time desc"
            }
           }
          };
        }
        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
           
            SQLVariables["startDay"] = DataItems["startDay"].Value;
            SQLVariables["endDay"] = DataItems["endDay"].Value;
        }
        public async Task OnGet()
        {
            await InitGet(cmd);
        }
    }

    [Route("api/[controller]/[action]")]
    public class BorrowBalanceController : QueryController
    { 
        public BorrowBalanceController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            BorrowBalanceModel model = new BorrowBalanceModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            BorrowBalanceModel model = new BorrowBalanceModel(cmd); 
            
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }
        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            BorrowBalanceModel model = new BorrowBalanceModel(cmd); 
            return await model.ExportExcel(Request, cmd); 
        }

    }
}
