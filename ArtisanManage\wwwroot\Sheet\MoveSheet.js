﻿ 
//正则表达式，检验数字
function checkNumber(theObj) {
    var reg = /^[-|+]?[0-9]+.?[0-9]*$/;

    if (!reg.test(theObj) && theObj) {
        bw.toast("请输入正确数字", 2000)
 
    }

        return theObj;
    
}
function isNotEmptyButIsNaN(variable) {
    return (variable.length > 0 && isNaN(variable))
}


//检查没有商品名称但行内有数据的行
function rowHasDataButNoItemID(row) {
    if (row.item_id) {
        return false;
    }
    if (row.cost_amount || row.cost_price || row.quantity || row.wholesale_price || row.wholesale_sub_amount) {
        return true
    }
}
//function isDateValid(date) {
//    //var reg = /^((\d{2}(([02468][048])|([13579][26]))[\-\/\s]?((((0?[13578])|(1[02]))[\-\/\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\-\/\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\-\/\s]?((0?[1-9])|([1-2][0-9])))))|(\d{2}(([02468][1235679])|([13579][01345789]))[\-\/\s]?((((0?[13578])|(1[02]))[\-\/\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\-\/\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\-\/\s]?((0?[1-9])|(1[0-9])|(2[0-8]))))))(\s((([0-1][0-9])|(2?[0-3]))\:([0-5]?[0-9])((\s)|(\:([0-5]?[0-9])))))?$/;
//    var reg2 = /^(\d{4})-(\d{2})-(\d{2})$/;

//    return reg2.test(date)
//}
function GetSheetData() {
    var msg = "";
    var formData = getFormData();
    if (!formData.seller_id) {
        msg = '请选择业务员';
        return { result: 'Error', msg: msg }
    }
    if (!formData.from_branch_id) {
        msg = '请选择出仓仓库';
        return { result: 'Error', msg: msg }
    }
    if (!formData.to_branch_id) {
        msg = '请选择入仓仓库';
        return { result: 'Error', msg: msg }
    }
    formData.company_name = window.g_companySetting.companyName
    formData.OperKey = g_operKey;
    var rows = $('#jqxgrid').jqxGrid('getrows');
    var sheetRows = new Array;
    var from_branch_id = $("#from_branch_id").val().value
    var to_branch_id = $("#to_branch_id").val().value
    var rowIndex = 0;//记录正确行的下标
    var wholesale_total_amount = 0
    for (var i = 0; i < rows.length; i++) {
        var sheetRow = {};// new myJXC.CSheetRow();
        var row = $('#jqxgrid').jqxGrid('getrowdata', i);
        if (rowHasDataButNoItemID(row)) {
            msg = '第' + (i + 1) + '行存在数据但没有商品名称';
            return { result: 'Error', msg: msg }
        }
        if (row.item_id) {
            for (var fld in row) {
                sheetRow[fld] = row[fld];
            }
            if (!sheetRow.produce_date) {
                sheetRow.produce_date = ''
                sheetRow.batch_id = "0"
            }
            if (!sheetRow.batch_no) sheetRow.batch_no = ''
            if (!sheetRow.from_branch_position) sheetRow.from_branch_position = '0'
            if (!sheetRow.to_branch_position) sheetRow.to_branch_position = '0'
            if (!sheetRow.unit_no) {
                msg = '请选择' + sheetRow.item_name + '的单位';
                break;
            }
            if (!parseFloat(sheetRow.quantity)) {
                msg = '请输入' + sheetRow.item_name + '的数量';
                break;
            }
            if (sheetRow.produce_date && !isDateValid(sheetRow.produce_date)) {
                msg = '请输入第' + (i + 1) + '行商品正确的生产日期:230901或2023-09-01';
                break;
            } else if (!sheetRow.produce_date && sheetRow.batch_no) {
                msg = '请输入' + sheetRow.item_name + '的生产日期';
                break;
            }
            var bHaveInvalidBatchlevel = !sheetRow.batch_level || sheetRow.batch_level === "0"
            var bHaveValidProduceDate = sheetRow.produce_date && sheetRow.produce_date != '无产期'
            if (bHaveInvalidBatchlevel && (sheetRow.batch_no || bHaveValidProduceDate)) {
                msg = '第' + (i + 1) + '行商品没有开启严格产期/批次管理';
                break;
            }
            /*
               暂时去掉，有些客户需要不严格录入产期，2024-12-16 大相要求加入校验
              
               }*/
            if (sheetRow.batch_level && !sheetRow.produce_date) {
                msg = '请输入第' + (i + 1) + '行商品正确的生产日期, 或选择“无产期”';
                break;
            }
            if (sheetRow.batch_level == "1" && sheetRow.batch_no) {
                msg = '第' + (i + 1) + '行商品没有开启批次管理';
                break;
            }
            else if (isNaN(sheetRow.quantity)) {
                msg = sheetRow.item_name + '的数量输入错误，请输入正确的数量';
                break;
            }

            if (sheetRow.wholesale_price)
                sheetRow.wholesale_sub_amount = toMoney(sheetRow.wholesale_price * sheetRow.quantity, 2)
            if (sheetRow.wholesale_sub_amount)
                wholesale_total_amount += parseFloat(sheetRow.wholesale_sub_amount)
            if (sheetRow.contract_price) {
                sheetRow.real_price = sheetRow.contract_price
                sheetRow.sub_amount = sheetRow.contract_sub_amount
            }
            else {
                sheetRow.real_price = sheetRow.wholesale_price
                sheetRow.sub_amount = sheetRow.wholesale_sub_amount
            }
         

            /*
            if(isNaN(sheetRow.wholesale_price)) {
                msg = sheetRow.item_name + '的批发价输入错误，请输入正确的批发价'; 
                break;
            }
            if (isNaN(sheetRow.wholesale_sub_amount)) {
                msg = sheetRow.item_name + '的批发金额输入错误，请输入正确的批发金额';
                break;
            }
            if (isNaN(sheetRow.cost_amount_avg)) {
                msg = sheetRow.item_name + '的成本金额输入错误，请输入正确的成本金额';
                break;
            }
            if (isNaN(sheetRow.cost_price)) {
                msg = sheetRow.item_name + '的成本价输入错误，请输入正确的成本价';
                break;

            承包
            */
            sheetRows[rowIndex++] = sheetRow;//写入正确行下标
        }
    }
    if (msg) return { result: 'Error', msg: msg }
    if (sheetRows.length == 0) {
        return { result: 'Error', msg: '请输入至少一行商品' }
    }
    formData.wholesale_amount = wholesale_total_amount;
    formData.SheetRows = sheetRows;

    var result = msg ? 'Error' : "OK";
    return { result: result, msg: msg, sheet: formData };
}

function onPageReady(sheetRows) {
    $('#from_branch_id').on('change', function (a, b) {
        var rowsData = $("#jqxgrid").jqxGrid('getrows')
        let rowindexs = []
        for (let i = 0; i < rowsData.length; i++) {
            let row = rowsData[i]
            if (!row.item_id) continue
            rowindexs.push(i)
        }
        if (rowindexs.length > 0) {
            var from_branch_id = $('#from_branch_id').val().value
            from_branch_id = from_branch_id ? from_branch_id : "-1"
            GetBranchPosition(rowindexs, from_branch_id, "from","from")
        }
    })
    $('#to_branch_id').on('change', function (a, b) {
        var rowsData = $("#jqxgrid").jqxGrid('getrows')
        let rowindexs = []
        for (let i = 0; i < rowsData.length; i++) {
            let row = rowsData[i]
            if (!row.item_id) continue
            rowindexs.push(i)
        }
        if (rowindexs.length > 0) {
            var from_branch_id = $('#from_branch_id').val().value
            from_branch_id = from_branch_id ? from_branch_id : "-1"
            var to_branch_id = $('#to_branch_id').val().value
            to_branch_id = to_branch_id ? to_branch_id : "-1"
            GetBranchPosition(rowindexs, to_branch_id, "from","to")
        }
    })
    function mmRefreshStockQty() {
        console.log("window.g_queriedItems_bySheetRow")
        console.log(window.g_queriedItems_bySheetRow)
        if (!window.g_queriedItems) return
        var rows = $('#jqxgrid').jqxGrid('getrows');
        rows.forEach(function (row) {
            let rowFromBranchPosition = row.from_branch_position ? row.from_branch_position : "0"
            let rowToBranchPosition = row.to_branch_position ? row.to_branch_position : "0"
            let rowBatchId = row.batch_id ? row.batch_id : "0"
            var key = row.item_id + "," + rowFromBranchPosition + "_" + rowToBranchPosition + "_" + rowBatchId
            console.log(row.item_id, rowFromBranchPosition, rowToBranchPosition)
            var item = window.g_queriedItems_bySheetRow[key]
            var column1 = $('#jqxgrid').jqxGrid('getcolumn', 'from_branch_qty_unit');
            var column2 = $('#jqxgrid').jqxGrid('getcolumn', 'to_branch_qty_unit');
            console.log(item)
            if (column1 && column2 && item && item.from_stock_qty_unit && item.to_stock_qty_unit) {
                console.log(item.from_stock_qty_unit)
                row.from_branch_qty_unit = item.from_stock_qty_unit
                row.to_branch_qty_unit = item.to_stock_qty_unit
            }//   $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'stock_qty_unit', item.stock_qty_unit)
            if (column1 && column2 && item && item.from_usable_stock_qty_unit && item.to_usable_stock_qty_unit) {
                row.from_branch_available_qty = item.from_usable_stock_qty_unit
                row.to_branch_available_qty = item.to_usable_stock_qty_unit
            }
           rowIndex++
        })
        // $('#jqxgrid').jqxGrid('updategrid')

    }
    let temp_approve_time = $('#approve_time').text();
    var canRedAndChange = window.getRightValue('stock.sheetMove.red')

    if (canRedAndChange != "true") $('#btnRedAndChange').css('display', 'none');
    if (temp_approve_time) {
        var red_flag = $('#red_flag').val()
        if (red_flag) {
            $("#btnRedAndChange").attr('disabled', true);
        }
    } else {
        $("#btnRedAndChange").attr('disabled', true);
    }

    //if (unitRights) var seeInPrice= unitRights.delicacy.seeInPrice.value
            
    if (requestString('copy')) {
        var params = paramsFromSrcWindow
        let from_branch_id = $('#from_branch_id').val();
        if (from_branch_id == '') {//由采购单复制过来时，仓库字段名不一样
            $('#from_branch_id').jqxInput('val', { value: paramsFromSrcWindow.branch_id, label: paramsFromSrcWindow.branch_name || '' }); 
        }
        AddItemRows(0, params.branch_id, '', '', '', params.SheetRows, true)
    }
    let windowHeight = document.body.offsetHeight - 50
    let windowWidth = document.body.offsetWidth - 80
    $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 900, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
    $("#popQuickMove").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 500, width: 800, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
    $('#dateTimeInputStart').jqxDateTimeInput({ placeHolder: "开始日期",culture:'zh-CN' ,width: '200px', height: '25px', formatString: 'yyyy-MM-dd', value: null });
    $('#dateTimeInputEnd').jqxDateTimeInput({ placeHolder: "结束日期", culture: 'zh-CN', width: '200px', height: '25px', formatString: 'yyyy-MM-dd', value: null });
    $('.way-type-group').jqxRadioButtonGroup({
        change: (item) => {
            let moveType = ""
            console.log(item)
            if (item.checked) moveType = item.value
            window.moveWay = moveType
            handleTimeSelectShow()
        }
    });
    $('.date-type-group').jqxRadioButtonGroup({
        change: (item) => {
            let dateType = ""
            console.log(item)
            if (item.checked) dateType = item.value
            checkedEvent(dateType)
        }
    });
    GetClassTreeData()
    var approve_time = $('#approve_time').text();
    if (approve_time) {
        $('#btnApprove').attr('disabled', true);
        $("#btnSave").attr('disabled', true);
    }
 
    function AddItemRows(rowIndex, from_branch_id, to_branch_id, from_branch_position, to_branch_position, rows, bUsePriceInRows, bDontEditBatch = false) {
        console.log(rows)
        var items_id = ''
        //if (!from_branch_position) from_branch_position = "0"
        //if (!to_branch_position) to_branch_position = "0"
            rows.forEach(function (row) {
                if (items_id != '') items_id += ','
                items_id += row.item_id
            })
        var newRows = []
        rows.forEach(row => {

            var subRow = null
            if (row.b_qty) {
                subRow = JSON.parse(JSON.stringify(row))
                subRow.quantity = row.b_qty
                subRow.unit_no = row.b_unit_no
                subRow.unit_factor = row.b_unit_factor
                subRow.unit_relation1 = getRowUnitRelation(row)
                newRows.push(subRow)
            }

            if (row.m_qty) {
                subRow = JSON.parse(JSON.stringify(row))
                subRow.quantity = row.m_qty
                subRow.unit_no = row.m_unit_no
                subRow.unit_factor = row.m_unit_factor
                subRow.unit_relation1 = getRowUnitRelation(row)
                newRows.push(subRow)
            }

            if (row.s_qty) {
                subRow = JSON.parse(JSON.stringify(row))
                subRow.quantity = row.s_qty
                subRow.unit_no = row.s_unit_no
                subRow.unit_factor = row.s_unit_factor
                subRow.unit_relation1 = getRowUnitRelation(row)
                newRows.push(subRow)
            }

            if (!subRow) {
                newRows.push(row)
            }

        })
        rows = newRows
        var cost_price_type = ''
        if (g_companySetting && g_companySetting.costPriceType) {
            cost_price_type = g_companySetting.costPriceType
        }
        let defaultBranchPositionType = null
        let defaultFromBranchPositionID = "0"
        let defaultToBranchPositionID = "0"
        let defaultBranchPositionID="0"
        if (window.g_dicDefualtBranchPosition) {
            if (window.g_dicDefualtBranchPosition[from_branch_id]) defaultFromBranchPositionID = window.g_dicDefualtBranchPosition[from_branch_id].branch_position
            if (window.g_dicDefualtBranchPosition[to_branch_id]) defaultToBranchPositionID = window.g_dicDefualtBranchPosition[to_branch_id].branch_position
            if (defaultFromBranchPositionID == "0" || defaultToBranchPositionID == "0") defaultBranchPositionType = window.g_companySetting.defaultBranchPositionType
        }
        else {
           defaultBranchPositionType = window.g_companySetting.defaultBranchPositionType
        }
        if (defaultFromBranchPositionID == defaultToBranchPositionID) defaultBranchPositionID = defaultFromBranchPositionID
        else defaultBranchPositionID = defaultFromBranchPositionID + "," + defaultToBranchPositionID
        let isShowNegativeStock = false
        if (window.g_companySetting && window.g_companySetting.showNegativeStock && window.g_companySetting.showNegativeStock == "True") {
            isShowNegativeStock = true
        }
        let isShowFromBranchPosition = false
        let isShowToBranchPosition = false

        let isContractSeller = false;
        if (window.g_operRights && window.g_operRights.delicacy && window.g_operRights.delicacy.isContractSeller) {
        if (window.g_operRights.delicacy.isContractSeller.value) {
            isContractSeller = true
            }
        }
        
        $.ajax({
            url: '/api/MoveSheet/GetItemsInfo_Post',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ operKey: g_operKey, items_id: items_id, from_branch_id: from_branch_id, to_branch_id: to_branch_id, from_branch_position, to_branch_position, bGetAttrs: !window.attributes, cost_price_type, defaultBranchPositionType, defaultBranchPositionID, isShowNegativeStock, isContractSeller: isContractSeller }),
            success: function (data) {
                if (data.result === 'OK') {
                    if (!window.g_dicDefualtBranchPosition) window.g_dicDefualtBranchPosition = {}
                    if (data.defaultBranchPosition) {
                        if (!window.g_dicDefualtBranchPosition[from_branch_id]) window.g_dicDefualtBranchPosition[from_branch_id] = data.defaultBranchPosition[from_branch_id]
                        if (!window.g_dicDefualtBranchPosition[to_branch_id]) window.g_dicDefualtBranchPosition[to_branch_id] = data.defaultBranchPosition[to_branch_id]
                    }
                    if (!window.g_queriedItems) window.g_queriedItems = {}
                    var gridRows = $('#jqxgrid').jqxGrid('getrows');
                    var nAddRows = rowIndex + rows.length + 10 - gridRows.length
                    if (nAddRows > 0)
                        addEmptyRows(nAddRows)

                    if (data.attrOptions) window.attrOptions = data.attrOptions
                    if (data.attributes) window.attributes = data.attributes
                  
                    rows.forEach(function (row) {
                        var item = null
                        var unit_no = '', unit_factor = '', barcode = '', contract_price = '', wholesale_price = '', quantity = '', sub_amount = '', cost_price = '', cost_amount = '', last_time_price = '';
                        var unitType = 'b'
                        for (var item_id in data.items) {
                            var curItem = data.items[item_id]

                            if ($('#defaultUnit').length == 1) {
                                var useSmallUnit = $('#defaultUnit').jqxInput('val')
                                console.log(useSmallUnit)
                                if (useSmallUnit && useSmallUnit.value) unitType = useSmallUnit.value
                            }
                            if (row.item_id == curItem.item_id) {
                                item = curItem
                                window.g_queriedItems[item.item_id] = item
                            }
                           
                        }
                        var gridRow = gridRows[rowIndex]
                        item.units.forEach((unit) => {
                            if (row.unit_no)
                            {
                                if (row.unit_no == unit.unit_no)
                                {
                                    if (unit.unit_type != 's')
                                    {
                                        unit_no = row.unit_no
                                        unit_factor = row.unit_factor
                                        barcode = unit.barcode
                                        wholesale_price = toMoney(unit.wholesale_price)
                                        contract_price = toMoney(unit.contract_price)
                                        cost_price = toMoney(unit.cost_price * unit_factor)
                                    }
                                    else
                                    {
                                        unit_no = row.unit_no
                                        unit_factor = row.unit_factor
                                        barcode = unit.barcode
                                        wholesale_price = toMoney(unit.wholesale_price)
                                        contract_price = toMoney(unit.contract_price)
                                        cost_price = toMoney(unit.cost_price)
                                        gridRow.s_barcode = unit.barcode
                                    }
                                }
                            }
                            else
                            {
                                if (unit.unit_type == unitType || item.units.length == 1) {
                                    unit_no = unit.unit_no
                                    unit_factor = unit.unit_factor
                                    barcode = unit.barcode
                                    wholesale_price = toMoney(unit.wholesale_price)
                                    last_time_price = toMoney(unit.recent_price)
                                    contract_price = toMoney(unit.contract_price)
                                    cost_price = toMoney(unit.cost_price * unit_factor)
                                }
                                if (unit.unit_type == 's') {
                                    gridRow.s_barcode = unit.barcode
                                    //unit_no = row.s_unit_no
                                    //unit_factor = row.s_unit_factor
                                }
                            }
                        })
                       if (bUsePriceInRows) {
                            wholesale_price = row.real_price
                            unit_no = row.unit_no
                            unit_factor = row.unit_factor
                            quantity = row.quantity
                            sub_amount = row.sub_amount
                       }

                       
                        gridRow.mum_attributes = item.mum_attributes
                        if (gridRow.mum_attributes) {
                            gridRow.mum_attributes = JSON.parse(gridRow.mum_attributes)
                            var attrs = gridRow.mum_attributes
                            for (var i = attrs.length - 1; i >= 0; i--) {
                                var attr = attrs[i]
                                if (!attr.distinctStock) {
                                    attrs.splice(i, 1)
                                }
                            }
                            if (gridRow.mum_attributes.length == 0) gridRow.mum_attributes = null
                        }
                       
                        gridRow.item_id = item.item_id
                        gridRow.item_name = item.item_name
                        gridRow.item_spec = item.item_spec
                        gridRow.item_no = item.item_no
                        gridRow.valid_days = item.valid_days
                        gridRow.produce_date = row.produce_date
                        gridRow.batch_no = row.batch_no
                        let fromBranchPosition = window.g_dicDefualtBranchPosition && window.g_dicDefualtBranchPosition[from_branch_id] ? window.g_dicDefualtBranchPosition[from_branch_id].branch_position : "0"
                        let fromBranchPositionName = window.g_dicDefualtBranchPosition && window.g_dicDefualtBranchPosition[from_branch_id] ? window.g_dicDefualtBranchPosition[from_branch_id].branch_position_name : ""
                        let toBranchPosition = window.g_dicDefualtBranchPosition && window.g_dicDefualtBranchPosition[to_branch_id] ? window.g_dicDefualtBranchPosition[to_branch_id].branch_position : "0"
                        let toBranchPositionName = window.g_dicDefualtBranchPosition && window.g_dicDefualtBranchPosition[to_branch_id] ? window.g_dicDefualtBranchPosition[to_branch_id].branch_position_name : ""
                        gridRow.from_branch_position = row.from_branch_position ? row.from_branch_position : fromBranchPosition
                        gridRow.to_branch_position = row.to_branch_position ? row.to_branch_position : toBranchPosition
                        gridRow.from_branch_position_name = row.from_branch_position_name ? row.from_branch_position_name : fromBranchPositionName
                        gridRow.to_branch_position_name = row.to_branch_position_name ? row.to_branch_position_name : toBranchPositionName

                        gridRow.from_branch_qty_unit = item.from_stock_qty_unit
                        gridRow.to_branch_qty_unit = item.to_stock_qty_unit

                        gridRow.from_branch_available_qty = item.from_usable_stock_qty_unit
                        gridRow.to_branch_available_qty = item.to_usable_stock_qty_unit

                        gridRow.from_branch_qty = item.from_stock_qty
                        gridRow.to_branch_qty = item.to_stock_qty
                        gridRow.batch_level = item.batch_level
                        gridRow.quantity = row.quantity
                        gridRow.unit_no = unit_no
                        gridRow.unit_factor = unit_factor
                        gridRow.unit_relation1 = getRowUnitRelation(row)
                        gridRow.wholesale_price = toMoney(wholesale_price)
                        gridRow.last_time_price = toMoney(last_time_price)
                        gridRow.contract_price = toMoney(contract_price)
                       
                        gridRow.cost_price = toMoney(cost_price)
                        if (quantity) {
                            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'quantity', quantity)
                        }
                        if (gridRow.from_branch_position_name) {
                            isShowFromBranchPosition = true
                        }
                        if (gridRow.to_branch_position_name) {
                            isShowToBranchPosition = false
                        }

                        updateRowSubAmount(rowIndex)                        
                        updateTotalAmount()
                        row.barcode = barcode
                        if (
                            !bDontEditBatch
                            &&
                            (gridRow.from_branch_position != "0" || gridRow.to_branch_position != "0" || gridRow.batch_level)
                        ) {
                            // let fromKey = item.item_id + from_branch_id + gridRow.from_branch_position
                            // let toKey = item.item_id + to_branch_id + gridRow.to_branch_position
                            let fromKey = `${item.item_id}_${from_branch_id ? from_branch_id : "-1"}_${gridRow.from_branch_position}`
                            let toKey = `${item.item_id}_${to_branch_id ? to_branch_id: "-1"}_${gridRow.to_branch_position}`
                            if (!window.itemsBatchStockTotal) window.itemsBatchStockTotal = {}
                            if (!window.itemsBatchStockForShow) window.itemsBatchStockForShow = {}
                            window.itemsBatchStockTotal[fromKey] = data.batchStockTotal&&data.batchStockTotal[fromKey] ? data.batchStockTotal[fromKey] : []
                            window.itemsBatchStockTotal[toKey] = data.batchStockTotal&&data.batchStockTotal[toKey] ? data.batchStockTotal[toKey] : []
                            window.itemsBatchStockForShow[fromKey] = data.batchStockForShow&&data.batchStockForShow[fromKey] ? data.batchStockForShow[fromKey] : []
                            window.itemsBatchStockForShow[toKey] = data.batchStockForShow&&data.batchStockForShow[toKey] ? data.batchStockForShow[toKey] : []
                            let batchStockForShow = window.itemsBatchStockForShow[fromKey] ? JSON.parse(JSON.stringify(window.itemsBatchStockForShow[fromKey])) : []
                            batchStockForShow = batchStockForShow.filter(e => e.batch_id !== "0")
                            batchStockForShow.sort((pre, next) => {
                                return Date.parse(pre.produce_date) - Date.parse(next.produce_date)
                            })
                            batchStockForShow.sort((pre, next) => {
                                if (pre.batch_no > next.batch_no) return 1
                            })
                            if (batchStockForShow.length === 0 && window.getSettingValue('showNoProduceDate').toLowerCase() === 'true') {
                                gridRow.produce_date = "无产期"
                            } else {
                                gridRow.produce_date = batchStockForShow.length ? batchStockForShow[0].produce_date : ''
                            }
                            gridRow.batch_no = batchStockForShow.length ? batchStockForShow[0].batch_no : ''
                            gridRow.batch_id = batchStockForShow.length ? batchStockForShow[0].batch_id : '0'
                            let batchStockToTalFrom = window.itemsBatchStockTotal[fromKey] ? JSON.parse(JSON.stringify(window.itemsBatchStockTotal[fromKey])) : []
                            let batchStockToTalTo = window.itemsBatchStockTotal[toKey] ? JSON.parse(JSON.stringify(window.itemsBatchStockTotal[toKey])) : []
                            let fromflag = batchStockToTalFrom.some(e => {
                                if (e.batch_no == gridRow.batch_no && e.produce_date == gridRow.produce_date) {
                                    gridRow.from_branch_qty_unit = e.stock_qty_unit
                                    gridRow.from_branch_qty = e.stock_qty
                                    return true
                                }
                            })
                            let toflag = batchStockToTalTo.some(e => {
                                if (e.batch_no == gridRow.batch_no && e.produce_date == gridRow.produce_date) {
                                    gridRow.to_branch_qty_unit = e.stock_qty_unit
                                    gridRow.to_branch_qty = e.stock_qty
                                    return true
                                }
                            })
                            if (!fromflag) {
                                gridRow.from_branch_qty_unit = ""
                                gridRow.from_branch_qty = ""
                            }
                            if (!toflag) {
                                gridRow.to_branch_qty_unit = ""
                                gridRow.to_branch_qty = ""
                            }
                        }
                        rowIndex++                        
                    })
                    if (isShowFromBranchPosition) $("#jqxgrid").jqxGrid('showcolumn', 'from_branch_position_name')
                    if (isShowToBranchPosition) $("#jqxgrid").jqxGrid('showcolumn', 'to_branch_position_name');
                    $('#jqxgrid').jqxGrid('updategrid')
                    window.g_queryStatus = 'done'
                }
            }
        })
    }

    window.addEventListener('message', function (rs) {
        if (rs.data.msgHead === "ItemsView") {
            if (rs.data.action === "selectMulti") {
                
                var cell = $('#jqxgrid').jqxGrid('getselectedcell');
                var rowIndex = cell.rowindex;
                var editable = $("#jqxgrid").jqxGrid('endcelledit', rowIndex, "item_id", false);
                
                $('#popItem').jqxWindow('close');
                var from_branch_id = $('#from_branch_id').jqxInput('val').value
                var to_branch_id = $('#to_branch_id').jqxInput('val').value
                var from_branch_position = "0"
                var to_branch_position = "0"
                AddItemRows(rowIndex, from_branch_id, to_branch_id, from_branch_position, to_branch_position, rs.data.checkedRows)
            }
            else if (rs.data.action === "update") {
                $('#gridItems').jqxGrid('setcellvalue', RowIndex, "n", rs.data.record.sup_name);
            }
            $('#popItem').jqxWindow('close');
        } else if (rs.data.msgHead === "BrandsView") {
            if (rs.data.action === "selectMulti") {
                var rows_select = rs.data.checkedRows
                var isLoadNoStock = rs.data.isLoadNoStock;
                GetItemsByBrands(rows_select, isLoadNoStock)

            }
        }

    });
    function GetItemsByBrands(rows_select, isLoadNoStock) {
        var brands_id = ''
        rows_select.forEach(brand => {
            if (brands_id != '') brands_id += ','
            brands_id += brand.brand_id
        })
        var branch_id = $('#from_branch_id').jqxInput('val').value;
        var fromBanchID = branch_id
        var toBranchID = $('#to_branch_id').jqxInput('val').value;
        var formData = new FormData();
        formData.append('brands_id', brands_id)//品牌
        formData.append('branch_id', branch_id)//仓库

        formData.append('isLoadNoStock', isLoadNoStock)
        $.ajax({
            url: `/api/InventorySheet/GetItemsByBrands?operKey=${g_operKey}`,
            type: 'post',
            contentType: false,
            processData: false,
            data: formData,
            success: function (data) {
                if (data.result === 'OK') {
                    if (data.data.length === 0) {
                        bw.toast("暂无数据")
                        return
                    }
                    $('#jqxgrid').jqxGrid('clear');
                    $('#popQuickMove').jqxWindow('close');
                    AddItemRows(0, fromBanchID, toBranchID,"0","0", data.data)
                } else {
                    bw.toast(data.msg)
                }

            }
        });
    }
    window.GetItemsByClasses = function (rows_select, isLoadNoStock) {
        var classes_id = ''
        rows_select.forEach(classInfo => {
            if (classes_id != '') classes_id += ','
            classes_id += classInfo.value

        })
        var branch_id = $('#from_branch_id').jqxInput('val').value;
        var fromBanchID = branch_id
        var toBranchID = $('#to_branch_id').jqxInput('val').value;
        var formData = new FormData();
        formData.append('branch_id', branch_id)
        formData.append('classes_id', classes_id)
        formData.append('isLoadNoStock', isLoadNoStock)
        $.ajax({
            url: `/api/InventorySheet/GetItemsByClasses?operKey=${g_operKey}`,
            type: 'post',
            contentType: false,
            processData: false,
            data: formData,
            success: function (data) {
                if (data.result === 'OK') {
                    if (data.data.length === 0) {
                        bw.toast("暂无数据")
                        return
                    }
                    $('#jqxgrid').jqxGrid('clear')
                    $('#popQuickMove').jqxWindow('close');
                    AddItemRows(0, fromBanchID, toBranchID,"0","0", data.data)
                } else {
                    bw.toast(data.msg)
                }

            }
        })
    }
    function handleCalendarToast() {
        var startTime = $('#dateTimeInputStart').val();
        var endTime = $('#dateTimeInputEnd').val();
        //var tDate = $("#todaySheet").val();
        //var yesDate = $("#yesterdaySheet").val();
        //var lstDate = $("#afterLastMove").val();
        var selfSet = $("#selfSet").val();
        var date = $(".date-type-group").jqxRadioButtonGroup("getValue")

        if ((!startTime || !endTime) && !date) { 
            bw.toast("请选择日期")
            return false
        }
        return true
    }
    function format(date,fmt) {
        var o = {
            "M+": date.getMonth() + 1, //月份
            "d+": date.getDate(), //日
            "h+": date.getHours(), //小时
            "m+": date.getMinutes(), //分
            "s+": date.getSeconds(), //秒
            "q+": Math.floor((date.getMonth() + 3) / 3), //季度
            "S": date.getMilliseconds() //毫秒
        };
        if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
        for (var k in o)
            if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        return fmt;
    }
    function fillBySale(isFromReturn, forDisplayItems) {
        //let tDate = $("#todaySheet").val();
        //let yesDate = $("#yesterdaySheet").val();
        //let lstDate = $("#afterLastMove").val();
        let date = $(".date-type-group").jqxRadioButtonGroup("getValue")
        console.log(date[0])
        let timeRange = ""
        let day = new Date()
        let startTime = ""
        let endTime = ""
        if (date[0] == "todaySheet") {
            timeRange = "today"
            startTime = format(day, "yyyy-MM-dd")
            endTime = format(day,"yyyy-MM-dd")
        } else if (date[0] == "yesterdaySheet") {
            timeRange = "other"
            day.setDate(day.getDate() - 1)
            startTime = format(day,"yyyy-MM-dd")
            endTime = format(day,"yyyy-MM-dd")
        } else if (date[0] == "afterLastMove") {
            timeRange = "sinceLastMove";
        } else {
            timeRange = "other"
            startTime = $('#dateTimeInputStart').val();
            endTime = $('#dateTimeInputEnd').val();
        }
        let fromBanchID = $('#from_branch_id').jqxInput('val').value
        let toBranchID = $('#to_branch_id').jqxInput('val').value
        let classes = $("#classOption").jqxDropDownTree("getSelectedItem");
        let classes_id = ""
        if (classes) classes_id = classes.value
        let brands = $("#brandOption").jqxDropDownList("getCheckedItems");
        let brands_id = ""
        if (brands&&brands.length !== 0) {
            brands.forEach(e => {
                if (brands_id == "") brands_id = e.value
                else brands_id += ',' + e.value
            })
        }
        //let branch_position = "0"
        let from_branch_position = ""
        let to_branch_position = ""
        //if (branchPositions.split(",").length == 1) branch_position = branchPositions.split(",")[0]
        //if (isFromReturn) from_branch_position = branch_position
        //else to_branch_position = branch_position
        var params = {
            operKey: g_operKey,
            timeRange: timeRange,
            fromBanchID: fromBanchID,
            toBranchID: toBranchID,
            isFromReturn: isFromReturn,
            forDisplayItems: forDisplayItems,
            startTime: startTime,
            endTime: endTime,
            classes: classes_id,
            brandsID: brands_id,
            branchPositions: ""
        };
        $.ajax({
            url: '/AppApi/AppSheetMove/GetFillItemsFromSale',
            type: 'GET',
            contentType: 'application/json',
            data: params ,
            success: function (res) {
                console.log(res)
                if (res.result === "OK") {
                    if (res.data.length == 0) {
                        bw.toast("暂无数据");
                        return;
                    }
                    $('#popQuickMove').jqxWindow('close');
                    $('#jqxgrid').jqxGrid('clear')
                    let newSheetRows = []
                    res.data.forEach(row => {
                        var b_qty = 0, m_qty = 0, s_qty = 0;
                        var b_wholesale_price;
                        if (row.b_unit_no && row.b_unit_factor) {
                            b_qty = parseInt(Number(row.s_qty) / Number(row.b_unit_factor))
                            s_qty = Number(row.s_qty) % Number(row.b_unit_factor)
                            if (row.m_unit_no && row.m_unit_factor) {
                                m_qty = parseInt(Number(s_qty) / Number(row.m_unit_factor))
                                s_qty = Number(s_qty) % Number(row.m_unit_factor)
                            }
                            if (row.b_wholesale_price)
                                b_wholesale_price = Number(row.b_wholesale_price);
                        }
                        else {
                            s_qty = Number(row.s_qty);
                        }
                        var unitRow;
                        if (b_qty > 0) {
                            unitRow = {
                                item_id: row.item_id,
                                classId: row.classid,
                                item_name: row.item_name,
                                b_unit_no: row.b_unit_no,
                                b_unit_factor: row.b_unit_factor,
                                b_wholesale_price: row.b_wholesale_price,
                                b_barcode: row.b_barcode,
                                s_unit_no: row.s_unit_no,
                                s_unit_factor: row.s_unit_factor,
                                s_barcode: row.s_barcode,
                                s_wholesale_price: row.s_wholesale_price,
                                m_unit_no: row.m_unit_no,
                                m_unit_factor: row.m_unit_factor,
                                m_wholesale_price: row.m_wholesale_price,
                                m_barcode: row.m_barcode,
                                unit_no: row.b_unit_no,
                                unit_type: "b",
                                wholesale_price: b_wholesale_price || 0,
                                real_price: b_wholesale_price || 0,
                                quantity: b_qty,
                                unit_factor: row.b_unit_factor,
                                remark: "",
                                batch_id: row.batch_id,
                                produce_date: row.produce_date,
                                batch_no: row.batch_no,
                                from_branch_position: row.from_branch_position,
                                from_branch_position_name: row.from_branch_position_name,
                                to_branch_position: row.to_branch_position,
                                to_branch_position_name: row.to_branch_position_name,
                            };
                            newSheetRows.push(unitRow);

                        }
                        if (m_qty > 0) {
                            unitRow = {
                                item_id: row.item_id,
                                classId: row.classid,
                                item_name: row.item_name,
                                b_unit_no: row.b_unit_no,
                                b_unit_factor: row.b_unit_factor,
                                b_wholesale_price: row.b_wholesale_price,
                                b_barcode: row.b_barcode,
                                s_unit_no: row.s_unit_no,
                                s_unit_factor: row.s_unit_factor,
                                s_barcode: row.s_barcode,
                                s_wholesale_price: row.s_wholesale_price,
                                m_unit_no: row.m_unit_no,
                                m_unit_factor: row.m_unit_factor,
                                m_wholesale_price: row.m_wholesale_price,
                                m_barcode: row.m_barcode,
                                unit_no: row.m_unit_no,
                                unit_type: "m",
                                wholesale_price: row.m_wholesale_price || 0,
                                real_price: row.m_wholesale_price || 0,
                                quantity: m_qty,
                                unit_factor: row.m_unit_factor,
                                remark: "",
                                batch_id: row.batch_id,
                                produce_date: row.produce_date,
                                batch_no: row.batch_no,
                                from_branch_position: row.from_branch_position,
                                from_branch_position_name: row.from_branch_position_name,
                                to_branch_position: row.to_branch_position,
                                to_branch_position_name: row.to_branch_position_name,
                            };
                            newSheetRows.push(unitRow);
                        }
                        if (s_qty > 0) {
                            unitRow = {
                                item_id: row.item_id,
                                classId: row.classid,
                                item_name: row.item_name,
                                b_unit_no: row.b_unit_no,
                                b_unit_factor: row.b_unit_factor,
                                b_wholesale_price: row.b_wholesale_price,
                                b_barcode: row.b_barcode,
                                s_unit_no: row.s_unit_no,
                                s_unit_factor: row.s_unit_factor,
                                s_barcode: row.s_barcode,
                                s_wholesale_price: row.s_wholesale_price,
                                m_unit_no: row.m_unit_no,
                                m_unit_factor: row.m_unit_factor,
                                m_wholesale_price: row.m_wholesale_price,
                                m_barcode: row.m_barcode,
                                unit_no: row.s_unit_no,
                                wholesale_price: row.s_wholesale_price || 0,
                                real_price: row.s_wholesale_price || 0,
                                quantity: s_qty,
                                unit_factor: 1,
                                unit_type: "s",
                                remark: "",
                                batch_id: row.batch_id,
                                produce_date: row.produce_date,
                                batch_no: row.batch_no,
                                from_branch_position: row.from_branch_position,
                                from_branch_position_name: row.from_branch_position_name,
                                to_branch_position: row.to_branch_position,
                                to_branch_position_name: row.to_branch_position_name,
                            };
                            newSheetRows.push(unitRow);
                        }
                        
                    })
                    console.log(newSheetRows)
                    //$('#defaultUnit').val({ label: '小' ,value:'s'})
                    AddItemRows(0, fromBanchID, toBranchID, from_branch_position, to_branch_position, newSheetRows, false)
                } else {
                    bw.toast(res.msg);
                }
            }
        })
    }
    function GetClassTreeData() {
        $.ajax({
            url: `/api/InventorySheet/GetClassTreeData?operKey=${g_operKey}`,
            type: 'get',
            contentType: false,
            processData: false,
            success: function (data) {
                if (data.result === 'OK') {
                    var treeSource =
                    {
                        datatype: 'json',
                        datafields: [
                            { name: 'v' },
                            { name: 'pv' },
                            { name: 'l' },
                            { name: 'z' },
                            { name: 'status' }
                        ],
                        id: 'id',
                        localdata: data.data
                    };
                    var dataAdapter = new $.jqx.dataAdapter(treeSource);
                    dataAdapter.dataBind();
                    var records = dataAdapter.getRecordsHierarchy('v', 'pv', 'items', [{ name: 'l', map: 'label' }, { name: 'v', map: 'value' }]);
                    $("#classOption").jqxDropDownTree({ dropDownWidth: '150px', dropDownHeight: '150px', source: records, checkboxes: false, mumSelectable: true, });
                    var source =
                    {
                        datatype: "json",
                        datafields: [
                            { name: 'l' },
                            { name: 'v' }
                        ],
                        id: 'v',
                        url: '../api/SalesSummaryByItem/GetDataItemOptions?operKey= ' + g_operKey + '&dataItemName=brand_id',
                    };
                    var dataAdapter = new $.jqx.dataAdapter(source);
                    $("#brandOption").jqxDropDownList({
                        height: 25, width: 150,
                        dropDownHeight: 160,
                        checkboxes:true,
                        displayMember: 'l',
                        valueMember: 'v',
                        placeHolder:'',
                        source: dataAdapter,
                    });
                }

            }
        });
    }
    function handleTimeSelectShow() {
        if (window.moveWay === "stock") {
            $(".branch-position-type").css({ "display": "" })
            $(".date-type").css({ "display": "none" })
            getBranchPosition()
        } else {
            $(".branch-position-type").css({ "display": "none" })
            $(".date-type").css({ "display": "" })
        }
    }
    $("#btnForSale").on("click", function () {
        if (window.moveWay == "stock") {
            btnFillByStock_click()
        } else if (window.moveWay == "sale") {
            if (handleCalendarToast()) {
                fillBySale(false);
            }
        } else if (window.moveWay == "return") {
            if (handleCalendarToast()) {
                fillBySale(true);
            }
        } else {
            if (handleCalendarToast()) {
                fillBySale(false, true);
            }
        }
    })
    $("#dropdownFromBranchposition").on('close', function () {
        let checkedItems = $("#dropdownFromBranchposition").jqxDropDownList('getCheckedItems');
        setBranchPositionlabel("from", checkedItems)
    });
    $("#dropdownToBranchposition").on('change', function (event) {
        let checkedItems = $("#dropdownToBranchposition").val(); 
        setBranchPositionlabel("to", checkedItems)
    });
    function setBranchPositionlabel(flag, checkedItems) {
        console.log(checkedItems)
        let branchPositions = ""
        if (flag == "from" && checkedItems) {
            checkedItems.forEach(e => {
                if (!branchPositions) {
                    branchPositions = e.value
                } else {
                    branchPositions += "," + e.value
                }
            })
        }
        if (flag == "from") {
            window.fromBranchPositions = branchPositions
        } else {
            window.toBranchPositions = checkedItems ? checkedItems.value:""
        }
    }
    function btnFillByStock_click() {
        $("#classBrand").css('display', "none")
        $("#classType").css('display', "none")
        $(".branchPositionSelect").css("display", "none")
        let fromBanchID = $('#from_branch_id').jqxInput('val').value
        let toBranchID = $('#to_branch_id').jqxInput('val').value
        var params = {
            operKey: g_operKey,
            fromBanchID: fromBanchID,
            toBranchID: toBranchID,
            fromBranchPosition: window.fromBranchPositions,
            toBranchPosition: window.toBranchPositions,
        }
        $.ajax({
            url: '/AppApi/AppSheetMove/GetFillItemsFromStock',
            type: 'GET',
            contentType: 'application/json',
            data: params,
            success: function (res) {
                if (res.result === "OK") {
                    if (res.data.length == 0) {
                        bw.toast("暂无数据");
                        return;
                    }
                    $('#popQuickMove').jqxWindow('close');
                    $('#jqxgrid').jqxGrid('clear')
                    res.data.forEach(e => {
                        e.quantity = e.s_qty
                        e.unit_no = e.s_unit_no
                        e.real_price = e.s_wholesale_price
                        e.unit_factor = e.s_unit_factor
                        e.sub_amount = e.s_unit_factor * e.s_wholesale_price * e.s_qty
                        if (!e.produce_date && window.getSettingValue('showNoProduceDate').toLowerCase() === 'true') {
                            e.produce_date = "无产期"
                        }
                    })
                    //$('#defaultUnit').val({ label: '小', value: 's' })
                    AddItemRows(0, fromBanchID, toBranchID,"0","0", res.data, false, true)
                } else {
                    bw.toast(res.msg);
                }
            }
        })
    }
    function checkedEvent(dateType) {
        if (dateType === "selfSet") {
            $(".date-input-group").css({ "display": "" })
        } else {
            $(".date-input-group").css({ "display": "none" })
        }
    }
    window.OnQuickMove = function () {
        var from_branch_id = $('#from_branch_id').jqxInput('val').value
        var to_branch_id = $('#to_branch_id').jqxInput('val').value
        if (!from_branch_id || !to_branch_id) {
            bw.toast("请选择仓库")
            return
        } else if (from_branch_id === to_branch_id) {
            bw.toast("出仓与入仓相同，请重新选择")
            return
        }
        $(".way-type-group").jqxRadioButtonGroup({ value: 'sale' });
        $(".date-type-group").jqxRadioButtonGroup({ value: 'todaySheet' });
        window.moveWay="sale"
        handleTimeSelectShow()
        $('#popQuickMove').jqxWindow('open');
    }
    var theme = "";

    var datafields = [];
    var data = new Array; var rowscount = 10;
    
    var source =
    {
        localdata: data,
        unboundmode: true,
        totalrecords: 10,
        datafields: datafields,
        updaterow: function (rowid, rowdata) {
        }
    };

    var dataAdapter = new $.jqx.dataAdapter(source);

    var fixColCss = 'jqx-widget-header';
    if (theme !== '') fixColCss += ' jqx-widget-header-' + theme;

    var sheetShowPriceList = true
    if (window.g_companySetting && window.g_companySetting.sheetShowPriceList && window.g_companySetting.sheetShowPriceList.toLowerCase() == 'false')
        sheetShowPriceList = false;

    function initeditor_wholesale_price(row, cellvalue, editor, celltext, pressedkey) {
        // set the editor's current value. The callback is called each time the editor is displayed.
        var inputElement = editor.find('div')[0];
        $(inputElement).jqxInput('clearOptions');
        var inputField = editor.find('input');
        if (pressedkey) {
            inputField.val(pressedkey);
            inputField.jqxInput('selectLast');
        }
        else {
            inputField.val({ value: cellvalue, label: celltext });
            inputField[0].value = celltext || '';
            inputField.jqxInput('selectAll');
        }
        $(inputElement).jqxInput('suggest', true);
    }

    function createeditor_wholesale_price(row, cellvalue, editor, cellText, width, height) {
        var element = $('<div id="txtRealPrice"></div >');
        editor.append(element);
        var inputElement = editor.find('div')[0];

        var dataFields = new Array(
            { datafield: "price_type", text: "类型", width: 100 },
            { datafield: "show_price", text: "价格", width: 140 },
            { datafield: "price", text: "单价", width: 60 }
        )

        var gridSlt = '#jqxgrid'
        if (window.curGridSlt) gridSlt = window.curGridSlt
        $(inputElement).jqxInput({
            placeHolder: "", height: height, width: width,
            borderShape: "none",
            buttonUsage: 'list',
            showHeader: true,
            searchable: false,
            dropDownHeight: 180,
            displayMember: "price",
            valueMember: "price",
            dataFields: dataFields,
            searchFields: [],
            maxRecords: 4,
            url: '',
            source: function (query, response) {
                var item = query;
                var cell = $(gridSlt).jqxGrid('getselectedcell')
                var rowindex = cell.rowindex
                let sheetRows = $(gridSlt).jqxGrid('getrows')
                var sheetRow = sheetRows[rowindex]
                var key = getQueriedItemsKeyFromRow(sheetRow)
                if (window.g_queriedItems && key) {
                    var item = window.g_queriedItems[key];
                    var prices = []
                    if (item) {
                        var canSeeInPrice = false
                        if (window.g_operRights && window.g_operRights.delicacy && window.g_operRights.delicacy.seeInPrice) {
                            if (window.g_operRights.delicacy.seeInPrice.value) {
                                canSeeInPrice = true
                            }
                        }

                        var wholesale_price = '', buy_price = '', retail_price = '', recent_price = ''
                        var unit_wholesale_price = '', unit_buy_price = '', unit_retail_price = '', unit_recent_price = ''

                        var recent_time = ''
                        var planPrices = []
                        var curUnitPrice = ''
                        item.units.forEach(unit => {
                            if (unit.recent_price) {
                                recent_price += `${unit.recent_price}/${unit.unit_no}`
                                recent_time = unit.recent_time
                                if (unit.unit_no == sheetRow.unit_no)
                                    unit_recent_price = unit.recent_price
                            }
                            if (unit.wholesale_price) {
                                wholesale_price += `${unit.wholesale_price}/${unit.unit_no}`
                                if (unit.unit_no == sheetRow.unit_no)
                                    unit_wholesale_price = unit.wholesale_price
                            }
                            if (canSeeInPrice && unit.buy_price) {
                                buy_price += `${unit.buy_price}/${unit.unit_no}`
                                if (unit.unit_no == sheetRow.unit_no)
                                    unit_buy_price = unit.buy_price
                            }

                            if (unit.retail_price) {
                                retail_price += `${unit.retail_price}/${unit.unit_no}`
                                if (unit.unit_no == sheetRow.unit_no)
                                    unit_retail_price = unit.retail_price
                            }
                            if (unit.planPrices) {
                                for (var plan_name in unit.planPrices) {
                                    var unit_price = unit.planPrices[plan_name]
                                    var unitPlan = planPrices[plan_name]
                                    if (!unitPlan) unitPlan = { price_type: plan_name, show_price: '', price: '' }

                                    unitPlan.show_price += `${unit_price}/${unit.unit_no}`
                                    if (sheetRow.unit_no == unit.unit_no) unitPlan.price = unit_price
                                    planPrices[plan_name] = unitPlan
                                }
                            }

                        })
                        var price
                        if (recent_price) {
                            price = { price_type: recent_time, price: unit_recent_price, show_price: recent_price }
                            prices.push(price)
                        }
                        if (wholesale_price) {
                            price = { price_type: '批发价', price: unit_wholesale_price, show_price: wholesale_price }
                            prices.push(price)
                        }
                        if (retail_price) {
                            price = { price_type: '零售价', price: unit_retail_price, show_price: retail_price }
                            prices.push(price)
                        }
                        for (var plan_name in planPrices) {
                            var plan_price = planPrices[plan_name]
                            price = { price_type: plan_name, show_price: plan_price.show_price, price: plan_price.price, }
                            prices.push(price)
                        }
                        
                        response(prices, null, true)
                    }
                }
            },
            renderer: function (itemValue, inputValue, way) {
                if (way == 'keyboard') return inputValue
                else return itemValue
            }
        })
    }

    window.GridData = {
        source: dataAdapter,
        showaggregates: true,
        showstatusbar: true,
        columnsheight: 36,
        rowsheight: 36,
        statusbarheight: 30,
        pageable: false,
        // autoheight: true,
        sortable: false,
        editable: true,
        columnsresize: true,
        ready: function () {
            $("#jqxgrid").jqxGrid('focus');
        },
        renderstatusbar: function (statusbar) {
        },
        editmode: 'click',// 'selectedcell',
        selectionmode: 'multiplecellsadvanced',//'singlecell',// 
        hoverrow: true,
        theme: theme,
        cellhover: cellhover,
        handlekeyboardnavigation:
            function (event) {
                var key = event.charCode ? event.charCode : event.keyCode ? event.keyCode : 0;
                addEmptyRowsAtTail(4, 4);
                if (key === 13) {


                    cell = $('#jqxgrid').jqxGrid('getselectedcell');

                    $("#jqxgrid").jqxGrid('endcelledit', cell.row, cell.column, false);

                    if (cell.column == "item_id") {
                        var item_id = $('#jqxgrid').jqxGrid('getcellvalue', cell.row, 'item_id');
                        if (!item_id) {
                            $('#jqxgrid').jqxGrid('begincelledit', cell.row, cell.column);
                            return true
                        }
                    }
                    var nextCell = getNextCellByEnterKey(cell.row, cell.column)

                    $('#jqxgrid').jqxGrid('clearselection')

                    $('#jqxgrid').jqxGrid('selectcell', nextCell.row, nextCell.datafield)
                    if (nextCell.datafield == 'item_id')
                        $('#jqxgrid').jqxGrid('ensurerowvisible', cell.row + 3)

                    if (nextCell.datafield == 'item_id' || nextCell.datafield == 'from_branch_position' || nextCell.datafield == 'to_branch_position' || nextCell.datafield == 'unit_no' || nextCell.datafield == 'quantity_unit_conv' || nextCell.datafield == 'quantity' || nextCell.datafield == 'batch_id' || nextCell.datafield == 'batch_id_f') {


                        if (window.g_queryStatus == 'querying') {
                            var tmInterval = setInterval(function () {
                                if (window.g_queryStatus == 'done') {
                                    clearInterval(tmInterval)
                                    $('#jqxgrid').jqxGrid('begincelledit', nextCell.row, nextCell.datafield);
                                }
                            }, 30)
                        }
                        else {
                            // setTimeout(function () {
                            $('#jqxgrid').jqxGrid('begincelledit', nextCell.row, nextCell.datafield);
                            // }, 1);
                        }

                    }
                    return true

                    if (cell.column === "item_id") {
                        var item_id = $('#jqxgrid').jqxGrid('getcellvalue', cell.row, 'item_id');
                        if (item_id) {
                            $('#jqxgrid').jqxGrid('clearselection');
                            $('#jqxgrid').jqxGrid('selectcell', cell.row, 'unit_no');
                            setTimeout(function () {
                                $('#jqxgrid').jqxGrid('begincelledit', cell.row, 'unit_no');
                            }, 300);
                        }
                        else {
                            /*setTimeout(function () {
                                var a = document.activeElement;
                                if (a && a.id == 'contentjqxgrid') { //跳转到button上面
                                    var button = $('#divButtons').find('button')[0];
                                    button.focus();
                                }
                            }, 300);*/
                        }
                    }
                    else if (cell.column === "unit_no") {
                        $('#jqxgrid').jqxGrid('clearselection');
                        $('#jqxgrid').jqxGrid('selectcell', cell.row, 'quantity');
                    }
                    else if (cell.column === "quantity") {
                        $('#jqxgrid').jqxGrid('clearselection');
                        $('#jqxgrid').jqxGrid('selectcell', cell.row + 1, 'item_id');
                    }
                    else if (cell.column === "from_branch_position") {
                        $('#jqxgrid').jqxGrid('clearselection');
                        if (!nextCol) nextCol = 'quantity'
                        $('#jqxgrid').jqxGrid('selectcell', cell.row, nextCol);
                    }
                    else if (cell.column === "to_branch_position") {
                        $('#jqxgrid').jqxGrid('clearselection');
                        if (!nextCol) nextCol = 'quantity'
                        $('#jqxgrid').jqxGrid('selectcell', cell.row, nextCol);
                    }
                    else if (cell.column === "batch_id") {
                        $('#jqxgrid').jqxGrid('clearselection');
                        if (!nextCol) nextCol = 'quantity'
                        $('#jqxgrid').jqxGrid('selectcell', cell.row, nextCol);
                    }
                    else if (cell.column === "batch_id_f") {
                        $('#jqxgrid').jqxGrid('clearselection');
                        if (!nextCol) nextCol = 'quantity'
                        $('#jqxgrid').jqxGrid('selectcell', cell.row, nextCol);
                    }
                    return true;
                }
                else if (key === 27) {
                    bw.toast('Pressed Esc Key.');
                    return true;
                }
                else if (key >= 37 && key <= 40) {
                    setTimeout(() => {
                        cell = $('#jqxgrid').jqxGrid('getselectedcell');
                        if (!cell) return
                        if (cell.column == 'unit_no' || cell.column == "item_id" || cell.column == "quantity" || cell.column == "quantity_unit_conv") {
                            $('#jqxgrid').jqxGrid('begincelledit', cell.row, cell.column);
                        }
                    }, 100)

                }
            },
        columns: [
            {
                text: '', sortable: false, filterable: false, editable: false, pinned: true,
                groupable: false, draggable: false, resizable: false,
                datafield: '', columntype: 'number', width: 45,
                cellclassname: fixColCss,
                cellsrenderer: pinCellsRenderer,
                renderer: leftTopCellRenderer
            },
            {
                text: '出仓库位',
                sortable: true,
                hidden: true,
                datafield: 'from_branch_position',
                displayfield: 'from_branch_position_name',
                width: 150, align: 'center',
                columntype: 'template',
                createeditor: function (row, cellvalue, editor, cellText, width, height) {
                    createeditor_branch_position_name(row, cellvalue, editor, cellText, width, height, 'from_branch_position_name')
                },
                initeditor: initeditor_branch_position_name,
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val();
                    return v;
                },
                cellbeginedit: function (row, datafield, columntype, value) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var branch_id = $('#from_branch_id').jqxInput('val').value
                    if (!branch_id || !item_id) return false;
                },
            },
            {
                text: '入仓库位',
                sortable: true,
                hidden: true,
                datafield: 'to_branch_position',
                displayfield: 'to_branch_position_name',
                width: 150, align: 'center',
                columntype: 'template',
                createeditor: function (row, cellvalue, editor, cellText,width,height) {
                    createeditor_branch_position_name(row, cellvalue, editor, cellText, width, height, 'to_branch_position_name')
                },
                initeditor: initeditor_branch_position_name,
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val();
                    return v;
                },
                cellbeginedit: function (row, datafield, columntype, value) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var branch_id = $('#to_branch_id').jqxInput('val').value
                    if (!branch_id || !item_id) return false;
                },
            },
            {
                text: '商品名称', datafield: 'item_id', displayfield: 'item_name', width: '150', columntype: 'template',
                createeditor: createeditor_item_name,
                align: 'center',
                initeditor: initeditor_item_name,
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val();
                    return v;
                }
            },
            {
                text: '商品编号', datafield: 'item_no', width: '70', align: 'center', cellsalign: 'center', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '规格', datafield: 'item_spec', width: '60', align: 'center', cellsalign: 'right',
                cellbeginedit: function (row, datafield, columntype, value) {
                    if (datafield === "item_spec") return false;
                }
            },
            {
                text: '商品条码', datafield: 'barcode', width: '150', align: 'center', cellsalign: 'right',
                cellbeginedit: function (row, datafield, columntype, value) {
                    if (datafield === "barcode") return false;
                }
            },
            {
                text: '条码(小)', datafield: 's_barcode', width: '150', align: 'center', cellsalign: 'right', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '单位', datafield: 'unit_no', width: '80', align: 'center', cellsalign: 'center', columntype: 'template',
                createeditor: createeditor_unit_no,
                initeditor: initeditor_unit_no,
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val(); return v;
                }
            },
            {
                text: '包装率', datafield: 'unit_factor', width: '80', align: 'center', cellsalign: 'center',
                cellbeginedit: function (row, datafield, columntype, value) {
                    if (datafield === "unit_factor") return false;
                }
            },
            {
                text: '单位关系', datafield: 'unit_relation1', width: '100', align: 'center', cellsalign: 'center', alwaysShow: false, hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '生产日期', datafield: 'batch_id', displayfield: 'produce_date', width: '150', align: 'center', cellsalign: 'center', columntype: 'template', hidden: true,
                createeditor: createeditor_produce_date,
                initeditor: initeditor_produce_date,
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val()
                    return v;
                },
                cellsrenderer: function (row, columnfield, value) {
                    var rowsData = $("#jqxgrid").jqxGrid('getrows')
                    var rowData = rowsData[row]
                    if (rowData.batch_level == "" || rowData.batch_level == "0") value = "无产期"
                    return '<div style="line-height:37px;text-align:center;">' + value + '</div>';
                },
                cellbeginedit: function (row, datafield, columntype, value) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var rowsData = $("#jqxgrid").jqxGrid('getrows')
                    rowData = rowsData[row]
                    if ((datafield === "batch_id" && !item_id) || rowData.batch_level == "" || rowData.batch_level == "0") return false;
                },
            },
            {
                text: '批次', datafield: 'batch_id_f', displayfield: 'batch_no', width: '60', align: 'center', cellsalign: 'center', columntype: 'template', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var rowsData = $("#jqxgrid").jqxGrid('getrows')
                    rowData = rowsData[row]
                    console.log(rowData, row)
                    if ((datafield === "batch_id_f" && !item_id) || rowData.batch_level !== "2") return false;
                },
                createeditor: createeditor_batch_no,
                initeditor: initeditor_batch_no,
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val(); return v;
                }

            },
            {
                text: '数量', datafield: 'quantity', width: '60',
                cellsrenderer: cellsrenderer_quantity,
                align: 'center', cellsalign: 'right',
                aggregates: aggregates_quantity,
                aggregatesrenderer: aggregatesrenderer_quantity,
                geteditorvalue: geteditorvalue_quantity,
                cellbeginedit: function (row, datafield, columntype, value) {
                    var gridRow = $('#jqxgrid').jqxGrid('getrows')[row]
                    var item_id = gridRow.item_id
                    if (!item_id) return false
                    if (gridRow.mum_attributes) {
                        if (gridRow.mum_attributes.find(attr => attr.distinctStock))
                            return false
                    }

                }
            },
            {
                text: '辅助数量',
                datafield: 'quantity_unit_conv',
                width: '100',
                hidden: true,
                cellsrenderer: cellsrenderer_multi_qty,
                columntype: 'template',
                align: 'center',
                cellsalign: 'right',
                //aggregates: aggregates_quantity,
                //aggregatesrenderer: aggregatesrenderer_quantity,
                createeditor: createeditor_multi_qty,
                initeditor: initeditor_multi_qty,
                geteditorvalue: geteditorvalue_multi_qty,
                cellbeginedit: function (row, datafield, columntype, value) {
                    var gridRow = $('#jqxgrid').jqxGrid('getrows')[row]
                    var item_id = gridRow.item_id
                    if (!item_id) return false
                    if (gridRow.mum_attributes) {
                        if (gridRow.mum_attributes.find(attr => attr.distinctStock))
                            return false
                        if (gridRow.mum_attributes.find(attr => !attr.distinctStock)) {
                            if (gridRow.attr_qty && gridRow.attr_qty.length > 0) {
                                return false
                            }
                        }
                    }

                }
            },
            { text: '承包价', datafield: 'contract_price', width: '80', align: 'center', cellsalign: 'right' ,hidden:true,
            columntype: sheetShowPriceList ? 'template' : undefined,
                createeditor: sheetShowPriceList ? createeditor_wholesale_price : undefined,
                initeditor: sheetShowPriceList ? initeditor_wholesale_price : undefined,
                geteditorvalue: sheetShowPriceList ? function (row, cellvalue, editor) {
                    var v = editor.find('input').val();
                    if (v.label && !v.value) {
                        v.value = v.label.toString()
                    }

                    return v;
                } : undefined,
            },
            {
                text: '承包金额', datafield: 'contract_sub_amount', width: '100', align: 'center', cellsalign: 'right', hidden: true, aggregates: [{
                    'xj':
                        function (aggregatedValue, currentValue) {
                            return toMoney(Number(aggregatedValue || 0) + Number(currentValue || 0))
                        }
                }],
                aggregatesrenderer: function (aggregates, column, element, summaryData) {
                    var renderstring = "<div class='jqx-widget-content jqx-widget-content-" + theme + "' style='float: left; width: 100%; height: 100%; '>";
                    $.each(aggregates, function (key, value) {
                        renderstring += '<div style="position: relative; margin: 6px; text-align: right; overflow: hidden;">' + value + '</div>';
                    });
                    renderstring += "</div>";
                    return renderstring;
                }
            },
            {
                text: '零售价(小)', datafield: 's_retail_price', width: '100', align: 'center', cellsalign: 'right',
                hidden: true,
                hideOnLoad: false,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '批发价', datafield: 'wholesale_price', width: '100', align: 'center', cellsalign: 'right',
                columntype: sheetShowPriceList ? 'template' : undefined,
                createeditor: sheetShowPriceList ? createeditor_wholesale_price : undefined,
                initeditor: sheetShowPriceList ? initeditor_wholesale_price : undefined,
                geteditorvalue: sheetShowPriceList ? function (row, cellvalue, editor) {
                    var v = editor.find('input').val();
                    if (v.label && !v.value) {
                        v.value = v.label.toString()
                    }

                    return v;
                } : undefined,
            },
            {
                text: '上次批发价', datafield: 'last_time_price', width: '100', align: 'center', cellsalign: 'right',
                hidden: true,
                hideOnLoad: false,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '批发金额', datafield: 'wholesale_sub_amount', width: '100', align: 'center', cellsalign: 'right', aggregates: [{
                    'xj':
                        function (aggregatedValue, currentValue) {
                            return toMoney( Number(aggregatedValue || 0) + Number(currentValue || 0) );
                        }
                }],
                aggregatesrenderer: function (aggregates, column, element, summaryData) {
                    var renderstring = "<div class='jqx-widget-content jqx-widget-content-" + theme + "' style='float: left; width: 100%; height: 100%; '>";
                    $.each(aggregates, function (key, value) {
                        renderstring += '<div style="position: relative; margin: 6px; text-align: right; overflow: hidden;">' + value + '</div>';
                    });
                    renderstring += "</div>";
                    return renderstring;
                }
            },
            {
                text: '成本价',
                datafield: 'cost_price',
                width: '100',
                align: 'center',
                cellsalign: 'right',
                hidden: !seeInPrice,
                hideOnLoad: !seeInPrice,
                hiddenByRight: !seeInPrice

            },
            {
                text: '成本金额', datafield: 'cost_amount', width: '100', align: 'center', cellsalign: 'right', hidden: !seeInPrice, hiddenByRight: !seeInPrice, hideOnLoad: !seeInPrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    if (datafield === "cost_amount") return false;
                },
                aggregates: [{
                    'xj':
                        function (aggregatedValue, currentValue) {
                            return toMoney((Number(aggregatedValue) || 0) + (Number(currentValue) || 0));
                        }
                }],
                aggregatesrenderer: function (aggregates, column, element, summaryData) {
                    var renderstring = "<div class='jqx-widget-content jqx-widget-content-" + theme + "' style='float: left; width: 100%; height: 100%; '>";
                    $.each(aggregates, function (key, value) {
                        renderstring += '<div style="position: relative; margin: 6px; text-align: right; overflow: hidden;">' + value + '</div>';
                    });
                    renderstring += "</div>";
                    return renderstring;
                }
            },         
            { text: '出仓实际库存', datafield: 'from_branch_qty_unit', width: '120', align: 'center', cellsalign: 'right' },
            { text: '入仓实际库存', datafield: 'to_branch_qty_unit', width: '120', align: 'center', cellsalign: 'right' },
            // 出仓可用库存列，默认不显示  
            {
                text: '出仓可用库存',
                datafield: 'from_branch_available_qty',
                width: '120',
                align: 'center',
                cellsalign: 'right',
                hidden: true // 添加此属性来默认隐藏列  
            },
            // 入仓可用库存列，默认不显示  
            {
                text: '入仓可用库存',
                datafield: 'to_branch_available_qty',
                width: '120',
                align: 'center',
                cellsalign: 'right',
                hidden: true // 添加此属性来默认隐藏列  
            },
            { text: '备注', datafield: 'remark', width: '200', align: 'center' }
        ]
    }
    adjustColumnsBySetting()
    $("#jqxgrid").jqxGrid(
        GridData
    );
    //单元格添加自动添加行
    $('#jqxgrid').on('cellclick', function () {
        addEmptyRowsAtTail(4, 4);
    });
    var items_id = ''
    var from_branch_id = $('#from_branch_id').jqxInput('val');
    var to_branch_id = $('#to_branch_id').jqxInput('val');
    if (from_branch_id) from_branch_id = from_branch_id.value;
    if (to_branch_id) to_branch_id = to_branch_id.value;
    sheetRows.forEach(function (row) {
        if (items_id != '') items_id += ','
        items_id += row.item_id
    })
    var rowIndex = 0;

    var cost_price_type=''
    if (g_companySetting && g_companySetting.costPriceType) {
        cost_price_type = g_companySetting.costPriceType
    }
    console.log(window.g_queriedItems)
    //$.ajax({
    //    url: '/api/MoveSheet/GetItemsInfoBySheetRows',
    //    type: 'GET',
    //    contentType: 'application/json',
    //    data: { operKey: g_operKey, from_branch_id: from_branch_id, to_branch_id: to_branch_id, cost_price_type: cost_price_type,sheetRows: },
    //    success: function (data) {
    //        if (data.result === 'OK') {
    //            console.log(window.g_queriedItems)
    //            if (!window.g_queriedItems) window.g_queriedItems = {}
    //            sheetRows.forEach(function (row) {

    //                var item = null
    //                for (var key in data.items) {
    //                    var curItem = data.items[key]
    //                    if (row.item_id == curItem.item_id && row.from_branch_position == curItem.from_branch_position && row.to_branch_position == curItem.to_branch_position && row.batch_id == curItem.batch_id) {
    //                        item = curItem
    //                        if (item.from_stock_qty_unit) row.from_branch_qty_unit = item.from_stock_qty_unit;
    //                        if (item.to_stock_qty_unit) row.to_branch_qty_unit = item.to_stock_qty_unit;
    //                        window.g_queriedItems[key] = item

    //                    }
    //                }
    //                rowIndex++
    //            })
    //        }
    //    }
    //})
    window.loadSheetData = function (sheetRows) {
        initProduceDate(sheetRows)
        loadSheetRows(sheetRows)
    }
    loadSheetData(sheetRows)
    mmRefreshStockQty()

    window.setRowOnItemSelected = function (rowIndex, item1) {
        var from_branch_id = $('#from_branch_id').jqxInput('val').value
        var to_branch_id = $('#to_branch_id').jqxInput('val').value
        var rowFromBranchPosition = $("#jqxgrid").jqxGrid('getcellvalue', rowIndex, "from_branch_position");
        var rowToBranchPosition = $("#jqxgrid").jqxGrid('getcellvalue', rowIndex, "to_branch_position");
        var rows = []
        if (Array.isArray(item1)) {
            item1.forEach(item => {
                const itemInfo = { item_id: item.value, item_name: item.label, from_branch_position: rowFromBranchPosition, to_branch_position: rowToBranchPosition }
                rows.push(itemInfo)
            })
        } else {
            const itemInfo = {}
            itemInfo.item_id = item1.value
            itemInfo.item_name = item1.label
            itemInfo.from_branch_position = rowFromBranchPosition
            itemInfo.to_branch_position = rowToBranchPosition
            rows.push(itemInfo)
        }
        $("#jqxgrid").jqxGrid('endcelledit', rowIndex, "item_id", false);
        window.g_queryStatus = 'querying'
        AddItemRows(rowIndex, from_branch_id, to_branch_id, rowFromBranchPosition, rowToBranchPosition,rows)
         

    }
    window.cellendedit= function (event) {
        var args = event.args;
        var colName = args.datafield;
        var rowIndex = args.rowindex;
        var cellValue = args.value;
        var oldValue = args.oldvalue;

     
        if (oldValue === cellValue) return;

        if (cellValue.value != undefined) cellValue = cellValue.value
        var row = args.row;
        row[colName] = cellValue

            var item_id = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'item_id');
            var from_branch_id = $('#from_branch_id').jqxInput('val')
            var to_branch_id = $('#to_branch_id').jqxInput('val')
            if (from_branch_id) from_branch_id = from_branch_id.value
            if (to_branch_id) to_branch_id = to_branch_id.value
            if (!item_id) {
                return;
            }

            var contract_price = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'contract_price');
            var wholesale_price = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'wholesale_price');
              var cost_price = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'cost_price');
            var cost_amount = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'cost_amount');
            var wholesale_sub_amount = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'wholesale_sub_amount');          //
            var qty = parseFloat($('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'quantity'));                           //数量
            // if(isNaN(wholesale_price) || isNaN(cost_price) || isNaN(cost_amount_avg) || isNaN(wholesale_sub_amount) ||isNaN(qty)) {
            //     bw.toast('请输入正确的数字');
            //     return;
            // }
            function CheckNumberAndAlert(colTitle) {
                if (!myIsNumber(cellValue)) {
                    let msg = '请正确输入' + colTitle
                    bw.toast(msg, 5000)
                    $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, colName, oldValue || '')
                    return false
                }
                return true
            }
        

            if (colName === 'item_id') {
              
            }
            else if (colName === 'unit_no') {
                if (window.g_queriedItems && item_id) {
                    var item = window.g_queriedItems[item_id];
                    if (item) {
                        item.units.forEach(function (unit) {
                            //if (unit.unit_no === cellValue.value) {
                            if (unit.unit_factor == 1) row.s_barcode = unit.barcode
                            if (unit.unit_no === cellValue) {
                                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'barcode', unit.barcode);
                                let qty = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'quantity');
                                let wholesale_price = toMoney( unit.wholesale_price )
                               
                                let cost_price; //unit中是小单位成本价
                                let wholesale_sub_amount, cost_sub_amount;
                                if (unit.wholesale_price) {
                                    wholesale_sub_amount = qty * wholesale_price
                                }
                                last_time_price = unit.recent_price
                                var contract_price = toMoney(unit.contract_price)
                                var contract_sub_amount=''
                                if (unit.contract_price) {                                    
                                    contract_sub_amount = qty * contract_price
                                }
                                if (unit.cost_price && seeInPrice) { // 先有权限看到成本价，再渲染计算
                                    cost_price = toMoney(unit.cost_price * unit.unit_factor)
                                    cost_sub_amount = qty * cost_price
                                }

                                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'contract_price', contract_price);
                                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'contract_sub_amount', toMoney(contract_sub_amount));


                                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'wholesale_price', wholesale_price);
                                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'wholesale_sub_amount', toMoney(wholesale_sub_amount));

                                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'last_time_price', last_time_price);

                                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'cost_price', cost_price);
                                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'cost_amount', toMoney(cost_sub_amount));


                                //$('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'real_price', unit.price);
                            }
                        });

                        updateTotalAmount();
                    }
                }
            }
            else if (colName === 'quantity') {
                //var msg = evaluate(cellValue, rowIndex)
                //if (msg) return
                if (!CheckNumberAndAlert("数量")) return
                qty = parseFloat(cellValue);
                wholesale_price = parseFloat(wholesale_price);
                contract_price = parseFloat(contract_price);
                cost_price = parseFloat(cost_price);
                if (qty && contract_price) {
                    var new_sub_amount = (qty * contract_price).toString();
                    $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'contract_sub_amount', toMoney(new_sub_amount) || toMoney());
                }
                if (qty && wholesale_price) {
                    var new_sub_amount = (qty * wholesale_price).toString();
                    $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'wholesale_sub_amount', toMoney(new_sub_amount) || toMoney());
                }
                if (qty && cost_price) {
                    new_sub_amount = (qty * cost_price).toString();
                    $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'cost_amount', toMoney(new_sub_amount) || toMoney());
                }
                updateRowSubAmount(rowIndex)

                updateTotalAmount();
            }
            else if (colName === 'wholesale_price') {

                if (!isNaN(cellValue)) {
                    $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'wholesale_price', toMoney(cellValue));


                    var price = parseFloat(cellValue);
                    if (qty && wholesale_price) {
                        new_sub_amount = qty * price;
                        $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'wholesale_sub_amount', toMoney(new_sub_amount) || toMoney(0));
                    }
                    updateTotalAmount();
                }
                else {
                    var msg = '请输入正确的金额';
                    bw.toast(msg, 5000);
                    if (isNaN(oldValue)) {
                        oldValue = '';
                    }
                    $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'wholesale_price', oldValue);
                }
            }
            else if (colName === 'contract_price') {

                if (!isNaN(cellValue)) {
                    $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'contract_price', toMoney(cellValue))
                    var price = parseFloat(cellValue)
                    if (qty && contract_price) {
                        new_sub_amount = qty * price
                        $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'contract_sub_amount', toMoney(new_sub_amount) || toMoney())
                    }
                    updateTotalAmount()
                }
                else {
                    var msg = '请输入正确的金额';
                    bw.toast(msg, 5000);
                    if (isNaN(oldValue)) {
                        oldValue = '';
                    }
                    $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'contract_price', oldValue);
                }
            }
            else if (colName === 'cost_price') {
                if (!isNaN(cellValue)) {
                    $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'cost_price', toDecimal(cellValue));


                    var price = parseFloat(cellValue);
                    if (qty && price) {
                        new_sub_amount = qty * price;
                        $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'cost_amount', toDecimal(new_sub_amount) || toDecimal(0));
                    }
                    updateTotalAmount();
                }
                else {
                    var msg = '请输入正确的金额';
                    bw.toast(msg, 5000);
                    if (isNaN(oldValue)) {
                        oldValue = '';
                    }
                    $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'cost_price', oldValue);
                }
            }
            else if (colName === 'wholesale_sub_amount') {
                if (!isNaN(cellValue)) {
                    $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'wholesale_sub_amount', toDecimal(cellValue));


                    if (cellValue) checkNumber(cellValue);
                    var price = parseFloat(cellValue);
                    if (qty && wholesale_sub_amount) {
                        new_sub_amount = price / qty;
                        $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'wholesale_price', toDecimal(new_sub_amount) || toDecimal(0));
                    }
                    updateTotalAmount();
                }
                else {
                    var msg = '请输入正确的金额';
                    bw.toast(msg, 5000);
                    if (isNaN(oldValue)) {
                        oldValue = '';
                    }
                    $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'wholesale_sub_amount', oldValue);
                }
            }
            else if (colName === 'contract_sub_amount') {
                if (!isNaN(cellValue)) {
                    $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'contract_sub_amount', toDecimal(cellValue))

                    if (cellValue) checkNumber(cellValue);
                    var price = parseFloat(cellValue);
                    if (qty && wholesale_sub_amount) {
                        new_sub_amount = price / qty;
                        $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'contract_price', toDecimal(new_sub_amount) || toDecimal(0));
                    }
                    updateTotalAmount();
                }
                else {
                    var msg = '请输入正确的金额';
                    bw.toast(msg, 5000);
                    if (isNaN(oldValue)) {
                        oldValue = '';
                    }
                    $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'contract_sub_amount', oldValue);
                }
            }
            else if (colName === 'cost_amount') {
                if (!isNaN(cellValue)) {

                    $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'cost_amount', toDecimal(cellValue));
                    var price = parseFloat(cellValue);
                    if (qty && price) {
                        new_cost_price = price / qty;
                        $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'cost_price', toDecimal(new_cost_price) || toDecimal(0));
                    }
                    updateTotalAmount();
                }


                updateRowSubAmount(rowIndex)

                updateTotalAmount();

            }
            else if (colName === 'from_branch_position') {
                row.from_branch_position_name = args.value ? args.value.label : ""
                row.from_branch_position = cellValue ? cellValue:"0"
                window.g_queryStatus = 'querying'
                GetBatchStock([rowIndex], row.from_branch_position, "from","from")
            }
            else if (colName === 'to_branch_position') {
                row.to_branch_position_name = args.value ? args.value.label : ""
                row.to_branch_position = cellValue ? cellValue : "0"
                window.g_queryStatus = 'querying'
                GetBatchStock([rowIndex], row.to_branch_position, "from","to")
            }
            else if (colName === 'batch_id') {
                let fromBranchId = $("#from_branch_id").val() ? $("#from_branch_id").val().value:"-1"
                let toBranchId = $("#to_branch_id").val() ? $("#to_branch_id").val().value : "-1"
                let rowFromBranchPosition = row.from_branch_position ? row.from_branch_position : "0"
                let rowToBranchPosition = row.to_branch_position ? row.to_branch_position : "0"
                let fromKey = row.item_id + "_" + fromBranchId + "_" + rowFromBranchPosition
                let toKey = row.item_id + "_" + toBranchId + "_" + rowToBranchPosition
                let produceDate = args.row.produce_date
                if (produceDate.length == 6) {
                    produceDate = '20' + produceDate.substr(0, 2) + '-' + produceDate.substr(2, 2) + '-' + produceDate.substr(4, 2)
                    row[colName] = produceDate
                }
                let batchNo = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, "batch_no")
                if (!batchNo) batchNo = ""
                window.g_queryStatus = 'querying'
                setStockQty(fromKey, rowIndex, produceDate, batchNo, "from", "from")
                setStockQty(toKey, rowIndex, produceDate, batchNo, "from", "to")
            }
            else if (colName === 'batch_id_f') {
                let fromBranchId = $("#from_branch_id").val() ? $("#from_branch_id").val().value : "-1"
                let toBranchId = $("#to_branch_id").val() ? $("#to_branch_id").val().value : "-1"
                let rowFromBranchPosition = row.from_branch_position ? row.from_branch_position : "0"
                let rowToBranchPosition = row.to_branch_position ? row.to_branch_position : "0"
                let fromKey = row.item_id+"_" + fromBranchId+"_" + rowFromBranchPosition
                let toKey = row.item_id+"_" + toBranchId+"_" + rowToBranchPosition
                let batchNo = args.row.batch_no
                let produceDate = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, "produce_date")
                if (!produceDate) produceDate = ""
                window.g_queryStatus = 'querying'
                setStockQty(fromKey, rowIndex, produceDate, batchNo, "from", "from")
                setStockQty(toKey, rowIndex, produceDate, batchNo, "from", "to")
        }
            //会引起Bug,修改备注导致成本金额发生变化，这是三年前的一段代码
            //else {
            //    var msg = '请输入正确的金额';
            //    bw.toast(msg, 5000);
            //    if (isNaN(oldValue)) {
            //        oldValue = '';
            //    }
            //    $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'cost_amount', oldValue);
            //}

        saveSheetToCach()
    }
    $("#jqxgrid").on('cellendedit', cellendedit)

    $("#btnClose").on('click', function () {
        window.parent.closeTab(window);

    });
   
    function setStockOnbranchUpdate(rowIndex, item_id, branch_id, branch_position, batch_id, produce_date, batch_no,flag) {
        if (!item_id) item_id = -1
        if (!branch_id) branch_id = -1
        if (!branch_position) branch_position = 0
        if (!produce_date && !batch_no) batch_id = 0
        if (produce_date && !batch_no) batch_no = ""
        var params = { operKey: g_operKey, item_id: item_id, branch_id: branch_id, branch_position: branch_position, batch_id, produce_date, batch_no }
        $.ajax({
            url: '/api/SaleSheet/SetStockOnbranchUpdate',
            type: 'GET',
            contentType: 'application/json',
            processData: true,
            data: params,
            success: function (data) {
                let curGrid = '#jqxgrid'
                if (window.curGridSlt) curGrid = window.curGridSlt
                if (data.result === 'OK' && data.data.length) {
                    if (flag == "from") {
                        $(curGrid).jqxGrid('setcellvalue', rowIndex, 'from_branch_qty_unit', data.data[0].stock_qty_unit)
                    } else {
                        $(curGrid).jqxGrid('setcellvalue', rowIndex, 'to_branch_qty_unit', data.data[0].stock_qty_unit)
                    }
                    
                    //$(curGrid).jqxGrid('setcellvalue', rowIndex, 'usable_stock_qty_unit', data.data[0].usable_stock_qty_unit)
                } else {
                    if (flag == "from") {
                        $(curGrid).jqxGrid('setcellvalue', rowIndex, 'from_branch_qty_unit', "")
                    } else {
                        $(curGrid).jqxGrid('setcellvalue', rowIndex, 'to_branch_qty_unit', "")
                    }
                }
                window.g_queryStatus = 'done'
            }
        })
    }
   
    $("#btnSave").on('click', function () {

        var res = GetSheetData();
        if (res.result === "Error") {
            bw.toast(res.msg, 5000); return;
        }
        var sheet = res.sheet;
 
        $.ajax({
            url: '/api/MoveSheet/Save',
            type: 'POST',
            contentType: 'application/json',
            // data: JSON.stringify({sheet_id:'1',supcust_id:'2'}),
            data: JSON.stringify(sheet),
            success: function (data) {
                if (data.result === 'OK') {
                    $('#sheet_id').val(data.sheet_id)
                    $('#sheet_no').text(data.sheet_no)

                    updateSheetState()
                    removeSheetFromCach()
                    bw.toast('保存成功', 3000)
         
                }
                else {
                    bw.toast(data.msg, 3000)
                }
            },
            error: function (xhr) {
                console.log("返回响应信息：" + xhr.responseText)
            }
        });
    });

  /*
    $("#btnCopyToSheets").on('click', function () {
        var checkedSheets = copyToSheets(true)
        if (checkedSheets.length == 0) {
            bw.toast('请先选择复制到新单据的单据类型', 3000)
        }
        var res = GetSheetData();
        if (res.result === "Error") {
            bw.toast(res.msg, 5000); return;
        }
        var sheet = res.sheet;
        if (checkedSheets) {

            checkedSheets.forEach((checkedSheet) => {
                var url = checkedSheet.url
                var params = ''
                for (var key in sheet) {
                    var value = sheet[key]
                    if ('SheetRows,operKey,OperKey,approve_time,red_flag,sheet_no,sheet_id,approver_id,approver_name,maker_id,maker_name,happen_time'.indexOf(key) == -1) {
                        params += '&'
                        params += `${key}=${value}`
                    }
                }
                url += params
                window.parent.newTabPage(`${checkedSheet.title}`, `${url}`, window, sheet)
            });
        }

    })
    */
    $("#btnAdd").on('click', function () {
        window.parent.newTabPage('调拨单', `Sheets/MoveSheet?`);
        if (!window.firstCachTime) {
            setTimeout(() => {
                window.parent.closeTab(window);
            }, 50)

        }
    })


    function approveMoveSheet(bReview) {
        var res = GetSheetData();
        if (res.result === "Error") {
            bw.toast(res.msg, 5000); return;
        }
        var sheet = res.sheet;

        var alertMsg = '';
        var alertCondi = "审核";
        if (bReview) alertCondi = "复核";

        function approve() {
            jConfirm('确定要' + alertCondi + '吗?<br>' + alertMsg, function () {
                if (res.result === "Error") {
                    bw.toast(res.msg, 5000); return;
                }

                $("#btnApprove").attr('disabled', true);

                var sheets = "";
                var checkAccount_sellerID = "";
                if (window.srcWindow) {
                    sheets = window.srcWindow.sheets;
                    if (sheets) {
                        checkAccount_sellerID = sheets.sellerID;
                        sheets = getPreviousSheetsList(sheets);
                    }
                }

                var tempi = null;
                if (window.g_temp_index) tempi = window.g_temp_index;

                if (bReview) sheet.bReview = true;

                $.ajax({
                    url: '/api/MoveSheet/SaveAndApprove',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(sheet),
                    success: function (data) {
                        if (data.result === 'OK') {
                            $('#sheet_id').val(data.sheet_id);
                            $('#sheet_no').text(data.sheet_no);
                            $('#approve_time').text(data.approve_time);
                            $('#approve_time').val(data.approve_time);
                            $('#happen_time').jqxDateTimeInput('val', data.happen_time);
                            updateSheetState();
                            removeSheetFromCach();
                          
                            if (bReview) {
                                bw.toast('复核成功', 3000);
                            }
                            else bw.toast('审核成功', 3000);
                            if (srcWindow && srcWindow.updateGridRow)
                                srcWindow.updateGridRow(data.sheet_id, { sheet_status: '已审', approve_time: data.approve_time });

                            var sellerID = $('#seller_id').val();
                            var order_sheet_id = $('#order_sheet_id').val();
                            var senders_id = $('#senders_id').val();
                            var flag = false;
                            var senderID = senders_id;
                           

                            var newRes = GetSheetData();
                            if (newRes.result === "Error") {
                                bw.toast(res.msg, 5000); return;
                            }
                            var newSheetInfo = newRes.sheet;
                            if (order_sheet_id) {
                                if (senderID.value == checkAccount_sellerID) {
                                    flag = true;
                                }
                            } else {
                                if (sellerID.value == checkAccount_sellerID) {
                                    flag = true;
                                }
                            }

                            if (flag) {
                                newSheetInfo.isChecked = true;
                                newSheetInfo.isFromWeb = false;
                                newSheetInfo.isSum = false;
                                newSheetInfo.is_imported = false;
                                newSheetInfo.fixinG_ARREARS = false;
                                newSheetInfo.sheetRows = newSheetInfo.SheetRows;
                                if (tempi) {
                                    sheets.splice(tempi, 0, newSheetInfo);
                                } else {
                                    sheets.unshift(newSheetInfo);
                                }
                                assignSheets(sheets);
                            }
                            if (window.g_companySetting) {
                                if (window.g_companySetting.newSheetAfterApprove == 'True') {
                                    btnCopySheetHead_click();
                                }
                            }
                        } else {
                            $("#btnApprove").attr('disabled', false);
                            bw.toast(data.msg, 3000);
                        }
                    },
                    error: function (xhr) {
                        console.log("返回响应信息：" + xhr.responseText);
                    }
                });
            }, "");
        }

        if (sheet.sheet_id != '') {
            checkHasInventory(sheet, 'MoveSheet').then((data) => {
                let itemNameList = data.inventoryItemNames;
                itemNameList.forEach((itemName) => {
                    if (alertMsg == '') {
                        alertMsg = itemName;
                    } else {
                        alertMsg += ',' + itemName;
                    }
                });
                if (alertMsg != '') {
                    alertMsg = alertMsg + '<br>以上商品单据保存期间发生过盘点，审核可能会造成库存不准';
                }
                approve();
            });
        } else {
            approve();
        }
    }

    $("#btnApprove").on('click', function () {
        approveMoveSheet(false);
    });

    $("#btnReview").on('click', function () {
        var sheet_id = $('#sheet_id').val();
        var approve_time = $('#approve_time').val();
        if (approve_time) {
            jConfirm('确定要复核吗?', function (r) {
                if (r) {
            $.ajax({
                url: '/api/MoveSheet/Review',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ sheet_id: sheet_id, operKey: g_operKey, approve_time: approve_time }),
                success: function (data) {
                    if (data.result === 'OK') {
                        $('#sheet_id').val(data.sheet_id);
                        $('#sheet_no').text(data.sheet_no);
                        $('#review_time').text(data.review_time);
                        updateSheetState();
                        removeSheetFromCach();
                        bw.toast('复核成功', 3000);
                    } else {
                        bw.toast(data.msg, 3000);
                    }
                },
                error: function (xhr) {
                    console.log("返回响应信息：" + xhr.responseText);
                }
            });
                }
            }, "确认复核");
        } else {
            approveMoveSheet(true);
        }
    });
       
     


    $("#btnDelete").on('click', function () {
    
        var sheet_id = $('#sheet_id').val()
        jConfirm('确定要删除本单据吗？', function () {
            $.ajax({
                url: '/api/MoveSheet/Delete',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ operKey: g_operKey, sheet_id: sheet_id }),
                success: function (data) {
                    if (data.result === 'OK') {
                        //$("#btnApprove").attr('disabled', true);
                        $("#btnSave").attr('disabled', true);
                        $("#btnApprove").attr('disabled', true);
                        $("#btnDelete").attr('disabled', true);
                        $("#btnPrint").attr('disabled', true);
                        $("#btnCopy").attr('disabled', true);
                        $("#btnAdd").attr('disabled', true);
                        bw.toast('删除成功,即将关闭窗口', 3000);
                        setTimeout(function () {
                            window.parent.closeTab(window);
                        }, 2000);

                    }
                    else {
                        bw.toast(data.msg, 3000);
                    }
                },
                error: function (xhr) {
                    console.log("返回响应信息：" + xhr.responseText);
                }
            });
        }, "");

    });
  

    $("#btnDelete").on('click', function () {
        var res = GetSheetData();
        if (res.result === "Error") {
            bw.toast(res.msg, 5000); return;
        }
        var sheet = res.sheet;
        jConfirm('确定要删除本单据吗？', function () {
            $.ajax({
                url: '/api/MoveSheet/Delete',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ operKey: g_operKey, sheet_id: sheet.sheet_id }),
                success: function (data) {
                    if (data.result === 'OK') {
                        //$("#btnApprove").attr('disabled', true);
                        $("#btnSave").attr('disabled', true);
                        $("#btnApprove").attr('disabled', true);
                        $("#btnDelete").attr('disabled', true);
                        $("#btnPrint").attr('disabled', true);
                        $("#btnCopy").attr('disabled', true);
                        bw.toast('删除成功,即将关闭窗口', 3000);
                        setTimeout(function () {
                            window.parent.closeTab(window);
                        }, 2000);

                    }
                    else {
                        bw.toast(data.msg, 3000);
                    }
                },
                error: function (xhr) {
                    console.log("返回响应信息：" + xhr.responseText);
                }
            });
        }, "");

    });
    function initProduceDate(rows) {
        rows.forEach((row) => {
            if (row.item_id && row.batch_level && row.batch_level !== "0" && !row.produce_date) {
                row.produce_date = "无产期"
                row.batch_id = "0"
            }
        })
    }
    function getSheetToPrint() {
        var res = GetSheetData();
        if (res.result === "Error") {
            bw.toast(res.msg, 5000); return;
        }
        
      
        res.sheet.SheetRows.forEach(function (row) {
            var item = window.g_queriedItems[row.item_id]
            var smallQty = Number(row.quantity) * Number(row.unit_factor)
            var leftQty = 0
            var unit, qty, leftQty
            var sQty = ''
            leftQty = smallQty
            var unit_relation = ''
            var absLeftQty = Math.abs(leftQty)
            var flag = leftQty / absLeftQty
            for (var i = 0; i <= item.units.length - 1; i++) {
                unit = item.units[i]
                if (!unit.unit_factor) continue
                if (unit.unit_type == 'b') unit_relation = "1*" + unit.unit_factor.toString()

                qty = Math.floor(absLeftQty / unit.unit_factor)

                if (qty > 0)
                    sQty += (qty * flag).toString() + unit.unit_no

                absLeftQty = absLeftQty % unit.unit_factor
                if (absLeftQty <= 0) break
            }

            row.unit_relation = unit_relation
            row.quantity_unit_conv = sQty
            row.retail_price = item.retail_price
            if (window.g_companySetting.sheetShowBarcodeStyle == '2') {
                row.barcode = row.s_barcode
            }

        })
       
        return res.sheet
    }
    $("#btnPrint").on('click', function () {
        //获取表格的类型
        var sheetType = $('#sheet_type').val();
        var sheet_id = $('#sheet_id').val();
        //获取客户的id
      
        var sheet = getSheetToPrint()
        if (!sheet) return
        $.ajax({
            url: '/api/PrintTemplate/GetTemplateToUse',
            type: 'GET',
            contentType: 'application/json',
            data: {
                operKey: g_operKey,
                sheetType: sheetType
            },
            success: function (printInfo) {
                if (printInfo.result === 'OK') {
                    if (printInfo.templateList.length == 0) {
                        bw.toast("没有可用打印模板", 5000)
                        return
                    }
                    var tmp = printInfo.templateList[0]
                    var sTmp = tmp.template_content
                    tmp = JSON.parse(tmp.template_content)
                    var printTemplate = []
                    if (sTmp.indexOf('"print_count"')) printTemplate.push({ name: "print_count" })     
                    
                    $.ajax({
                        url: '/api/MoveSheet/GetSheetToPrint',
                        type: 'GET',
                        contentType: 'application/json',
                        data: {
                            operKey: g_operKey,
                            sheet_id: sheet_id,
                            smallUnitBarcode: true,
                            printTemplate: JSON.stringify(printTemplate)
                        },
                        success: function (data) {
                            if (data.result === 'OK') {
                                var sheet=data.sheet

                              
                                var container = window.parent.CEFPrinter
                                if (!container)
                                    container = window.parent.CefGlue

                                if (!container)
                                    container = window.parent

                                if (!container.printSheetByTemplate) {
                                    bw.toast('在客户端程序中才可以打印', 3000)
                                    return
                                }
                                window.parent.g_SheetsWindowForPrint = window.srcWindow
                                container.printSheetByTemplate(sheet, tmp, true, printInfo.cloudPrinters, printInfo.variables)


                                return
                            }
                            else {
                                bw.toast(data.msg, 3000)
                            }
                        },
                        error: function (xhr) {
                            console.log("返回响应信息：" + xhr.responseText)
                        }
                    }) 
                }
                else {
                    bw.toast(printInfo.msg, 3000)
                }
            },
            error: function (xhr) {
                console.log("返回响应信息：" + xhr.responseText)
            }
        })
    })  
  $('#choosetemplate').on('click', function () {

        var maskBg = document.getElementById('topCoverDiv');
        var dia = document.getElementById('dia');
        maskBg.style.display = (maskBg.style.display == 'none') ? 'block' : 'none';
        dia.style.display = (dia.style.display == 'none') ? 'block' : 'none';


        //获取表格的类型
        var sheetType = $('#sheet_type').val();
        var sheet_id = $('#sheet_id').val();
        var sheet = getSheetToPrint()
        if (!sheet) return
        $.ajax({
            url: '/api/PrintTemplate/GetTemplateToUse',
            type: 'GET',
            contentType: 'application/json',
            data: {
                operKey: g_operKey,
                sheetType: sheetType
            },
            success: function (printInfo) {
                if (printInfo.result === 'OK') {
                    if (printInfo.templateList.length == 0) {
                        bw.toast("没有可用打印模板", 5000)
                        return
                    }

                    
                    var templateList = printInfo.templateList;
                    var templateShowInHTML = $('#template-list');
                    templateShowInHTML.empty();
                    for (let i = 0; i < templateList.length; i++) {
                        templateHTML = `<button id="templatePrint" value="${i}" style="margin: 10px 0px 0px 10px;width:auto;">${templateList[i].template_name}</button>`
                        templateShowInHTML.append(templateHTML);
                    }
                 

                    $("[id='templatePrint']").on('click', function () {
                        var index = parseInt(this.value);
                        var tmp = printInfo.templateList[index];
                        var sTmp = tmp.template_content
                        tmp = JSON.parse(tmp.template_content);

                        var printTemplate = []
                       

                        //if (sTmp.indexOf('"prepay_balance"') >= 0) printTemplate.push({ name: "prepay_balance" })
                        //if (sTmp.indexOf('"arrears_balance"') >= 0) printTemplate.push({ name: "arrears_balance" })
                        //if (sTmp.indexOf('"order_item_balance"') >= 0) printTemplate.push({ name: "order_item_balance" })
                        if (sTmp.indexOf('"print_count"') >= 0) printTemplate.push({ name: "print_count" })
                        if (sTmp.indexOf('"name":"give_qty_unit"') >= 0) printTemplate.push({ name: "give_qty_unit" })
                     
                        var smallUnitBarcode = document.all.ckPrintSmallBarcode.checked
                        var sheet_id = $('#sheet_id').val();

                        $.ajax({
                            url: '/api/MoveSheet/GetSheetToPrint',
                            type: 'GET',
                            contentType: 'application/json',
                            data: {
                                operKey: g_operKey,
                                sheet_id: sheet_id,
                                smallUnitBarcode: smallUnitBarcode,
                                printTemplate: JSON.stringify(printTemplate)

                            },
                            success: function (data) {
                                if (data.result === 'OK') {
                                    var sheet = data.sheet

                                    var container = window.parent.CEFPrinter
                                    if (!container)
                                        container = window.parent.CefGlue

                                    if (!container)
                                        container = window.parent

                                    if (!container.printSheetByTemplate) {

                                        if (printInfo.cloudPrinters.length == 0) {
                                            bw.toast("请到客户端打印，或是添加一个云打印机", 5000)
                                            return
                                        }
                                        templateShowInHTML.empty();
                                        templateShowInHTML.append("请选择打印机：<br><br>");
                                        for (let i = 0; i < printInfo.cloudPrinters.length; i++) {
                                            templateHTML = `<button id="cloudPrinter" value="${i}" style="margin: 10px 0px 0px 10px;width:auto;">${printInfo.cloudPrinters[i].printer_name}</button>`
                                            templateShowInHTML.append(templateHTML);
                                        }
                                        $("[id='cloudPrinter']").on('click', function () {
                                            var index = parseInt(this.value);
                                                
                                            var ptr = printInfo.cloudPrinters[index]
                                            var device_id = ptr.device_id
                                            var check_code = ptr.check_code
                                            var printer_brand = ptr.printer_brand

                                                    $.ajax({
                                                        url: '/AppApi/CloudPrint/PrintSheetWithTemplate',
                                                        type: 'POST',
                                                        contentType: 'application/json',
                                                        data: JSON.stringify({
                                                            operKey: g_operKey,
                                                            device_id: device_id,
                                                            check_code: check_code,
                                                            printer_brand: printer_brand,
                                                            sheet: sheet,
                                                            tmp: tmp,
                                                            variables:printInfo.variables,
                                                            cus_orderid: sheet.sheet_no,
                                                            copies: "1"
                                                        }),
                                                        success: function (res) {
                                                            console.log(res)
                                                            var msg = res.msg == "" ? "打印请求已发送,请等待" : res.msg
                                                            bw.toast(msg)
                                                            console.log("Print post sent to " + device_id)
                                                        },
                                                        error: function (xhr) {
                                                            bw.toast('发送打印请求失败')
                                                            console.log("返回响应信息：" + xhr.responseText)
                                                        }
                                                    });
                                                })

                                         
                                        return
                                    }
                                   
                                    window.parent.g_SheetsWindowForPrint = window.srcWindow
                                    container.printSheetByTemplate(sheet, tmp, true, printInfo.cloudPrinters,printInfo.variables)

                                    var clientVersion = 0
                                    if (window.parent && window.parent.CefGlue) {
                                        clientVersion = window.parent.CefGlue.getClientVersion()
                                    }

                                    if (parseFloat(clientVersion) < 3.32) {
                                        $.ajax({
                                            url: '/api/Printer/PrintMark',
                                            type: 'POST',
                                            contentType: 'application/json',
                                            data: JSON.stringify({
                                                operKey: g_operKey,
                                                sheetType: 'X',
                                                sheetIDs: sheet.sheet_id,
                                                printEach: true,
                                                printSum: false
                                            }),
                                            success: function (data) {
                                                if (data.result === 'OK') {
                                                }
                                                else {

                                                }
                                            },
                                            error: function (xhr) {
                                                // console.log("返回响应信息：" + xhr.responseText)
                                            }
                                        })
                                    }
                                }
                                else {
                                    bw.toast(data.msg, 3000)
                                }
                            },
                            error: function (xhr) {
                                console.log("返回响应信息：" + xhr.responseText)
                            }
                        }) 
                         
                    });
                  

                    return;

                }
                else {
                    bw.toast(data.msg, 3000);
                }
            },
            error: function (xhr) {
                console.log("返回响应信息：" + xhr.responseText);
            }
        });
    });
   /* updateSheetState()*/
    window.onresize();
}


    function updateTotalAmount() {
        var rows = $('#jqxgrid').jqxGrid('getrows');
        var wholesale_amount = 0;
        var contract_amount = 0;
        var cost_amount = 0;
        for (var i = 0; i < rows.length; i++) {
            var row = rows[i]
            if (row.item_id && parseFloat(row.wholesale_sub_amount))  wholesale_amount += parseFloat(row.wholesale_sub_amount);
            
     
            if (row.item_id && parseFloat(row.contract_sub_amount))  contract_amount += parseFloat(row.contract_sub_amount)
            
            if (row.item_id && parseFloat(row.cost_amount))           cost_amount += parseFloat( row.cost_amount);
           
        }
        
        $('#contract_amount').jqxInput('val', toMoney(contract_amount));
        $('#wholesale_amount').jqxInput('val', toMoney(wholesale_amount));
        $('#cost_amount_avg').jqxInput('val', toMoney(cost_amount));
}
function updateRowSubAmount(rowIndex) {
    debugger
    var rows = $('#jqxgrid').jqxGrid('getrows')
    var gridRow = rows[rowIndex]
    if (gridRow.quantity) {
        if (gridRow.wholesale_price) gridRow.wholesale_sub_amount = toMoney(parseFloat(gridRow.quantity) * parseFloat(gridRow.wholesale_price))
        if (gridRow.contract_price) gridRow.contract_sub_amount = toMoney(parseFloat(gridRow.quantity) * parseFloat(gridRow.contract_price))
        gridRow.cost_amount = toMoney(parseFloat(gridRow.quantity) * parseFloat(gridRow.cost_price))
    }
}
