using ArtisanManage.Models;
using ArtisanManage.Services;
using ArtisanManage.Services.Helpers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Primitives;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using ArtisanManage.MyJXC;

namespace ArtisanManage
{
    public class FixDataModel : PageBaseModel
    {
        public string operKey;
        CMySbCommand cmd;
         
 
        public FixDataModel(CMySbCommand cmd):base(MenuId.companySetting)
        {
            this.cmd = cmd;
        }
  
        public async Task OnGet()
        {
            

        }
    }

    [Route("setting/[controller]/[action]")]
    public class FixDataController : YjController
    {
        CMySbCommand cmd;


        public FixDataController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }
        
        string AppCondi = "approve_time is not null and red_flag is null";
        string ArrearsCondi = " abs(total_amount-now_pay_amount-now_disc_amount)>0.1 ";
        //string ArrearsAndPrepayCondi = "(abs(total_amount-now_pay_amount-now_disc_amount)>0.1 or abs(prepay_amount)>0.01) ";
        string ArrearsAndPrepayCondi = "not(abs(total_amount-now_pay_amount-now_disc_amount)<=0.1 and  abs(prepay_amount)<=0.01) ";


        [HttpPost]
        public async Task<JsonResult> FixArrears([FromBody] dynamic data)
        { 
            string companyID = data.companyID;
            string companyName = data.companyName;
            string clientID = data.clientID;
            string clientName = data.clientName;
            string supcustFlag = data.supcustFlag;

            var result = "OK";
            var msg = "";

            string sql = "";
            string condi = "";
         
            if (clientID != "")
            {
                var supcustsql = @$"select supcust_id  from info_supcust  where (supcust_id ={clientID} or acct_cust_id={clientID} ) and company_id={companyID}";


                condi = $" and supcust_id in ({supcustsql} )";




                sql = $"select * from info_supcust ic left join g_company gc on ic.company_id=gc.company_id where ic.company_id={companyID} and gc.company_name='{companyName}' and ic.supcust_id={clientID} and ic.sup_name='{clientName}';";
                dynamic clientRec = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                if (clientRec == null)
                {
                    return Json(new { result = "Error", msg = "客户信息不匹配" });
                }
            }
            else
            {
                sql = $"select * from g_company gc where company_id={companyID} and company_name='{companyName}';";
                dynamic clientRec = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                if (clientRec == null)
                {
                    return Json(new { result = "Error", msg = "公司信息不匹配" });
                }
            }

            sql = "";


			sql += $"delete from arrears_balance where company_id={companyID} {condi};";
            sql += $"delete from prepay_balance where company_id={companyID} {condi};";
            sql += $"delete from prepay_detail where company_id={companyID} {condi};";
            sql += $"delete from client_account_history where company_id={companyID} {condi};";
           
            sql += $"update sheet_sale_main set paid_amount=now_pay_amount,disc_amount=now_disc_amount where company_id={companyID} and {AppCondi} and {ArrearsAndPrepayCondi} {condi};";
            sql += $"update sheet_buy_main set paid_amount=now_pay_amount,disc_amount=now_disc_amount where company_id={companyID} and {AppCondi} and {ArrearsAndPrepayCondi} {condi};";
            sql += $"update sheet_prepay set paid_amount=now_pay_amount,disc_amount=now_disc_amount where company_id={companyID} and {AppCondi} and {ArrearsCondi} {condi};";
            sql += $"update sheet_fee_out_main set paid_amount=now_pay_amount,disc_amount=now_disc_amount where company_id={companyID} and {AppCondi} and {ArrearsCondi} {condi};";

            // sql += $"update sheet_get_arrears_main set paid_amount=0,now_pay_amount=0 where approve_time is not null and red_flag is null and company_id={companyID} {condi};";
            CMySbTransaction tran = cmd.Connection.BeginTransaction();
            try
            {
                cmd.company_id = companyID;
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();

                List<dynamic> lstAllSheets = new List<dynamic>();
                await LoadSheets<SheetPrepay<SheetRowBase>, SheetRowBase>(lstAllSheets, companyID, AppCondi +  condi);
                await LoadSheets<SheetBuy,SheetRowBuy>(lstAllSheets, companyID,AppCondi +" and " + ArrearsAndPrepayCondi + condi);
                await LoadSheets<SheetSale, SheetRowSale>(lstAllSheets, companyID, AppCondi + " and " + ArrearsAndPrepayCondi + condi);
                await LoadSheets<SheetGetArrears, SheetRowArrears>(lstAllSheets, companyID, AppCondi + condi);

               // await LoadSheets<SheetOrderItem, SheetRowOrderItem>(lstAllSheets, companyID, condi);

                //lstAllSheets.Sort((sht1,sht2) =>
                //{
                //    string t1 =  sht1.approve_time;
                //    string t2 =  sht2.approve_time;
                    
                //    int c= t1.CompareTo(t2);
                //    return c;
                //});

                var newLstAllSheets = lstAllSheets.OrderBy(sht => sht.approve_time).ThenBy(s => s.happen_time).ToList();
                int index = 0;
                //foreach (var sht in lstAllSheets)
                foreach (var sht in newLstAllSheets)
                {
                    Console.WriteLine(index);
                    if(index==406)
                    {

                    }
                    index++;
                    bool bIgnoreApprove = false;

					if (sht.GetType() == typeof(SheetGetArrears))
                    {                       
                        SheetGetArrears aSht = (SheetGetArrears)sht;
                        List<SheetRowArrears> lstRemoveRows = new List<SheetRowArrears>();
                        foreach (var row in aSht.SheetRows)
                        {
                            string mm_table = "";
                            if (row.m_sheet_type == "X" || row.m_sheet_type == "T")
                                mm_table = "sheet_sale_main";
                            else if (row.m_sheet_type == "CG" || row.m_sheet_type == "CT")
                                mm_table = "sheet_buy_main";
                            else if (row.m_sheet_type == "YS" || row.m_sheet_type == "YF" || row.m_sheet_type == "DH")
                                mm_table = "sheet_prepay";
                            else if (row.m_sheet_type == "ZC" || row.m_sheet_type == "SR")
                                mm_table = "sheet_fee_out_main";

                            sql = $"select (case when sheet_type in ('T','CT','ZC','SR') then -1 else 1 end)*total_amount total_amount,(case when sheet_type in ('T','CT','ZC','SR') then -1 else 1 end)*paid_amount paid_amount,(case when sheet_type in ('T','CT','ZC','SR') then -1 else 1 end)*disc_amount disc_amount from {mm_table} where company_id={companyID} and sheet_id={row.mm_sheet_id}";
                            //sql = $"select total_amount, paid_amount, disc_amount from {mm_table} where company_id={companyID} and sheet_id={row.mm_sheet_id}";

                            dynamic rec = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                            if (rec != null)
                            {
                                decimal total_amount = CPubVars.ToDecimal(rec.total_amount);
                                decimal paid_amount = CPubVars.ToDecimal(rec.paid_amount);
                                decimal disc_amount = CPubVars.ToDecimal(rec.disc_amount);
                                if (Math.Abs(total_amount - paid_amount - disc_amount) < 0.01m)
                                {
                                    lstRemoveRows.Add(row);
                                }
                                else
                                {
                                    if(Math.Abs(row.paid_amount- paid_amount) > 0.01m)
                                    {
                                        decimal left_amount = row.sheet_amount - paid_amount - row.now_disc_amount - row.now_pay_amount;
                                        if (left_amount < -0.01m)
                                        {
                                            throw (new Exception($"单据{row.mm_sheet_no}已经不欠款,收款单{aSht.sheet_no}无需收款"));
                                        }
                                        row.paid_amount = paid_amount;
                                        row.left_amount = left_amount; 
                                    }
                                   
                                }
                            }

                        }

                        sql = $""; 
                        foreach (var row in lstRemoveRows)
                        {
                            aSht.SheetRows.Remove(row);
                            sql += $"delete from {aSht.DetailTable} where company_id={companyID} and sheet_id={aSht.sheet_id} and mm_sheet_id={row.mm_sheet_id};";
                        }

                       // if (lstRemoveRows.Count > 0)
                        { 
                            aSht.sheet_amount = 0;
                            aSht.paid_amount = 0;
                            aSht.disc_amount = 0;
                            aSht.left_amount = 0;
                            aSht.now_pay_amount = 0;
                            aSht.now_disc_amount = 0;
                            foreach (var row in aSht.SheetRows)
                            {
                                aSht.sheet_amount += row.sheet_amount;
                                aSht.paid_amount += row.paid_amount;
                                aSht.disc_amount += row.disc_amount;
                                aSht.left_amount += row.left_amount;
                                aSht.now_pay_amount += row.now_pay_amount;
                                aSht.now_disc_amount += row.now_disc_amount;
                               
                            }
                            var payway1_amount = aSht.now_pay_amount - aSht.payway2_amount-aSht.payway3_amount;
                            aSht.payway1_amount = payway1_amount;
                            sql += $"update {aSht.MainTable} set sheet_amount={aSht.sheet_amount},paid_amount={aSht.paid_amount},disc_amount={aSht.disc_amount},now_pay_amount={aSht.now_pay_amount},now_disc_amount={aSht.now_disc_amount},left_amount={aSht.left_amount},payway1_amount={aSht.payway1_amount} where company_id={companyID} and sheet_id={aSht.sheet_id};";
                        }

                        if (sql != "")
                        {
                            cmd.CommandText = sql;
                            await cmd.ExecuteNonQueryAsync();
                        }
                        if (aSht.SheetRows.Count == 0)
                            bIgnoreApprove = true;
                    }
                    sht.FIXING_ARREARS = true;
                    sht.OperID = sht.maker_id;
                    string err = "";
                    if (!bIgnoreApprove)
                    { 
                        err = await sht.Approve(cmd, false);
                        if (err != "")
                        {
                            err = $"单据{sht.sheet_no}审核出错:{err}";
                            throw (new Exception(err));
                        }
                    }
                }
                tran.Commit();
                Console.WriteLine("OK");
            }
            catch(Exception e)
            {
                msg = e.Message;
                tran.Rollback();
            }
            result = msg == "" ? "OK" : "Error";

            return Json(new { result, msg });
        }
        private async Task LoadSheets<TSHEET,TROW>(List<dynamic> lstAllSheets,string companyID,string condi) where TSHEET:SheetBase<TROW>,new() where TROW:SheetRowBase,new()
        {
            TSHEET sheet = new TSHEET();
            string balCondi = $" and { ArrearsAndPrepayCondi }";

            if (sheet.GetType()==typeof(SheetGetArrears))
            {
                balCondi = "";
            }
            else if (sheet.GetType() == typeof(SheetPrepay<SheetRowBase>))
            {
                balCondi= $" and { ArrearsCondi}";
            }

            //   string sql = $"select string_agg(sheet_id::text,',') from {sheet.MainTable} where company_id={companyID} and {AppCondi} {balCondi} and {condi}";
            string sql = $"select string_agg(sheet_id::text,',') from {sheet.MainTable} where company_id={companyID} and {condi}";
            cmd.CommandText = sql;
            object ov = await cmd.ExecuteScalarAsync();
            string sheetIDs = "";
            if (ov != null && ov != DBNull.Value) sheetIDs = ov.ToString();
            if (sheetIDs != "")
            {
                List<TSHEET> lstSheet = (List<TSHEET>)await sheet.LoadMultiSheets<TSHEET>(cmd, companyID, sheetIDs, "", "", LOAD_PURPOSE.SHOW); ;
                lstSheet.ForEach(sht => lstAllSheets.Add(sht));
            }
        }

      
    }
}