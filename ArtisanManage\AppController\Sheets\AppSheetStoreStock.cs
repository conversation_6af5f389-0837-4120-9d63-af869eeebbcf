﻿using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Pages.WeChat.SheetPages;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using myJXC;
using NPOI.SS.Formula.Functions;
using NuGet.Packaging.Signing;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using static ArtisanManage.AppController.VisitStrategyController;

namespace ArtisanManage.AppController.Sheets
{ 
    [Route("AppApi/[controller]/[action]")]
    public class AppSheetStoreStock : QueryController
    { 
        public AppSheetStoreStock(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        /// <summary>
        /// 加载门店库存上报单--
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="sheetID"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> Load(string operKey, string sheetID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            SheetStoreStock sheet = new SheetStoreStock(LOAD_PURPOSE.SHOW);
            await sheet.Load(cmd, companyID, sheetID);
            SQLQueue QQ = new SQLQueue(cmd);
            string sql = @$"SELECT opt_id, opt_name, attr.attr_id FROM info_attr_opt opt left join info_attribute attr on opt.attr_id = attr.attr_id where opt.company_id ={ companyID} and not attr.spec_opt_in_item order by opt.order_index";
            QQ.Enqueue("attr_options", sql);
            
            List<ExpandoObject> attrOptions = null;
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "attr_options")
                {
                    attrOptions = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, sheet, attrOptions});
        }
        
        /// <summary>
        /// 提交门店库存上报单
        /// </summary>GetItemList
        /// <param name="sheet">
        [HttpPost]
        public async Task<JsonResult> Save([FromBody] dynamic dSheet)
        {
            SheetStoreStock sheet = null;
            string sSheet = Newtonsoft.Json.JsonConvert.SerializeObject(dSheet);
            var currentTime = DateTime.Now.ToText();
            string result;
            string msg = "";
            
            // 获取当前的UTC时间
            DateTimeOffset utcNow = DateTimeOffset.UtcNow;
            // 将UTC时间转换为北京时间（北京时间比UTC时间快8小时）
            DateTimeOffset beijingTime = utcNow.AddHours(8);
            string timestamp = ((DateTimeOffset)beijingTime).ToUniversalTime().ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
            DateTime currentDate = DateTime.Now;


            try
            {
                sheet = Newtonsoft.Json.JsonConvert.DeserializeObject<SheetStoreStock>(sSheet);
                // msg = await saleSheet.SaveAndApprove(cmd, false);
                //if (AutoCommit && msg == "") tran.Commit();
                //else if (bAutoCommit && msg != "") tran.Rollback();
            }
            catch (Exception e)
            {
                msg = e.Message;
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error("in AppSheetSave.Save:" + msg);
                MyLogger.LogMsg("in AppSheetSave.Submit:" + msg + sSheet, Token.CompanyID);
                msg = "提交失败,请联系技术支持";
                return new JsonResult(new { result = "Error", msg });
            }
            if (msg == "")
            {
                cmd.ActiveDatabase = "";
                sheet.Init();
                msg = await sheet.Save(cmd);
            }
            result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.make_time, sheet.happen_time, currentTime });
        }
        [HttpPost]
        public async Task<JsonResult> CreateSaleSheet([FromBody] dynamic dSheet)
        {
            SheetStoreStock sheet = null;
            string sSheet = Newtonsoft.Json.JsonConvert.SerializeObject(dSheet);
            var currentTime = DateTime.Now.ToText();
            string result;
            string msg = "";
            SheetSale saleSheet = new SheetSale();
            SheetSaleOrder saleOrderSheet = new SheetSaleOrder();

            // 获取当前的UTC时间
            DateTimeOffset utcNow = DateTimeOffset.UtcNow;
            // 将UTC时间转换为北京时间（北京时间比UTC时间快8小时）
            DateTimeOffset beijingTime = utcNow.AddHours(8);
            string timestamp = ((DateTimeOffset)beijingTime).ToUniversalTime().ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
            DateTime currentDate = DateTime.Now;


            try
            {
                sheet = Newtonsoft.Json.JsonConvert.DeserializeObject<SheetStoreStock>(sSheet);
                // msg = await saleSheet.SaveAndApprove(cmd, false);
                //if (AutoCommit && msg == "") tran.Commit();
                //else if (bAutoCommit && msg != "") tran.Rollback();
                //string sale_sheet_no = "X" + sheet.sheet_no.Substring(2);
                if(sheet.sheetTypeCreate == SHEET_TYPE.SHEET_SALE)
                {
                    saleSheet.sheet_type = SHEET_TYPE.SHEET_SALE;
                    saleSheet.company_id = sheet.company_id;
                    saleSheet.company_name = sheet.company_name;
                    saleSheet.sheet_name = "销售单";
                    saleSheet.supcust_id = sheet.client_id;
                    saleSheet.sup_name = "";//这个需要补充
                                            //saleSheet.SheetRows=sheet.SheetRows;
                    saleSheet.maker_id = sheet.maker_id;
                    saleSheet.make_time = timestamp;
                    saleSheet.sheet_no = "";//这个需要补充
                    saleSheet.sheet_id = "";
                    saleSheet.OperID = sheet.OperID;
                    saleSheet.OperKey = sheet.OperKey;
                    saleSheet.make_brief = sheet.make_brief;
                    saleSheet.branch_id = sheet.branch_id_forsale;

                    List<SheetRowSale> saleSheetDetails = new List<SheetRowSale>();
                    foreach (SheetRowStoreStock sheetRow in sheet.SheetRows)
                    {
                        SheetRowSale saleSheetDetail = new SheetRowSale();
                        saleSheetDetail.item_id = sheetRow.item_id;
                        saleSheetDetail.item_name = sheetRow.item_name;
                        saleSheetDetail.sheet_id = "";
                        saleSheetDetail.row_index = sheetRow.row_index;
                        //saleSheetDetail.owner_id = "1";
                        saleSheetDetail.batch_id = sheetRow.batch_id;
                        saleSheetDetail.unit_no = sheetRow.s_unit_no;
                        saleSheetDetail.remark = sheetRow.remark;
                        saleSheetDetail.s_unit_no = sheetRow.s_unit_no;
                        saleSheetDetail.b_unit_no = sheetRow.b_unit_no;
                        saleSheetDetail.m_unit_no = sheetRow.m_unit_no;
                        saleSheetDetail.quantity = int.Parse(sheetRow.buy_quantity);
                        saleSheetDetail.unit_factor = sheetRow.unit_factor;
                        saleSheetDetail.b_unit_factor = sheetRow.b_unit_factor;
                        saleSheetDetail.m_unit_factor = sheetRow.m_unit_factor;
                        //saleSheetDetail.quantity = int.Parse(sheetRow.buy_quantity);
                        //saleSheetDetail.branch_id = this.branch_id;
                        saleSheetDetails.Add(saleSheetDetail);
                }
                    saleSheet.SheetRows = saleSheetDetails;
                }
                else if(sheet.sheetTypeCreate == SHEET_TYPE.SHEET_SALE_DD)
                {
                    saleOrderSheet.sheet_type = SHEET_TYPE.SHEET_SALE_DD;
                    saleOrderSheet.company_id = sheet.company_id;
                    saleOrderSheet.company_name = sheet.company_name;
                    saleOrderSheet.sheet_name = "销售订单";
                    saleOrderSheet.supcust_id = sheet.client_id;
                    saleOrderSheet.sup_name = "";//这个需要补充
                                            //saleSheet.SheetRows=sheet.SheetRows;
                    saleOrderSheet.maker_id = sheet.maker_id;
                    saleOrderSheet.make_time = timestamp;
                    saleOrderSheet.sheet_no = "";//这个需要补充
                    saleOrderSheet.sheet_id = "";
                    saleOrderSheet.OperID = sheet.OperID;
                    saleOrderSheet.OperKey = sheet.OperKey;
                    saleOrderSheet.make_brief = sheet.make_brief;
                    saleOrderSheet.branch_id = sheet.branch_id_forsale;

                    List<SheetRowSaleOrder> saleOrderSheetDetails = new List<SheetRowSaleOrder>();
                    foreach (SheetRowStoreStock sheetRow in sheet.SheetRows)
                    {
                        SheetRowSaleOrder saleOrderSheetDetail = new SheetRowSaleOrder();
                        saleOrderSheetDetail.item_id = sheetRow.item_id;
                        saleOrderSheetDetail.item_name = sheetRow.item_name;
                        saleOrderSheetDetail.sheet_id = "";
                        saleOrderSheetDetail.row_index = sheetRow.row_index;
                        //saleOrderSheetDetail.owner_id = "1";
                        saleOrderSheetDetail.batch_id = sheetRow.batch_id;
                        saleOrderSheetDetail.unit_no = sheetRow.s_unit_no;
                        saleOrderSheetDetail.remark = sheetRow.remark;
                        saleOrderSheetDetail.s_unit_no = sheetRow.s_unit_no;
                        saleOrderSheetDetail.b_unit_no = sheetRow.b_unit_no;
                        saleOrderSheetDetail.m_unit_no = sheetRow.m_unit_no;
                        saleOrderSheetDetail.quantity = int.Parse(sheetRow.buy_quantity);
                        saleOrderSheetDetail.unit_factor = sheetRow.unit_factor;
                        saleOrderSheetDetail.b_unit_factor = sheetRow.b_unit_factor;
                        saleOrderSheetDetail.m_unit_factor = sheetRow.m_unit_factor;
                        //saleOrderSheetDetail.quantity = int.Parse(sheetRow.buy_quantity);
                        //saleOrderSheetDetail.branch_id = this.branch_id;
                        saleOrderSheetDetails.Add(saleOrderSheetDetail);
                    }
                    saleOrderSheet.SheetRows = saleOrderSheetDetails;

                }
            }
            catch (Exception e)
            {
                msg = e.Message;
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error("in AppSheetSave.Save:" + msg);
                MyLogger.LogMsg("in AppSheetSave.Submit:" + msg + sSheet, Token.CompanyID);
                msg = "提交失败,请联系技术支持";
                return new JsonResult(new { result = "Error", msg });
            }
            if (msg == "")
            {
                cmd.ActiveDatabase = "";
                if (sheet.sheetTypeCreate == SHEET_TYPE.SHEET_SALE)
                {
                    saleSheet.Init();
                    msg = await saleSheet.Save(cmd);
                }
                if (sheet.sheetTypeCreate == SHEET_TYPE.SHEET_SALE_DD)
                {
                    saleOrderSheet.Init();
                    msg = await saleOrderSheet.Save(cmd);
                }
            }
            result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.make_time, sheet.happen_time, currentTime });
        }
        /// <summary>
        /// 审核
        /// </summary>
        /// <param name="dSheet"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Submit([FromBody] dynamic dSheet)
        {
            SheetStoreStock sheet = null;
            string sSheet = Newtonsoft.Json.JsonConvert.SerializeObject(dSheet);
            var currentTime = DateTime.Now.ToText();
            string result;
            string msg = "";

            // 获取当前的UTC时间
            DateTimeOffset utcNow = DateTimeOffset.UtcNow;
            // 将UTC时间转换为北京时间（北京时间比UTC时间快8小时）
            DateTimeOffset beijingTime = utcNow.AddHours(8);
            string timestamp = ((DateTimeOffset)beijingTime).ToUniversalTime().ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
            DateTime currentDate = DateTime.Now;
            try
            {
                sheet = Newtonsoft.Json.JsonConvert.DeserializeObject<SheetStoreStock>(sSheet);

                
            }
            catch (Exception e)
            {
                msg = e.Message;
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error("in AppSheetStoreStock.Submit:" + msg + " sheet:" + sSheet);
                MyLogger.LogMsg("in AppSheetSave.Submit:" + msg + sSheet, Token.CompanyID);
                msg = "提交失败,请联系技术支持";
                return new JsonResult(new { result = "Error", msg });
            }
            sheet.Init();
            msg = await sheet.SaveAndApprove(cmd);
            result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no,sheet.approve_time, currentTime });
        }

        
        /// <summary>
        /// 红冲
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="sheetID"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Red([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            string sheetID = data.sheetID;
            string result = "OK"; string msg = null;
            try
            {
                var currentTime = DateTime.Now.ToText();
                Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
                SheetStoreStock sheet = new SheetStoreStock(LOAD_PURPOSE.SHOW);
                msg = await sheet.Red(cmd, companyID, sheetID, operID,"");
                if (msg != "") result = "Error";
                return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time, currentTime });

            }
            catch (Exception e)
            {
                result = "Error";
                msg = e.Message;
                return new JsonResult(new { result, msg });
            }
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Delete([FromBody] dynamic data)
        {
            string sheet_id = data.sheet_id;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetStoreStock sheet = new SheetStoreStock(LOAD_PURPOSE.SHOW);
            string msg = await sheet.Delete(cmd, companyID, sheet_id, operID);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return Json(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time });
        }

        /// <summary>  下方注释为下面注释接口注释，不一定参考意义
        /// 商品档案列表----返回商品详情{bstock--大单位库存，bunit--大单位名称，bfactor--大单位换算，bpprice--大单位批发价，blprice--大单位零售价 }，总条数
        /// </summary>
        /// <param name="operKey">Aa18nTx5omI=</param>
        /// <param name="searchStr">商品名，助记码，商品编号，商品条码 模糊查询</param>
        /// <param name="brandID">品牌ID查询</param>
        /// <param name="classID">分类ID查询</param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <param name="branchID">仓库名 （1）</param>
        /// <returns>商品详情{bstock--大单位库存，bunit--大单位名称，bfactor--大单位换算，bpprice--大单位批发价，blprice--大单位零售价 }，总条数</returns>
        [HttpGet]
        public async Task<JsonResult> GetItemList(string operKey,string sortType, string searchStr, string brandIDs, string classID, int pageSize, int startRow, string branchID, bool showStockOnly,string oftenMonths, string supcustID)
        { 
            bool firstRequest = false;
            string oftenSql = "";
            string orderCondi = "";
            string ORDER_BY = "order by";
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string condi = $"  (ip.status is null or ip.status='1') and son_mum_item is NULL ";
            string condi2 = "";

            if (searchStr != null)
            {
                string b = "%";
                if (searchStr.Length >= 6)
                {
                    b = "";
                }
                string flexStr = CPubVars.GetFlexLikeStr(searchStr);
                condi2 += $" where (ip.item_name ilike '%{flexStr}%' or ip.py_str ilike '%{searchStr}%' or ip.py_str1 ilike '%{searchStr}%' or ip.item_no ilike '%{searchStr}%'  or ip.mum_attributes::text ilike '%{searchStr}%' or id.s_barcode like '%{searchStr}{b}' or  id.b_barcode like '%{searchStr}{b}' or id.m_barcode like '%{searchStr}{b}')";
            }
            if (brandIDs != null && brandIDs != "") condi += $"and (ip.item_brand is null OR ip.item_brand in ({brandIDs})) ";
            if (classID != null && classID != "-1" && classID != "0") condi += $" and ip.other_class like '%/{classID}/%' ";
            //if (showStockOnly) condi += $" and stock_qty > 0 ";
            string showStockOnlyCondi = "";
            if (showStockOnly) showStockOnlyCondi += $" and stock_qty>0 ";
            if (startRow == 0) firstRequest = true;
            string sortSQL = "";
            if (sortType == "item_name")
            {
                sortSQL = "py_str asc";
            }
            else if (sortType == "order_index")
            {
                sortSQL = " item_order_index asc";
            }
            else
            {
                sortSQL = "item_order_index,item_id desc";
            }
            //classID=="-1"代表常用
            if (classID == "-1")
            {
                if (!oftenMonths.IsValid()) oftenMonths = "3";
                if (Convert.ToInt32(oftenMonths) > 48) oftenMonths = "48";

                oftenSql = $@"
left join 
( 
    select s.item_id,true as is_often ,count(*) ct,max(happen_time) recent_sale_time
	from
	(
		select item_id, sd.happen_time from sheet_sale_detail sd 
		left join sheet_sale_main sm on sd.sheet_id = sm.sheet_id and sm.company_id = {companyID}
		where sd.company_id = {companyID}  and sm.supcust_id = {supcustID} 
            and sd.happen_time >= (now()-interval'{oftenMonths} months')
            and sm.happen_time >= (now()-interval'{oftenMonths} months')
            and sm.approve_time is not null
	) s group by s.item_id,is_often order by ct desc limit 200 
) often on often.item_id = ip.item_id";
                condi += $" and is_often";
            }
            SQLQueue QQ = new SQLQueue(cmd);
            // 2025.03.19 dyj：
            // 新增返回结果：
            // （1）计算实际销售出去的量（sum_actual_sale_qty）：所有 buy_quantity 之和 + 最早 happen_time 的 client_stock_quantity - （最晚 happen_time 的 buy_quantity 和 client_stock_quantity）  
            // （2）计算日均动销（daily_avg_sale_qty）：sum_actual_sale_qty除 最早库存上报记录和最晚库存上报记录的day差，如果相差天数<1，daily_avg_sale_qty为0，可能要确认下怎么处理
            // 备注：目前的unit_conv处理为中单位用了floor取整数 形如“1箱=4提=50瓶” 和电脑端是一样的
            string sql_noLimit = @$"

WITH start_end_stock AS 
(
     SELECT item_id,company_id,
                                -- 计算实际销售出去的量：所有 buy_quantity 之和 + 最早 happen_time 的 client_stock_quantity - （最晚 happen_time 的 buy_quantity 和 client_stock_quantity）
                                -- 和泓宁讨论确认过
         (SELECT
             (
               SELECT SUM(buy_quantity)
               FROM sheet_client_stock_detail scsd
                   JOIN sheet_client_stock_main scsm ON scsd.sheet_id = scsm.sheet_id
                                         WHERE scsd.item_id = ip.item_id
                                           AND scsd.company_id = ip.company_id
                                           AND scsm.client_id = {supcustID}) +
                                        (SELECT client_stock_quantity
                                         FROM sheet_client_stock_detail scsd
                                                  JOIN sheet_client_stock_main scsm ON scsd.sheet_id = scsm.sheet_id
                                         WHERE scsd.item_id = ip.item_id
                                           AND scsd.company_id = ip.company_id
                                           AND scsm.client_id = {supcustID}
                                         ORDER BY scsd.happen_time ASC
                                         LIMIT 1) -
                                        (SELECT buy_quantity + client_stock_quantity
                                         FROM sheet_client_stock_detail scsd
                                                  JOIN sheet_client_stock_main scsm ON scsd.sheet_id = scsm.sheet_id
                                         WHERE scsd.item_id = ip.item_id
                                           AND scsd.company_id = ip.company_id
                                           AND scsm.client_id = {supcustID}
                                         ORDER BY scsd.happen_time DESC
                                         LIMIT 1))             AS sum_actual_sale_qty,

                                -- 获取最早和最晚 happen_time 之间的日期差
                                (SELECT MAX(scsd.happen_time)::DATE - MIN(scsd.happen_time)::DATE
                                 FROM sheet_client_stock_detail scsd
                                          JOIN sheet_client_stock_main scsm ON scsd.sheet_id = scsm.sheet_id
                                 WHERE scsd.item_id = ip.item_id
                                   AND scsd.company_id = ip.company_id
                                   AND scsm.client_id = {supcustID}) AS date_diff
                  FROM info_item_prop ip 
                  where ip.company_id = {companyID} and {condi}), 
                        item_details AS (SELECT ip.item_id,
                             ip.item_name,
                             mum_attributes,
                             ip.item_order_index,
                             ip.wholesale_price,
                             ip.cost_price_avg,
                             ip.cost_price_spec,
                             ip.item_images,
                             ip.batch_level,
                             (t.s ->> 'f1') AS s_unit_factor,
                             (t.s ->> 'f2') AS s_unit_no,
                             t.s ->> 'f3'   AS s_barcode,
                             (t.s ->> 'f4')     AS s_wholesale_price,
                             t.s ->> 'f6'   AS s_buy_price,
                             (t.b ->> 'f1') AS b_unit_factor,
                             (t.b ->> 'f2') AS b_unit_no,
                             t.b ->> 'f3'   AS b_barcode,
                             (t.b ->> 'f4')     AS b_wholesale_price,
                             t.b ->> 'f6'   AS b_buy_price,
                             (t.m ->> 'f1') AS m_unit_factor,
                             (t.m ->> 'f2') AS m_unit_no,
                             t.m ->> 'f3'   AS m_barcode
                      FROM info_item_prop AS ip
                               LEFT JOIN (SELECT item_id, s, m, b
                                          FROM crosstab('SELECT item_id, unit_type, row_to_json(row(unit_factor, unit_no, barcode, wholesale_price, retail_price, buy_price)) AS json
                      FROM info_item_multi_unit WHERE company_id = {companyID} ORDER BY item_id',
                                                        $$values('s'::text),('m'::text),('b'::text)$$)
                                                   AS errr(item_id INT, s JSONB, m JSONB, b JSONB)) t
                                         ON ip.item_id = t.item_id
                               LEFT JOIN (SELECT * FROM info_item_class WHERE company_id = {companyID}) AS ic
                                         ON ip.item_class = ic.class_id
                      WHERE ip.company_id = {companyID}
                        AND (ip.status IS NULL OR ip.status = '1')
                        AND son_mum_item IS NULL)
SELECT id.item_id,
       id.item_name,
       id.mum_attributes,
       id.item_order_index,
       id.wholesale_price,
       id.cost_price_avg,
       id.cost_price_spec,
       id.item_images,
       id.batch_level,
       id.s_unit_factor,
       id.s_unit_no,
       id.s_barcode,
       id.s_wholesale_price,
       id.s_buy_price,
       id.b_unit_factor,
       id.b_unit_no,
       id.b_barcode,
       id.b_wholesale_price,
       id.b_buy_price,
       id.m_unit_factor,
       id.m_unit_no,
       id.m_barcode,
       (case  when m_unit_factor is null and b_unit_factor is not null then concat(s_unit_factor, b_unit_no, '=', b_unit_factor, s_unit_no) when b_unit_factor is not null and m_unit_factor is not null then
        concat(s_unit_factor, b_unit_no, '=', floor(b_unit_factor::numeric/m_unit_factor::numeric), m_unit_no, '=', b_unit_factor, s_unit_no) when b_unit_factor is null then concat(s_unit_factor, s_unit_no) end) as unit_conv,
       ss.sum_actual_sale_qty,
       -- 计算日均动销
       CASE
           WHEN ss.date_diff = 0 THEN 0
           ELSE sum_actual_sale_qty / ss.date_diff
           END AS daily_avg_sale_qty
FROM item_details AS id
         JOIN start_end_stock AS ss ON id.item_id = ss.item_id
LEFT JOIN info_item_prop AS ip ON id.item_id = ip.item_id and ip.company_id={companyID}
 {condi2}";
            
            var sql = sql_noLimit + $" order by {sortSQL} limit {pageSize} offset {startRow}";
            QQ.Enqueue("data", sql);
            sql = $"select count(*) as itemCount from ({sql}) tt";
            QQ.Enqueue("count", sql);
            List<ExpandoObject> data = null;
            var dr = await QQ.ExecuteReaderAsync();
            var itemCount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count"&& firstRequest)
                {
                    dr.Read();
                    itemCount = CPubVars.GetTextFromDr(dr, "itemCount");
                }
            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, itemCount });
        }
    }
}

