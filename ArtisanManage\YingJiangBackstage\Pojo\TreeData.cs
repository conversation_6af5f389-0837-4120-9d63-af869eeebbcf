﻿using System.Collections.Generic;
using ArtisanManage.Services;

namespace ArtisanManage.YingJiangBackstage.Pojo
{
    /// <summary>
    /// 后台管理系统树形处理
    /// </summary>
    public class TreeData: ITreeData<TreeData>
    {
        public string Key { get; set; }     // Id
        public int Value { get; set; }   // Id 
        
        public string Label { get; set; }   // name
        public int ParentId { get; set; }
        
        public int depart_supcust_id { get; set; }
        
        public string department_path { get; set; }

        public List<TreeData> Children { get; set; }
    }
    public class TreeData_Common : ITreeData_Common<TreeData_Common>
    {
        public string Key { get; set; }
        public int Value { get; set; }

        public string Label { get; set; }
        public int ParentId { get; set; }

        public List<TreeData_Common> Children { get; set; }
    }
    public class ModuleWorkOrderTree: ITreeData<ModuleWorkOrderTree>
    {
        public string Key { get; set; }     // Id
        public int Value { get; set; }   // Id 

        public string Label { get; set; }   // name
        public int ParentId { get; set; }

        public int depart_supcust_id { get; set; }

        public string department_path { get; set; }
        public dynamic workOrderArray { get; set; }
        public List<ModuleWorkOrderTree> Children { get; set; }
    }
    public class TreeDataOther: ITreeData<TreeDataOther>
    {
        public string Key { get; set; }     // Id
        public int Value { get; set; }   // Id 
        public string Label { get; set; }   // name

        public int depart_supcust_id { get; set; }
        public int ParentId { get; set; } 
        public List<TreeDataOther> Children { get; set; }
        public List<dynamic> SelectSon { get; set; }
    }
    public interface ITreeData<T>
    {
        public string Key { get; set; }
        public int Value { get; set; }
        public string Label { get; set; }
        public int depart_supcust_id { get; set; }

        public int ParentId { get; set; }
        public List<T> Children { get; set; }
    }
    public interface ITreeData_Common<T>
    {
        public string Key { get; set; }
        public int Value { get; set; }
        public string Label { get; set; }

        public int ParentId { get; set; }
        public List<T> Children { get; set; }
    }


}