[
  {
    "Id": 1,
    "Class": "mainMenu",
    "Title": "销售",
    "Url": "/images/images.svg#sale",
    "SubNodes": {
      "基础单据": [
        {
          "Id": "sheetSaleOrder",
          "Class": "menu",
          "Title": "销售订单",
          "Url": "Sheets/SaleOrderSheet",
          "ViewUrl": "Sheets/SaleOrderSheetView?forReturn=false",
          "View": true
        },
        {
          "Id": "sheetReturnOrder",
          "Class": "menu",
          "Title": "退货订单",
          "Url": "Sheets/SaleOrderSheet?forReturn=true",
          "ViewUrl": "Sheets/SaleOrderSheetView?forReturn=true&sheet_type=TD",
          "View": true
        },
        {
          "Id": "sheetSale",
          "Class": "menu",
          "Title": "销售单",
          "Url": "Sheets/SaleSheet?forReturn=false",
          "ViewUrl": "Sheets/SaleSheetView?forReturn=false",
          "View": true
        },
        {
          "Id": "sheetReturn",
          "Class": "menu",
          "Title": "退货单",
          "Url": "Sheets/SaleSheet?forReturn=true",
          "ViewUrl": "Sheets/SaleSheetView?forReturn=true&sheet_type=T",
          "View": true
        },
        {
          "Id": "sheetBorrowItem",
          "Class": "menu",
          "Title": "借货单",
          "Url": "Sheets/BorrowItemSheet?forReturn=false",
          "ViewUrl": "Sheets/BorrowItemSheetView?forReturn=false&sheet_type=JH",
          "View": true
        },
        {
          "Id": "sheetReturnItem",
          "Class": "menu",
          "Title": "还货单",
          "Url": "Sheets/BorrowItemSheet?forReturn=true",
          "ViewUrl": "Sheets/BorrowItemSheetView?forReturn=true&sheet_type=HH",
          "View": true
        },
    
        {
          "Id": "sheetPriceAdjust",
          "Class": "menu",
          "Title": "调价单",
          "Url": "Sheets/PriceAdjustSheet",
          "View": true,
          "ViewUrl": "Sheets/PriceAdjustSheetView"
        },
        {
          "Id": "sheetPlaceholderOrder",
          "Class": "menu",
          "Title": "占位订单",
          "Url": "Sheets/PlaceholderOrderSheet",
          "ViewUrl": "Sheets/PlaceholderOrderSheetView?forModify=false",
          "View": true
        },
        {
          "Id": "sheetSaleFeeApportion",
          "Class": "menu",
          "Title": "销售费用分摊单",
          "Url": "Sheets/SaleFeeApportionSheet",
          "ViewUrl": "Sheets/SaleFeeApportionSheetView",
          "View": true
        }
      ],
      "业务流程": [
        {
          "Id": "sheetCheckSheets",
          "Class": "menu",
          "Title": "交账单",
          "Url": "WorkFlow/CheckAccount/CheckSheetsSheet",
          "ViewUrl": "WorkFlow/CheckAccount/CheckSheetsSheetHistory",
          "View": true
        },
        {
          "Id": "sheetCheckSheetsNew",
          "Class": "menu",
          "Title": "交账单(内测)",
          "Url": "Sheets/CheckAccountNew",
          "ViewUrl": "Sheets/CheckAccountNewHistory",
          "View": true
        },
        {
          "Id": "orderItemSheet",
          "Class": "menu",
          "Title": "定货会",
          "Url": "Sheets/OrderItemSheet",
          "ViewUrl": "Sheets/OrderItemSheetView?forReturn=false",
          "View": true
        },
        {
          "Id": "orderItemAdjustSheet",
          "Class": "menu",
          "Title": "定货会调整单",
          "Url": "Sheets/OrderItemAdjustSheet",
          "ViewUrl": "Sheets/OrderItemAdjustSheetView?forReturn=false",
          "View": true
        },
        {
          "Id": "sheetDisplayAgreement",
          "Class": "menu",
          "Title": "陈列协议",
          "Url": "Sheets/DisplayAgreementSheet",
          "View": true,
          "ViewUrl": "Sheets/DisplayAgreementSheetView"
        },
        {
          "Id": "promotionView",
          "Class": "menu",
          "Title": "促销活动",
          "Url": "BaseInfo/PromotionView"
        },
        {
          "Id": "redPacket",
          "Class": "menu",
          "Title": "红包",
          "Url": "BaseInfo/RedPacket"
        }

      ],
      "访销流程": [
        {
          "Id": "orderPrint",
          "Class": "menu",
          "Title": "打单",
          "Url": "WorkFlow/OrderManagePrint",
          "ViewUrl": "",
          "View": false
        },
        {
          "Id": "orderReview",
          "Class": "menu",
          "Title": "复核",
          "Url": "WorkFlow/OrderManageReview",
          "ViewUrl": "",
          "View": false
        },
        {
          "Id": "assignVan",
          "Class": "menu",
          "Title": "装车",
          "Url": "WorkFlow/OrderManageAssignVan",
          "ViewUrl": "",
          "View": false
        },
        {
          "Id": "deliverItems",
          "Class": "menu",
          "Title": "发货",
          "Url": "WorkFlow/OrderManageDelivery",
          "ViewUrl": "",
          "View": false
        },
        {
          "Id": "orderToSale",
          "Class": "menu",
          "Title": "转单",
          "Url": "WorkFlow/OrderManageOrderToSale",
          "ViewUrl": "",
          "View": false
        },
        {
          "Id": "backBranch",
          "Class": "menu",
          "Title": "回库",
          "Url": "WorkFlow/OrderManageBackBranch",
          "ViewUrl": "",
          "View": false
        }

      ],
      "基本报表": [
        {
          "Id": "salesSummaryBySeller",
          "Class": "menu",
          "Title": "销售汇总(业务员)",
          "Url": "Report/SalesSummaryBySeller?sheetType=x",
          "OrderTable": true,
          "OrderUrl": "Report/SalesSummaryBySeller?sheetType=xd"

        },
        {
          "Id": "salesSummaryByItem",
          "Class": "menu",
          "Title": "销售汇总(商品)",
          "Url": "Report/SalesSummaryByItem?sheetType=x",
          "OrderTable": true,
          "OrderUrl": "Report/SalesSummaryByItem?sheetType=xd"

        },
        {
          "Id": "salesSummaryByClient",
          "Class": "menu",
          "Title": "销售汇总(客户)",
          "Url": "Report/SalesSummaryByClient?sheetType=x",
          "OrderTable": true,
          "OrderUrl": "Report/SalesSummaryByClient?sheetType=xd"

        },
        {
          "Id": "salesSummaryByClientAndItem",
          "Class": "menu",
          "Title": "销售汇总(客/品)",
          "Url": "Report/SalesSummaryByClientAndItem?sheetType=x",
          "OrderTable": true,
          "OrderUrl": "Report/SalesSummaryByClientAndItem?sheetType=xd"

        },
        {
          "Id": "salesSummaryByBrand",
          "Class": "menu",
          "Title": "销售汇总(品牌)",
          "Url": "Report/SalesSummaryByBrand?sheetType=x",
          "OrderTable": true,
          "OrderUrl": "Report/SalesSummaryByBrand?sheetType=xd"

        },
        {
          "Id": "salesSummaryBySender",
          "Class": "menu",
          "Title": "销售汇总(送货员)",
          "Url": "Report/SalesSummaryBySender?sheetType=x",
          "OrderTable": true,
          "OrderUrl": "Report/SalesSummaryBySender?sheetType=xd"

        },
        {
          "Id": "salesSummaryByGroup",
          "Class": "menu",
          "Title": "销售汇总(渠道)",
          "Url": "Report/SalesSummaryByGroup?sheetType=x",
          "OrderTable": true,
          "OrderUrl": "Report/SalesSummaryByGroup?sheetType=xd"

        },
        {
          "Id": "salesTrend",
          "Class": "menu",
          "Title": "销量走势图",
          "Url": "Report/SalesTrend"
        },
        {
          "Id": "salesDetaill",
          "Class": "menu",
          "Title": "销售明细表",
          "Url": "Report/SalesDetail?sheetType=x",
          "OrderTable": true,
          "OrderUrl": "Report/SalesDetail?sheetType=xd"
        },
        {
          "Id": "checkSheetBySeller",
          "Class": "menu",
          "Title": "交账汇总",
          "Url": "Report/CheckSheetBySeller",
          "OrderUrl": "Report/CheckSheetBySeller"

        },
        {
          "Id": "orderToSale",
          "Class": "menu",
          "Title": "配送差异一览表",
          "Url": "Report/OrderToSaleDifferencel",
          "OrderUrl": "Report/OrderToSaleDifferencel"

        }

      ],
      "业务流程报表": [
        {
          "Id": "borrowedView",
          "Class": "menu",
          "Title": "借还货汇总",
          "Url": "Report/BorrowedView"
        },
        {
          "Id": "borrowItem",
          "Class": "menu",
          "Title": "借还货明细表",
          "Url": "Report/BorrowItem"
        },
        {
          "Id": "borrowBalance",
          "Class": "menu",
          "Title": "客户借货余额",
          "Url": "Report/BorrowBalance"
        },
        {
          "Id": "borrowHistory",
          "Class": "menu",
          "Title": "客户借货历史",
          "Url": "Report/BorrowHistory"
        },
        {
          "Id": "borrowMigration",
          "Class": "menu",
          "Title": "借货数据迁移助手",
          "Url": "Report/BorrowMigration"
        },

        //{
        //  "Id": 517,
        //  "Class": "menu",
        //  "Title": "定货款余额(商品)",
        //  "Url": "Report/ItemsOrderedList"
        //},
        {
          "Id": "itemsOrderedSummary",
          "Class": "menu",
          "Title": "定货汇总",
          "Url": "Report/ItemsOrderedSummary"
        },
        {
          "Id": "itemsOrderedSummaryByItem",
          "Class": "menu",
          "Title": "定货变化明细表",
          "Url": "Report/ItemsOrderedSummaryByItem"
        },
        {
          "Id": "itemsOrderedAdjust",
          "Class": "menu",
          "Title": "定货调整记录",
          "Url": "Report/ItemsOrderedAdjust"
        },
        {
          "Id": "displayAgreementDetail",
          "Class": "menu",
          "Title": "陈列协议明细",
          "Url": "Report/DisplayAgreementDetail"
        },
        {
          "Id": "displayMoneyDetail",
          "Class": "menu",
          "Title": "陈列协议变化(金额)",
          "Url": "Report/DisplayMoneyDetail"
        },
        {
          "Id": "displaySheetBrowser",
          "Class": "menu",
          "Title": "陈列协议全览",
          "Url": "Report/DisplaySheetBrowser"
        },
        {
          "Id": "AssignSummaryByItem",
          "Class": "menu",
          "Title": "装车汇总(商品)",
          "Url": "Report/AssignSummaryByItem"
        }

      ]
    }
  },
  {
    "Id": 3,
    "Class": "mainMenu",
    "Title": "采购",
    "Url": "/images/images.svg#buy",
    "SubNodes": {
      "采购单据": [
        {
          "Id": "sheetBuy",
          "Class": "menu",
          "Title": "采购单",
          "Url": "Sheets/BuySheet?forReturn=false",
          "View": true,
          "ViewUrl": "Sheets/BuySheetView?forReturn=false&sheet_type=CG"
        },
        {
          "Id": "sheetBuyReturn",
          "Class": "menu",
          "Title": "采购退货单",
          "Url": "Sheets/BuySheet?forReturn=true",
          "View": true,
          "ViewUrl": "Sheets/BuySheetView?forReturn=true&sheet_type=CT"
        },
        {
          "Id": "sheetBuyOrder",
          "Class": "menu",
          "Title": "采购订单",
          "Url": "Sheets/BuyOrderSheet?forReturn=false",
          "View": true,
          "ViewUrl": "Sheets/BuyOrderSheetView?forReturn=false&sheet_type=CD"
        }
      ],
      "采购报表": [
        {
          "Id": "buysSummaryByItem",
          "Class": "menu",
          "Title": "采购汇总(商品)",
          "Url": "Report/BuysSummaryByItem"
        },
        {
          "Id": "buysDetail",
          "Class": "menu",
          "Title": "采购明细",
          "Url": "Report/BuysDetail"
        }
      ]

     
    }
  },
  {
    "Id": 4,
    "Class": "mainMenu",
    "Title": "仓库",
    "Url": "/images/images.svg#stock",
    "SubNodes": {
      "仓库单据": [
        {
          "Id": "sheetMove",
          "Class": "menu",
          "Title": "调拨单",
          "Url": "Sheets/MoveSheet",
          "View": true,
          "ViewUrl": "Sheets/MoveSheetView",
          "SaveDb": false
        },
        {
          "Id": "sheetInventChange",
          "Class": "menu",
          "Title": "盘点盈亏单",
          "Url": "Sheets/InventChangeSheet?forReduce=false",
          "View": true,
          "ViewUrl": "Sheets/InventChangeSheetView?forReduce=false"
        },
        {
          "Id": "sheetInventReduce",
          "Class": "menu",
          "Title": "报损单",
          "Url": "Sheets/InventChangeSheet?forReduce=true",
          "View": true,
          "ViewUrl": "Sheets/InventChangeSheetView?forReduce=true"
        },
        {
          "Id": "sheetInvent",
          "Class": "menu",
          "Title": "盘点单",
          "Url": "Sheets/InventorySheet",
          "View": true,
          "ViewUrl": "Sheets/InventorySheetView"
        },
        {
          "Id": 304,
          "Class": "menu",
          "Title": "组装单",
          "Url": "Sheets/CombineSheet",
          "View": true,
          "ViewUrl": "Sheets/CombineSheetView?forSplit=false&sheet_type=ZZ"
        },
        {
          "Id": 305,
          "Class": "menu",
          "Title": "拆分单",
          "Url": "Sheets/CombineSheet?forSplit=true",
          "View": true,
          "ViewUrl": "Sheets/CombineSheetView?forSplit=true&sheet_type=CF"
        },
        {
          "Id": "sheetCostPriceAdjust",
          "Class": "menu",
          "Title": "成本调价单",
          "Url": "Sheets/CostPriceAdjustSheet",
          "View": true,
          "ViewUrl": "Sheets/CostPriceAdjustSheetView"
        }
      ],
      "仓库报表": [
        {
          "Id": "viewStock",
          "Class": "menu",
          "Title": "库存表",
          "Url": "Report/StocksView",
          "View": false
        },
        {
          "Id": "stockChangeSum",
          "Class": "menu",
          "Title": "库存变化表",
          "Url": "Report/StockChangeSum",
          "View": false
        },
        {
          "Id": "stocksChangeByOrder",
          "Class": "menu",
          "Title": "库存变化明细表",
          "Url": "Report/StocksChangeByOrder",
          "View": false
        },
        {
          "Id": "moveSummary",
          "Class": "menu",
          "Title": "调拨汇总",
          "Url": "Report/MoveSummaryByItem"
        },
        {
          "Id": "movesDetail",
          "Class": "menu",
          "Title": "调拨明细",
          "Url": "Report/MovesDetail"
        },
        {
          "Id": "inventorySummary",
          "Class": "menu",
          "Title": "盘点汇总",
          "Url": "Report/InventorySummary"
        },
        {
          "Id": "inventoryDetail",
          "Class": "menu",
          "Title": "盘点明细",
          "Url": "Report/InventoryDetail"
        },
        {
          "Id": "stockAlert",
          "Class": "menu",
          "Title": "库存预警表",
          "Url": "Report/StockAlertDetail?forSetting=false"
        },
        {
          "Id": "contractProfitCompany",
          "Class": "menu",
          "Title": "承包利润表(公司)",
          "Url": "Report/MoveContractorProfit"
        },
        {
          "Id": "contractProfitSeller",
          "Class": "menu",
          "Title": "承包利润表(业务员)",
          "Url": "Report/SaleContractorProfit"
        }
      ]
    }
  },
  {
    "Id": 5,
    "Class": "mainMenu",
    "Title": "资金",
    "Url": "/images/images.svg#money",
    "SubNodes": {
      "资金单据": [
        {
          "Id": "sheetGetArrears",
          "Class": "menu",
          "Title": "收款单",
          "Url": "Sheets/GetArrearsSheet?forPayOrGet=false",
          "View": true,
          "ViewUrl": "Sheets/GetArrearsSheetView?forPayOrGet=false&sheet_type=SK",
          "Display": null,
          "SubNodes": null
        },
        {
          "Id": "sheetPayArrears",
          "Class": "menu",
          "Title": "付款单",
          "Url": "Sheets/GetArrearsSheet?forPayOrGet=true",
          "View": true,
          "ViewUrl": "Sheets/GetArrearsSheetView?forPayOrGet=true&sheet_type=FK",
          "Display": null,
          "SubNodes": null
        },
        {
          "Id": "sheetPreget",
          "Class": "menu",
          "Title": "预收款单",
          "Url": "Sheets/PrepaySheet?forPayOrGet=false",
          "View": true,
          "ViewUrl": "Sheets/PrepaySheetView_new?forPayOrGet=false",
          "Display": null,
          "SubNodes": null
        },
        {
          "Id": "sheetPrepay",
          "Class": "menu",
          "Title": "预付款单",
          "Url": "Sheets/PrepaySheet?forPayOrGet=true",
          "View": true,
          "ViewUrl": "Sheets/PrepaySheetView?forPayOrGet=true&sheet_type=YF",
          "Display": null,
          "SubNodes": null
        },
        {
          "Id": "sheetLoan",
          "Class": "menu",
          "Title": "贷款单",
          "Url": "Sheets/LoanSheet?forPayOrGet=false",
          "View": true,
          "ViewUrl": "Sheets/LoanSheetView?forPayOrGet=false",
          "Display": null,
          "SubNodes": null
        },
        {
          "Id": "sheetRepay",
          "Class": "menu",
          "Title": "还贷款单",
          "Url": "CwPages/RepaySheet",
          "View": true,
          "ViewUrl": "Sheets/RepaySheetView_new?forPayOrGet=true",
          "Display": null,
          "SubNodes": null
        },
        {
          "Id": "sheetFeeOut",
          "Class": "menu",
          "Title": "费用支出单",
          "Url": "Sheets/FeeOutSheet?forOutOrIn=true",
          "View": true,
          "ViewUrl": "Sheets/FeeOutSheetView?forOutOrIn=true&forSelect=0&sheet_type=ZC",
          "Display": null,
          "SubNodes": null
        },
        {
          "Id": "sheetFeeIn",
          "Class": "menu",
          "Title": "其他收入单",
          "Url": "Sheets/FeeOutSheet?forOutOrIn=false",
          "View": true,
          "ViewUrl": "Sheets/FeeOutSheetView?forOutOrIn=false&forSelect=0&sheet_type=SR",
          "Display": null,
          "SubNodes": null
        }
      ],
      "资金报表": [
        {
          "Id": "arrearsBlance",
          "Class": "menu",
          "Title": "应收款",
          "Url": "Report/ClientArrears"
        },
        {
          "Id": "arrearsBlanceDetail",
          "Class": "menu",
          "Title": "应收款明细",
          "Url": "Report/ClientArrearsDetail"
        },
        {
          "Id": "pregetBalance",
          "Class": "menu",
          "Title": "预收款余额",
          "Url": "Report/PregetBalance"
        },
        {
          "Id": "clientBusinessHistory",
          "Class": "menu",
          "Title": "客户往来账",
          "Url": "Report/AccountHistory"
        },
        {
          "Id": "accountChangeSum",
          "Class": "menu",
          "Title": "客户往来汇总表",
          "Url": "Report/AccountChangeSum"
        },
        {
          "Id": "supplierArrears",
          "Class": "menu",
          "Title": "应付款",
          "Url": "Report/SupplierArrears"
        },
        {
          "Id": "prepayBalance",
          "Class": "menu",
          "Title": "预付款余额",
          "Url": "Report/PrepayBalance"
        },
        {
          "Id": "supplierBusinessHistory",
          "Class": "menu",
          "Title": "供应商往来账",
          "Url": "Report/SupplierAccountHistory"
        },
        {
          "Id": "supplierAccountChangeSum",
          "Class": "menu",
          "Title": "供应商往来汇总表",
          "Url": "Report/SupplierAccountChangeSum"
        }

      ],
        "经营报表": [
            {
                "Id": "businessProfit",
                "Class": "menu",
                "Title": "经营利润表",
                "Url": "Report/BusinessProfit"
            },
            {
                "Id": "feeOutSummary",
                "Class": "menu",
                "Title": "费用合计表",
                "Url": "Report/FeeOutSummary"
            },
            {
                "Id": "cashInOut",
                "Class": "menu",
                "Title": "现金收支表",
                "Url": "Report/CashInOut"
            },
            {
                "Id": "feeOutDetail",
                "Class": "menu",
                "Title": "收入支出明细表",
                "Url": "Report/FeeOutDetail"
            }

        ]

    }
  },
  {
    "Id": 6,
    "Class": "mainMenu",
    "Title": "财务",
    "Url": "/images/images.svg#accounting",
    "SubNodes": {
      "设置": [
        {
          "Id": "openingAccount",
          "Class": "menu",
          "Title": "开账",
          "Url": "CwPages/OpeningAccount"
        },
        {
          "Id": "paywaysView",
          "Class": "menu",
          "Title": "会计科目",
          "Url": "Setting/PaywaysView"
        },
        /*{
          "Id": "assistAccounting",
          "Class": "menu",
          "Title": "辅助核算",
          "Url": "CwPages/assistAccounting"
        },*/
        {
          "Id": "openingBalance",
          "Class": "menu",
          "Title": "科目期初",
          "Url": "CwPages/OpeningBalance",
          "View": false,
          "ViewUrl": "CwPages/OpeningBalance"
        }

      ],
      "凭证": [
        {
          "Id": "sheetVoucher",
          "Class": "menu",
          "Title": "凭证录入",
          "Url": "CwPages/CwVoucher",
          "View": true,
          "ViewUrl": "CwPages/CwVoucherView"
        },
        {
          "Id": "sheetVoucherLists",
          "Class": "menu",
          "Title": "批量生成凭证",
          "Url": "CwPages/CwVoucherLists",
          "View": false,
          "ViewUrl": "CwPages/CwVoucherListsView"
        },
        {
          "Id": "closingCarryForward",
          "Class": "menu",
          "Title": "期末结转",
          "Url": "CwPages/ClosingCarryForward",
          "View": false,
          "ViewUrl": "CwPages/ClosingCarryForward"
        }
      ],
      "财务报表": [
        {
          "Id": "balanceSheet",
          "Class": "menu",
          "Title": "资产负债",
          "Url": "CwPages/Report/Balance",
          "View": false,
          "ViewUrl": "CwPages/Report/Balance"
        },
        {
          "Id": "profitSheet",
          "Class": "menu",
          "Title": "利润表",
          "Url": "CwPages/Report/Profit",
          "View": false,
          "ViewUrl": "CwPages/Report/Balance"
        },
        {
          "Id": "accountBalance",
          "Class": "menu",
          "Title": "科目余额表",
          "Url": "CwPages/Report/SubBalance",
          "View": false,
          "ViewUrl": "CwPages/Report/SubBalance"
        },
        {
          "Id": "detailBill",
          "Class": "menu",
          "Title": "明细账",
          "Url": "CwPages/Report/DetailAccount",
          "View": false,
          "ViewUrl": "CwPages/Report/DetailAccount"
        },
        {
          "Id": "assistBalance",
          "Class": "menu",
          "Title": "核算项目余额表",
          "Url": "CwPages/Report/AssistBalance"
        },
        /*{
          "Id": "assistDetail",
          "Class": "menu",
          "Title": "核算项目明细账",
          "Url": "CwPages/Report/AssistDetail"
        }*/
      ]
    }
  },
  {
    "Id": 6,
    "Class": "mainMenu",
    "Title": "报表",
    "Url": "/images/images.svg#report",
    "SubNodes": {
      "员工报表": [
        {
          "Id": "visitPath",
          "Class": "menu",
          "Title": "外勤轨迹",
          "Url": "Report/VisitPath"
        },
        //{
        //  "Id": 504,
        //  "Class": "menu",
        //  "Title": "排行榜",
        //  "Url": "Report/SellerRank"
        //},
        {
          "Id": "visitRecord",
          "Class": "menu",
          "Title": "拜访记录",
          "Url": "Report/VisitRecord"
        },
        {
          "Id": "visitSummaryBySeller",
          "Class": "menu",
          "Title": "拜访汇总",
          "Url": "Report/VisitSummaryBySeller"
        },



        {
          "Id": "commission",
          "Class": "menu",
          "Title": "提成",
          "Url": "/Report/CommissionSummary"
          //"Url": "/commission/commission"
        },
        {
          "Id": "commission",
          "Class": "menu",
          "Title": "提成明细",
          "Url": "/Report/CommissionDetail"
        },



        {
          "Id": "supcustDistribution",
          "Class": "menu",
          "Title": "客户分布",
          "Url": "Report/SupcustDistributionMap"
        },


        {
          "Id": "AttendanceReport",
          "Class": "menu",
          "Title": "考勤报表",
          "Url": "/Report/AttendanceReportView"
        },
        {
          "Id": "AttendanceReport",
          "Class": "menu",
          "Title": "考勤报表(月)",
          "Url": "/Report/AttendanceMonthReportView"
        }
       

      ],
      "会员积分": [
        {
          "Id": "vipPointsSummary",
          "Class": "menu",
          "Title": "会员积分汇总表",
          "Url": "Report/VipPointsSummary"
        },
        {
          "Id": "vipPointsDetail",
          "Class": "menu",
          "Title": "会员积分明细表",
          "Url": "Report/VipPointsDetail"
        }
      ],
      "其他报表": [
        {
          "Id": "customerLiveness",
          "Class": "menu",
          "Title": "客户活跃度",
          "Url": "Report/CustomerLiveness"
        },
        {
          "Id": "clientValue",
          "Class": "menu",
          "Title": "客户价值分析",
          "Url": "Report/ClientValue"
        },
        {
          "Id": "clientAdd",
          "Class": "menu",
          "Title": "新增客户报表",
          "Url": "Report/ClientAdd"
        },

        {
          "Id": "businessHistoryLists",
          "Class": "menu",
          "Title": "经营历程",
          "Url": "Report/BusinessHistoryLists"
        },
        {
          "Id": "itemsOccupyReport",
          "Class": "menu",
          "Title": "铺市率报表（单品）",
          "Url": "Report/ItemOccupyReport"
        },
        {
          "Id": "brandsOccupyReport",
          "Class": "menu",
          "Title": "铺市率报表（品牌）",
          "Url": "Report/BrandOccupyReport"
        }
      ]      

    }
  },
  {
    "Id": 7,
    "Class": "mainMenu",
    "Title": "档案",
    "Url": "/images/images.svg#info",
    "SubNodes": {
      "商品相关": [
        {
          "Id": "infoItem",
          "Class": "menu",
          "Title": "商品档案",
          "Url": "BaseInfo/ItemsView"
        },
        {
          "Id": "infoUnit",
          "Class": "menu",
          "Title": "计量单位",
          "Url": "BaseInfo/UnitsView"
        },
        {
          "Id": "infoBrand",
          "Class": "menu",
          "Title": "品牌",
          "Url": "BaseInfo/BrandsView"
        },
        {
          "Id": "infoPrice",
          "Class": "menu",
          "Title": "价格方案",
          "Url": "BaseInfo/PriceView"
        },
        {
          "Id": "priceStrategy",
          "Class": "menu",
          "Title": "价格策略",
          "Url": "BaseInfo/PriceStrategy"
        },
        {
          "Id": "preSalePrice",
          "Class": "menu",
          "Title": "上次售价",
          "Url": "BaseInfo/PreSalePrice"
        },
        {
          "Id": 707,
          "Class": "menu",
          "Title": "组装拆分模型",
          "Url": "BaseInfo/CombineTempView"
        }
      ],
      "客户相关": [
        {
          "Id": "infoClient",
          "Class": "menu",
          "Title": "客户档案",
          "Url": "BaseInfo/ClientsView"
        },
        {
          "Id": "infoGroup",
          "Class": "menu",
          "Title": "渠道档案",
          "Url": "BaseInfo/GroupsView"
        },
        {
          "Id": "infoRank",
          "Class": "menu",
          "Title": "客户等级",
          "Url": "BaseInfo/RanksView"
        },
        {
          "Id": "infoSupplier",
          "Class": "menu",
          "Title": "供应商档案",
          "Url": "BaseInfo/SuppliersView"
        },
        {
          "Id": "infoFeeUnit",
          "Class": "menu",
          "Title": "费用单位档案",
          "Url": "BaseInfo/FeeUnitsView"
        },
        {
          "Id": "infoAcctWay",
          "Class": "menu",
          "Title": "结算方式",
          "Url": "BaseInfo/AcctTypeDetailView"//memu中添加支付档案
        }
      ],
      "员工相关": [
        {
          "Id": "infoOperator",
          "Class": "menu",
          "Title": "员工档案",
          "Url": "BaseInfo/OperatorsView"
        },
        {
          "Id": "infoRole",
          "Class": "menu",
          "Title": "员工角色",
          "Url": "BaseInfo/RolesView"
        },
        {
          "Id": "commissionPlan",
          "Class": "menu",
          "Title": "提成方案",
          "Url": "/commission/plan"
        },
        {
          "Id": "commissionStrategiesView",
          "Class": "menu",
          "Title": "提成策略",
          "Url": "BaseInfo/CommissionStrategiesView"
        },
        {
          "Id": "visitSchedule",
          "Class": "menu",
          "Title": "拜访行程",
          "Url": "BaseInfo/VisitDayView"
        },
        {
          "Id": "attendanceSetting",
          "Class": "menu",
          "Title": "考勤设置",
          "Url": "BaseInfo/AttendanceSettingView"
        },
        {
          "Id": "visitStandard",
          "Class": "menu",
          "Title": "拜访规范",
          "Url": "BaseInfo/VisitStandard"
        }
      ],
      "其他": [
        {
          "Id": "infoBranch",
          "Class": "menu",
          "Title": "仓库档案",
          "Url": "BaseInfo/BranchsView"

        },
        {
          "Id": "infoLoanPartner",
          "Class": "menu",
          "Title": "借贷款单位档案",
          "Url": "BaseInfo/LoanPartnersView"

        },
        {
          "Id": "infoBranchPositionType",
          "Class": "menu",
          "Title": "库位类型",
          "Url": "BaseInfo/BranchPositionTypeView"

        },
        {
          "Id": "infoPayQrCode",
          "Class": "menu",
          "Title": "收款二维码",
          "Url": "BaseInfo/PayQrCodeView"
        }
      ]
    }
  },
  {
    "Id": 8,
    "Class": "mainMenu",
    "Title": "设置",
    "Url": "/images/images.svg#setting",
    "SubNodes": {
      "初期录入": [
        {
          "Id": "companySetting",
          "Class": "menu",
          "Title": "公司设置",
          "Url": "Setting/CompanySetting"
        },
        {
          "Id": "infoImport",
          "Class": "menu",
          "Title": "档案导入",
          "Url": "Setting/ImportInfo"
        }
        ,
        {
          "Id": "emartSetting",
          "Class": "menu",
          "Title": "电商对接",
          "Url": "Setting/EmartSetting"
        }

        //{
        //  "Id": 702,
        //  "Class": "menu",
        //  "Title": "公司开户",
        //  "Url": "Setting/OpenAccount"
        //},

      ],
      "其他": [
        {
          "Id": "infoPrintTemplate",
          "Class": "menu",
          "Title": "打印模板",
          "Url": "Setting/PrintTemplateView"
        },
        {
          "Id": "infoReceiptTemplate",
          "Class": "menu",
          "Title": "小票打印模板",
          "Url": "Setting/ReceiptTemplateView"
        },
        {
          "Id": "setCloudPrinter",
          "Class": "menu",
          "Title": "云打印机",
          "Url": "Setting/CloudPrintersView"
        },
        {
          "Id": "paywaysView",
          "Class": "menu",
          "Title": "会计科目",
          "Url": "Setting/PaywaysView"
        },
        {
          "Id": "briefsView",
          "Class": "menu",
          "Title": "备注信息",
          "Url": "Setting/BriefsView"
        }
        //{
        //  "Id": 821,
        //  "Class": "menu",
        //  "Title": "支付方式",
        //  "Url": "Setting/PaywaysQuickView"
        //}

      ],
      "消息推送": [
        {
          "Id": "msgSubscribeCompany",
          "Class": "menu",
          "Title": "员工消息订阅",
          "Url": "Setting/MsgSubscribeCompany"
        }
      ],
      "商城": [
        {
          "Id": "mallSetting",
          "Class": "menu",
          "Title": "商城配置",
          "Url": "Mall/MallSetting"
        },
        {
          "Id": "onsaleTemplate",
          "Class": "menu",
          "Title": "上架管理",
          "Url": "Mall/OnSaleTemplate"
        },
        {
          "Id": "mainpageMaintenance",
          "Class": "menu",
          "Title": "裝修模板",
          "Url": "Mall/MainpageMaintenance"
        },
        {
          "Id": "mallUser",
          "Class": "menu",
          "Title": "商城用户",
          "Url": "Mall/MallUser"
        }
      ]
    }
  }
]
