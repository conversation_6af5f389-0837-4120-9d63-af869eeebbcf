# 销售订单分单功能说明

## 功能概述

销售订单分单功能根据商品库存情况自动将一个销售订单拆分成多个订单，确保每个订单都能在指定仓库中有足够的库存来满足需求。

## 实现逻辑

### 1. 统计每个商品所需数量
- 遍历销售订单中的所有商品行
- 计算每个商品的总需求数量（数量 × 单位换算系数）
- 合并相同商品的数量

### 2. 查询库存数量
- 查询每个商品在各个仓库的库存数量
- 只考虑库存数量大于0的仓库
- 按库存数量从小到大排序（优先选择库存少的仓库）

### 3. 分单判断规则

#### 3.1 库存检查
- 首先检查所有仓库的库存总和是否能满足商品需求
- 如果库存总和不足，分单失败，返回库存不足错误信息

#### 3.2 分单逻辑
对每个商品按以下规则处理：

**情况1：商品无库存**
- 分配到默认仓库（空字符串）
- 不影响分单判断

**情况2：商品只在一个仓库有库存**
- 直接分配到该仓库
- 不需要分单

**情况3：商品在多个仓库都有库存**
- 查找能满足全部需求的仓库，按库存数量从小到大排序
- 如果有仓库能满足全部需求：
  - 选择库存最少的仓库（优化库存利用）
  - 不需要分单
- 如果没有单个仓库能满足全部需求：
  - **需要分单**
  - 按库存数从大到小依次分配，直至满足需求
  - 优先使用库存多的仓库，最大化单仓库分配量

#### 3.3 最终分单判断
- 如果所有商品都不需要分单：`needDivide = false`
- 如果任何一个商品需要分单：`needDivide = true`

### 4. 金额分配规则
- 每单的金额 = 总金额 ÷ 单数（向下取整）
- 最后一单的金额 = 总金额 - 已分配的金额
- 确保不出现小数且分单总金额等于原单据金额
- 所有相关金额字段都按相同规则分配：
  - `total_amount`：订单总金额
  - `no_disc_amount`：未折扣金额
  - `now_disc_amount`：当前折扣金额
  - `now_pay_amount`：当前支付金额
  - `payway1_amount`：支付方式1金额
  - `payway2_amount`：支付方式2金额
  - `payway3_amount`：支付方式3金额

## API接口

### checkIsNeedDivideSheet
**路径**: `POST /SaleOrderSheet/checkIsNeedDivideSheet`

**功能**: 检查是否需要分单并执行分单操作

**请求参数**: 
```json
{
  "company_id": "公司ID",
  "total_amount": 1000.00,
  "SheetRows": [
    {
      "item_id": "商品ID",
      "quantity": 10,
      "unit_factor": 1,
      "real_price": 50.00,
      "sub_amount": 500.00
    }
  ]
}
```

**返回结果**:

成功分单：
```json
{
  "result": "OK",
  "needDivide": true,
  "msg": "订单已按库存情况自动分单",
  "originalSheetId": "SHEET001",
  "originalSheetNo": "SO2024000",
  "dividedSheetCount": 2,
  "dividedSheetNos": ["SO2024001", "SO2024002"]
}
```

无需分单：
```json
{
  "result": "OK",
  "needDivide": false,
  "msg": "订单审核成功",
  "sheet_id": "SHEET001",
  "sheet_no": "SO2024000"
}
```

库存不足：
```json
{
  "result": "Error",
  "msg": "商品 ITEM001 库存不足，需要 100，总库存仅有 80"
}
```

## 使用场景示例

### 场景1：商品分别在不同仓库有库存
**原订单**:
- 商品A: 需要100个，仓库1有库存150个
- 商品B: 需要50个，仓库2有库存80个

**分单结果**:
- 订单1（仓库1）: 商品A 100个
- 订单2（仓库2）: 商品B 50个

### 场景2：商品在多个仓库都有库存（单仓库能满足需求）
**原订单**:
- 商品A: 需要50个
- 仓库1有库存60个，仓库2有库存80个，仓库3有库存120个

**分单结果**:
- 订单1（仓库1）: 商品A 50个（选择能满足需求的最小库存仓库）

### 场景3：商品在多个仓库都有库存（单仓库无法满足需求）
**原订单**:
- 商品A: 需要100个
- 仓库1有库存30个，仓库2有库存40个，仓库3有库存50个

**分单结果**:
- 订单1（仓库3）: 商品A 50个（优先选择库存最多的仓库）
- 订单2（仓库2）: 商品A 40个（然后选择次多的仓库）
- 订单3（仓库1）: 商品A 10个（最后选择剩余的仓库）

### 场景4：库存不足的情况
**原订单**:
- 商品A: 需要100个
- 仓库1有库存30个，仓库2有库存40个（总库存70个）

**分单结果**:
- 分单失败，返回错误信息："商品 A 库存不足，需要 100，总库存仅有 70"

### 场景5：智能仓库选择示例

**商品需求**: ITEM001 需要 20个

**库存情况**:
- 仓库A: 50个
- 仓库B: 30个
- 仓库C: 25个

**分析过程**:
1. 多个仓库有库存，检查能满足需求的仓库
2. 仓库A(50)、B(30)、C(25)都能满足需求20个
3. 按库存从小到大排序：C(25) < B(30) < A(50)
4. 选择库存最少的仓库C

**结果**: 不分单，直接分配到仓库C
- 最终订单: 仓库C，ITEM001 20个

**优势**: 优化库存利用，避免库存积压

### 场景6：需要分单的示例

**商品需求**: ITEM001 需要 80个

**库存情况**:
- 仓库A: 50个
- 仓库B: 30个
- 仓库C: 25个

**分析过程**:
1. 多个仓库有库存，检查能满足需求的仓库
2. 没有单个仓库能满足需求80个（最大库存50 < 80）
3. 需要分单，按库存从大到小分配：A(50) > B(30) > C(25)
4. 分配过程：
   - 仓库A: 分配50个，剩余需求30个
   - 仓库B: 分配30个，剩余需求0个

**结果**: 需要分单，生成2个订单
- 订单1: 仓库A，ITEM001 50个
- 订单2: 仓库B，ITEM001 30个

### 场景7：混合场景示例

**订单包含多个商品**:
- ITEM001 需要 20个
- ITEM002 需要 80个

**库存情况**:
- 仓库A: ITEM001 50个，ITEM002 40个
- 仓库B: ITEM001 30个，ITEM002 35个
- 仓库C: ITEM001 25个，ITEM002 20个

**分析过程**:
- ITEM001: 仓库C(25)能满足需求20，选择库存最少的仓库C
- ITEM002: 没有单个仓库能满足需求80，需要分单

**结果**: 需要分单（因为ITEM002需要分单）
- 订单1: 仓库A，ITEM001 0个，ITEM002 40个
- 订单2: 仓库B，ITEM001 0个，ITEM002 35个
- 订单3: 仓库C，ITEM001 20个，ITEM002 5个（剩余需求）

### 场景8：金额分配示例
**原订单金额信息**:
- 总金额(total_amount): 1000元
- 调拨库存金额(move_stock): 118.8元
- 未扣税金额(no_ks_amount): 120元
- 当前折扣金额(now_disc_amount): 1.20元
- 当前支付金额(now_pay_amount): 118.80元

**分成3单的结果**:

订单1:
- total_amount: 333元（1000÷3向下取整）
- move_stock: 39元（118.8÷3向下取整）
- no_ks_amount: 40元（120÷3向下取整）
- now_disc_amount: 0元（1.20÷3向下取整）
- now_pay_amount: 39元（118.80÷3向下取整）

订单2:
- total_amount: 333元
- move_stock: 39元
- no_ks_amount: 40元
- now_disc_amount: 0元
- now_pay_amount: 39元

订单3（最后一单）:
- total_amount: 334元（1000-333-333）
- move_stock: 40.8元（118.8-39-39）
- no_ks_amount: 40元（120-40-40）
- now_disc_amount: 1.20元（1.20-0-0）
- now_pay_amount: 40.80元（118.80-39-39）

## 注意事项

1. **原单据保存**: 分单时会先保存原单据，然后创建分单，确保原单据有完整的sheet_id
2. **事务处理**: 分单操作按顺序执行，先保存原单据，再保存各个分单
3. **库存检查**: 只考虑当前可用库存，不考虑预留库存
4. **单据状态**: 分单后的新订单状态与原订单相同
5. **仓库分配**: 如果商品无库存，会分配到默认仓库（空字符串）
6. **金额精度**: 确保分单后总金额与原单据金额完全一致，避免精度丢失
7. **返回信息**: 分单成功后会返回原单据的sheet_id和sheet_no，以及所有分单的单据号

## 技术实现

### 核心方法
- `GenerateDivideSheetPlan`: 生成分单方案
- `CreateDivideSheets`: 创建分单
- `ExecuteDivideSheetPlan`: 执行分单方案

### 数据库操作
- 查询库存表获取商品库存信息
- 创建新的销售订单记录
- 使用事务确保数据一致性

## 算法逻辑总结

### 分单判断算法
```
对于每个商品：
1. 检查库存总和是否满足需求
   - 不满足 → 返回库存不足错误

2. 如果无库存 → 分配到默认仓库，继续下一个商品

3. 如果只有一个仓库有库存 → 直接分配，继续下一个商品

4. 如果多个仓库有库存：
   a. 查找能满足全部需求的仓库
   b. 如果有 → 选择库存最少的仓库，继续下一个商品
   c. 如果没有 → 标记需要分单，按库存从大到小分配

最终判断：
- 如果所有商品都不需要分单 → needDivide = false
- 如果任何一个商品需要分单 → needDivide = true
```

### 仓库选择策略
1. **优先选择库存少的仓库**：当多个仓库都能满足需求时
2. **优先使用库存多的仓库**：当需要分单时，最大化单仓库分配量
3. **避免不必要的分单**：能用单仓库满足就不分单

### 优化效果
- 减少分单数量，提高处理效率
- 优化库存利用，避免库存积压
- 保持业务灵活性，确保需求得到满足

