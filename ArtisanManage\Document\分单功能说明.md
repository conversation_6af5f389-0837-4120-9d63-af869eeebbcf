# 销售订单分单功能说明文档

## 概述

销售订单分单功能是一个智能的库存分配系统，能够根据各仓库的库存情况和业务员权限，自动将一个销售订单拆分成多个子订单，确保每个子订单都能在对应的仓库中得到满足。

## 功能特点

### 1. 智能库存分析
- **实时库存查询**：系统实时查询各仓库的可用库存（库存数量 - 销售待发数量）
- **正负库存分离**：将库存分为正可用库存和负库存，分别处理
- **批量查询优化**：一次性查询所有相关商品的库存信息，提高性能

### 2. 权限控制机制
- **业务员权限检查**：根据业务员的权限设置决定是否允许负库存开单
- **仓库权限管理**：每个仓库可以单独设置是否允许负库存操作
- **分级权限控制**：支持不同级别的权限控制策略

### 3. 智能分单算法
- **正库存优先**：优先使用正可用库存进行分配
- **负库存兜底**：在正库存不足时，智能选择允许负库存的仓库
- **库存集中原则**：优先使用库存较多的仓库，避免库存分散

## 核心组件

### 1. 主入口方法
**方法名**：`DivideSaveAndApprove`
**功能**：分单保存并审核的主入口
**流程**：
1. 解析前端传入的订单数据
2. 初始化订单信息
3. 查询库存信息和权限
4. 根据权限选择分单策略
5. 生成分单方案
6. 创建分单并执行保存审核

### 2. 库存信息查询
**方法名**：`GetStockInfo`
**功能**：查询商品库存信息和权限设置
**返回数据**：
- 正可用库存字典
- 负库存字典
- 商品需求数量
- 商品名称映射
- 仓库权限设置

### 3. 分单策略算法

#### 3.1 正库存分单算法
**方法名**：`DividePlanByPositive`
**适用场景**：业务员不允许负库存开单
**算法特点**：
- **隐式商品检查**：有正库存即意味着仓库中存在该商品，无需额外检查
- 只使用正可用库存进行分配
- 按库存数量降序排序，优先使用库存多的仓库
- 库存不足时直接返回错误

#### 3.2 负库存分单算法
**方法名**：`DividePlanByNegative`
**适用场景**：业务员允许负库存开单
**算法特点**：
- **仓库商品检查**：确保仓库中存在该商品（包括正库存和负库存记录）
- 第一阶段：使用正库存分配（算法同DividePlanByPositive）
- 第二阶段：剩余需求使用允许负库存且有该商品的仓库
- 优先选择已有负库存的仓库，避免负库存分散

### 4. 分单创建
**方法名**：`CreateDivideSheets`
**功能**：根据分配方案创建具体的分单
**处理逻辑**：
- 按仓库分组创建子订单
- 按比例分配优惠金额
- 保持原订单的所有属性
- 设置正确的仓库信息

### 5. 分单执行
**方法名**：`ExecuteDivideSheetPlan`
**功能**：执行分单方案，保存并审核所有子订单
**事务处理**：
- 使用数据库事务确保数据一致性
- 批量保存所有分单
- 逐个审核分单
- 异常时自动回滚

## 业务流程

### 1. 单仓库场景
当所有商品都能在同一个仓库满足时：
1. 系统识别为单仓库场景
2. 直接保存并审核原订单
3. 设置对应的仓库信息
4. 返回成功结果

### 2. 多仓库分单场景
当需要多个仓库协同满足订单时：
1. 生成分单方案
2. 创建多个子订单
3. 逻辑删除原订单（设置is_del=true）
4. 批量保存所有子订单
5. 逐个审核子订单
6. 返回分单结果

## 权限控制

### 1. 业务员权限
- **allowNegativeStock**：是否允许负库存开单
- 从操作员权限配置中读取
- 决定使用哪种分单算法

### 2. 仓库权限
- **allow_negative_stock_order**：仓库是否允许负库存
- 存储在info_branch表中
- 影响负库存分单的仓库选择

## 数据结构

### 1. 库存数据结构
```csharp
// 正可用库存：商品ID -> 仓库ID -> 库存数量
Dictionary<string, Dictionary<string, decimal>> positiveAvailStock

// 负库存：商品ID -> 仓库ID -> 库存数量（负数）
Dictionary<string, Dictionary<string, decimal>> negativeAvailStock

// 商品需求：商品ID -> 需求数量
Dictionary<string, decimal> itemQuantity

// 仓库权限：仓库ID -> 是否允许负库存
Dictionary<string, bool> stockRoles
```

### 2. 分配方案数据结构
```csharp
// 分配方案：商品ID -> [(仓库ID, 分配数量)]
Dictionary<string, List<(string branchId, decimal quantity)>> itemBranchAllocation
```

## 商品仓库匹配机制

### 1. 业务背景
在分单过程中，必须确保选择的仓库中实际存在该商品。这是一个重要的业务约束，避免将商品分配到从未进过该商品的仓库。

### 2. 检查逻辑
```csharp
// 正库存分单：有正库存即意味着商品存在，无需额外检查
if (!positiveAvailStock.ContainsKey(itemId) || positiveAvailStock[itemId].Count == 0)

// 负库存分单：只从负库存记录中查找有该商品且允许负库存的仓库
var availableBranches = negativeAvailStock[itemId].Keys.Intersect(allowNegativeStockBranches).ToList();
```

### 3. 应用场景
- **正库存分单**：无需额外检查，有正库存即意味着商品存在
- **负库存分单**：只从负库存记录中查找，确保选择的仓库曾经有过该商品

### 4. 优化原理
- **正库存的逻辑前提**：要有正库存，必须先有入库操作
- **入库即存在**：有入库操作就意味着仓库中存在该商品记录
- **查询逻辑**：SQL查询基于库存表，有正库存记录就证明商品存在
- **性能优化**：减少不必要的检查，提高算法效率

### 5. 错误处理
- **正库存分单**：直接检查是否有正库存，无需额外的商品存在性检查
- **负库存分单**：仍需检查商品存在性，因为要选择曾经有过该商品的仓库
- 如果允许负库存的仓库中都没有该商品，返回相应错误信息

## 错误处理

### 1. 商品存在性检查
- 商品在所有仓库中都不存在：返回商品不存在错误
- 允许负库存的仓库中都没有该商品：返回相应错误

### 2. 库存不足处理
- 正库存分单：直接返回库存不足错误
- 负库存分单：检查是否有允许负库存且有该商品的仓库

### 3. 权限不足处理
- 业务员无负库存权限且库存不足时返回错误
- 没有允许负库存的仓库时返回错误

### 4. 系统异常处理
- 数据库操作异常自动回滚事务
- 详细的错误日志记录
- 用户友好的错误提示

## 性能优化

### 1. 批量查询
- 一次性查询所有商品的库存信息
- 减少数据库交互次数

### 2. 内存优化
- 使用字典结构快速查找
- 避免重复计算

### 3. 事务优化
- 批量保存减少事务次数
- 合理的事务边界设计

## 扩展性设计

### 1. 算法扩展
- 分单算法采用策略模式
- 可以轻松添加新的分单策略

### 2. 权限扩展
- 支持更复杂的权限控制逻辑
- 可以添加更多权限维度

### 3. 业务扩展
- 支持不同类型的单据分单
- 可以扩展到其他业务场景

## 详细算法说明

### 1. 正库存分单算法（DividePlanByPositive）

**算法步骤**：
1. **正库存检查**：检查是否有正可用库存（有正库存即意味着商品存在）
2. **库存充足性判断**：如果任何商品的总可用库存小于需求量，直接返回错误
3. **仓库排序**：对每个商品的仓库按可用库存数量降序排序
4. **贪心分配**：优先从库存最多的仓库分配，直到满足需求
5. **结果验证**：确保所有商品都得到完整分配

**特点**：
- 严格遵循库存约束，不允许超卖
- 优化库存利用，减少库存分散
- 算法复杂度：O(n*m*log(m))，其中n为商品数，m为仓库数

### 2. 负库存分单算法（DividePlanByNegative）

**第一阶段：正库存分配**
1. **商品存在性检查**：检查仓库中是否存在该商品（包括正库存和负库存记录）
2. 使用与DividePlanByPositive相同的算法
3. 记录每个商品的剩余需求量
4. 不强制要求完全满足，允许部分满足

**第二阶段：负库存处理**
1. **仓库筛选**：从stockRoles中筛选允许负库存的仓库
2. **商品仓库匹配**：从负库存记录中筛选出有该商品且允许负库存的仓库
3. **智能选择**：选择负库存最多的仓库（绝对值最大）
   - 目的是集中负库存，便于后续补货管理
4. **剩余分配**：将所有剩余需求分配到选定的仓库

**算法优势**：
- 最大化利用正库存
- 智能管理负库存分布
- 便于库存补货计划

## 库存查询机制

### 1. SQL查询逻辑
```sql
SELECT ip.item_name,
    s.item_id,
    s.branch_id,
    sum(stock_qty - COALESCE(sell_pend_qty, 0)) avail_stock,
    ib.allow_negative_stock_order stock_role
FROM info_item_prop ip
    LEFT JOIN stock s on ip.item_id = s.item_id
    LEFT JOIN info_branch ib on ib.branch_id = s.branch_id
WHERE s.company_id = {company_id}
    AND s.item_id IN ({item_ids})
GROUP BY ip.item_name, s.item_id, s.branch_id, ib.allow_negative_stock_order
ORDER BY avail_stock ASC
```

### 2. 可用库存计算
- **可用库存** = 库存数量 - 销售待发数量
- **正库存**：可用库存 > 0 的记录
- **负库存**：可用库存 ≤ 0 的记录

### 3. 权限信息获取
- 从info_branch表获取仓库的allow_negative_stock_order字段
- 从操作员权限配置获取allowNegativeStock设置

## 分单创建详细流程

### 1. 数据分组
```csharp
// 按仓库分组商品行
Dictionary<string, List<SheetRowSaleOrder>> branchRows
```

### 2. 行数据处理
- **数量转换**：分配数量 ÷ 单位换算系数 = 显示数量
- **金额计算**：显示数量 × 实际单价 = 行金额
- **仓库设置**：设置每行的branch_id

### 3. 优惠金额分配
- **平均分配**：总优惠金额 ÷ 分单数量
- **余额处理**：最后一个分单承担余额
- **公式**：
  ```csharp
  decimal baseAmount = Math.Floor(totalDiscount / sheetCount);
  decimal remainingAmount = totalDiscount - (baseAmount * (sheetCount - 1));
  ```

### 4. 分单属性设置
- **继承原单属性**：客户、日期、业务员等
- **生成新单号**：系统自动生成新的单据编号
- **设置分单标识**：标记为分单生成的子订单

## 事务处理机制

### 1. 事务范围
- **保存阶段**：批量保存所有分单
- **审核阶段**：逐个审核分单（独立事务）

### 2. 错误处理策略
- **保存失败**：整个分单操作回滚
- **审核失败**：记录失败数量，不影响其他分单
- **部分成功**：返回详细的成功/失败信息

### 3. 数据一致性保证
- 原订单逻辑删除（is_del=true）
- 分单保存成功后才执行审核
- 异常情况下自动回滚保存操作

## 前端交互

### 1. 调用接口
```javascript
$.ajax({
    url: '/api/SaleOrderSheet/DivideSaveAndApprove',
    type: 'POST',
    contentType: 'application/json',
    data: JSON.stringify(sheet)
});
```

### 2. 返回结果处理
- **成功场景**：显示成功提示，关闭窗口
- **失败场景**：显示错误信息，允许重试
- **分单场景**：显示分单信息，更新界面状态

### 3. 用户体验优化
- 操作过程中禁用相关按钮
- 显示处理进度提示
- 自动关闭窗口和刷新列表
