# 销售订单分单功能说明

## 功能概述

销售订单分单功能根据商品库存情况自动将一个销售订单拆分成多个订单，确保每个订单都能在指定仓库中有足够的库存来满足需求。

## 实现逻辑

### 1. 统计每个商品所需数量
- 遍历销售订单中的所有商品行
- 计算每个商品的总需求数量（数量 × 单位换算系数）
- 合并相同商品的数量

### 2. 查询库存数量
- 查询每个商品在各个仓库的库存数量
- 只考虑库存数量大于0的仓库
- 按库存数量从小到大排序（优先选择库存少的仓库）

### 3. 分单判断规则
- **如果商品只在一个仓库有库存**：直接按仓库分单
- **如果商品在多个仓库都有库存**：
  - 首先检查是否有单个仓库能满足全部需求
    - 如果有，优先选择库存少的仓库（能满足需求的最小库存仓库）
    - 如果没有合适的，使用库存最多的仓库
  - 如果没有单个仓库能满足全部需求：
    - 按库存数从大到小依次选择仓库直至满足需求
    - 商品按库存数分配到不同仓库

### 4. 金额分配规则
- 每单的金额 = 总金额 ÷ 单数（向下取整）
- 最后一单的金额 = 总金额 - 已分配的金额
- 确保不出现小数且分单总金额等于原单据金额

## API接口

### checkIsNeedDivideSheet
**路径**: `POST /SaleOrderSheet/checkIsNeedDivideSheet`

**功能**: 检查是否需要分单并执行分单操作

**请求参数**: 
```json
{
  "company_id": "公司ID",
  "total_amount": 1000.00,
  "SheetRows": [
    {
      "item_id": "商品ID",
      "quantity": 10,
      "unit_factor": 1,
      "real_price": 50.00,
      "sub_amount": 500.00
    }
  ]
}
```

**返回结果**:
```json
{
  "result": "OK",
  "needDivide": true,
  "msg": "订单已按库存情况自动分单",
  "dividedSheetCount": 2,
  "dividedSheetNos": ["SO2024001", "SO2024002"]
}
```

## 使用场景示例

### 场景1：商品分别在不同仓库有库存
**原订单**:
- 商品A: 需要100个，仓库1有库存150个
- 商品B: 需要50个，仓库2有库存80个

**分单结果**:
- 订单1（仓库1）: 商品A 100个
- 订单2（仓库2）: 商品B 50个

### 场景2：商品在多个仓库都有库存（单仓库能满足需求）
**原订单**:
- 商品A: 需要50个
- 仓库1有库存60个，仓库2有库存80个，仓库3有库存120个

**分单结果**:
- 订单1（仓库1）: 商品A 50个（选择能满足需求的最小库存仓库）

### 场景3：商品在多个仓库都有库存（单仓库无法满足需求）
**原订单**:
- 商品A: 需要100个
- 仓库1有库存30个，仓库2有库存40个，仓库3有库存50个

**分单结果**:
- 订单1（仓库3）: 商品A 50个（优先选择库存最多的仓库）
- 订单2（仓库2）: 商品A 40个（然后选择次多的仓库）
- 订单3（仓库1）: 商品A 10个（最后选择剩余的仓库）

### 场景4：金额分配示例
**原订单总金额**: 1000元，分成3单

**分单结果**:
- 订单1: 333元（1000÷3向下取整）
- 订单2: 333元
- 订单3: 334元（1000-333-333）

## 注意事项

1. **事务处理**: 分单操作在事务中执行，如果任何一个分单保存失败，整个操作会回滚
2. **库存检查**: 只考虑当前可用库存，不考虑预留库存
3. **单据状态**: 分单后的新订单状态与原订单相同
4. **仓库分配**: 如果商品无库存，会分配到默认仓库（空字符串）
5. **金额精度**: 确保分单后总金额与原单据金额完全一致，避免精度丢失

## 技术实现

### 核心方法
- `GenerateDivideSheetPlan`: 生成分单方案
- `CreateDivideSheets`: 创建分单
- `ExecuteDivideSheetPlan`: 执行分单方案

### 数据库操作
- 查询库存表获取商品库存信息
- 创建新的销售订单记录
- 使用事务确保数据一致性

## 扩展功能

未来可以考虑的扩展功能：
1. 支持按客户优先级分单
2. 支持按配送路线分单
3. 支持手动调整分单方案
4. 支持分单历史记录查询
