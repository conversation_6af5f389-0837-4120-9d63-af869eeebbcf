# 销售订单智能分单功能说明

## 功能概述

销售订单智能分单功能是一个自动化的仓库分配系统，根据商品库存情况智能地将订单分配到最优仓库。系统会分析库存分布，在保证订单完成的前提下，优化库存利用率并减少不必要的分单操作。

## 触发机制

### 自动触发条件
- 当销售订单的 `branch_id` 字段为空时
- 系统在 `SaveAndApprove` 方法中检测到需要分单
- 自动调用 `divideSaveAndApprove` 方法进行智能分单

### 核心方法
- **入口方法**: `divideSaveAndApprove(SheetSaleOrder sheet)`
- **方案生成**: `GenerateDivideSheetPlan(SheetSaleOrder originalSheet, CMySbCommand cmd)`
- **分单创建**: `CreateDivideSheets(SheetSaleOrder originalSheet, Dictionary itemBranchAllocation)`
- **分单执行**: `ExecuteDivideSheetPlan(SheetSaleOrder originalSheet, List<SheetSaleOrder> divideSheets, CMySbCommand cmd)`

## 智能分单流程

### 1. 商品需求统计
- 遍历订单中的所有商品行
- 计算每个商品的总需求数量（quantity × unit_factor）
- 按商品ID合并相同商品的数量

### 2. 库存查询与分析
- 查询每个商品在各仓库的可用库存
- 只考虑库存数量大于0的仓库
- 获取商品名称用于错误提示

### 3. 智能分配算法

#### 3.1 库存充足性检查
- 检查所有仓库的库存总和是否能满足商品需求
- 如果库存总和不足，返回库存不足错误信息
- 错误格式：`"商品 {商品名称} 可用库存不足，需要 {需求量}，可用库存仅有 {总库存}"`

#### 3.2 智能仓库选择
对每个商品按以下优先级处理：

**情况1：商品无库存**
- 分配到默认仓库（空字符串）
- 不影响分单判断

**情况2：商品只在一个仓库有库存**
- 直接分配到该仓库
- 不需要分单

**情况3：商品在多个仓库都有库存**
- 查找能满足全部需求的仓库，按库存从小到大排序
- 如果有仓库能满足全部需求：
  - 选择库存最少但能满足需求的仓库
  - 优化库存利用，避免库存积压
  - 不需要分单
- 如果没有单个仓库能满足全部需求：
  - 需要选择多个仓库
  - 按库存从大到小依次分配
  - 最大化单仓库分配量

#### 3.3 分单判断逻辑
- 统计所有商品的分配结果
- 如果任何商品需要多个仓库：生成多个订单
- 如果所有商品都能单仓库满足：生成1个订单

### 4. 金额分配规则

#### 4.1 基础金额计算
每个分单的金额基于该分单包含的商品行计算：
- **订单总金额** (`total_amount`) = 该分单所有商品行的 `money_amount` 之和
- **商品金额** = 商品数量 × 商品单价

#### 4.2 优惠金额分配
优惠金额按分单数量平均分配：
- **基础优惠金额** = 原单据优惠金额 ÷ 分单数量（向下取整）
- **最后一单优惠金额** = 原单据优惠金额 - 前面所有分单的优惠金额
- **分配规则**：
  ```
  前 N-1 单：now_disc_amount = Math.Floor(总优惠金额 ÷ 分单数)
  最后一单：now_disc_amount = 总优惠金额 - 已分配的优惠金额
  ```

#### 4.3 支付金额计算
各支付相关金额基于订单总金额和优惠金额计算：
- **实际支付金额** (`now_pay_amount`) = 订单总金额 - 优惠金额
- **已付金额** (`paid_amount`) = 订单总金额 - 优惠金额
- **实收金额** (`real_get_amount`) = 订单总金额 - 优惠金额

#### 4.4 支付方式金额
支付方式金额根据原单据的支付方式设置：
- 如果原单据某支付方式金额为0，则分单中该支付方式金额也为0
- 如果原单据某支付方式有金额，则分单中该支付方式金额 = 实际支付金额
- **支付方式1** (`payway1_amount`) = 原单据有此支付方式 ? 实际支付金额 : 0
- **支付方式2** (`payway2_amount`) = 原单据有此支付方式 ? 实际支付金额 : 0
- **支付方式3** (`payway3_amount`) = 原单据有此支付方式 ? 实际支付金额 : 0

#### 4.5 金额分配特点
1. **商品驱动**：订单总金额由实际分配的商品决定，不是平均分配
2. **优惠平分**：只有优惠金额按分单数量平均分配
3. **支付一致**：支付相关金额保持逻辑一致性
4. **精度保证**：通过向下取整和最后一单补齐确保总金额准确

## 执行流程详解

### 5. 订单处理流程

#### 5.1 单仓库订单（不分单）
当分析结果显示只需要1个订单时：

1. **创建订单**：根据分配方案创建单个订单
2. **设置仓库**：将订单的 `branch_id` 设置为选定的仓库
3. **保存订单**：调用 `Save` 方法保存订单到数据库
4. **自动审核**：调用 `divideApprove` 方法进行审核
5. **返回结果**：返回成功信息和订单详情

**返回数据格式**：
```json
{
  "result": "OK",
  "needDivide": false,
  "msg": "订单审核成功,已自动分配仓库",
  "sheet_id": "生成的订单ID",
  "sheet_no": "生成的订单号",
  "happen_time": "发生时间",
  "approve_time": "审核时间",
  "review_time": "复核时间",
  "placeholder_sheet_id": "占位单ID",
  "placeholder_sheet_no": "占位单号"
}
```

#### 5.2 多仓库订单（分单）
当分析结果显示需要多个订单时：

1. **保存原单据**：保存原订单并设置 `is_del = true`（软删除标记）
2. **创建分单**：根据分配方案创建多个订单
3. **设置关系**：每个分单的 `parent_sheet_id` 指向原订单ID
4. **批量保存**：逐个保存所有分单到数据库
5. **批量审核**：对每个分单调用 `divideApprove` 进行审核
6. **返回结果**：返回分单信息和所有分单号列表

**返回数据格式**：
```json
{
  "result": "OK",
  "needDivide": true,
  "msg": "订单已按库存情况自动分单",
  "sheet_id": "原订单ID",
  "sheet_no": "原订单号",
  "dividedSheetCount": 3,
  "dividedSheetNos": ["分单1号", "分单2号", "分单3号"]
}
```

### 6. 审核处理（divideApprove）
每个订单（无论是否分单）都会经过以下审核流程：

1. **复核处理**：如果需要复核，设置复核人和复核时间
2. **附件处理**：处理订单附件图片
3. **陈列单据处理**：处理相关的陈列单据信息
4. **保存并审核**：调用 `SaveAndApprove` 或 `RedAndChange` 方法
5. **消息创建**：为需要复核的陈列单据创建待办消息

## API接口

### SaveAndApprove
**路径**: `POST /SaleOrderSheet/SaveAndApprove`

**功能**: 保存并审核销售订单，自动检测是否需要分单

**触发条件**: 当订单的 `branch_id` 为空时，自动调用分单功能

### divideSaveAndApprove
**内部方法**: 智能分单的核心处理方法

**功能**: 根据库存情况智能分配仓库并完成订单保存审核

**请求参数**: 
```json
{
  "company_id": "公司ID",
  "total_amount": 1000.00,
  "SheetRows": [
    {
      "item_id": "商品ID",
      "quantity": 10,
      "unit_factor": 1,
      "real_price": 50.00,
      "sub_amount": 500.00
    }
  ]
}
```

**返回结果**:

成功分单：
```json
{
  "result": "OK",
  "needDivide": true,
  "msg": "订单已按库存情况自动分单",
  "originalSheetId": "SHEET001",
  "originalSheetNo": "SO2024000",
  "dividedSheetCount": 2,
  "dividedSheetNos": ["SO2024001", "SO2024002"]
}
```

无需分单：
```json
{
  "result": "OK",
  "needDivide": false,
  "msg": "订单保存成功",
  "sheet_id": "SHEET001",
  "sheet_no": "SO2024000"
}
```

库存不足：
```json
{
  "result": "Error",
  "msg": "商品 ITEM001 库存不足，需要 100，总库存仅有 80"
}
```

## 使用场景示例

### 场景1：商品分别在不同仓库有库存
**原订单**:
- 商品A: 需要100个，仓库1有库存150个
- 商品B: 需要50个，仓库2有库存80个

**分单结果**:
- 订单1（仓库1）: 商品A 100个
- 订单2（仓库2）: 商品B 50个

### 场景2：商品在多个仓库都有库存（单仓库能满足需求）
**原订单**:
- 商品A: 需要50个
- 仓库1有库存60个，仓库2有库存80个，仓库3有库存120个

**分单结果**:
- 订单1（仓库1）: 商品A 50个（选择能满足需求的最小库存仓库）

### 场景3：商品在多个仓库都有库存（单仓库无法满足需求）
**原订单**:
- 商品A: 需要100个
- 仓库1有库存30个，仓库2有库存40个，仓库3有库存50个

**分单结果**:
- 订单1（仓库3）: 商品A 50个（优先选择库存最多的仓库）
- 订单2（仓库2）: 商品A 40个（然后选择次多的仓库）
- 订单3（仓库1）: 商品A 10个（最后选择剩余的仓库）

### 场景4：库存不足的情况
**原订单**:
- 商品A: 需要100个
- 仓库1有库存30个，仓库2有库存40个（总库存70个）

**分单结果**:
- 分单失败，返回错误信息："商品 A 库存不足，需要 100，总库存仅有 70"

### 场景5：智能仓库选择示例

**商品需求**: ITEM001 需要 20个

**库存情况**:
- 仓库A: 50个
- 仓库B: 30个
- 仓库C: 25个

**分析过程**:
1. 多个仓库有库存，检查能满足需求的仓库
2. 仓库A(50)、B(30)、C(25)都能满足需求20个
3. 按库存从小到大排序：C(25) < B(30) < A(50)
4. 选择库存最少的仓库C

**结果**: 不分单，直接分配到仓库C
- 最终订单: 仓库C，ITEM001 20个

**优势**: 优化库存利用，避免库存积压

### 场景6：需要分单的示例

**商品需求**: ITEM001 需要 80个

**库存情况**:
- 仓库A: 50个
- 仓库B: 30个
- 仓库C: 25个

**分析过程**:
1. 多个仓库有库存，检查能满足需求的仓库
2. 没有单个仓库能满足需求80个（最大库存50 < 80）
3. 需要分单，按库存从大到小分配：A(50) > B(30) > C(25)
4. 分配过程：
   - 仓库A: 分配50个，剩余需求30个
   - 仓库B: 分配30个，剩余需求0个

**结果**: 需要分单，生成2个订单
- 订单1: 仓库A，ITEM001 50个
- 订单2: 仓库B，ITEM001 30个

### 场景7：混合场景示例

**订单包含多个商品**:
- ITEM001 需要 20个
- ITEM002 需要 80个

**库存情况**:
- 仓库A: ITEM001 50个，ITEM002 40个
- 仓库B: ITEM001 30个，ITEM002 35个
- 仓库C: ITEM001 25个，ITEM002 20个

**分析过程**:
- ITEM001: 仓库C(25)能满足需求20，选择库存最少的仓库C
- ITEM002: 没有单个仓库能满足需求80，需要分单

**结果**: 需要分单（因为ITEM002需要分单）
- 订单1: 仓库A，ITEM001 0个，ITEM002 40个
- 订单2: 仓库B，ITEM001 0个，ITEM002 35个
- 订单3: 仓库C，ITEM001 20个，ITEM002 5个（剩余需求）

### 场景8：金额分配示例

**原订单信息**:
- 商品A: 100个，单价10元，小计1000元
- 商品B: 50个，单价20元，小计1000元
- 订单总金额: 2000元
- 优惠金额(now_disc_amount): 100元
- 支付方式1金额(payway1_amount): 1900元（原单据有此支付方式）
- 支付方式2金额(payway2_amount): 0元（原单据无此支付方式）

**分单结果**（分成2单）:

**订单1（仓库A）**:
- 包含商品：商品A 100个，商品B 30个
- total_amount: 1000 + 600 = 1600元（基于实际商品计算）
- now_disc_amount: 50元（100÷2向下取整）
- now_pay_amount: 1600 - 50 = 1550元
- paid_amount: 1550元
- payway1_amount: 1550元（原单据有此支付方式）
- payway2_amount: 0元（原单据无此支付方式）
- real_get_amount: 1550元

**订单2（仓库B）**:
- 包含商品：商品B 20个
- total_amount: 400元（基于实际商品计算）
- now_disc_amount: 50元（100-50，最后一单补齐）
- now_pay_amount: 400 - 50 = 350元
- paid_amount: 350元
- payway1_amount: 350元（原单据有此支付方式）
- payway2_amount: 0元（原单据无此支付方式）
- real_get_amount: 350元

**验证总和**:
- total_amount: 1600 + 400 = 2000元 ✓
- now_disc_amount: 50 + 50 = 100元 ✓
- now_pay_amount: 1550 + 350 = 1900元 ✓

**关键特点**:
1. 订单总金额由实际分配的商品决定，不是平均分配
2. 只有优惠金额按分单数量平均分配
3. 支付金额 = 订单总金额 - 优惠金额
4. 支付方式金额根据原单据设置决定

## 注意事项

1. **原单据保存**: 分单时会先保存原单据，然后创建分单，确保原单据有完整的sheet_id
2. **事务处理**: 分单操作按顺序执行，先保存原单据，再保存各个分单
3. **库存检查**: 只考虑当前可用库存，不考虑预留库存
4. **单据状态**: 分单后的新订单状态与原订单相同
5. **仓库分配**: 如果商品无库存，会分配到默认仓库（空字符串）
6. **金额精度**: 确保分单后总金额与原单据金额完全一致，避免精度丢失
7. **返回信息**: 分单成功后会返回原单据的sheet_id和sheet_no，以及所有分单的单据号

## 技术实现

### 核心方法
- `GenerateDivideSheetPlan`: 生成分单方案
- `CreateDivideSheets`: 创建分单
- `ExecuteDivideSheetPlan`: 执行分单方案

### 数据库操作
- 查询库存表获取商品库存信息
- 创建新的销售订单记录
- 使用事务确保数据一致性

## 算法逻辑总结

### 分单判断算法
```
对于每个商品：
1. 检查库存总和是否满足需求
   - 不满足 → 返回库存不足错误

2. 如果无库存 → 分配到默认仓库，继续下一个商品

3. 如果只有一个仓库有库存 → 直接分配，继续下一个商品

4. 如果多个仓库有库存：
   a. 查找能满足全部需求的仓库
   b. 如果有 → 选择库存最少的仓库，继续下一个商品
   c. 如果没有 → 标记需要分单，按库存从大到小分配

最终判断：
- 如果所有商品都不需要分单 → needDivide = false
- 如果任何一个商品需要分单 → needDivide = true
```

### 仓库选择策略
1. **优先选择库存少的仓库**：当多个仓库都能满足需求时
2. **优先使用库存多的仓库**：当需要分单时，最大化单仓库分配量
3. **避免不必要的分单**：能用单仓库满足就不分单

### 优化效果
- 减少分单数量，提高处理效率
- 优化库存利用，避免库存积压
- 保持业务灵活性，确保需求得到满足

## 完整算法流程图

```
开始
  ↓
检查 branch_id 是否为空
  ↓ (是)
调用 divideSaveAndApprove
  ↓
生成分单方案 (GenerateDivideSheetPlan)
  ↓
检查库存是否充足
  ↓ (否) → 返回库存不足错误
  ↓ (是)
创建分单 (CreateDivideSheets)
  ↓
判断分单数量
  ↓
┌─────────────────┬─────────────────┐
│   只有1个订单    │   有多个订单     │
│   (不分单)      │   (需要分单)     │
│       ↓         │       ↓         │
│ 保存单个订单     │ 保存原单据       │
│       ↓         │ (is_del=true)   │
│ 调用审核方法     │       ↓         │
│       ↓         │ 批量保存分单     │
│ 返回成功结果     │       ↓         │
│                │ 批量审核分单     │
│                │       ↓         │
│                │ 返回分单结果     │
└─────────────────┴─────────────────┘
  ↓
结束
```

## 关键特性总结

1. **智能仓库选择**：优先选择库存较少但能满足需求的仓库
2. **最小化分单**：能用单仓库满足就不分单
3. **自动审核**：分单后自动完成审核流程
4. **完整追溯**：通过 parent_sheet_id 建立父子关系
5. **精确金额分配**：确保分单后金额总和完全一致
6. **错误处理**：完善的错误检查和提示机制

