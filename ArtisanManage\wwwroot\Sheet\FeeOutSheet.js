﻿ 
function onPageReady(sheetRows) {

    var sheetType = $('#sheetType').val();
    if (sheetType == 'ZC') {
        $('#pfs_btn').css('display', 'flex');//仅费用支出单允许代垫
    }

    var incomeSheetInfo = $('#incomeSheetInfo').val();
    if (incomeSheetInfo) {
        incomeSheetInfo = JSON.parse(incomeSheetInfo);
        $('#pfs_btn').text(`代厂家支付：${incomeSheetInfo.supplier_name}`);
    }

    function getQueryVariable(variable) {
        debugger
        var query = window.location.search.substring(1);
        var vars = query.split("&");
        for (var i = 0; i < vars.length; i++) {
            var pair = vars[i].split("=");
            if (pair[0] == variable) { return pair[1]; }
        }
        return (false);
    }
    var forOutOrIn = getQueryVariable("forOutOrIn")
    var title = '费用类型';
    var datafldTitle = '费用名称'
    var bForFeeOut = true
    if (forOutOrIn == "false") {
        bForFeeOut = false;
        title = '收入类型';
        datafldTitle = '收入名称';
        $('#clientCaption span').text('供应商')
    }


    function myIsNumber(value) {
        if (value === undefined) return false
        if (value === null) return false
        value = value.toString().trim()
        if (value === '') return false
        return !isNaN(value)
    }
    $(".tab").css({
        "cursor": "pointer",
        "padding": "5px 15px",
        "border-bottom": "2px solid transparent"
    });
    $(".tab.active").css("border-bottom", "2px solid #1890ff");
    
    //aaaaa
    //$('#supcust_id').jqxInput({
    //    onButtonClick: function (event) {
    //        $('#popClient').jqxWindow('open')
    //        if (forOutOrIn == "false") {
    //            $("#popClient").jqxWindow('setContent', `<iframe src="/BaseInfo/SuppliersView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
    //        }
    //        else {
    //            $("#btnSeleteDisp").css('display', 'inline-block');
    //            $("#popClient").jqxWindow('setContent', `<iframe src="/BaseInfo/FeeUnitsView?forSelect=1&withClients=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
    //        }

    //       // $("#popClient").jqxWindow('setContent', `<iframe src="/BaseInfo/ClientsView?forSelect=1&forAll=true&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);


    //    }


    //})
    $('#supcust_id').jqxInput({
        onButtonClick: function (event) {
            $('#popClient').jqxWindow('open');
            if (forOutOrIn == "false") {
                // 其他收入单只显示供应商
                $('.tabs').hide();
                $("#clientContent").html(`<iframe src="/BaseInfo/SuppliersView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
            } else {
                // 费用支出单显示标签页，默认显示客户
                $('.tabs').show();
                $("#btnSeleteDisp").css('display', 'inline-block');
                switchTab('client');
            }
        }
    });
    $('#supcust_id').on('optionSelected',
        function (e) {
            var supcust_id = e.args.item.value;
            var sup_name = e.args.item.label;
            $('#supcust_id').jqxInput('val', { value: supcust_id, label: sup_name });
            if (bForFeeOut) {
                getClientAccountInfo(supcust_id);
                // showSelectDisp(supcust_id);
                
            }
            $("#btnSeleteDisp").css('display', 'inline-block');
        });
    let windowHeight = document.body.offsetHeight - 50
    let windowWidth = document.body.offsetWidth - 80
    $("#popClient").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 900, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
    $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 900, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
    $("#popDisplay").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 900, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
    $("#popPayForSupplier").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 400, width: 550, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
    $("#popImport").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 400, width: 550, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade'});
    // 附件窗口
    $("#popAppendix").jqxWindow({ closeButtonAction: 'keepFrame',isModal: true, modalOpacity: 0.3, height: 600, width: 900, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
    var approve_time = $('#approve_time').text();
    if (approve_time) {
        $('#btnApprove').attr('disabled', true);
        $("#btnSave").attr('disabled', true);
        
    }
    debugger
    var supcustID = $('#supcust_id').val().value;
    var t = document.getElementById('div_left_amount')
    if (!supcustID) {
        t.style.display = 'none';
    } else {
        t.style.display = 'block';
    }
    if (supcustID && bForFeeOut) getClientAccountInfo(supcustID);

    $('#no_disc_amount').jqxInput('disabled', true);
    var payway2_amount = $('#payway2_amount').jqxInput('val');
    if (parseFloat(payway2_amount))
        onShowGroupBtnClicked('payway');

    showSelectDisp = function(supcustID) {
        $.ajax({
            url: '/api/FeeOutSheet/GetDisplaySheet',
            type: 'Get',
            contentType: 'application/json',
            data: { operKey: g_operKey, supcust_id: supcustID },
            success: function (data) {
                if (data.result === 'OK') {
                    var records = data.displaySheets;
                    var str = '';
                    records.forEach((rec) => {
                        str += `<tr onclick='selectDisplayRow(this)'>
                                            <td id="display_id" class="hidden">${rec.display_id}</td>
                                            <td>${rec.display_no}</td>
                                            <td id="display_fee_id" class="hidden">${rec.display_fee_id}</td>
                                            <td id="display_fee_name">${rec.display_fee_name}</td>
                                            <td id="display_sub_amount">${rec.display_sub_amount}</td>
                                            <td>${rec.display_remark}</td>
                                        </tr>
                                     `
                    })
                    $('#disp_tbody').html(str);
                    if (str != "") $('#popDisplay').jqxWindow('open');
                }
                else {
                    bw.toast(data.msg, 3000);
                }
            },
            error: function (xhr) {
                console.log("返回响应信息：" + xhr.responseText);
            }
        });
    }

    selectDisplayRow =function (obj) {
        var rows = $('#jqxgrid').jqxGrid('getrows')
        var obj_fee_sub_amount = obj.fee_sub_amount
        var obj_fee_sub_id = obj.fee_sub_id
        var obj_fee_sub_name = obj.fee_sub_name
        var obj_display_id = obj.display_id
        var obj_display_no = obj.display_no
        var obj_display_month_qty = obj.display_month_qty
        var obj_display_month_given = obj.display_month_given
        var obj_display_month = obj.display_month
        var obj_remark = obj.remark
        // 初始页面存在20个空数组，但是rows中的对象都是空值
        let tempItem = rows.find(sheetRow => obj_display_id === sheetRow.display_id && obj_display_month === sheetRow.display_month)
        if (tempItem) {
            let showMsgFlag = ''
            if (Number(tempItem.fee_sub_amount) + Number(obj_fee_sub_amount) <= (Number(obj_display_month_qty) - Number(obj_display_month_given)) ) {
                tempItem.fee_sub_amount = Number(tempItem.fee_sub_amount) + Number(obj_fee_sub_amount)
            } else {
                tempItem.fee_sub_amount = Number(monthItem.display_month_qty) - Number(monthItem.display_month_given)
            }
            showMsgFlag = `${obj_display_no},${obj_remark}存在相同，将进行合并`
            if (tempItem.remark === '') {
                tempItem.remark = oobj_remark
            } else {
                if (obj_remark !== '') {
                    tempItem.remark = tempItem.remark + '，' + obj_remark
                }
            }
            if (showMsgFlag) {
                bw.toast(showMsgFlag)
            }
        } else {
            let tempItem = rows.find(sheetRow => sheetRow.display_id === '' && sheetRow.fee_sub_id === '')
            if (tempItem) {
                tempItem.display_id = obj_display_id;
                tempItem.fee_sub_id = obj_fee_sub_id;
                tempItem.fee_sub_name = obj_fee_sub_name;
                tempItem.fee_sub_amount = obj_fee_sub_amount;
                tempItem.remark = obj_remark;
                tempItem.display_month = obj_display_month;
            } else {
                let row = {
                    display_id: obj_display_id,
                    fee_sub_id: obj_fee_sub_id,
                    fee_sub_name: obj_fee_sub_name,
                    fee_sub_amount: obj_fee_sub_amount,
                    remark: obj_remark,
                    display_month: obj_display_month,
                }
                rows.push(row)
            }
        }
        $('#popDisplay').jqxWindow('close');
        $('#supcust_id').jqxInput({ disabled: true })
        var source = $('#jqxgrid').jqxGrid('getsource')
        source = source._source
        source.localdata = rows
        $('#jqxgrid').jqxGrid('updatebounddata')
        var total_amount = $('#total_amount').jqxInput('val');
        var payway1_amount = $('#payway1_amount').jqxInput('val');
        $('#total_amount').jqxInput('val', toMoney(parseFloat(total_amount) + parseFloat(obj_fee_sub_amount)));
        $('#payway1_amount').jqxInput('val', toMoney(parseFloat(payway1_amount) + parseFloat(obj_fee_sub_amount)));

    }

    // 附件列表
    let appendixPhotoList = null

    let appendixSrcList = []

    if ($("#appendix_photos").val()) {
        let photoList = JSON.parse($("#appendix_photos").val())

        if (Array.isArray(photoList) && photoList.length > 0) {
            for (p of photoList) {//用for of的话，photoList为键值格式时会报错
                appendixSrcList.push(Href + '/uploads' + p)
            }
        }
    }
    window.addEventListener('message', function (rs) {
        /*
        if (rs.data.msgHead === "ClientsView") {
            if (rs.data.action === "select") {
                var supcust_id = rs.data.supcust_id;
                var sup_name = rs.data.sup_name;
                $('#supcust_id').jqxInput('val', { value: supcust_id, label: sup_name });
            }
            $('#popClient').jqxWindow('close');
            $('#popDisplay').jqxWindow('open');

        }
        */
        if (rs.data.msgHead === "FeeUnitsView" || rs.data.msgHead === "SuppliersView") {
            debugger
            if (rs.data.action === "select") {
                var supcust_id = rs.data.supcust_id;
                if (supcust_id) t.style.display = 'block';
                var sup_name = rs.data.sup_name;
                $('#supcust_id').jqxInput('val', { value: supcust_id, label: sup_name });
                $('#popClient').jqxWindow('close');
                if (bForFeeOut) {
                    getClientAccountInfo(supcust_id)
                    // showSelectDisp(supcust_id)
                }
            }
        }
        else if (rs.data.msgHead === "FeeOutView") {
            if (rs.data.action === "select") {
                var sub_id = rs.data.sub_id;
                var sub_name = rs.data.sub_name;

                var cell = $('#jqxgrid').jqxGrid('getselectedcell');
                var rowIndex = cell.rowindex;
                var editable = $("#jqxgrid").jqxGrid('endcelledit', rowIndex, "fee_sub_id", false);
                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, "fee_sub_id", { value: sub_id, label: sub_name });
                //var vv = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, "sub_id");

                // $('#popItem').jqxWindow('close');

            }
            else if (rs.data.action === "update") {
                $('#gridItems').jqxGrid('setcellvalue', RowIndex, "n", rs.data.record.sup_name);
            }
            $('#popItem').jqxWindow('close');
        }
        else if (rs.data.msgHead === "FeeOutDisplayView") {
            if (rs.data.action === "cancel") {
                $("#popDisplay").css('display', 'none')
                $('#popDisplay').jqxWindow('close');
                ;
            } else if (rs.data.action === "select") {
                $("#popDisplay").css('display', 'none')
                $('#popDisplay').jqxWindow('close');
                console.log('selectDisplaySheetRows', rs.data.selectDisplaySheetRows)
                rs.data.selectDisplaySheetRows.forEach(item => {
                    selectDisplayRow(item)
                })

            }
        }
        else if (rs.data.msgHead === "AppendixPhotoEdit") {
            // 附件
            if (rs.data.action === "close") {
                appendixPhotoList = rs.data.appendix
                appendixSrcList = rs.data.src
                console.log("appendixSaved:" + appendixPhotoList)
                $("#popAppendix").css('display', 'none')
                $('#popAppendix').jqxWindow('closeWindow');
            }
        }
        else if (rs.data.msgHead === 'FeePayForSupplierForm') {
            if (rs.data.action == 'ok') {
                if (rs.data.form.supplier_id) {
                    $('#pfs_btn').text(`代厂家支付：${rs.data.form.supplier_name}`);
                    $('#incomeSheetInfo').val(JSON.stringify(rs.data.form));
                } else {
                    $('#pfs_btn').text('代厂家支付');
                    $('#incomeSheetInfo').val('');
                }
            }
            $('#popPayForSupplier').jqxWindow('close');
        }
    });
    var theme = "";
    var numberrenderer = function (row, column, value) {
        return '<div style="text-align: center; margin-top: 5px;">' + (1 + value) + '</div>';
    }
    var datafields = [];
    var data = new Array; var rowscount = 10;


    var source =
    {
        localdata: data,
        unboundmode: true,
        totalrecords: 10,
        datafields: datafields,
        updaterow: function (rowid, rowdata) {
        }
    };

    var dataAdapter = new $.jqx.dataAdapter(source);

    window.onresize = function () {
        //return;
        var h1 = document.all.divTitle.offsetHeight;
        var h2 = document.all.divHead.offsetHeight;// $("#divHead").height();
        var h3 = $("#divTail").height();
        var h4 = document.all.divButtons.offsetHeight;
        var h5 = $(window).height();
        var h6 = window.innerHeight;
        var windowWidth = window.innerWidth;
        //var h7 = document.documentElement.clientHeight;

        var h = h6 - h1 - h2 - h3 - h4 - 5;

        var rowsheight = $('#jqxgrid').jqxGrid('rowsheight');
        var headerheight = $('#jqxgrid').jqxGrid('columnsheight');
        var statusbarheight = $('#jqxgrid').jqxGrid('statusbarheight');
        var max_rows = Math.floor((h - headerheight - statusbarheight) / rowsheight);
        var now_rows = $('#jqxgrid').jqxGrid('getrows').length;
        h = max_rows * rowsheight + headerheight + statusbarheight + 2 - 50;
        $("#jqxgrid").jqxGrid(
            {
                height: h,
                width: windowWidth - 20
            });
        var fill_rows = max_rows - now_rows - 1;
        if (fill_rows > 0) {
            var addrows = new Array(); var i;
            for (i = 0; i < fill_rows; i++) {
                addrows.push({});
            }
            $('#jqxgrid').jqxGrid('addrow', null, addrows);
        }

    };
    var fixColCss = 'jqx-widget-header';
    if (theme !== '') fixColCss += ' jqx-widget-header-' + theme;
    window.GridData =
    {
        source: dataAdapter,
        showaggregates: true,
        showstatusbar: true,
        statusbarheight: 25,
        pageable: false,
        // autoheight: true,
        sortable: false,
        editable: true,
        columnsresize: true,
        ready: function () {
            $("#jqxgrid").jqxGrid('focus');
        },
        renderstatusbar: function (statusbar) {
            //var cells = statusbar.find("div");
            // $.each(cells, function () {
            // $(this).removeClass("jqx-grid-cell-pinned");
            // });

        },
        editmode: 'click',
        selectionmode: 'singlecell',// 'multiplecellsadvanced',
        hoverrow: true,
        theme: theme,
        cellhover: cellhover,
        handlekeyboardnavigation: function (event) {
            var key = event.charCode ? event.charCode : event.keyCode ? event.keyCode : 0;
            var cell = $('#jqxgrid').jqxGrid('getselectedcell');
            if (!cell) return
            if (key === 13) {
                /* var cell = $('#jqxgrid').jqxGrid('getselectedcell');
                 var row_index = $('#pop_sale_sheet').jqxGrid('getselectedrowindex');
                 var id=event.target.id;
                 var i = id.indexOf('jqxgrid');
                 var colName = ''; var ctlName = '';
                 if (i > 0) {
                     i += 7;
                     colName = id.substr(i, id.length - i);
                 }
                 else if (event.target.offsetParent) {
                     ctlName = event.target.offsetParent.id; 
                 }
                 $("#jqxgrid").jqxGrid('endcelledit', row_index, colName, false);
                 */
                addEmptyRowsAtTail(4, 20);
                cell = $('#jqxgrid').jqxGrid('getselectedcell');

                $("#jqxgrid").jqxGrid('endcelledit', cell.row, cell.column, false);
                var nextCell = getNextCellByEnterKey(cell.row, cell.column)
                let nextCol = nextCell.datafield
                if (cell.column === "fee_sub_id") {
                    $('#jqxgrid').jqxGrid('clearselection');
                    if (!nextCol) {
                        nextCol = 'fee_sub_id'
                        $('#jqxgrid').jqxGrid('selectcell', cell.row + 1, nextCol);
                    } else {
                        $('#jqxgrid').jqxGrid('selectcell', cell.row, nextCol).focus();
                    }

                }
                else if (cell.column === "fee_sub_amount") {
                    $('#jqxgrid').jqxGrid('clearselection');
                    if (!nextCol) {
                        nextCol = 'fee_sub_id'
                        $('#jqxgrid').jqxGrid('selectcell', cell.row + 1, nextCol).focus();
                    } else {
                        $('#jqxgrid').jqxGrid('selectcell', cell.row, nextCol);
                    }
                }
                else if (cell.column === "remark") {
                    $('#jqxgrid').jqxGrid('clearselection');
                    $('#jqxgrid').jqxGrid('selectcell', cell.row + 1, 'fee_sub_id');
                }
                return true;
            }
            else if (key === 27) {
                bw.toast('Pressed Esc Key.');
                return true;
            }
        },
        columns: [
            {
                text: '', sortable: false, filterable: false, editable: false, pinned: true,
                groupable: false, draggable: false, resizable: false,
                datafield: '', columntype: 'number', width: 45,
                cellclassname: fixColCss,
                cellsrenderer: pinCellsRenderer,
                renderer: leftTopCellRenderer
            },
            { text: '陈列单', datafield: 'display_id', align: 'center',hidden:true,hideOnLoad:true },
            { text: '陈列月份', datafield: 'display_month', align: 'center',hidden:true,hideOnLoad:true },
            {
                text: title, datafield: 'fee_sub_id', displayfield: 'fee_sub_name', width: '200', columntype: 'template',
                createeditor: function (row, cellvalue, editor, cellText, width, height) {
                    // construct the editor.
                    //var inputElement = $("<input/>").prependTo(editor);
                    // $("<input style='width:10px;'/><label>xx</label>").prependTo(editor);
                    //debugger;
                    // var element = $('<div id="txtItemName"> <input type= "text" style= "" /> <div id="search" ><img alt="search" width="16" height="16" src="jqwidgets/images/search_lg.png" /></div></div >');
                    var element = $('<div id="txtItemName"></div>');
                    editor.append(element);

                    var inputElement = editor.find('div')[0];

                    var datafields = new Array({ datafield: "sub_name", text: datafldTitle, width: 120 },
                        { datafield: "sub_code", text: "编码", width: 100 }
                    )

                    $(inputElement).jqxInput({
                        placeHolder: "助记码/名称", height: height, width: width,
                        borderShape: "none",
                        buttonUsage: 'list',
                        showHeader: true,
                        dropDownHeight: 160,
                        displayMember: "sub_name",
                        valueMember: "sub_id",
                        datafields: datafields,
                        searchFields: ["py_str", "sub_name", "sub_code"],
                        //searchMode: 'none',
                        maxRecords: 9,
                        source: function (query, response) {
                            $.ajax({
                                url: '/AppApi/AppSheetFeeOut/Load',
                                type: 'GET',
                                contentType: 'application/json',
                                data: { operKey: g_operKey, sheetID: '' },
                                success: function (data) {
                                    if (data.result === 'OK') {
                                        if (forOutOrIn == "false") response(data.feeIn, null, query == null || query == '');
                                        else response(data.feeOut, null, query==null || query=='');
                                    }
                                }
                            });
                        },
                        renderer: function (subValue, inputValue) {
                            // debugger;
                            var terms = inputValue.split(/,\s*/);
                            // remove the current input
                            terms.pop();
                            // add the selected item
                            terms.push(subValue);
                            // add placeholder to get the comma-and-space at the end

                            // terms.push("");
                            //var value = terms.join(", ");
                            //return terms;
                            return subValue;
                        }, onButtonClick: function () {
                           // window.curRowIndex = row;
                          //  $('#popItem').jqxWindow('open');
                          //  $("#popItem").jqxWindow('setContent', `<iframe src="/BaseInfo/FeeOutsView?forSelect=1&operKey=${g_operKey}&" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);

                        }

                    });
                    $(inputElement).on('optionSelected',
                        function (a, b) {
                            //  debugger;
                            var value = $(inputElement).val();
                            var id = '';
                            console.log('value:' + JSON.stringify(value));
                            if (value.value)
                                id = value.value;
                            var name = '';
                            if (value.label)
                                name = value.label;
                        });

                    // debugger;

                    }, 
                    align: 'center',
                    initeditor: function (row, cellvalue, editor, celltext, pressedkey) {
                        // set the editor's current value. The callback is called each time the editor is displayed.
                        var inputField = editor.find('input');
                        if (pressedkey) {
                            inputField.val(pressedkey);
                            inputField.jqxInput('selectLast');
                        }
                        else {
                            inputField.val({ value: cellvalue, label: celltext });
                            inputField[0].value = celltext;
                            inputField.jqxInput('selectAll');
                        }
                    },
                    geteditorvalue: function (row, cellvalue, editor) {
                        // return the editor's value.
                        var v = editor.find('input').val();
                        return v;
                    }
                },
            {
                text: '金额', datafield: 'fee_sub_amount', width: '300', align: 'center', cellsalign: 'right', aggregates: [{
                    'xj':
                        function (aggregatedValue, currentValue) {
                            return Number(toMoney(  Number(toMoney(aggregatedValue || 0)) + Number(toMoney(currentValue || 0))  ))
                        }
                }],
                aggregatesrenderer: function (aggregates, column, element, summaryData) {
                    var renderstring = "<div class='jqx-widget-content jqx-widget-content-" + theme + "' style='float: left; width: 100%; height: 100%; '>";
                    $.each(aggregates, function (key, value) {
                        renderstring += '<div style="position: relative; margin: 6px; text-align: right; overflow: hidden;">' + value + '</div>';
                    });
                    renderstring += "</div>";
                    return renderstring;
                },
                 cellbeginedit: function(row, datafield, columntype, value, c, d, e) {
                     var fee_sub_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'fee_sub_id');
                     if (!fee_sub_id) return false;
                 }
            },
            // {
            //     text: '陈列可用金额', datafield: 'display_sub_amount', align: 'center', cellsalign: 'right', width: '20%', hidden: !bForFeeOut, aggregates: [{
            //         'xj':
            //             function (aggregatedValue, currentValue) {
            //                 return Number(aggregatedValue || 0) + Number(currentValue || 0);
            //             }
            //     }],
            //     aggregatesrenderer: function (aggregates, column, element, summaryData) {
            //         var renderstring = "<div class='jqx-widget-content jqx-widget-content-" + theme + "' style='float: left; width: 100%; height: 100%; '>";
            //         $.each(aggregates, function (key, value) {
            //             renderstring += '<div style="position: relative; margin: 6px; text-align: right; overflow: hidden;">' + value + '</div>';
            //         });
            //         renderstring += "</div>";
            //         return renderstring;
            //     },
            //     cellbeginedit: function (row, datafield, columntype, value) {
            //         return false;
            //     }
            // },
            { text: '备注', datafield: 'remark', align: 'center' }
                 
            ]
    };

    adjustColumnsBySetting()

    $("#jqxgrid").jqxGrid(
        GridData
    );
    loadSheetRows(sheetRows);

    $("#jqxgrid").on('cellendedit', function (event) {
        var args = event.args;
        var colName = args.datafield;
        var rowIndex = args.rowindex;
        var cellValue = args.value;
        var oldValue = args.oldvalue;

        setTimeout(function () {
            if (oldvalue === cellValue) return;
            var fee_sub_id = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'fee_sub_id');
            if (!fee_sub_id) {
                return;
            }
            if (colName === 'fee_sub_id') {
                /*$.ajax({
                    url: '/api/FeeOutSheet/GetSubInfo',
                    type: 'GET',
                    contentType: 'application/json',
                    data: { operKey: g_operKey, item_id: item_id },
                    success: function (data) {
                        if (data.result === 'OK') {
                            if (!window.g_queriedItems) window.g_queriedItems = {};
                            window.g_queriedItems[item_id] = data.item;
                        }
                    }
                });*/
            }
            else if (colName === 'fee_sub_amount') {
                if (myIsNumber(cellValue)) {
                    $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'fee_sub_amount', toMoney(cellValue));//小数修正

                    // if (fee_sub_amount) { 
                    //  fee_sub_amount = fee_sub_amount.toFixed(2);
                    //   $('#jqxgrid').jqxGrid('setcellvalue', fee_sub_amount);
                    //  }
                    updateTotalAmount();
                }
            }
        }, 100);
       
    });

    $("#btnClose").on('click', function () {
        window.parent.closeTab(window);

    });



    function GetSheetData() {
        var msg = "";
        var formData = getFormData();
        if (window.g_companySetting) {
            formData.company_name = window.g_companySetting.companyName
            formData.company_tel = window.g_companySetting.contactTel
            formData.company_address = window.g_companySetting.companyAddress
        }
        formData.OperKey = g_operKey;
        var rows = $('#jqxgrid').jqxGrid('getrows');
        var sheetRows = new Array;
        for (var i = 0; i < rows.length; i++) {
            var sheetRow = {};// new myJXC.CSheetRow();
            var row = $('#jqxgrid').jqxGrid('getrowdata', i);
            if (row.fee_sub_id) {
                for (var fld in row) {
                    sheetRow[fld] = row[fld];
                }
                
                if (!parseFloat(sheetRow.fee_sub_amount)) {
                    msg = '请输入' + sheetRow.fee_sub_name + '的金额'; break;
                }
                sheetRows[i] = sheetRow;
            }
        }
        if (msg) return { result: 'Error', msg: msg }

        let approve_time = $('#approve_time').text();
        if (!approve_time && window.dispAccounts) {
            window.dispAccounts.forEach((acct) => {
                var subAmt = 0;
                sheetRows.forEach((row) => {
                    if (row.display_id && acct.sub_id == row.fee_sub_id) subAmt += parseFloat(row.fee_sub_amount);
                })
                if ((parseFloat(subAmt) - parseFloat(acct.disp_amount)) > 0.01) {
                    msg = acct.sub_name + '的金额不能大于可用金额' + acct.disp_amount;
                    return { result: 'Error', msg: msg }
                }
            })
        }
        if (!formData.payway1_amount) formData.payway1_amount = 0;//处理支付金额为空时，后端接口类型转换报错
        if (!formData.payway2_amount) formData.payway2_amount = 0;
        formData.now_pay_amount = parseFloat(formData.payway1_amount) + parseFloat(formData.payway2_amount);
        formData.paid_amount = formData.now_pay_amount;
        formData.disc_amount = formData.now_disc_amount;
        formData.no_disc_amount = formData.total_amount - formData.disc_amount
        formData.left_amount = toMoney(formData.no_disc_amount - formData.payway1_amount - formData.payway2_amount);

        // 2024.04.11 temp-fix: 费用支出单打印读取不到支付方式
        // * 修正方法同`2024.03.12 temp-fix`
        if (formData.payway1_amount) {
            formData.payway1 = formData.payway1_name + ":" + formData.payway1_amount
        }
        if (formData.payway2_amount) {
            formData.payway2 = formData.payway2_name + ":" + formData.payway2_amount
        }

        if (Math.abs(formData.total_amount - formData.now_pay_amount - formData.disc_amount - formData.left_amount)>0.01) {
            msg = "支付金额与欠款金额的合计与总额不相等";
            return { result: 'Error', msg: msg };
        }

        if (parseFloat(formData.payway2_amount) != 0 && !formData.payway2_id) {
            msg = '请选择第二种支付方式';
            return { result: 'Error', msg: msg }
        }
        // 附件图片处理
        debugger
        console.log('readItemImages started');
        let images = { "photos": [] };

        if (appendixSrcList) {
            for (let img of appendixSrcList) {

                images.photos.push(img)

            }
        }
       
        formData.appendix_photos =  JSON.stringify(images); // Activate infact.
        $('#appendix_photos').val(JSON.stringify(images)); // Save for possible use.

        

        formData.SheetRows = sheetRows;
        var result = msg ? 'Error' : "OK";
        return { result: result, msg: msg, sheet: formData };
    }
    $("#btnSave").on('click', function () {

        var res = GetSheetData();
        if (res.result === "Error") {
            bw.toast(res.msg, 5000); return;
        }
        var sheet = res.sheet;

        $.ajax({
            url: '/api/FeeOutSheet/Save',
            type: 'POST',
            contentType: 'application/json',
            // data: JSON.stringify({sheet_id:'1',supcust_id:'2'}),
            data: JSON.stringify(sheet),
            success: function (data) {
                if (data.result === 'OK') {
                    $('#sheet_id').val(data.sheet_id);
                    $('#sheet_no').text(data.sheet_no);
                    bw.toast('保存成功', 3000)

                    updateSheetState();
                    //$("#btnDelete").css('display', 'inline-block');
                    //$("#btnDelete").attr('disabled', false);
                    //$('#btnRed').css('display', 'none');
                    // var action = 'update';
                    // if (m_bNewRecord) {
                    //     action = 'add';
                    // }
                    // var msg = { msgHead: '~msgHead', action: action, record: data.record };
                    //window.parent.postMessage(msg, '*');
                }
                else {
                    bw.toast(data.msg, 3000);
                }
            },
            error: function (xhr) {
                console.log("返回响应信息：" + xhr.responseText);
            }
        });
    });


    $("#btnDelete").on('click', function () {
        var res = GetSheetData();
        if (res.result === "Error") {
            bw.toast(res.msg, 5000); return;
        }
        var sheet = res.sheet;
        jConfirm('确定要删除本单据吗？', function () {
            $.ajax({
                url: '/api/FeeOutSheet/Delete',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ operKey: g_operKey, sheet_id: sheet.sheet_id }),
                success: function (data) {
                    if (data.result === 'OK') {
                        //$("#btnApprove").attr('disabled', true);
                        $("#btnSave").attr('disabled', true);
                        $("#btnApprove").attr('disabled', true);
                        $("#btnDelete").attr('disabled', true);
                        $("#btnPrint").attr('disabled', true);
                        bw.toast('删除成功,即将关闭窗口', 3000);
                        setTimeout(function () {
                            window.parent.closeTab(window);
                        }, 2000);

                    }
                    else {
                        bw.toast(data.msg, 3000);
                    }
                },
                error: function (xhr) {
                    console.log("返回响应信息：" + xhr.responseText);
                }
            });
        }, "");

    });

    $("#btnAdd").on('click', function () {
        var sheetType = $('#sheet_type').val();
        if (sheetType == 'SHEET_FEE_OUT') window.parent.newTabPage('费用支出单', `Sheets/FeeOutSheet?forOutOrIn=true`);
        if (sheetType == 'SHEET_FEE_IN') window.parent.newTabPage('其他收入单', `Sheets/FeeOutSheet?forOutOrIn=false`);
    })

    $("#btnApprove").on('click', function () {

        jConfirm('确定要审核吗？', function () {
            var res = GetSheetData();
            if (res.result === "Error") {
                bw.toast(res.msg, 5000); return;
            }
            var sheet = res.sheet;

            $('#btnApprove').attr('disabled', true);

            $.ajax({
                url: '/api/FeeOutSheet/SaveAndApprove',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(sheet),
                success: function (data) {
                    if (data.result === 'OK') {
                        $('#sheet_id').val(data.sheet_id);
                        $('#sheet_no').text(data.sheet_no);
                        $('#approve_time').text(data.approve_time);
                        updateSheetState()
                        bw.toast('审核成功', 3000);
                        $('#btnSeleteDisp').attr('disabled', true);
                        // 附件窗口设置
                        window.appendixApproved = true

                        //代厂家支付信息
                        if (data.fee_to_income_sheet_id) {
                            let incomeSheetInfo = $('#incomeSheetInfo').val() ? JSON.parse($('#incomeSheetInfo').val()) : '';
                            if (incomeSheetInfo) {
                                incomeSheetInfo.income_sheet_id = data.fee_to_income_sheet_id;
                                incomeSheetInfo.income_sheet_no = data.fee_to_income_sheet_no;
                                $('#incomeSheetInfo').val(JSON.stringify(incomeSheetInfo));
                            }
                        }
                    }
                    else {
                        $('#btnApprove').attr('disabled', false);

                        bw.toast(data.msg, 3000);
                    }
                },
                error: function (xhr) {
                    console.log("返回响应信息：" + xhr.responseText);
                }
            });
        }, "");
    });
    
    $("#btnPrint").on('click', function () {

        var res = GetSheetData();
        if (res.result === "Error") {
            bw.toast(res.msg, 5000); return;
        }
        var sheetType = $('#sheet_type').val();
        var clientID = $('#supcust_id').jqxInput('val');
        if (clientID) clientID = clientID.value;
        $.ajax({
            url: '/api/PrintTemplate/GetTemplateToUse',
            type: 'GET',
            contentType: 'application/json',
            data: {
                operKey: g_operKey,
                sheetType: sheetType,
                clientID: clientID
            },
            success: function (data) {
                if (data.result === 'OK') {
                 
                    if (data.templateList.length == 0) {
                        // bw.toast("没有可用打印模板", 5000);  
                    }
                    var tmp = data.templateList[0];
                    tmp = JSON.parse(tmp.template_content);

                    var container = window.parent.CefGlue

                    if (!container)
                        container = window.parent

                    if (!container.printSheetByTemplate) {
                        bw.toast('在客户端程序中才可以打印', 3000)
                        return
                    }
                    var smallUnitBarcode = document.all.ckPrintSmallBarcode.checked
                    var sheet_id = $('#sheet_id').val();
                    $.ajax({
                        url: '/api/FeeOutSheet/GetSheetToPrint',
                        type: 'GET',
                        contentType: 'application/json',
                        data: {
                            operKey: g_operKey,
                            sheet_id: sheet_id,
                            smallUnitBarcode: smallUnitBarcode,
                            //printTemplate: JSON.stringify(printTemplate)

                        },
                        success: function (data) {
                            if (data.result === 'OK') {
                                var sheet = data.sheet


                                var container = window.parent.CEFPrinter
                                if (!container)
                                    container = window.parent.CefGlue

                                if (!container)
                                    container = window.parent
                                window.parent.g_SheetsWindowForPrint = window.srcWindow
                                container.printSheetByTemplate(sheet, tmp, true, data.cloudPrinters, data.variables)
                            }
                            else {
                                bw.toast(data.msg, 3000)
                            }
                        },
                        error: function (xhr) {
                            bw.toast('获取单据信息失败')
                            console.log("返回响应信息：" + xhr.responseText)
                        }
                    }) 
                   
                    

                }
                else {
                    bw.toast(data.msg, 3000);
                }
            },
            error: function (xhr) {
                console.log("返回响应信息：" + xhr.responseText);
            }
        });
    });

    $("#btnSeleteDisp").on('click', function () {
        var allowAdvanceDisplayFee = false
        var canGiveDisplayCrossDept = false
        if (JsonOperRights)
        {
            JsonOperRightsOrig = JsonOperRightsOrig.replace(/&quot;/g,'"')
            JsonOperRightsOrig = JsonOperRightsOrig.replace(/&#xA;/g,'')
            JsonOperRightsOrig = JsonOperRightsOrig.replace(/&#xD;/g,'')
            var operRights = JSON.parse(JsonOperRightsOrig)
            if (operRights != null && operRights.delicacy != null) {
                if( operRights.delicacy.allowAdvanceDisplayFee) {
                    allowAdvanceDisplayFee = operRights.delicacy.allowAdvanceDisplayFee.value
                }
                if (operRights.delicacy.giveDisplayCrossDept) {
                    canGiveDisplayCrossDept = operRights.delicacy.giveDisplayCrossDept.value
                }
            } 
        }

        
        
        $("#popDisplay").css('display', 'inline-block')
        $("#popDisplay").css('z-index', '9999')
        $('#popDisplay').jqxWindow('open');
        $("#popDisplay").jqxWindow('setContent', `<iframe src="/Sheets/FeeOutSheetDisplayAgreement?&operKey=${g_operKey}&operDepartPath=${OperDepartPath}&canGiveDisplayCrossDept=${canGiveDisplayCrossDept}&supcustID=${$('#supcust_id').val().value}&allowAdvanceDisplayFee=${allowAdvanceDisplayFee}&operID=${OperID}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
    });
    window.appendixWindowCreated = false
    window.appendixApproved = approve_time ? true : false
    let iframeElement = null
    $("#btnAppendix").on('click', function () {
        // 附件按钮触发
        $('#popAppendix').jqxWindow('open')
      
        
        // var clientView = 'ClientsView'
        if (!window.appendixWindowCreated) {
            debugger
            let approve_time = $('#approve_time').text();
            let src = `/BaseInfo/AppendixPhotoEdit?operKey=${g_operKey}`
            if (approve_time) { src = `/BaseInfo/AppendixPhotoEdit?operKey=${g_operKey}&editable=false` }
            $("#popAppendix").jqxWindow('setContent', `<iframe id="iframeAppendix" src="${src}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
            window.appendixWindowCreated = true
            $('#iframeAppendix').on('load', function () {
                // 获取 iframe 元素
                iframeElement = document.getElementById('iframeAppendix');
                // 构造要发送的数据
                let photo = $("#appendix_photos").val()
                debugger
                if (photo === "" || photo === undefined || photo === null) {
                    return
                } else {
                    // let href = Href + "/"
                    let href = Href + "/uploads"
                    // 向 iframe 发送数据
                    iframeElement.contentWindow.postMessage({ msg: "loadPhoto", photo: photo, href: href }, '*');
                }
            });

        } else {
            if (window.appendixApproved) {

                // 获取 iframe 元素
                // let iframeElement = document.getElementById('iframeAppendix');
                // 构造要发送的数据

                iframeElement.contentWindow.postMessage({ msg: "setDisable" }, '*');


            }
            if (!window.appendixApproved && window.appendixWindowCreated) {
                iframeElement.contentWindow.postMessage({ msg: "setAble" }, '*');
            }
        }
        
       
        // $("#popClient").jqxWindow('setContent', `<iframe src="/BaseInfo/ClientsView?forSelect=1&forAll=true&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);


    })
    $("#popupWindow").jqxWindow({
        width: 410, height: 510, resizable: true, theme: 'Redmond', isModal: true, autoOpen: false, cancelButton: $("#cancelButton"), modalOpacity: 0.01
    });


    function getClientAccountInfo(supcust_id) {

        $('#div_get_account').empty()
        $.ajax({
            url: '/api/FeeOutSheet/GetClientAccountInfo',
            type: 'GET',
            contentType: 'application/json',
            data: {
                operKey: g_operKey,
                supcust_id: supcust_id
            },
            success: function (data) {
                if (data.result === 'OK') {
                    var dispLabel = "";
                    t.style.display = 'block';
                    $('#mobile').val(data.mobile)
                    $('#sup_addr').val(data.sup_addr)
                    $('#boss_name').val(data.boss_name)                    
                    $('#acct_supcust_id').val(data.acct_cust_id)

                    window.dispAccounts = data.disp
                    if (data.disp.length > 0) {
                        for (var i = 0; i < data.disp.length; i++) {
                            //var sub_id = data.disp[i].sub_id
                            var sub_name = data.disp[i].sub_name
                            var disp_amount = data.disp[i].disp_amount
                        }
                    }
                    if (dispLabel) $('#div_get_account').append(dispLabel)

                }
                else {
                    bw.toast(data.msg, 3000);
                }
            },
            error: function (xhr) {
                console.log("返回响应信息：" + xhr.responseText);
            }
        });
    }
    $("#supcust_id").on('change', function () {
        debugger
        var supcustID = $('#supcust_id').val().value;
        var t = document.getElementById('div_left_amount')
        if (supcustID) {
            t.style.display = 'block';
        } else {
            t.style.display = 'none';
        }
    }
    )
    $("#supcust_id").on('input', function () {
    
        var supcustID = $('#supcust_id').val().value;
        var t = document.getElementById('div_left_amount')
        if (supcustID) {
            t.style.display = 'block';
        } else {
            t.style.display = 'none';

        }
    }
    )
    $("#choose").click(function () {
        var cell_sheet = $('#jqxgrid').jqxGrid('getselectedcell');
        var row_index = $('#pop_sale_sheet').jqxGrid('getselectedrowindex');
        var rows = $('#pop_sale_sheet').jqxGrid('getrowdatabyid', row_index);

        $("#jqxgrid").jqxGrid('setcellvaluebyid', cell_sheet.row, "item_id", rows.item_id);
        $("#jqxgrid").jqxGrid('setcellvaluebyid', cell_sheet.row, "item_name", rows.item_name);
        $("#jqxgrid").jqxGrid('setcellvaluebyid', cell_sheet.row, "unit_no", rows.unit_no);
        $("#jqxgrid").jqxGrid('setcellvaluebyid', cell_sheet.row, "real_price", rows.price);
        $("#jqxgrid").jqxGrid('endrowedit', cell_sheet.row, false);
        $("#pop_sale_sheet").jqxGrid('endrowedit', row_index, false);
        $("#popupWindow").jqxWindow('close');
    });
    $("#popupWindow").on('open', function () {
    });
    $("#pop_sale_sheet").on('celldoubleclick', function () {
        var cell_sheet = $('#jqxgrid').jqxGrid('getselectedcell');
        var row_index = $('#pop_sale_sheet').jqxGrid('getselectedrowindex');
        var rows = $('#pop_sale_sheet').jqxGrid('getrowdatabyid', row_index);

        $("#jqxgrid").jqxGrid('setcellvaluebyid', cell_sheet.row, "item_id", rows.item_id);
        $("#jqxgrid").jqxGrid('setcellvaluebyid', cell_sheet.row, "item_name", rows.item_name);
        $("#jqxgrid").jqxGrid('setcellvaluebyid', cell_sheet.row, "unit_no", rows.unit_no);
        $("#jqxgrid").jqxGrid('setcellvaluebyid', cell_sheet.row, "real_price", rows.price);
        $("#popupWindow").jqxWindow('close');
    });

    window.onCopySheet = function () {
        // 复制页面时可以自定义的一些方法
        debugger
        console.log('wozaifuzhi')
        window.appendixWindowCreated = false
        window.appendixApproved = false
    }
    //setTimeout(function () { 
    window.onresize();
    // }, 4000);            
    window.updateTotalAmount=function() {
        var rows = $('#jqxgrid').jqxGrid('getrows');
        var total_amt = 0;
        for (var i = 0; i < rows.length; i++) {
            var sheetRow = {};// new myJXC.CSheetRow();
            var row = $('#jqxgrid').jqxGrid('getrowdata', i);
            var amt = row.fee_sub_amount;
            if (parseFloat(amt)) {
                total_amt += parseFloat(amt || 0);
            }
        }
        var disc_amt = $('#now_disc_amount').jqxInput('val');
        if (!disc_amt) disc_amt = 0;
        var no_disc_amount = total_amt - disc_amt;
        $('#total_amount').jqxInput('val', '');
        $('#total_amount').jqxInput('val', toMoney(total_amt));
        $('#no_disc_amount').jqxInput('val', toMoney(no_disc_amount));
        updatePayAmount();
    }
    var ignoreChange = false;
    function updatePayAmount(fromPayWay1) {
        var total_amount = $('#total_amount').jqxInput('val');
        var now_disc_amount = $('#now_disc_amount').jqxInput('val');
        var no_disc_amount = total_amount - now_disc_amount;
        ignoreChange = true;

        var payway1_amount = $('#payway1_amount').jqxInput('val');
        var payway2_amount = $('#payway2_amount').jqxInput('val');
        var left_amount = $('#left_amount').jqxInput('val');
        var supcust_id = $('#supcust_id').jqxInput('val');
        $('#no_disc_amount').jqxInput('val', toMoney(no_disc_amount));
        if (!fromPayWay1)payway1_amount = toMoney(no_disc_amount - payway2_amount - left_amount)
        if (supcust_id != "") {
            left_amount = toMoney(no_disc_amount - payway1_amount - payway2_amount);
        }
        $('#payway1_amount').jqxInput('val', payway1_amount);
        $('#left_amount').jqxInput('val', left_amount);
        ignoreChange = false;
    }

     

    $('#now_disc_amount').on('input', function () {
        if (ignoreChange) return;
        updatePayAmount();
    });
    $('#no_disc_amount').on('input', function () {
        if (ignoreChange) return;
        updatePayAmount();
    });

    $('#left_amount').on('change', function () {
        updatePayAmount();
    });

    $('#payway1_amount').on('input', function () {
        if (ignoreChange) return;

        updatePayAmount(true);
    });
    $('#payway1_amount').on('change', function () {
        if (ignoreChange) return;

        updatePayAmount(true);
    });

    $('#payway2_amount').on('input', function () {
        updatePayAmount();
    });
    $('#payway1_id').on('optionSelected', (a, b) => {
        var payway = $('#payway1_id').jqxInput('val')
        $('#payway1_type').val(payway.sub_type)
    });
    $('#payway2_id').on('optionSelected', (a, b) => {
        var payway = $('#payway2_id').jqxInput('val')
        $('#payway2_type').val(payway.sub_type)
    });

    var fee_appo_sheet_id = $('#fee_appo_sheet_id').val();
    if (fee_appo_sheet_id) {
        $('#fee_appo_sheet_no').on('click', function () {
            window.parent.newTabPage('采购费用分摊单', `/Sheets/FeeApportionSheet?sheet_id=${fee_appo_sheet_id}`);
        });
    } else {
        $('#fee_appo_sheet_no').parent().css('display', 'none');
    }

    var pay_for_supplier_fee_sheet_id = $('#pay_for_supplier_fee_sheet_id').val();
    if (pay_for_supplier_fee_sheet_id) {
        $('#pay_for_supplier_fee_sheet_no').on('click', function () {
            window.parent.newTabPage('费用支出单', `/Sheets/FeeOutSheet?sheet_id=${pay_for_supplier_fee_sheet_id}`);
        });
    } else {
        $('#pay_for_supplier_fee_sheet_no').parent().css('display', 'none');
    }

    window.btnImport_click = function () {
        $('#popImport').jqxWindow('open')
    }
    window.btnSelectImportFile_click = function () { // 点击选择文件后 打开选择Excel的窗口
        onSelectFile('fileImportExcel', 'excel', 'labelForImportFileName')
    }
    window.btnImportConfirm_click = function () { // 点击导入后
        var file = $("#fileImportExcel")
        if (file.length == 0) {
            bw.toast('请选择EXCEL文件')
            return
        }
        if (file[0].files.length == 0) {
            bw.toast('请选择一个EXCEL文件')
            return
        }
        file = file[0].files[0]
        onStartInvent(true, file)
    }

    window.onStartInvent = function (bImportFile, file) {
        // 检查文件类型，确保是 Excel 文件
        if (file && !file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
            bw.toast('请选择正确的 Excel 文件');
            return;
        }

        var reader = new FileReader();

        reader.onload = function (e) {
            console.log("文件加载完成");
            console.log(e.target.result);
            var data = e.target.result;
            //var rows = $('#jqxgrid').jqxGrid('getrows');

            // 解析Excel文件
            try {
                var workbook = XLSX.read(data, { type: 'array' });
                var worksheet = workbook.Sheets[workbook.SheetNames[0]]; // 获取第一个工作表

                // 将工作表转换为JSON格式数据（以行为单位）
                var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }); // 跳过标题行


                function loadData(callback) {//如果直接将excel中的内容填充到表单 只有sub_name 没有sub_id
                    $.ajax({
                        url: '/AppApi/AppSheetFeeOut/Load',
                        type: 'GET',
                        contentType: 'application/json',
                        data: {
                            operKey: g_operKey,
                            sheetID: ''
                        },
                        success: function (data) {
                            if (data.result === 'OK') {
                                console.log('成功获取数据:', data);
                                callback(null, data);  // 通过回调函数传递数据
                            } else {
                                console.log('获取数据失败:', data);
                                callback('获取数据失败', null);
                            }
                        },
                        error: function (xhr, status, error) {
                            console.error('请求失败:', error);
                            callback(error, null);
                        }
                    });
                }

                // 使用回调函数
                loadData(function (err, data) {
                    if (err) {
                        console.error(err);
                        return;
                    }

                    // 在这里使用获取到的数据
                    console.log('从回调中获取的数据:', data);

                    if (jsonData.length > 1) {
                        for (var i = 1; i < jsonData.length; i++) {
                            var expenseType = jsonData[i][0];
                            var amount = jsonData[i][1];
                            var notes = jsonData[i][2];
                            var rowIndex = i - 1;

                            var result = data.feeOut.find(item => item.sub_name === expenseType);

                            // 如果未找到对应的 sub_name
                            if (!result) {
                                bw.toast(`费用名称设置错误: ${expenseType}`);
                                $("#popImport").jqxWindow('close');
                                return; // 直接返回，终止导入操作
                            }
                            var subId = result ? result.sub_id : null;
                            
                            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'fee_sub_name', expenseType);
                            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'fee_sub_id', subId);
                            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'fee_sub_amount', amount);
                            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'remark', notes);

                            // 更新合计金额
                            updateTotalAmount();
                        }
                        // 导入完关闭窗口
                        $("#popImport").jqxWindow('close');
                    } else {
                        bw.toast('Excel 文件格式不正确，数据缺失');
                    }
                });
                

            } catch (error) {
                console.error("文件解析出错:", error);
                bw.toast('解析 Excel 文件失败');
            }
        };

        // 读取文件
        if (file) {
            reader.readAsArrayBuffer(file); // 使用 readAsArrayBuffer 来读取 Excel 文件
        } else {
            bw.toast('请选择一个EXCEL文件');
        }

        // 错误处理
        reader.onerror = function (error) {
            console.error("文件读取失败:", error);
        };

    }
}

function switchTab(type) {
    $('.tab').removeClass('active').css("border-bottom", "2px solid transparent");
    $(`#tab${type.charAt(0).toUpperCase() + type.slice(1)}`).addClass('active').css("border-bottom", "2px solid #1890ff");
    
    if (type === 'client') {
        $("#clientContent").html(`<iframe src="/BaseInfo/FeeUnitsView?forSelect=1&withClients=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
    } else {
        $("#clientContent").html(`<iframe src="/BaseInfo/SuppliersView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
    }
}
function ShowPayForSupplierBox() {
    //if ($('#approve_time').val()) {
        //window.parent.newTabPage('其他收入单', `/Sheets/FeeOutSheet?forOutOrIn=false&supplierFeeSheetId=${$('#sheet_id').val()}`);
    //} else {
        $('#popPayForSupplier').jqxWindow('open');
        $("#popPayForSupplier").jqxWindow('setContent', `<iframe src="/Sheets/FeePayForSupplierForm?operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
    //}
}
