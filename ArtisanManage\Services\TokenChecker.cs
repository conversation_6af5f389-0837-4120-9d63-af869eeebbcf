﻿using ArtisanManage.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace ArtisanManage.Services
{


    public class QuickAccessChecker
    {
        public class UserAddr
        {
            public long AccessTime = 0;
            public int ContinuiousCount = 0;
            public Dictionary<string, DateTime> UserActions = new Dictionary<string, DateTime>();
        }
        public class UserAction {
            //public string name
        }
        public static Dictionary<string, UserAddr> g_UserAddr = new Dictionary<string, UserAddr>();
        public static bool CheckAccessValid(HttpContext httpContext, string companyID)
        {
            string ip = httpContext.Connection.RemoteIpAddress.ToString();
            lock (g_UserAddr)
            {
                g_UserAddr.TryGetValue(ip, out UserAddr addr);
                if (addr == null)
                {
                    addr = new UserAddr();
                    g_UserAddr.Add(ip,addr);
                }
                else
                {
                    long ms = DateTime.Now.Ticks - addr.AccessTime;
                     
                    string msg = "";
                    
                    /*if (ms < 100)
                    { 
                        msg = $"too quick at {ip} {httpContext.Request.Path}";
                    }*/
                    if (ms < 1000)
                    {
                        addr.ContinuiousCount++;
                    }
                    else addr.ContinuiousCount = 0;

                    if (addr.ContinuiousCount > 15)
                    { 
                        msg = $"too many continious access at {ip} {httpContext.Request.Path}";

                    }
                    if (msg!="")
                    {
                        MyLogger.LogMsg(msg, companyID);
                        return false;
                    }
                }
                addr.AccessTime = DateTime.Now.Ticks;
            }
            return true;            
        }
    }
    public class TokenChecker
    {
        public static Dictionary<string, string> g_dicOperators = new Dictionary<string, string>();
        
        public static async Task UpdateOperPwd(string operID,string pwd)
        {
            string pwKey = "pw" + operID;
            await   RedisHelper.SetAsync(pwKey, pwd, 3600 * 24);

            //lock (g_dicOperators)
           // {
            //    g_dicOperators[operID] = pwd;
           // } 
        }
        public static async Task RemoveOper(string operID)
        {
            string pwKey = "pw" + operID;
            await RedisHelper.SetAsync(pwKey, "invalid", 1);
            /*lock (g_dicOperators)
            {
                if (g_dicOperators.ContainsKey(operID))
                    g_dicOperators.Remove(operID);
            }*/ 
        }
        public static async Task  LoadAllUsers()
        {
            g_dicOperators.Clear();
            using (CMySbCommand cmd = CMySbCommand.GetCommand("Report"))
            {
                cmd.ActiveDatabase = "Report";
                cmd.CommandText = "select oper_id,oper_pw from g_operator where ((can_login or can_login is null) and (oper_status=1 or oper_status is null))";
                CMySbDataReader dr = await cmd.ExecuteReaderAsync();
                while (dr.Read())
                {
                    string oper_id = CPubVars.GetTextFromDr(dr, "oper_id");
                    string oper_pw = CPubVars.GetTextFromDr(dr, "oper_pw");
                    g_dicOperators.Add(oper_id, oper_pw);
                }
                dr.Close();
                
            } 
        }
        private readonly RequestDelegate next;
        private readonly string[] whiteList = new string[]
        {
            "/uploads","/report/bmap",
            "/appapi/Main/Test",
            "/appapi/Main/GCCollect",
            "/appapi/login","/appapi/GetOperRights","/login","/","/download","/errorMessage",
            "/swagger","/index","/api/OpenAccount","/setting/OpenAccount","/setting/FixData","/setting/FixItemAttr",
			"/openapi", // 公开API. openapi提供的函数均应可以随意访问,如有涉密内容请自行加密
            "/openapi/CloudPrintCallBack/GetCallBack_Ym","/openapi/CloudPrintCallBack/GetCallBack_Sw", // 云打印回调相关
            "/openapi/EmartAuth/Pdd_ReceiveAuthData","/loginfrompdd","/appapi/loginfrompdd", // 拼多多电商对接相关
            "/loginfromtb","/appapi/loginfromtb", // 淘宝电商对接相关
			"/api/cashier/login", "/LoginFromCashier", // 收银系统登录接口
            "/yingjiang-cashier", "/yingjiang-cashier/*", // 收银系统页面          
            "/AppApi/Login/LoginAndroidDumpReport",
			"/WeChat","/WeChat/*","/YingJiangPrivacy","/WorkOrder", "/WorkOrder/*","/Backstage","/Backstage/*","/WeChatOpen","/WeChatOpen/*",
            "/common","/common/*", "/MallApi", "/MallApi/*","/MallMini","/MallMini/*","/ProdMiniApi","/ProdMiniApi/*",
            "/user_guide","/user_guide/*",
            "/api/diagscenario",
            "/api/Login/GetQRCode",
            "/api/Login/GetScanResult",
             "/apple-app-site-association",
              "/seller.png",
            "/.env",  //centos 8下面一启动就会自动打开这个页面
            "/Document/cw/UserManual/index.html", //财务使用手册
			 "/PrintTemplate/img/*",
			 "/ai_customer","/ai_customer/*"

		};
        private readonly string[] blackList = new string[] 
        { 
            //"*********" 
        };

        public TokenChecker(RequestDelegate requestDelegate)
        {
            next = requestDelegate;
        }

        public async Task InvokeAsync(HttpContext httpContext)
        {
            if (blackList.Any(x => x == httpContext.Connection.RemoteIpAddress.ToString())) return;
            Thread.CurrentThread.CurrentCulture = new CultureInfo("zh-CN", true)//统一日期格式
            {
                DateTimeFormat = { ShortDatePattern = "yyyy-MM-dd", FullDateTimePattern = "yyyy-MM-dd HH:mm:ss", LongTimePattern = "HH:mm:ss" }
            };
         
            var Request = httpContext.Request;
#if DEBUG
            // var db = Request.Query["db"].ToString();
            // if (db.IsValid()) { CPubVars.ConnString = Startup.Configuration.GetSection("ConnectionString")[db]; return; };
#endif

            if(Request.Method == "OPTIONS")
            {
                await next(httpContext);
            }
            string invalidBody = "";
            var operKey = Request.Query["operKey"].ToString();

           

            string body = ""; 
            if (!operKey.IsValid() && Request.Method=="POST" && Request.ContentType.Contains("json",StringComparison.OrdinalIgnoreCase))
            {  
                Request.EnableBuffering();
                Request.Body.Position = 0;
                StreamReader sr = new StreamReader(Request.Body);//此处sr不能关闭，一关闭后续就无法读取了
                 
                body = await sr.ReadToEndAsync();
                Request.Body.Position = 0;
                 
                try
                {
                    if (body.Contains("")) //\u0001
                    {
                        body = body.Replace("", "");
                    }
                    
                    /*if (body.Contains(""))
                    {
                            
                        if (!(body.EndsWith("}")))
                        {
                              
                            int nn = body.LastIndexOf("}");
                            invalidBody = body;
                            body = body.Substring(0, nn+1);
                                

                        }
                    }*/
                    dynamic dBody=JsonConvert.DeserializeObject(body);
                   

                    operKey = dBody.operKey;// JToken.Parse(body).SelectToken("operKey")?.ToString();

                    

                }
                catch(Exception e)
                {

                }
                               
                   
            }
       
            #region 过滤SQL注入
            string err = SqlFilter.Filter(httpContext, body);
            if (err != "")
            { 
                var res = new { result = "Error", msg = err };
                httpContext.Response.ContentType = "application/json;charset=utf-8;";
                httpContext.Response.StatusCode = 200;
                await httpContext.Response.WriteAsync(JsonConvert.SerializeObject(res), Encoding.UTF8);
                return;
            }
            #endregion

          

            bool bOperKeyValid = false;
            string companyID = "";
            string operID = "";
            string pwdDb = "";
            string pwd="";
            
            if (operKey.IsValid() && Token.TryParse(operKey, out Token t))
            {

                if (httpContext.Items.ContainsKey("token"))
                {
                    await next(httpContext);
                    return;
                }
                httpContext.Items.Add("token", t);
                companyID = t.CompanyID;
                operID = t.OperID;
               
                // g_dicOperators.TryGetValue(t.OperID, out pwd);
                string pwKey = "pw" + t.OperID;
                pwd = await RedisHelper.GetAsync("pw" + t.OperID);

                if (t.Pwd == pwd || t.Pwd.StartsWith("YingJiang168MiNiCheckPass") )
                {
                    bOperKeyValid = true;
                }
                else
                {
                    using(CMySbCommand cmd=CMySbCommand.GetCommand("Report"))
                    {
                        cmd.CommandText = $"select oper_pw from g_operator where company_id={companyID} and oper_id={t.OperID} and coalesce(can_login,true) and coalesce(oper_status,'1') = '1' ";
                        object ov=await cmd.ExecuteScalarAsync();     
                        if(ov!=null && ov != DBNull.Value)
                        {
                             pwdDb = ov.ToString();
                            /*lock (g_dicOperators)
                            {
                                g_dicOperators[t.OperID] = pwdDb;
                            }*/
                            MyLogger.LogMsg($"err pwd:operKey:{operKey},operID：{operID},companyID:{companyID},pwd:{pwd},dbPwd:{pwdDb}",companyID);
                            NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();

                            logger.Error($"here err pwd:operKey:{operKey},operID：{operID},companyID:{companyID},pwd:{pwd},dbPwd:{pwdDb}");

                            await RedisHelper.SetAsync(pwKey, pwdDb,3600*24);
                            if (pwdDb == t.Pwd) bOperKeyValid = true;
                        }
                    }
                }

                if (invalidBody.Length > 0)
                    MyLogger.LogMsg("invalid body:" + invalidBody, companyID);
            }
           

            #region 过滤白名单
            var path = Request.Path;
            var pass = whiteList.Any(x => {
                var res = path.StartsWithSegments(x);
                return res;
            });//如果不需要token，请加入白名单中，跳过token验证。
            if (pass)
            {
                await next(httpContext);
                return;
            }
            #endregion

            if (!bOperKeyValid)
            {
                var Response = httpContext.Response;
                if(!Response.Headers.ContainsKey("Access-Control-Allow-Origin"))
                    Response.Headers.Append("Access-Control-Allow-Origin", "*");
                if (path.Value.Contains("AppApi/", StringComparison.OrdinalIgnoreCase))
                {
                    if (path.ToString().Contains("GetOperRights",StringComparison.OrdinalIgnoreCase))
                    {
                        return;
                    }
                    Response.ContentType = "application/json;charset=utf-8;";
                    Response.StatusCode = 200;
                    NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();

                    logger.Error("in TokenChecker. path:" + path);
                    MyLogger.LogMsg("error pwd in token checker, path:" + path + " operKey:" + operKey,companyID);
                    logger.Error($"company_id:{companyID}  error pwd in token checker, path:{ path}  operKey:{ operKey} operid:{operID} pwd:{pwd},dbPwd:{pwdDb}");
                    var res = new { result = "PwdError", msg = "密码错误, 请重新登录" };
                    await Response.WriteAsync(JsonConvert.SerializeObject(res), Encoding.UTF8);                                   

                }
                else
                {
                    Response.ContentType = "text/plain;charset=utf-8;";
                    Response.StatusCode = 403;
                    NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                    logger.Error("in TokenChecker. path is:" + path);  
                    await Response.WriteAsync("身份验证失败了,请重新登录", Encoding.UTF8);  
                }
                return;
            }
           

        

            /*
           
            bool valid = QuickAccessChecker.CheckAccessValid(httpContext, companyID);
            if (!valid)
            {
                var Response = httpContext.Response;
                Response.Headers.Add("Access-Control-Allow-Origin", "*");
                Response.ContentType = "application/json;charset=utf-8;";
                Response.StatusCode = 200;
            
                var res = new { result = "TooQuick", msg = "访问频率过快, 请稍候再试" };
                await Response.WriteAsync(JsonConvert.SerializeObject(res), Encoding.UTF8);
            }*/



            if (MyLogger.g_logMsgLevel == MyLogger.LOG_MSG_LEVEL.LOG_DETAIL)
            {
                string ip = httpContext.Connection.RemoteIpAddress.ToString(); 
                string queryStr = Request.QueryString.ToString();
                string msg ="ip:"+ip +" " + path + queryStr;
                if (body != "")
                {
                //    msg += " body:" + body;
                }
                MyLogger.LogFileMsg(msg, companyID);
            }
            try
            {
                await next(httpContext);
            }
            catch(Exception e)
            {
                //NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                string errMsg = Request.GetDisplayUrl(); 
                errMsg += "\r\nBody:" + body;
                errMsg += "\r\nCompanyID:" + companyID;
                errMsg += "\r\nOperID:" + operID;

                errMsg += "\r\nErrorMsg:" + e.Message;
                if (e.Message.IsInvalid())
                {
					errMsg += "\r\nError:" + e.ToString();
				}
                errMsg += "\r\nStackTrace:" + e.StackTrace;
            
                NLogger.Error(errMsg);
                MyLogger.LogMsg(errMsg, companyID);
                string msg = "系统需升级，请联系客服";
                var res = new { result = "bug", msg , message =msg};
                httpContext.Response.ContentType = "application/json;charset=utf-8;";
                httpContext.Response.StatusCode = 200;
                await httpContext.Response.WriteAsync(JsonConvert.SerializeObject(res), Encoding.UTF8);

                 
            }


        }
    }

   
}