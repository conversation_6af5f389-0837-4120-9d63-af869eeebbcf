@page
@model FeeOutSheetModel
@{
    Layout = null;
    
}
<!DOCTYPE html>
<html lang="en">
<head>
    <partial name="_SheetHead" model="Model.PartialViewModel" />
    <script>
        var OperDepartPath = '@Model.OperDepartPath';
        var JsonOperRights = '@Model.JsonOperRights';
        var JsonOperRightsOrig = '@Model.JsonOperRightsOrig';
        var OperID = '@Model.OperID';
        var Href = '@Html.Raw(Model.ObsBucketLinkHref)'
        //var SupplierFeeSheetId = '@Model.  SupplierFeeSheetId';//代厂家支付事后开其他收入单，以后再做
    </script>
    <script type="text/javascript" src="~/Sheet/FeeOutSheet.js?v=@Model.PartialViewModel.Version"></script>
    <script type="text/javascript" src="~/js/ImageDeal.js?v=@Model.Version"></script>
    <script src="~/js/xlsx.full.min.js"></script>
    <script type="text/javascript">
         function btnNewCust_Click() {
            // document.all.inputsale_time.focus();

           //  document.all.inputsale_time.selectionStart = 0;

         }
         function OnRequestComplete(result) {
             var brr = result;
             if (brr.length > 0) {
                 if (brr.length > 1) {
                     var offset = $("#jqxgrid").offset();
                     $("#popupWindow").jqxWindow({ position: { x: parseInt(offset.left) + 60, y: parseInt(offset.top) + 60 } });
                     $("#popupWindow").jqxWindow('open');
                     $('#pop_sale_sheet').jqxGrid('clear');
                     $('#pop_sale_sheet').jqxGrid('focus');
                     $('#pop_sale_sheet').jqxGrid('selectrow', 0);
                     for (var i = 0; i < brr.length; i++) {
                         var value = $('#pop_sale_sheet').jqxGrid('addrow', i, {});
                         var infoSale = brr[i];

                         $("#pop_sale_sheet").jqxGrid('setcellvaluebyid', i, "item_no", infoSale.item_no);
                         $("#pop_sale_sheet").jqxGrid('setcellvaluebyid', i, "item_name", infoSale.item_name);
                         $("#pop_sale_sheet").jqxGrid('setcellvaluebyid', i, "unit_no", infoSale.unit_no);
                         $("#pop_sale_sheet").jqxGrid('setcellvaluebyid', i, "price", infoSale.price);
                     }
                 }
                 else {
                     var infoSale = brr[0];
                     var cell = $('#jqxgrid').jqxGrid('getselectedcell');
                     $("#jqxgrid").jqxGrid('setcellvaluebyid', cell.row, "item_no", infoSale.item_no);
                     $("#jqxgrid").jqxGrid('setcellvaluebyid', cell.row, "item_name", infoSale.item_name);
                     $("#jqxgrid").jqxGrid('setcellvaluebyid', cell.row, "unit_no", infoSale.unit_no);
                     $("#jqxgrid").jqxGrid('setcellvaluebyid', cell.row, "price", infoSale.price);
                 }
             } else {
                 bw.toast("没有包含此关键字的支出方式！");
             }
         }
        
    </script>
    <style>
        .dispTable {
            margin:10px;
            width:800px;
            border:1px solid #ABABAB;
            border-collapse:collapse;
            
        }
        .dispTable th {
            padding:8px;
            border:1px solid #ABABAB;
            background-color:#E8E8E8;
            font-weight:400;
        }
        .dispTable td{
            padding:8px;
            border:1px solid #ABABAB;
        }

        .dispTable tr:hover{
            background-color:#E8E8E8;
            cursor:pointer;
        }

        .hidden{
            display:none;
        }

        #fee_appo_sheet_no, #pay_for_supplier_fee_sheet_no {
            color:blue;
            cursor:pointer;
        }
    </style>


</head>

<body class='default' style="overflow:hidden;">
     
        
        <div id="divTitle" style="text-align:center;height:45px;margin-top:5px;">
            <label id="lblSheetTitle" style="font-weight:500;font-size:25px;">@Html.Raw(Model.SheetTitle)</label>
            <img id="imgState" style="display:none;position:fixed;top:0px;left:calc(50% - 150px);" src="" />
            <div class="makeInfo" style="position:absolute;top:5px; right:0px;">
                <div><div><label>单号:</label></div> <div id="sheet_no"></div></div>
                <div><div><label>分摊单:</label></div> <div id="fee_appo_sheet_no"></div></div>
                <div><div><label>代付单:</label></div> <div id="pay_for_supplier_fee_sheet_no"></div></div>
                @*<div><div><label>订单:</label></div> <div><span id="order_sheet_no" placeholder="dddd"></span></div></div>*@
                <div><div><label>制单:</label></div> <div><span id="make_time"></span><span id="maker_name"></span></div></div>
                <div><div><label>审核:</label></div> <div><span id="approve_time"></span><span id="approver_name"></span></div></div>

            </div>

        </div>
        <div id="divHead" class="headtail" style="margin-bottom:10px;margin-top:0px;">
           
            <div style="float:none;height:0px; clear:both;"></div>
        </div>

        <div id="jqxgrid" style="margin-left:10px; position:static;width:100%;height:100%;border-bottom-color:#dedede;"></div>


        <div style="display:flex;">
            <div id="divTail" class="headtail" style="margin-bottom:0px;margin-top:10px;margin-left:30px;">

                <div style="float:none;height:0px; clear:both;"></div>

            </div>
            <div id="pfs_btn" onclick="ShowPayForSupplierBox()" style="display:none;cursor:pointer;border-bottom:1px solid #595857;margin:18px 20px 10px 0;color:#595857;font-size:14px;">代厂家支付</div>
            <div id="div_get_account" style="display:flex;">

            </div>
        </div>
        @*<div id="divTail" class="headtail" style="margin-bottom:10px;margin-top:10px;margin-left:30px;">
           
          <div style="float:none;height:0px; clear:both;"></div>
        </div>*@
        <div id="divButtons" style="text-align:center;">
            <button id="btnSave" type="button" style="margin-right:40px;">保存</button>
            <button id="btnApprove" type="button" style="margin-right:40px;" class="main-button">审核</button>
            <button id="btnCopy" type="button">复制</button>
            <button id="btnRedAndChange" type="button" style="margin-right:40px;">冲改</button>
            <button id="btnRed" type="button" disabled>红冲</button>
            <button id="btnAdd" type="button">新增</button>
            <button id="btnDelete" type="button" disabled>删除</button>
            <button id="btnImport" type="button" style="margin-right:40px;" onclick="btnImport_click()">导入</button>
            <button id="btnPrint" type="button" style="margin-right:40px;">打印</button>
            <button id="btnAppendix" type="button" style="margin-right:40px;">附件</button>
            <button id="btnClose" type="button">关闭</button>
            <button id="btnSeleteDisp" type="button" style="margin-right:40px;display: none;">陈列费</button>
        </div>
        <div id="cellbegineditevent"></div>

        <input id="hdDbID" type="hidden" />
        
            
        
        <div id="popupWindow" style="display:none;">
            <div style="margin: 10px">
                <div id="pop_sale_sheet"> </div>
                <div>
                    <input style="margin-right: 5px;" type="button" id="choose" value="选择" /><input type="button" value="取消" id="cancelButton" />
                </div>
                <br />
                <br />
            </div>
        </div>
        <div id="popClient" style="display:none">
            <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;">
                <div class="tabs" style="display:flex;justify-content:center;gap:20px;">
                    <div id="tabClient" class="tab active" onclick="switchTab('client')">客户</div>
                    <div id="tabSupplier" class="tab" onclick="switchTab('supplier')">供应商</div>
                </div>
            </div>
            <div style="overflow:hidden;" id="clientContent"> </div>
        </div>
        @* <div id="popClient" style="display:none"> 
            <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">客户</span></div>
            <div style="overflow:hidden;"> </div>
        </div> *@
        <div id="popItem" style="display:none"> 
            <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择支出类别</span></div>
            <div style="overflow:hidden;"> </div>
        </div>
        <!--上传附件-->
        <div id="popAppendix" style="display:none;">
            <div id="appendixCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">上传附件</span></div>
            <div style="overflow:hidden;"> </div>
        </div>
        <!-- 选择陈列协议 start -->
        <div id="popDisplay" style="display:none">
            <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择陈列协议费用</span></div>
            <div id="divDisplay" style="height: 99%;overflow-y: hidden;">
            </div>
        </div>
        <div id="popPayForSupplier" style="display:none">
            <div id="pfsCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">代厂家支付</span></div>
            <div style="overflow:hidden;"> </div>
        </div>
        <!-- 导入的弹窗 -->
        <div id="popImport" style="display:none">
            <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:16px;">导入费用支出数据</span></div>
                <div style="padding-left:30px;">
                    <div>
                        <p>第一步 下载模板，填入数据（不可改变表结构）并保存：</p>
                        <a href="../download/费用支出模板.xlsx" download="费用支出表.xlsx">下载模板</a>
                        @*href是下载地址 download指定下载之后的名字 *@
                        <p>第二步 选择已保存的模板文档，然后点击“导入”按钮：</p>
                    </div>


                    <div style="padding-left:20px;">
                        <button onclick="btnSelectImportFile_click()">选择文件</button>
                        <label id="labelForImportFileName"></label>
                    </div>
                    <div style="display:flex;justify-content:center;margin-top:50px;">
                        <button onclick="btnImportConfirm_click()" class="main-button" style="margin-right:80px;">导入</button>
                        <button onclick="$('#popImport').jqxWindow('close')">关闭</button>

                    </div>
                </div>

         </div>
        
        @* <table class="dispTable"> *@
        @*     <thead> *@
        @*         <tr> *@
        @*             <th class="hidden">单号</th> *@
        @*             <th>单号</th> *@
        @*             <th class="hidden">费用账户ID</th> *@
        @*             <th>费用账户</th> *@
        @*             <th>可用金额</th> *@
        @*             <th>备注</th> *@
        @*         </tr> *@
        @*     </thead> *@
        @*     <tbody id='disp_tbody'> *@
        @*          *@
        @*     </tbody> *@
        @*      *@
        @* </table> *@
        <!-- 选择陈列协议 end -->
</body>
</html>
