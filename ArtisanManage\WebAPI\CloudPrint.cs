﻿using _YingJiang.Print;
using ArtisanManage.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.DrawingCore;
//using System.Drawing.Printing;
using System.IO;
using System.Text;
using YM.MCP.Demo.SDK;
using YM.MCP.Demo.Util;
/*using iTextSharp.text;*/
using System.Threading.Tasks;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System.Security.Cryptography;
using System.Net;
using System.DrawingCore.Imaging;
using System.DrawingCore.Printing;
using System.Net.Http;
using ArtisanManage.Pages;
using ArtisanManage.MyJXC;
using System.Net.Http.Headers;

using Image = System.DrawingCore.Image;
using Font = System.DrawingCore.Font;
using Rectangle = System.DrawingCore.Rectangle;
using System.Reflection;
using ArtisanManage.WebAPI;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
/*using i_Image = iTextSharp.text.Image;*/

namespace ArtisanManage.CloudPrinterController
{
    [Route("AppApi/[controller]/[action]")]
    public class CloudPrintController:BaseController
    {
        // private CMySbCommand cmd;
        private readonly IHttpClientFactory _httpClientFactory;
        public CloudPrintController(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }
        private const string _YM_TOKEN_NAME = "_AccessToken_YingMei";
        private const string _SW_TOKEN_NAME = "_AccessToken_ShangWei";
        /*private string GetBytesFromImage(List<byte[]> lstImageData, Bitmap img)
        {
            lstImageData.Add(HeadEscCommand(img));
            lstImageData.Add(SheetPrinter.TransferImageToBytes(img, false));
           // lstImageData.Add(TransferImageToBytes(img));
            lstImageData.Add(TailEscCommand(img));
            return "";
        }*/
        public static void Params2ReqID(out string reqID, in string companyID, in string sheetType, in string sheetID, in int option, in string operID)
        {
            reqID = $"v2|{companyID}|{sheetType}|{sheetID}|{option}|{operID}";
        }
        public static void ReqID2Params(in string reqID, out string companyID, out string sheetType, out string sheetID, out int option, out string operID)
        {
            if (reqID.StartsWith("v"))
            {
                string[] arr = reqID.Split('|');
                string version = arr[0];
                if (version == "v2")
                {
                    companyID = arr[1];
                    sheetType = arr[2];
                    sheetID = arr[3];
                    option = Convert.ToInt32(arr[4]);
                    operID = arr[5];
                }
                else
                {
                    companyID = ""; sheetType = ""; sheetID = ""; option = 0; operID = "";
                    throw new Exception("Invalid reqID for cloud print");
                }
            }
            else
            {
                string[] arr = reqID.Split('_');
                companyID = arr[1];
                operID = arr[2];
                option = Convert.ToInt32(arr[3]);
                sheetType = arr[4];
                sheetID = arr[5];
            }
        }
        private async Task<string> LogPrintPost(string companyId, string operId, string deviceId, 
            string sheetType, string sheetId, dynamic postResult)
        {
            string msg = string.Empty;
            string logPostResult = string.Empty;
            try { 
                logPostResult = JsonConvert.SerializeObject(postResult); 
            } catch (Exception ex) {
                msg += ex.Message;
            }
            string now = CPubVars.GetDateText(DateTime.Now);
            string sql = $@"
                INSERT INTO log_cloudprint
                    (company_id, happen_time, oper_id, sheet_id, sheet_type, device_id, post_result)
                VALUES 
                    ({companyId}, '{now}', {operId}, {sheetId}, '{sheetType}', '{deviceId}', '{logPostResult}')
                RETURNING flow_id;
            ";
            cmd.CommandText = sql;
            try {
                await cmd.ExecuteNonQueryAsync(); 
            } catch (Exception ex) {
                msg += ex.Message;
                NLogger.Error(ex.ToString());
            }
            return msg;
        }

        /*
     
        [HttpPost]
        public async Task<dynamic> StartPrint([FromBody] dynamic param)
        {
            string operKey = param.operKey;
            string device_id = param.device_id;             // 打印机制造编号
            string check_code = param.check_code;           // 打印机校验码
            string printer_brand = param.printer_brand;     // 打印机品牌, "ym" or "sw"
            dynamic sheet = param.sheet;
            dynamic tmp = param.tmp;
                  
            string copies = param.copies;                   // 打印份数
            bool directPrint = param.directPrint == true;   // [Optional] 是否直接使用escCommand作为ESC指令打印
            string escCommand = param.escCommand;           // [Optional] BASE64格式,directPrint时的打印内容
            string fromPage = param.fromPage;
            string toPage = param.toPage;

            if (operKey.IsInvalid()) { return ErrorResult("登录信息过期,请重新登录!"); }
            if (device_id.IsInvalid()) { return ErrorResult("设备ID不合法!"); }
            if (check_code.IsInvalid()) { return ErrorResult("设备校验码不合法!"); }
            if (printer_brand.IsInvalid()) { return ErrorResult("打印机品牌未设置!"); }

            // 1. Init.
            this.device_id = device_id;
            device_code = device_id + "#" + check_code;
            paper_width = tmp.width;
            paper_height = tmp.height;
            int p_width = Convert.ToInt32(Convert.ToDouble(paper_width));
            paper_width = Convert.ToInt32(Convert.ToDouble(paper_width)).ToString();
            paper_height = Convert.ToInt32(Convert.ToDouble(paper_height)).ToString();
            paper_type = "3";

            // 2. Convert.
            byte[] bytes = null; string file = "";

            string cus_orderid = "";

            if (directPrint && escCommand != null)
            {
                bytes = Convert.FromBase64String(escCommand);
                file = escCommand;
            }
            else
            {
                await Task.Run(() =>
                {
#if DEBUG
                    DateTime tm = DateTime.Now;
#endif

                    List<byte[]> data = new List<byte[]>();
                    Func<Bitmap, string> cbDeal = img =>
                    {
                        return GetBytesFromImage(data, img);
                    };

                    Sheet2Images(sheet, tmp, cbDeal);

                    bytes = MergeByteArrayLists(data);
                    file = TransferBytesToString(bytes);
                    data.Clear();

#if DEBUG
                    TimeSpan ts = DateTime.Now - tm;
                        NLogger.Info($"StartPrint: 耗费了{ts.TotalMilliseconds}(ms)来将单据转换为图片。");
#endif
                });
            }

            // 3. Mark.
            string sheet_id = sheet.sheet_id;
            bool isSum = sheet.isSum == true;
            bool isOpenStock = sheet.isOpenStock == true;
            bool isEach = !isSum && !isOpenStock;

            // [!] 必须按二进制 [>!>递进<!<] 位设置printOptionNum。
            // 0代表false, 1代表true。
            // ————————————SAMPLE—————————————
            //  isEach | isOpenStock | isSum
            //     ↓          ↓          ↓
            //     1          0          1
            //                ↓
            //                5
            // Resolve: (101)b = (1*2^2+1*2^0)x
            // ———————————————————————————————
            int printOptionNum = 0;
            if (isSum) printOptionNum |= 1;         // 0b0001
            if (isOpenStock) printOptionNum |= 2;   // 0b0010
            if (isEach) printOptionNum |= 4;        // 0b0100

            //CMySbCommand cmd = new CMySbCommand();
            //var pc = new WebAPI.PrinterController(cmd);
            var data = new
            {
                operKey = operKey,
                sheetType = sheet.sheetType,
                sheetIDs = sheet_id,            // 打印汇总单时的sheet_id为类似'1211,1232,5414'的格式
                printSum = isSum,
                printEach = isEach,
                printOpenStock = isOpenStock,
            };

            //var mark = await pc.PrintInitLog(data);
            //dynamic mark_value = mark.Value;
            //    string flow_id = mark_value.logFlowID;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            // [!] 必须按给定顺序设置流水号。顺序：flowID_公司id_operID_打印类型num_单据类型_单据id

            //  cus_orderid = $"_{companyID}_{operID}_{printOptionNum}_{sheet.sheetType}_{sheet_id}";

            Params2ReqID(out cus_orderid, companyID, (string)(sheet.sheetType), sheet_id, printOptionNum, operID);

            // 4. Print.
            dynamic result;//, waitForBind;
            if (printer_brand == "ym")
            {
                //waitForBind = await YM_BindPrinterAsync(device_code);
                result = await YM_PrintFileByEsc(cus_orderid, file, copies);
            }
            else
            {
                //waitForBind = await SW_BindPrinter(device_id, check_code);
                result = await SW_StartPrint(device_id, cus_orderid, bytes, p_width);
            }
            bytes = null;
            file = null;
            return result;
        }*/

        [HttpPost] 
        public async Task<dynamic> PrintSheetWithTemplate([FromBody] dynamic param)
        {
            string operKey = param.operKey;
            string device_id = param.device_id;             // 打印机制造编号
            string check_code = param.check_code;           // 打印机校验码
            string printer_brand = param.printer_brand;     // 打印机品牌, "ym" or "sw"
            dynamic sheet = param.sheet;
            dynamic tmp = param.tmp;

            string copies = param.copies;                   // 打印份数 映美  // 暂不启用
            // int pcopy = int.Parse(param.copies);         // 打印份数 商为暂不支持  未启用
            bool directPrint = param.directPrint == true;   // [Optional] 是否直接使用escCommand作为ESC指令打印
            string escCommand = param.escCommand;           // [Optional] BASE64格式,directPrint时的打印内容
            string fromPage = param.fromPage;
            string toPage = param.toPage;
            bool ignorePaperFeedCmd = param.ignorePaperFeedCmd == true;

            if (operKey.IsInvalid()) { return ErrorResult("登录信息过期,请重新登录!"); }
            if (device_id.IsInvalid()) { return ErrorResult("设备ID不合法!"); }
            if (check_code.IsInvalid()) { return ErrorResult("设备校验码不合法!"); }
            if (printer_brand.IsInvalid()) { return ErrorResult("打印机品牌未设置!"); }

            // 1. Init.
            this.device_id = device_id;
            device_code = device_id + "#" + check_code;
            paper_width = tmp.width;
            paper_height = tmp.height;
            int p_width = Convert.ToInt32(Convert.ToDouble(paper_width));
            paper_width = Convert.ToInt32(Convert.ToDouble(paper_width)).ToString();
            paper_height = Convert.ToInt32(Convert.ToDouble(paper_height)).ToString();
            paper_type = "3";

            // 2. Convert.
            byte[] bytes = null; string file = "";

            string cus_orderid = "";

            if (directPrint && escCommand != null)
            {
                bytes = Convert.FromBase64String(escCommand);
                file = escCommand;
            }
            else
            {
                await Task.Run(() =>
                {
#if DEBUG
                    DateTime tm = DateTime.Now;
#endif
                    /*
                    List<byte[]> data = new List<byte[]>();
                    Func<Bitmap, string> cbDeal = img =>
                    {
                        return GetBytesFromImage(data, img);
                    };*/
                    int nFromPage = -1,nToPage=-1;
                    if (fromPage != null) nFromPage = Convert.ToInt32(fromPage);
                    if (toPage != null) nToPage = Convert.ToInt32(toPage);
                    List<byte[]> lstEscCmd = new List<byte[]>();

                    Sheet2EscOrImage(sheet, tmp, nFromPage, nToPage, lstEscCmd,null, ignorePaperFeedCmd);
                    if (lstEscCmd != null && lstEscCmd.Count > 0)
                    {
                        bytes = SheetPrinter.MergeByteArrayLists(lstEscCmd);
                        lstEscCmd.Clear();
                    }
                     

#if DEBUG
                    TimeSpan ts = DateTime.Now - tm;
                    NLogger.Info($"StartPrint: 耗费了{ts.TotalMilliseconds}(ms)来将单据转换为图片。");
#endif
                });
            }

            // 3. Mark.
            string sheet_id = sheet.sheet_id;
            bool isSum = sheet.isSum == true;
            bool isOpenStock = sheet.isOpenStock == true;
            bool isEach = !isSum && !isOpenStock;

            // [!] 必须按二进制 [>!>递进<!<] 位设置printOptionNum。
            // 0代表false, 1代表true。
            // ————————————SAMPLE—————————————
            //  isEach | isOpenStock | isSum
            //     ↓          ↓          ↓
            //     1          0          1
            //                ↓
            //                5
            // Resolve: (101)b = (1*2^2+1*2^0)x
            // ———————————————————————————————
            int printOptionNum = 0;
            if (isSum) printOptionNum |= 1;         // 0b0001
            if (isOpenStock) printOptionNum |= 2;   // 0b0010
            if (isEach) printOptionNum |= 4;        // 0b0100

            //CMySbCommand cmd = new CMySbCommand();
            //var pc = new WebAPI.PrinterController(cmd);
            var data = new
            {
                operKey = operKey,
                sheetType = sheet.sheetType,
                sheetIDs = sheet_id,            // 打印汇总单时的sheet_id为类似'1211,1232,5414'的格式
                printSum = isSum,
                printEach = isEach,
                printOpenStock = isOpenStock,
            };

            //var mark = await pc.PrintInitLog(data);
            //dynamic mark_value = mark.Value;
            //    string flow_id = mark_value.logFlowID;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            // [!] 必须按给定顺序设置流水号。顺序：flowID_公司id_operID_打印类型num_单据类型_单据id

            //  cus_orderid = $"_{companyID}_{operID}_{printOptionNum}_{sheet.sheetType}_{sheet_id}";

            Params2ReqID(out cus_orderid, companyID, (string)(sheet.sheetType), sheet_id, printOptionNum, operID);

            // 4. Print.
            dynamic result;//, waitForBind;
            if (printer_brand == "ym")
            {
                //waitForBind = await YM_BindPrinterAsync(device_code);
                file = TransferBytesToString(bytes);
                result = await YM_PrintFileByEsc(cus_orderid, file, copies);  
            }
            else
            {
                //waitForBind = await SW_BindPrinter(device_id, check_code);
                //var s = Convert.ToBase64String(bytes);
                //System.IO.File.WriteAllText(@"C:\Users\<USER>\Desktop\debug_sheetBytesB64.txt", s);
                result = await SW_StartPrint(companyID,device_id, cus_orderid, bytes, p_width);
            }
            bytes = null;
            file = null;

            // * 2024.07.16
            // #2848 - 在云打印发送指令时就标记打印次数
            Console.WriteLine("cloud-print-result:" + JsonConvert.SerializeObject(result));
            if (result.result == "OK")
            {
                dynamic pmResponse = await PrinterController.PrintMark_static(cmd,
                    companyID, operID, (string)(sheet.sheetType), sheet_id, "", "(云打印)已发送打印，响应结果：" + result.msg,
                    isSum, isEach, isOpenStock);
                if (pmResponse.Value.result != "OK")
                {
                    NLogger.Error($"[{DateTime.Now}] '{companyID}-{sheet_id}'的打印次数标记失败:{pmResponse.Value.result}");
                }
            }

            await LogPrintPost(companyID, operID, device_id, (string)(sheet.sheetType), sheet_id, result);

            return result;
        }


        [HttpPost] 
        public async Task<dynamic> PrintSheetWithEscCmd([FromBody] dynamic param)
        {
            string operKey = param.operKey;
            string device_id = param.device_id;             // 打印机制造编号
            string check_code = param.check_code;           // 打印机校验码
            string printer_brand = param.printer_brand;     // 打印机品牌, "ym" or "sw"
            dynamic sheet = param.sheet;
          //  dynamic tmp = param.tmp;

            string copies = param.copies;                   // 打印份数 映美  // lmy 20230314
          // int pcopy = int.Parse(param.copies);            // 打印份数 商为 未启用
          //  bool directPrint = param.directPrint == true;   // [Optional] 是否直接使用escCommand作为ESC指令打印
            string escCommand = param.escCommand;           // [Optional] BASE64格式,directPrint时的打印内容
          

            if (operKey.IsInvalid()) { return ErrorResult("登录信息过期,请重新登录!"); }
            if (device_id.IsInvalid()) { return ErrorResult("设备ID不合法!"); }
            if (check_code.IsInvalid()) { return ErrorResult("设备校验码不合法!"); }
            if (printer_brand.IsInvalid()) { return ErrorResult("打印机品牌未设置!"); }

            // 1. Init.
            this.device_id = device_id;
            device_code = device_id + "#" + check_code;
            //paper_width = tmp.width;
            //paper_height = tmp.height;
            int p_width = Convert.ToInt32(Convert.ToDouble(paper_width));
            paper_width = Convert.ToInt32(Convert.ToDouble(paper_width)).ToString();
            paper_height = Convert.ToInt32(Convert.ToDouble(paper_height)).ToString();
            paper_type = "3";

            // 2. Convert.
            byte[] bytes = null;

            string cus_orderid = "";
             
            bytes = Convert.FromBase64String(escCommand);
             

            // 3. Mark.
            string sheet_id = sheet.sheet_id;
            bool isSum = sheet.isSum == true;
            bool isOpenStock = sheet.isOpenStock == true;
            bool isEach = !isSum && !isOpenStock;

            // [!] 必须按二进制 [>!>递进<!<] 位设置printOptionNum。
            // 0代表false, 1代表true。
            // ————————————SAMPLE—————————————
            //  isEach | isOpenStock | isSum
            //     ↓          ↓          ↓
            //     1          0          1
            //                ↓
            //                5
            // Resolve: (101)b = (1*2^2+1*2^0)x
            // ———————————————————————————————
            int printOptionNum = 0;
            if (isSum) printOptionNum |= 1;         // 0b0001
            if (isOpenStock) printOptionNum |= 2;   // 0b0010
            if (isEach) printOptionNum |= 4;        // 0b0100
            //CMySbCommand cmd = new CMySbCommand();
            //var pc = new WebAPI.PrinterController(cmd);
            var data = new
            {
                operKey = operKey,
                sheetType = sheet.sheetType,
                sheetIDs = sheet_id,            // 打印汇总单时的sheet_id为类似'1211,1232,5414'的格式
                printSum = isSum,
                printEach = isEach,
                printOpenStock = isOpenStock,
            };

            //var mark = await pc.PrintInitLog(data);
            //dynamic mark_value = mark.Value;
            //    string flow_id = mark_value.logFlowID;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
      
            Params2ReqID(out cus_orderid, companyID, (string)(sheet.sheetType), sheet_id, printOptionNum, operID);

            // 4. Print.
            dynamic result;//, waitForBind;
            if (printer_brand == "ym")
            { 
                result = await YM_PrintFileByEsc(cus_orderid, escCommand, copies);
            }
            else
            { 
                result = await SW_StartPrint(companyID, device_id, cus_orderid, bytes, p_width);
            }
            bytes = null;

            // * 2024.07.16
            // #2848 - 在云打印发送指令时就标记打印次数
            Console.WriteLine("cloud-print-result:" + JsonConvert.SerializeObject(result));
            if (result.result == "OK")
            {
                dynamic pmResponse = await PrinterController.PrintMark_static(cmd,
                    companyID, operID, (string)(sheet.sheetType), sheet_id, "", "(云打印)已发送打印，响应结果：" + result.msg,
                    isSum, isEach, isOpenStock);
                if (pmResponse.Value.result != "OK")
                {
                    NLogger.Error($"[{DateTime.Now}] '{companyID}-{sheet_id}'的打印次数标记失败:{pmResponse.Value.result}");
                }
            }

            await LogPrintPost(companyID, operID, device_id, (string)(sheet.sheetType), sheet_id, result);
            return result;
        }
         
        // For app invocation. do not del.
        [HttpPost]
        public async Task<string> SheetToEsc([FromBody] dynamic reqest)
        {
            dynamic sheet = reqest.sheet;
            dynamic tmp = reqest.tmp;
            bool ignorePaperFeedCmd = reqest.ignorePaperFeedCmd == true;

            byte[] bytes = null;
            await Task.Run(() =>
            {
                int nFromPage = -1, nToPage = -1; 
                List<byte[]> lstEscCmd = new List<byte[]>(); 
                Sheet2EscOrImage(sheet, tmp, nFromPage, nToPage, lstEscCmd, null, ignorePaperFeedCmd);
                if (lstEscCmd != null && lstEscCmd.Count > 0)
                {
                    bytes = SheetPrinter.MergeByteArrayLists(lstEscCmd);
                }
            });
            string result = TransferBytesToString(bytes);                
            return result;
        }
        /*
        public string SheetToEsc_old([FromBody] dynamic reqest)
        {
            dynamic sheet = reqest.sheet;
            dynamic tmp = reqest.tmp;

            List<byte[]> data = new List<byte[]>();

            Func<Bitmap, string> cbDeal = img =>
            {
                return GetBytesFromImage(data, img);
            };

            Sheet2Images(sheet, tmp, cbDeal);
            byte[] bytes = MergeByteArrayLists(data);
            string result = TransferBytesToString(bytes);

            return result;
        }*/

        [HttpPost]
        public async Task<CallResult> ClearCloudPrintTasks([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyId, out string operId);
            string deviceId = data.device_id;
            var hasPermissionToCast = await CheckCompanyHasThisPrinter(companyId, deviceId);
            if (!hasPermissionToCast.IsOK)
                return new CallResult("Error", hasPermissionToCast.msg);
            if (!hasPermissionToCast.data)
                return new CallResult("Failed", "无法操作未登记在本账户上的打印机");
            var response = await SW_ClearAllWaitingTask(deviceId);
            if (response == "true")
                return new CallResult("OK", "");
            else
                return new CallResult("Error", response);
        }

        [HttpPost]
        public async Task<string> SheetToImages([FromBody] dynamic data)
        {
            dynamic sheet = data.sheet;
            dynamic tmp = data.tmp;
            bool ignorePaperFeedCmd = data.ignorePaperFeedCmd == true;
            List<string> lstBase64 = new List<string>();

            await Task.Run(() =>
            {
                int nFromPage = -1, nToPage = -1; 
                Sheet2EscOrImage(sheet, tmp, nFromPage, nToPage, null, lstBase64, ignorePaperFeedCmd);
                
            });
     
            string result = JsonConvert.SerializeObject(lstBase64.ToArray());
            if (result[0] != '[') { result = $"[{result}]"; }
            return result;
        }
        /*
        public string SheetToImages_old([FromBody] dynamic datas)
        {
            dynamic sheet = datas.sheet;
            dynamic tmp = datas.tmp;
            List<string> lstBase64 = new List<string>();
            List<byte[]> data = new List<byte[]>();
            Func<Image, string> cbDeal = img => {
                var s = ImageToBase64(img);
                 

                if (s != null)
                    lstBase64.Add(s);
                img.Dispose();
                return "";
            };
            Sheet2Images(sheet, tmp, cbDeal);

            string result = JsonConvert.SerializeObject(lstBase64.ToArray());
            if (result[0] != '[') { result = $"[{result}]"; }
            return result;
        }*/

        #region Jolimark - 映美云打印
        /// <summary>映美云打印的AccessToken,根据公司账号的app_id和app_key获取。
        /// 考虑到不同用户可能同时打印，应当尽可能避免频繁刷新。
        /// 请通过GetAccessToken()来获取，而非直接引用。</summary>
        public static string YmAccessToken;
        public static DateTime YmExpires;

        // 可在 http://open.jolimark.com/app/ 查询与更新
        private readonly string app_id = "220228112356748";
        private readonly string app_key = "cxdbxbbffengxl8w";
        private readonly string YmApi = "https://mcp.jolimark.com";

        private string device_id;    // 打印机编码
        private string device_code;  // 打印机编码和校验码,以“#”连接,绑定多个打印机以“,”分隔
        //private string cus_orderid;// 客户系统定义的订单流水号，在客户系统应该作为唯一值。
        private string paper_width;  // 纸张宽度(单位为mm)
        private string paper_height; // 纸张高度(单位为mm)

        //private int dpi;
        //private int pixel_width;        // 待打印对象需要的像素宽度
        //private int pixel_height;       // 待打印对象需要的像素高度
        //private string machine_code;    // 查询打印机基础信息返回的devicetype_id
        private string paper_type;      // 打印纸类型. 1:热敏纸, 2:标签纸, 3:带孔纸

        //private const string _TYPE_PDF = "1";
        //private const string _TYPE_IMG = "2";

        //private string file_toprint;

        #region 主要接口. 初始化,打印表单,设置接口

        // For app invocation. do not del.
        [HttpGet]
        public async Task<IActionResult> GetSheetToPrint(string sheetType,int sheet_type, string operKey, string sheet_id, bool smallUnitBarcode, string printTemplate)
        {
            //int sheet_type = data.sheet_type;
            //string operKey = data.operKey;
            //string sheet_id = data.sheet_id;
            //bool smallUnitBarcode = data.smallUnitBarcode; 
            cmd.ActiveDatabase = "";
            SHEET_TYPE sheet_type1;

			if (sheetType.IsValid())
            {
                sheet_type1 = SheetBase<SheetRowItem>.SheetTypeFromStr(sheetType);
            }
            else
            {
                sheet_type1 = (SHEET_TYPE) sheet_type;
			}

            switch (sheet_type1)
            {
                case SHEET_TYPE.SHEET_SALE:
                    SaleSheetController ss = new SaleSheetController(cmd, null);
                    return await ss.GetSheetToPrint(operKey, sheet_id, smallUnitBarcode, printTemplate);
                case SHEET_TYPE.SHEET_SALE_RETURN:
                    SaleSheetController ssc = new SaleSheetController(cmd, null);
                    return await ssc.GetSheetToPrint(operKey, sheet_id, smallUnitBarcode, printTemplate);
                case SHEET_TYPE.SHEET_SALE_DD_RETURN:
                    var sshc = new SaleOrderSheetController(cmd, null);
                    return await sshc.GetSheetToPrint(operKey, sheet_id, smallUnitBarcode, printTemplate);
                case SHEET_TYPE.SHEET_SALE_DD:
                    SaleOrderSheetController sosc = new SaleOrderSheetController(cmd, _httpClientFactory);
                    return await sosc.GetSheetToPrint(operKey, sheet_id, smallUnitBarcode, printTemplate);
                case SHEET_TYPE.SHEET_BUY:
                case SHEET_TYPE.SHEET_BUY_RETURN:
                    BuySheetController bsc = new BuySheetController(cmd);
                    return await bsc.GetSheetToPrint(operKey, sheet_id, smallUnitBarcode, printTemplate);
                case SHEET_TYPE.SHEET_BUY_DD:
                    BuyOrderSheetController bosc = new BuyOrderSheetController(cmd);
                    return await bosc.GetSheetToPrint(operKey, sheet_id, smallUnitBarcode, printTemplate);
                case SHEET_TYPE.SHEET_MOVE_STORE:
                    MoveSheetController msc = new MoveSheetController(cmd);
                    return await msc.GetSheetToPrint(operKey, sheet_id, smallUnitBarcode);
                case SHEET_TYPE.SHEET_ORDER_ITEM:
                    OrderItemSheetController oisc = new OrderItemSheetController(cmd, _httpClientFactory);
                    return await oisc.GetSheetToPrint(operKey, sheet_id, smallUnitBarcode, printTemplate);
                case SHEET_TYPE.SHEET_INVENT_INPUT:
                    InventorySheetController inv = new InventorySheetController(cmd);
                    return await inv.GetSheetToPrint(operKey, sheet_id, SHEET_TYPE.SHEET_INVENT_INPUT);
                default:
                    return new JsonResult(new
                    {
                        result = "Error",
                        msg = $"云打印暂时不支持这种类型的单据({sheet_type})",
                        sheet = ""
                    });
            }
        }

		// For app invocation. do not del.
		[HttpPost]
		public async Task<IActionResult> GetSheetToPrint_Post([FromBody] dynamic data, string operKey)
		{
            //int sheet_type = data.sheet_type;
            //string operKey = data.operKey;
            //string sheet_id = data.sheet_id;
            //bool smallUnitBarcode = data.smallUnitBarcode; 

            string sheetType = data.sheetType;
          
            //string operKey;
            string sheet_id = data.sheet_id;
            bool smallUnitBarcode = data.smallUnitBarcode;
            string printTemplate = data.printTemplate;

			cmd.ActiveDatabase = "";
			SHEET_TYPE sheet_type1;

			if (sheetType.IsValid())
			{
				sheet_type1 = SheetBase<SheetRowItem>.SheetTypeFromStr(sheetType);
			}
			else
			{
				int sheet_type = data.sheet_type;
				sheet_type1 = (SHEET_TYPE)sheet_type;
			}

			switch (sheet_type1)
			{
				case SHEET_TYPE.SHEET_SALE:
					SaleSheetController ss = new SaleSheetController(cmd, null);
					return await ss.GetSheetToPrint(operKey, sheet_id, smallUnitBarcode, printTemplate);
				case SHEET_TYPE.SHEET_SALE_RETURN:
					SaleSheetController ssc = new SaleSheetController(cmd, null);
					return await ssc.GetSheetToPrint(operKey, sheet_id, smallUnitBarcode, printTemplate);
				case SHEET_TYPE.SHEET_SALE_DD_RETURN:
					var sshc = new SaleOrderSheetController(cmd, null);
					return await sshc.GetSheetToPrint(operKey, sheet_id, smallUnitBarcode, printTemplate);
				case SHEET_TYPE.SHEET_SALE_DD:
					SaleOrderSheetController sosc = new SaleOrderSheetController(cmd, _httpClientFactory);
					return await sosc.GetSheetToPrint(operKey, sheet_id, smallUnitBarcode, printTemplate);
				case SHEET_TYPE.SHEET_BUY:
				case SHEET_TYPE.SHEET_BUY_RETURN:
					BuySheetController bsc = new BuySheetController(cmd);
					return await bsc.GetSheetToPrint(operKey, sheet_id, smallUnitBarcode, printTemplate);
				case SHEET_TYPE.SHEET_BUY_DD:
					BuyOrderSheetController bosc = new BuyOrderSheetController(cmd);
					return await bosc.GetSheetToPrint(operKey, sheet_id, smallUnitBarcode, printTemplate);
				case SHEET_TYPE.SHEET_MOVE_STORE:
					MoveSheetController msc = new MoveSheetController(cmd);
					return await msc.GetSheetToPrint(operKey, sheet_id, smallUnitBarcode);
				case SHEET_TYPE.SHEET_ORDER_ITEM:
					OrderItemSheetController oisc = new OrderItemSheetController(cmd, _httpClientFactory);
					return await oisc.GetSheetToPrint(operKey, sheet_id, smallUnitBarcode, printTemplate);
				case SHEET_TYPE.SHEET_INVENT_INPUT:
					InventorySheetController inv = new InventorySheetController(cmd);
					return await inv.GetSheetToPrint(operKey, sheet_id, SHEET_TYPE.SHEET_INVENT_INPUT);
				default:
					return new JsonResult(new
					{
						result = "Error",
						msg = $"云打印暂时不支持这种类型的单据({sheet_type1})",
						sheet = ""
					});
			}
		}

		// For app invocation. do not del.
		[HttpPost]
        public async Task<dynamic> BindPrinter([FromBody] dynamic data)
        {
            string printer_brand = data.printer_brand;
            string device_id = data.device_id;
            string check_code = data.check_code;
            switch (printer_brand)
            {
                case "ym": // ymCloud
                    string device_code = device_id + "#" + check_code;
                    return await YM_BindPrinterAsync(device_code);
                case "sw": // swCloud
                    return await SW_BindPrinter(device_id, check_code);
                default:
                    return new { result = "Error", msg = "打印机品牌未设置或不支持" };
            }
        }



        #endregion

        #region 配置存取
        /// <summary>
        /// 获取访问令牌
        /// </summary>
        /// <returns>此前缓存的access_token，若已过期则刷新访问令牌再返回</returns>
        private async Task<CallResult> YM_GetAccessTokenAsync()
        {
            YmAccessToken = await RedisHelper.GetAsync(_YM_TOKEN_NAME);
            if (IsZeroOrNull(YmAccessToken))
            {
                CallResult res = await YM_SurfAccessTokenAsync();
                return res;
            }
            else
            {
                return new CallResult("OK", "", YmAccessToken);
            }

        }

        #endregion

        #region 打印接口
        /*
        /// <summary>
        /// 打印文档(PDF,以 base64编码格式 或 URL 传入)
        /// </summary>
        /// <param name="cus_orderid">单据(销售单/采购单..)编号(sheet_id)</param>
        /// <param name="file">打印对象(图片)的base64编码版本</param>
        /// <param name="copies">打印份数</param>
        /// <returns></returns>
        private async Task<dynamic> PrintFile(string cus_orderid, string file, string copies)
        {
            Dictionary<string, dynamic> collection = new Dictionary<string, dynamic>();
            collection.Add("app_id", app_id);
            CallResult res = await YM_GetAccessTokenAsync();
            if (res.result != "OK") return res;
            collection.Add("access_token", await YM_GetAccessTokenAsync());
            collection.Add("device_ids", device_id);
            collection.Add("copies", copies); // 打印份数
            collection.Add("cus_orderid", cus_orderid);
            collection.Add("file_type", "1"); // 1:pdf, 2:图片
            collection.Add("bill_content", file);
            if (!IsZeroOrNull(paper_width))
                collection.Add("paper_width", paper_width);
            if (!IsZeroOrNull(paper_height))
                collection.Add("paper_height", paper_height);
            collection.Add("paper_type", paper_type); // 纸张类型. 1:热敏纸, 2:标签纸, 3:带孔纸
            collection.Add("time_out", "600"); // 超时时间, 范围为600~86400(s)
            bool isurl = !IsB64(file);
            //var coll = ConvertToNameValueCollection(collection);
            dynamic result = CommonUtil.HttpPostData1Async(YmApi + (isurl ? ApiConsts.SEND_PRINT_FILEBYURL : ApiConsts.SEND_PRINT_FILE), collection);
            return result;
        }
        */
        private async Task<dynamic> YM_PrintFileByEsc(string cus_orderid, string escCommand, string copies, bool isSurf = false)
        {
            //escCommand = StringToBase64(escCommand);
            CallResult res = await YM_GetAccessTokenAsync();
            if (!res.IsOK) return res;
            Dictionary<string, dynamic> collection = new Dictionary<string, dynamic>
            {
                { "app_id", app_id },
                { "access_token", res.data},
                { "device_ids", device_id },
                { "copies", copies }, // 打印份数
                { "cus_orderid", cus_orderid },
                { "bill_content", escCommand }
            };
            if (!IsZeroOrNull(paper_width))
                collection.Add("paper_width", paper_width);
            if (!IsZeroOrNull(paper_height))
                collection.Add("paper_height", paper_height);
            collection.Add("paper_type", paper_type); // 纸张类型. 1:热敏纸, 2:标签纸, 3:带孔纸
            collection.Add("time_out", "600"); // 超时时间, 范围为600~86400(s)
                                               //string strParam = JsonConvert.SerializeObject(collection);

            string post_result = await CommonUtil.HttpPostData1Async(_httpClientFactory, YmApi + ApiConsts.SEND_PRINT_ESC, collection);
            var jsonRes = JsonConvert.DeserializeObject<JObject>(post_result);
            if ((jsonRes["return_code"].ToString() == "10102" || jsonRes["return_code"].ToString() == "10103") && !isSurf)
            {
                await YM_SurfAccessTokenAsync();
                return await YM_PrintFileByEsc(cus_orderid, escCommand, copies, true);
            }
            var return_msg = jsonRes["return_msg"].ToString();
            var return_code = jsonRes["return_code"].ToString();
            bool success = return_msg == "";
            return_msg = success ? "打印成功" : return_msg;
            string printer_state = jsonRes["return_msg"].ToString();
            string extra_data = jsonRes["return_msg"].ToString() + "|" + printer_state;
            string result = success ? "OK" : "Error";
            string msg = success ? "" : return_msg;
            var obj_result = new
            {
                return_msg,//待删
                return_code,//待删
                success,//待删
                extra_data,//extra_data
                result,
                msg, // 只有这两个是以后要使用到的变量,其余都是为了兼容保留的
            };
            return obj_result;
        }

        #endregion

        #region 打印任务管理,查询任务状态.查询待打印任务.取消待打印任务
        /// <summary>
        /// 查询打印任务状态
        /// </summary>
        /// <returns>json串，格式如
        /// {"return_code":0,"return_data":[{
        /// "cus_orderid":"2018011512126","order_id":"02228b1051e9072e474bbd34c3f45687ef29","order_status":1
        /// }],"return_msg":""}</returns>
        /// <param name="cus_orderid">单据(销售单/采购单..)编号(sheet_id)</param>
        private async Task<string> QueryPrintTaskStatusAsync(string cus_orderid)
        {
            CallResult res = await YM_GetAccessTokenAsync();
            Dictionary<string, string> parameters = new Dictionary<string, string>();
            parameters.Add("app_id", app_id);
            parameters.Add("access_token", res.data);
            parameters.Add("cus_orderid", cus_orderid);
            string result = SendGetRequestByForm(YmApi + ApiConsts.GET_PRINT_ORDER_STATUS, parameters);
            return result;
        }

        /// <summary>
        /// 查询指定打印机未打印任务
        /// </summary>
        /// <param name="device_id">打印机编号</param>
        /// <returns>json串，格式如
        /// (成功) {"return_code": 0,"return_data": {"count": 2,"result": [{
        /// "cus_orderid": "001","order_id": "0507d909c1b5c53c417ba88596ec3a03b85a","order_content": "http://mcp.jolimark.com/billtemplate/hanhongtest.html",
        /// "order_date": "2018/6/1 12:05:38"},{"cus_orderid": "001","order_id": "0507d909c1b5c53c417ba88596ec3a03b85a",
        /// "order_content": "http://mcp.jolimark.com/billtemplate/hanhongtest.html","order_date": "2018/6/1 12:05:38"
        /// }] },"return_msg":""}
        /// (失败) {"return_code": 10101,"return_data": "", "return_msg": "app_id不存在"}</returns>
        private async Task<string> QueryNotPrintTaskAsync(string device_id)
        {
            Dictionary<string, string> parameters = new Dictionary<string, string>();
            parameters.Add("app_id", app_id);
            parameters.Add("access_token", (await YM_GetAccessTokenAsync()).data);
            parameters.Add("device_id", device_id);
            string result = SendGetRequestByForm(YmApi + ApiConsts.GET_NOT_PRINT_ORDER, parameters);
            return result;
        }

        /// <summary>
        /// 取消指定的打印机当前未完成的打印任务
        /// </summary>
        /// <param name="device_id">打印机编号</param>
        /// <returns>json串，格式如
        /// {"count": 2, "result": 
        /// [{"cus_orderid": "001", "order_id": "0507d909c1b5c53c417ba88596ec3a03b85a", 
        /// "order_content": "http://mcp.jolimark.com/billtemplate/hanhongtest.html", 
        /// "order_date": "2018/6/1 12:05:38"}] }</returns>
        private async Task<dynamic> CancelNotPrintTask(string device_id)
        {
            NameValueCollection collection = new NameValueCollection();
            collection.Add("app_id", app_id);
            collection.Add("access_token", (await YM_GetAccessTokenAsync()).data);
            collection.Add("device_id", device_id);
            //dynamic result = await CommonUtil.HttpPostData(YmApi + ApiConsts.CANCEL_NOT_PRINT_ORDER, collection);
            dynamic result = await CommonTool.PostJsonByFactory(_httpClientFactory, YmApi + ApiConsts.CANCEL_NOT_PRINT_ORDER, collection);
            return result;
        }
        #endregion

        #region 打印机管理,查询.绑定.解绑
        /// <summary>
        /// 解绑打印机
        /// </summary>
        /// <returns>json串，格式如
        /// {"return_code":0,"return_data":"OK","return_msg":""}</returns>
        private async Task<dynamic> UnBindPrinter(dynamic device_id)
        {
            Dictionary<string, dynamic> collection = new Dictionary<string, dynamic>();
            collection.Add("app_id", app_id);
            collection.Add("access_token", (await YM_GetAccessTokenAsync()).data);
            collection.Add("device_id", device_id);
            //var coll = ConvertToNameValueCollection(collection);
            dynamic result = CommonUtil.HttpPostData1Async(_httpClientFactory, YmApi + ApiConsts.UNBIND_PRINTER, collection);
            //获取到正常结果格式如 {"return_code":0,"return_data":"OK","return_msg":""}
            return result;
        }
        /// <summary>
        /// 解绑当前的打印机
        /// </summary>
        /// <returns>json串，格式如
        /// {"return_code":0,"return_data":"OK","return_msg":""}</returns>
        private async Task<dynamic> UnBindCurrentPrinter()
        {
            Dictionary<string, dynamic> collection = new Dictionary<string, dynamic>();
            collection.Add("app_id", app_id);
            collection.Add("access_token", (await YM_GetAccessTokenAsync()).data);
            collection.Add("device_id", device_id);
            //var coll = ConvertToNameValueCollection(collection);
            dynamic result = CommonUtil.HttpPostData1Async(_httpClientFactory, YmApi + ApiConsts.UNBIND_PRINTER, collection);
            //获取到正常结果格式如 {"return_code":0,"return_data":"OK","return_msg":""}
            return result;
        }

        /// <summary>
        /// 绑定打印机
        /// </summary>
        /// <returns>json串，格式如
        /// {"return_code":0,"return_data":"OK","return_msg":""}</returns>
        private async Task<dynamic> YM_BindPrinterAsync(string device_code, bool isSurf = false)
        {
            CallResult res = await YM_GetAccessTokenAsync();
            if (!res.IsOK) return res;
            Dictionary<string, dynamic> collection = new Dictionary<string, dynamic>();
            collection.Add("app_id", app_id);
            collection.Add("access_token", res.data);
            collection.Add("device_codes", device_code);
            //var coll = ConvertToNameValueCollection(collection);
            var s = await CommonUtil.HttpPostData1Async(_httpClientFactory, YmApi + ApiConsts.BIND_PRINTER, collection);
            dynamic jsonRes = JsonConvert.DeserializeObject<dynamic>(s);
            string return_code = (string)jsonRes.return_code;
            if ((return_code == "10102" || return_code == "10103") && !isSurf)
            {
                await YM_SurfAccessTokenAsync();
                return await YM_BindPrinterAsync(device_code, true);
            }
            string result = "OK", msg = "";
            if (!(return_code == "0" || return_code == "10205"))
            {
                result = "Error";
                msg = jsonRes.return_msg;
            }
            return new { result, msg, jsonRes.return_code, jsonRes.return_msg, jsonRes.return_data };
        }
        /// <summary>
        /// 绑定当前的打印机
        /// </summary>
        /// <returns>json串，格式如
        /// {"return_code":0,"return_data":"OK","return_msg":""}</returns>
        private async Task<dynamic> BindCurrentPrinter()
        {
            Dictionary<string, dynamic> collection = new Dictionary<string, dynamic>();
            collection.Add("app_id", app_id);
            collection.Add("access_token", await YM_GetAccessTokenAsync());
            collection.Add("device_codes", device_code);
            //var coll = ConvertToNameValueCollection(collection);
            dynamic result = CommonUtil.HttpPostData1Async(_httpClientFactory,YmApi + ApiConsts.BIND_PRINTER, collection);
            return result;
        }

        /// <summary>
        /// 查询打印机基础信息
        /// </summary>
        /// <param name="device_id">打印机编号</param>
        /// <returns>json串，格式如
        /// {"return_code":0,"return_data":
        /// {"device_id":"17080253UJ","devicetype_id":2,"devicetype_name":"",
        /// "var_version":"4.0.4.2","firmware_version":"2.9.1","last_varversion":"4.0.3",
        /// "last_firmwareversion":"2.9.1"
        /// },"return_msg":""}</returns>
        private async Task<JObject> QueryPrinterInfoAsync(string device_id)
        {
            Dictionary<string, string> parameters = new Dictionary<string, string>();
            parameters.Add("app_id", app_id);
            parameters.Add("access_token", (await YM_GetAccessTokenAsync()).data);
            parameters.Add("device_id", device_id);
            string jsons = SendGetRequestByForm(YmApi + ApiConsts.GET_PRINTER_INFO, parameters);
            JObject result = (JObject)JsonConvert.DeserializeObject(jsons);
            return result;
        }
        /// <summary>
        /// 查询当前打印机的基础信息
        /// </summary>
        /// <returns>json串，格式如
        /// {"return_code":0,"return_data":
        /// {"device_id":"17080253UJ","devicetype_id":2,"devicetype_name":"",
        /// "var_version":"4.0.4.2","firmware_version":"2.9.1","last_varversion":"4.0.3",
        /// "last_firmwareversion":"2.9.1"
        /// },"return_msg":""}</returns>
        private async Task<string> QueryCurrentPrinterInfoAsync()
        {
            Dictionary<string, string> parameters = new Dictionary<string, string>();
            parameters.Add("app_id", app_id);
            parameters.Add("access_token", (await YM_GetAccessTokenAsync()).data);
            parameters.Add("device_id", device_id);
            string result = SendGetRequestByForm(YmApi + ApiConsts.GET_PRINTER_INFO, parameters);
            return result;
        }
        #endregion

        #region 底层逻辑,访问令牌.签名

        /// <summary>
        /// 刷新访问令牌
        /// </summary>
        /// <returns>是否成功刷新</returns>
        private async Task<CallResult> YM_SurfAccessTokenAsync()
        {
            Dictionary<string, string> parameters = new Dictionary<string, string>();
            parameters.Add("app_id", app_id);
            parameters.Add("sign_type", "MD5");
            parameters.Add("time_stamp", CommonUtil.GetUnixTimestamp().ToString());

            string sign = GetSign(parameters);
            parameters.Add("sign", sign);

            string url = YmApi + ApiConsts.GET_TOKEN + "?" + parameters.ToParamString();
            dynamic jso = await CommonTool.GetJsonByFactory(_httpClientFactory, url);
            string jsons = JsonConvert.SerializeObject(jso);
            //获取到正常结果格式如 {"return_code":0,"return_data":{"access_token":"e051ce87478d4dc98c78cabfb133d9f1","expires_in":2592000,"create_time":"2019-02-21 19:13:19"},"return_msg":""}
            //其中access_token 是访问令牌，expires_in 是有效时长2592000秒（30天），开发者需将得到的结果保存到自己的数据库/缓存。
            dynamic res = JsonConvert.DeserializeObject(jsons);
            string result;
            if ((string)res.return_msg == "")
            {
                result = (string)res.return_data.access_token;
                int furtherExpire = int.Parse((string)res.return_data.expires_in);
                await RedisHelper.SetAsync(_YM_TOKEN_NAME, result, furtherExpire);
                YmAccessToken = result;
                YmExpires = DateTime.Now.AddSeconds(furtherExpire);
                return new CallResult("OK", "", YmAccessToken);
            }
            else
            {
                return new CallResult("Error", (string)res.return_msg); ;
            }
        }

        private dynamic SendGetRequestByForm(string url, Dictionary<string, string> parameters)
        {
            try
            {
                url += "?" + parameters.ToParamString(); 
                return CommonTool.GetJsonByFactory(_httpClientFactory,url);
            }
            catch (Exception ex)
            {
                throw new Exception("SendGetRequestByForm异常");
            }
        }

        /// <summary>
        /// 返回一个可以被APP和WEB端识别的错误结果以供报错和调试
        /// </summary>
        /// <param name="message">错误信息,请勿填写空字符或null</param>
        /// <returns>结构体,成员包括:return_msg</returns>
        private dynamic ErrorResult(string message)
        {
            var r = new
            {
                success = false,
                return_msg = message,
                return_code = "-1",
                extra_data = "",
                msg = message,
                result = "Error"
            };
            return r;
        }

        /// <summary>
        /// 获取签名
        /// </summary>
        /// <returns>MD5签名</returns>
        private string GetSign(Dictionary<string, string> dic)
        {
            StringBuilder sbQuest = new StringBuilder();
            foreach (var d in dic)
            {
                if (!d.Key.ToLower().Equals("sign"))
                {
                    var value = d.Value;
                    if (!string.IsNullOrEmpty(value))
                    {
                        sbQuest.AppendFormat("{0}={1}&", d.Key, value);
                    }
                }
            }
            var parms = sbQuest.ToString().TrimEnd('&');
            return CommonUtil.GetMD5Sign(app_key, parms);
        }

        /// <summary>
        /// 判断纸张类型是否是支持的
        /// </summary>
        private bool CheckPaperType(string type)
        {
            switch (type)
            {
                case "1":
                case "2":
                case "3": return true;
                default: return false;
            }
        }

        /// <summary>
        /// 根据打印机的devicetype_id，返回其DPI值. 
        /// </summary>
        private int GetDpiByMachineCode(string machine_code)
        {
            switch (machine_code)
            {
                case "1":
                case "2":
                case "3":
                case "6":
                case "7":
                case "8": return 203; // CLP-180
                default: return 180;
            }
        }

        /// <summary>
        /// 根据打印机的devicetype_id，返回其默认paper_width. 
        /// </summary>
        private int GetWidthByMachineCode(string machine_code)
        {
            switch (machine_code)
            {
                case "1": return 58;  // MCP-58
                case "2":               // MCP-350
                case "3":               // MCP-360
                case "4":               // FP-570
                case "5":               // MCP-610
                case "7": return 80;  // MCP-330
                case "6": return 78;  // MCP-230
                case "9": return 203; // CFP-535
                default: return 110; // CLP-180, CLQ-200FW
            }
        }

        /// <summary>
        /// mm转像素(将返回int)
        /// </summary>
        private int GetPixelByMm(float mm, int dpi)
        {
            // DPI是图像每英寸长度内的像素点数. 因此需要先将毫米换算为英寸，再根据DPI换算为像素点数量.
            float inch = Convert.ToSingle(mm) / 25.4f;
            int pixel = (int)(inch * dpi);
            return pixel;
        }

        private void SetSheetPrinterByTemplateAndSheet(SheetPrinter SheetPrinter, dynamic sheet, SheetTemplate commonTemplate, out SheetTemplate templateChoosed)
        {
            SheetTemplate template = JsonConvert.DeserializeObject<SheetTemplate>(JsonConvert.SerializeObject(commonTemplate));


            JObject jsheet = JObject.FromObject(sheet);


            jsheet.TryGetValue("printTemplate", out JToken jTemplate);
            if (jTemplate != null)
                template = JsonConvert.DeserializeObject<SheetTemplate>(JsonConvert.SerializeObject(jTemplate));
            templateChoosed = template;
            if (template == null) return;

            string docName = "";
            if (sheet.sup_name != null) docName += sheet.sup_name;
            if (template != null && template.name != null) docName += template.name;
            if (sheet.sheetType != null) docName += "-" + sheet.sheetType;
            SheetPrinter.DocumentName = docName;


            if (template.fontSize == null || template.fontSize == "")
            {
                template.fontSize = "3.5";
            }

            template.TransferSizeFromMM(null);

            SheetPrinter.Template = template;

            foreach (var k in jsheet.Properties())
            {
                if (k.Name.ToLower() == "sheetrows")
                {
                    JArray arr = (JArray)k.Value;
                    foreach (JObject jrow in arr)
                    {
                        Dictionary<string, string> row = new Dictionary<string, string>();
                        foreach (var rk in jrow.Properties())
                        {
                            row.Add(rk.Name, rk.Value.ToString());
                        }
                        SheetPrinter.TableRows.Add(row);
                    }
                }
                else SheetPrinter.MainItems.Add(k.Name, k.Value.ToString());
            }
            if (!SheetPrinter.MainItems.ContainsKey("print_time"))
                SheetPrinter.MainItems.Add("print_time", CPubVars.GetDateText(DateTime.Now));
        }

        #region 图片转pdf功能. 因[业务全面转用图片转ESC打印]和[Linux疑似不支持itext]而暂时弃用。
        /*
        /// <summary>
        /// 获取页面像素Rect
        /// </summary>
        /// 
        
        /*private RectangleReadOnly GetPageSize()
        {
            return new RectangleReadOnly(pixel_width, pixel_height);
        }*/

        /// <summary>
        /// 将List的Image传入，将其依次插入PDF中(每页一个)，然后返回PDF的b64编码
        /// </summary>

        #endregion

        #endregion

        #endregion

        #region Sw-Aiot - 商为云打印
        //public static string SwAccessToken;
        ///public static DateTime SwExpires;
        #region 账号,秘钥,接口URL
        private const string _SW_ACCOUNT_LOGINNAME = "YingJiang";
        private const string _SW_ACCOUNT_SECRET_KEY = "62a91fc51c4f5fe05be4b3c33038643f";

        private const string _SW_ACT_GET_TOKEN = "https://open.sw-aiot.com/api/getToken";
        private const string _SW_ACT_BIND = "https://open.sw-aiot.com/api/device/bindPrint";
        private const string _SW_ACT_UNBIND = "https://open.sw-aiot.com/api/device/unBindPrint";
        private const string _SW_ACT_PRINT_MSG = "https://open.sw-aiot.com/api/message/printMsg";
        private const string _SW_ACT_VOICE_MSG = "https://open.sw-aiot.com/api/message/voiceMsg";
        private const string _SW_ACT_PRINT = "http://file.sw-aiot.com/files/ptFile";
        private const string _SW_ACT_GET_DEVICE_INFO = "https://open.sw-aiot.com/api/device/getDevice/";
        private const string _SW_ACT_CLEAR_TASK = "https://open.sw-aiot.com/api/message/clear";
        #endregion
        /*public async Task<dynamic> SW_StartPrint(string device_id, string cus_orderid, byte[] file, int paper_width)
        {
            var res = await SW_PrintByByteFileAsync(device_id, cus_orderid, file, paper_width);
            return res;
        }*/

        private async Task<dynamic> SW_StartPrint(string companyID,string device_id, string cus_orderid, byte[] escCommands, int paper_width)
        {
            string url = $"http://file.sw-aiot.com/files/ptFile";


            NameValueCollection data = new NameValueCollection 
            {
                { "devid", device_id },
                { "type", "1" },
                { "reqid", cus_orderid },
                { "width", paper_width.ToString() }
            };
            CloudCallResult res = null;
            bool bGetNewToken = false;
            for (int i = 1; i <= 2; i++)
            {
                CallResult tokenRes = await SW_GetToken(bGetNewToken);
                if (tokenRes.result != "OK")
                {
                    return tokenRes;
                }

                Dictionary<string, string> headers = new Dictionary<string, string>
                {
                    { "token", tokenRes.data}
                };

                res = await SW_PostFormData(companyID, _httpClientFactory,url, headers, data, escCommands);
                if (res.IsTokenInvalid)
                {
                    bGetNewToken = true;
                }
                else
                    break;
                

            }
            return res;
        }
        class CloudCallResult : CallResult
        {
            public bool IsTokenInvalid = false;
            public bool printSuccess = false;
            public CloudCallResult(string result, string msg, string data = "", bool tokenInvalid = false) : base(result, msg, data)
            {
                IsTokenInvalid = tokenInvalid;
            }
            public CloudCallResult(string result, string msg, bool printSuccess, bool tokenInvalid) : base(result, msg, "")
            {
                this.printSuccess = printSuccess;
                this.IsTokenInvalid = tokenInvalid;
            }
        }
        private async Task<CloudCallResult> SW_BindPrinter(string device_id, string check_code, int paper_type = 1)
        {
            string device_name = device_id;
            Dictionary<string, dynamic> parameters = new Dictionary<string, dynamic>
            {
                { "devid", device_id },
                { "key", check_code },
                { "timeout", 1200 },
                { "pwidth", "50" }, // 纸张宽度随意指定, 打印时再读取模板
                { "ptype", paper_type },
                { "nickname", device_name },
                { "remark", "" }
            };
            CloudCallResult res = null;

            res = await SW_SendRequest(_SW_ACT_BIND, parameters); 
            if (res.msg.Contains("已绑定"))
            {
                res.result = "OK";
                res.msg = "";
            }
            return res;



        }

        private string SW_JsonString(int code, string msg)
        {
            return "{\"return_code\":" + code + ",\"return_msg\":\"" + msg + "\"}";
        }
        /*private string SW_GeneratePassword()
        {
            string key = "secret=" + _SW_ACCOUNT_SECRET_KEY + "&times=" + GetCurrentTimeStamp() + "&username=" + _SW_ACCOUNT_LOGINNAME;

            MD5 md5 = MD5.Create();
            byte[] buffer = Encoding.Default.GetBytes(key);
            byte[] md5buffer = md5.ComputeHash(buffer);
            string str = null;
            foreach (byte b in md5buffer)
            {
                str += b.ToString("X2");
            }
            return str; 
            
        }*/
        private async Task<CloudCallResult> SW_GetToken(bool bGetNewToken = false)
        {
            using (var Lock = RedisHelper.Lock("SW_GetToken", 10))
            {
                if (Lock == null)
                {
                    throw new Exception("获取Redis锁失败");
                }
                if (!bGetNewToken)
                {
                    string token = await RedisHelper.GetAsync(_SW_TOKEN_NAME);
                    if (!IsZeroOrNull(token))
                    {
                        return new CloudCallResult("OK", "", token);
                    }
                }

                CloudCallResult result = null;
                for (int i = 1; i <= 3; i++)
                {
                    result = await SW_GetNewToken();
                    if (result.IsOK)
                    {
                        break;
                    }
                }
                return result;
            }
        }
        /* private async Task<CallResult> SW_RefreshToken()
         {
             Dictionary<string, dynamic> parameters = new Dictionary<string, dynamic>
             {
                 { "username", _SW_ACCOUNT_LOGINNAME },
                 { "password", SW_GeneratePassword() },
                 { "times", GetCurrentTimeStamp() }
             };
             dynamic res = await SW_SendRequest(_SW_ACT_GET_TOKEN, parameters, true);
             if (res.result == "OK")
             {
                 string token = res.rawRes.data.token;
                 await RedisHelper.SetAsync(_SW_TOKEN_NAME, token, 7000);
                 return new CallResult("OK","", token);
             }
             return new CallResult("Error","刷新令牌失败" + res.msg);
         }*/
        /*
        private async Task<bool> SW_UnBindPrinterAsync(string device_id, string check_code)
        {
            Dictionary<string, dynamic> parameters = new Dictionary<string, dynamic>
            {
                { "devid", device_id },
                { "key", check_code }
            };
            dynamic = await SW_SendRequest(_SW_ACT_UNBIND, parameters);
            JObject jo = (JObject)JsonConvert.DeserializeObject(jsons);
            return jo["success"].ToString() == "True";
        }*/
        /* private async Task<dynamic> SW_PrintByEscAsync_NOT_USED(string device_id, string sheet_no, string escCommand, int paper_width)
         {
             string printTaskId = sheet_no + "_" + DateTime.Now.ToString("yyyy-mm-dd_hh-mm-ss");
             // 商为云打印会识别reqid, 推送id相同的重复打印信息时不会重复打印, 故在此生成动态的reqid
             // 以此格式组装是为了便于可能的Bug处理
             Dictionary<string, dynamic> parameters = new Dictionary<string, dynamic>
             {
                 { "devid", device_id },
                 { "reqid", printTaskId },
                 { "message", escCommand },
                 { "type", 1 },
                 { "pwidth", paper_width }
             };
             dynamic res = await SW_SendRequest(_SW_ACT_PRINT_MSG, parameters);
             return res;


         }*/

        
        public async Task<string> SW_ClearAllWaitingTask(string device_id, bool fromRetry = false)
        {
            Dictionary<string, dynamic> parameters = new Dictionary<string, dynamic>
            {
                { "devid", device_id }
            };
            var response = await SW_DoCommonPostAsync(_SW_ACT_CLEAR_TASK, parameters);

            if (response != "true" && !fromRetry)
            {
                dynamic res = JsonConvert.DeserializeObject(response);
                string code = res.code;

                if (code == "401") // [401/非法访问]代表token过期或无效
                {
                    await SW_GetToken(true);
                    response = await SW_DoCommonPostAsync(_SW_ACT_CLEAR_TASK, parameters);
                }

            }
            return response;
        }

        /// <summary>
        /// 商为少数接口返回的不是合法JSON串,因此针对这些接口单独实现一个返回string的POST方法
        /// </summary>
        /// <returns>
        /// response content in string format if SUCCESS, null if FAILED.
        /// </returns>
        private async Task<string> SW_DoCommonPostAsync(string url, Dictionary<string, dynamic> param)
        {
            try
            {
                string strParam = JsonConvert.SerializeObject(param);

                using var client = CPubVars.GetHttpClientFromFactory(_httpClientFactory);
                client.DefaultRequestHeaders.Add("Accept", "application/json");

                CloudCallResult tokenRes = await SW_GetToken();

                if (tokenRes.result != "OK")
                    return null;
                client.DefaultRequestHeaders.Add("token", tokenRes.data);

                using HttpContent content = new StringContent(strParam);
                content.Headers.ContentType.MediaType = "application/json";

                using var response = await client.PostAsync(url, content);
                var s = await response.Content.ReadAsStringAsync();
                Console.WriteLine(s);

                return s;
            }
            catch (Exception ex)
            {
                NLogger.Error(ex.ToString());
                return null;
            }
        }

        private async Task<CloudCallResult> SW_SendRequest(string url, Dictionary<string, dynamic> param)
        {
            string strParam = JsonConvert.SerializeObject(param);


            string result = "OK", msg = "";
            bool bGetNewToken = false;
            bool IsTokenInvalid = false;
            for (int i = 1; i <= 2; i++)
            {
                using (HttpClient client = CPubVars.GetHttpClientFromFactory(_httpClientFactory))
                {
                    client.DefaultRequestHeaders.Add("Accept", "application/json");

                    CloudCallResult tokenRes = await SW_GetToken(bGetNewToken);

                    if (tokenRes.result != "OK")
                    {
                        return tokenRes;
                    }
                    client.DefaultRequestHeaders.Add("token", tokenRes.data);

                    using (HttpContent content = new StringContent(strParam))
                    {
                        content.Headers.ContentType.MediaType = "application/json";
                        using (var response = await client.PostAsync(url, content))
                        {
                            var s = await response.Content.ReadAsStringAsync();
                            #if DEBUG
                            NLogger.Info(s);
                            #endif
                            dynamic res = JsonConvert.DeserializeObject(s);

                            if (res.code == "401") // [401/非法访问]代表token过期或无效
                            {
                                bGetNewToken = true;
                                IsTokenInvalid = true;
                                result = "Error";
                                msg = (string)res.message;
                            }
                            else
                            {
                                IsTokenInvalid = false;
                                if (res.success.ToString().ToLower() != "true")
                                {
                                    result = "Error"; msg = (string) res.message;
                                }
                                else
                                {
                                    result = "OK"; msg = "";
                                }
                                break;
                            }
                        }
                    }
                }
            }
            return new CloudCallResult(result, msg, "", IsTokenInvalid);
        }

        private async Task<CloudCallResult> SW_GetNewToken()
        {

            TimeSpan ts = DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0);
            string timeStamp = Convert.ToInt64(ts.TotalMilliseconds).ToString();

            string key = "secret=" + _SW_ACCOUNT_SECRET_KEY + "&times=" + timeStamp + "&username=" + _SW_ACCOUNT_LOGINNAME;

            MD5 md5 = MD5.Create();
            byte[] buffer = Encoding.Default.GetBytes(key);
            byte[] md5buffer = md5.ComputeHash(buffer);
            string pwd = null;
            foreach (byte b in md5buffer)
            {
                pwd += b.ToString("X2");
            }

            Dictionary<string, dynamic> parameters = new Dictionary<string, dynamic>
            {
                { "username", _SW_ACCOUNT_LOGINNAME },
                { "password", pwd },
                { "times", timeStamp}
            };

            string strParam = JsonConvert.SerializeObject(parameters);


            HttpClient client = CPubVars.GetHttpClientFromFactory(_httpClientFactory);

            client.DefaultRequestHeaders.Add("Accept", "application/json");

            HttpContent content = new StringContent(strParam);
            content.Headers.ContentType.MediaType = "application/json";
            var response = await client.PostAsync(_SW_ACT_GET_TOKEN, content);
            var s = await response.Content.ReadAsStringAsync();
            dynamic res = JsonConvert.DeserializeObject(s);
            client.Dispose();
            string result = "OK", msg = "";
            string data = "";
            if (res.success.ToString().ToLower() != "true")
            {
                result = "Error";
                msg ="获取Token时发生错误:" + (string)res.message;
            }
            else
            {
                data = res.data.token;
                await RedisHelper.SetAsync(_SW_TOKEN_NAME, data, 7000);
            }
            return new CloudCallResult(result, msg, data);
        }

        #endregion

        #region 通用方法

        /// <summary>
        /// 检查给定公司是否拥有给定的云打印机
        /// </summary>
        /// <param name="deviceId">云打印机的制造编号</param>
        /// <param name="printerBrand">云打印机的品牌(直接传入数据库字段,如'ym','sw')</param>
        /// <returns>CallResult,data类型为bool,为true时表示此公司拥有这台打印机,为false时表示不拥有或有错误</returns>
        private async Task<CallResult<bool>> CheckCompanyHasThisPrinter(string companyId, string deviceId,
            string printerBrand = "sw")
        {
            if (companyId.IsInvalid() || deviceId.IsInvalid())
                return new CallResult<bool>("Error", "未登录或设备编号缺失", false);

            var sql_queue = new SQLQueue(cmd);
            var sql = $@"
                SELECT
                    count(printer_id) as valid_printer_amount
                FROM
                    info_cloud_printer
                WHERE
                    company_id = {companyId}
                    and device_id = '{deviceId}'
                    and printer_brand = '{printerBrand}'
                LIMIT
                    5;
            ";
            sql_queue.Enqueue("getList", sql);
            using var dr = await sql_queue.ExecuteReaderAsync();

            bool result = false;
            while (sql_queue.Count > 0)
            {
                string sqlName = sql_queue.Dequeue();

                if (sqlName == "getList")
                {
                    var records = CDbDealer.GetRecordsFromDr(dr, false);
                    if (records.Count > 0)
                    {
                        dynamic record = records[0];
                        string valid_printer_amount = record.valid_printer_amount;
                        int.TryParse(valid_printer_amount, out int amount);
                        result = amount > 0;
                    }
                }
            }
            sql_queue.Clear(); dr.Close();
            return new CallResult<bool>("OK", "", result);
        }
        /*private byte[] TransferImagesToEscCommands(List<Image> images)
        {
            List<byte[]> data = new List<byte[]>();
            foreach (Image image in images)
            {
                data.Add(HeadEscCommand(image));
                data.Add(TransferImageToBytes(image));
                data.Add(TailEscCommand(image));
                image.Dispose();
            }
            byte[] bytes = MergeByteArrayLists(data);
            return bytes;
            //PrintBytes(bytes);
            //return TransferBytesToString(bytes);
        }*/

        private string SaveBytesToLocal(byte[] bytes)
        {
            string path = Environment.CurrentDirectory + "/wwwroot/uploads/cache_" + DateTime.Now.ToString("yyyy-MM-dd_hh-mm-ss-fffff") + ".dat";
            System.IO.File.WriteAllBytes(path, bytes);
            return path;
        }
        private void DeleteFile(string filepath)
        {
            if (System.IO.File.Exists(filepath))
            {
                System.IO.File.Delete(filepath);
            }
        }
        static string ByteArrayToHexString(byte[] byteArray)
        {
            return BitConverter.ToString(byteArray).Replace("-", "").ToLower();
        }
        /// <summary>
        /// 使用multipart/form-data方式上传文件及其他数据
        /// </summary>
        /// <param name="headers">请求头参数</param>
        /// <param name="nameValueCollection">键值对参数</param>
        /// <param name="fileCollection">文件参数：参数名，文件路径</param>
        /// <returns>接口返回结果</returns>
        private static async Task<CloudCallResult> SW_PostFormData(string companyID, IHttpClientFactory httpClientFactory,string url, Dictionary<string, string> headers, NameValueCollection nameValueCollection, byte[] fileBytes)
        {
            var client = CPubVars.GetHttpClientFromFactory(httpClientFactory);
           
                foreach (var item in headers)
                {
                    client.DefaultRequestHeaders.Add(item.Key, item.Value);
                }
                string hex = ByteArrayToHexString(fileBytes);

                using (var content = new MultipartFormDataContent())
                {
                    string[] allKeys = nameValueCollection.AllKeys;
                    foreach (string key in allKeys)
                    {
                        var value = nameValueCollection[key];
                        var dataContent = new ByteArrayContent(Encoding.UTF8.GetBytes(value));
                        dataContent.Headers.ContentDisposition = new ContentDispositionHeaderValue("form-data")
                        {
                            Name = key
                        };
                        content.Add(dataContent);
                    }

                    using (var fileContent = new ByteArrayContent(fileBytes))
                    {
                        fileContent.Headers.ContentDisposition = new ContentDispositionHeaderValue("form-data")
                        {
                            Name = "file",
                            FileName = $"annoy_cache_{DateTime.Now:yyyy-MM-dd_hh-mm-ss-fffff}.dat"
                        };
                        content.Add(fileContent);

                        using (var response = await client.PostAsync(url, content))
                        {
                            string s = await response.Content.ReadAsStringAsync();
						    NLogger.Info("商为返回" + s);
						    dynamic res = null;

							try
                            {
							     res = JsonConvert.DeserializeObject(s);
						    }
                            catch(Exception e)
                            {
                                MyLogger.LogMsg($"Errir pase SW result:{s},err:{e.Message}", companyID);
                            }
                          
                            string result = "OK", msg = "";
                           
                            //bool success = true; string return_msg = "";//待删
                            string code = (string) res.code;
                            bool IsTokenInvalid = (code == "401");
                            bool printSuccess = false;
                            if (res.success.ToString().ToLower() != "true")
                            {
                                result = "Error"; msg = res.message;
                            }
                            else
                            {
                                if (res.data.success.ToString().ToLower() == "true")
                                {
                                    string innerCode = (string)res.data.respCode;
                                    if (innerCode == "0" || innerCode == "000")
                                    {
                                        msg = "打印成功"; printSuccess = true;
                                    }
                                    else if (innerCode == "010" || innerCode == "012")
                                    {
                                        msg = "已加入打印队列"; printSuccess = true;
                                    }
                                    else if (innerCode == "011")
                                    {
                                        msg = "打印机未联网,3分内联网可以打印";
                                    }
                                    else if (innerCode == "101")
                                    {
                                        msg = "打印机缺纸";
                                    }
                                    else if (innerCode == "300" || innerCode == "301")
                                    {
                                        msg = "打印机正在升级,请等待";
                                    }
                                }
                                else
                                {
                                    msg = "打印失败:" + res.data.message;
                                    result = "Error";
                                }
                            }
                            return new CloudCallResult(result, msg, printSuccess, IsTokenInvalid);
                        }
                    }
                }            
        }
        private string TransferBytesToString(byte[] bytes)
        { 
            return Convert.ToBase64String(bytes); 
        }
        /*
        private byte[] TransferImageToBytes(Bitmap bmp)
        {
            // Convert to Bitmap, Calculate params.
            Bitmap bmp = new Bitmap(image);
            int w = bmp.Width, h = bmp.Height;
            int loopTime = h / 24, finalHeight = h % 24;
            if (finalHeight != 0) { loopTime++; }
            h = loopTime * 24;

            // >>> Way of GetPixel. Retained in case for need.
            

            // Use BitmapData rather than GetPixel to improve efficiency.
            Rectangle rect = new Rectangle(0, 0, bmp.Width, bmp.Height);
            BitmapData bmpData = bmp.LockBits(rect, ImageLockMode.ReadOnly, bmp.PixelFormat);

            IntPtr ptr = bmpData.Scan0;
            int bytes = bmpData.Stride * bmp.Height;
            byte[] rgbValues = new byte[bytes];
            System.Runtime.InteropServices.Marshal.Copy(ptr, rgbValues, 0, bytes);
            int counter;

            // Average RGB value.
            for (counter = 0; counter < rgbValues.Length; counter += 4)
                rgbValues[counter] = (byte)((rgbValues[counter] + rgbValues[counter + 1] + rgbValues[counter + 2]) / 3);

            // Convert to ImageMap.
            int[,] imageMap = new int[h, w]; // h行w列
            counter = 0;
            for (int i = 0; i < h; i++)
                for (int j = 0; j < w; j++)
                {
                    if (counter >= rgbValues.Length)
                    {
                        imageMap[i, j] = 0; continue;
                    }
                    imageMap[i, j] = rgbValues[counter] <= 160 ? 1 : 0;
                    counter += 4;
                }

            // Convert to bytes.
            List<byte[]> datas = new List<byte[]>();
            List<int> sums = new List<int>();
            int _contentSize = w * h / 8, _extraSize = 9;
            int _bodySize = _contentSize + _extraSize * loopTime - 3;
            byte n2 = (byte)(w / 256);
            byte n1 = (byte)(w % 256);
            for (int loop = 0; loop < loopTime; loop++)
            {
                int content_size = w * 3;
                int extra_size = loop == loopTime - 1 ? 6 : 9;
                byte[] result = new byte[content_size + extra_size];
                int resPtr = 0;

                int loopedLines = loop * 24;

                // Head command bytes
                result[resPtr++] = 27; // 1B, ESC
                result[resPtr++] = 42; // 2A, *
                result[resPtr++] = 39; // 27, m
                result[resPtr++] = n1;
                result[resPtr++] = n2;

                // Body command bytes
                int sum = 0;
                for (int j = 0; j < w; j++)
                {
                    for (int i = loopedLines; i < loopedLines + 24; i += 8)
                    {
                        int num = 0;
                        for (int a = i; a < i + 8; a++)
                            num += imageMap[a, j] * (int)Math.Pow(2, 7 - (a - i));
                        sum += num;
                        result[resPtr++] = (byte)num;
                    }
                }

                // Tail command bytes
                result[resPtr++] = 13;                  // 0D, 回车
                if (loop < loopTime - 1)               // 最后一组不需要走纸
                {
                    result[resPtr++] = 27;              // 1B
                    result[resPtr++] = 74;              // 4A
                    result[resPtr++] = 24;              // 18, 走纸
                }

                // Save to list
                datas.Add(result);
                sums.Add(sum);
            }

            int whiteCount = 0;
            byte[] lastbyte = datas[sums.Count - 1];
            sums.RemoveAt(sums.Count - 1);
            datas.RemoveAt(sums.Count - 1);
            for (int i = sums.Count - 2; i >= 0; i--)    // 最后一行不处理
            {
                if (sums[i] == 0)
                {
                    whiteCount++;
                    datas.RemoveAt(i);
                }
                if (sums[i] != 0 && whiteCount != 0)
                {
                    datas.Insert(i + 1, CalculateWhiteBytes(whiteCount));
                    whiteCount = 0;
                }
            }
            datas.Add(lastbyte);
            bmp.UnlockBits(bmpData);
            return MergeByteArrayLists(datas);
        }
        private byte[] HeadEscCommand(Image image)
        {
            int _MAX_PAGE_HEIGHT = 22 * 180;
            int ht = image.Height > _MAX_PAGE_HEIGHT ? _MAX_PAGE_HEIGHT : image.Height;
            //ht = ht;
            byte mH = (byte)(ht / 256);
            byte mL = (byte)(ht % 256);
            byte[] bytes = new byte[15]
            {
                27, 64,                    // 初始化            | 1b 40
                27, 40, 85, 1, 0, 20,      // 设置页长单位为1像素 | 1B 28 55 01 00 m; m可为10/20/...
                27, 40, 67, 2, 0, mL, mH   // 设置页长           | 1B 28 43 02 00 mL mH
            };
            return bytes;
        }
        private byte[] TailEscCommand(Image image)
        {
            int _MAX_PAGE_HEIGHT = 22 * 180;
            int size = image.Height > _MAX_PAGE_HEIGHT ? 8 : 1;
            byte[] bytes = size == 1 ? new byte[1] { 0x0C } // 换页
                : new byte[8] { 27, 40, 67, 2, 0, 1, 0, 0x0C }; // 暂时用不到 备用
            return bytes;
        }

        private byte[] CalculateWhiteBytes(int count)
        {
            // 13 27 74 n, n:0~255
            // 走纸n个像素
            int totalHeight = count * 24;
            if (totalHeight < 255)
            {
                return new byte[] { 13, 27, 74, (byte)totalHeight };
            }
            else
            {
                int time = totalHeight % 255 == 0 ? totalHeight / 255 : totalHeight / 255 + 1;
                List<byte[]> bytes = new List<byte[]>();
                for (int i = 0; i < time - 1; i++)
                {
                    bytes.Add(new byte[] { 13, 27, 74, 255 });
                }
                bytes.Add(new byte[] { 13, 27, 74, (byte)(totalHeight % 255) });
                byte[] byts = MergeByteArrayLists(bytes);
                return byts;
            }
        }
        
        */
        private static NameValueCollection ConvertToNameValueCollection<tValue>(IDictionary<string, tValue> dictionary)
        {
            var collection = new NameValueCollection();
            foreach (var pair in dictionary)
                collection.Add(pair.Key, pair.Value.ToString());
            return collection;
        }

        private static string ImageToBase64(Image bmp)
        {
            try
            {
                using (MemoryStream ms = new MemoryStream())
                {
                    bmp.Save(ms, ImageFormat.Jpeg);
                    byte[] arr = new byte[ms.Length]; ms.Position = 0;
                    ms.Read(arr, 0, (int)ms.Length); ms.Close();
                    return Convert.ToBase64String(arr);
                }

            }
            catch (Exception ex)
            {
#if DEBUG
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error($"在尝试将某个IMAGE转为BASE64字符串时遇到了问题。请插入断点以检查。报错信息：{ex}");
#endif
                return null;
            }
        }
        /*
        private string StringToBase64(string Str, Encoding encode = null)
        {
            encode ??= Encoding.Default;
            byte[] b = encode.GetBytes(Str);
            return Convert.ToBase64String(b);
        }*/

        public static object g_gdiSyncLock = new { };
        private void Sheet2Images(dynamic sheet, dynamic tmp, Func<Bitmap, string> cbDeal)
        {
            string s = JsonConvert.SerializeObject(tmp);
            SheetTemplate template = JsonConvert.DeserializeObject<SheetTemplate>(s);
            var lstSheetsWithTemplate = new List<SheetsWithTemplate>();
            var tempSheets = new SheetsWithTemplate();
            tempSheets.template = template;
            tempSheets.sheets = new List<dynamic>();
            tempSheets.sheets.Add(sheet);
            lstSheetsWithTemplate.Add(tempSheets);

            #region 废弃代码. 计算sheet的总高度
            // 2. 计算总高度，确认height
            /*
            int printHeight = 0;
            foreach (var tmp_sheets in lstSheetsWithTemplate)
            {
                foreach (var tmp_sheet in tempSheets.sheets)
                {
                    var SheetPrinter = new SheetPrinter();
                    SetSheetPrinterByTemplateAndSheet(SheetPrinter, sheet, tempSheets.template, out SheetTemplate templateChoosed);
                    
                    if (templateChoosed == null)
                    {
                        if (noTemplateSheetNos != "") noTemplateSheetNos += ",";
                        noTemplateSheetNos += (string)sheet.sheet_no;
                        continue;
                    }
                    nSheet++;
                    SheetPrinter.PrintPrepare();
                    int nLeft = 0;
                    for (int i = 0; i < 100; i++)
                    {
                        PrintPageEventArgs arg = null;// new PrintPageEventArgs(gg, Rectangle.Empty, Rectangle.Empty, null);
                        SheetPrinter.PageTop = nTop;
                        SheetPrinter.PageLeft = nLeft;
                        // gg.DrawString(nSheet.ToString() + "-" + (i + 1).ToString(), font, Brushes.Black, new PointF(nLeft - 60, nTop));
                        // gg.FillRectangle(Brushes.White, new Rectangle(nLeft, nTop, (int)templateChoosed.width, (int)templateChoosed.height)); //(int)templateChoosed.height);//);
                        SheetPrinter.PrintPage(arg, i, out bool bFinished);
                        nTop += (int)templateChoosed.height;
                        printHeight += (int)templateChoosed.height;
                        // images.Add(image);
                        if (bFinished)
                            break;
                    }
                }
            }
            pixel_height = GetPixelByMm(printHeight,dpi);
            */
            #endregion

            int nTop;
            int nSheet = 0;
            string noTemplateSheetNos = "";
            List<Image> lstImages = new List<Image>();
            PrintElement.g_bServerRender = true;

            foreach (var tmp_sheets in lstSheetsWithTemplate)
            {
                foreach (var tmp_sheet in tempSheets.sheets)
                {
                    using (var printer = new SheetPrinter())
                    {
                        SetSheetPrinterByTemplateAndSheet(printer, sheet, tempSheets.template, out SheetTemplate templateChoosed);

                        if (templateChoosed == null)
                        {
                            if (noTemplateSheetNos != "") noTemplateSheetNos += ",";
                            noTemplateSheetNos += (string)sheet.sheet_no;
                            continue;
                        }

                        nSheet++;

                        printer.PrintPrepare();
                        int nLeft = 0;
                        int pixel_width = (int)templateChoosed.width;
                        int pixel_height = (int)templateChoosed.height;

                        for (int i = 0; i < 100; i++)
                        {
                            using (Bitmap b = new Bitmap(pixel_width, pixel_height))
                            {
                                bool bFinished = false;
                                lock (SheetPrinter.g_gdiSyncLock)//如果不lock,两个线程同时操作gdi对象会有冲突，各种绘图操作会返回objectBusy的异常
                                {
                                    using (Graphics gg = Graphics.FromImage(b))
                                    {
                                        gg.FillRectangle(Brushes.White, 0, 0, pixel_width, pixel_height);

                                        nTop = 0; // Gap
                                        PrintPageEventArgs arg = new PrintPageEventArgs(gg, Rectangle.Empty, Rectangle.Empty, null);
                                        printer.PageTop = nTop;
                                        printer.PageLeft = nLeft;
                                        DateTime tm = DateTime.Now;

                                        printer.PrintPage(arg, i, out bFinished);
                                        double a = (DateTime.Now - tm).TotalMilliseconds;
                                    }
                                }                             
                                cbDeal(b);
                                if (bFinished)
                                    break;
                            }


                        }
                    }

                }
            }

            lstSheetsWithTemplate.Clear();

            // return lstImages;

        }
    
        private void Sheet2EscOrImage(dynamic sheet, dynamic tmp, int fromPage, int toPage, List<byte[]> lstEscCmd, List<string> lstImageBase64, bool ignorePaperFeedCmd)
        {
            string s = JsonConvert.SerializeObject(tmp);
            SheetTemplate template = JsonConvert.DeserializeObject<SheetTemplate>(s);
            var lstSheetsWithTemplate = new List<SheetsWithTemplate>();
            var tempSheets = new SheetsWithTemplate();
            tempSheets.template = template;
            tempSheets.sheets = new List<dynamic>();
            tempSheets.sheets.Add(sheet);
            lstSheetsWithTemplate.Add(tempSheets);
            PrintPublicInfo pubInfo = null;
            if(fromPage!=-1 && toPage != -1)
            {
                pubInfo = new PrintPublicInfo();
                pubInfo.FromPage = fromPage;
                pubInfo.ToPage = toPage;
            }
            //List<byte[]> lstData = null;
          //  if(lstEscCmd)
           //   lstData = new List<byte[]>();

            //int nTop;
            int nSheet = 0;
            string noTemplateSheetNos = "";
            //List<Image> lstImages = new List<Image>();
            PrintElement.g_bServerRender = true; 

            foreach (var tmp_sheets in lstSheetsWithTemplate)
            {
                foreach (var tmp_sheet in tempSheets.sheets)
                {
                    using (var printer = new SheetPrinter())
                    {
                        printer.PubInfo = pubInfo;
                        SetSheetPrinterByTemplateAndSheet(printer, sheet, tempSheets.template, out SheetTemplate templateChoosed);

                        if (templateChoosed == null)
                        {
                            if (noTemplateSheetNos != "") noTemplateSheetNos += ",";
                            noTemplateSheetNos += (string)sheet.sheet_no;
                            continue;
                        }
                        nSheet++;
                        
                        printer.PrintToEscOrImage(lstEscCmd, lstImageBase64, ignorePaperFeedCmd);                                           
                    }
                }
            }
           
            lstSheetsWithTemplate.Clear(); 

        }
       
        private bool IsZeroOrNull(string s)
        {
            return s == "0" || s == "" || s == null;
        }
   
        /*private void SaveToLocal(string str)
        {
            string path = "d:\\debug_cache\\Cache_" + DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss") + ".bin";
            StreamWriter sw = new StreamWriter(path);//这里写上你要保存的路径
            sw.WriteLine(str);//按行写
            sw.Close();//关闭
        }*/
        private byte[] MergeByteArrayLists(List<byte[]> byteArrays)
        {
            // According to reference, list->[] is 26x faster than Array.Copy() and BlockCopy().
            List<byte> mergedBytes = new List<byte>();
            foreach (byte[] byteArray in byteArrays)
            {
                mergedBytes.AddRange(byteArray);
            }
            byte[] result = mergedBytes.ToArray();
            return result;
        }
        private dynamic SetResult(bool success, string msg)
        {
            Dictionary<string, dynamic> dict = new Dictionary<string, dynamic>();
            dict.Add("code", success);
            dict.Add("msg", msg);
            return JsonConvert.SerializeObject(dict);
        }
        #endregion
    }
}
