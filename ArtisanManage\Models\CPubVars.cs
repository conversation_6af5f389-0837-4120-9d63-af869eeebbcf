using ArtisanManage.Services;
using Jint.Parser;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Dynamic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;

namespace ArtisanManage.Models
{
    public class CPubVars
    {
       // public static string ConnString { get; set; }
    
       // public static Dictionary<string, string> g_Databases = new Dictionary<string, string>();
 
        //public static string ConnString = "";
        public static string g_RunByJexus = "";
        public static string g_ListenHost = "";

        public static string g_SslPfxFile = "";
        public static string g_SslPfxPwd = "";

        public static string SettingBrief = "";
        public static string BriefColor = "";
        public static string g_Version = "10.143";
        public static string BaiduKey = "zbQcVkpGET83cqhFyGIrmqrmkJMiAY4F";
        public static string BaiduApiKey = "nBIBdYlwIUiTU3Trtq5kXOSxBNm3lPhz";//后端

		public static bool g_bUseProduceDate = true;
      
        public static string RequestV(Microsoft.AspNetCore.Http.HttpRequest request,string param)
        {
            object ov = request.Query[param]; string value = null; if (ov != null) value = ov.ToString(); return value;
        }
        public static string GetCleanSeperateIDs(string s)
        {
            string[] arr = s.Split(',');
            string res = ",";
            foreach (string c in arr)
            {
                if (c != "")
                {
                    res += c+",";
                }
            }
            return res;
        }

        /*public static decimal TryToDecimal(object s)
        {
            try
            {
                decimal d= CPubVars.ToDecimal(Convert.ToDouble(s));
                return d;
            }
            catch (Exception)
            {
                return 0;
            }
         
        }*/
        public static string GetFlexLikeStr(string s)
		{
			s = s.Replace(" ", "");
			string m = "";			
			string pre_c = "";
            string num_str = "1234567890gl克斤k";
            for(int i = 0; i < s.Length; i++)
			{
                string c = s.Substring(i, 1);
                if (m != "")
                {
                    if(!(num_str.Contains(pre_c,StringComparison.OrdinalIgnoreCase) && num_str.Contains(c, StringComparison.OrdinalIgnoreCase)))
					{
                        m += "%";
                    }
                }
                m += c;
                pre_c = c;

            }
           


			return m;
		}
        public static string GetPyStrFlexLikeStr(string s)
        {
            string m = ""; 
            bool isLetter_pre = false;
            int preContinous = 0;
            int lettersLen = 2;
            int digitsLen = 1;
            var cArr = s.ToCharArray();
            for (int i = 0; i < cArr.Length; i++)
            {
                char c = cArr[i];
                bool isLetter = char.IsLetter(c);
            
                if (isLetter != isLetter_pre && i>0)
                {
                    int preLen = isLetter_pre ? lettersLen : digitsLen;
                    if (!isLetter_pre || preContinous >= preLen)
                    {
                        int nextLen = isLetter ? lettersLen : digitsLen;
                        int nextContinous = 0;
                        for (int j = i;  j< cArr.Length; j++)
                        {
                            char c1 = cArr[j];
                            bool nextIsLetter = char.IsLetter(c1);
                            if (nextIsLetter == isLetter)
                            {
                                nextContinous++;
                                if (nextContinous >= nextLen)
                                { 
                                    break;
                                }
                            }
                            else
                            {
                                break;
                            }
                        }
                        if (preContinous>=preLen && nextContinous>= nextLen)
                        {
                            if (m != "") m += "%";
                        }
                    }
                }
            
                m += c;

                if (isLetter == isLetter_pre || i == 0)
                {
                    preContinous++;
                }
           
                isLetter_pre = isLetter;
            }
            return m;
        }
        public static string GetBMSQty(string b_qty,string m_qty,string s_qty)
		{
            string s = "";
            if (b_qty != "" && b_qty != "0") s += b_qty + "大";
            if (m_qty != "" && m_qty != "0") s += m_qty + "中";
            if (s_qty != "" && s_qty != "0") s += s_qty + "小";
            return s;

        }

        public static int CompareString(string a,string b)
        {
            if(!string.IsNullOrEmpty(a) && !string.IsNullOrEmpty(b))
            {
                char a0 = a[0];
                char b0 = b[0];
                if (a0<=127 && b0>127) return -1;
                else if (b0 <= 127 && a0 > 127) return 1; 
            }
            return String.Compare(a, b);
        }
        public static decimal TryToDecimal(string d)
        {
            if (!d.IsValid()) return 0;
            return Decimal.Parse(d, System.Globalization.NumberStyles.Float);
        }
        public static decimal ToDecimal(string d)
        {            
           return Decimal.Parse(d, System.Globalization.NumberStyles.Float);
        }
        public static decimal ToDecimal(dynamic d)
        {
            return Decimal.Parse((string)d, System.Globalization.NumberStyles.Float);
        }

        public static decimal ToDecimal(double d)
        {
            return Convert.ToDecimal(d); 
        }
        public static decimal ToDecimal(decimal d)
        {
            return d;
        }
        public static decimal ToDecimal(int d)
        {
            return Convert.ToDecimal(d);
        }
    
        public static string GetPropertyFromTag(string code,int nStart,string tagName, string propertyName)
        {
            int n = code.IndexOf("<"+tagName, nStart, 100, StringComparison.OrdinalIgnoreCase);
            if (n == -1) return "";

            n += tagName.Length;
            n = code.IndexOf(propertyName, n + 1, 1000, StringComparison.OrdinalIgnoreCase);
            if (n == -1)
            {
                throw new Exception("columns should be specified in RELATE_COLUMNS block");
            }
          
            n = code.IndexOf("=", n + 1, 100, StringComparison.OrdinalIgnoreCase);
            string prop = "";
            if (n != -1)
            {
                int n1 = code.IndexOf("\"", n + 1, 1000, StringComparison.OrdinalIgnoreCase);
                if (n1 != -1)
                {
                    int n2 = code.IndexOf("\"", n1 + 1, 1000, StringComparison.OrdinalIgnoreCase);
                    prop = code.Substring(n1 + 1, n2 - 1 - (n1 + 1) + 1);
                }

            }
            return prop;
        }

        public static bool GetBool(dynamic d)
        {
            if (d == null) return false;
            return Convert.ToBoolean(d);
        }
        public static float GetFloat(dynamic d)
        {
            string s = d;
            if (s != "" && s != null) return Convert.ToSingle(s);
            return 0;
        }
        public static string GetStr(dynamic d)
        {
            if (d == null) return "";
            return Convert.ToString(d);
        }
        public static bool IsNumeric(object s)
        {
            if (s == null)
                return false;
            if (s == DBNull.Value)
                return false;
            if (s.ToString().Trim() == "")
                return false;

            double n = 0;
            if (s == null)
                return false;

            if (double.TryParse(s.ToString(), out n))
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        public class IgnoreReadonlyPropertiesResolver : DefaultContractResolver
        {
            protected override JsonProperty CreateProperty(System.Reflection.MemberInfo member, MemberSerialization memberSerialization)
            {
                JsonProperty property = base.CreateProperty(member, memberSerialization);
                if (property.PropertyName == "sale_amount" || property.PropertyName == "return_amount")
                {

                }
                // Ignore only readonly properties
                if (!property.Writable)
                {
                    //property.ShouldSerialize = _ => false;
                    property.ShouldDeserialize = _ => false;
                }
                else if (property.PropertyType != typeof(string))
                {
                    property.ShouldDeserialize = instance =>
                    {
                         
                        if (property.PropertyName == "return_amount")
                        {
                            return false;
                        }
                        try
                        {
                            object value = property.ValueProvider.GetValue(instance);
                            if (value != null && value.ToString() != "" && !value.ToString().Contains("null"))
                            {
                                return true;
                            }
                            return false;
                        }
                        catch(Exception e){
                            throw;// new Exception(value);
                        }

                      
                    };
                }
                return property;
            }
        }
        public static T FromJsonObj<T>(dynamic d)
        {
            var settings = new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore,
                ContractResolver = new IgnoreReadonlyPropertiesResolver()
            };
            string s = JsonConvert.SerializeObject(d);
            s = s.Replace("\"NaN\"", "\"\"");

            return  JsonConvert.DeserializeObject<T>(s, settings);             
        }
        public static string FormatMoney(decimal money)
        {
            //string s = money.ToString("F2");
            //if (s.Substring(s.Length - 3, 3) == ".00")
            //    s = s.Substring(0, s.Length - 3);
            string s = FormatMoney(money, 2);
            return s;
        }
        public static decimal GetMoneyValue(decimal m)
        {
            string s = FormatMoney(m, 6, 2);
            return CPubVars.ToDecimal(s);
        }
        public static string FormatMoney(string money, int dot_len)
        {
            if (!CPubVars.IsNumeric(money))
                return money;
            double n = Convert.ToDouble(money);
            return FormatMoney(n, dot_len);
        }
        public static string FormatMoney(object o_money, int dot_len, bool bKeepZero)
        {
            if (o_money == null || o_money == DBNull.Value)
                return "";
            if (!CPubVars.IsNumeric(o_money))
                return o_money.ToString();
            double money = Convert.ToDouble(o_money);
            if (money == 0) money = 0; // 极少数情况会出现 -0.00
            string s = money.ToString("F" + dot_len.ToString());
            if (bKeepZero)
                return s;
            int n_dot = s.IndexOf('.');
            if (n_dot > 0)
            {
                int n_end = s.Length - 1;
                for (int n = s.Length - 1; n >= n_dot; n--)
                {
                    string s_cur = s.Substring(n, 1);
                    if (s_cur == ".")
                    {
                        n_end = n - 1;
                    }
                    else if (s_cur != "0")
                    {
                        n_end = n;
                        break;
                    }
                }
                s = s.Substring(0, n_end + 1);
            }
            return s;
        }
        public static string FormatMoney(object o_money, int max_dot_len, int min_dot_len)
        {
            if (o_money == null || o_money == DBNull.Value)
                return "";
            if (!CPubVars.IsNumeric(o_money))
                return o_money.ToString();
            double money = Convert.ToDouble(o_money);
            string s = money.ToString("F" + max_dot_len.ToString());

            int n_dot = s.IndexOf('.');
            if (n_dot > 0)
            {
                int n_end = s.Length - 1;
                for (int n = s.Length - 1; n >= n_dot; n--)
                {
                    string s_cur = s.Substring(n, 1);
                    int after_dot_len = n - n_dot;
                    if (after_dot_len <= min_dot_len)
                    {
                        n_end = n;
                        break;
                    }
                    else if (s_cur == ".")
                    {
                        n_end = n - 1;
                    }
                    else if (s_cur != "0")
                    {
                        n_end = n;
                        break;
                    }
                }
                s = s.Substring(0, n_end + 1);
            }
            return s;
        }

        public static string FormatMoney(object o_money, int dot_len)
        {
            if (o_money == null || o_money == DBNull.Value)
                return "";
            double money = Convert.ToDouble(o_money);
            string s = money.ToString("F" + dot_len.ToString());

            int n_dot = s.IndexOf('.');
            if (n_dot > 0)
            {
                int n_end = s.Length - 1;
                for (int n = s.Length - 1; n >= n_dot; n--)
                {
                    string s_cur = s.Substring(n, 1);
                    if (s_cur == ".")
                    {
                        n_end = n - 1;
                    }
                    else if (s_cur != "0")
                    {
                        n_end = n;
                        break;
                    }
                }
                s = s.Substring(0, n_end + 1);
            }
            return s;
        }
        public static string FormatMoney(double money, int dot_len)
        {
            string s = money.ToString("F" + dot_len.ToString());
            int n_dot = s.IndexOf('.');
            if (n_dot > 0)
            {
                int n_end = s.Length - 1;
                for (int n = s.Length - 1; n >= n_dot; n--)
                {
                    string s_cur = s.Substring(n, 1);
                    if (s_cur == ".")
                    {
                        n_end = n - 1;
                    }
                    else if (s_cur != "0")
                    {
                        n_end = n;
                        break;
                    }
                }
                s = s.Substring(0, n_end + 1);
            }
            return s;
            //string s_zero = "";
            //s_zero = s_zero.PadRight(dot_len, '0');

            //if (s.Substring(s.Length - (dot_len + 1), dot_len + 1) == "." + s_zero)
            //    s = s.Substring(0, s.Length - (dot_len + 1));
            //return s;
        }

        public static string PadDateWith2359(string date)
        {
            if (date != null)
            {
                if (!date.Contains(":")) date += " 23:59:59";
            }
            return date;
        }
        public static string GetTextFromDr(CMySbDataReader dr, string fld)
        {
            object ov = dr[fld];
            if(ov== DBNull.Value) return "";
            if (ov == null) return "";
            string t = ov.GetType().Name;
            
          
            if (t.Contains("Object"))
            {
                return Newtonsoft.Json.JsonConvert.SerializeObject(ov);
            }
            else if (t == "DateTime")
            {
                string s = CPubVars.GetDateText(ov);
                return s;
            }
            else
            {
                string s = ov.ToString();
                if(s.Contains(" 上午") || s.Contains(" 下午"))
                {

                }
                return ov.ToString();
            }
        }
        public static string GetTextFromOdbcDr(DbDataReader dr, string fld)
        {
            object ov = dr[fld];
            if (ov == DBNull.Value) return "";
            if (ov == null) return "";
            string t = ov.GetType().Name;
            if (fld == "happen_time")
            {
                var a = 0;
            }
            if (t.Contains("Object"))
            {
                return Newtonsoft.Json.JsonConvert.SerializeObject(ov);
            }
            else if (t == "DateTime")
            {
                string s = CPubVars.GetDateText(ov);
                return s;
            }
            else
            {
               // string s = ov.ToString(); 
                return ov.ToString();
            }
        }
        public static bool IsDate(object s)
        {
            if (s == null)
                return false;
            if (s == DBNull.Value)
                return false;
            if (s.ToString().Trim() == "")
                return false;

            DateTime n;
            if (s == null)
                return false;
            //if (s.ToString().Contains(".") || s.ToString().Contains(",")) return false;
            if (s.ToString().Length < 8) return false;
            if (DateTime.TryParse(s.ToString(), out n))
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        public static bool IsInteger(object s)
        {

            if (int.TryParse(s.ToString(), out int result))
            {
                return true; // 转换成功，输入是整数
            }
            else
            {
                return false; // 转换失败，输入不是整数
            }
        }




        public static string GetDateText(object odt)
        {
            if (!CPubVars.IsDate(odt))
            {
                return "";
            }
            DateTime dt = Convert.ToDateTime(odt);
            string s = dt.Year.ToString().PadLeft(4, '0') + "-" + dt.Month.ToString().PadLeft(2, '0') + "-" + dt.Day.ToString().PadLeft(2, '0') + " " + dt.Hour.ToString().PadLeft(2, '0') + ":" + dt.Minute.ToString().PadLeft(2, '0') + ":" + dt.Second.ToString().PadLeft(2, '0');
            s = s.Replace(" 00:00:00", "");
            return s.Trim();
        }
        
        public static string GetDateMonthText(object odt)
        {
            if (!CPubVars.IsDate(odt))
            {
                return "";
            }
            DateTime dt = Convert.ToDateTime(odt);
            string s = dt.Year.ToString().PadLeft(4, '0') + "-" + dt.Month.ToString().PadLeft(2, '0') + "-" + "01";
            s = s.Replace(" 00:00:00", "");
            return s.Trim();
        }
        
        public static string GetDateTextWithZeroTime(object odt)
        {
            if (!CPubVars.IsDate(odt))
            {
                return "";
            }
            DateTime dt = Convert.ToDateTime(odt);
            string s = dt.Year.ToString().PadLeft(4, '0') + "-" + dt.Month.ToString().PadLeft(2, '0') + "-" + dt.Day.ToString().PadLeft(2, '0') + " " + dt.Hour.ToString().PadLeft(2, '0') + ":" + dt.Minute.ToString().PadLeft(2, '0') + ":" + dt.Second.ToString().PadLeft(2, '0');
            return s.Trim();
        }
        public static string GetDateTextNoTime(object odt)
        {
            if (!CPubVars.IsDate(odt))
            {
                return "";
            }
            DateTime dt = Convert.ToDateTime(odt);
            string s = dt.Year.ToString() + "-" + dt.Month.ToString().PadLeft(2, '0') + "-" + dt.Day.ToString().PadLeft(2, '0');
            return s;
        }


        public static string GetQtyUnit(double s_qty, string b_unit_no, double b_unit_factor, string m_unit_no, double m_unit_factor, string s_unit_no, ref double b_sum_quantity, ref double m_sum_quantity, ref double s_sum_quantity)
        {
            double leftQty = s_qty;
            var absLeftQty = Math.Abs(leftQty);
            double flag = 1;
            if (absLeftQty != 0) flag = leftQty / absLeftQty;
            string sQty = "";
            if (b_unit_factor > 0)
            {
                // row.unit_relation = "1*" + b_unit_factor;
                // row.unit_relation1 = $"1{row.b_unit_no}={row.b_unit_factor}{row.s_unit_no}";
                var qty = Math.Floor(absLeftQty / b_unit_factor);
                if (qty < 0.001f) qty = 0;
                qty = Convert.ToDouble(CPubVars.FormatMoney(qty, 3));
                if (qty > 0)
                    sQty += (qty * flag).ToString() + b_unit_no;
                absLeftQty = absLeftQty % b_unit_factor;
                b_sum_quantity += (float)qty * flag;
            }

            if (m_unit_factor > 0)
            {
                var qty = Math.Floor(absLeftQty / m_unit_factor);
                if (qty < 0.001f) qty = 0;
                qty = Convert.ToDouble(CPubVars.FormatMoney(qty, 3));
                if (qty > 0)
                    sQty += (qty * flag).ToString() + m_unit_no;
                absLeftQty = absLeftQty % Convert.ToDouble(m_unit_factor);
                m_sum_quantity += (float)qty * flag;
            }
            if (absLeftQty < 0.001f) absLeftQty = 0;

            absLeftQty = Convert.ToSingle(CPubVars.FormatMoney(absLeftQty, 3));
            if (absLeftQty > 0)
            {
                s_sum_quantity += absLeftQty;
                sQty += (absLeftQty * flag).ToString() + s_unit_no;
            }
            return sQty;
        }

        public static bool g_bSslDifferent = false;
        public static HttpClient GetNewHttpClient()
        {
            // 操作系统版本   
            //OperatingSystem os = Environment.OSVersion;   //消耗时间 0.0056  - 0.0072 波动
            // Console.WriteLine("操作系统版本：{0}" ,os.Version.ToString());
            // Console.WriteLine("操作系统平台: {0}" ,os.Platform.ToString());
            // Console.WriteLine("操作系统服务包: {0}" ,os.ServicePack.ToString());
            // Console.WriteLine("操作系统版本信息: {0}" ,os.VersionString.ToString());
            // Console.WriteLine();
            // 获取版本细节
            // Version ver = os.Version;
            // Console.WriteLine("主版本修订版: {0}", ver.MajorRevision);
            // Console.WriteLine("次版本号：{0}", ver.Minor);
            // Console.WriteLine("次版本修订版：{0}", ver.MinorRevision);
            // Console.WriteLine("内部版本号：{0}", ver.Build);

            // if (ver.Major >= 4) // Centos8 为4  Centos7为3 
            if (!g_bSslDifferent)
            {

                HttpClientHandler clientHandler = new HttpClientHandler();
                clientHandler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => { return true; };
                HttpClient client = new HttpClient(clientHandler);
                //  ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };
                return client;
            }
            else//在一台centos 7.6(www.yingjiang168.com)上，发现必须这样才可以，否则访问https报错，ssl curl组合之类的，在其他的centos7 和8上，都可以试用上面的方式
            {
                ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };
                HttpClient client = new HttpClient();
                return client;
            }

        }
        public static HttpClient g_httpClient = null;
        public static HttpClient GetHttpClient()
        {
             if (g_httpClient != null) return g_httpClient;
             if (!g_bSslDifferent)
             {
                 HttpClientHandler clientHandler = new HttpClientHandler();
                 clientHandler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => { return true; };
                 g_httpClient = new HttpClient(clientHandler);            
                 return g_httpClient;
             }
             else//在一台centos 7.6(www.yingjiang168.com)上，发现必须这样才可以，否则访问https报错，ssl curl组合之类的，在其他的centos7 和8上，都可以试用上面的方式
             {
                 ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };
                 g_httpClient = new HttpClient(); 
                 return g_httpClient;
             }
         }
        public static HttpClient GetHttpClientFromFactory(IHttpClientFactory factory)
        {
           // return GetNewHttpClient();
            return factory.CreateClient("default"); 
        } 
    }
    public class CallResult
    {
        public string result = "OK";
        public string msg = "";
        public string data = "";
        public CallResult(string result, string msg, string data)
        {
            this.result = result; this.msg = msg; this.data = data;
        }
        public CallResult(string result, string msg)
        {
            this.result = result; this.msg = msg;
        }
        public bool IsOK {
            get
            {
                return result == "OK";
            }
         }
        public string ToJsonText()
        {
            return JsonConvert.SerializeObject(this);
        }
    }
    public class CallResult<T>
    {
        public string result = "OK";
        public string msg = "";
        public T data = default;
        public CallResult(string result, string msg, T data)
        {
            this.result = result; this.msg = msg; this.data = data;
        }
        public CallResult(string result, string msg)
        {
            this.result = result; this.msg = msg;
        }
        public bool IsOK
        {
            get { return result == "OK"; }
        }
        public CallResult ToCommonResult(bool needData = false)
        {
            var commonResult = new CallResult(this.result, this.msg);
            if (needData) commonResult.data = JsonConvert.SerializeObject(data);
            return commonResult;
        }
    }

    public class MiniSQLParser
    {
        /*
        public enum PieceType
        {
            LeftJoin=0,
            Where
        }
        public class SQLPiece
        {
            public string PieceSQL;
            public string LeftJoinTable;
        }*/
        public string SqlCount;
        public string SqlData;
        public static SqlResult GetSQLResult(string sqlOrig,bool getCount,bool getRows, string orderBy, int pageSize, int startRow,string otherSumFlds="")
        {
            string sqlCount = "";
            if (getCount)
            {
                string sql = sqlOrig;
                int n = 0, nPre = -1;
                for (int i = 0; i < 100; i++)
                {
                    n = sql.IndexOf("FROM", nPre + 1, StringComparison.OrdinalIgnoreCase);         
                    if (n == -1) throw new Exception("no from in sql");
                    string ttt = sql.Substring(0, n);
                    n += 4;
                    string sFrom = sql.Substring(n, sql.Length - n);
                    char cc=(char)0;
                    if (sFrom.Length>0) cc = sFrom[0];
                    
                    if (sFrom.StartsWith(" ") || cc==13||cc==10)
                    {
                        sql = sFrom.Trim();
                        break;
                    }
                    
                    nPre = n + 1;
                }
 
                int nJoinEnd = -1;

                for (int i = 0; i < 100; i++)
                {
                    int nJoin = sql.IndexOf("left join", nJoinEnd + 1, StringComparison.OrdinalIgnoreCase);
                    if (nJoin == -1) break;
                    int nJoinEnd_new = -1;
                    int joinTbStart = 0;
                    for (int k = nJoin + 10; k < sql.Length; k++)
                    {
                        string s = sql.Substring(k,1);

                        if (s == "(")
                        {
                            string tt = sql.Substring(0, k);
                            int khCount = 1;
                            for (int l = k+1; l < sql.Length; l++)
                            {
                                string o = sql.Substring(l,1);
                                if (o == "(")
                                {
                                    khCount++;
                                }
                                else if (o == ")")
                                {
                                    khCount--;
                                }
                                if (khCount == 0)
                                {
                                    joinTbStart = l + 1;
                                    break;
                                }
                            }
                            if (khCount > 0)
                            {
                                throw new Exception("lack )");
                            }
                            
                        }
                        else if (char.IsLetter(s[0]))
                        {
                        //    string tt = sql.Substring(0, nJoinEnd);
                            joinTbStart = k;
                        }
                        if (joinTbStart != 0)
                        {
                            int n2 = sql.IndexOf(" on ", joinTbStart, StringComparison.OrdinalIgnoreCase);
                            if (n2 != -1)
                            {
                                string tt1 = sql.Substring(0, n2);
                                int  nEnd = sql.IndexOf("left join", joinTbStart, StringComparison.OrdinalIgnoreCase);
                                if (nEnd == -1)
                                {
                                    nEnd = sql.IndexOf("where", joinTbStart, StringComparison.OrdinalIgnoreCase);
                                }
                               
                                if (nEnd != -1)
                                {
                                    nJoinEnd = nJoinEnd_new = nEnd-1;
                                }


                                string tb = sql.Substring(joinTbStart, n2 - joinTbStart).Trim();
                                string[] arr = tb.Split(" ");
                                tb = arr[arr.Length - 1];
                               // if (wherePart.IndexOf($" {tb}.") == -1 && wherePart.IndexOf($"({tb}.") == -1)
                                if (sql.IndexOf($" {tb}.", nJoinEnd+1,StringComparison.OrdinalIgnoreCase) == -1 
                                 && sql.IndexOf($"({tb}.", nJoinEnd+1,StringComparison.OrdinalIgnoreCase) == -1)
                                {
                                    if (nEnd != -1)
                                    {
                                        sql = sql.Substring(0, nJoin) + sql.Substring(nEnd, sql.Length - nEnd);
                                        nJoinEnd = nJoin-1;
                                    }
                                }
                                //string tt2 = sql.Substring(0, nJoinEnd);
                            }
                            break;
                        }
                    }

                    if (nJoinEnd_new == -1)
                    {
                        throw new Exception("left join should followed by where or another left join");
                    }
                }
                if (otherSumFlds != "") otherSumFlds = "," + otherSumFlds;
                sqlCount = $"select count(*) as total {otherSumFlds} from " + sql;
            }

            string sqlRows = "";
            if (getRows)
            {
                sqlRows = $"{sqlOrig} {orderBy} limit {pageSize} offset {startRow};";
            }
            SqlResult res = new SqlResult() { SqlCount = sqlCount, SqlRows = sqlRows };
            return res;
        }
        public static async Task<JsonResult> RunSqlResult(CMySbCommand cmd, SqlResult sqlResult, bool getTotal, string otherSumFlds = "", Action<dynamic> cbDealData = null)
        {
            if (sqlResult.ErrMsg != "")
            {
                return new JsonResult(new { result = "Error", msg = sqlResult.ErrMsg });
            }
            SQLQueue QQ = new SQLQueue(cmd);
            if (getTotal)
            {
                QQ.Enqueue("count", sqlResult.SqlCount);
            }
            QQ.Enqueue("data", sqlResult.SqlRows);


            var dr = await QQ.ExecuteReaderAsync();
        //    List<ExpandoObject> data = null;
            string total = "";
            JObject res = new JObject();
            res["result"] = "OK";
            res["msg"] = "";
            res["data"] =null;
            res["total"]=total;
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();

                if (sqlName == "data")
                {
                    res["data"] = JArray.FromObject(CDbDealer.GetRecordsFromDr(dr, false));

                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    res["total"] = CPubVars.GetTextFromDr(dr, "total"); 
                    if (otherSumFlds != "")
                    {
                        string[] arr = otherSumFlds.Split(",");
                        JObject jobj = new JObject();
                        foreach(string s in arr)
                        {
                            string s1 = s.Trim();
                            res[s1] = CPubVars.GetTextFromDr(dr, s1);
                        }                        
                    }
                }

            }
            QQ.Clear();
            if (cbDealData != null)
            {
                cbDealData(res);
            }
            return new JsonResult(res);
        }

        /*
        public static string GetCountSQL1(string sql)
        {
            int n = 0, nPre = 0;
            string dsql = "";
            for (int i = 0; i < 100; i++)
            {
                n = sql.IndexOf("from", 0, 1, StringComparison.OrdinalIgnoreCase);
                if (n == -1) return "";
                n += 4;
                dsql = sql.Substring(n, sql.Length - n);
                if (dsql.StartsWith(" ") || dsql.StartsWith("\n") || dsql.StartsWith("\r"))
                {
                    break;
                }
                nPre = n + 1;
            }
            for (int i = 0; i < 100; i++)
            {
                n = dsql.IndexOf("[", 0, 1, StringComparison.OrdinalIgnoreCase);
                if (n == -1) break;
                int n1 = dsql.IndexOf("]", n, 1, StringComparison.OrdinalIgnoreCase);
                if (n1 == -1) throw new Exception("]没有对应的[");
                dsql = dsql.Substring(0, n) + dsql.Substring(n1 + 1, dsql.Length - n1 - 1);
            }
            return dsql;
        }
        */
        public static string GetOrigSQL(string sql)
        {
         
            return sql.Replace("[", "").Replace("]", "");
           
        }
    
    }
    public class SqlResult
    {
        public string SqlRows="";
        public string SqlCount="";
        public string ErrMsg="";
    }
}
