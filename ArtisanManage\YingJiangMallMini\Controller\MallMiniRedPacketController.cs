﻿using System;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.YingJiangBackstage.Pojo;
using ArtisanManage.YingJiangMallMini.Model;
using ArtisanManage.YingJiangMallMini.Services;
using ArtisanManage.YingJiangMallMini.Util;
using Microsoft.AspNetCore.Mvc;

namespace ArtisanManage.YingJiangMallMini.Controller;
[Route("MallMini/[controller]/[action]")]
public class MallMiniRedPacketController : BaseController
{
    public string ControllerName => GetType().Name.Replace("Controller", "");
    private readonly MallMiniRedPacketService _mallMiniRedPacketService;

    public MallMiniRedPacketController(CMySbCommand cmd)
    {
        this.cmd = cmd;
        _mallMiniRedPacketService = new MallMiniRedPacketService(cmd);
    }
    [HttpPost]
    public async Task<JsonResult> GetMallMiniRedPacketBaseInfo([FromBody] MallMinBaseParameter parameter)
    {
        try
        {
            if (parameter.supcustId <= 0)
            {
                // 兼容数据
                return Json(new ResultUtil<dynamic>().CommonResult(0, "success", new {}));
            }
            dynamic result = await _mallMiniRedPacketService.GetMallMiniRedPacketBaseInfoService(parameter);
            return Json(new ResultUtil<dynamic>().CommonResult(0, "success", result));
        }
        catch (MallMiniException e)
        {
            Console.WriteLine(e);
            return Json(new ResultUtil<dynamic>().ExceptionResult(ControllerName, parameter, e.Message, e.Message, null));
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            return Json(new ResultUtil<dynamic>().ExceptionResult(ControllerName, parameter, e.Message, "获取红包配置信息失败", null));
        }
    }
    [HttpPost]
    public async Task<JsonResult> GetMallMiniRedPacket_Send([FromBody] MallMinSendParameter parameter)
    {
        try
        {
            if (parameter.supcustId <= 0)
            {
                // 兼容数据
                return Json(new ResultUtil<dynamic>().CommonResult(0, "success", new { }));
            }
            dynamic result = await _mallMiniRedPacketService.GetMallMiniRedPacketSendService(parameter);
            return Json(new ResultUtil<dynamic>().CommonResult(0, "success", result));
        }
        catch (MallMiniException e)
        {
            Console.WriteLine(e);
            return Json(new ResultUtil<dynamic>().ExceptionResult(ControllerName, parameter, e.Message, e.Message, null));
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            return Json(new ResultUtil<dynamic>().ExceptionResult(ControllerName, parameter, e.Message, "获取红包（主动发送）信息失败", null));
        }
    }
    [HttpPost]
    public async Task<JsonResult> GetMallMiniRedPacket_PurchaseReward([FromBody] MallMinPurchaseRewardParameter parameter)
    {
        try
        {
            if (parameter.supcustId <= 0)
            {
                // 兼容数据
                return Json(new ResultUtil<dynamic>().CommonResult(0, "success", new { }));
            }
            dynamic result = await _mallMiniRedPacketService.GetMallMiniRedPacketPurchaseRewardService(parameter);
            return Json(new ResultUtil<dynamic>().CommonResult(0, "success", result));
        }
        catch (MallMiniException e)
        {
            Console.WriteLine(e);
            return Json(new ResultUtil<dynamic>().ExceptionResult(ControllerName, parameter, e.Message, e.Message, null));
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            return Json(new ResultUtil<dynamic>().ExceptionResult(ControllerName, parameter, e.Message, "获取红包（下单返利）信息失败", null));
        }
    }
    [HttpPost]
    public async Task<JsonResult> MallMini_LogRedPacketChange([FromBody] MallMinRedPacketLogandChangeParameter parameter)
    {
        try
        {
            if (parameter.supcustId <= 0)
            {
                // 兼容数据
                return Json(new ResultUtil<dynamic>().CommonResult(0, "success", new { }));
            }
            dynamic result = await _mallMiniRedPacketService.LogMallMiniRedPacketChangeService(parameter);
            return Json(new ResultUtil<dynamic>().CommonResult(0, "success", result));
        }
        catch (MallMiniException e)
        {
            Console.WriteLine(e);
            return Json(new ResultUtil<dynamic>().ExceptionResult(ControllerName, parameter, e.Message, e.Message, null));
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            return Json(new ResultUtil<dynamic>().ExceptionResult(ControllerName, parameter, e.Message, "记录红包流水、修改红包余额失败", null));
        }
    }
}