-- 升级脚本：为借货功能添加按借货单维度的金额管理
-- 执行日期：2024-12-19
-- 说明：支持独立借货单的按金额借还货，支持借A单还B单场景

-- 1. 创建借货单余额表（按借货单维度管理）
CREATE TABLE IF NOT EXISTS borrow_sheet_balance (
    company_id integer NOT NULL,
    borrow_sheet_id integer NOT NULL,        -- 借货单ID
    supcust_id integer NOT NULL,             -- 客户ID
    borrow_mode varchar(10) NOT NULL,        -- 借货模式：QTY=按数量，AMT=按金额
    total_amount numeric(15,2) DEFAULT 0,    -- 借货总金额
    returned_amount numeric(15,2) DEFAULT 0, -- 已还金额
    balance_amount numeric(15,2) DEFAULT 0,  -- 剩余金额
    borrow_time timestamp NOT NULL,         -- 借货时间
    last_return_time timestamp,             -- 最后还货时间
    status varchar(20) DEFAULT 'ACTIVE',    -- 状态：ACTIVE=有效，CLOSED=已结清
    CONSTRAINT pk_borrow_sheet_balance PRIMARY KEY (company_id, borrow_sheet_id)
);

-- 2. 创建还货记录表（记录每次还货对应的借货单）
CREATE TABLE IF NOT EXISTS return_borrow_mapping (
    company_id integer NOT NULL,
    return_sheet_id integer NOT NULL,       -- 还货单ID
    return_detail_id integer NOT NULL,      -- 还货明细ID（独立还货单时为0）
    borrow_sheet_id integer NOT NULL,       -- 对应的借货单ID
    return_amount numeric(15,2) DEFAULT 0,  -- 本次还货金额
    return_time timestamp DEFAULT now(),
    CONSTRAINT pk_return_borrow_mapping PRIMARY KEY (company_id, return_sheet_id, return_detail_id, borrow_sheet_id)
);

-- 3. 添加表注释
COMMENT ON TABLE borrow_sheet_balance IS '借货单余额表（按借货单维度管理，支持独立借货单）';
COMMENT ON COLUMN borrow_sheet_balance.borrow_sheet_id IS '借货单ID';
COMMENT ON COLUMN borrow_sheet_balance.borrow_mode IS '借货模式：QTY=按数量，AMT=按金额';
COMMENT ON COLUMN borrow_sheet_balance.balance_amount IS '剩余可还金额';

COMMENT ON TABLE return_borrow_mapping IS '还货与借货单对应关系表';
COMMENT ON COLUMN return_borrow_mapping.return_sheet_id IS '还货单ID';
COMMENT ON COLUMN return_borrow_mapping.borrow_sheet_id IS '对应的借货单ID';
COMMENT ON COLUMN return_borrow_mapping.return_detail_id IS '还货明细ID（独立还货单时为0）';

-- 4. 创建索引
CREATE INDEX IF NOT EXISTS idx_borrow_sheet_balance_supcust 
ON borrow_sheet_balance(company_id, supcust_id, status);

CREATE INDEX IF NOT EXISTS idx_borrow_sheet_balance_time 
ON borrow_sheet_balance(company_id, borrow_time);

CREATE INDEX IF NOT EXISTS idx_return_borrow_mapping_return 
ON return_borrow_mapping(company_id, return_sheet_id);

CREATE INDEX IF NOT EXISTS idx_return_borrow_mapping_borrow 
ON return_borrow_mapping(company_id, borrow_sheet_id);

-- 5. 为独立借货单主表添加借货模式字段
ALTER TABLE borrow_item_main
ADD COLUMN IF NOT EXISTS borrow_mode varchar(10) DEFAULT 'QTY';

COMMENT ON COLUMN borrow_item_main.borrow_mode IS '借货模式：QTY=按数量，AMT=按金额';

-- 注意：borrow_item_detail表不需要borrow_mode字段
-- 借货模式是单据级别的设置，明细表通过关联主表获取

-- 注意：销售单中的借还货逻辑保持不变，不支持借A还B模式
-- 只有独立借货单支持按金额借还货和借A单还B单功能

-- 7. 验证升级结果
DO $$
BEGIN
    -- 检查表是否创建成功
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'borrow_sheet_balance') THEN
        RAISE EXCEPTION '借货单余额表创建失败';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'return_borrow_mapping') THEN
        RAISE EXCEPTION '还货映射表创建失败';
    END IF;
    
    -- 检查字段是否添加成功
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'borrow_item_main'
        AND column_name = 'borrow_mode'
    ) THEN
        RAISE EXCEPTION '字段borrow_item_main.borrow_mode添加失败';
    END IF;


    
    RAISE NOTICE '借货单按金额功能数据库升级完成！支持独立借货单的借A单还B单场景。';
END $$;

-- 9. 使用说明
/*
功能范围：

✅ 支持借A单还B单的场景：
   - 独立借货单 ↔ 独立还货单
   - 使用borrow_item_main和borrow_item_detail表
   - 支持按数量(QTY)和按金额(AMT)两种模式

❌ 不支持借A单还B单的场景：
   - 销售单中的借还货
   - 使用sheet_sale_main和sheet_sale_detail表
   - 保持原有逻辑不变，继续使用borrowed_cust_items表

使用方法：

1. 独立借货单（支持按金额借还货）：
   - 使用专门的borrow_item_main和borrow_item_detail表
   - 借货金额记录在borrow_item_main.total_amount
   - 借货模式记录在borrow_item_main.borrow_mode
   - 按金额模式时创建borrow_sheet_balance记录

2. 销售单中的借货（保持原有逻辑）：
   - 使用sheet_sale_main和sheet_sale_detail表
   - 借货明细在sheet_sale_detail中，trade_type='J'
   - 继续使用borrowed_cust_items表管理按商品的借货数量
   - 不支持借A还B功能

3. 独立还货单（支持选择借货单）：
   - 使用borrow_item_main表
   - 按金额模式时可以选择要还给哪个借货单
   - 通过return_borrow_mapping表记录对应关系

4. 借货余额查询：
   - 通过borrow_sheet_balance表查看独立借货单的余额
   - 只查询borrow_item_main表中的借货单
   - 支持按借货单、客户、时间等维度查询
*/
