using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using HuaWeiObsController;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json.Linq;
using static ArtisanManage.Models.AttenceLeave;
using static ArtisanManage.Models.DataItem;

namespace ArtisanManage.Pages.BaseInfo
{
    public class FeeOutSheetViewModel : PageQueryModel
    {
        public bool ForSelect = false;
        public FeeOutSheetViewModel(CMySbCommand cmd) : base(Services.MenuId.sheetFeeOut)
        {
            this.cmd = cmd;
            CanQueryByApproveTime = true;
            this.PageTitle = "费用支出";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="fo.happen_time", CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期", FldArea="divHead",CtrlType="jqxDateTimeInput", SqlFld="fo.happen_time",   CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"depart_path",new DataItem(){Title="部门",Hidden=true, FldArea="divHead",LabelFld="depart_path_label", CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=true,DropDownWidth="150", CompareOperator="like",LikeWrapper="/",
                    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"
                }},
                {"sheet_type",new DataItem(){FldArea="divHead",Title="单据类型",LabelFld="sheet_type_name",ButtonUsage="list",Source = "[{v:'ZC',l:'费用支出'},{v:'SR',l:'其他收入'},{v:'',l:'所有'}]",CompareOperator="=",Hidden=true}},
                {"sup_name",new DataItem(){FldArea="divHead",Title="客户",SqlFld="s.supcust_id", CompareOperator="=" ,ButtonUsage="list",
                    CONDI_DATA_ITEM="sheet_type",SqlForOptions="select supcust_id as v,sup_name as l,py_str as z from info_supcust where company_id= ~COMPANY_ID and (supcust_flag ilike '%C%' or supcust_flag = 'W') and (status = '1' or status is null) "        }},
                {"seller_id",new DataItem(){FldArea="divHead",Title="业务员",LabelFld="seller_name",ButtonUsage="list",SqlFld="fo.getter_id" ,SqlForOptions=CommonTool.selectSellers,CompareOperator="="}},//SqlForOptions="select oper_id as v,oper_name as l,py_str as z from info_operator"
                {"fee_sub_id",new DataItem(){FldArea="divHead",Title="费用类别",LabelFld="fee_sub_name",ButtonUsage="list",ForQuery=false, CONDI_DATA_ITEM="sheet_type",
                    SqlForOptions="select sub_id as v,sub_name as l,py_str as z from cw_subject where company_id=~COMPANY_ID and sub_type = '~CONDI_DATA_ITEM' and sub_code>66",CompareOperator="=",
                    DealQueryItem=fee_sub=>{this.SQLVariable2 = $" and fee_sub_id = {fee_sub}";return""; },
                }},
                // 新增支付账户检索条件
                {"payway_id",new DataItem(){FldArea="divHead",Title="支付账户",LabelFld="payway_name",ButtonUsage="list",CompareOperator="=",
                    SqlForOptions="select sub_id as v,sub_name as l,py_str as z from cw_subject where company_id=~COMPANY_ID and sub_type='QT' and coalesce(status,'1')='1' order by sub_code",
                    DealQueryItem=payway_id=>{
                        if(!string.IsNullOrEmpty(payway_id)) {
                            this.SQLVariable1 += $" and (fo.payway1_id = '{payway_id}' or fo.payway2_id = '{payway_id}')";
                        }
                        return "";
                    }
                }},

                {"status",new DataItem(){FldArea="divHead",Title="单据状态",LabelFld = "status_name",ButtonUsage = "list",CompareOperator="=",Value="normal",Label="正常单据",
                        Source = @"[{v:'normal',l:'正常单据',condition:""red_flag is null""},
                                   {v:'unapproved',l:'未审核单据',condition:""approve_time is null""},
                                   {v:'approved',l:'已审核单据',condition:""approve_time is not null and red_flag is null""},
                                   {v:'red',l:'红冲单',condition:""red_flag in ('1','2') ""},
                                   {v:'all',l:'所有',condition:""true""}]"
                }},
                {"sheet_no",new DataItem(){FldArea="divHead",Title="单号", CompareOperator="like"}},
                {"byHappenTime",new DataItem(){FldArea="divHead",Title="按交易时间查询",CtrlType="jqxCheckBox",ForQuery=false,Value="true"}},
                {"group_id",new DataItem(){FldArea="divHead",Title="渠道",Checkboxes=true,LabelFld="group_name",ButtonUsage="list",CompareOperator="=",SqlFld="s.sup_group",
                    SqlForOptions="select group_id as v,group_name as l from info_supcust_group"
                }},
                 {"make_brief",new DataItem(){FldArea="divHead",Title="备注", CompareOperator="like"}},
                 {"feeApportionStatus",new DataItem(){FldArea="divHead",Title="分摊状态", Hidden=true, HideOnLoad=true, LabelFld = "feeApportionStatus_label",ButtonUsage = "list", CompareOperator="=",Value="all", Label="所有",
                        Source = @"[{v:'all',l:'所有',condition:""true""},
                                   {v:'used',l:'已用于分摊',condition:""fo.sheet_attribute->>'buy_sheet_id' is not null""},
                                   {v:'notuse',l:'未用于分摊',condition:""fo.sheet_attribute->>'buy_sheet_id' is null""}]"
                }},
                {"priceRebateStatus",new DataItem(){FldArea="divHead",Title="补差状态", Hidden=true, HideOnLoad=true, LabelFld = "priceRebateStatus_label",ButtonUsage = "list", CompareOperator="=",Value="all", Label="所有",
                        Source = @"[{v:'all',l:'所有',condition:""true""},
                                   {v:'used',l:'已用于补差',condition:""fo.sheet_attribute->>'related_sheet_id' is not null""},
                                   {v:'notuse',l:'未用于补差',condition:""fo.sheet_attribute->>'related_sheet_id' is null""}]"
                }},
            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true,
                     IdColumn = "sheet_id",
                     HasCheck=false,
                     Sortable=true,
                  //   SubRowsNameInRow="sub_rows",
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"sheet_id",   new DataItem(){Title="sheet_id",SqlFld="fo.sheet_id", Hidden=true, Linkable=true, Width="80",HideOnLoad = true}},
                       {"sheet_no",   new DataItem(){Title="单据编号",  Linkable=true, Width="150"}},
                       {"sheet_type", new DataItem(){Title="单据类型",  Width="80",SqlFld="(case WHEN fo.sheet_type='ZC' THEN '费用支出' when fo.sheet_type='SR' then '其他收入' END)"}},
                       {"sheet_status", new DataItem(){Title="状态",  Width="80",SqlFld="(case when fo.red_flag='2' then '红字单' when fo.red_flag='1' then '已红冲' when fo.approve_time is null then '未审' else '已审' END)"}},
                       {"sup_name",   new DataItem(){Title="客户",   Width="100"}},
                       {"happen_time",   new DataItem(){Title="交易时间",SqlFld="fo.happen_time",   Width="150", Sortable=true}},
                       {"approve_time",   new DataItem(){Title="审核时间",   Width="160", Sortable=true}},
                       {"group_name", new DataItem(){Title="渠道", Width="100", SqlFld="group_name", Sortable=true}},
                       //{"branch_name", new DataItem(){Title="仓库",  Width="80"}},
                       {"seller_name", new DataItem(){Title="业务员",  Width="80"}},
                       //{"now_pay_amount", new DataItem(){Title="支付", Width="80"}},
                       {"fee_sub_name_all",  new DataItem(){Title="类别", Width="120",SqlFld="string_agg((case when fee_sub_name is not null then concat(fee_sub_name,':',fee_sub_amount) end),',')",SubRowsColumn="fee_sub_name" }},
                       //{"fee_sub_name",  new DataItem(){Title="费用类别", Width="120",SqlFld="''",  SubRowsColumn="fee_sub_name" }},
                       //{"fee_sub_amount",  new DataItem(){Title="费用金额", Width="120", SqlFld="''",   SubRowsColumn="fee_sub_amount" }},
                       {"total_amount", new DataItem(){Title="金额",  Width="80",ShowSum=true, SubRowsColumn="fee_sub_amount"}},
                       {"now_arrears", new DataItem(){Title="尚欠金额", Width="90",CellsAlign="right",SqlFld="(total_amount - paid_amount - disc_amount)",Hidden=true,ShowSum=true}},
                       {"remark",  new DataItem(){Title="项目备注", Width="120",SqlFld="string_agg ( ( CASE WHEN remark IS NOT NULL THEN remark END ), ',' )", SubRowsColumn="remark" }},
                       {"check_appendix_photos", new DataItem(){Title="附件", Width="100",SqlFld="case when sheet_attribute->>'appendixPhotos' is not null and sheet_attribute->>'appendixPhotos' <> '[]'  then '查看' end",Linkable=true,
                          /* FuncDealMe = jarray =>
                         {
                               if (jarray != "")
                               {
                                   // var urls = Newtonsoft.Json.JsonConvert.DeserializeObject<List<string>>(jarray);
                                    var urls = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, object>>(jarray);
                                    var div = new StringBuilder();
                                    //var i = 1;
                                    //urls.ForEach(url=>{
                                    //div.Append($"<a target=\"_blank\" href='{HuaWeiObs.BucketLinkHref}/uploads{url}'>图片{i++} </a>" );
                                    //});

                                // 添加其他图片链接
                                if (urls.ContainsKey("photos"))
                                {
                                    var otherUrlsArray = (JArray)urls["photos"];
                                    var otherUrls = otherUrlsArray.ToObject<List<object>>();
                                     int i = 0;
                                    foreach (var url in otherUrls)
                                    {
                                        div.Append($"<a target=\"_blank\" href='{HuaWeiObs.BucketLinkHref}/{url}'>图片{i++}</a><br>");
                                    }
                                }

                                return div.ToString();
                                    
                               }
                               return "";
                                }*/
                            }
                        },
                       { "appendix_photos",new DataItem(){Title="照片对象",SqlFld="sheet_attribute->>'appendixPhotos'", Hidden=true,HideOnLoad=true} },
                       {"make_brief", new DataItem(){Title="备注", Width="100"}},
                       {"payways",  new DataItem(){Title="支付方式", Width="60",
                           FuncGetSubColumns = async (col) =>
                           {
                                ColumnsResult result=new ColumnsResult();
                                Dictionary<string,DataItem> subColumns=new Dictionary<string,DataItem>();
                               string sub_type=this.DataItems["sheet_type"].Value;
                               if(sub_type=="SR") sub_type="QTSR";
                                List<System.Dynamic.ExpandoObject> payways =  await CDbDealer.GetRecordsFromSQLAsync($"select sub_id,sub_name from cw_subject where company_id={company_id} and sub_type ='QT' and coalesce(status,'1')='1'",cmd) ;
                                string flds="";
                                foreach(dynamic pw in payways)
                                {
                                   var subcol=new DataItem();
                                   subcol.Width=col.Width;
                                   subcol.Title=pw.sub_name;
                                   string colKey="pw_"+ (string)pw.sub_id;
                                   subColumns.Add(colKey, subcol);
                                   string fld=$" case when payway1_id='{pw.sub_id}' then payway1_amount::text when payway2_id='{pw.sub_id}' then payway2_amount::text else '' end as {colKey}";
                                   if(flds!="") flds+=",";
                                   flds+=fld;
                                }
                                result.FldsSQL=flds;
                                result.Columns=subColumns;
                                return result;
                           }
                           
                       }} ,
                        {"sub_rows",   new DataItem(){SqlFld="'['||string_agg (FORMAT('{\"fee_sub_name\":\"%s\",\"fee_sub_amount\":\"%s\",\"remark\":\"%s\"}', fee_sub_name, fee_sub_amount,remark),',')||']' ", Hidden=true, Linkable=true, Width="80",HideOnLoad = true}},


                     },
                     QueryFromSQL=@"
from (select * from sheet_fee_out_detail where company_id = ~COMPANY_ID  ~SQL_VARIABLE2) od
LEFT JOIN sheet_fee_out_main fo on od.sheet_id = fo.sheet_id and fo.company_id= ~COMPANY_ID
LEFT JOIN info_supcust s on fo.supcust_id = s.supcust_id and s.company_id = ~COMPANY_ID
LEFT JOIN (select group_id, group_name, company_id from info_supcust_group) isg on s.sup_group = isg.group_id and isg.company_id = ~COMPANY_ID
LEFT JOIN (select sub_id,sub_name as payway1_name from cw_subject where company_id= ~COMPANY_ID) pw1 on fo.payway1_id = pw1.sub_id
LEFT JOIN (select sub_id,sub_name as payway2_name from cw_subject where company_id= ~COMPANY_ID) pw2 on fo.payway2_id = pw2.sub_id
LEFT JOIN (select sub_id,sub_name as fee_sub_name from cw_subject where company_id= ~COMPANY_ID) fs on od.fee_sub_id = fs.sub_id 
LEFT JOIN (select oper_id,depart_path from info_operator where company_id= ~COMPANY_ID) maker on fo.maker_id = maker.oper_id
LEFT JOIN (select oper_id,oper_name as seller_name from info_operator where company_id= ~COMPANY_ID) seller on fo.getter_id = seller.oper_id where fo.company_id= ~COMPANY_ID  ~SQL_VARIABLE1",
                     
                     QueryOrderSQL=" order by happen_time desc",
                     QueryGroupBySQL = " GROUP BY fo.sheet_id,sheet_no,fo.sheet_attribute,fo.sheet_type,fo.red_flag,fo.approve_time,fo.happen_time,sup_name,group_name,seller_name,total_amount,paid_amount,disc_amount,payway1_id,payway1_amount,payway2_id,payway2_amount,make_brief,fo.make_time"
                  }
                } 
            }; 
        }
        public override Task OnQueryConditionGot(CMySbCommand cmd)
        {
            return base.OnQueryConditionGot(cmd);

        }
        //public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        //{
        //    if (DataItems["sheet_type"].Value == "SR")
        //    {
        //        DataItems["sup_name"].Title = "供应商";
        //    }
        //}
        public async Task OnGet(string forOutOrIn,string forSelect)
        {         
            //用于采购费用分摊单gridB选择现有费用单
            if (forOutOrIn.ToLower()== "true")
            {
                DataItems["sheet_type"].Value = "ZC";
                DataItems["sheet_type"].Label = "费用支出";
                if (forSelect == "1")
                {
                    ForSelect = true;
                    Grids["gridItems"].HasCheck = true;
                    DataItems["sheet_type"].Disabled = true;
                    DataItems["status"].Value = "approved";
                    DataItems["status"].Label = "已审核单据";
                    DataItems["status"].Disabled = true;
                    DataItems["feeApportionStatus"].Value = "notuse";
                    DataItems["feeApportionStatus"].Label = "未用于分摊";
                    DataItems["feeApportionStatus"].Disabled = true;
                    DataItems["priceRebateStatus"].Value = "notuse";
                    DataItems["priceRebateStatus"].Label = "未用于补差";
                    DataItems["priceRebateStatus"].Disabled = true;
                }
            }
            else
            {
                DataItems["sup_name"].Title = "供应商";
                Grids["gridItems"].Columns["sup_name"].Title = "供应商";
                DataItems["sup_name"].SqlForOptions = "select supcust_id as v,sup_name as l,py_str as z from info_supcust where company_id= ~COMPANY_ID and (supcust_flag ilike '%S%' or supcust_flag = 'W') and (status = '1' or status is null) ";
            }
            await InitGet(cmd);
            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }
    }



    [Route("api/[controller]/[action]")]
    public class FeeOutSheetViewController : QueryController
    { 
        public FeeOutSheetViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, bool forOutOrIn, string availValues, string condiDataItem)
        {
            FeeOutSheetViewModel model = new FeeOutSheetViewModel(cmd);
            //if (!forOutOrIn)
            //{
            //    model.DataItems["fee_sub_id"].SqlForOptions = "select sub_id,sub_name,py_str,sub_code from cw_subject where company_id =~COMPANY_ID and sub_type = 'QTSR' and sub_code>66";
            //}
            if (condiDataItem == "SR") 
            { 
                condiDataItem = "QTSR";
                model.DataItems["sup_name"].SqlForOptions = "select supcust_id as v,sup_name as l,py_str as z from info_supcust where company_id= ~COMPANY_ID and (supcust_flag ilike '%S%' or supcust_flag = 'W') and (status = '1' or status is null) ";
            }
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues,condiDataItem);
            return data;
        }

        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            FeeOutSheetViewModel model = new FeeOutSheetViewModel(cmd);           
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            FeeOutSheetViewModel model = new FeeOutSheetViewModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }
    }
}
