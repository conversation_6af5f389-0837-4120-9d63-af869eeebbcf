# 销售订单分单流程说明文档

## 流程概览

销售订单分单流程是一个完整的业务处理链路，从用户点击"分单审核"按钮开始，到最终生成多个已审核的子订单结束。整个流程包含数据验证、库存分析、权限检查、分单生成、保存审核等多个环节。

## 主流程图

```
用户点击分单审核
        ↓
    数据解析验证
        ↓
    订单初始化
        ↓
    查询库存信息
        ↓
    权限检查
        ↓
    选择分单策略
        ↓
    生成分单方案
        ↓
    创建分单数据
        ↓
    判断分单数量
    ↙        ↘
单仓库场景    多仓库场景
    ↓            ↓
直接保存审核   批量保存分单
    ↓            ↓
返回结果      逐个审核分单
              ↓
            返回分单结果
```

## 详细流程步骤

### 第一阶段：数据准备

#### 1.1 前端数据提交
- **触发条件**：用户在没有选择仓库的情况下点击"审核"按钮
- **数据格式**：JSON格式的销售订单对象
- **请求方式**：POST请求到`/api/SaleOrderSheet/DivideSaveAndApprove`

#### 1.2 后端数据解析
```csharp
// 解析前端传入的订单数据
SheetSaleOrder sheet = CPubVars.FromJsonObj<SheetSaleOrder>(dSheet);
```
- **异常处理**：解析失败时记录错误日志并返回友好提示
- **数据验证**：检查必要字段的完整性

#### 1.3 订单初始化
```csharp
sheet.Init();
```
- **设置默认值**：为空字段设置默认值
- **计算字段**：计算总金额、税额等衍生字段
- **状态设置**：设置订单的初始状态

### 第二阶段：库存分析

#### 2.1 商品需求统计
```csharp
Dictionary<string, decimal> itemQuantity = new Dictionary<string, decimal>();
foreach (var row in originalSheet.SheetRows)
{
    string itemId = row.item_id;
    decimal requiredQuantity = row.quantity * row.unit_factor;
    // 累加同一商品的需求量
}
```

#### 2.2 库存信息查询
- **查询范围**：所有订单中涉及的商品
- **查询维度**：商品ID、仓库ID、可用库存、仓库权限
- **数据分类**：
  - 正可用库存（>0）
  - 负库存（≤0）
  - 仓库权限设置

#### 2.3 权限信息获取
```csharp
// 获取业务员权限
dynamic operRights = JsonConvert.DeserializeObject(JsonOperRightsOrig);
bool isAllowNegativeStockOrder = operRights?.delicacy?.allowNegativeStock?.value == "true";
```

### 第三阶段：分单策略选择

#### 3.1 权限判断
```csharp
if (!isAllowNegativeStockOrder)
{
    // 使用正库存分单算法
    dividePlan = DividePlanByPositive(...);
}
else
{
    // 使用负库存分单算法
    dividePlan = DividePlanByNegative(...);
}
```

#### 3.2 分单方案生成
- **输入参数**：库存信息、需求信息、权限信息
- **输出结果**：商品到仓库的分配方案
- **错误处理**：库存不足或权限不足时返回错误

### 第四阶段：分单创建

#### 4.1 数据分组
```csharp
// 按仓库分组商品行
Dictionary<string, List<SheetRowSaleOrder>> branchRows
```

#### 4.2 子订单生成
- **订单复制**：复制原订单的所有属性
- **行数据调整**：根据分配方案调整商品数量
- **金额重算**：重新计算行金额和订单总额
- **优惠分配**：按比例分配优惠金额

#### 4.3 分单数量判断
```csharp
if (divideSheets.Count == 1)
{
    // 单仓库场景
}
else
{
    // 多仓库分单场景
}
```

### 第五阶段：保存审核

#### 5.1 单仓库场景处理
```csharp
// 直接保存并审核
string saveResult = await sheet.Save(cmd, false);
var approveResult = await divideApprove(sheet);
```
- **流程简化**：不需要分单，直接处理
- **性能优化**：减少数据库操作
- **结果返回**：标记为needDivide=false

#### 5.2 多仓库分单场景处理
```csharp
// 执行分单方案
var executeResult = await ExecuteDivideSheetPlan(sheet, divideSheets, cmd);
```

**分单执行详细步骤**：

1. **事务开始**：开启数据库事务
2. **批量保存**：
   ```csharp
   foreach (var divideSheet in divideSheets)
   {
       string saveResult = await divideSheet.Save(cmd, false);
   }
   ```
3. **事务提交**：保存成功后提交事务
4. **逐个审核**：
   ```csharp
   foreach (var sheet in divideSheets)
   {
       var approveResult = await divideApprove(sheet);
   }
   ```
5. **结果统计**：统计审核成功和失败的数量

### 第六阶段：结果返回

#### 6.1 成功场景
```json
{
    "result": "OK",
    "needDivide": true/false,
    "msg": "处理结果描述",
    "sheet_id": "订单ID",
    "sheet_no": "订单编号"
}
```

#### 6.2 失败场景
```json
{
    "result": "Error",
    "msg": "错误信息描述"
}
```

## 异常处理流程

### 1. 数据解析异常
- **异常类型**：JSON解析失败
- **处理方式**：记录详细错误日志
- **用户提示**：显示"审核失败,请联系技术支持"

### 2. 库存不足异常
- **异常类型**：商品库存无法满足需求
- **处理方式**：返回具体的库存不足信息
- **用户提示**：显示具体商品的库存不足情况

### 3. 权限不足异常
- **异常类型**：业务员无负库存权限且库存不足
- **处理方式**：返回权限不足错误
- **用户提示**：提示联系管理员开通权限

### 4. 保存失败异常
- **异常类型**：数据库保存操作失败
- **处理方式**：事务回滚，记录错误日志
- **用户提示**：显示"保存失败"具体信息

### 5. 审核失败异常
- **异常类型**：订单审核过程失败
- **处理方式**：记录失败数量，不影响其他分单
- **用户提示**：显示部分审核失败信息

## 性能优化策略

### 1. 数据库优化
- **批量查询**：一次性查询所有商品库存
- **索引利用**：利用商品ID和仓库ID的复合索引
- **连接优化**：使用LEFT JOIN减少查询次数

### 2. 内存优化
- **数据结构**：使用Dictionary提高查找效率
- **对象复用**：合理复用对象，减少GC压力
- **延迟加载**：按需加载数据，避免内存浪费

### 3. 事务优化
- **事务边界**：合理设置事务边界，避免长事务
- **批量操作**：批量保存减少事务开销
- **异步处理**：使用异步方法提高并发性能

## 监控和日志

### 1. 关键节点日志
- 分单开始和结束时间
- 库存查询耗时
- 分单方案生成耗时
- 保存和审核耗时

### 2. 异常日志
- 详细的异常堆栈信息
- 用户操作上下文
- 数据状态快照

### 3. 业务日志
- 分单方案详情
- 库存分配结果
- 审核成功失败统计

## 扩展点设计

### 1. 分单策略扩展
- 可以添加新的分单算法
- 支持基于成本、距离等因素的分单
- 支持用户自定义分单规则

### 2. 审核流程扩展
- 支持多级审核流程
- 支持条件审核（金额、客户等）
- 支持审核委托和代理

### 3. 通知机制扩展
- 支持分单完成通知
- 支持库存预警通知
- 支持审核结果通知

## 具体算法流程

### 正库存分单算法流程

```
开始
  ↓
遍历每个商品
  ↓
计算商品总需求量
  ↓
查询商品在各仓库的正库存
  ↓
有正库存？
  ↙ 否        ↘ 是
返回无库存    计算总可用库存
  ↓            ↓
结束         总可用库存 >= 需求量？
              ↙ 否        ↘ 是
            返回库存不足    按库存量降序排序仓库
              ↓              ↓
             结束           贪心分配算法
                           ↓
                         记录分配方案
                           ↓
                         下一个商品
                           ↓
                         所有商品处理完？
                           ↙ 否    ↘ 是
                         继续循环   返回分配方案
                                    ↓
                                   结束
```

### 负库存分单算法流程

```
开始
  ↓
第一阶段：正库存分配
  ↓
检查仓库中是否存在该商品？
  ↙ 否           ↘ 是
返回商品不存在    使用正库存分单算法
  ↓               ↓
结束            记录每个商品的剩余需求
                ↓
              第二阶段：负库存处理
                ↓
              遍历有剩余需求的商品
                ↓
              查找允许负库存的仓库
                ↓
              筛选有该商品的允许负库存仓库
                ↓
              有符合条件的仓库？
                ↙ 否           ↘ 是
              返回错误        查找已有负库存的仓库
                ↓               ↓
               结束           有已有负库存的仓库？
                              ↙ 否        ↘ 是
                            选择第一个     选择负库存最多的
                            有该商品的     (绝对值最大)
                            允许负库存     ↓
                            的仓库         合并选择结果
                              ↓           ↓
                              ↓           ↓
                            分配剩余需求到选定仓库
                              ↓
                            下一个商品
                              ↓
                            所有商品处理完？
                              ↙ 否    ↘ 是
                            继续循环   返回分配方案
                                        ↓
                                       结束
```

## 数据流转详解

### 1. 前端到后端数据流
```
前端订单对象 → JSON序列化 → HTTP POST → 后端接收 → JSON反序列化 → SheetSaleOrder对象
```

### 2. 库存查询数据流
```
商品ID列表 → SQL查询 → 数据库结果集 → 数据分类处理 → 库存字典结构
```

### 3. 分单方案数据流
```
库存字典 + 需求字典 → 分单算法 → 分配方案字典 → 分单创建 → 子订单列表
```

### 4. 保存审核数据流
```
子订单列表 → 批量保存 → 数据库事务 → 逐个审核 → 结果统计 → 前端响应
```

## 关键决策点

### 1. 分单策略选择
**决策条件**：业务员是否有负库存开单权限
- **有权限**：使用DividePlanByNegative算法
- **无权限**：使用DividePlanByPositive算法

### 2. 仓库选择策略
**正库存场景**：
- 优先选择库存最多的仓库
- 目标：最小化仓库使用数量

**负库存场景**：
- 优先选择已有负库存的仓库
- 目标：集中负库存，便于管理

### 3. 分单数量判断
**判断条件**：生成的分单数量
- **数量=1**：单仓库场景，直接保存审核
- **数量>1**：多仓库场景，执行分单流程

## 错误恢复机制

### 1. 保存阶段错误恢复
```csharp
try
{
    // 批量保存分单
    foreach (var sheet in divideSheets)
    {
        await sheet.Save(cmd, false);
    }
    tran.Commit();
}
catch (Exception e)
{
    tran.Rollback(); // 回滚所有保存操作
    return ("Error", $"保存分单失败: {e.Message}", new List<SheetSaleOrder>());
}
```

### 2. 审核阶段错误处理
```csharp
int failedCount = 0;
foreach (var sheet in divideSheets)
{
    var approveResult = await divideApprove(sheet);
    if (approveResult.result != "OK")
    {
        failedCount++; // 记录失败数量，继续处理其他分单
    }
}
```

## 并发控制

### 1. 重复操作防护
```csharp
string redisKey = $"voOperateDuplicate{company_id}-{sheet_id}";
string redisValue = await RedisHelper.GetSetAsync(redisKey, "1");
if (redisValue == "1")
{
    return "点快啦，请勿重复操作凭证";
}
```

### 2. 库存锁定机制
- 查询时获取当前库存快照
- 保存时验证库存是否发生变化
- 必要时重新计算分单方案

## 测试验证点

### 1. 功能测试点
- 单商品单仓库场景
- 单商品多仓库场景
- 多商品多仓库场景
- 库存不足场景
- 权限不足场景

### 2. 性能测试点
- 大量商品的分单性能
- 大量仓库的查询性能
- 并发分单的系统表现
- 内存使用情况

### 3. 异常测试点
- 网络中断恢复
- 数据库连接异常
- 内存不足情况
- 并发冲突处理

## 运维监控指标

### 1. 业务指标
- 分单成功率
- 平均分单数量
- 库存利用率
- 审核通过率

### 2. 技术指标
- 接口响应时间
- 数据库查询耗时
- 内存使用峰值
- 错误发生频率

### 3. 告警规则
- 分单失败率超过阈值
- 响应时间超过阈值
- 数据库连接异常
- 内存使用过高
