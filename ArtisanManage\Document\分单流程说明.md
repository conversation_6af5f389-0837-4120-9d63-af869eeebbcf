# 分单流程说明

## 完整分单流程

### 1. 接收分单请求
- 接收前端传来的销售订单数据
- 解析JSON数据为SheetSaleOrder对象
- 初始化订单数据

### 2. 生成分单方案
- 统计每个商品所需数量
- 查询各仓库库存情况
- 检查库存总和是否满足需求
- 生成商品仓库分配方案（itemBranchAllocation）

### 3. 库存不足检查
```
如果任何商品的库存总和 < 需求数量
├── 返回错误信息："商品XXX库存不足，需要X，总库存仅有Y"
└── 结束流程
```

### 4. 分单判断
```
如果不需要分单
├── 保存原单据
├── 返回：needDivide = false, sheet_id, sheet_no
└── 结束流程

如果需要分单
├── 继续执行分单流程
└── 进入步骤5
```

### 5. 保存原单据
- **重要**：先保存原始订单
- 生成原单据的sheet_id和sheet_no
- 如果保存失败，返回错误信息

### 6. 创建分单
- 调用 CreateDivideSheets 方法
- 根据 itemBranchAllocation 参数创建分单
- 按仓库分组创建多个新订单
- 按规则分配金额到各个分单

### 7. 执行分单方案
- 逐个保存分单到数据库
- 收集所有分单的单据号

### 8. 返回结果
```json
{
  "result": "OK",
  "needDivide": true,
  "msg": "订单已按库存情况自动分单",
  "originalSheetId": "原单据ID",
  "originalSheetNo": "原单据号",
  "dividedSheetCount": 3,
  "dividedSheetNos": ["分单1号", "分单2号", "分单3号"]
}
```

## 关键特性

### 原单据保存
- **时机**：在创建分单之前
- **目的**：确保原单据有完整的sheet_id用于追溯
- **失败处理**：如果原单据保存失败，整个分单流程终止

### 分单保存
- **顺序**：按仓库分组顺序依次保存
- **失败处理**：如果任何分单保存失败，返回错误信息
- **单据号**：每个分单都有独立的sheet_id和sheet_no

### 金额分配
- **原则**：向下取整 + 最后一单补齐
- **字段**：所有金额字段都按相同规则分配
- **精度**：确保分单总金额与原单据完全一致

## 数据流转

```
原始订单数据
    ↓
解析和验证
    ↓
生成分单方案
    ↓
库存检查
    ↓
保存原单据 ← 生成sheet_id
    ↓
创建分单1 → 保存 → 生成sheet_no1
    ↓
创建分单2 → 保存 → 生成sheet_no2
    ↓
创建分单N → 保存 → 生成sheet_noN
    ↓
返回结果（包含原单据ID和所有分单号）
```

## 错误处理

1. **JSON解析失败**：返回"保存失败,请联系技术支持"
2. **库存不足**：返回具体的库存不足信息
3. **原单据保存失败**：返回"保存原单据失败"
4. **分单保存失败**：返回"保存分单失败"
5. **其他异常**：返回"分单处理失败,请联系技术支持"

## 使用建议

1. **前端处理**：根据needDivide字段判断是否发生了分单
2. **单据追溯**：使用originalSheetId关联原单据和分单
3. **用户提示**：向用户展示分单结果和各分单号
4. **后续操作**：可以基于dividedSheetNos进行后续的单据操作

## 注意事项

- 分单操作不可逆，请确保在合适的时机调用
- 原单据和分单都会保存到数据库，注意数据一致性
- 分单后的订单状态与原订单相同
- 建议在分单前进行充分的数据验证
