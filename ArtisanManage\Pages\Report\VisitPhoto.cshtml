﻿@page
@model ArtisanManage.Pages.Report.VisitPhotoModel
@{
}
<!DOCTYPE html>
<html>
<head>
    <title></title>
    <script src="~/js/Vue.js"></script>
    <link href="~/lib/element-ui/theme/index.css" rel="stylesheet" />
    <script src="~/lib/element-ui/index.min.js"></script>
    <script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>
        <script src="~/js/FileSaver.min.js"></script>

    <style>
    html,
    body {
    margin: 0;
    padding: 0;
    }

    [v-cloak] {
    display: none;
    }

    #visitMaintain {
        padding:0 20px;
        box-sizing: border-box;
        width: 100%;
    }
    .visit-mod{
            display: flex;
            flex-direction: column;
            width: 100%;
    }
    .visit-item{
        display:flex;
        flex-direction:column;    
    }
    .photo-name{ 
    }
    .photo-box{
        padding-left:10px;
        display:flex;
    }
    .photo-item{
        margin-right:10px;
        display:flex;
        flex-direction:column;
        align-items:center;
    }

    </style>
</head>
<body>
    <div id="visitMaintain">
       <div class="visit-mod">
           <div class="visit-item" v-for="(item,index) in visitInfo">
               <div class="photo-name" style="padding:10px 0;">{{item.name}}</div>
              <div class="photo-box">
                    <div class="photo-item" v-for="(citem,cindex) in item.photo">
                        <el-image :style="imageSize === 'large' ? 'width: 200px; height: 200px' : 'width: 100px; height: 100px'"
                                  :src="citem.url"
                                  :preview-src-list="photos">
                        </el-image>
                        <span style="padding:5px 0; font-size:12px; color:#999;">{{citem.type}}</span>
                        <div style="color:blue;cursor:pointer;" v-on:click="downloadPhoto(citem.url,citem.type)">下载</div>
                    </div>
              </div>
           </div>
       </div>
    </div>
    <script>
        var app = new Vue({
            el: "#visitMaintain",
            data() {
                return {
                    operKey:'',
                    visitInfo:[],
                    photos:[],
                    visitID: '',
                    huaWeiObs: '',
                    imageSize: 'normal'  // 添加图片大小变量
                }
            },
            methods:{
                getQueryString(name) {
                    var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
                    var r = window.location.search.substr(1).match(reg);
                    if (r != null) {
                        return decodeURI(r[2])
                    }
                    return null;
                },
                handleInitPageQueryParams() {
                    this.operKey = this.getQueryString('operKey')
                    this.visitID = this.getQueryString('visitID')
                    this.huaWeiObs = this.getQueryString('domain')
                    this.imageSize = this.getQueryString('imageSize') || 'normal'  // 添加图片大小参数
                },
                getVisitPhotos(params) {
                    $.ajax({
                        url: '/api/VisitRecord/GetVisitPhoto',
                        type: 'POST',
                        data: JSON.stringify(params),
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json'
                    }).then(res => {
                        this.handleVisitInfo(res.data)
                    })
                },
                downloadPhoto(url,type){
                    console.log(url)
                     this.imageUrlToBase64(url,base64=>{
                         saveAs(base64, this.visitID+`_${type}.jpg`)
                     })

                },
                imageUrlToBase64(src,cb){
                        const img = new Image()
                        img.crossOrigin = ''
                        img.src = src+"?a="+Math.random()
                        img.onload = function () {
                            const canvas = document.createElement('canvas')
                            canvas.width = img.width
                            canvas.height = img.height
                            const ctx = canvas.getContext('2d')
                            if(ctx){
                                ctx.drawImage(img, 0, 0, img.width, img.height)
                                const ext = img.src.substring(img.src.lastIndexOf('.') + 1).toLowerCase()
                                const dataURL = canvas.toDataURL('image/' + ext)
                                cb(dataURL)
                            }
                          
                        }
                },
                handleVisitInfo(jsonData) {
                   if(jsonData.door_picture !=""){
                        this.visitInfo.push({ name: "门头照", photo: [{ type: "必选", url: this.huaWeiObs + 'uploads' + jsonData.door_picture }] })
                        this.photos.push(this.huaWeiObs + 'uploads'+jsonData.door_picture)
                   }
                    if (jsonData.showcase_pictures != "") {
                        let showcase_pictures = JSON.parse(jsonData.showcase_pictures)
                        if (showcase_pictures.length !=0){
                            let new_showcase_pictures =[]
                            showcase_pictures.forEach(e=>{
                                this.photos.push(this.huaWeiObs + 'uploads' + e)
                                new_showcase_pictures.push({ type: "必选", url: this.huaWeiObs + 'uploads' + e })
                            })
                            this.visitInfo.push({ name: "陈列照", photo: new_showcase_pictures })
                        }
                    }
                    if (jsonData.work_content !=""){
                        let work_content = JSON.parse(jsonData.work_content)
                        if (work_content.length != 0) {
                            work_content.forEach(e=>{
                                if(e.action.type == "photo"){
                                    let photo = []
                                    if (e.work_content.mandatory && e.work_content.mandatory.length!=0){
                                       e.work_content.mandatory.forEach(e => {
                                            this.photos.push(this.huaWeiObs + e)
                                            photo.push({ type: "必选", url: this.huaWeiObs + e })
                                       })
                                    }
                                    if (e.work_content.optional && e.work_content.optional.length != 0){
                                        e.work_content.optional.forEach(e => {
                                            if (e.charAt(0) == "/") e = e.slice(1)
                                            this.photos.push(this.huaWeiObs + e)
                                            photo.push({ type: "可选", url: this.huaWeiObs + e })
                                        })
                                    }
                                    // 只有当photo数组有内容时才添加到visitInfo
                                    if(photo.length > 0) {
                                        this.visitInfo.push({ name:e.action.name, photo: photo })
                                    }
                                }
                            })
                        }
                    }
                },
            },
            async created() {
                this.handleInitPageQueryParams()
                var params =  {operKey: this.operKey,visitID:this.visitID}
                await this.getVisitPhotos(params)
            },

        })
    </script>
</body>
</html>
