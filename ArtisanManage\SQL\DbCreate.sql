DO $$
DECLARE
    batch_size INTEGER := 3000;  -- 每批次传输的数据量
    _offset INTEGER = 0;
    day_offset integer:=0;
    start_date TIMESTAMP := '2024-10-25 00:00:00';  -- 开始日期和时间
    end_date TIMESTAMP := '2024-10-29 00:00:00';    -- 结束日期和时间
    now_datetime TIMESTAMP;  -- 当前处理的时间段起点
    next_datetime TIMESTAMP; -- 当前时间段的终点
    total_records INTEGER := 0;
    now_day_records INTEGER := 0;
    now_period_records INTEGER := 0;
    now_period_seconds integer:=3600;
    sql TEXT := '';
    count_sql TEXT := '';
    total_offset INTEGER := 0;
    pieces integer:=0;
    now_minute integer:=0;
BEGIN
    -- 连接到源数据库
    PERFORM dblink_connect('backup', 'host=************ dbname=artisan_001 user=root password=Abcd1234_');

    -- 获取所有记录的总数，用于整体进度
    count_sql := format(
        'SELECT count(*) FROM log_sql
         WHERE happen_time >= ''%s'' AND happen_time < ''%s''',
        start_date, end_date
    );
    SELECT count INTO total_records FROM dblink('backup', count_sql) AS t(count INT);
    RAISE NOTICE 'Total records to process: %', total_records;

    -- 初始化当前时间段
    now_datetime := start_date;

    -- 循环遍历日期
    WHILE now_datetime < end_date LOOP
        -- 根据时间段选择查询方式
        IF EXTRACT(HOUR FROM now_datetime) =0 THEN
            day_offset=0;

          --  next_datetime := now_datetime + INTERVAL '8 hour';  -- 查询到当天8点

             -- 获取当前时间段的总记录数
            count_sql := format(
                'SELECT count(*) FROM log_sql
                 WHERE happen_time >= ''%s'' AND happen_time < ''%s''',
                now_datetime, now_datetime + INTERVAL '1 day'
            );
            SELECT count INTO now_day_records FROM dblink('backup', count_sql) AS t(count INT);
            RAISE NOTICE 'Processing date %  with % records', now_datetime,  now_day_records;

        --ELSIF EXTRACT(HOUR FROM now_datetime) >= 19 THEN
       --     next_datetime := date_trunc('day', now_datetime) + INTERVAL '1 day';  -- 查询到次日0点
        END IF;



        if now_period_records<10000 then
           now_period_seconds=3600;
        end if;
        next_datetime := now_datetime +  make_interval(secs => now_period_seconds);

        count_sql := format(
                'SELECT count(*) FROM log_sql
                 WHERE happen_time >= ''%s'' AND happen_time < ''%s''',
                now_datetime, next_datetime
        );
        SELECT count INTO now_period_records FROM dblink('backup', count_sql) AS t(count INT);
        if now_period_records>200000 then
            pieces= CEIL(now_period_records / 100000.0);
            if pieces>1 then
                now_period_seconds=now_period_seconds/pieces;
                continue;
                --next_datetime := now_datetime + make_interval(secs => now_period_seconds);
            end if;

         end if;

         if next_datetime>end_date then
            next_datetime=end_date;
         end if;

         _offset := 0;  -- 每时间段偏移量从0开始



        -- 按批次插入数据
        LOOP


            -- 查询当前时间段内的一个批次数据
            sql := format(
                'SELECT company_id, flow_id, sql, happen_time
                 FROM log_sql
                 WHERE happen_time >= ''%s'' AND happen_time < ''%s''
                 OFFSET %s LIMIT %s',
                now_datetime, next_datetime, _offset, batch_size
            );

            -- 将数据插入目标表
            INSERT INTO log_sql (company_id, flow_id, sql, happen_time)
            SELECT * FROM dblink('backup', sql)
            AS t(company_id INTEGER, flow_id INTEGER, sql TEXT, happen_time TIMESTAMP);

            -- 输出进度信息
            RAISE NOTICE 'Processed % records for period % to %, *** % of % for day %  **** % of total %',
                         _offset + batch_size, now_datetime, next_datetime,day_offset + batch_size,now_day_records,date_trunc('day', now_datetime),
                         total_offset + batch_size, total_records;

            -- 如果没有更多数据，退出批次循环
            IF NOT FOUND THEN
                EXIT;
            END IF;

            -- 增加偏移量
            _offset := _offset + batch_size;
            day_offset=day_offset+batch_size;
            total_offset := total_offset + batch_size;
        END LOOP;

        -- 处理下一个时间段
        now_datetime := next_datetime;
    END LOOP;

    -- 断开连接
    PERFORM dblink_disconnect('backup');
    RAISE NOTICE 'Data transfer completed from % to %.', start_date, end_date;
END $$;


create table if not exists public.log_sql
(
    flow_id     serial,
    company_id  integer,
    happen_time timestamp,
    database    text,
    sql         text,
    db_user     text,
    oper_id     integer
);
create table if not exists log_msg
(
    flow_id     serial,
    company_id  integer,
    happen_time timestamp,
    msg         text,
    msg_type    text
);

SELECT create_hypertable('log_sql','happen_time', 'company_id', 20);
ALTER TABLE log_sql SET (
  timescaledb.compress,
  timescaledb.compress_segmentby = 'company_id'
);
SET timescaledb.license = 'timescale';
SELECT add_compression_policy('log_sql', INTERVAL '15 days');

SELECT create_hypertable('log_sql','happen_time','company_id',20);

create extension timescaledb;
CREATE EXTENSION IF NOT EXISTS timescaledb;

create index idx_log_sql_happen_time on log_sql(company_id,happen_time);
CREATE INDEX idx_log_sql_sql_gin ON log_sql USING GIN (to_tsvector('english', sql));

create index idx_log_msg_happen_time on log_msg(company_id,happen_time);
CREATE INDEX idx_log_msg_msg_gin ON log_msg USING GIN (to_tsvector('english', msg));


CREATE EXTENSION tablefunc;
CREATE TABLE Commission_Plan(
company_id INT NOT NULL,
plan_id SERIAL PRIMARY KEY,
plan_name text UNIQUE not NULL,
type text,
content jsonb
);

CREATE TABLE Commission_Plan_Map(
company_id INT NOT NULL,
id SERIAL PRIMARY KEY,
employee_id int NOT NULL,
plan_id int NOT NULL,
UNIQUE (employee_id,plan_id)
);
create table prepay_detail
(
    supcust_id   integer not null,
    sheet_id     integer not null,
    company_id   integer not null,
    sub_id       integer not null,
    sheet_type   text,
    red_flag     smallint,
    balance      numeric,
    init_balance numeric,
    constraint prepay_detail_pk
        primary key (company_id, sheet_id, supcust_id, sub_id)
) partition by hash(company_id);

CREATE TABLE "prepay_detail_0" PARTITION OF prepay_detail FOR VALUES WITH(MODULUS 10, REMAINDER 0);
CREATE TABLE "prepay_detail_1" PARTITION OF prepay_detail FOR VALUES WITH(MODULUS 10, REMAINDER 1);
CREATE TABLE "prepay_detail_2" PARTITION OF prepay_detail FOR VALUES WITH(MODULUS 10, REMAINDER 2);
CREATE TABLE "prepay_detail_3" PARTITION OF prepay_detail FOR VALUES WITH(MODULUS 10, REMAINDER 3);
CREATE TABLE "prepay_detail_4" PARTITION OF prepay_detail FOR VALUES WITH(MODULUS 10, REMAINDER 4);
CREATE TABLE "prepay_detail_5" PARTITION OF prepay_detail FOR VALUES WITH(MODULUS 10, REMAINDER 5);
CREATE TABLE "prepay_detail_6" PARTITION OF prepay_detail FOR VALUES WITH(MODULUS 10, REMAINDER 6);
CREATE TABLE "prepay_detail_7" PARTITION OF prepay_detail FOR VALUES WITH(MODULUS 10, REMAINDER 7);
CREATE TABLE "prepay_detail_8" PARTITION OF prepay_detail FOR VALUES WITH(MODULUS 10, REMAINDER 8);
CREATE TABLE "prepay_detail_9" PARTITION OF prepay_detail FOR VALUES WITH(MODULUS 10, REMAINDER 9);

create table g_setting(set_name text primary key,set_value text);
create table g_server(server_id serial primary key,server_uri text,db_server text,coolie_server id);
create table g_version(g_app_ver float,g_pc_ver float) 

create table g_company(company_id serial primary key,company_name text,boss_mobile text, server_id integer,app_ver float,pc_ver float,agent_name text,user_count int2,user_count_discount1 int2,fee_discount1 float,user_count_discount2 int2,fee_discount2 float,position_way text);
alter table g_company add column business_start_period date;
alter table g_company add column business_period date;

create table g_operator(oper_id serial primary key,company_id integer not null,oper_name text,oper_status int2,mobile text, oper_pw text,can_login boolean, is_admin boolean default false, CONSTRAINT company_id_mobile_uKey UNIQUE(company_id,mobile));
create table business(business_id serial primary key,business_name text,company_id integer);

create table business_monthly_closing (
"company_id" int4 NOT NULL,
"sheet_id" serial,
"make_time" timestamp,
"maker_id" int4,
"period" date,
"red_flag" text,
"red_time" timestamp
);

create table info_operator(company_id integer not null,oper_id integer not null,mobile text, oper_group integer,oper_name text,oper_pw text,order_index int,can_login boolean, is_seller boolean, is_sender boolean,restrict_branches boolean, salary_style integer,base_salary numeric(14,4), base_salary_change integer, last_salary_sum_time timestamp,ticheng_set_name text,py_str text,depart_id integer,depart_path text,oper_customdesk jsonb,role_id integer,oper_regions json,status int2,oper_no text,log_report text,app_desktop jsonb,pc_desktop jsonb,seller_max_arrears numeric(10,2),entry_date timestamp, identity_card text,constraint pk_info_operator primary key(oper_id));
create table info_oper_group(company_id integer not null, group_id serial,group_name text, constraint pk_info_oper_group primary key(group_id));
CREATE INDEX idx_info_operator_company_id ON info_operator(company_id);
CREATE INDEX idx_info_operator_depart_path ON info_operator USING gin (depart_path gin_trgm_ops);

create table info_pay_way(company_id integer not null,sub_id integer,sub_name text,payway_index smallint,payway_key text,payway_type text,py_str text, sale_avail boolean,buy_avail boolean,is_order boolean  CONSTRAINT pk_info_pay_way_primary_key PRIMARY KEY(sub_id)); 

create table oper_branch_rights(company_id integer not null,oper_id integer not null,branch_id integer not null,sheet_x boolean,sheet_t boolean,sheet_xd boolean,sheet_td boolean,sheet_dr boolean,sheet_dc boolean,sheet_cg boolean,sheet_ct boolean,sheet_pd boolean,query_stock boolean, CONSTRAINT pk_oper_branch_rights PRIMARY KEY(company_id,oper_id,branch_id)); 

create table cw_subject(company_id integer not null,sub_id serial,sub_code int8,sub_name text,sub_type text,mother_id integer,level smallint,son_index smallint,py_str text,direction smallint,order_index int,other_sub text, check_client boolean,check_sup boolean,check_department smallint,make_brief text,status text,is_order boolean,for_pay boolean, constraint pk_cw_subject primary key(sub_id));
 
 
 
 CREATE TABLE "public"."cw_sub_balance" (
  "company_id" int4 NOT NULL,
  "period" date NOT NULL,
  "sub_id" int4 NOT NULL,
  "month_start_balance" float8,
  "year_start_balance" float8,
  "balance" numeric(14,2),
  "debit_amount" numeric(14,2),
  "credit_amount" numeric(14,2),
  primary key(company_id,sub_id,period) 
) partition by hash(company_id)


CREATE TABLE "cw_sub_balance_0" PARTITION OF cw_sub_balance FOR VALUES WITH(MODULUS 10, REMAINDER 0);
CREATE TABLE "cw_sub_balance_1" PARTITION OF cw_sub_balance FOR VALUES WITH(MODULUS 10, REMAINDER 1);
CREATE TABLE "cw_sub_balance_2" PARTITION OF cw_sub_balance FOR VALUES WITH(MODULUS 10, REMAINDER 2);
CREATE TABLE "cw_sub_balance_3" PARTITION OF cw_sub_balance FOR VALUES WITH(MODULUS 10, REMAINDER 3);
CREATE TABLE "cw_sub_balance_4" PARTITION OF cw_sub_balance FOR VALUES WITH(MODULUS 10, REMAINDER 4);
CREATE TABLE "cw_sub_balance_5" PARTITION OF cw_sub_balance FOR VALUES WITH(MODULUS 10, REMAINDER 5);
CREATE TABLE "cw_sub_balance_6" PARTITION OF cw_sub_balance FOR VALUES WITH(MODULUS 10, REMAINDER 6);
CREATE TABLE "cw_sub_balance_7" PARTITION OF cw_sub_balance FOR VALUES WITH(MODULUS 10, REMAINDER 7);
CREATE TABLE "cw_sub_balance_8" PARTITION OF cw_sub_balance FOR VALUES WITH(MODULUS 10, REMAINDER 8);
CREATE TABLE "cw_sub_balance_9" PARTITION OF cw_sub_balance FOR VALUES WITH(MODULUS 10, REMAINDER 9);


create table cw_sub_balance_assister(
  company_id integer,
  period date,
  sub_id integer,
  assister_type text,
  assister_id integer,
  year_start_balance float, 
  month_start_balance float, 
  balance float,
  debit_amount float,
  credit_amount float,
  constraint pk_cw_sub_balance_assister primary key (company_id,period,sub_id,assister_type,assister_id)
) partition by hash(company_id);

CREATE TABLE cw_sub_balance_assister_0 PARTITION OF cw_sub_balance_assister FOR VALUES WITH(MODULUS 10, REMAINDER 0);
CREATE TABLE cw_sub_balance_assister_1 PARTITION OF cw_sub_balance_assister FOR VALUES WITH(MODULUS 10, REMAINDER 1);
CREATE TABLE cw_sub_balance_assister_2 PARTITION OF cw_sub_balance_assister FOR VALUES WITH(MODULUS 10, REMAINDER 2);
CREATE TABLE cw_sub_balance_assister_3 PARTITION OF cw_sub_balance_assister FOR VALUES WITH(MODULUS 10, REMAINDER 3);
CREATE TABLE cw_sub_balance_assister_4 PARTITION OF cw_sub_balance_assister FOR VALUES WITH(MODULUS 10, REMAINDER 4);
CREATE TABLE cw_sub_balance_assister_5 PARTITION OF cw_sub_balance_assister FOR VALUES WITH(MODULUS 10, REMAINDER 5);
CREATE TABLE cw_sub_balance_assister_6 PARTITION OF cw_sub_balance_assister FOR VALUES WITH(MODULUS 10, REMAINDER 6);
CREATE TABLE cw_sub_balance_assister_7 PARTITION OF cw_sub_balance_assister FOR VALUES WITH(MODULUS 10, REMAINDER 7);
CREATE TABLE cw_sub_balance_assister_8 PARTITION OF cw_sub_balance_assister FOR VALUES WITH(MODULUS 10, REMAINDER 8);
CREATE TABLE cw_sub_balance_assister_9 PARTITION OF cw_sub_balance_assister FOR VALUES WITH(MODULUS 10, REMAINDER 9); 


CREATE TABLE cw_op_sub_init_main (
  "company_id" int4 NOT NULL,
  "sheet_id" serial,
  "happen_time" timestamp,
  "approver_id" int4,
  "approve_brief" text,
  "red_flag" text,
  "period" text
);

CREATE TABLE cw_op_sub_init_detail(
  "company_id" int4,
  "sheet_id" int4,
  "sub_id" int4,
  "year_start_balance" numeric(14,2),
  "balance" numeric(14,2),
  "remark" text
);


CREATE TABLE cw_op_sub_init_assister_main (
  "company_id" int4 NOT NULL,
  "sheet_id" serial,
  "sheet_no" int4,
  "period" text,
  "make_time" timestamp,
  "maker_id" int4,
  "make_brief" text
);

CREATE TABLE cw_op_sub_init_assister_detail(
  "company_id" int4,
  "sheet_id" int4,
  "row_index" int4 NOT NULL ,
  "period" text,
  "make_time" timestamp,
  "maker_id" int4,
  "sub_id" int4,
  "assister1_type" text,
  "assister1_id" int4,
  "assister2_type" text,
  "assister2_id" int4,
  "balance" numeric(14,2),
  "remark" text
);


CREATE TABLE cw_voucher_main (
  "company_id" int4,
  "sheet_id" serial,
  "period" date,
  "sheet_no" int4,
  "make_time" timestamp,
  "maker_id" int4,
  "approve_time" timestamp,
  "approver_id" int4,
  "happen_time" timestamp,
  "make_brief" text,
  "red_flag" text,
  "auto_created" bool,
  "red_sheet_id" int4,
  "approve_brief" text
)
PARTITION BY RANGE (happen_time);


CREATE INDEX idx_cw_voucher_main_company_id ON cw_voucher_main(company_id);
CREATE INDEX idx_cw_voucher_main_sheet_id ON cw_voucher_main(sheet_id);
CREATE INDEX idx_cw_voucher_main_happen_time ON cw_voucher_main(happen_time);
CREATE INDEX idx_cw_voucher_main_approve_time ON cw_voucher_main(approve_time);
CREATE INDEX idx_cw_voucher_main_red_flag ON cw_voucher_main(red_flag); 
 
create table cw_voucher_main_23012308 partition of cw_voucher_main for values from ('2023-01-01') to ('2023-08-01'); 
alter table cw_voucher_main add column sheet_attribute jsonb;


CREATE TABLE "public"."cw_voucher_detail" (
  "company_id" int4,
  "sheet_id" int4,
  "row_index" int4 NOT NULL ,
  "sub_id" int4,
  "assister1_type" text,
  "assister1_id" int4,
  "assister2_type" text,
  "assister2_id" int4,
  "change_amount" float8,
  "happen_time" timestamp,
  "remark" text
)
PARTITION BY RANGE (happen_time);
CREATE INDEX idx_cw_voucher_detail_company_id ON cw_voucher_detail(company_id);
CREATE INDEX idx_cw_voucher_detail_sheet_id ON cw_voucher_detail(sheet_id);
CREATE INDEX idx_cw_voucher_detail_happen_time ON cw_voucher_detail(happen_time);
CREATE INDEX idx_cw_voucher_detail_sub_id ON cw_voucher_detail(sub_id);
create table cw_voucher_detail_23012308 partition of cw_voucher_detail for values from ('2023-01-01') to ('2023-8-01'); 

alter table cw_voucher_detail add column debit_amount float8; 
alter table cw_voucher_detail add column credit_amount float8;

CREATE TABLE "public"."cw_voucher_no" (
  "company_id" int4 NOT NULL,
  "period" date NOT NULL,
  "next_voucher_no" int4
);

CREATE TABLE  cw_voucher_sheet_mapper (
  company_id int4 NOT NULL,
  voucher_id int4 NOT NULL,
  business_sheet_type text,
  business_sheet_id int4 NOT NULL,
  primary key(company_id,voucher_id,business_sheet_type,business_sheet_id)
);

CREATE TABLE "public"."cw_op_monthly_closing" (
  "company_id" int4,
  "sheet_id" serial,
  "period" date,
  "profit_loss_voucher_id" int4,
  "happen_time" timestamp(6),
  "approve_time" timestamp(6),
  "approver_id" int4,
  "approve_brief" text,
  "red_flag" text,
  "red_flag_id" int4
)
;
CREATE INDEX idx_cw_op_monthly_closing_company_id ON cw_op_monthly_closing(company_id);

CREATE INDEX idx_cw_op_monthly_closing_period ON cw_op_monthly_closing(period);


CREATE OR REPLACE FUNCTION public.yj_getnewvoucherno(v_company_id int4, happen_time date, OUT o_sheet_no text) RETURNS pg_catalog.text AS $$   
declare    
V_LOST_ID integer=0;
V_NEXT_ID integer=0;
IN_HAPPEN_TIME date=happen_time;
BEGIN 
select sheet_no+1 into V_LOST_ID from cw_voucher_main where company_id=V_COMPANY_ID and to_char(period, 'yyyymm')=to_char(IN_HAPPEN_TIME, 'yyyymm') and sheet_no+1 not in(select sheet_no from cw_voucher_main where company_id=V_COMPANY_ID and to_char(period, 'yyyymm')=to_char(IN_HAPPEN_TIME, 'yyyymm') ) and sheet_no+1 <> (select max(sheet_no)+1 from  cw_voucher_main where company_id=V_COMPANY_ID and to_char(period, 'yyyymm')=to_char(IN_HAPPEN_TIME, 'yyyymm')) order by sheet_no+1 limit 1;
if V_LOST_ID is not null then		--有断号，则补充
	V_NEXT_ID=V_LOST_ID;
else	--没有断号，则新增
	select next_voucher_no into V_NEXT_ID from cw_voucher_no where company_id=V_COMPANY_ID and to_char(period, 'yyyymm')=to_char(IN_HAPPEN_TIME, 'yyyymm');
	if V_NEXT_ID is null  then
		insert into cw_voucher_no (company_id,period,next_voucher_no) values (V_COMPANY_ID,to_char(IN_HAPPEN_TIME, 'yyyy-mm-01')::date,2);
		V_NEXT_ID=1;
	else
		update cw_voucher_no set next_voucher_no=next_voucher_no+1 where company_id=V_COMPANY_ID and to_char(period, 'yyyymm')=to_char(IN_HAPPEN_TIME, 'yyyymm');
	end if;
end if;
O_SHEET_NO=V_NEXT_ID::text;
END;   
$$ LANGUAGE plpgsql VOLATILE COST 100


CREATE TABLE "public"."cw_log" (
  "company_id" int4 NOT NULL,
  "flow_id" serial,
  "happen_time" timestamp(6),
  "oper_id" int4,
  "sheet_id" int4,
  "sheet_type" text COLLATE "pg_catalog"."default",
  "log" text COLLATE "pg_catalog"."default"
)
PARTITION BY RANGE (
  "happen_time" "pg_catalog"."timestamp_ops"
)
;
create table cw_log_23012308 partition of cw_log for values from ('2023-01-01') to ('2023-08-01'); 
create table cw_log_23082312 partition of cw_log for values from ('2023-08-01 00:00:01') to ('2023-12-01');


CREATE TABLE cw_ppe_type ( 
    "company_id" int4 NOT NULL,
    "type_id" serial,
    "type_no" text,
    "type_name" text,
    "remark" text
);


create table arrears_balance(company_id integer not null,supcust_id integer,balance float default 0,pend_amount float default 0, init_balance float8, init_time timestamp ,constraint pk_arrears_balance primary key(company_id,supcust_id));

alter table arrears_balance add column balance_cust numeric(15,2), add column balance_sup numeric(15,2);--客户/供应商余额分开，原合并余额保留

CREATE INDEX idx_arrears_balance_company_id ON arrears_balance(company_id);
CREATE INDEX idx_arrears_balance_supcust_id ON arrears_balance(supcust_id);

create table arrears_balance_auxiliary(company_id integer,auxiliary_type text,auxiliary_id integer,auxiliary_balance float default 0, auxiliary_pend_amount float default 0);
CREATE UNIQUE INDEX idx_arrears_balance_auxiliary ON arrears_balance_auxiliary(company_id,auxiliary_type,auxiliary_id);



create table prepay_balance(company_id integer not null,supcust_id integer,sub_id integer,balance float,init_balance float8,init_time timestamp,constraint pk_prepay_balance primary key(company_id,supcust_id,sub_id));
 
create table client_account_history(company_id integer not null,flow_id serial,supcust_id integer, happen_time timestamp,sheet_type text,sub_type text,sheet_id integer,sub_id integer,change_amount numeric(14,2),now_balance numeric(14,2),now_prepay_balance  numeric(14,2),red_flag integer) partition by range(happen_time);

create table client_account_history_10012001 partition of client_account_history for values from ('2010-01-01') to ('2020-01-01');

create table client_account_history_20012301 partition of client_account_history for values from ('2020-01-01') to ('2023-01-01');
create index idx_client_account_history on client_account_history(sheet_id);
CREATE INDEX idx_client_account_history_supcust_id ON client_account_history(supcust_id);
CREATE INDEX idx_client_account_history_company_id ON client_account_history(company_id);
CREATE INDEX idx_client_account_history_happen_time ON client_account_history(happen_time);

--往来账重构
alter table client_account_history add column ap_type text, add column arrears_balance_cust_approve numeric(15,2), add column arrears_balance_sup_approve numeric(15,2),
    add column arrears_balance_happen numeric(15,2), add column arrears_balance_cust_happen numeric(15,2), add column arrears_balance_sup_happen numeric(15,2),
    add column now_prepay_balance_real numeric(15,2), add column prepay_all_cust_approve numeric(15,2), add column prepay_all_sup_approve numeric(15,2),
    add column now_prepay_balance_happen_time_real numeric(15,2), add column prepay_all_cust_happen numeric(15,2), add column prepay_all_sup_happen numeric(15,2);

alter table arrears_balance add column balance_cust numeric(15,2), add column balance_sup numeric(15,2);



CREATE TABLE sheet_sale_order_main(company_id integer NOT NULL,sheet_id serial,sheet_no text NOT NULL, visit_id integer,sheet_type text,money_inout_flag smallint, branch_id integer,department_id integer,receive_addr integer, red_flag smallint,supcust_id integer,total_amount float8 NOT NULL, is_retail boolean,tax_amount float4,discount_info text,order_source text,
payway1_id integer,payway1_amount float8,payway2_id integer,payway2_amount float8,payway3_id integer,payway3_amount float8, now_pay_amount float8 NOT NULL,
now_disc_amount float4 DEFAULT 0 NOT NULL,paid_amount float8 NOT NULL,disc_amount float4 DEFAULT 0 NOT NULL, prepay_amount numeric(8,2) default 0 not null,
maker_id integer, make_time timestamp, happen_time timestamp, approver_id integer,approve_time timestamp, seller_id integer,getter_id integer,make_brief text,approve_brief text, submit_time timestamp,shop_id integer,red_sheet_id integer,is_imported boolean) partition by range(happen_time);
CREATE INDEX idx_sheet_sale_order_main_company_id ON sheet_sale_order_main(company_id);
CREATE INDEX idx_sheet_sale_order_main_sheet_id ON sheet_sale_order_main(sheet_id);
CREATE INDEX idx_sheet_sale_order_main_happen_time ON sheet_sale_order_main(happen_time);
CREATE INDEX idx_sheet_sale_order_main_approve_time ON sheet_sale_order_main(approve_time);
create index idx_sheet_sale_order_main_make_time on sheet_sale_order_main(make_time);
CREATE INDEX idx_sheet_sale_order_main_red_flag ON sheet_sale_order_main(red_flag); 
CREATE INDEX idx_sheet_sale_order_main_department_id ON sheet_sale_order_main(department_id);

create table sheet_sale_order_main_10012006 partition of sheet_sale_order_main for values from ('2010-01-01') to ('2020-06-30');
create table sheet_sale_order_main_20062007 partition of sheet_sale_order_main for values from ('2020-06-30') to ('2020-07-01');
create table sheet_sale_order_main_20072102 partition of sheet_sale_order_main for values from ('2020-07-01') to ('2021-03-01');
create table sheet_sale_order_main_21022106 partition of sheet_sale_order_main for values from ('2021-03-01') to ('2021-06-01');

CREATE TABLE sheet_sale_order_detail(company_id integer NOT NULL,flow_id serial NOT NULL,inout_flag int2,sheet_id integer,row_index int,item_id integer not null,sheet_item_name text,branch_id integer, batch_id integer,unit_no text,unit_factor float4,quantity float4,orig_price float4,real_price float4,cost_price_prop float4,cost_price_recent float4,
  sub_amount float8,virtual_produce_date text, combine_flag text, tax_amount float4,happen_time timestamp, remark text, attr_qty jsonb,other_info jsonb,disp_month_id int4,disp_flow_id int4,disp_sheet_id int4) partition by range(happen_time);

CREATE INDEX idx_sheet_sale_order_detail_company_id ON sheet_sale_order_detail(company_id);/*非常重要,如果没有的话left join 大数据量很慢*/
CREATE INDEX idx_sheet_sale_order_detail_sheet_id ON sheet_sale_order_detail(sheet_id);
CREATE INDEX idx_sheet_sale_order_detail_item_id ON sheet_sale_order_detail(item_id);



create table sheet_sale_order_detail_10012006 partition of sheet_sale_order_detail for values from ('2010-01-01') to ('2020-06-30');
create table sheet_sale_order_detail_20062007 partition of sheet_sale_order_detail for values from ('2020-06-30') to ('2020-07-01');
create table sheet_sale_order_detail_20072102 partition of sheet_sale_order_detail for values from ('2020-07-01') to ('2021-03-01');
create table sheet_sale_order_detail_21032106 partition of sheet_sale_order_detail for values from ('2021-03-01') to ('2021-06-01');



CREATE TABLE sheet_buy_order_main (
  company_id int4 NOT NULL,
  sheet_id serial,
  sheet_no text,
  sheet_type text,
  money_inout_flag int2,
  branch_id int4,
  department_id integer,
  red_flag int2,
  red_sheet_id int4,
  red_sheet_date timestamp,
  supcust_id int4,
  total_amount float8 NOT NULL,
  tax_amount float4,
  discount_info text,
  payway1_id int4,
  payway1_amount float8,
  payway2_id int4,
  payway2_amount float8,
  payway3_id integer,payway3_amount float8, 
  now_pay_amount float8 NOT NULL,
  now_disc_amount float4 NOT NULL DEFAULT 0,
  paid_amount float8 NOT NULL,
  disc_amount float4 NOT NULL DEFAULT 0,
  maker_id int4,
  make_time timestamp,
  happen_time timestamp,
  approver_id int4,
  approve_time timestamp,
  seller_id int4,
  getter_id integer,
  make_brief text,
  approve_brief text,
  submit_time timestamp,
  prepay_amount float8,
  sheet_attribute jsonb,
  total_quantity text,
  receivers_id text,
  receivers_name text,
  is_imported boolean
)
PARTITION BY RANGE (
  happen_time
)

CREATE INDEX idx_sheet_buy_order_main_company_id ON sheet_buy_order_main(company_id);
CREATE INDEX idx_sheet_buy_order_mainn_sheet_id ON sheet_buy_order_main(sheet_id);
CREATE INDEX idx_sheet_buy_order_main_happen_time ON sheet_buy_order_main(happen_time);
CREATE INDEX idx_sheet_buy_order_main_approve_time ON sheet_buy_order_main(approve_time);
CREATE INDEX idx_sheet_buy_order_main_red_flag ON sheet_buy_order_main(red_flag);
CREATE INDEX idx_sheet_buy_order_main_supcust_id ON sheet_buy_order_main(supcust_id);
CREATE INDEX idx_sheet_buy_order_main_seller_id ON sheet_buy_order_main(seller_id);
CREATE INDEX idx_sheet_buy_order_main_department_id ON sheet_buy_order_main(department_id);

create table sheet_buy_order_main_22012301 partition of sheet_buy_order_main for values from ('2022-01-01') to ('2023-01-01');


CREATE TABLE sheet_buy_order_detail(company_id integer NOT NULL,flow_id serial NOT NULL,inout_flag int2,sheet_id integer,row_index int,item_id integer not null,sheet_item_name text,branch_id integer, batch_id integer,unit_no text,unit_factor float4,quantity float4, sys_price float4,orig_price float4,real_price float4,cost_price_prop float4,
cost_price_avg float4, sub_amount float8,produce_date text, combine_flag text, tax_amount float4,happen_time timestamp, remark text,remark_id integer, attr_qty jsonb,other_info jsonb,disp_month_id int4,disp_dlow_id int4,disp_sheet_id int4) partition by range(happen_time);


CREATE INDEX idx_sheet_buy_order_detail_company_id ON sheet_buy_order_detail(company_id); 
CREATE INDEX idx_sheet_buy_order_detail_sheet_id ON sheet_buy_order_detail(sheet_id);
CREATE INDEX idx_sheet_buy_order_detail_item_id ON sheet_buy_order_detail(item_id);
CREATE INDEX idx_sheet_buy_order_detail_happen_time ON sheet_buy_order_detail(happen_time);


create table sheet_buy_order_detail_22012301 partition of sheet_buy_order_detail for values from ('2022-01-01') to ('2023-01-01');



create table current_account(company_id integer not null,supcust_id integer,happen_time timestamp,sheet_type text,arrears_bal float,prepay_sub_code integer,prepay_bal float);
 
CREATE TABLE sheet_sale_main(company_id integer NOT NULL,sheet_id serial,sheet_no text NOT NULL, order_sheet_id integer,order_sheet_date date,visit_id integer,sheet_type text,money_inout_flag smallint, branch_id integer,department_id integer,receive_addr integer, red_flag smallint,red_sheet_id integer, red_sheet_date date,supcust_id integer,shop_id integer,total_amount float8 NOT NULL, is_retail boolean,tax_amount float4,discount_info text,order_source text,
payway1_id integer,payway1_amount float8,payway2_id integer,payway2_amount float8,payway3_id integer,payway3_amount float8, now_pay_amount float8 NOT NULL,
now_disc_amount float4 DEFAULT 0 NOT NULL,paid_amount float8 NOT NULL,disc_amount float4 DEFAULT 0 NOT NULL,prepay_amount float4,   
maker_id integer, make_time timestamp, happen_time timestamp, approver_id integer,approve_time timestamp, seller_id integer, getter_id integer, make_brief text,approve_brief text, submit_time timestamp,settle_time timestamp,visit_id int4,senders_id text,senders_name text,sheet_attribute jsonb,is_imported boolean) partition by range(happen_time);

CREATE INDEX idx_sheet_sale_main_company_id ON sheet_sale_main(company_id);
CREATE INDEX idx_sheet_sale_main_sheet_id ON sheet_sale_main(sheet_id);
CREATE INDEX idx_sheet_sale_main_happen_time ON sheet_sale_main(happen_time);
CREATE INDEX idx_sheet_sale_main_approve_time ON sheet_sale_main(approve_time);
create index idx_sheet_sale_main_make_time on sheet_sale_main(make_time);

create index idx_sheet_sale_main_make_time on sheet_sale_main(make_time);
CREATE INDEX idx_sheet_sale_main_red_flag ON sheet_sale_main(red_flag);
CREATE INDEX idx_sheet_sale_main_supcust_id ON sheet_sale_main(supcust_id);
CREATE INDEX idx_sheet_sale_main_seller_id ON sheet_sale_main(seller_id);
CREATE INDEX idx_sheet_sale_main_order_sheet_id ON sheet_sale_main(order_sheet_id);
CREATE INDEX idx_sheet_sale_main_settle_time ON sheet_sale_main(settle_time); 
CREATE INDEX idx_sheet_sale_main_department_id ON sheet_sale_main(department_id);

create table sheet_sale_main_10012007 partition of sheet_sale_main for values from ('2010-01-01') to ('2020-07-01');
create table sheet_sale_main_20072102 partition of sheet_sale_main for values from ('2020-07-01') to ('2021-03-01');
create table sheet_sale_main_21032106 partition of sheet_sale_main for values from ('2021-03-01') to ('2021-06-01');
 
CREATE TABLE sheet_log (flow_id serial, company_id int4, happen_time timestamp, oper_id int4, sheet_id int8, sheet_type text, action text,sheet_info jsonb) PARTITION BY range(happen_time);
CREATE TABLE sheet_log_23072312 PARTITION OF sheet_log FOR VALUES FROM ('2023-07-01') TO ('2023-12-01');

CREATE TABLE sheet_sale_detail(company_id int4 NOT NULL,flow_id serial NOT NULL,inout_flag int2,sheet_id integer,row_index int4, item_id integer not null,sheet_item_name text,branch_id int4, batch_id integer,unit_no text,unit_factor float4,quantity float4,orig_price float4,real_price float4,last_time_price float4,cost_price_avg float4,cost_price_prop float4,cost_price_recent float4,
 sub_amount float8,combine_flag text, tax_amount float4,virtual_produce_date text,happen_time timestamp, remark text,cost_price_suspect boolean,order_sub_id integer,remark_id integer,sys_price float4,cost_price_buy float4,trade_type text,attr_qty jsonb,other_info jsonb,disp_month_id int4,disp_flow_id int4,disp_sheet_id int4,sn_code text) partition by range(happen_time);
CREATE INDEX idx_sheet_sale_detail_company_id ON sheet_sale_detail(company_id);/*非常重要,如果没有的话left join 大数据量很慢，销售按商品汇总表查询巨慢*/
CREATE INDEX idx_sheet_sale_detail_sheet_id ON sheet_sale_detail(sheet_id);
CREATE INDEX idx_sheet_sale_detail_item_id ON sheet_sale_detail(item_id);
CREATE INDEX idx_sheet_sale_detail_happen_time ON sheet_sale_detail(happen_time);
CREATE INDEX idx_sheet_sale_detail_disp_sheet_id ON sheet_sale_detail(disp_sheet_id);

create table sheet_sale_detail_10012006 partition of sheet_sale_detail for values from ('2010-01-01') to ('2020-06-30');
create table sheet_sale_detail_200630_200701 partition of sheet_sale_detail for values from ('2020-06-30') to ('2020-07-01');

create table sheet_sale_detail_20072102 partition of sheet_sale_detail for values from ('2020-07-01') to ('2021-03-01');
create table sheet_sale_detail_21032106 partition of sheet_sale_detail for values from ('2021-03-01') to ('2021-06-01');


CREATE TABLE sheet_combine_main
(
company_id integer NOT NULL,
sheet_id serial,
sheet_type text not null,
sheet_no text NOT NULL,
red_flag smallint,
red_sheet_id integer,
sub_other_fee1_id integer,
sub_other_fee2_id integer,
sub_other_fee3_id integer,
sub_other_fee1 float4,
sub_other_fee2 float4,
sub_other_fee3 float4,
apportionment_way text,
seller_id integer,
from_branch_id integer,
to_branch_id integer,
maker_id integer,
make_time timestamp,
happen_time timestamp,
approver_id integer,
approve_time timestamp,
make_brief text,
approve_brief text,
submit_time timestamp,
sheet_attribute jsonb
)partition by range(happen_time);
create table sheet_combine_main_23082312 partition of sheet_combine_main for values from ('2023-08-01') to ('2023-12-01');

CREATE TABLE sheet_combine_detail
(
company_id int4 NOT NULL,
flow_id serial NOT NULL,
sheet_id integer,
inout_flag smallint,
row_index smallint,
item_id integer not null,
branch_id int4,
batch_id int4, 
unit_no text,
unit_factor float4,
quantity float4,
cost_price_avg float4,
cost_price_spec float4,
buy_price float4,
cost_price float4,
cost_amount float4,
happen_time timestamp,
remark text
)partition by range(happen_time);
create table sheet_combine_detail_23082102 partition of sheet_combine_detail for values from ('2023-08-01') to ('2023-12-01');

CREATE TABLE combine_template_main
(
company_id integer NOT NULL,
sheet_id serial,
model_name text,
maker_id integer,
happen_time timestamp,
make_brief text,
constraint combine_template_name
unique (company_id,model_name)

);
CREATE TABLE combine_template_detail
(
company_id integer NOT NULL,
flow_id serial NOT NULL,
sheet_id integer,
inout_flag smallint,
row_index smallint,
item_id integer not null,
unit_no text,
unit_factor float4,
quantity float4,
happen_time timestamp,
remark text
);

CREATE TABLE sheet_buy_main(company_id integer NOT NULL,sheet_id serial,sheet_no text NOT NULL, order_sheet_id integer,order_sheet_date date,sheet_type text,money_inout_flag smallint, branch_id integer,department_id integer, red_flag smallint,red_sheet_id integer, red_sheet_date date,supcust_id integer,total_amount float8 NOT NULL, is_retail boolean,tax_amount float4,discount_info text,
payway1_id integer,payway1_amount float8,payway2_id integer,payway2_amount float8,payway3_id integer,payway3_amount float8,  now_pay_amount float8 NOT NULL,
now_disc_amount float4 DEFAULT 0 NOT NULL,paid_amount float8 NOT NULL,disc_amount float4 DEFAULT 0 NOT NULL,   
maker_id integer, make_time timestamp, happen_time timestamp, approver_id integer,approve_time timestamp, seller_id integer,getter_id integer,make_brief text,approve_brief text, submit_time timestamp,prepay_amount float,is_imported boolean) partition by range(happen_time);



create table sheet_buy_main_20072102 partition of sheet_buy_main for values from ('2020-07-01') to ('2021-03-01');
create table sheet_buy_main_21032106 partition of sheet_buy_main for values from ('2021-03-01') to ('2021-06-01');

CREATE INDEX idx_sheet_buy_main_company_id ON sheet_buy_main(company_id);
CREATE INDEX idx_sheet_buy_main_sheet_id ON sheet_buy_main(sheet_id);
CREATE INDEX idx_sheet_buy_main_happen_time ON sheet_buy_main(happen_time);
CREATE INDEX idx_sheet_buy_main_approve_time ON sheet_buy_main(approve_time);
CREATE INDEX idx_sheet_buy_main_red_flag ON sheet_buy_main(red_flag);
CREATE INDEX idx_sheet_buy_main_supcust_id ON sheet_buy_main(supcust_id);
CREATE INDEX idx_sheet_buy_main_seller_id ON sheet_buy_main(seller_id);
CREATE INDEX idx_sheet_buy_main_department_id ON sheet_buy_main(department_id); 

CREATE TABLE sheet_buy_detail(company_id int4 NOT NULL,flow_id serial NOT NULL,inout_flag int2,sheet_id integer,row_index int, item_id integer not null,sheet_item_name text,branch_id int, batch_id integer,unit_no text,unit_factor float4,quantity float4,orig_price float4,real_price float4,cost_price_prop float4,
cost_price_avg float4, sub_amount float8,combine_flag text, tax_amount float4,happen_time timestamp, remark text,attr_qty jsonb,sn_code text) partition by range(happen_time);

create table sheet_buy_detail_20072102 partition of sheet_buy_detail for values from ('2020-07-01') to ('2021-03-01');
create table sheet_buy_detail_21032106 partition of sheet_buy_detail for values from ('2021-03-01') to ('2021-06-01');

CREATE INDEX idx_sheet_buy_detail_company_id ON sheet_buy_detail(company_id);
CREATE INDEX idx_sheet_buy_detail_sheet_id ON sheet_buy_detail(sheet_id);
CREATE INDEX idx_sheet_buy_detail_item_id ON sheet_buy_detail(item_id);
 

CREATE TABLE sheet_move_main(company_id integer NOT NULL,sheet_id serial,sheet_no text NOT NULL, order_sheet_id integer,red_flag smallint,red_sheet_id integer,seller_id integer, receiver_id int4,sender_id integer,from_branch_id integer,to_branch_id integer,contract_amount numeric(10,2), wholesale_amount float8,buy_amount float8,cost_amount_avg float8,cost_amount_prop float8, 
 maker_id integer, make_time timestamp, happen_time timestamp, approver_id integer,approve_time timestamp, make_brief text,approve_brief text, submit_time timestamp,sheet_attribute jsonb,is_imported boolean) partition by range(happen_time);
 
create table sheet_move_main_20072102 partition of sheet_move_main for values from ('2020-07-01') to ('2021-03-01');
create table sheet_move_main_21032106 partition of sheet_move_main for values from ('2021-03-01') to ('2021-06-01');

CREATE TABLE sheet_move_detail(company_id int4 NOT NULL,flow_id serial NOT NULL,inout_flag smallint,sheet_id integer,row_index smallint,  item_id integer not null,sheet_item_name text,branch_id integer, batch_id integer,unit_no text,unit_factor float4,quantity float4,cost_price_avg float4,cost_price_prop float4, buy_price float4,wholesale_price float4,contract_price numeric(10,2),
 happen_time timestamp, remark text) partition by range(happen_time);

create table sheet_move_detail_20072102 partition of sheet_move_detail for values from ('2020-07-01') to ('2021-03-01');
create table sheet_move_detail_21032106 partition of sheet_move_detail for values from ('2021-03-01') to ('2021-06-01');

CREATE INDEX idx_sheet_move_detail_sheet_id ON sheet_move_detail(sheet_id);
CREATE INDEX idx_sheet_move_detail_item_id ON sheet_move_detail(item_id);


CREATE TABLE sheet_combine_main(company_id integer NOT NULL,sheet_id serial,sheet_type text not null,sheet_no text NOT NULL, red_flag smallint,red_sheet_id integer,seller_id integer, from_branch_id integer,to_branch_id integer,wholesale_amount float8,buy_amount float8,cost_amount_avg float8,cost_amount_prop float8, 
 maker_id integer, make_time timestamp, happen_time timestamp, approver_id integer,approve_time timestamp, make_brief text,approve_brief text, submit_time timestamp,sheet_attribute jsonb) partition by range(happen_time);

create table sheet_combine_main_213032304 partition of sheet_combine_main for values from ('2023-03-01') to ('2023-04-01');

CREATE TABLE sheet_combine_detail(company_id int4 NOT NULL,flow_id serial NOT NULL,sheet_id integer,inout_flag smallint,row_index smallint,item_id integer not null,sheet_item_name text,branch_id integer, batch_id integer,unit_no text,unit_factor float4,quantity float4,cost_price_avg float4,cost_price_prop float4, buy_price float4,wholesale_price float4,
 happen_time timestamp, remark text) partition by range(happen_time);

CREATE INDEX idx_sheet_combine_detail_sheet_id ON sheet_combine_detail(sheet_id);
CREATE INDEX idx_sheet_combine_detail_item_id ON sheet_combine_detail(item_id);
 
create table sheet_combine_detail_213032304 partition of sheet_combine_detail for values from ('2023-03-01') to ('2023-04-01');



CREATE TABLE sheet_invent_change_main(company_id integer NOT NULL,sheet_id serial,sheet_no text NOT NULL, order_sheet_id integer,red_sheet_id integer,sheet_type text, money_inout_flag smallint, branch_id integer,seller_id integer,red_flag smallint, wholesale_amount float8, cost_amount_avg float8, cost_amount_prop float8, buy_amount float8, 
 maker_id integer, make_time timestamp, happen_time timestamp, approver_id integer,approve_time timestamp, make_brief text,approve_brief text, submit_time timestamp,inventory_sheet_id integer) partition by range(happen_time);

create table sheet_invent_change_main_20072102 partition of sheet_invent_change_main for values from ('2020-07-01') to ('2021-03-01');
create table sheet_invent_change_main_21032106 partition of sheet_invent_change_main for values from ('2021-03-01') to ('2021-06-01');

CREATE INDEX idx_sheet_invent_change_main_company_id ON sheet_invent_change_main(company_id);
CREATE INDEX idx_sheet_invent_change_main_sheet_id ON sheet_invent_change_main(sheet_id);
CREATE INDEX idx_sheet_invent_change_main_happen_time ON sheet_invent_change_main(happen_time);
CREATE INDEX idx_sheet_invent_change_main_approve_time ON sheet_invent_change_main(approve_time);
CREATE INDEX idx_sheet_invent_change_red_flag ON sheet_invent_change_main(red_flag);


CREATE TABLE sheet_invent_change_detail(company_id int4 NOT NULL,flow_id serial NOT NULL,inout_flag smallint,sheet_id integer,row_index smallint,  item_id integer not null,sheet_item_name text,branch_id integer, batch_id integer,unit_no text,unit_factor float4,quantity float4,cost_price_avg float4,cost_price_prop float4,cost_price_buy float4,wholesale_price float4,
 happen_time timestamp, remark text) partition by range(happen_time);

create table sheet_invent_change_detail_20072102 partition of sheet_invent_change_detail for values from ('2020-07-01') to ('2021-03-01');
create table sheet_invent_change_detail_21032106 partition of sheet_invent_change_detail for values from ('2021-03-01') to ('2021-06-01');

CREATE INDEX idx_sheet_invent_change_detail_company_id ON sheet_invent_change_detail(company_id);
CREATE INDEX idx_sheet_invent_change_detail_sheet_id ON sheet_invent_change_detail(sheet_id);
CREATE INDEX idx_sheet_invent_change_detail_happen_time ON sheet_invent_change_detail(happen_time);
CREATE INDEX idx_sheet_invent_change_detail_item_id ON sheet_invent_change_detail(item_id);

CREATE TABLE sheet_inventory_main(company_id integer NOT NULL,sheet_id serial,sheet_no text NOT NULL, red_sheet_id integer,sheet_type text, money_inout_flag smallint, branch_id integer,seller_id integer,red_flag smallint, wholesale_amount float8, cost_amount_avg float8, cost_amount_prop float8, buy_amount float8, 
 maker_id integer, make_time timestamp, happen_time timestamp, approver_id integer,approve_time timestamp, make_brief text,approve_brief text, sheet_attribute jsonb, submit_time timestamp) partition by range(happen_time);

create table sheet_inventory_main_20072102 partition of sheet_inventory_main for values from ('2020-07-01') to ('2021-03-01');
create table sheet_inventory_main_21032106 partition of sheet_inventory_main for values from ('2021-03-01') to ('2021-06-01');

CREATE TABLE sheet_inventory_detail(company_id int4 NOT NULL,flow_id serial NOT NULL,inout_flag smallint,sheet_id integer,row_index smallint, item_id integer not null,sheet_item_name text,branch_id integer, batch_id integer,unit_no text,unit_factor float4, sys_quantity float4, real_quantity float4,cost_price_avg float4,cost_price_prop float4,buy_price float4,wholesale_price float4,
cost_price float4, happen_time timestamp, remark text) partition by range(happen_time);

create table sheet_inventory_detail_20072102 partition of sheet_inventory_detail for values from ('2020-07-01') to ('2021-03-01');
create table sheet_inventory_detail_21032106 partition of sheet_inventory_detail for values from ('2021-03-01') to ('2021-06-01');


create table sheet_get_arrears_main(company_id int4 not null,visit_id integer,sheet_id serial not null,sheet_no text,sheet_type text, supcust_id integer,money_inout_flag smallint,sheet_amount float8,disc_amount float4, paid_amount float,left_amount float, approve_flag smallint,red_flag smallint,red_sheet_id integer,maker_id integer,make_time timestamp,approver_id integer,approve_time timestamp,happen_time timestamp,getter_id integer,payway1_id integer,payway1_amount float8,payway2_id integer,payway2_amount float8,payway3_id integer,payway3_amount float8,make_brief text,submit_time timestamp,now_pay_amount float8,now_disc_amount float8,approve_brief text,is_imported boolean,sheet_attribute jsonb) partition by range(happen_time);

create table sheet_get_arrears_main_10012007 partition of sheet_get_arrears_main for values from ('2010-01-01') to ('2020-07-01');
create table sheet_get_arrears_main_20072102 partition of sheet_get_arrears_main for values from ('2020-07-01') to ('2021-03-01');
create table sheet_get_arrears_main_21032106 partition of sheet_get_arrears_main for values from ('2021-03-01') to ('2021-06-01');

CREATE INDEX idx_sheet_get_arrears_main_company_id ON sheet_get_arrears_main(company_id);
CREATE INDEX idx_sheet_get_arrears_main_sheet_id ON sheet_get_arrears_main(sheet_id);
CREATE INDEX idx_sheet_get_arrears_main_happen_time ON sheet_get_arrears_main(happen_time);
CREATE INDEX idx_sheet_get_arrears_main_approve_time ON sheet_get_arrears_main(approve_time);
CREATE INDEX idx_sheet_get_arrears_main_red_flag ON sheet_get_arrears_main(red_flag);
CREATE INDEX idx_sheet_get_arrears_main_supcust_id ON sheet_get_arrears_main(supcust_id);
CREATE INDEX idx_sheet_get_arrears_main_geter_id ON sheet_get_arrears_main(seller_id);



create table sheet_get_arrears_detail(company_id int4 not null,sheet_id integer, mm_sheet_id integer, inout_flag integer,row_index integer,sheet_amount float,paid_amount numeric(14,4),left_amount numeric(14,4),disc_amount numeric(14,4), now_pay_amount numeric(14,4),now_disc_amount numeric(14,4), happen_time timestamp,remark text) partition by range(happen_time);

CREATE INDEX idx_sheet_get_arrears_detail_company_id ON sheet_get_arrears_detail(company_id);
CREATE INDEX idx_sheet_get_arrears_detail_sheet_id ON sheet_get_arrears_detail(sheet_id);
CREATE INDEX idx_sheet_get_arrears_detail_mm_sheet_id ON sheet_get_arrears_detail(mm_sheet_id);
CREATE INDEX idx_sheet_get_arrears_detail_happen_time ON sheet_get_arrears_detail(happen_time);
 
 

create table sheet_get_arrears_detail_10012007 partition of sheet_get_arrears_detail for values from ('2010-01-01') to ('2020-07-01');
create table sheet_get_arrears_detail_20072102 partition of sheet_get_arrears_detail for values from ('2020-07-01') to ('2021-03-01');
create table sheet_get_arrears_detail_21032106 partition of sheet_get_arrears_detail for values from ('2021-03-01') to ('2021-06-01');


CREATE TABLE sheet_fee_out_main(company_id integer NOT NULL,visit_id integer,sheet_id serial,sheet_no text NOT NULL, sheet_type text,money_inout_flag smallint, branch_id integer, red_flag smallint,red_sheet_id integer, red_sheet_date date,supcust_id integer,shop_id integer,total_amount float8 NOT NULL,
payway1_id integer,payway1_amount float8,payway2_id integer,payway2_amount float8,payway3_id integer,payway3_amount float8, now_pay_amount float8 NOT NULL,now_disc_amount numeric(14,4),
getter_id integer,maker_id integer, make_time timestamp, happen_time timestamp, approver_id integer,approve_time timestamp, make_brief text,approve_brief text, submit_time timestamp) partition by range(happen_time);

create table sheet_fee_out_main_16072007 partition of sheet_fee_out_main for values from ('2016-07-01') to ('2020-07-01');
create table sheet_fee_out_main_20072102 partition of sheet_fee_out_main for values from ('2020-07-01') to ('2021-03-01');
create table sheet_fee_out_main_21032106 partition of sheet_fee_out_main for values from ('2021-03-01') to ('2021-06-01');


create table sheet_fee_out_detail(company_id int4,sheet_id integer, fee_sub_id integer, supcust_id integer, inout_flag integer,row_index integer,fee_sub_amount float,now_pay_amount numeric(14,4),now_disc_amount numeric(14,4),happen_time timestamp,remark text) partition by range(happen_time);

create table sheet_fee_out_detail_16072007 partition of sheet_fee_out_detail for values from ('2016-07-01') to ('2020-07-01');
create table sheet_fee_out_detail_20072102 partition of sheet_fee_out_detail for values from ('2020-07-01') to ('2021-03-01');
create table sheet_fee_out_detail_21032106 partition of sheet_fee_out_detail for values from ('2021-03-01') to ('2021-06-01');

CREATE INDEX idx_cw_subject_company_id ON cw_subject(company_id);

CREATE INDEX idx_sheet_fee_out_main_company_id ON sheet_fee_out_main(company_id);
CREATE INDEX idx_sheet_fee_out_main_sheet_id ON sheet_fee_out_main(sheet_id);
CREATE INDEX idx_sheet_fee_out_main_happen_time ON sheet_fee_out_main(happen_time);
CREATE INDEX idx_sheet_fee_out_main_approve_time ON sheet_fee_out_main(approve_time);

 

CREATE INDEX idx_sheet_fee_out_main_supcust_id ON sheet_fee_out_main(supcust_id);
CREATE INDEX idx_sheet_fee_out_main_getter_id ON sheet_fee_out_main(getter_id);


CREATE INDEX idx_sheet_fee_out_detail_company_id ON sheet_fee_out_detail(company_id);
CREATE INDEX idx_sheet_fee_out_detail_sheet_id ON sheet_fee_out_detail(sheet_id);
CREATE INDEX idx_sheet_fee_out_detail_fee_sub_id ON sheet_fee_out_detail(fee_sub_id);

create table sheet_visit(company_id integer not null,visit_id serial not null,seller_id integer,supcust_id integer not null,shop_id integer,start_time timestamp,end_time timestamp,door_picture text,showcase_pictures jsonb,longitude float4,latitude float4,sign_distance int4, have_trade boolean,visit_region text,sale_amount float4,order_amount float4,work_content jsonb,remark text) partition by range(start_time);
create table sheet_visit_20112105 partition of sheet_visit for values from ('2020-11-01') to ('2021-06-01');

CREATE INDEX idx_sheet_visit_company_id ON sheet_visit(company_id);
CREATE INDEX idx_sheet_visit_visit_id ON sheet_visit(visit_id);
CREATE INDEX idx_sheet_visit_start_time ON sheet_visit(start_time);
CREATE INDEX idx_sheet_visit_supcust_id ON sheet_visit(supcust_id);
CREATE INDEX idx_sheet_visit_shop_id ON sheet_visit(shop_id);
CREATE INDEX idx_sheet_visit_seller_id ON sheet_visit(seller_id);

CREATE TABLE info_visit_template_mapping (company_id int NOT NULL,mapping_id serial PRIMARY KEY,  sup_group int, sup_rank int, dept_id int, sellers_id text, visit_tmp_id int, display_tmp_id int, equipment_tmp_id int, order_index int);
CREATE TABLE info_visit_template (company_id int NOT NULL, template_id serial PRIMARY KEY,  template_name text, actions jsonb);

create table seller_trail(company_id integer not null,flow_id serial not null,seller_id integer,longitude float8,latitude float8,happen_time timestamp) partition by range(happen_time);
create table seller_trail_20112105 partition of seller_trail for values from ('2020-11-01') to ('2021-06-01');
create INDEX idx_seller_trail_company_id ON seller_trail(company_id);
create INDEX idx_seller_trail_happen_time ON seller_trail(happen_time);

create table sheet_prepay(company_id integer not null,visit_id integer,sheet_id serial not null,red_sheet_id integer,sheet_no text,sheet_type text,supcust_id integer,getter_id integer,total_amount float,prepay_sub_id integer, approve_flag smallint,red_flag smallint,order_sheet_id integer, maker_id integer,make_time timestamp,approver_id integer,approve_time timestamp,happen_time timestamp,payway1_id integer,payway1_amount float8,payway2_id integer,payway2_amount float8,payway3_id integer,payway3_amount float8,make_brief text,submit_time timestamp,disc_amount float,money_inout_flag int2,approve_brief text,now_pay_amount numeric,now_disc_amount numeric,paid_amount numeric,order_adjust_sheet_id integer,is_imported boolean) partition by range(happen_time);

create table sheet_prepay_18011901 partition of sheet_prepay for values from ('2018-01-01') to ('2019-01-01');
create table sheet_prepay_19012001 partition of sheet_prepay for values from ('2019-01-01') to ('2020-01-01');
create table sheet_prepay_20012006 partition of sheet_prepay for values from ('2020-01-01') to ('2020-07-01');
create table sheet_prepay_20072102 partition of sheet_prepay for values from ('2020-07-01') to ('2021-03-01');
create table sheet_prepay_21032106 partition of sheet_prepay for values from ('2021-03-01') to ('2021-06-01');

CREATE INDEX idx_sheet_prepay_company_id ON sheet_prepay(company_id);
CREATE INDEX idx_sheet_prepay_sheet_id ON sheet_prepay(sheet_id);
CREATE INDEX idx_sheet_prepay_happen_time ON sheet_prepay(happen_time);
CREATE INDEX idx_sheet_prepay_approve_time ON sheet_prepay(approve_time);
 CREATE INDEX idx_sheet_prepay_make_time ON sheet_prepay(make_time);
CREATE INDEX idx_sheet_prepay_red_flag ON sheet_prepay(red_flag);

CREATE INDEX idx_sheet_prepay_supcust_id ON sheet_prepay(supcust_id);
CREATE INDEX idx_sheet_prepay_getter_id ON sheet_prepay(getter_id);

CREATE INDEX idx_sheet_prepay_prepay_sub_id ON sheet_prepay(prepay_sub_id);


create table items_ordered_balance(company_id integer not null,flow_id serial,supcust_id integer,item_id INTEGER,unit_no text,unit_factor integer,quantity numeric(24,6),order_price float4,balance numeric(24,4),prepay_sub_id integer not null,init_quantity float8,init_time timestamp,constraint pk_items_ordered_balance primary key(company_id,supcust_id,item_id,unit_no,prepay_sub_id,order_price));


create TABLE items_ordered_change(company_id integer not null,flow_id serial PRIMARY key,prepay_sub_id integer,supcust_id integer,item_id integer,unit_no text,old_quantity numeric(24,4),now_quantity numeric(24,4),real_price numeric,
old_sub_amount numeric,now_sub_amount numeric,happen_time timestamp,oper_type text,red_flag int2,sheet_id integer );


CREATE TABLE sheet_item_ordered_adjust_main(company_id integer NOT NULL,sheet_id serial,sheet_no text NOT NULL,red_sheet_id integer,sheet_type text,money_inout_flag smallint,supcust_id integer,prepay_sub_id integer,seller_id integer,red_flag smallint, total_amount numeric, prepay_balance numeric,now_pay_amount numeric,now_disc_amount numeric,
 maker_id integer, make_time timestamp, happen_time timestamp, approver_id integer,approve_time timestamp, make_brief text,approve_brief text, submit_time timestamp,payway1_id integer,payway1_amount numeric,payway2_id integer,payway2_amount numeric) partition by range(happen_time);

create table sheet_item_ordered_adjust_main_21032105 partition of sheet_item_ordered_adjust_main for values from ('2021-03-01') to ('2021-06-01');
create table sheet_item_ordered_adjust_main_21062108 partition of sheet_item_ordered_adjust_main for values from ('2021-06-01') to ('2021-09-01');



CREATE TABLE sheet_item_ordered_adjust_detail(company_id int4 NOT NULL,flow_id serial NOT NULL ,inout_flag smallint,sheet_id integer,row_index smallint,item_id integer not null,unit_no text,unit_factor float4,quantity float4,real_price numeric,sub_amount numeric,happen_time timestamp, remark text) partition by range(happen_time);

create table sheet_item_ordered_adjust_detail_21032105 partition of sheet_item_ordered_adjust_detail for values from ('2021-03-01') to ('2021-06-01');
create table sheet_item_ordered_adjust_detail_21062108 partition of sheet_item_ordered_adjust_detail for values from ('2021-06-01') to ('2021-09-01');



CREATE TABLE sheet_order_item_detail(company_id integer NOT NULL,flow_id serial NOT NULL,inout_flag int2,sheet_id integer,row_index int,item_id integer not null,sheet_item_name text,branch_id integer, batch_id integer,unit_no text,unit_factor float4,quantity float4,orig_price float4,real_price float4,cost_price_prop float4,
cost_price float4, sub_amount float8,produce_date text, combine_flag text, tax_amount float4,happen_time timestamp, remark text) partition by range(happen_time) ;

create table sheet_order_item_detail_19012001 partition of sheet_order_item_detail for values from ('2019-01-01') to ('2020-01-01');
create table sheet_order_item_detail_20012005 partition of sheet_order_item_detail for values from ('2020-01-01') to ('2020-06-01');
create table sheet_order_item_detail_20062102 partition of sheet_order_item_detail for values from ('2020-06-01') to ('2021-03-01');
create table sheet_order_item_detail_21032105 partition of sheet_order_item_detail for values from ('2021-03-01') to ('2021-06-01');
create table sheet_order_item_detail_21062109 partition of sheet_order_item_detail for values from ('2021-06-01') to ('2021-09-01');



create table sheet_check_sheets_main(company_id integer not null,sheet_id serial not null,getter_id integer not null,happen_time timestamp,maker_id integer,make_time timestamp,approve_id integer,approve_time timestamp,make_brief text,red_flag int2,sale_amount float,return_amount float,get_arrears float,get_prepay float,fee_out float,start_time timestamp,end_time timestamp);
CREATE INDEX idx_sheet_check_sheets_main_sheet_id ON sheet_check_sheets_main(sheet_id);
CREATE INDEX idx_sheet_check_sheets_main_company_id ON sheet_check_sheets_main(company_id);
CREATE INDEX idx_sheet_check_sheets_main_happen_time ON sheet_check_sheets_main(happen_time);
CREATE INDEX idx_sheet_check_sheets_main_approve_time ON sheet_sheet_check_sheets_main(approve_time);

create table sheet_check_sheets_detail(company_id integer not null,sheet_id integer,business_sheet_id integer,business_sheet_no text,business_sheet_type text);
 CREATE INDEX idx_sheet_check_sheets_detail_sheet_id ON sheet_check_sheets_detail(sheet_id);
 CREATE INDEX idx_sheet_check_sheets_detail_business_sheet_id ON sheet_check_sheets_detail(business_sheet_id);
  CREATE INDEX idx_sheet_check_sheets_detail_business_company_id ON sheet_check_sheets_detail(company_id);
create table sheet_check_sheet_payways(sheet_id integer,sub_id integer,amount float);

create table info_branch(company_id integer not null,branch_id serial,branch_name text,order_index int, ignore_batch boolean, branch_addr text,branch_tel text, py_str text,keepers text,need_keeper_confirm boolean,distinct_pos boolean,branch_type text,for_contract_seller boolean,constraint pk_info_branch PRIMARY KEY(company_id,branch_id));

create table info_item_class(company_id integer not null,class_id serial not null, class_name text,service_flag int,cls_type int,brand_id integer,mother_id integer,order_index int,cls_status text,py_str text, ignore_stock_class boolean,general_class text,batch_level int2,constraint pk_info_item_class primary key(company_id,class_id));
 
create table info_item_brand(company_id integer not null,brand_id serial not null,brand_name text, brand_status text,brand_order_index integer, remark text,constraint pk_info_item_brand primary key(company_id,brand_id));
              
create table info_item_prop(company_id integer not null,item_id serial not null,item_name text,item_alias text,item_no text,barcode text,item_spec text,shelf_life integer,origin_city text,simple_name text,service_flag boolean,item_class integer,item_brand integer, son_mum_item integer, son_options_id text, son_options_name text,mum_attributes jsonb, buy_price float8,wholesale_price float8,retail_price float8,min_sale_price float8,cost_change_qty float8,cost_price_avg float8,cost_amt float8, cost_price_recent float8,cost_price_spec float8,cost_type smallint, combine_sta text,status text ,display_flag text,barcode_style smallint,py_str text, py_str1 text, item_season integer, other_barcode  text, allow_disc boolean, vip_price1 float8 ,vip_price2 float8 ,valid_days int, valid_day_type text, other_class text, ignore_stock boolean, item_order_index int,  rpt_class text, create_time timestamp,update_time timestamp,update_photo_time timestamp,item_cost_price_suspect boolean,item_images jsonb,supplier_id integer,manufactor_id integer,batch_level int2) partition by hash(company_id);
                CREATE INDEX idx_info_item_prop_company_id ON info_item_prop(company_id);
                CREATE INDEX idx_info_item_prop_item_id ON info_item_prop(item_id);
                CREATE INDEX idx_info_item_prop_item_name ON info_item_prop USING gin (item_name gin_trgm_ops);
                CREATE INDEX idx_info_item_prop_py_str ON info_item_prop USING gin (py_str gin_trgm_ops);
                CREATE INDEX idx_info_item_prop_item_no ON info_item_prop USING gin (item_no gin_trgm_ops);
                CREATE INDEX idx_info_item_prop_barcode ON info_item_prop(barcode);
                CREATE INDEX idx_info_item_prop_other_class ON info_item_prop USING gin (other_class gin_trgm_ops);
                CREATE INDEX idx_info_item_prop_status ON info_item_prop(status);

 CREATE TABLE info_item_prop_0 PARTITION OF info_item_prop FOR VALUES WITH(MODULUS 4, REMAINDER 0);
 CREATE TABLE info_item_prop_1 PARTITION OF info_item_prop FOR VALUES WITH(MODULUS 4, REMAINDER 1);
 CREATE TABLE info_item_prop_2 PARTITION OF info_item_prop FOR VALUES WITH(MODULUS 4, REMAINDER 2);
 CREATE TABLE info_item_prop_3 PARTITION OF info_item_prop FOR VALUES WITH(MODULUS 4, REMAINDER 3); 
 
 CREATE TABLE public.info_item_batch (company_id int NOT NULL, batch_id serial, produce_date timestamp, batch_no text) partition by hash(company_id);
 CREATE INDEX idx_info_item_batch_company_id ON info_item_batch(company_id);
 CREATE INDEX idx_info_item_batch_batch_id ON info_item_batch(batch_id);
 CREATE UNIQUE INDEX idx_batch_uindex ON public.info_item_batch USING btree (company_id, batch_no, produce_date)

 CREATE TABLE info_item_batch_0 PARTITION OF info_item_batch FOR VALUES WITH(MODULUS 4, REMAINDER 0);
 CREATE TABLE info_item_batch_0 PARTITION OF info_item_batch FOR VALUES WITH(MODULUS 4, REMAINDER 1);
 CREATE TABLE info_item_batch_0 PARTITION OF info_item_batch FOR VALUES WITH(MODULUS 4, REMAINDER 2);
 CREATE TABLE info_item_batch_0 PARTITION OF info_item_batch FOR VALUES WITH(MODULUS 4, REMAINDER 3);
 

create table info_client_item_code (company_id integer,client_id integer,item_id integer,item_code text,constraint pk_info_client_item_code primary key(company_id,client_id,item_id))

create table info_item_unit(company_id integer not null,unit_no text,can_be_big_unit boolean,constraint pk_info_item_unit primary key(company_id,unit_no));
create table info_item_multi_unit(company_id integer not null,item_id integer,unit_no text,unit_factor float4,wholesale_price numeric(10,4),retail_price numeric(10,4),buy_price numeric(10,4),vip_price numeric(10,4),vip_price1 numeric(10,4),vip_price2 numeric(10,4),vip_price3 numeric(10,4),barcode text,unit_type text,lowest_price float8,contract_price numeric(10,4),cost_price_spec numeric(10,4),weight numeric(10,4),volume numeric(10,4)) partition by hash(company_id);
CREATE INDEX idx_info_item_multi_unit_company_id ON info_item_multi_unit(company_id);
CREATE INDEX idx_info_item_multi_unit_item_id ON info_item_multi_unit(item_id);
CREATE INDEX idx_info_item_multi_unit_unit_type ON info_item_multi_unit(unit_type);
CREATE UNIQUE INDEX idx_info_item_multi_unit_unique ON  info_item_multi_unit USING btree (company_id, item_id, unit_type)
CREATE INDEX idx_info_item_multi_unit_unit_no ON info_item_multi_unit(unit_no);

CREATE TABLE info_item_multi_unit_0 PARTITION OF info_item_multi_unit FOR VALUES WITH(MODULUS 4, REMAINDER 0);
CREATE TABLE info_item_multi_unit_1 PARTITION OF info_item_multi_unit FOR VALUES WITH(MODULUS 4, REMAINDER 1);
CREATE TABLE info_item_multi_unit_2 PARTITION OF info_item_multi_unit FOR VALUES WITH(MODULUS 4, REMAINDER 2);
CREATE TABLE info_item_multi_unit_3 PARTITION OF info_item_multi_unit FOR VALUES WITH(MODULUS 4, REMAINDER 3);
      
 
create table info_avail_unit(company_id integer not null,unit_id serial not null, unit_no text not null, is_big_unit boolean, order_index integer,constraint pk_info_avail_unit primary key(company_id,unit_no));

            
create table info_supcust(company_id integer not null,supcust_id serial not null,supcust_no text,boss_name text, sup_rank int,retail_wholesale_flag text, supcust_flag text,sup_name text,py_str text,sup_order_index integer, acct_cust_id integer, acct_type text,acct_way_id integer, mobile text, sup_group integer, status int,region_id integer,other_region text, sup_addr text,sup_addr2 text, addr_lat numeric(11,6),addr_lng numeric(11,6),addr2_lat numeric(11,6),addr2_lng numeric(11,6),zip text,sup_email text,sup_tel text, sup_tax_no text,birthday text,cn_birthday text,create_time timestamp,creator_id integer,charge_seller integer,supcust_remark text,sup_door_photo text,max_arrears numeric(10,2),pay_info text,approve_status text) partition by hash(company_id);
CREATE INDEX idx_info_supcust_company_id ON info_supcust(company_id);
//CREATE INDEX idx_info_supcust_supcust_id ON info_supcust(supcust_id);
CREATE unique INDEX idx_info_supcust_supcust_id ON ONLY public.info_supcust USING btree (company_id,supcust_id)
CREATE INDEX idx_info_supcust_sup_name ON info_supcust(sup_name);
CREATE INDEX idx_info_supcust_py_str ON info_supcust(py_str);
CREATE INDEX idx_info_supcust_mobile ON info_supcust(mobile);
CREATE INDEX idx_info_supcust_other_region ON info_supcust USING gin (other_region gin_trgm_ops);/*普通索引不支持%ab%这样的查询，导致数据量大时检索缓慢，gin_trgm_ops模式支持的，需要安装扩展 */
create index idx_info_supcust_acct_cust_id on info_supcust(acct_cust_id)

alter table info_supcust add addr_lnglat geography;
update info_supcust set addr_lnglat=ST_POINT(addr_lat,addr_lng) where 1=1;

CREATE TABLE info_supcust_0 PARTITION OF info_supcust FOR VALUES WITH(MODULUS 5, REMAINDER 0);
CREATE TABLE info_supcust_1 PARTITION OF info_supcust FOR VALUES WITH(MODULUS 5, REMAINDER 1);
CREATE TABLE info_supcust_2 PARTITION OF info_supcust FOR VALUES WITH(MODULUS 5, REMAINDER 2);
CREATE TABLE info_supcust_3 PARTITION OF info_supcust FOR VALUES WITH(MODULUS 5, REMAINDER 3);
CREATE TABLE info_supcust_4 PARTITION OF info_supcust FOR VALUES WITH(MODULUS 5, REMAINDER 4);


create table info_supcust_group(company_id integer not null,group_id serial not null,group_name text,supcust_flag text,order_index integer,remark text);

create table info_supcust_rank(company_id integer not null,rank_id serial not null,rank_name text,rank_note text,constraint pk_info_supcust_rank primary key(company_id,rank_name));

create table info_supcust_shop(company_id integer not null,supcust_id integer not null,shop_id serial not null,shop_name text not null,shop_py_str text,shop_addr text,shop_addr_lat numeric(11,6),shop_addr_lng numeric(11,6),shop_addr_lnglat geography,shop_region_id integer,shop_other_region text,shop_code text,shop_tel text);

		
create table info_client_address (company_id integer,addr_id serial primary key,client_id integer,addr_desc text,addr_order integer);
create index idx_info_client_address_company_id on info_client_address(company_id);
	
CREATE TABLE "public"."info_acct_way" (
  "company_id" int4,
  "acct_way_id" serial,
	"acct_way_name" text,
  "acct_type" text
);
 

create table info_region(company_id integer not null,region_id serial,region_name text,mother_id integer,order_index integer,constraint pk_info_region primary key(company_id,region_id));
CREATE INDEX idx_info_region_region_name ON info_region(region_name);
CREATE INDEX idx_info_region_region_id ON info_region(region_id);
CREATE INDEX idx_info_region_company_id ON info_region(company_id);


create table info_manufactor(manufactor_id serial primary key,company_id integer,supplier_id integer,manufactor_name text,manufactor_addr text,manufactor_tel text,order_index integer);
CREATE INDEX info_manufactor_company_id ON info_manufactor(company_id);
CREATE INDEX info_manufactor_supplier_id ON info_manufactor(supplier_id);
		
		


create table info_role(company_id integer not null,role_id serial primary key,role_name text,rights jsonb,remark text,templ_id integer);

create table info_role_template(company_id integer not null,templ_id integer not null,templ_name text,templ_rights jsonb,fee_discount float, templ_remark text,primary key(company_id,templ_id));
create table g_role_template(templ_id serial primary key,templ_name text,templ_rights jsonb,business_type integer, fee_discount float, templ_remark text );

/*alter table info_role add column role_templ_id integer;*/


create table info_department(company_id integer not null,depart_id serial not null,depart_name text,mother_id integer,order_index int, brands_id text, brands_name text,managers_id text,managers_name text );

create table info_sheet_detail_brief(company_id integer not null,brief_id serial,brief_text text,sheet_type text,is_price_remember bool,sub_id int4);
create table info_sheet_main_brief(company_id integer not null,brief_id serial,brief_text text,sheet_type text);

CREATE INDEX idx_info_oper_group_company_id ON info_oper_group(company_id);

create table info_oper_action(company_id integer not null, group_id integer, action_name text,action_value text);
CREATE INDEX idx_info_oper_action_company_id ON info_oper_action(company_id);
CREATE INDEX idx_info_oper_action_group_id ON info_oper_action(group_id);


create table info_cloud_printer(company_id integer not null, printer_id serial, printer_name text, printer_brand text, device_id text, check_code text, status int2, PRIMARY KEY(company_id,printer_id));
create unique index idx_info_cloud_printer_device_id on info_cloud_printer(device_id);

CREATE TABLE info_stock_alert (company_id int4 NOT NULL, branch_id int4 NOT NULL, item_id int4 NOT NULL, threshold_lack numeric(14), threshold_overload numeric(14), remark text, PRIMARY KEY (company_id, branch_id, item_id) );

create table realtime_supcust (company_id integer,supcust_id integer,realtime jsonb,constraint pk_realtime_supcust primary key(company_id,supcust_id))  partition by hash(company_id);
CREATE TABLE realtime_supcust_0 PARTITION OF realtime_supcust FOR VALUES WITH(MODULUS 5, REMAINDER 0);
CREATE TABLE realtime_supcust_1 PARTITION OF realtime_supcust FOR VALUES WITH(MODULUS 5, REMAINDER 1);
CREATE TABLE realtime_supcust_2 PARTITION OF realtime_supcust FOR VALUES WITH(MODULUS 5, REMAINDER 2);
CREATE TABLE realtime_supcust_3 PARTITION OF realtime_supcust FOR VALUES WITH(MODULUS 5, REMAINDER 3);
CREATE TABLE realtime_supcust_4 PARTITION OF realtime_supcust FOR VALUES WITH(MODULUS 5, REMAINDER 4);
CREATE INDEX idx_realtime_supcust_company_id ON realtime_supcust(company_id);
CREATE INDEX idx_realtime_supcust_supcust_id ON realtime_supcust(supcust_id);



create table stock(company_id integer not null,stock_flow serial not null,branch_id integer,item_id integer,branch_position integer not null default 0,batch_id integer not null default 0,size_id integer not null default '0',color_id integer not null default '0',stock_qty numeric(14, 4) NOT NULL default 0,sell_pend_qty numeric(14, 4) NOT NULL default 0,sell_re_pend_qty numeric(14, 4) NOT NULL default 0,buy_pend_qty numeric(14, 4) NOT NULL default 0,buy_re_pend_qty numeric(14, 4) NOT NULL default 0,move_out_pend_qty numeric(14, 4) NOT NULL default 0,move_in_pend_qty numeric(14, 4) NOT NULL default 0,cost_price numeric(14,4),cost_amt numeric(14,2),order_qty numeric(14, 4),allow_negative_qty numeric(14, 4),negative_till text,negative_oper text) partition by hash(company_id);
CREATE INDEX idx_stock_company_id ON stock(company_id);
CREATE INDEX idx_stock_item_id ON stock(item_id); 
CREATE UNIQUE INDEX idx_stock_unique ON public.stock USING btree (company_id, item_id, branch_id, branch_position, batch_id)
CREATE TABLE stock_0 PARTITION OF stock FOR VALUES WITH(MODULUS 10, REMAINDER 0);
CREATE TABLE stock_1 PARTITION OF stock FOR VALUES WITH(MODULUS 10, REMAINDER 1);
CREATE TABLE stock_2 PARTITION OF stock FOR VALUES WITH(MODULUS 10, REMAINDER 2);
CREATE TABLE stock_3 PARTITION OF stock FOR VALUES WITH(MODULUS 10, REMAINDER 3);
CREATE TABLE stock_4 PARTITION OF stock FOR VALUES WITH(MODULUS 10, REMAINDER 4);
CREATE TABLE stock_5 PARTITION OF stock FOR VALUES WITH(MODULUS 10, REMAINDER 5);
CREATE TABLE stock_6 PARTITION OF stock FOR VALUES WITH(MODULUS 10, REMAINDER 6);
CREATE TABLE stock_7 PARTITION OF stock FOR VALUES WITH(MODULUS 10, REMAINDER 7);
CREATE TABLE stock_8 PARTITION OF stock FOR VALUES WITH(MODULUS 10, REMAINDER 8);
CREATE TABLE stock_9 PARTITION OF stock FOR VALUES WITH(MODULUS 10, REMAINDER 9); 

create table stock_change_log (flow_id serial,company_id integer,approve_time timestamp,sheet_type text,sheet_id integer,item_id integer,branch_id integer,batch_id int4,  branch_position int4,

pre_stock_qty numeric(14,4),new_stock_qty numeric(14,4),pre_sell_pend_qty numeric(14,4),new_sell_pend_qty numeric(14,4)
) partition by range (approve_time);
create table stock_change_log_22092301 partition of stock_change_log for values from ('2022-09-23') to ('2023-01-01');
 
 CREATE INDEX idx_stock_change_log_company_id ON stock_change_log(company_id);
CREATE INDEX idx_stock_change_log_sheet_id ON stock_change_log(sheet_id);
CREATE INDEX idx_stock_change_log_item_id ON stock_change_log(item_id);
CREATE INDEX idx_stock_change_log_branch_id ON stock_change_log(branch_id);
CREATE INDEX idx_stock_change_log_approve_time ON stock_change_log(approve_time);


/*期初库存单*/
create table sheet_stock_opening_detail(company_id integer not null,flow_id serial,inout_flag smallint,sheet_id serial,row_index smallint,item_id integer not null,sheet_item_name text,branch_id integer,branch_position integer not null default 0,batch_id integer not null default 0,unit_no text,unit_factor real,quantity real,cost_price_avg real,cost_price_prop real,cost_price_buy real,wholesale_price real,remark text,happen_time timestamp) partition by range(happen_time);
create table sheet_stock_opening_main(company_id integer not null,sheet_id serial,sheet_no text,red_sheet_id integer,sheet_type text,money_inout_flag smallint,branch_id integer,seller_id integer,red_flag smallint,wholesale_amount double precision,cost_amount_avg double precision,buy_amount double precision,cost_amount_prop double precision,maker_id integer,make_time timestamp,happen_time timestamp,approver_id integer,approve_time timestamp,make_brief text,approve_brief text,submit_time timestamp) partition by range(happen_time);
create table sheet_stock_opening_main_24042408 partition of sheet_stock_opening_main for values from ('2024-04-01 00:00:01') to ('2024-09-01 00:00:00');
create table sheet_stock_opening_detail_24042408 partition of sheet_stock_opening_detail for values from ('2024-04-01 00:00:01') to ('2024-09-01 00:00:00');

/*门店库存上报单*/
create table sheet_client_stock_main(company_id integer NOT NULL,client_id integer NOT NULL,sheet_id serial,sheet_no text NOT NULL, red_sheet_id integer,sheet_type text, money_inout_flag smallint, branch_id integer,seller_id integer,red_flag smallint, wholesale_amount float8, cost_amount_avg float8, cost_amount_prop float8, buy_amount float8,maker_id integer, make_time timestamp, happen_time timestamp, approver_id integer,approve_time timestamp, make_brief text,approve_brief text, sheet_attribute jsonb, submit_time timestamp) partition by range(happen_time);
create table sheet_client_stock_detail(company_id int4 NOT NULL,flow_id serial NOT NULL,inout_flag smallint,sheet_id integer,row_index smallint, item_id integer not null,sheet_item_name text,client_stock_quantity float4,buy_quantity float4,branch_id integer, batch_id integer,unit_no text,unit_factor float4, sys_quantity float4, real_quantity float4,cost_price_avg float4,cost_price_prop float4,buy_price float4,wholesale_price float4,cost_price float4, happen_time timestamp, remark text,branch_position integer) partition by range(happen_time);
create table sheet_client_stock_main_24070312 partition of sheet_client_stock_main for values from ('2024-07-03 00:00:01') to ('2024-12-01 00:00:00');
create table sheet_client_stock_detail_24070312 partition of sheet_client_stock_detail for values from ('2024-07-03 00:00:01') to ('2024-12-01 00:00:00');

create table client_recent_prices(company_id integer not null,supcust_id INTEGER,item_id integer,unit_no text,recent_price numeric(14,4),happen_time TIMESTAMP,CONSTRAINT pk_client_recent_prices primary key(company_id,supcust_id,item_id,unit_no)) PARTITION by hash(company_id); 
CREATE INDEX idx_client_recent_prices_company_id ON client_recent_prices(company_id);
CREATE INDEX idx_client_recent_prices_item_id ON client_recent_prices(item_id);
CREATE INDEX idx_client_recent_prices_supcust_id ON client_recent_prices(supcust_id);
CREATE TABLE client_recent_prices_0 PARTITION OF client_recent_prices FOR VALUES WITH(MODULUS 4, REMAINDER 0);
CREATE TABLE client_recent_prices_1 PARTITION OF client_recent_prices FOR VALUES WITH(MODULUS 4, REMAINDER 1);
CREATE TABLE client_recent_prices_2 PARTITION OF client_recent_prices FOR VALUES WITH(MODULUS 4, REMAINDER 2);
CREATE TABLE client_recent_prices_3 PARTITION OF client_recent_prices FOR VALUES WITH(MODULUS 4, REMAINDER 3); 
 

create table supplier_recent_prices(company_id integer not null,supcust_id INTEGER,item_id integer,unit_no text,recent_price numeric(14,4),happen_time TIMESTAMP,CONSTRAINT pk_supplier_recent_prices primary key(company_id,supcust_id,item_id,unit_no)) PARTITION by hash(company_id); 
CREATE INDEX idx_supplier_recent_prices_company_id ON supplier_recent_prices(company_id);
CREATE INDEX idx_supplier_recent_prices_item_id ON supplier_recent_prices(item_id);
CREATE INDEX idx_supplier_recent_prices_supcust_id ON supplier_recent_prices(supcust_id);
CREATE TABLE supplier_recent_prices_0 PARTITION OF supplier_recent_prices FOR VALUES WITH(MODULUS 4, REMAINDER 0);
CREATE TABLE supplier_recent_prices_1 PARTITION OF supplier_recent_prices FOR VALUES WITH(MODULUS 4, REMAINDER 1);
CREATE TABLE supplier_recent_prices_2 PARTITION OF supplier_recent_prices FOR VALUES WITH(MODULUS 4, REMAINDER 2);
CREATE TABLE supplier_recent_prices_3 PARTITION OF supplier_recent_prices FOR VALUES WITH(MODULUS 4, REMAINDER 3); 


create table item_recent_produce_date(company_id integer not null,item_id integer,produce_date text,happen_time TIMESTAMP,CONSTRAINT pk_item_recent_produce_date primary key(company_id,item_id)); 


create table cost_price_log(company_id INTEGER not null,sheet_id int4,item_id int4,happen_time timestamp,suspect_status text,sheet_type text,infected_sheet_id int4,new_cost_price float8,CONSTRAINT pk_cost_price_log primary key(company_id,sheet_id,item_id,sheet_type,happen_time,infected_sheet_id)) PARTITION by hash(company_id); 

CREATE INDEX idx_cost_price_log_company_id ON cost_price_log(company_id);
CREATE INDEX idx_cost_price_log_item_id ON cost_price_log(item_id);
CREATE INDEX idx_cost_price_log_sheet_id ON cost_price_log(sheet_id);
CREATE TABLE cost_price_log_0 PARTITION OF cost_price_log FOR VALUES WITH(MODULUS 10, REMAINDER 0);
CREATE TABLE cost_price_log_1 PARTITION OF cost_price_log FOR VALUES WITH(MODULUS 10, REMAINDER 1);
CREATE TABLE cost_price_log_2 PARTITION OF cost_price_log FOR VALUES WITH(MODULUS 10, REMAINDER 2);
CREATE TABLE cost_price_log_3 PARTITION OF cost_price_log FOR VALUES WITH(MODULUS 10, REMAINDER 3);
CREATE TABLE cost_price_log_4 PARTITION OF cost_price_log FOR VALUES WITH(MODULUS 10, REMAINDER 4);
CREATE TABLE cost_price_log_5 PARTITION OF cost_price_log FOR VALUES WITH(MODULUS 10, REMAINDER 5);
CREATE TABLE cost_price_log_6 PARTITION OF cost_price_log FOR VALUES WITH(MODULUS 10, REMAINDER 6);
CREATE TABLE cost_price_log_7 PARTITION OF cost_price_log FOR VALUES WITH(MODULUS 10, REMAINDER 7);
CREATE TABLE cost_price_log_8 PARTITION OF cost_price_log FOR VALUES WITH(MODULUS 10, REMAINDER 8);
CREATE TABLE cost_price_log_9 PARTITION OF cost_price_log FOR VALUES WITH(MODULUS 10, REMAINDER 9); 

create table log_report (flow_id serial,company_id integer,oper_id integer,log text,report_time timestamp);

create unique index idx_stock_unique on stock(company_id,branch_id,item_id,batch_id);


create table sheet_no(company_id integer not null,sheet_day timestamp,oper_id integer, next_id integer,constraint pk_sheet_no PRIMARY KEY(company_id,sheet_day,oper_id,next_id));


create table print_template(company_id integer not null,template_id serial primary key,template_name text, sheet_type text, template_content json,author_id integer,order_index integer);
create index idx_print_template_company_id on print_template(company_id)
create index idx_print_template_sheet_type on print_template(sheet_type)

create table print_template_avail_elements (sheet_type text primary key,avail_elements json);
create table print_template_choose(company_id integer not null,sheet_type text,client_group_id integer, client_id integer,template_id integer,constraint pk_print_template_choose primary key (company_id,sheet_type,client_group_id,client_id));
create table print_template_shared(company_id integer not null, template_id serial primary key,template_name text, sheet_type text, template_content json,order_index integer, author_id integer,author_name text,industry text, brief text);
 
   
 
CREATE TABLE "public"."print_template_receipt" (
  "company_id" int4 NOT NULL,
  "template_id" serial,
  "template_name" text COLLATE "pg_catalog"."default",
  "sheet_type" text COLLATE "pg_catalog"."default",
  "template_content" json,
  "author_id" int4
)
;
COMMENT ON TABLE "public"."print_template_receipt" IS '存储小票票据模板内容';

 
 
 
CREATE TABLE "public"."print_template_receipt_avail_elements" (
  "sheet_type" text COLLATE "pg_catalog"."default" NOT NULL,
  "avail_elements" json
)
;
COMMENT ON TABLE "public"."print_template_receipt_avail_elements" IS '存储小票票据模板自定义界面的可选添加项目';
   
DROP TABLE IF EXISTS "public"."print_template_receipt_choose";
CREATE TABLE "public"."print_template_receipt_choose" (
  "company_id" int4 NOT NULL,
  "sheet_type" text COLLATE "pg_catalog"."default" NOT NULL,
  "client_group_id" int4 NOT NULL,
  "client_id" int4 NOT NULL,
  "template_id" int4
)
;
 
CREATE TABLE "public"."print_template_receipt_shared" (
  "company_id" int4 NOT NULL,
  "template_id" serial,
  "template_name" text COLLATE "pg_catalog"."default",
  "sheet_type" text COLLATE "pg_catalog"."default",
  "template_content" json,
  "order_index" int4,
  "author_id" int4,
  "author_name" text COLLATE "pg_catalog"."default",
  "industry" text COLLATE "pg_catalog"."default",
  "brief" text COLLATE "pg_catalog"."default"
)
;
  
 
CREATE TABLE "public"."print_template_sample_sheet" (
  "sheet_type" text COLLATE "pg_catalog"."default" NOT NULL,
  "sample_data" json
)
;
COMMENT ON TABLE "public"."print_template_sample_sheet" IS '存储打印模板、小票票据模板的实例数据';

 
ALTER TABLE "public"."print_template_receipt" ADD CONSTRAINT "print_template_receipt_pkey" PRIMARY KEY ("template_id");

 
ALTER TABLE "public"."print_template_receipt_avail_elements" ADD CONSTRAINT "print_template_receipt_avail_elements_pkey" PRIMARY KEY ("sheet_type");

 
ALTER TABLE "public"."print_template_receipt_choose" ADD CONSTRAINT "print_template_receipt_choose_pkey" PRIMARY KEY ("client_group_id", "client_id", "sheet_type");

 
ALTER TABLE "public"."print_template_receipt_shared" ADD CONSTRAINT "print_template_receipt_shared_pkey" PRIMARY KEY ("template_id");

 
ALTER TABLE "public"."print_template_sample_sheet" ADD CONSTRAINT "print_template_sample_sheet_pkey" PRIMARY KEY ("sheet_type");



create table company_setting(company_id integer primary key,company_cachet text,business_type integer,cost_price_type integer,barcode_type integer,setting jsonb);



create table options_remembered(company_id integer,oper_id integer,options jsonb,constraint pk_options_remembered primary key(company_id,oper_id))
create or replace function jsonb_merge(CurrentData jsonb,newData jsonb)
 returns jsonb
 language sql
 immutable
as $jsonb_merge_func$
 select case jsonb_typeof(CurrentData)
   when 'object' then case jsonb_typeof(newData)
     when 'object' then (
       select    jsonb_object_agg(k, case
                   when e2.v is null then e1.v
                   when e1.v is null then e2.v
                   when e1.v = e2.v then e1.v 
                   else jsonb_merge(e1.v, e2.v)
                 end)
       from      jsonb_each(CurrentData) e1(k, v)
       full join jsonb_each(newData) e2(k, v) using (k)
     )
     else newData
   end
   when 'array' then CurrentData || newData
   else newData
 end
$jsonb_merge_func$;

CREATE TABLE sheet_status_order( company_id int4 NOT NULL,sheet_id integer primary key, print_time timestamp, sheet_print_count int4, sum_print_count int4, open_stock_print_count int4, senders_id text,
  senders_name text, order_status text, receipt_status text,reject_time timestamp, van_id int4,  move_stock boolean,sale_sheet_id integer,move_to_van_op_id integer )

CREATE INDEX idx_sheet_status_order_company_id ON sheet_status_order(company_id);
CREATE INDEX idx_sheet_status_order_reject_time ON sheet_status_order(reject_time);


create table sheet_status_sale(company_id integer,sheet_id integer primary key, print_time timestamp,  sheet_print_count integer, sum_print_count integer,status text);
create table sheet_status_move(company_id integer,sheet_id integer primary key, print_time timestamp,  sheet_print_count integer);
create table sheet_status_buy(company_id integer,sheet_id integer primary key, print_time timestamp,  sheet_print_count integer, sum_print_count integer,status text);

 CREATE TABLE sheet_status_arrears_bill (
  "company_id" int4 NOT NULL,
  "sheet_id" int4,
  "print_time" timestamp(6),
  "sheet_print_count" int4,
  "sum_print_count" int4,
  "status" text COLLATE "pg_catalog"."default",
  CONSTRAINT "sheet_status_arrears_bill_pkey" PRIMARY KEY (company_id,sheet_id)
)
;

create table print_log(company_id integer,flow_id serial PRIMARY KEY, happen_time timestamp, sheet_type text,print_sum boolean, print_sheet boolean, sheet_ids integer, oper_id integer,print_result text);
 
ALTER TABLE  print_log ADD CONSTRAINT pk_print_log PRIMARY KEY (flow_id);

create table borrowed_cust_items(company_id integer not null,cust_id integer,item_id integer,borrowed_qty numeric(8, 3),constraint pk_borrowed_cust_items primary key(company_id,cust_id,item_id));

-- 新的借货余额管理系统（按借货单维度管理，支持借A单还B单场景）
create table borrow_sheet_balance(company_id integer not null,borrow_sheet_id integer not null,supcust_id integer not null,borrow_mode varchar(10) not null,total_amount numeric(15,2) default 0,returned_amount numeric(15,2) default 0,balance_amount numeric(15,2) default 0,borrow_time timestamp not null,last_return_time timestamp,status varchar(20) default 'ACTIVE',constraint pk_borrow_sheet_balance primary key(company_id,borrow_sheet_id));

create table return_borrow_mapping(company_id integer not null,return_sheet_id integer not null,return_detail_id integer not null,borrow_sheet_id integer not null,return_amount numeric(15,2) default 0,return_time timestamp default now(),constraint pk_return_borrow_mapping primary key(company_id,return_sheet_id,return_detail_id,borrow_sheet_id));

-- 为独立借货单表添加借货模式字段（如果表存在的话）
-- 注意：只有独立借货单支持按金额借还货，销售单中的借货保持原有逻辑
DO $$
BEGIN
    -- 为borrow_item_main添加borrow_mode字段
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'borrow_item_main') THEN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'borrow_item_main' AND column_name = 'borrow_mode') THEN
            ALTER TABLE borrow_item_main ADD COLUMN borrow_mode varchar(10) DEFAULT 'QTY';
        END IF;
    END IF;

    -- borrow_item_detail表不需要borrow_mode字段，借货模式是单据级别的设置
END $$;

CREATE TABLE display_agreement_main (
  "company_id" int4 NOT NULL,
  "sheet_id" serial NOT NULL,
  "sheet_no" text COLLATE "pg_catalog"."default" NOT NULL,
  "sheet_type" text COLLATE "pg_catalog"."default",
  "money_inout_flag" int2,
  "red_flag" int2,
  "red_sheet_id" int4,
  "red_sheet_date" date,
  "supcust_id" int4,
  "fee_sub_id" int4,
  "total_amount" float8 NOT NULL,
  "start_time" timestamp(6),
  "end_time" timestamp(6),
  "maker_id" int4,
  "make_time" timestamp(6),
  "approver_id" int4,
  "approve_time" timestamp(6),
  "seller_id" int4,
  "make_brief" text COLLATE "pg_catalog"."default",
  "approve_brief" text COLLATE "pg_catalog"."default",
  "happen_time" timestamp(6),
  "submit_time" timestamp(6),
  "adjust_sheet_id" int4,
  "orig_sheet_id" int4,
  "total_money" float8,
  "total_quantity" text,
  "terminate_time" timestamp,
  "terminator_oper" int4
)
PARTITION BY RANGE (
  happen_time
)
;
create table display_agreement_main_10012101 partition of display_agreement_main for values from ('2010-01-01') to ('2021-01-01');
create table display_agreement_main_21012106 partition of display_agreement_main for values from ('2021-01-01') to ('2021-07-01');
create table display_agreement_main_21072112 partition of display_agreement_main for values from ('2021-07-01') to ('2022-01-01');
 
CREATE INDEX idx_display_agreement_main_company_id ON display_agreement_main(company_id);
CREATE INDEX idx_display_agreement_main_sheet_id ON display_agreement_main(sheet_id);



CREATE TABLE display_agreement_detail (
  company_id int4 NOT NULL,
  "flow_id" serial NOT NULL,
  "sheet_id" int4,
  "items_id" text,
  "items_name" text,
  "unit_no" text,
  "month1_qty" float8,
  "month1_given" float8,
  "month2_qty" float8,
  "month2_given" float8,
  "month3_qty" float8,
  "month3_given" float8,
  "month4_qty" float8,
  "month4_given" float8,
  "month5_qty" float8,
  "month5_given" float8,
  "month6_qty" float8,
  "month6_given" float8,
  "month7_qty" float8,
  "month7_given" float8,
  "month8_qty" float8,
  "month8_given" float8,
  "month9_qty" float8,
  "month9_given" float8,
  "month10_qty" float8,
  "month10_given" float8,
  "month11_qty" float8,
  "month11_given" float8,
  "month12_qty" float8,
  "month12_given" float8,
  "happen_time" timestamp(6),
  "sub_amount"  float8,
  "remark"  text,
  "inout_flag" int2,
  "row_index" int4,
  "all_given" bool
)
PARTITION BY RANGE (
  "happen_time" "pg_catalog"."timestamp_ops"
);

create table display_agreement_detail_10012101 partition of display_agreement_detail for values from ('2010-01-01') to ('2021-01-01');
create table display_agreement_detail_21012106 partition of display_agreement_detail for values from ('2021-01-01') to ('2021-07-01');
create table display_agreement_detail_21072112 partition of display_agreement_detail for values from ('2021-07-01') to ('2022-01-01');

 
CREATE INDEX idx_display_agreement_detail_company_id ON display_agreement_detail(company_id);
CREATE INDEX idx_display_agreement_detail_sheet_id ON display_agreement_detail(sheet_id);
CREATE INDEX idx_display_agreement_detail_flow_id ON display_agreement_detail(flow_id);


 
 --价格策略 start
 create table price_plan_main (company_id integer,plan_id serial,plan_name text,create_time timestamp,update_time timestamp,CONSTRAINT pk_price_plan_main primary key(plan_id) );

create table price_plan_item (company_id integer,plan_id serial,item_id integer,s_price numeric(10,4),m_price numeric(10,4),b_price numeric(10,4),discount float,CONSTRAINT pk_price_plan_item primary key(company_id,plan_id,item_id) );

create table price_plan_class (company_id integer,plan_id serial,plan_name text,create_time timestamp,update_time timestamp,CONSTRAINT pk_price_plan_class primary key(plan_id) );

create table price_strategy_client (company_id integer,flow_id serial,supcust_id integer,price1 text,price2 text,price3 text,price4 text,price5 text,CONSTRAINT pk_price_strategy_client primary key(flow_id) );

create table price_strategy_class (company_id integer,flow_id serial,group_id integer,region_id integer,rank_id integer,price1 text,price2 text,price3 text,price4 text,price5 text,CONSTRAINT pk_price_strategy_class primary key(flow_id) );
-- 价格策略end
--促销
CREATE TABLE strategy_promotion(
  company_id int4 NOT NULL,
  promotion_id serial,
  promotion_name text NOT NULL,
  start_time timestamp NOT NULL,
  end_time timestamp NOT NULL,
  departments_id text NOT NULL,
  groups_id text NOT NULL,
  ranks_id text NOT NULL,
  regions_id text NOT NULL,
  promotion_content jsonb NOT NULL
)

create index idx_strategy_promotion_end_time on strategy_promotion(end_time);
create index idx_strategy_promotion_start_time on strategy_promotion(start_time);

-- 微信相关
create table wx_user
(
    wx_user_id      serial
        constraint info_wx_user_pk
            primary key,
    open_id         text,
    union_id        text,
    mini_open_id    text,
    mini_mobile     text,
    subscribe_scene text,
    qr_scene        text,
    qr_scene_str    text,
    nick_name       text,
    mobile_first    text,
    mobile_second   text,
    mobile_third    text,
    subscribe_flag  text
);

comment on column wx_user.open_id is '微信公众号绑定id';

comment on column wx_user.union_id is '微信UnionID机制';

comment on column wx_user.mini_open_id is '微信小程序id';

comment on column wx_user.mini_mobile is '微信授权手机号';

comment on column wx_user.subscribe_scene is '返回用户关注的渠道来源，ADD_SCENE_SEARCH 公众号搜索，ADD_SCENE_ACCOUNT_MIGRATION 公众号迁移，ADD_SCENE_PROFILE_CARD 名片分享，ADD_SCENE_QR_CODE 扫描二维码，ADD_SCENE_PROFILE_LINK 图文页内名称点击，ADD_SCENE_PROFILE_ITEM 图文页右上角菜单，ADD_SCENE_PAID 支付后关注，ADD_SCENE_WECHAT_ADVERTISEMENT 微信广告，ADD_SCENE_REPRINT 他人转载 ,ADD_SCENE_LIVESTREAM 视频号直播，ADD_SCENE_CHANNELS 视频号 , ADD_SCENE_OTHERS 其他';

comment on column wx_user.qr_scene is '二维码扫码场景（开发者自定义）';

comment on column wx_user.qr_scene_str is '二维码扫码场景描述（开发者自定义）';

comment on column wx_user.nick_name is '昵称，后期提供修改';

comment on column wx_user.mobile_first is '绑定手机号';

comment on column wx_user.subscribe_flag is '用户状态';

create unique index info_wx_user_wx_user_id_uindex
    on wx_user (wx_user_id);

create table info_cust_contact
(
    company_id        integer not null,
    supcust_id        integer not null,
    contact_id        serial
        primary key,
    contact_nick_name text,
    contact_mobile    text,
    wx_user_id        integer
);

comment on column info_cust_contact.contact_nick_name is '昵称';

comment on column info_cust_contact.contact_mobile is '授权电话';

alter table info_cust_contact
    owner to postgres;

create unique index info_cust_contact_contact_id_uindex
    on info_cust_contact (company_id, supcust_id, contact_mobile, wx_user_id);


comment on column info_cust_contact.contact_nick_name is '昵称';

comment on column info_cust_contact.contact_mobile is '授权电话';


create table wx_user_subscribe_setting
(
    subscribe_id      serial
        constraint wx_user_subscribe_setting_pk_2
            primary key
        constraint wx_user_subscribe_setting_pk
            unique,
    wx_user_id        integer not null
        constraint wx_user_subscribe_setting_wx_user_wx_user_id_fk
            references wx_user,
    subscribe_flag    text,
    subscribe_content jsonb
);

comment on column wx_user_subscribe_setting.subscribe_flag is '订阅标记true 订阅false 不订阅';

comment on column wx_user_subscribe_setting.subscribe_content is '订阅内容json';

alter table wx_user_subscribe_setting
    owner to postgres;

create unique index wx_user_subscribe_setting_subscribe_id_uindex
    on wx_user_subscribe_setting (subscribe_id);

create unique index wx_user_subscribe_setting_wx_user_id_uindex
    on wx_user_subscribe_setting (wx_user_id);


-- 微信相关


CREATE TABLE "public"."op_move_from_van_main" (
  "op_id" serial,
  "company_id" int4,
  "oper_id" int4,
  "happen_time" timestamp(6),
  "move_sheet_id" int4,
  "senders_id" text COLLATE "pg_catalog"."default",
  "senders_name" text COLLATE "pg_catalog"."default",
  CONSTRAINT "op_move_from_van_main_pkey" PRIMARY KEY ("op_id")
)
;

CREATE TABLE "public"."op_move_from_van_detail" (
  "op_id" int8 NOT NULL,
  "company_id" int4,
  "move_sheet_id" int4,
  "sale_order_sheet_id" int4,
  "sale_sheet_id" int4,
  "oper_id" int4
)
;

CREATE TABLE "public"."op_move_to_van_main" (
  "op_id" serial,
   op_no text,
  "op_type" text COLLATE "pg_catalog"."default",
  "oper_id" int4,
  "company_id" int4,
  "happen_time" timestamp(6),
  "move_sheet_id" int4,
  "senders_id" text COLLATE "pg_catalog"."default",
  "senders_name" text COLLATE "pg_catalog"."default",
  from_branch integer,
  to_van integer,
  move_stock bool,
  CONSTRAINT "op_move_to_van_main_pkey" PRIMARY KEY ("op_id")
);
 

CREATE TABLE "public"."op_move_to_van_detail" (
  "op_id" int8 NOT NULL,
  "oper_id" int4 NOT NULL,
  "company_id" int4,
  "move_sheet_id" int4,
  "sale_order_sheet_id" int4
);
CREATE INDEX idx_op_move_to_van_detail_sale_order_sheet_id ON "op_move_to_van_detail"(sale_order_sheet_id);

CREATE TABLE op_move_to_van_row (company_id int4 NOT NULL, flow_id serial,op_id int4 not null,happen_time timestamp, item_id integer not null,batch_id int DEFAULT 0,unit_no text,unit_factor float4,sheet_order_quantity float4,quantity float4,row_index integer,remark text) partition by range(happen_time);

CREATE INDEX idx_op_move_to_van_row_company_id ON op_move_to_van_row(company_id);
CREATE INDEX idx_op_move_to_van_row_happen_time ON op_move_to_van_row(happen_time);
CREATE INDEX idx_op_move_to_van_row_op_id ON op_move_to_van_row(op_id);
CREATE INDEX idx_op_move_to_van_row_item_id ON op_move_to_van_row(item_id);
CREATE INDEX idx_op_move_to_van_row_unit_no ON op_move_to_van_row(unit_no); 

create table op_move_to_van_row_230101230401 partition of op_move_to_van_row for values from ('2023-01-01') to ('2023-04-01');


alter table op_move_to_van_detail add column retreat_id integer;
alter table op_move_to_van_row add column retreat_qty float4;



--回库操作
CREATE TABLE "op_move_from_van_main" (
  "op_id" serial,op_no text,
  "company_id" int4,
  "oper_id" int4,
  "happen_time" timestamp(6),
  from_branch int4，
  move_stock bool，
  "move_sheet_id" int4,
  "senders_id" text COLLATE "pg_catalog"."default",
  "senders_name" text COLLATE "pg_catalog"."default",
  back_type text,back_branch int4,sheet_qty float4,move_qty float4,move_sheet_id int4,
  CONSTRAINT "op_move_from_van_main_pkey" PRIMARY KEY ("op_id")
);
 

CREATE TABLE "op_move_from_van_detail" (
  "op_id" int8 NOT NULL,
  "company_id" int4,
  "move_sheet_id" int4,
  "sale_order_sheet_id" int4,
  "sale_sheet_id" int4,
  "oper_id" int4
);

CREATE TABLE "public"."op_move_from_van_row" (
  "company_id" int4 NOT NULL,
  "op_id" int4 NOT NULL,
  "sale_order_sheet_id" int4 NOT NULL,
  "order_flow_id" int4,
  "sale_sheet_id" int4,
  "sale_flow_id" int4,
  "item_id" int4 NOT NULL,
  "unit_no" text,
  "unit_factor" float4,
  "old_reject_qty" float4,
  "old_back_qty" float4,
  "reject_qty" float4,
  "back_qty" float4,
  "assign_van_type" text,
  back_type text,back_branch int4,need_move_qty float4,move_qty float4,move_sheet_id int4,sale_qty float4, 
  is_previous_move bool,back_branch_status text,
  remark text,
  "happen_time" timestamp(6),
  "row_index" int4,
  PRIMARY KEY (company_id,op_id,sale_order_sheet_id,item_id,unit_no,back_type)
);

--转单操作
CREATE TABLE "public"."op_order_to_sale_sheets_main" (
  "op_id" serial,
  "oper_id" int4,
  "company_id" int4,
  "happen_time" timestamp(6),
  CONSTRAINT "op_order_to_sale_sheets_main_pkey" PRIMARY KEY ("op_id")
)
;

CREATE TABLE "public"."op_order_to_sale_sheets_detail" (
  "op_id" int8 NOT NULL,
  "oper_id" int4,
  "company_id" int4,
  "sale_order_sheet_id" int4
);



 
CREATE TABLE "public"."print_log" (
  "company_id" int4,
  "flow_id" serial,
  "happen_time" timestamp(6),
  "sheet_type" text COLLATE "pg_catalog"."default",
  "print_sum" bool,
  "print_sheet" bool,
  "sheet_ids" text COLLATE "pg_catalog"."default",
  "oper_id" int4,
  "print_open_stock" bool
)
;

create table page_set(company_id integer,oper_id integer, page_name text,setting jsonb,columns_width jsonb,constraint pk_page_set primary key(company_id,oper_id,page_name))


/*for item attributes*/
create table info_attribute(company_id integer,attr_id serial, attr_name text,spec_opt_in_item boolean,use_opt_group boolean,distinct_stock boolean, distinct_stock_editable boolean, not_distinct_stock_in_van boolean,order_index integer,remember_price boolean,sale_combine_print boolean,primary key(company_id,attr_id));

create table info_attr_opt(company_id integer,opt_id serial, opt_name text, attr_id integer,order_index integer, primary key(company_id,opt_id)) partition by hash(company_id);

create unique index idx_info_attr_opt  on info_attr_opt(company_id,attr_id,opt_name);

CREATE TABLE info_attr_opt_0 PARTITION OF info_attr_opt FOR VALUES WITH(MODULUS 10, REMAINDER 0);
CREATE TABLE info_attr_opt_1 PARTITION OF info_attr_opt FOR VALUES WITH(MODULUS 10, REMAINDER 1);
CREATE TABLE info_attr_opt_2 PARTITION OF info_attr_opt FOR VALUES WITH(MODULUS 10, REMAINDER 2);
CREATE TABLE info_attr_opt_3 PARTITION OF info_attr_opt FOR VALUES WITH(MODULUS 10, REMAINDER 3);
CREATE TABLE info_attr_opt_4 PARTITION OF info_attr_opt FOR VALUES WITH(MODULUS 10, REMAINDER 4);
CREATE TABLE info_attr_opt_5 PARTITION OF info_attr_opt FOR VALUES WITH(MODULUS 10, REMAINDER 5);
CREATE TABLE info_attr_opt_6 PARTITION OF info_attr_opt FOR VALUES WITH(MODULUS 10, REMAINDER 6);
CREATE TABLE info_attr_opt_7 PARTITION OF info_attr_opt FOR VALUES WITH(MODULUS 10, REMAINDER 7);
CREATE TABLE info_attr_opt_8 PARTITION OF info_attr_opt FOR VALUES WITH(MODULUS 10, REMAINDER 8);
CREATE TABLE info_attr_opt_9 PARTITION OF info_attr_opt FOR VALUES WITH(MODULUS 10, REMAINDER 9);

create table info_attr_group(company_id integer,group_id serial,group_name text,attr_id integer, primary key(company_id,group_id));
create table info_attr_group_opt(company_id integer,group_id integer,opt_id integer, primary key(company_id,group_id,opt_id)) partition by hash(company_id);

CREATE TABLE info_attr_group_opt_0 PARTITION OF info_attr_group_opt FOR VALUES WITH(MODULUS 10, REMAINDER 0); 
CREATE TABLE info_attr_group_opt_1 PARTITION OF info_attr_group_opt FOR VALUES WITH(MODULUS 10, REMAINDER 1);
CREATE TABLE info_attr_group_opt_2 PARTITION OF info_attr_group_opt FOR VALUES WITH(MODULUS 10, REMAINDER 2);
CREATE TABLE info_attr_group_opt_3 PARTITION OF info_attr_group_opt FOR VALUES WITH(MODULUS 10, REMAINDER 3);
CREATE TABLE info_attr_group_opt_4 PARTITION OF info_attr_group_opt FOR VALUES WITH(MODULUS 10, REMAINDER 4);
CREATE TABLE info_attr_group_opt_5 PARTITION OF info_attr_group_opt FOR VALUES WITH(MODULUS 10, REMAINDER 5);
CREATE TABLE info_attr_group_opt_6 PARTITION OF info_attr_group_opt FOR VALUES WITH(MODULUS 10, REMAINDER 6);
CREATE TABLE info_attr_group_opt_7 PARTITION OF info_attr_group_opt FOR VALUES WITH(MODULUS 10, REMAINDER 7);
CREATE TABLE info_attr_group_opt_8 PARTITION OF info_attr_group_opt FOR VALUES WITH(MODULUS 10, REMAINDER 8);
CREATE TABLE info_attr_group_opt_9 PARTITION OF info_attr_group_opt FOR VALUES WITH(MODULUS 10, REMAINDER 9);


alter table info_item_prop add column son_mum_item integer, add column son_options_id text, add column son_options_name text,add column mum_attributes jsonb;      
alter table sheet_sale_detail add column attr_qty jsonb;

alter table sheet_sale_order_detail add column attr_qty jsonb;
 


/*end for item attributes*/


CREATE TABLE item_price_adjust(company_id integer not null,plan_id integer not null,item_id integer not null,adjust_time timestamp,constraint pk_item_price_adjust primary key(company_id,plan_id,item_id));


CREATE TABLE sheet_cost_price_adjust_main (company_id integer NOT NULL, sheet_id serial, sheet_no text NOT NULL, sheet_type text, money_inout_flag smallint, seller_id integer, red_flag smallint, red_sheet_id integer, red_sheet_date date, total_amount numeric, maker_id integer, make_time timestamp, make_brief text, approver_id integer, approve_time timestamp, approve_brief text, happen_time timestamp, submit_time timestamp, buy_price_change bool) 
partition by range(happen_time);

create table sheet_cost_price_adjust_main_20072201 partition of sheet_cost_price_adjust_main  for values from ('2020-07-01') to ('2022-01-01');
create table sheet_cost_price_adjust_main_22012301 partition of sheet_cost_price_adjust_main  for values from ('2022-01-01') to ('2023-01-01');

CREATE TABLE sheet_cost_price_adjust_detail(company_id int4 NOT NULL,flow_id serial NOT NULL,sheet_id integer,row_index int,inout_flag integer, item_id integer not null,unit_no text,unit_factor float4,real_price float4,old_avg_price float4,old_buy_price float4,combine_flag text, quantity float4, sub_amount numeric,tax_amount float4,happen_time timestamp, remark text) 
partition by range(happen_time)

create table sheet_cost_price_adjust_detail_20072201 partition of sheet_cost_price_adjust_detail for values from ('2020-07-01') to ('2022-01-01');
create table sheet_cost_price_adjust_detail_22012301 partition of sheet_cost_price_adjust_detail for values from ('2022-01-01') to ('2023-01-01');


/*推送*/

 
CREATE TABLE "public"."g_push_oper_reg" (
  "id" serial primary key,
  "company_id" int4,
  "oper_id" int4,
  "reg_id" text COLLATE "pg_catalog"."default",
  "platform" text COLLATE "pg_catalog"."default"
)
;


CREATE TABLE "public"."info_visit_day_client" (
  "company_id" int4 NOT NULL,
  "order_index" int4,
  "day_id" int4 NOT NULL,
  "supcust_id" int4 NOT NULL
)


/*考勤*/
 
CREATE TABLE "public"."info_attence_group" (
  "group_id" serial,
  "group_name" text COLLATE "pg_catalog"."default",
  "members_id" text COLLATE "pg_catalog"."default",
  "members_name" text COLLATE "pg_catalog"."default",
  "check_days_id" text COLLATE "pg_catalog"."default",
  "check_days_name" text COLLATE "pg_catalog"."default",
  "check_start_time" text COLLATE "pg_catalog"."default",
  "check_end_time" text COLLATE "pg_catalog"."default",
  "check_latitude" numeric(32,8),
  "check_longitude" numeric(32,8),
  "check_distance" numeric(32,8),
  "company_id" numeric(32,8),
  "check_addr" varchar(255) COLLATE "pg_catalog"."default",
   schedule jsonb
)
;

CREATE TABLE "public"."attence_record" (
  "company_id" int4,
  "flow_id" serial primary key,
  "oper_id" int4,
  "start_time" timestamp(6),
  "end_time" timestamp(6),
  "group_id" int4,
  "addr" varchar(255) COLLATE "pg_catalog"."default",
  "longitude" numeric(255,8),
  "latitude" numeric(255,8),
  "in_pic" varchar(255) COLLATE "pg_catalog"."default",
  "out_pic" varchar(255) COLLATE "pg_catalog"."default", 
   schedule_id text
)
;

 
 /*新客雷达 客户类别*/

 
CREATE TABLE "public"."info_supcust_type" (
  "id" int4 primary key,
  "p_id" int4,
  "sup_type" varchar(255) COLLATE "pg_catalog"."default",
  "status" int4
)
;
CREATE TABLE "public"."attence_leave" (
  "flow_id" int4 NOT NULL DEFAULT nextval('attence_leave_record_flow_id_seq'::regclass),
  "leave_type" int4,
  "leave_reason" text COLLATE "pg_catalog"."default",
  "appendix_photo" text COLLATE "pg_catalog"."default",
  "status" int4,
  "seller_id" int4,
  "audit_id" int4,
  "create_time" timestamp(6),
  "start_date" date,
  "end_date" date NOT NULL,
  "company_id" int4
)
;
COMMENT ON COLUMN "public"."attence_leave"."status" IS '-2被驳回 -1 撤回 0 审批中 1 审批完成';

 
ALTER TABLE "public"."attence_leave" ADD CONSTRAINT "attence_leave_pkey" PRIMARY KEY ("flow_id");

create table log_sql(flow_id serial,company_id integer,happen_time timestamp,database text,sql text)
create table log_msg(flow_id serial,company_id integer,happen_time timestamp,msg text);
create index idx_log_msg_happen_time on log_msg(happen_time)
 

create table log_user_action(flow_id serial,company_id integer, happen_time timestamp,oper_id integer,action jsonb);
create index idx_log_user_action_happen_time on log_user_action(happen_time);

 

/*
create table sheet_sale_main_21062201 partition of sheet_sale_main for values from ('2021-06-01') to ('2022-01-01');
 
 create table sheet_sale_detail_21062201 partition of sheet_sale_detail for values from  ('2021-06-01') to ('2022-01-01');

 create table sheet_sale_order_main_21062201 partition of sheet_sale_order_main for values from ('2021-06-01') to ('2022-01-01');
 create table sheet_sale_order_detail_21062201 partition of sheet_sale_order_detail for values from ('2021-06-01') to ('2022-01-01');
 
 
  create table sheet_buy_main_21062201 partition of sheet_buy_main for values from  ('2021-06-01') to ('2022-01-01');
   create table sheet_buy_detail_21062201 partition of sheet_buy_detail for values from  ('2021-06-01') to ('2022-01-01');
  
	   create table sheet_move_main_21062201 partition of sheet_move_main for values from  ('2021-06-01') to ('2022-01-01');
  
	   create table sheet_move_detail_21062201 partition of sheet_move_detail for values from  ('2021-06-01') to ('2022-01-01');
  
	
	   create table sheet_invent_change_main_21062201 partition of sheet_invent_change_main for values from  ('2021-06-01') to ('2022-01-01');
		 	   create table sheet_invent_change_detail_21062201 partition of sheet_invent_change_detail for values from  ('2021-06-01') to ('2022-01-01');
				 
  create table sheet_inventory_main_21062201 partition of sheet_inventory_main for values from  ('2021-06-01') to ('2022-01-01');
	 create table sheet_inventory_detail_21062201 partition of sheet_inventory_detail for values from  ('2021-06-01') to ('2022-01-01');
		
		  create table sheet_get_arrears_main_21062201 partition of sheet_get_arrears_main for values from  ('2021-06-01') to ('2022-01-01'); 
		 	  create table sheet_get_arrears_detail_21062201 partition of sheet_get_arrears_detail for values from  ('2021-06-01') to ('2022-01-01');
		

  create table sheet_fee_out_main_21062201 partition of sheet_fee_out_main for values from  ('2021-06-01') to ('2022-01-01');
  create table sheet_fee_out_detail_21062201 partition of sheet_fee_out_detail for values from  ('2021-06-01') to ('2022-01-01');
	
	  create table sheet_visit_21062201 partition of sheet_visit for values from  ('2021-06-01') to ('2022-01-01');
	
		  create table seller_trail_21062201 partition of seller_trail for values from  ('2021-06-01') to ('2022-01-01');
	
 create table sheet_prepay_21062201 partition of sheet_prepay for values from  ('2021-06-01') to ('2022-01-01');

 create table sheet_item_ordered_adjust_main_21062201 partition of sheet_item_ordered_adjust_main for values from  ('2021-09-01') to ('2022-01-01');

 create table sheet_item_ordered_adjust_detail_21062201 partition of sheet_item_ordered_adjust_detail for values from  ('2021-09-01') to ('2022-01-01');

 
 create table sheet_order_item_detail_21062201 partition of sheet_order_item_detail for values from  ('2021-09-01') to ('2022-01-01');


 */

drop function if exists yj_exeSqlByInsertedRowID;
CREATE OR REPLACE FUNCTION yj_exeSqlByInsertedRowID(insert_sql text, sql text, id_var text)
  RETURNS text AS $BODY$   
declare
    id1 integer; id2 integer;
        return_id integer;
        sql1 text;
        BEGIN
			id2:= -1;
            EXECUTE insert_sql into id1;  
            if sql<>'' then
                sql1:=replace(sql, id_var, cast(id1 as text));
                if position(' returning ' in lower(sql))>0 then
                   execute sql1 into id2; 
                ELSE
                   execute sql1; 
                end if;  
            end if;
			return cast(id1 as text) || ',' || cast(id2 as text);
           
        END;   
$BODY$ LANGUAGE plpgsql;


/* 财务模块*/


create table cw_voucher_no(company_id INTEGER not null,period date,next_voucher_no integer,primary key (company_id,period))

ALTER TABLE "sheet_sale_detail" ADD COLUMN "sn_code" text;
ALTER TABLE "sheet_buy_detail" ADD COLUMN "sn_code" text;
ALTER TABLE "info_department" ADD COLUMN "brands_id" text, ADD COLUMN "brands_name" text;

-- 电商对接相关
CREATE TABLE "emart_authinfo" (
  "company_id" int4 NOT NULL, "emart_type" text NOT NULL,
  "access_token" text, "auto_update" bool, "update_time" int8,
	PRIMARY KEY("company_id", "emart_type")
);
CREATE TABLE "emart_sheet_pair" (
  "company_id" int4 NOT NULL, "emart_type" text NOT NULL,
  "src_sheet_id" text NOT NULL, "dest_sheet_id" int4,
	PRIMARY KEY("company_id", "emart_type", "src_sheet_id")
);


 

  



-- 2022.12.12 历史交账单改造,添加支付方式字段
ALTER TABLE "sheet_check_sheets_main" 
  ADD COLUMN "payway" jsonb;
COMMENT ON COLUMN "sheet_check_sheets_main"."payway" IS '支付方式总集,格式如 { "1":{"name":"现金","amt":55.2}, "66":{"name":"支付宝","amt":22} }';


/*特价审批*/

CREATE TABLE sheet_special_price_main (
 company_id INTEGER NOT NULL,
 sheet_id serial,
 sheet_no TEXT NOT NULL,
 sheet_type TEXT,
 red_flag SMALLINT,
 red_sheet_id INTEGER,
 red_sheet_date DATE,
 supcust_id INTEGER,
 maker_id INTEGER,
 make_time TIMESTAMP,
 happen_time TIMESTAMP,
 approver_id INTEGER,
 approve_time TIMESTAMP,
 start_time TIMESTAMP,
 end_time TIMESTAMP,
 seller_id INTEGER,
 make_brief TEXT,
 approve_brief TEXT,
 submit_time TIMESTAMP,
 sheet_attribute jsonb
);

CREATE TABLE sheet_special_price_detail (
 company_id int4 NOT NULL,
 flow_id serial NOT NULL,
 row_index int4,
 sheet_id INTEGER,
 item_id INTEGER NOT NULL,
 unit_no TEXT,
 unit_factor float4,
 recent_price float4,
 wholesale_price float4,
 real_price float4,
 special_price float4,
 happen_time TIMESTAMP,
 remark TEXT
);

//CREATE TABLE client_special_sale_price (company_id int4 NOT NULL,supcust_id integer, item_id integer not null,unit_no text,unit_factor float4,real_price float4,old_special_price float4,special_price float4,start_time timestamp, end_time timestamp);

-- 陈列活动
CREATE TABLE info_display_form (company_id int NOT NULL, form_id serial CONSTRAINT info_display_form_pk PRIMARY KEY, form_name text, status int);
CREATE UNIQUE INDEX info_display_form_form_id_uindex ON info_display_form USING btree (form_id);
create table info_display_template
(
    company_id             integer not null,
    disp_template_id       serial
        constraint info_display_template_pk
            primary key,
    disp_template_name     text,
    sup_id                 integer,
    display_form           integer,
    sign_actions           jsonb,
    keep_actions           jsonb,
    maintain_actions       jsonb,
    month_maintain_times   integer,
    maintain_interval_days integer,
    latest_sign_month_day  integer,
    start_time             timestamp,
    end_time               timestamp,
    give_condition         text,
    give_items             jsonb,
    sign_need_review       boolean,
    maintain_need_review   boolean,
    resign_interval_months smallint,
    fd_seller_actions      jsonb,
    fd_sender_actions      jsonb,
    cx_give_actions        jsonb,
    fd_seller_need_review  boolean,
    fd_sender_need_review  boolean,
    cx_give_need_review    boolean,
    keep_need_review       boolean
);

comment on column info_display_template.fd_seller_actions is '访单业务员';

comment on column info_display_template.fd_sender_actions is '访单送货员';

comment on column info_display_template.cx_give_actions is '车销兑付';

create unique index info_display_template_disp_template_id_uindex
    on info_display_template (disp_template_id);

-- 修改陈列协议主表
alter table display_agreement_main
    add display_temp_id integer;

comment on column display_agreement_main.display_temp_id is '陈列模板id';

alter table display_agreement_main
    add sign_work_content jsonb;

alter table display_agreement_main
    add reviewer integer;

alter table display_agreement_main
    add review_time timestamp;

alter table display_agreement_main
    add review_comment text;

alter table display_agreement_main
    add review_refused boolean;

alter table display_agreement_main
    add sheet_attribute jsonb;

-- msg_queue
create table msg_queue
(
    msg_id       serial,
    company_id   integer,
    msg_class    text,
    msg_type     text,
    msg_sub_type text,
    creater_id   integer,
    create_time  timestamp,
    msg_content  jsonb,
    deal_time    timestamp,
    deal_worker  integer
) partition by range(create_time);
create table msg_queue_230101230401 partition of msg_queue for values from ('2023-01-01') to('2023-04-01');
create index idx_msg_queue_msg_id
    on msg_queue (msg_id);

-- msg_subscribe
create table msg_subscribe
(
    company_id   integer,
    msg_type     text,
    msg_sub_type text,
    worker_id    integer,
    constraint msg_subscribe_pk
        primary key (company_id, msg_type, msg_sub_type, worker_id)
);
-- msg_subscribe_role
create table msg_subscribe_role
(
    company_id   integer,
    msg_type     text,
    msg_sub_type text,
    role_id      integer,
    constraint msg_subscribe_role_pk
        primary key (company_id, msg_type, msg_sub_type, role_id)
);
-- msg_read
create table msg_read
(
    company_id integer,
    msg_id     integer,
    worker_id  integer,
    read_time  timestamp
) partition by range(read_time);
create table msg_read_230101230401 partition of msg_read for values from ('2023-01-01') to('2023-04-01');
create table msg_read_23120120240301 partition of msg_read for values from ('2023-12-01') to('2024-03-01');
create index idx_msg_read_msg_id
    on msg_read (msg_id);




-- auto-generated definition
create table op_display_maintain
(
    company_id     integer,
    maintain_id    serial
        constraint op_display_maintain_pk
            primary key,
    oper_id        integer,
    client_id      integer,
    disp_temp_id   integer,
    disp_sheet_id  integer,
    work_content   jsonb,
    happen_time    timestamp,
    visit_id       integer,
    reviewer       integer,
    review_time    timestamp,
    review_comment text,
    review_refused boolean
);
create unique index op_display_maintain_maintain_id_uindex
    on op_display_maintain (maintain_id);

create table sum_display_maintain
(
    company_id      integer,
    sum_maintain_id serial
        constraint sum_display_maintain_pk
            primary key,
    client_id       integer,
    disp_temp_id    integer,
    disp_sheet_id   integer,
    maintain_times  int,
    months          timestamp
);

create unique index sum_display_maintain_sum_maintain_id_uindex
    on sum_display_maintain (sum_maintain_id);

create table op_display_keep
(
    company_id     integer,
    keep_id        serial
        constraint op_display_keep_pk
            primary key,
    oper_id        integer,
    client_id      integer,
    disp_temp_id   integer,
    disp_sheet_id  integer,
    work_content   jsonb,
    happen_time    timestamp,
    visit_id       integer,
    reviewer       integer,
    review_time    timestamp,
    review_comment text,
    review_refused bool
);

create unique index op_display_keep_keep_id_uindex
    on op_display_keep (keep_id);

create table sum_display_keep
(
    company_id      integer,
    sum_keep_id     serial
        constraint sum_display_keep_pk
            primary key,
    client_id       integer,
    disp_temp_id    integer,
    disp_sheet_id   integer,
    keep_times      integer,
    need_keep_times integer
);

create unique index sum_display_keep_sum_keep_id_uindex
    on sum_display_keep (sum_keep_id);

alter table msg_queue
    add receiver_id integer;



//2022-12-29
create table client_account_history_230101230401 partition of client_account_history for values from ('2023-01-01') to('2023-04-01');
create table sheet_sale_order_main_230101230401 partition of sheet_sale_order_main for values from ('2023-01-01') to('2023-04-01');
create table sheet_sale_order_detail_230101230401 partition of sheet_sale_order_detail for values from ('2023-01-01') to('2023-04-01');
create table sheet_buy_order_main_230101230401 partition of sheet_buy_order_main for values from ('2023-01-01') to('2023-04-01');
create table sheet_buy_order_detail_230101230401 partition of sheet_buy_order_detail for values from ('2023-01-01') to('2023-04-01');
create table sheet_sale_main_230101230401 partition of sheet_sale_main for values from ('2023-01-01') to('2023-04-01');
create table sheet_sale_detail_230101230401 partition of sheet_sale_detail for values from ('2023-01-01') to('2023-04-01');
create table sheet_buy_main_230101230401 partition of sheet_buy_main for values from ('2023-01-01') to('2023-04-01');
create table sheet_buy_detail_230101230401 partition of sheet_buy_detail for values from ('2023-01-01') to('2023-04-01');
create table sheet_move_main_230101230401 partition of sheet_move_main for values from ('2023-01-01') to('2023-04-01');
create table sheet_move_detail_230101230401 partition of sheet_move_detail for values from ('2023-01-01') to('2023-04-01');
create table sheet_inventory_main_230101230401 partition of sheet_inventory_main for values from ('2023-01-01') to('2023-04-01');
create table sheet_inventory_detail_230101230401 partition of sheet_inventory_detail for values from ('2023-01-01') to('2023-04-01');
create table sheet_invent_change_main_230101230401 partition of sheet_invent_change_main for values from ('2023-01-01') to('2023-04-01');
create table sheet_invent_change_detail_230101230401 partition of sheet_invent_change_detail for values from ('2023-01-01') to('2023-04-01');
create table sheet_get_arrears_main_230101230401 partition of sheet_get_arrears_main for values from ('2023-01-01') to('2023-04-01');
create table sheet_get_arrears_detail_230101230401 partition of sheet_get_arrears_detail for values from ('2023-01-01') to('2023-04-01');
create table sheet_fee_out_main_230101230401 partition of sheet_fee_out_main for values from ('2023-01-01') to('2023-04-01');
create table sheet_fee_out_detail_230101230401 partition of sheet_fee_out_detail for values from ('2023-01-01') to('2023-04-01');
create table sheet_visit_230101230401 partition of sheet_visit for values from ('2023-01-01') to('2023-04-01');
create table seller_trail_230101230401 partition of seller_trail for values from ('2023-01-01') to('2023-04-01');
create table sheet_prepay_230101230401 partition of sheet_prepay for values from ('2023-01-01') to('2023-04-01');
create table sheet_order_item_detail_230101230401 partition of sheet_order_item_detail for values from ('2023-01-01') to('2023-04-01');
create table sheet_item_ordered_adjust_main_230101230401 partition of sheet_item_ordered_adjust_main for values from ('2023-01-01') to('2023-04-01');
create table sheet_item_ordered_adjust_detail_230101230401 partition of sheet_item_ordered_adjust_detail for values from ('2023-01-01') to('2023-04-01');
create table display_agreement_main_230101230401 partition of display_agreement_main for values from ('2023-01-01') to('2023-04-01');
create table display_agreement_detail_230101230401 partition of display_agreement_detail for values from ('2023-01-01') to('2023-04-01');
create table sheet_cost_price_adjust_main_230101230401 partition of sheet_cost_price_adjust_main for values from ('2023-01-01') to('2023-04-01');
create table sheet_cost_price_adjust_detail_230101230401 partition of sheet_cost_price_adjust_detail for values from ('2023-01-01') to('2023-04-01');
create table stock_change_log_230101230401 partition of stock_change_log for values from ('2023-01-01') to('2023-04-01');

升级SQL
 2023-1-16:
alter table op_move_from_van_row rename column sheet_qty to need_move_qty;
ALTER TABLE op_move_from_van_row ADD COLUMN sale_qty float4;
ALTER TABLE op_move_from_van_row ADD COLUMN is_previous_move bool;
ALTER TABLE sheet_status_order ADD COLUMN back_branch_status text;
ALTER TABLE op_move_from_van_main ADD COLUMN back_done bool;
ALTER TABLE op_move_from_van_row DROP CONSTRAINT op_move_from_van_row_pkey;
UPDATE op_move_from_van_row set back_type = assign_van_type;
/*ALTER TABLE op_move_from_van_row ADD CONSTRAINT op_move_from_van_row_pkey PRIMARY KEY (company_id,op_id,sale_order_sheet_id,item_id,unit_no,back_type);已经去除该约束*/
 
create table stock_change_log_230101230401 partition of stock_change_log for values from ('2023-01-01') to('2023-04-01');
create table sheet_price_adjust_main_230101230401 partition of sheet_price_adjust_main for values from ('2023-01-01') to('2023-04-01');
create table sheet_price_adjust_detail_230101230401 partition of sheet_price_adjust_detail for values from ('2023-01-01') to('2023-04-01');


CREATE OR REPLACE FUNCTION text_to_int(str text)
 RETURNS integer
 LANGUAGE plpgsql
AS $function$ 
declare
n integer;
begin 
	  n := case when str='' then null else str::integer end;
    return n; 
end;
$function$;

	
CREATE TABLE wm_user_contract (contract_id serial PRIMARY KEY, company_id int, user_count int, 
user_count_discount1 int, user_count_discount2 int, discount1 numeric(8,2), discount2 numeric(8,2), 
duration_months int, 
start_time date, end_time date, total_amount numeric(8,2),settle_amount numeric(8,2), maker_id int, approver_id int, 
make_time timestamp, approve_time timestamp, red_flag int, give_months int, give_reason text, 
mark_brief text, appendix jsonb, sale_sheet_id int, sheet_type text, payway text)


alter table  g_company  add sale_supcust_id integer;

alter table  work_order_department add depart_supcust_id integer;

alter table  work_order_staff add sale_oper_id integer;

create table   work_order_setting(
set_name text ,
set_value text,
primary key(set_name ,set_value )

)



alter table info_display_template
    add dept_id text;
alter table info_display_template
    add sellers_id text;
alter table info_display_template
    add dept_path text;

alter table g_company add e_mall_type text;

alter table work_order_department add department_path text;

create table wx_qr_code
(
    qr_code_id serial
        constraint wx_qr_code_pk
            primary key,
    qr_scene   text,
    company_id integer,
    supcust_id integer,
    oper_id    integer,
    other_info jsonb
);

comment on column wx_qr_code.qr_scene is '客户w、零售r';

create unique index wx_qr_code_qr_code_id_uindex
    on wx_qr_code (qr_code_id);

-- 添加费用支出，具体到月份
alter table sheet_fee_out_detail
    add display_month timestamp;

create table mall_page_template
(
    company_id       integer,
    template_id      serial
        constraint mall_page_template_pk
            primary key,
    template_content jsonb,
    page_type        text,
    template_name text
);
create unique index mall_page_template_template_id_uindex
    on mall_page_template (template_id);

create table mall_page_template_department_map
(
    company_id    integer,
    template_id   integer,
    department_id integer,
    constraint mall_page_template_department_map_pk
        primary key (company_id, template_id, department_id)
);
create table mall_main_page_content
(
    company_id      integer,
    content_id      serial
        constraint mall_main_page_content_pk
            primary key,
    department_id   integer,
    swiper_images   jsonb,
    often_class     jsonb,
    recommend_items jsonb
);

create unique index mall_main_page_content_content_id_uindex
    on mall_main_page_content (content_id);

create table mall_setting
(
    company_id             integer not null
        constraint mall_setting_pk
            primary key,
    mall_type              text,
    default_branch_id      integer,
    enable_branch          boolean,
    branch_map             jsonb,
    qr_code_enable_expired boolean,
    qr_code_alive_time     integer,
    mini_app_id            text,
    mini_app_secret        text,
    mini_app_private       boolean
);

create table mall_items
(
    company_id  integer,
    item_id     integer,
    class_id    integer,
    other_class text,
    on_sale     bool,
    images      jsonb,
    item_desc   text,
    constraint mall_items_pk
        primary key (company_id, item_id)
);

create table mall_class
(
    company_id         integer not null,
    class_id           serial,
    class_name         text,
    service_flag       integer,
    cls_type           integer,
    brand_id           integer,
    mother_id          integer,
    order_index        integer,
    cls_status         text,
    py_str             text,
    ignore_stock_class boolean,
    general_class      text,
    constraint pk_mall_class
        primary key (company_id, class_id)
);

/*发货单*/
CREATE TABLE sheet_delivery_main (
	company_id INTEGER NOT NULL,
	sheet_id serial primary key not null  ,
	sheet_no TEXT NOT NULL,
	sheet_type TEXT,
	red_flag SMALLINT,
	oper_id INTEGER,
	van_id INTEGER,
	happen_time TIMESTAMP,
	approver_id INTEGER,
	approve_time TIMESTAMP,
	make_brief TEXT
) ;


CREATE TABLE sheet_delivery_detail (
	company_id int4 NOT NULL,
	flow_id serial NOT NULL primary key,
	row_index int4,
	sheet_id INTEGER,
	op_id INTEGER NOT NULL,
	op_type TEXT,
  	op_time TIMESTAMP,
	happen_time TIMESTAMP,
	senders_id text,
	senders_name text,
	from_branch_id int4,
	to_van_id int4,
	remark TEXT,
	sale_order_sheet_ids text
)


CREATE TABLE "public"."info_item_world" (
  "id" int4 NOT NULL DEFAULT nextval('info_item_world_id_seq'::regclass),
  "sbarcode" text COLLATE "pg_catalog"."default" NOT NULL CONSTRAINT "info_item_world_pkey"  PRIMARY KEY,
  "mbarcode" text COLLATE "pg_catalog"."default",
  "bbarcode" text COLLATE "pg_catalog"."default",
  "sunit" text COLLATE "pg_catalog"."default",
  "munit" text COLLATE "pg_catalog"."default",
  "bunit" text COLLATE "pg_catalog"."default",
  "bfactor" int4,
  "mfactor" int4,
  "sfactor" int4,
  "item_name" text COLLATE "pg_catalog"."default",
  "item_images" jsonb,
  "update_time" timestamp(6),
  "image_judge" jsonb
)
;
alter table op_display_maintain add for_month timestamp;
CREATE unique index idx_sum_display_maintain_uindex ON sum_display_maintain USING btree (company_id, client_id, disp_sheet_id, months);

idx_sum_display_maintain_uindex
-- region 未执行  2023/04/11
create index mall_class_company_id_mother_id_index on mall_class (company_id, mother_id);
alter table mall_page_template add department_id integer;
create unique index info_cust_contact_company_id_supcust_id_wx_user_id_uindex on info_cust_contact (company_id, supcust_id, wx_user_id);
create unique index mall_page_template_department_map_uindex_2 on mall_page_template_department_map (company_id, department_id);
alter table info_cust_contact add oper_id integer;
alter table info_cust_contact add approver_id integer;
alter table info_cust_contact add approve_time timestamp;
alter table info_cust_contact add apply_time timestamp;
alter table info_cust_contact add approve_status bool;
alter table mall_setting add new_client_approve boolean;
alter table info_cust_contact add is_default bool;
alter table mall_setting add use_mall_item_class boolean;
comment on column mall_setting.use_mall_item_class is 'true启用商城分类';
-- endregion


CREATE TABLE commission_strategy (
company_id int not null,
strategy_id SERIAL PRIMARY KEY,
strategy_name text not null,
worker_type text not null,
constraint commission_strategy_name
    unique (company_id,strategy_name)
);



CREATE TABLE commission_strategy_client (
company_id int not null,
flow_id SERIAL PRIMARY KEY,
strategy_id int not null,
supcust_id int not null UNIQUE,
plan_id int not null
);



CREATE TABLE commission_strategy_class (
company_id int not null,
flow_id SERIAL PRIMARY KEY,
strategy_id int not null,
group_id int,
rank_id int,
region_id int,
plan_id int not null,
constraint idx_u_commission_plan_class
        unique (company_id,strategy_id,group_id,rank_id,region_id)
);

 


CREATE TABLE commission_strategy_map (
company_id int not null,
flow_id SERIAL PRIMARY KEY,
worker_id int not null,
strategy_id int not null, 
constraint idx_u_commission_strategy_map
        unique (company_id,worker_id,strategy_id)
);

-- 云打印日志功能
-- MAIN TABLE
CREATE TABLE log_cloudprint (flow_id serial, company_id int4, happen_time timestamp, oper_id int4, sheet_id int4, sheet_type text, device_id text, post_result text) PARTITION BY range(happen_time);

-- PARTITIONS
CREATE TABLE log_cloudprint_23072308 PARTITION OF log_cloudprint FOR VALUES FROM ('2023-07-01') TO ('2023-08-01');

-- work_order_info 添加新列 start_time
alter table work_order_info
    add start_time timestamp;

-- 支付
CREATE TABLE pay_channel (
  "channel_id" Serial NOT NULL PRIMARY KEY,
  "channel_name" text NOT NULL
)

create table info_pay_qrcode(company_id integer not null,qrcode_id serial, sub_id integer,qrcode_uri text);
ALTER TABLE info_pay_qrcode ADD COLUMN "pay_channel_id" int4;
alter table info_pay_qrcode add column bank_no text;
alter table info_pay_qrcode add column bank_name text;
alter table info_pay_qrcode add column remark text;

CREATE TABLE sheet_cashbank_transfer_main (
    "company_id" int4 NOT NULL,
    "sheet_id" serial,
    "sheet_no" text,
    "sheet_type" text,
    "seller_id" int4,
    "red_flag" int2,
    "red_sheet_id" int4,
    "red_sheet_date" timestamp,
    "happen_time" timestamp,
    "maker_id" int4,
    "make_time" timestamp,
    "approver_id" int4,
    "approve_time" timestamp,
    "approve_brief" text,
    "make_brief" text,
    "sheet_attribute" jsonb,
    "submit_time" timestamp
) partition by range(happen_time);
create table sheet_cashbank_transfer_main_23122401 partition of sheet_cashbank_transfer_main for values from ('2023-12-01 00:00:00') to ('2024-03-31 23:59:59');

create table sheet_cashbank_transfer_detail(
    "company_id" int4,
    "sheet_id" int4,
    "row_index" int4,
    "money_out_id" int4,
    "money_in_id" int4,
    "fee_id" int4,
    "out_amount_withfee" float8,
    "out_amount_withoutfee" float8,
    "fee_amount" float8,
    "in_amount" float8,
    "happen_time" timestamp,
    "remark" text,
    "inout_flag" int2
) partition by range(happen_time);
create table sheet_cashbank_transfer_detail_23122401 partition of sheet_cashbank_transfer_detail for values from ('2023-12-01 00:00:00') to ('2024-03-31 23:59:59');


CREATE TABLE cashbank_balance_init (
    "company_id" int4 NOT NULL,
    "qrcode_id" int4,
    "sub_id" int4,
    "period" date,
    "balance" float8,
    "remark" text,
    PRIMARY KEY ("company_id", "qrcode_id", "sub_id", "period")
);

CREATE TABLE cashbank_balance (
    "company_id" int4 NOT NULL,
    "period" date,
    "qrcode_id" int4,
    "sub_id" int4 not null,
    "h_month_start_balance" float8,
    "h_in_amount" float8,
    "h_out_amount" float8,
    "h_month_end_balance" float8,
    "m_month_start_balance" float8,
    "m_in_amount" float8,
    "m_out_amount" float8,
    "m_month_end_balance" float8,
    "a_month_start_balance" float8,
    "a_in_amount" float8,
    "a_out_amount" float8,
    "a_month_end_balance" float8,
    primary key(company_id,qrcode_id,sub_id,period)
) partition by hash(company_id);

CREATE TABLE "cashbank_balance_0" PARTITION OF cashbank_balance FOR VALUES WITH(MODULUS 10, REMAINDER 0);
CREATE TABLE "cashbank_balance_1" PARTITION OF cashbank_balance FOR VALUES WITH(MODULUS 10, REMAINDER 1);
CREATE TABLE "cashbank_balance_2" PARTITION OF cashbank_balance FOR VALUES WITH(MODULUS 10, REMAINDER 2);
CREATE TABLE "cashbank_balance_3" PARTITION OF cashbank_balance FOR VALUES WITH(MODULUS 10, REMAINDER 3);
CREATE TABLE "cashbank_balance_4" PARTITION OF cashbank_balance FOR VALUES WITH(MODULUS 10, REMAINDER 4);
CREATE TABLE "cashbank_balance_5" PARTITION OF cashbank_balance FOR VALUES WITH(MODULUS 10, REMAINDER 5);
CREATE TABLE "cashbank_balance_6" PARTITION OF cashbank_balance FOR VALUES WITH(MODULUS 10, REMAINDER 6);
CREATE TABLE "cashbank_balance_7" PARTITION OF cashbank_balance FOR VALUES WITH(MODULUS 10, REMAINDER 7);
CREATE TABLE "cashbank_balance_8" PARTITION OF cashbank_balance FOR VALUES WITH(MODULUS 10, REMAINDER 8);
CREATE TABLE "cashbank_balance_9" PARTITION OF cashbank_balance FOR VALUES WITH(MODULUS 10, REMAINDER 9);

CREATE TABLE cashbank_balance (
"company_id" int4 NOT NULL,
"sub_id" int4 NOT NULL,
"balance" float8,
primary key(company_id,sub_id)
);

CREATE TABLE cashbank_balance_log (
"company_id" int4 NOT NULL,
"sub_id" int4,
"oper_id" int4,
"make_time" timestamp,
"amount" float8,
"sheet_type" text,
"sheet_id" int4,
"log" text
) PARTITION BY RANGE (
"make_time" "pg_catalog"."timestamp_ops"
);
create table cashbank_balance_log_23122403 partition of cashbank_balance_log for values from ('2023-12-01') to ('2024-03-31 23:59:59'); 


CREATE TABLE pay_merchant (
	"company_id" INT4 NOT NULL,
    "channel_id" INT4 NOT NULL,
    "merchant_id" text,
	"merchant_name" text
);

CREATE TABLE pay_bill (
    "bill_id" Serial NOT NULL,
    "company_id" INT4 NOT NULL,
    "create_time" timestamp,
    "pay_time" timestamp,
    "return_time" timestamp,
	"bill_status" text,
	"amount" float4,
	"sheet_type" text,
	"sheet_id" int4,
	"pay_channel" int4,
	"pay_qrcode" text 
) PARTITION BY range(create_time);

CREATE INDEX idx_pay_bill_bill_id ON pay_bill(bill_id);
CREATE INDEX idx_pay_bill_create_time ON pay_bill(create_time);
CREATE TABLE pay_bill_23072309 PARTITION OF pay_bill FOR VALUES FROM ('2023-08-01') TO ('2023-12-01');

-- 小程序添加不同类型公码
alter table info_cust_contact add approve_retail_wholesale_flag text;
drop index info_cust_contact_company_id_supcust_id_contact_mobile_wx_user_;
drop index info_cust_contact_company_id_supcust_id_wx_user_id_uindex;

-- 红包
CREATE TABLE red_packet_setting (
    company_id int4 NOT NULL,
    register_reward numeric NOT NULL,
    purchase_reward numeric NOT NULL,
    use_limit_type text,
    use_limit_amount numeric NOT NULL,
    PRIMARY KEY (company_id)
);
CREATE TABLE red_packet_balance (
    company_id int4 NOT NULL,
    supcust_id int4 NOT NULL,
    balance numeric,
    PRIMARY KEY (supcust_id)
);
CREATE TABLE red_packet_history (
    flow_id serial,
    company_id int4 NOT NULL,
    supcust_id int4 NOT NULL,
    happen_time timestamp NOT NULL,
    change_amount numeric,
    change_type text,
    relate_sheet_id int4,
    relate_sheet_type text,
    PRIMARY KEY (flow_id)
);
drop index info_cust_contact_company_id_supcust_id_wx_user_id_uindex;

CREATE TABLE oper_setting(
  "company_id" int4 NOT NULL,
  "oper_id" int4 primary key,
  "app_setting" jsonb,
  "pc_setting" jsonb
);

-- 交账单
alter table sheet_check_sheets_main
  add sale_feeout_total_amount double precision;

alter table sheet_check_sheets_main
  add fee_out_total_amount double precision;

comment on column sheet_check_sheets_main.fee_out_total_amount is '费用支出总额';

alter table sheet_check_sheets_main
  add fee_out_left_amount double precision;

comment on column sheet_check_sheets_main.fee_out_left_amount is '费用支出欠款';

alter table sheet_check_sheets_main
  add fee_out_disc_amount double precision;

comment on column sheet_check_sheets_main.fee_out_disc_amount is '费用支出优惠';

alter table sheet_check_sheets_main
  add check_account_payway1_id integer;

alter table sheet_check_sheets_main
  add check_account_payway1_amount double precision;

alter table sheet_check_sheets_main
  add check_account_payway2_id integer;

alter table sheet_check_sheets_main
  add check_account_payway2_amount integer;

alter table sheet_check_sheets_main
  add reder_id integer;

alter table sheet_check_sheets_main
  add red_time timestamp;

alter table sheet_check_sheets_main
  add arrears_total_amount double precision;

alter table sheet_check_sheets_main
  add credential jsonb;


  CREATE TABLE document_change_log (flow_id serial, company_id int NOT NULL, obj_id int NOT NULL, obj_name text NOT NULL, diff_describe jsonb, oper_id int, happen_time timestamp, approve_status text, approve_brief text, approver_id int, approve_time timestamp, oper_action text)





 
alter table sheet_check_sheets_main add column sale_feeout_total_amount double precision;
 

alter table sheet_check_sheets_main add column fee_out_total_amount double precision;

comment on column sheet_check_sheets_main.fee_out_total_amount is '费用支出总额';

alter table sheet_check_sheets_main add column fee_out_left_amount double precision;

comment on column sheet_check_sheets_main.fee_out_left_amount is '费用支出欠款';

alter table sheet_check_sheets_main add column fee_out_disc_amount double precision;

comment on column sheet_check_sheets_main.fee_out_disc_amount is '费用支出优惠';

alter table sheet_check_sheets_main
  add column check_account_payway1_id integer;

alter table sheet_check_sheets_main
  add column check_account_payway1_amount double precision;

alter table sheet_check_sheets_main
  add column check_account_payway2_id integer;

alter table sheet_check_sheets_main
  add column check_account_payway2_amount double precision;

alter table sheet_check_sheets_main
  add column reder_id integer;

alter table sheet_check_sheets_main
  add column red_time timestamp;

alter table sheet_check_sheets_main
  add column arrears_total_amount double precision;

alter table sheet_check_sheets_main
  add column credential jsonb;



CREATE OR REPLACE FUNCTION "public"."yj_parse_int"("txtstr" text)
  RETURNS "pg_catalog"."int8" AS $BODY$ 
BEGIN 
    
if( txtStr ~ '^([0-9]+[.]?[0-9]*|[.][0-9]+)$') then
   return txtstr::int8;
 else 
   return 0;
end if;

       
END; 
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100

-- 2023.09.22 支付功能改造
-- 支付渠道表添加字段[布尔:是否可用于二维码支付]和[布尔:是否可用于小程序微信支付]
ALTER TABLE "pay_channel" 
  ADD COLUMN "can_qrcode_pay" bool,
  ADD COLUMN "can_miniapp_pay" bool;
-- 支付订单表添加字段[文本:银行端的支付单号]
ALTER TABLE "pay_bill" 
  ADD COLUMN "trade_no" text;
-- 2023.10.08 支付功能改造
-- MERCHANT表设置主键以便之后INSERT-ON-CONFLICT
ALTER TABLE "pay_merchant" 
  ADD PRIMARY KEY ("company_id", "channel_id");
-- 2023.10.11 销售单/销售订单添加字段,关联到最新的支付订单
ALTER TABLE "sheet_sale_main" 
  ADD COLUMN "pay_bill_id" int4;
ALTER TABLE sheet_sale_order_main 
  ADD COLUMN "pay_bill_id" int4;
-- 2023.10.17 促销活动添加字段,限制活动范围为线上/线下
ALTER TABLE strategy_promotion
  ADD COLUMN promotion_scope text;

-- 2023.11.20 促销活动的限时特价添加限制表
CREATE TABLE "promotion_seckill_limit" (
  "promotion_id" int4 NOT NULL,
  "item_id" int4 NOT NULL,
  "limit_type" text NOT NULL,
  "limit_qty" numeric,
  PRIMARY KEY ("promotion_id", "item_id", "limit_type")
);
CREATE TABLE "promotion_seckill_count" (
  "promotion_id" int4 NOT NULL,
  "item_id" int4 NOT NULL,
  "client_id" int4 NOT NULL, -- 0 represents global
  "limit_qty" numeric,
  "used_qty" numeric,
  PRIMARY KEY ("promotion_id", "item_id", "client_id")
)

-- 2023.12.01 促销活动的限时特价添加历史表
CREATE TABLE promotion_seckill_history (
    flow_id serial,
    happen_time timestamp NOT NULL,
    company_id int4 NOT NULL,
    promotion_id int4 NOT NULL,
    item_id int4 NOT NULL,
    client_id int4 NOT NULL,
    change_amount numeric,
    change_type text,
    relate_sheet_id int4,
    relate_sheet_type text,
    PRIMARY KEY (flow_id)
);

--采购订单分批次转采购单，添加字段
ALTER TABLE sheet_buy_order_main ADD COLUMN buy_order_status text;
ALTER TABLE sheet_buy_order_detail ADD COLUMN done_qty float4;
ALTER TABLE sheet_buy_detail ADD COLUMN order_qty float4;
-- 添加json类型商城设置
alter table mall_setting add mall_mini_setting jsonb;

--销售订单增加上次售价字段
ALTER TABLE sheet_sale_order_detail ADD COLUMN last_time_price float4

-- 交账单收欠款，使用预收款
alter table sheet_check_sheets_main add arrears_prepay_amount decimal;

-- 微信第三方平台(未提交)
create table wx_open_platform
(
    company_id               integer   not null,
    authorizer_mini_appid    text,
    authorizer_refresh_token text,
    mini_start_time          timestamp,
    mini_expire_time         timestamp not null,
    register_status_code     integer,
    register_time            timestamp,
    auth_status              text,
    auth_status_time         timestamp,
    enterprise_name          text,
    enterprise_code          text,
    enterprise_code_type     integer,
    legal_person_wechat      text,
    legal_person_name        text
);

comment on table wx_open_platform is '微信第三方平台';

comment on column wx_open_platform.authorizer_mini_appid is '商家的app_id';

comment on column wx_open_platform.authorizer_refresh_token is '商家的刷新令牌';

comment on column wx_open_platform.mini_start_time is '启用时间';

comment on column wx_open_platform.mini_expire_time is '到期时间';

comment on column wx_open_platform.register_status_code is '小程序注册状态';

comment on column wx_open_platform.register_time is '注册成功时间';

comment on column wx_open_platform.auth_status is '小程序授权状态';

comment on column wx_open_platform.auth_status_time is '授权操作时间';

comment on column wx_open_platform.enterprise_name is '企业名';

comment on column wx_open_platform.enterprise_code is '企业代码';

comment on column wx_open_platform.enterprise_code_type is '企业代码类型';

comment on column wx_open_platform.legal_person_wechat is '法人微信';

comment on column wx_open_platform.legal_person_name is '法人姓名';



-- 二级分销

CREATE TABLE rs_plan (
  "company_id" int4 NOT NULL,
  "plan_id" serial,
  "plan_name" text NOT NULL,
  "brand_id" text ,
  "brand_name" text ,
  "sheet_sync" bool NOT NULL,
  "share" bool NOT NULL
)
;

CREATE TABLE rs_seller (
  "company_id" int4 NOT NULL,
  "reseller_id" serial,
  "reseller_company_id" int4,
  "reseller_name" text ,
  "reseller_count" int4 NOT NULL,
  "reseller_mobile" text NOT NULL,
  "plan_id" int4,
  "client_id" int4,
  "supplier_id" int4,
  "company_name" text 
)
;

ALTER TABLE sheet_buy_main add COLUMN order_source text;
ALTER TABLE sheet_buy_order_main add COLUMN order_source text;

ALTER TABLE info_item_brand add COLUMN rs_mum_id int4;
ALTER TABLE info_item_prop add COLUMN rs_mum_id int4;

-- 小程序绑定员工档案
alter table wx_qr_code add mini_oper_id integer;
comment on column wx_qr_code.mini_oper_id is '小程序绑定的员工档案';
-- 员工绑定表
create table info_oper_contact
(
    company_id        integer,
    mini_oper_id      integer,
    contact_id        serial not null
        constraint info_oper_contact_pk
            primary key,
    contact_nick_name text,
    avatar_url text,
    contact_mobile    text,
    wx_user_id        integer,
    oper_id           integer,
    apply_time        timestamp,
    is_default        bool
);
create unique index idx_uni_info_oper_contact
    on info_oper_contact (company_id, wx_user_id);

-- 创建前置仓表
create table front_branch
(
    company_id        integer not null,
    front_branch_id   serial,
    front_branch_name text,
    branches_id       text,
    regions_id        text,
    dept_id           integer
);
alter table front_branch add constraint front_branch_pk primary key (front_branch_id);

alter table mall_class add front_branch_id integer;
alter table mall_page_template add front_branch_id integer;
alter table mall_items add front_branch_id integer;
alter table mall_items drop constraint mall_items_pk;

alter table mall_items add constraint mall_items_pk primary key (company_id, item_id, class_id);









CREATE TABLE sheet_buy_price_adjust_main (
 company_id INTEGER NOT NULL,
 sheet_id serial primary key,
 sheet_no TEXT NOT NULL,
 sheet_type TEXT,
 red_flag SMALLINT,
 red_sheet_id INTEGER,
 supcust_id INTEGER,
 maker_id INTEGER,
 make_time TIMESTAMP,
 happen_time TIMESTAMP,
 approver_id INTEGER,
 approve_time TIMESTAMP,
 seller_id INTEGER,
 make_brief TEXT,
approve_brief TEXT,
submit_time TIMESTAMP

);
CREATE INDEX idx_sheet_buy_price_adjust_main_company_id ON sheet_buy_price_adjust_main(company_id);
 


CREATE TABLE sheet_buy_price_adjust_detail (
 company_id int4 NOT NULL,
 flow_id serial NOT NULL,
 row_index int4,
 sheet_id INTEGER,
 item_id INTEGER NOT NULL,
 s_price FLOAT4,
 m_price FLOAT4,
 b_price FLOAT4,
 s_old_price FLOAT4,
 m_old_price FLOAT4,
 b_old_price FLOAT4,
 happen_time TIMESTAMP,
 remark TEXT,
inout_flag int4
)  
CREATE INDEX idx_sheet_buy_price_adjust_detail_company_id ON sheet_buy_price_adjust_detail(company_id);
CREATE INDEX idx_sheet_buy_price_adjust_detail_sheet_id ON sheet_buy_price_adjust_detail(sheet_id);

CREATE TABLE "sheet_label_print_main"(
    sheet_id serial,
    sheet_no text NOT NULL,
    sheet_type text NOT NULL,
    company_id int4 NOT NULL,
    red_flag int2,
    red_sheet_id int4,
    maker_id int4,
    approver_id int4,
    make_brief text,
    approve_brief text,
    make_time timestamp,
    happen_time timestamp,
    approve_time timestamp,
    submit_time timestamp
);
CREATE TABLE "sheet_label_print_detail"(
    flow_id serial,
    sheet_id int4,
    company_id int4 NOT NULL,
    happen_time timestamp,
    -- sheetBase
    inout_flag int2,
    row_index int4,
    remark text,
    -- sheetRowItem
    item_id int4,
    branch_id int4,
    unit_no text,
    unit_factor float4,
    quantity float4,
    -- sheetMM
    orig_price float4,
    real_price float4,
    sys_price float4,
    sub_amount float4,
    remark_id int4,
    virtual_produce_date text,
    cost_price_avg float4
);

CREATE TABLE buy_item_price_adjust(
		 company_id int4 NOT NULL,
		 sheet_id INTEGER,
			item_id INTEGER NOT NULL PRIMARY KEY,
			last_adjust_time TIMESTAMP
)


CREATE INDEX buy_item_price_adjust_company_id
ON buy_item_price_adjust  (company_id );


CREATE INDEX buy_item_price_adjust_sheet_id
ON buy_item_price_adjust  (sheet_id  );

CREATE INDEX buy_item_price_adjust_item_id 
ON buy_item_price_adjust  (item_id  );




create table if not exists public.sheet_move_arrears_bill_detail
(
    company_id          integer,
    sheet_id            integer,
    flow_id             serial,
    happen_time         timestamp(6),
    business_sheet_no   text,
    business_sheet_id   integer,
    orig_amount         double precision,
    left_amount         double precision,
    business_sheet_type text,

    row_index           integer,
    inout_flag          integer,
    remark              text,
    bill_id             integer,
    keeper_id           integer
);
create index idx_sheet_move_arrears_bill_detail on sheet_move_arrears_bill_detail(sheet_id);

 
	
	create table if not exists public.sheet_move_arrears_bill_main
(
    company_id      integer,
    sheet_id        serial,
    sheet_no        text,
    sheet_type      text,

    worker_id       integer,
    make_brief      text,
    total_amount    double precision,
    sheet_attribute jsonb,

    red_flag        smallint,
    red_sheet_id    integer,
    make_time       timestamp(6),
    maker_id        integer,
    approve_time    timestamp(6),
    approver_id     integer,
    happen_time     timestamp(6),
    approve_brief   text,
    submit_time     timestamp(6),
    primary key(company_id,sheet_id)
);

	
	
	CREATE TABLE "public"."arrears_bill" (
  "bill_id" serial,
  "business_sheet_type" text COLLATE "pg_catalog"."default",
  "business_sheet_id" int4,
  "business_sheet_no" text COLLATE "pg_catalog"."default",
  "supcust_id" int4,
  "supcust_name" text COLLATE "pg_catalog"."default",
  "orig_amount" float8,
  "left_amount" float8,
  "keeper_id" int4,
  "out_company" bool,
  "arrears_status" text COLLATE "pg_catalog"."default",
  "company_id" int4 NOT NULL,
  CONSTRAINT "arrears_bill_pkey" PRIMARY KEY ("company_id", "bill_id")
)
;

-- 增加商品属性组合字段
alter table info_item_prop add avail_attr_combine jsonb;
-- 增加头像、微信小程序昵称
alter table wx_user add avatar_url text;
alter table wx_user add mini_nick_name text;

alter table info_item_prop add mall_status integer;

-- 2024.05 收银系统POS机信息表
CREATE TABLE info_pos (
  "pos_id" serial,
  "company_id" int4 NOT NULL,
  "pos_name" text NOT NULL,
  "branch_id" int4 NOT NULL,
  "mac_addrs" text NOT NULL,
  "create_time" timestamp NOT NULL,
  "duty_id" int4,
  PRIMARY KEY ("pos_id")
);


CREATE TABLE "public"."sheet_get_arrears_order_main" (
  "company_id" int4 NOT NULL,
  "sheet_id" serial NOT NULL ,
  "sheet_no" text COLLATE "pg_catalog"."default",
  "sheet_type" text COLLATE "pg_catalog"."default",
  "supcust_id" int4,
  "money_inout_flag" int2,
  "sheet_amount" float8,
  "disc_amount" float4,
  "paid_amount" float4,
  "left_amount" float4,
  "approve_flag" int2,
  "red_flag" int2,
  "red_sheet_id" int4,
  "maker_id" int4,
  "make_time" timestamp(6),
  "approver_id" int4,
  "approve_time" timestamp(6),
  "happen_time" timestamp(6),
  "getter_id" int4,
  "payway1_id" int4,
  "payway1_amount" float8,
  "payway2_id" int4,
  "payway2_amount" float8,
  "payway3_id" int4,
  "payway3_amount" float8,
  "make_brief" text COLLATE "pg_catalog"."default",
  "submit_time" timestamp(6),
  "now_pay_amount" float8,
  "now_disc_amount" float8,
  "visit_id" int4,
  "is_imported" bool,
  "sheet_attribute" jsonb,
  "approve_brief" text COLLATE "pg_catalog"."default",
  "real_left_amount" float8,
  "apply_flag" int2
)
;


CREATE TABLE "public"."sheet_get_arrears_order_detail" (
  "company_id" int4 NOT NULL,
  "sheet_id" int4,
  "mm_sheet_id" int4,
  "inout_flag" int2,
  "row_index" int4,
  "sheet_amount" float8,
  "paid_amount" float8,
  "disc_amount" float8,
  "now_pay_amount" float8,
  "now_disc_amount" float8,
  "happen_time" timestamp(6),
  "remark" text COLLATE "pg_catalog"."default",
  "m_sheet_type" text COLLATE "pg_catalog"."default",
  "left_amount" float8
)
;


--采购费用分摊单
CREATE TABLE sheet_fee_apportion_main(
"company_id" int4 NOT NULL,
"sheet_id" serial,
"sheet_no" text,
"sheet_type" text,
"red_flag" int2,
"red_sheet_id" int4,
"happen_time" timestamp,
"seller_id" int4,
"maker_id" int4,
"make_time" timestamp,
"approver_id" int4,
"approve_time" timestamp,
"approve_brief" text,
"reviewer_id" int4,
"submit_time" timestamp,
"make_brief" text,
"sheet_attribute" jsonb
);

create table sheet_fee_apportion_detail(
"company_id" int4 not null,
"sheet_id" int4,
"flow_id" serial NOT NULL,
"row_index" int4,
"happen_time" timestamp,
"relate_sheet_type" text,
"relate_sheet_id" int4
);


-- 送货地址升级
alter table info_client_address add receiver_name text;

alter table info_client_address add receiver_mobile text;

alter table info_client_address add addr_status int2;

ALTER TABLE info_operator ADD COLUMN avail_pay_ways json, ADD COLUMN restrict_pay_ways boolean

ALTER TABLE sheet_sale_main ADD COLUMN cost_amount_avg decimal;
ALTER TABLE sheet_sale_main ADD COLUMN cost_amount_buy decimal;
ALTER TABLE sheet_sale_main ADD COLUMN cost_amount_recent decimal;
ALTER TABLE sheet_sale_main ADD COLUMN cost_amount_prop decimal;

 
ALTER TABLE sheet_sale_order_main ADD COLUMN cost_amount_avg decimal;
ALTER TABLE sheet_sale_order_main ADD COLUMN cost_amount_buy decimal;
ALTER TABLE sheet_sale_order_main ADD COLUMN cost_amount_recent decimal;
ALTER TABLE sheet_sale_order_main ADD COLUMN cost_amount_prop decimal;
ALTER TABLE info_item_prop ADD approaching_days DOUBLE PRECISION;
ALTER TABLE info_sheet_detail_brief ADD COLUMN default_for_give BOOLEAN DEFAULT false;

-- 增加小程序绑定渠道来源
alter table info_cust_contact add bind_way text;


CREATE TABLE IF NOT EXISTS arrears_strategy_class (
    company_id INT NOT NULL,
    flow_id  SERIAL PRIMARY KEY,
    group_id INT,
    region_id INT,
    rank_id INT,
    max_arrears INT,
    max_arrears_day INT
);
create index idx_arrears_strategy_class_group_id on arrears_strategy_class(company_id,group_id);
create index idx_arrears_strategy_class_region_id on arrears_strategy_class(company_id,region_id);
create index idx_arrears_strategy_class_rank_id on arrears_strategy_class(company_id,rank_id);


CREATE TABLE IF NOT EXISTS arrears_strategy_client (
    company_id INT NOT NULL,
    flow_id  SERIAL  PRIMARY KEY,
    supcust_id INT,
    max_arrears INT,
    max_arrears_day INT
);
create index idx_arrears_strategy_client_supcust_id on arrears_strategy_client(company_id,supcust_id);

CREATE TABLE IF NOT EXISTS arrears_strategy_operator (
    company_id INT NOT NULL,
    flow_id  SERIAL  PRIMARY KEY,
    oper_id INT,
    max_arrears INT,
    max_arrears_days INT
);
create index idx_arrears_strategy_operator_oper_id on arrears_strategy_operator(company_id,oper_id);





-- 会员积分 ↓
create table vip_level
(
    company_id          integer not null,
    vip_level_id        serial,
    vip_level_order     integer,
    vip_level_name      text,
    upgrade_points      numeric(10, 2),
    upgrade_money       numeric(10, 2),
    vip_card_face_style jsonb,
    create_time         timestamp,
    creator             integer,
    update_time         timestamp,
    updater             integer,
    constraint vip_card_level_pk primary key (company_id, vip_level_id)
);


create table vip_plan
(
    company_id               integer not null,
    vip_plan_id              serial  not null,
    vip_plan_name            text,
    can_point                boolean,
    can_store_value          boolean,
    point_with_red_packet    boolean,
    point_with_full_gift     boolean,
    point_with_full_reduce   boolean,
    point_wit_pay_use_point  boolean,
    point_with_combine_item  boolean,
    point_with_seckill       boolean,
    max_give_value_per_sheet text,
    points_to_1yuan        numeric(10, 2),
    point_expire_days    integer,
    create_time              timestamp,
    creator                  integer,
    update_time              timestamp,
    updater                  integer,
    delete_time              timestamp,
    deleter                  integer,
    constraint vip_plan_pk   primary key (company_id, vip_plan_id)
);


create table vip_plan_point
(
    company_id           integer,
    vip_plan_id          integer,
    flow_id              serial,
    brand_id             integer,
    class_id             integer,
    item_id              integer,
    yuan_to_1point       numeric(10, 2),
    b_unit_qty_to_1point     numeric(10, 2),
    m_unit_qty_to_1point     numeric(10, 2),
    s_unit_qty_to_1point     numeric(10, 2),
   
    create_time          timestamp,
    creator              integer,
    update_time          timestamp,
    updater              integer,
    delete_time          timestamp,
    deleter              integer,
    constraint vip_plan_point_pk    primary key (company_id, flow_id)
);


create table vip_plan_redeemable
(
    company_id     integer not null,
    vip_plan_id    integer,
    flow_id        serial  not null,
    redeem_type    text,
    redeem_item_id integer,
    redeem_item_unit text,
    redeem_point   numeric(10, 2),
    redeem_price   numeric(10, 2),
    create_time    timestamp,
    creator        integer,
    update_time    timestamp,
    updater        integer,
    delete_time    timestamp,
    deleter        integer,
    constraint vip_plan_redeemable_pk primary key (company_id, flow_id)
);


create table vip_strategy
(
    company_id        integer not null,
    vip_strategy_id   serial  not null,
    vip_class_id      integer,
    vip_level_id      integer,
    retail_whole_flag text,
    order_source      text,
    groups_id         integer,
    ranks_id          integer,
    regions_id        integer,
    vip_plan_id       integer,
    create_time       timestamp,
    creator           integer,
    update_time       timestamp,
    updater           integer,
    delete_time       timestamp,
    deleter           integer,
    constraint vip_strategy_pk
        primary key (company_id, vip_strategy_id)
);


create table vip_card
(
    company_id         integer not null,
    vip_card_id        serial  not null,
    vip_class_id       integer,
    vip_card_no        text,
    vip_card_pwd       text,
    vip_card_status    integer,
    client_id          integer,
    apply_way          text,
    vip_level_id       integer,
    init_value         numeric(10, 2),
    init_give_value    numeric(10, 2),
    value_balance      numeric(10, 2),
    give_value_balance numeric(10, 2),
    points_balance     numeric(10, 2),
    history_points     numeric(10, 2),
    wx_user_id         integer,
    oper_id            integer,
    create_time        timestamp,
    update_time        timestamp,
    constraint vip_card_info_pk primary key (vip_card_id, company_id)
);
create index idx_vip_card_client_id on vip_card (client_id);

create table vip_point_gain_detail
(
    company_id        integer not null,
    flow_id           serial  not null,
    vip_card_id       integer,
    client_id         integer,
    wx_user_id        integer,
    sheet_id          integer,
    points_type       text,
    init_points       numeric(10, 2),
    init_points_desc  jsonb,
    pend_points       numeric(10, 2),
    point_balance     numeric(10, 2),
    point_avail_balance     numeric(10, 2),
    status            integer,
    create_time       timestamp,
    point_expire_time timestamp,
    update_time       timestamp,
    constraint vip_point_gain_detail_pk  primary key (company_id, flow_id)
);


create table vip_point_change_log
(
    company_id          integer not null,
    flow_id             serial  not null,
    vip_card_id         integer,
    client_id           integer,
    wx_user_id          integer,
    retail_whole_flag   text,
    order_source        text,
    point_group_id      integer,
    point_group_total   numeric(10, 2),
    gain_detail_flow_id integer,
    point_change_type   text,
    point_to_money      numeric(10, 2),
    change_points       numeric(10, 2),
    description         jsonb,
    redeem_flow_id      integer,
    create_time         timestamp,
    sheet_type          text,
    sheet_id            integer,
    pre_points          numeric(10, 2),
    cur_points          numeric(10, 2),
    pre_pend_points     numeric(10, 2),
    cur_pend_points     numeric(10, 2),
    constraint vip_point_change_log_pk
        primary key (company_id, flow_id)
);

-- 创建相关的序列, 组id使用
CREATE SEQUENCE vip_point_change_log_point_group_id_seq;


create table vip_level_log
(
    company_id    integer not null,
    flow_id       serial  not null,
    vip_card_id   integer,
    client_id     integer,
    wx_user_id    integer,
    pre_level_id  integer,
    cur_level_id  integer,
    change_reason text,
    create_time   timestamp,
    oper_id       integer,
    constraint vip_level_log_pk   primary key (company_id, flow_id)
);

-- 会员积分 ↑


-- 智能客服
CREATE TABLE customer_service_conversation 
(
    user_id INT NOT NULL,
    conversation_id serial NOT NULL PRIMARY KEY,
    title TEXT,
    status INT,
    start_time TIMESTAMP,
    end_time TIMESTAMP
);

CREATE TABLE customer_service_message
(
    conversation_id int not null,
    sender_id int not null,
    message_type text,
    content text,
    create_time TIMESTAMP,
    sender_type text,
    sender_name text
);
-- 增加收银台小程序
alter table wx_user add cashier_open_id text;

--销售费用分摊单
CREATE TABLE sheet_sale_fee_apportion_main (
  "company_id" int4 NOT NULL,
  "sheet_id" serial,
  "sheet_no" text,
  "sheet_type" text,
  "red_flag" int2,

  "red_sheet_id" int4,
  "happen_time" timestamp,
  "seller_id" int4,
  "maker_id" int4,
  "make_time" timestamp,
  "approver_id" int4,
  "approve_time" timestamp,
  "approve_brief" text,
  "reviewer_id" int4,
  "submit_time" timestamp,
  "make_brief" text,
  "sheet_attribute" jsonb
);


CREATE TABLE sheet_sale_fee_apportion_detail (
  "company_id" int4 NOT NULL,
  "sheet_id" int4,
  "flow_id" serial NOT NULL,
  "row_index" int4,
  "happen_time" timestamp,
  "relate_sheet_type" text,
  "relate_sheet_id" int4
);

ALTER TABLE sheet_sale_detail ADD COLUMN allocate_amount float8;


create table public.mall_notice
(
    notice_id       serial  not null,
    company_id      integer not null,
    notice_type     text,
    notice_content  jsonb,
    show_after_read boolean,
    order_index     integer,
    redirect_target jsonb,
    show_start_time timestamp,
    show_end_time   timestamp,
    status          integer,
    create_time     timestamp,
    creator         integer,
    update_time     timestamp,
    updater         integer,
    constraint mall_notice_pk
        primary key (notice_id, company_id)
);

-- 公告表
create table mall_notice
(
    notice_id       serial  not null,
    company_id      integer not null,
    notice_type     text,
    notice_content  jsonb,
    show_after_read boolean,
    order_index     integer,
    redirect_target jsonb,
    show_start_time timestamp,
    show_end_time   timestamp,
    status          integer,
    groups_id       text,
    ranks_id       text,
    regions_id       text,
    remark       text,
    create_time     timestamp,
    creator         integer,
    update_time     timestamp,
    updater         integer,
    constraint mall_notice_pk
        primary key (notice_id, company_id)
);


-- create table mall_notice_strategy
-- (
--     notice_strategy_id       serial  not null,
--     company_id      integer not null,
--     notice_id         integer,
--     groups_id         integer,
--     ranks_id         integer,
--     regions_id         integer,
--     constraint mall_notice_strategy_pk
--         primary key (notice_strategy_id, company_id,notice_id)
-- );

create table mall_notice_close
(
    flow_id       serial  not null,
    company_id      integer not null,
    notice_id         integer,
    supcust_id         integer,
    wx_user_id         integer,
    close_time         timestamp,
    constraint mall_notice_close_pk
        primary key (flow_id, company_id,notice_id,supcust_id,wx_user_id)
);

create unique index idx_mall_notice_close
    on mall_notice_close (company_id, notice_id, supcust_id, wx_user_id);


ALTER TABLE display_agreement_main ADD COLUMN settle_time timestamp;

alter table info_item_prop add item_desc text;
alter table info_item_multi_unit add mall_min_qty integer;


--tax税
alter table sheet_sale_detail add column output_tax_rate DECIMAL(5, 2);
alter table sheet_buy_detail add column input_tax_rate DECIMAL(5, 2);
alter table sheet_sale_order_detail add column output_tax_rate DECIMAL(5, 2);
alter table sheet_buy_order_detail add column input_tax_rate DECIMAL(5, 2);
alter table info_item_prop add column input_tax_rate DECIMAL(5, 2);
alter table info_item_prop add column output_tax_rate DECIMAL(5, 2);


--贷款单
alter table sheet_loan add column paid_principal_total numeric(16,2);
alter table sheet_loan add column paid_interest_total numeric(16,2);

alter table sheet_loan_repay_plan add column period_total_due numeric(16,2);
alter table sheet_loan_repay_plan add column period_range text;

alter table sheet_repay add column period_total_to_pay numeric(16,2);
alter table sheet_repay add column period_principal_to_pay numeric(16,2);
alter table sheet_repay add column period_interest_to_pay numeric(16,2);

insert into print_template_avail_elements values ('DK', '{"pageHead": [{"name": "company_name", "title": "公司名称", "value": "文胜商贸"}, {"name": "sheet_title", "title": "贷款单", "value": ""}, {"name": "sheet_no", "title": "单据号", "value": "DK2020023344433", "showTitle": false}, {"name": "page_index", "title": "页码", "value": "第1/2页"}, {"name": "unapproved", "title": "未审核", "value": ""}], "pageTail": [{"name": "page_index", "title": "页码", "value": "第1/2页"}], "tableHead": [{"name": "partner_name", "title": "借贷款单位", "value": "白鸽银行"},  {"name": "getter_name", "title": "业务员", "value": "小王"}, {"name": "loan_partner_name", "title": "账户", "value": ""}, {"name": "rate_m_show", "title": "月利率（%）", "value": "5"}, {"name": "rate_y_show", "title": "年利率（%）", "value": "60"}, {"name": "installment_count", "title": "贷款期数（月）", "value": "9"}, {"name": "repay_way_name", "title": "利息结算方式", "value": "等额本息"},{"name": "repay_period_name", "title": "还款方式", "value": "按季度还款"},{"name": "happen_time", "title": "交易时间", "value": "2020-12-31"}, {"name": "repay_date", "title": "还款日期", "value": "2021-09-30"}, {"name": "approve_time", "title": "审核时间", "value": "2020-12-31"}, {"name": "print_time", "title": "打印时间", "value": "2020-12-31"}, {"name": "print_count", "title": "打印次数", "value": "2"}, {"name": "make_brief", "title": "整单备注", "value": ""}], "tableTail": [{"name": "total_amount", "title": "总额", "value": "100元"}, {"name": "payway1", "title": "支付方式1", "value": "现金:10元"}, {"name": "payway2", "title": "支付方式2", "value": "银行:10元"}, {"name": "company_address", "title": "公司地址", "value": "黄海路24号"}, {"name": "company_tel", "title": "联系电话", "value": "138***2834"}, {"name": "make_brief", "title": "备注", "value": ""}, {"name": "maker_name", "title": "制单人", "value": ""}, {"name": "label", "title": "说明文字", "value": ""}, {"name": "custom_table", "title": "表格", "value": ""}]}'::jsonb);

insert into print_template_shared (company_id, template_id,template_name, sheet_type, template_content) values(-1,68,'贷款单','DK','{"name":"贷款单","title":"贷款单","width":241,"height":139,"fontBold":"bold","showTitle":"","pageHead":{"name":"pageHead","title":"","fontBold":"","showTitle":true,"left":2.91,"height":13.23,"elements":[{"name":"sheet_no","title":"单据号","x":4.5,"y":3.7,"showTitle":true},{"name":"company_name","title":"未设置","x":51.06,"y":4.23,"showTitle":true},{"name":"company_name","title":"公司名称","x":64.56,"y":2.65,"showTitle":true,"fontSize":7},{"name":"sheet_title","title":"贷款单","x":147.37,"y":2.38,"showTitle":true}],"show":false,"fontStyle":""},"tableHead":{"name":"tableHead","title":"表头区","fontBold":"","showTitle":true,"left":2.91,"height":105.04,"images":[],"elements":[{"name":"partner_name","title":"借贷款单位","x":6.09,"y":15.08,"showTitle":true},{"name":"total_amount","title":"总额","x":6.61,"y":38.63,"showTitle":true},{"name":"payway1","title":"支付方式1","x":109.8,"y":39.42,"showTitle":true},{"name":"payway2","title":"支付方式2","x":158.49,"y":39.16,"showTitle":true},{"name":"company_address","title":"地址","x":7.41,"y":82.55,"showTitle":true},{"name":"company_tel","title":"联系电话","x":39.95,"y":83.08,"showTitle":true},{"name":"sheet_title","title":"贷款单","x":155.05,"y":3.44,"showTitle":true},{"name":"company_name","title":"公司名称","x":73.02,"y":2.12,"showTitle":true,"fontSize":7},{"name":"sheet_no","title":"单据号","x":6.09,"y":2.12,"showTitle":true},{"name":"happen_time","title":"交易时间","x":84.14,"y":15.35,"fontName":"默认"}],"show":true,"fontStyle":""},"tableTail":{"name":"tableTail","title":"","fontBold":"","showTitle":true,"left":2.91,"height":10.58,"elements":[{"name":"total_amount","title":"总额","x":8.47,"y":2.91,"showTitle":true},{"name":"payway1","title":"支付方式1","x":81.76,"y":2.65,"showTitle":true},{"name":"payway2","title":"支付方式2","x":123.03,"y":2.38,"showTitle":true}],"show":false,"fontStyle":""},"pageTail":{"name":"pageTail","title":"","fontBold":"","showTitle":true,"left":2.91,"height":7.14,"elements":[{"name":"page_index","title":"页码","x":218.81,"y":1.32,"showTitle":true},{"name":"company_address","title":"地址","x":2.38,"y":1.85,"showTitle":true},{"name":"company_tel","title":"联系电话","x":104.25,"y":1.59,"showTitle":true}],"show":false,"fontStyle":""},"paddingTop":"3","paddingLeft":"3","paddingRight":"3","paddingBottom":"3","landscape":false,"backImage":""}'::jsonb);


insert into print_template_avail_elements values ('HDK', '{"pageHead": [{"name": "company_name", "title": "公司名称", "value": "文胜商贸"}, {"name": "sheet_title", "title": "还贷款单", "value": ""}, {"name": "sheet_no", "title": "单据号", "value": "HDK2020023344433", "showTitle": false}, {"name": "page_index", "title": "页码", "value": "第1/2页"}, {"name": "unapproved", "title": "未审核", "value": ""}], "pageTail": [{"name": "page_index", "title": "页码", "value": "第1/2页"}], "tableHead": [{"name": "partner_name", "title": "借贷款单位", "value": "白鸽银行"},  {"name": "getter_name", "title": "业务员", "value": "小王"}, {"name": "loan_partner_name", "title": "账户", "value": ""}, {"name": "interest_rate_month", "title": "月利率（%）", "value": "5"}, {"name": "installment_no", "title": "当前期数", "value": "1"}, {"name": "installment_count", "title": "还款总期数", "value": "3"}, {"name": "repay_way_name", "title": "利息结算方式", "value": "等额本息"},{"name": "repay_period_name", "title": "还款方式", "value": "按季度还款"},{"name": "interest_rate_month", "title": "月利率（%）", "value": "5"}, {"name": "loan_sheet_no", "title": "贷款单单号", "value": "DK2020123111"},{"name": "principal_total", "title": "贷款总金额", "value": "10000"},{"name": "period_total_due", "title": "本期计划总额", "value": "550"},{"name": "period_principal_due", "title": "本期计划本金", "value": "500"},{"name": "period_interest_due", "title": "本期计划利息", "value": "50"},{"name": "period_paid_amount", "title": "本期已还总额", "value": "0"},{"name": "period_principal_paid", "title": "本期已还本金", "value": "0"},{"name": "period_interest_paid", "title": "本期已还利息", "value": "0"},{"name": "period_total_to_pay", "title": "本期剩余金额", "value": "0"},{"name": "period_principal_to_pay", "title": "本期剩余本金", "value": "0"},{"name": "period_interest_to_pay", "title": "本期剩余利息", "value": "0"},{"name": "happen_time", "title": "交易时间", "value": "2020-12-31"}, {"name": "repay_date", "title": "还款日期", "value": "2021-09-30"}, {"name": "approve_time", "title": "审核时间", "value": "2020-12-31"}, {"name": "print_time", "title": "打印时间", "value": "2020-12-31"}, {"name": "print_count", "title": "打印次数", "value": "2"}, {"name": "make_brief", "title": "整单备注", "value": ""}], "tableTail": [{"name": "total_amount", "title": "总额", "value": "100元"}, {"name": "payway1", "title": "支付方式1", "value": "现金:10元"}, {"name": "payway2", "title": "支付方式2", "value": "银行:10元"}, {"name": "company_address", "title": "公司地址", "value": "黄海路24号"}, {"name": "company_tel", "title": "联系电话", "value": "138***2834"}, {"name": "make_brief", "title": "备注", "value": ""}, {"name": "maker_name", "title": "制单人", "value": ""}, {"name": "label", "title": "说明文字", "value": ""}, {"name": "custom_table", "title": "表格", "value": ""}]}'::jsonb);

insert into print_template_shared (company_id, template_id,template_name, sheet_type, template_content) values(-1,69,'还贷款单','HDK','{"name":"还贷款单","title":"还贷款单","width":241,"height":139,"fontBold":"bold","showTitle":"","pageHead":{"name":"pageHead","title":"","fontBold":"","showTitle":true,"left":2.91,"height":13.23,"elements":[{"name":"sheet_no","title":"单据号","x":4.5,"y":3.7,"showTitle":true},{"name":"company_name","title":"未设置","x":51.06,"y":4.23,"showTitle":true},{"name":"company_name","title":"公司名称","x":64.56,"y":2.65,"showTitle":true,"fontSize":7},{"name":"sheet_title","title":"还贷款单","x":147.37,"y":2.38,"showTitle":true}],"show":false,"fontStyle":""},"tableHead":{"name":"tableHead","title":"表头区","fontBold":"","showTitle":true,"left":2.91,"height":105.04,"images":[],"elements":[{"name":"partner_name","title":"借贷款单位","x":6.09,"y":15.08,"showTitle":true},{"name":"total_amount","title":"总额","x":6.61,"y":38.63,"showTitle":true},{"name":"payway1","title":"支付方式1","x":109.8,"y":39.42,"showTitle":true},{"name":"payway2","title":"支付方式2","x":158.49,"y":39.16,"showTitle":true},{"name":"company_address","title":"地址","x":7.41,"y":82.55,"showTitle":true},{"name":"company_tel","title":"联系电话","x":39.95,"y":83.08,"showTitle":true},{"name":"sheet_title","title":"贷款单","x":155.05,"y":3.44,"showTitle":true},{"name":"company_name","title":"公司名称","x":73.02,"y":2.12,"showTitle":true,"fontSize":7},{"name":"sheet_no","title":"单据号","x":6.09,"y":2.12,"showTitle":true},{"name":"happen_time","title":"交易时间","x":84.14,"y":15.35,"fontName":"默认"}],"show":true,"fontStyle":""},"tableTail":{"name":"tableTail","title":"","fontBold":"","showTitle":true,"left":2.91,"height":10.58,"elements":[{"name":"total_amount","title":"总额","x":8.47,"y":2.91,"showTitle":true},{"name":"payway1","title":"支付方式1","x":81.76,"y":2.65,"showTitle":true},{"name":"payway2","title":"支付方式2","x":123.03,"y":2.38,"showTitle":true}],"show":false,"fontStyle":""},"pageTail":{"name":"pageTail","title":"","fontBold":"","showTitle":true,"left":2.91,"height":7.14,"elements":[{"name":"page_index","title":"页码","x":218.81,"y":1.32,"showTitle":true},{"name":"company_address","title":"地址","x":2.38,"y":1.85,"showTitle":true},{"name":"company_tel","title":"联系电话","x":104.25,"y":1.59,"showTitle":true}],"show":false,"fontStyle":""},"paddingTop":"3","paddingLeft":"3","paddingRight":"3","paddingBottom":"3","landscape":false,"backImage":""}'::jsonb);



alter table info_item_prop add item_desc text;
alter table info_item_multi_unit add mall_min_qty integer;

-- 账户快捷键
alter table info_pay_qrcode add shortcut varchar(50);
-- 自定义快捷键
create table info_shortcut_key
(
    company_id        integer not null
        constraint info_shortcut_key_pk
            primary key,
    key_price         text,
    key_quantity      text,
    key_submit        text,
    key_hold_sheet    text,
    key_disc_amt      text,
    key_setting       text,
    key_sheet_type    text,
    key_wholesale     text,
    key_client        text,
    key_use_vip_card  text,
    key_sell_vip_card text
);
create unique index idx_info_shortcut_key_company_id on info_shortcut_key (company_id);


-- 增加收货地址
alter table info_client_address
    add province text;

alter table info_client_address
    add city text;

alter table info_client_address
    add area text;

