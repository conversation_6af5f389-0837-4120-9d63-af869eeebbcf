﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using ArtisanManage.WebAPI;
using ArtisanManage.YingJiangMallMini.Dao;
using ArtisanManage.YingJiangMallMini.Model;
using ArtisanManage.YingJiangMallMini.Util;
using HuaweiCloud.SDK.Ocr.V1.Model;
using Microsoft.CodeAnalysis.Elfie.Diagnostics;
using Microsoft.VisualStudio.Web.CodeGenerators.Mvc.Templates.Blazor;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.SS.Formula.Functions;
using NuGet.Common;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Linq;
using System.Threading.Channels;
using System.Threading.Tasks;
using static ArtisanManage.AppController.VisitStrategyController;
using static ArtisanManage.Pages.Report.AttendanceReportAllViewController;
using static ArtisanManage.WebAPI.RedPacketController;
using static ArtisanManage.YingJiangMallMini.Model.GetPurchaseRewardResponse;

namespace ArtisanManage.YingJiangMallMini.Services;

public class MallMiniRedPacketService
{
    private readonly MallMiniRedPacketDao _dao;

    List<PurchaseRewardDetail> details = new List<PurchaseRewardDetail>();
    decimal total_purchase_reward_redpacket = 0m;
    public MallMiniRedPacketService(CMySbCommand cmd)
    {
        _dao = new MallMiniRedPacketDao(cmd);
    }

    public Task<MallMiniAccountSummary> GetRedPacketBalance(MallMiniSummaryAccountParameter parameter)
    {
        return _dao.GetRedPacketBalanceDao(parameter);
    }
    public Task<List<MiniRedPacketHistory>> GetRedPacketHistoryService(MallMiniGetRedPacketHistoryParameter parameter)
    {
        return _dao.GetRedPacketHistoryDao(parameter);
    }
    #region 检查新客红包
    public Task<MallMiniRedPacketBaseInfo> GetMallMiniRedPacketBaseInfoService(MallMinBaseParameter parameter)
    {
        Security.GetInfoFromOperKey(parameter.operKey, out string companyID);
        parameter.companyId = companyID;
        
        return _dao.GetMallMiniRedPacketBaseInfoDao(parameter);
    }
    #endregion
    #region 主动发送红包
    public async Task<List<MallMiniRedPacketSend>> GetMallMiniRedPacketSendService(MallMinSendParameter parameter)
    {
        Security.GetInfoFromOperKey(parameter.operKey, out string companyID);
        parameter.companyId = companyID;
        
        
        List<MallMiniRedPacketSend> initialResult =
            await _dao.GetMallMiniRedPacketSendDao(parameter) ??
            new List<MallMiniRedPacketSend>();

        List<MallMiniRedPacketSend> result = new List<MallMiniRedPacketSend>();

        foreach (var record in initialResult)
        {
            int canPickNum = record.can_pick_num;
            for (int i = 0; i < canPickNum; i++)
            {
                var newRecord = new MallMiniRedPacketSend
                {

                    can_pick_amount = record.red_packet_amount.Contains("~")
                        ? GetRandomAmount(record.red_packet_amount)
                        : (decimal.TryParse(record.red_packet_amount, out var amount) ? amount : 0m),
                    red_packet_id=record.red_packet_id,
                    red_packet_amount=record.red_packet_amount,
                    restrict_time=record.restrict_time,
                    red_packet_name=record.red_packet_name,
                    red_packet_max_num=record.red_packet_max_num,
                    user_picked_num=record.user_picked_num,
                    can_pick_num=record.can_pick_num
                };
                result.Add(newRecord);
            }
        }
        return result;
    }
    private decimal GetRandomAmount(string range)
    {
        var parts = range.Split('~');
        if (parts.Length == 2 &&
            decimal.TryParse(parts[0], out decimal min) &&
            decimal.TryParse(parts[1], out decimal max))
        {
            var random = new Random();
            var randomAmount = Math.Round((decimal)(random.NextDouble() * ((double)max - (double)min) + (double)min),2);
            return randomAmount;
        }
        throw new ArgumentException("Invalid range format", nameof(range));
    }
    #endregion
    #region 下单返利
    public async Task<GetPurchaseRewardResponse> GetMallMiniRedPacketPurchaseRewardService(MallMinPurchaseRewardParameter parameter)
    {
        string companyID = "";
        if (parameter?.operKey != null&& parameter?.operKey != "")
        {
            Security.GetInfoFromOperKey(parameter.operKey, out companyID);
            parameter.companyId = companyID;
        }
        if (parameter?.companyId != null)
        {
            companyID= parameter.companyId;
        }

        List<GetPurchaseRewardDao> result =
            await _dao.GetMallMiniRedPacketPurchaseRewardDao(parameter);
        //var sheetRowDict = parameter.sheet_rows.ToDictionary(row => row.item_id, row => row);

        //合并相同item_id的sheet_rows
       var sheetRowDict = parameter.sheet_rows
       .GroupBy(row => row.item_id)
       .ToDictionary(g => g.Key, g => new GetPurchaseRewardRequiredSheetRow
       {
           item_id = g.Key,
           item_brand = g.First().item_brand,
           item_class = g.First().item_class,
           total_price = g.Sum(row => row.total_price) // 合并总价 
       }
       );



        // 用一个字典暂存每个 item_id 最大的奖励
        Dictionary<string, PurchaseRewardDetail> itemRewardDict = new Dictionary<string, PurchaseRewardDetail>();

        foreach (var item in result)
        {
            if (item.type == "item")
            {
                var item_id = item.item_id;

                if (!sheetRowDict.TryGetValue(item_id.ToString(), out var sheetRow))
                    continue;

                var policy = JsonConvert.DeserializeObject<List<PolicyItem>>(item.policy);

                decimal max_purchase_reward = 0m;
                PolicyItem? selectedPolicy = null;

                foreach (var costInfo in policy)
                {
                    decimal reach_cost = costInfo.reach_cost;
                    if (sheetRow.total_price >= reach_cost)
                    {
                        decimal purchase_reward_rec = 0m;
                        if (costInfo.red_packet_amount.Contains("~"))
                        {
                            purchase_reward_rec = GetRandomAmount(costInfo.red_packet_amount);
                        }
                        else if (costInfo.red_packet_amount.EndsWith("%"))
                        {
                            var percentage = decimal.Parse(costInfo.red_packet_amount.TrimEnd('%')) / 100;
                            purchase_reward_rec = percentage * sheetRow.total_price;
                        }
                        else
                        {
                            if (decimal.TryParse(costInfo.red_packet_amount, out var amount))
                            {
                                purchase_reward_rec = amount;
                            }
                        }

                        if (purchase_reward_rec > max_purchase_reward)
                        {
                            max_purchase_reward = purchase_reward_rec;
                            selectedPolicy = costInfo;
                        }
                    }
                }

                if (max_purchase_reward > 0 && selectedPolicy != null)
                {
                    var newDetail = new PurchaseRewardDetail
                    {
                        item_id = item_id,
                        red_packet_id = selectedPolicy.red_packet_id.ToString(),
                        item_brand = sheetRow.item_brand,
                        item_class = sheetRow.item_class,
                        total_price = sheetRow.total_price,
                        purchase_reward = max_purchase_reward,
                        reward_type = "item"
                    };

                    if (itemRewardDict.TryGetValue(item_id.ToString(), out var existingDetail))
                    {
                        if (newDetail.purchase_reward > existingDetail.purchase_reward)
                        {
                            itemRewardDict[item_id.ToString()] = newDetail;
                        }
                    }
                    else
                    {
                        itemRewardDict[item_id.ToString()] = newDetail;
                    }
                }
            }
        }

        // 最后统一加到 details
        foreach (var detail in itemRewardDict.Values)
        {
            total_purchase_reward_redpacket += detail.purchase_reward;
            details.Add(detail);
        }
        var redPacketIds = string.Join(",",
            result
                .Where(item => item.type == "brand" || item.type == "class") // 只选择 type 为 brand 或 class 的项
                .SelectMany(x =>
                {
                    try
                    {
                        var array = JArray.Parse(x.policy);
                        return array
                            .Select(j => j["red_packet_id"]?.ToString())
                            .Where(id => !string.IsNullOrEmpty(id));
                    }
                    catch
                    {
                        return Enumerable.Empty<string>();
                    }
                })
                .Distinct()
        );
        GetPurchaseRewardParameter redPacketPlanParameter =new GetPurchaseRewardParameter() {
            companyId=companyID,
            redPacketId = redPacketIds };

        //拿出相关的红包方案
        
        List<GetRedpacketPlanDao> redPacketPlanList =new List<GetRedpacketPlanDao>();
        if(redPacketIds!="")
            redPacketPlanList = await _dao.GetMallMiniRedPacketPlanListDao(redPacketPlanParameter);

        string itemIdList = parameter.sheet_rows != null ? "(" + string.Join(",", parameter.sheet_rows.Select(row => row.item_id)) + ")" : "";
        var existingItemIds = new HashSet<string>(details.Select(d => d.item_id.ToString()));

        //拿出用来匹配品牌和类别的商品
        var ItemIds_forBrandClass = parameter.sheet_rows
            .Where(row => !existingItemIds.Contains(row.item_id.ToString()))
            .Select(row => new GetPurchaseRewardRequiredSheetRow
            {
                item_id = row.item_id,
                item_class = result.FirstOrDefault(item => item.item_id.ToString() == row.item_id)?.item_class ?? row.item_class, // 替换为result中的item_class
                item_brand = row.item_brand,
                total_price = row.total_price
            })
            .ToList();
        var rebateAmount_return=await MaximizeRebate(parameter, ItemIds_forBrandClass, redPacketPlanList);
        total_purchase_reward_redpacket += rebateAmount_return;
        while (rebateAmount_return != 0)
        {
            existingItemIds = new HashSet<string>(details.Select(d => d.item_id.ToString()));
            ItemIds_forBrandClass = parameter.sheet_rows
                .Where(row => !existingItemIds.Contains(row.item_id.ToString()))
                .Select(row => new GetPurchaseRewardRequiredSheetRow
                {
                    item_id = row.item_id,
                    item_class = result.FirstOrDefault(item => item.item_id.ToString() == row.item_id)?.item_class ?? row.item_class, // 替换为result中的item_class
                    item_brand = row.item_brand,
                    total_price = row.total_price
                })
                .ToList();

            if (ItemIds_forBrandClass.Any())
            {
                rebateAmount_return = await MaximizeRebate(parameter, ItemIds_forBrandClass, redPacketPlanList);
                total_purchase_reward_redpacket += rebateAmount_return;
            }
            else
            {
                break; // 如果ItemIds_forBrandClass为空，退出循环
            }
        }

        //整单返利
        if (ItemIds_forBrandClass.Any()) {
            var negativeItem = result.FirstOrDefault(item => item.item_id == -1);
            decimal totalPriceSum = ItemIds_forBrandClass.Sum(item => item.total_price);
            decimal maxRebateAmount = 0m;
            string selectedRedPacketId = "";
            redPacketIds = string.Join(",",
                result
                    .Where(item => item.type == "all")
                    .SelectMany(x =>
                    {
                        try
                        {
                            var array = JArray.Parse(x.policy);
                            return array
                                .Select(j => j["red_packet_id"]?.ToString())
                                .Where(id => !string.IsNullOrEmpty(id) && id != "-1");
                        }
                        catch
                        {
                            return Enumerable.Empty<string>();
                        }
                    })
                    .Distinct()
            );
            if (redPacketIds != "")
            {
                redPacketPlanParameter = new GetPurchaseRewardParameter()
                {
                    companyId = companyID,
                    redPacketId = redPacketIds
                };
                redPacketPlanList = await _dao.GetMallMiniRedPacketPlanListDao(redPacketPlanParameter);


                // 计算redPacketPlanList中的最大返利
                foreach (var plan in redPacketPlanList)
                {
                    if (totalPriceSum >= plan.reach_cost && totalPriceSum != 0)
                    {
                        decimal rebateAmount = CalculateRebate(plan.red_packet_amount, totalPriceSum);
                        if (rebateAmount > maxRebateAmount)
                        {
                            maxRebateAmount = rebateAmount;
                            selectedRedPacketId = plan.red_packet_id.ToString();
                        }
                    }
                }
            }
            if (negativeItem != null && !string.IsNullOrEmpty(negativeItem.policy))
            {
                try
                {
                    var settingpolicy = JsonConvert.DeserializeObject<List<PolicyItem>>(negativeItem.policy);
                    // 计算settingpolicy中的返利
                    if (settingpolicy != null)
                    {
                        foreach (var costInfo in settingpolicy)
                        {
                            if (totalPriceSum >= costInfo.reach_cost && totalPriceSum != 0)
                            {
                                var percentage = decimal.Parse(costInfo.red_packet_amount) / 100;
                                decimal rebateAmount = percentage * totalPriceSum;
                                if (rebateAmount > maxRebateAmount)
                                {
                                    maxRebateAmount = rebateAmount;
                                    selectedRedPacketId = "-1";//-1表示是红包设置里面的我
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    // 记录日志但不影响主流程
                    NLogger.Error($"解析红包设置策略失败: {ex.Message}, policy: {negativeItem.policy}");
                }
            }
            // 如果找到有效的返利金额，则添加到details中
            if (maxRebateAmount > 0)
            {
                total_purchase_reward_redpacket += maxRebateAmount;
                foreach (var item in ItemIds_forBrandClass)
                {
                    details.Add(new PurchaseRewardDetail
                    {
                        item_id = Convert.ToInt32(item.item_id),
                        red_packet_id = selectedRedPacketId,
                        item_brand = string.IsNullOrEmpty(item.item_brand) ? "" : item.item_brand,
                        item_class = item.item_class,
                        total_price = item.total_price,
                        purchase_reward = maxRebateAmount * (item.total_price / ItemIds_forBrandClass.Sum(item => item.total_price)),
                        reward_type = "all"
                    });
                }
            }
        }
            return new GetPurchaseRewardResponse
        {
            result="OK",
            msg="success",
            total_purchase_reward_redpacket = total_purchase_reward_redpacket,
            details = details
        };
    }
    #endregion
    #region 返利最大化
    public async Task<decimal> MaximizeRebate(MallMinPurchaseRewardParameter parameter,List<GetPurchaseRewardRequiredSheetRow> ItemIds_forBrandClass, List<GetRedpacketPlanDao> redPacketPlanList)
    {
        GetRedpacketPlanDao redPacketPlan_rec=new GetRedpacketPlanDao();
        decimal rebateAmount_rec = 0m;
        List<GetPurchaseRewardRequiredSheetRow> ItemIds_forBrandClass_rec = new List<GetPurchaseRewardRequiredSheetRow>();
        foreach (var plan in redPacketPlanList)
        {
            var restrictGoods = JsonConvert.DeserializeObject<RestrictGoods>(plan.restrict_goods);
            var eligibleItems = parameter.sheet_rows
                .Where(row => ItemIds_forBrandClass.Any(item => item.item_id == row.item_id) &&
                                (restrictGoods.brands.Contains("all") || restrictGoods.brands.Contains(row.item_brand.ToString()) ||
                                (restrictGoods.classes.Contains("all") || restrictGoods.classes.Any(cls => row.item_class.StartsWith(cls)))) &&
                                !restrictGoods.items_ban.Contains(row.item_id.ToString())
                ).ToList();

            decimal totalPrice = eligibleItems.Sum(item => item.total_price);
            if (totalPrice >= plan.reach_cost)
            {
                decimal rebateAmount = CalculateRebate(plan.red_packet_amount, totalPrice);
                if (rebateAmount_rec < rebateAmount && rebateAmount!=0)
                {
                    rebateAmount_rec = rebateAmount;
                    redPacketPlan_rec = plan;
                    ItemIds_forBrandClass_rec = eligibleItems;
                    if (plan.reach_cost == 0) {
                        ItemIds_forBrandClass_rec = ItemIds_forBrandClass;
                    }
                }


            }
        }
        foreach (var item in ItemIds_forBrandClass_rec)
                        {
                            details.Add(new PurchaseRewardDetail
                            {
                                item_id = Convert.ToInt32(item.item_id),
                                red_packet_id = redPacketPlan_rec.red_packet_id.ToString(),
                                item_brand = string.IsNullOrEmpty(item.item_brand) ? "" : item.item_brand,
                                item_class = item.item_class,
                                total_price = item.total_price,
                                purchase_reward = rebateAmount_rec * (item.total_price / ItemIds_forBrandClass_rec.Sum(item => item.total_price)), // 按比例分配返利
                                reward_type = "itemorBrand"
                            });
                        }

        return rebateAmount_rec;
    }

    private decimal CalculateRebate(string redPacketAmount, decimal totalPrice)
    {
        if (redPacketAmount.Contains("~"))
        {
            return GetRandomAmount(redPacketAmount);
        }
        else if (redPacketAmount.EndsWith("%"))
        {
            var percentage = decimal.Parse(redPacketAmount.TrimEnd('%')) / 100;
            return percentage * totalPrice;
        }
        else
        {
            return decimal.TryParse(redPacketAmount, out var amount) ? amount : 0m;
        }
    }
    #endregion
    #region 记录变动及余额变动
    public async Task<object> LogMallMiniRedPacketChangeService(MallMinRedPacketLogandChangeParameter parameter)
    {
        Security.GetInfoFromOperKey(parameter.operKey, out string companyID);
        
        CMySbCommand cmd = new CMySbCommand();

        var logRedPacketResult = (object)null;
        string sql = "";
        string msg = "";
        foreach (var detail in parameter.red_packet_details)
        {
            var redPacketChanges = new List<RedPacketController.RedPacketChange>();
            string red_packet_id = detail.red_packet_id.ToString();
            if (detail.red_packet_type == "RegisterReward")
            {
                redPacketChanges.Add(new RedPacketController.RedPacketChange(RedPacketController.RedPacketChangeType.RegisterReward, detail.red_packet_amount, red_packet_id,null));
                logRedPacketResult = await RedPacketController.LogSheetRedPacketChange(cmd, null,null, companyID, parameter.supcustId.ToString(), redPacketChanges.ToList());
            }
            else if (detail.red_packet_type == "ProactivelySend")
            {
                redPacketChanges.Add(new RedPacketController.RedPacketChange(RedPacketController.RedPacketChangeType.ProactivelySend, detail.red_packet_amount, red_packet_id,null));
                logRedPacketResult = await RedPacketController.LogSheetRedPacketChange(cmd, null,null, companyID, parameter.supcustId.ToString(), redPacketChanges.ToList());
            }
            if (red_packet_id != null && red_packet_id != "" && red_packet_id != "-1")
            { 
                sql+= $@"
                    UPDATE sheet_red_packet_plan
                    SET sent_red_packet_sum = COALESCE(sent_red_packet_sum, 0) + {detail.red_packet_amount},
                        sent_red_packet_num = COALESCE(sent_red_packet_num, 0) + 1
                    WHERE
                        company_id = {companyID}
                        AND red_packet_id in ({red_packet_id});
                ";
            }
        }
        try
        {
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
        }
        catch (Exception ex)
        {
            NLogger.Error("[LogSheetRedPacketChange] 数据库操作失败，语句为" + sql);
            NLogger.Error(ex.ToString());
            msg = ex.Message;
        }

        string result = msg.Length > 0 ? "Error" : "OK";
        return logRedPacketResult;
    }

    #endregion
}
