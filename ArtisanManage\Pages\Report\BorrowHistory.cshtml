@page
@model BorrowHistoryModel
@{
    Layout = "_LayoutQuery";
}

<script type="text/javascript">
    var itemSource = {};
    $(document).ready(function () {
        @Html.Raw(Model.m_showFormScript)
        @Html.Raw(Model.m_createGridScript)
        
        $("#gridItems").on("cellclick", function (event) {
            var args = event.args;
            var sheet_id = args.row.bounddata.sheet_id;
            var sheet_type = args.row.bounddata.sheet_type;
            
            if (args.datafield == "sheet_no" && sheet_id) {
                // 点击单据号，打开对应的单据
                var title = "";
                var url = "";
                
                if (sheet_type === 'SHEET_BORROW_ITEM') {
                    title = "借货单";
                    url = `Sheets/BorrowItemSheet?sheet_id=${sheet_id}`;
                } else if (sheet_type === 'SHEET_RETURN_ITEM') {
                    title = "还货单";
                    url = `Sheets/BorrowItemSheet?sheet_id=${sheet_id}`;
                }
                
                if (url) {
                    window.parent.newTabPage(title, url, window);
                }
            }
        });

        QueryData();
        
        let windowHeight = document.body.offsetHeight - 50;
        let windowWidth = document.body.offsetWidth - 80;
        var isScroll = document.body.offsetHeight > 600;
        
        $('#supcust_id').jqxInput({
            onButtonClick: function (event) {
                $('#popClient').jqxWindow('open');
                $("#popClient").jqxWindow('setContent', 
                    `<iframe src="/BaseInfo/ClientsView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="${isScroll ? "yes" : "no"}" frameborder="no"></iframe>`);
            }
        });
    });

    function QueryData() {
        var queryData = GetQueryData();
        $("#gridItems").jqxGrid('updatebounddata');
    }

    function GetQueryData() {
        var queryData = {};
        
        var startDay = $('#startDay').jqxDateTimeInput('val');
        if (startDay) queryData.startDay = startDay.toString();
        
        var endDay = $('#endDay').jqxDateTimeInput('val');
        if (endDay) queryData.endDay = endDay.toString();
        
        var supcust_id = $('#supcust_id').val();
        if (supcust_id && supcust_id.value) queryData.supcust_id = supcust_id.value;
        
        var sheet_type = $('#sheet_type').val();
        if (sheet_type && sheet_type.value) queryData.sheet_type = sheet_type.value;
        
        queryData.operKey = g_operKey;
        return queryData;
    }
</script>

<style>
    .grid-cell-borrow {
        color: #d9534f;
        font-weight: bold;
    }
    
    .grid-cell-return {
        color: #5cb85c;
        font-weight: bold;
    }
    
    .grid-cell-sheet-no {
        color: #337ab7;
        cursor: pointer;
    }
    
    .grid-cell-sheet-no:hover {
        text-decoration: underline;
    }
</style>

<div id="popClient" style="display:none">
    <div style="height:30px;background-color:#fff; text-align:center;">
        <span style="font-size:20px;">选择客户</span>
    </div>
    <div style="overflow:hidden;"></div>
</div>
