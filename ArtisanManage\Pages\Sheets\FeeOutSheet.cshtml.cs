using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using ArtisanManage.Models;
using ArtisanManage.Services;
using ArtisanManage.MyJXC;
using static ArtisanManage.MyJXC.SheetSale;
using static ArtisanManage.Pages.FeeOutSheetModel;
using NPOI.Util;
using System.Dynamic;
using System.Net.Http;
using Newtonsoft.Json;
using NPOI.SS.UserModel;

namespace ArtisanManage.Pages
{
    public class FeeOutSheetModel : PageSheetModel<SheetRowFeeOut>
    { 
      
        public string SheetTitle = "";
        //public string SupplierFeeSheetId = "";//SR
        
        public FeeOutSheetModel(CMySbCommand cmd) : base(Services.MenuId.sheetFeeOut)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"sheet_no",new DataItem(){Title="单号",UseJQWidgets=false}},
                {"sheet_id",new DataItem(){Title="sheet_id", CtrlType="hidden", FldArea="divHead"}},
                {"red_flag",new DataItem(){Title="red_flag", CtrlType="hidden", FldArea="divHead"}},
                {"sheet_type",new DataItem(){Title="sheet_type", CtrlType="hidden", FldArea="divHead"}},
                {"sheetType",new DataItem(){Title="sheetType", CtrlType="hidden", FldArea="divHead"}},
                {"supcust_id",new DataItem(){FldArea="divHead",Title="往来单位",CONDI_DATA_ITEM="sheetType",LabelFld="sup_name", ButtonUsage="event",SqlForOptions="select supcust_id as v,sup_name as l,py_str as z from info_supcust where supcust_flag in ('C','CS','W','S') and COALESCE(status,'1')='1'",DropDownWidth = "280"}},
                {"acct_supcust_id",new DataItem(){Title="结算单位", CtrlType="hidden", FldArea="divHead"}},
                {"getter_id",new DataItem(){FldArea="divHead",Title="业务员",LabelFld="getter_name",ButtonUsage="list",SqlForOptions=CommonTool.selectSellers } },//SqlForOptions="select oper_id as v,oper_name as l,py_str as z from info_operator"}},
                {"happen_time",new DataItem(){FldArea="divHead",Title="交易日期",CtrlType="jqxDateTimeInput",PlaceHolder="当前时间" }},
                {"make_brief",new DataItem(){FldArea="divHead",Title="备    注"}},

                {"sup_addr",new DataItem(){Title="客户地址", CtrlType="hidden", FldArea="divHead"}},
                {"boss_name",new DataItem(){Title="老板姓名", CtrlType="hidden", FldArea="divHead"}},
                {"mobile",new DataItem(){Title="客户电话", CtrlType="hidden", FldArea="divHead"}},
                {"seller_mobile",new DataItem(){Title="业务电话", CtrlType="hidden", FldArea="divHead"}},

                {"total_amount",new DataItem(){FldArea="divTail", Title="合计金额",Width="80",Value="0",Disabled=true}},
                {"now_disc_amount",new DataItem(){FldArea="divTail",Title="优惠金额",Hidden=true, Width="80",Value="0"}},
                {"no_disc_amount",new DataItem(){FldArea="divTail",Title="合计",Hidden=true, Width="80"}},
                {"payway1_id",new DataItem(){FldArea="divTail",HideGroup="payway1", HideClass="payway",Title="",LabelFld="payway1_name",InnerTitle="支付方式1",ClassName="itemLeft",PlaceHolder="支付方式",ButtonUsage="list",
                    Width="80",GetOptionsOnLoad=true,FirstOptionAsDefault=true}},
                {"payway1_type",new DataItem(){FldArea = "divTail", Title = "", InnerTitle = "支付类型1", Hidden = true, HideOnLoad = true, Width = "80"}},
                {"payway1_amount",new DataItem(){FldArea="divTail",HideGroup="payway1",HideClass="payway", Title="",ClassName="itemRight",InnerTitle="支付金额1",PlaceHolder="支付金额",Width="80"}},
                {"payway2_id",new DataItem(){FldArea="divTail",HideGroup="payway2",HideClass="payway", Title="",LabelFld="payway2_name",InnerTitle="支付方式2",ClassName="titleItem",PlaceHolder="支付方式", ButtonUsage="list",
                    Width="80",GetOptionsOnLoad=true}},
                {"payway2_type",new DataItem(){FldArea = "divTail", Title = "", InnerTitle = "支付类型2", Hidden = true, HideOnLoad = true, Width = "80"}},
                {"payway2_amount",new DataItem(){FldArea="divTail",HideGroup="payway2",HideClass="payway", Title="",InnerTitle="支付金额2",PlaceHolder="支付金额",Width="80"}},               
                {"left_amount",new DataItem(){FldArea="divTail",Title="欠款",Width="80"}},
                {"LeftAmount",new DataItem(){FldArea="divTail",Title="尚欠",Width="80",Disabled = true}},
                {"maker_id",new DataItem(){UseJQWidgets=false, Title="制单人", Width="80"}},
                {"maker_name",new DataItem(){UseJQWidgets=false, Title="制单人", Width="80"}},
                {"make_time",new DataItem(){UseJQWidgets=false, Title="审核时间"}},
                {"approver_id",new DataItem(){UseJQWidgets=false, Title="审核人", Width="80"}},
                {"approver_name",new DataItem(){UseJQWidgets=false, Title="审核人", Width="80"}},
                {"approve_time",new DataItem(){UseJQWidgets=false, Title="审核时间"}},
                {"red_sheet_id",new DataItem(){Title="red_sheet_id", CtrlType="hidden", FldArea="divHead"}},
                //{"red_sheet_no",new DataItem(){UseJQWidgets=false, Title="red_sheet_no"}},
                {"sheet_id_red_me",new DataItem(){Title="sheet_id_red_me", CtrlType="hidden", FldArea="divHead"}},
                {"appendix_photos",new DataItem(){Title="appendix_photos", CtrlType="hidden", FldArea="divHead"}},
                {"isRedAndChange",new DataItem(){Title="isRedAndChange",CtrlType="hidden",  FldArea="divHead"} },
                {"old_sheet_id",new DataItem(){Title="old_sheet_id",CtrlType="hidden",  FldArea="divHead"} },
                {"fee_appo_sheet_id", new DataItem(){Title="fee_appo_sheet_id", CtrlType="hidden", FldArea="divHead" }},
                {"fee_appo_sheet_no", new DataItem(){Title="fee_appo_sheet_no", UseJQWidgets=false }},
                {"incomeSheetInfo",new DataItem(){Title="incomeSheetInfo", CtrlType="hidden", FldArea="divHead" }},//ZC
                {"pay_for_supplier_fee_sheet_id", new DataItem(){Title="pay_for_supplier_fee_sheet_id", CtrlType="hidden", FldArea="divHead" }},//SR
                {"pay_for_supplier_fee_sheet_no", new DataItem(){Title="pay_for_supplier_fee_sheet_no", UseJQWidgets=false }},//SR
               // {"branch_state",new DataItem(){title="状态",labelFld="branch_status_name",labelInDB=false,value="1",label="正常", buttonUsage="list", source = "[{v:1,l:'正常'},{v:0,l:'停用'}]"}}
            };

           // m_idFld = "sheet_id"; 
           // m_tableName = "sheet_item_master";
          //  m_selectFromSQL = "from sheet_item_master sht left join info_supcust on sht.supcust_id=info_supcust.supcust_id left join info_branch on sht.branch_id=info_branch.brand_id left join (select oper_id,oper_name as order_man_name from info_operator) tb_order_man on sht.order_man=tb_order_man.oper_id where sheet_id='~ID'";
            /*
            Grids = new Dictionary<string, FormDataGrid>()
            {
                {"gridUnit" ,new FormDataGrid(){
                   Columns = new Dictionary<string, DataItem>()
                   {
                       //{"unit_no",new DataItem(){title="单位",width="100",url="../api/ItemEdit/GetUnits"}},
                       {"unit_no",new DataItem(){title="单位",width="100",SqlForOptions="select unit_no from info_item_unit",getOptionsOnLoad=true,buttonUsage="list"}},
                       {"unit_factor",new DataItem(){title="包装率",width="80"}},
                   },
                   TableName="info_item_multi_unit",
                   IdFld="item_no",
                   SelectFromSQL="from info_item_multi_unit where item_no='~ID'"
                }}
            };*/
        }

        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            if (DataItems["payway1_id"].Value == "")
            {
                string sql = $"select  sub_id as v,sub_name as l from  cw_subject  where company_id={company_id} and sub_type='QT' and is_order is not true and (status!='0' or status is null) order by sub_code::text";

                dynamic record = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                if (record != null)
                {
                    DataItems["payway1_id"].Value = record.v;
                    DataItems["payway1_id"].Label = record.l;
                }
            }

            if (DataItems["LeftAmount"].Value == "" || DataItems["LeftAmount"].Value == "0")
            {
                DataItems["LeftAmount"].Hidden = true;
            }

        }

        public async Task OnGet(bool forOutOrIn)//, string supplierFeeSheetId=""
        {            
            SheetFeeOut sheet = new SheetFeeOut(forOutOrIn?SHEET_FEE_OUT.IS_OUT:SHEET_FEE_OUT.NOT_OUT, LOAD_PURPOSE.SHOW);
            if (!forOutOrIn)
            {
                DataItems["supcust_id"].Title = "供应商";
                DataItems["payway1_id"].SqlForOptions = CommonTool.selectPayWayFeeIn;
                DataItems["payway2_id"].SqlForOptions = CommonTool.selectPayWayFeeIn;
            }
            else
            {
                DataItems["payway1_id"].SqlForOptions = CommonTool.selectPayWayFeeOut;
                DataItems["payway2_id"].SqlForOptions = CommonTool.selectPayWayFeeOut;
            }
            await InitGet(cmd,sheet);
            SheetTitle = sheet.sheet_type==SHEET_TYPE.SHEET_FEE_OUT ? "费用支出" : "其他收入单";
            SheetRowsJson = Newtonsoft.Json.JsonConvert.SerializeObject(sheet.SheetRows);
            //SupplierFeeSheetId = supplierFeeSheetId;
        }
    }
    [ApiController]
    [Route("api/[controller]/[action]")]
    public class FeeOutSheetController : BaseController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        public FeeOutSheetController(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey,string dataItemName, string flds, string value, string availValues,string condiDataItem)
        {
            var model = new FeeOutSheetModel(cmd);
            if (condiDataItem == "SR")
            {
                model.DataItems["supcust_id"].SqlForOptions = model.DataItems["supcust_id"].SqlForOptions.Replace("'C'", "'S'");
            }
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey,model.DataItems, dataItemName, flds, value, availValues, condiDataItem);
            return data;
        }

        [HttpGet]
        public async Task<IActionResult> GetSheetToPrint(string operKey, string sheet_id, bool smallUnitBarcode, string printTemplate)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            cmd.ActiveDatabase = "";
            SheetFeeOut sheet = new SheetFeeOut(LOAD_PURPOSE.SHOW);
            await sheet.Load(cmd, companyID, sheet_id);
      
            dynamic dTemplate = null;
            if (printTemplate.IsValid())
            {
                dTemplate = JsonConvert.DeserializeObject(printTemplate);
            }
            await sheet.LoadInfoForPrint(cmd, smallUnitBarcode, true, dTemplate);

            string msg = "";
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, sheet });
        }

        [HttpGet]
        public async Task<IActionResult> GetDisplaySheet(string operKey,string supcust_id)
        {
            string sql = "";
            List<ExpandoObject> displaySheets = null;
            Security.GetInfoFromOperKey(operKey, out string companyID,out string operID);
            var model = new FeeOutSheetModel(cmd);
            #region 判断提前使用陈列费
            //var dispCondi = " where t.disp_month<=(to_char(now(),'MM')::int) and disp_year<=(to_char(now(),'YYYY')::int) ";
            var dispCondi = @" 
WHERE  
(
    disp_year < to_char(now(),'YYYY')::int
    OR
    (
    disp_year = to_char(now(),'YYYY')::int AND t.disp_month <= to_char(now(),'MM')::int  
    )
)";
        sql = $"select rights->'delicacy'->'allowAdvanceDisplayFee'->'value' allow_advance_disp_fee from info_operator o left join info_role r on r.role_id= o.role_id where o.company_id={companyID} and oper_id={operID}";
            dynamic advanceDisp = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            if (advanceDisp != null && advanceDisp.allow_advance_disp_fee == "true") dispCondi = $" ";
            #endregion
            var fld = "";
            for(int i = 1; i < 13; i++)
            {
                if (fld != "") fld += "||','||";
                fld += $"coalesce(month{i}_qty,0)-coalesce(month{i}_given,0)";
            }
            if (supcust_id != "")
            {
                sql = @$"
SELECT display_fee_id,sub_name display_fee_name,sum(display_left_money) display_sub_amount,display_remark,display_id,display_no
FROM 
(
    SELECT display_fee_id,display_id,display_no,display_remark,display_left_money,
           (case when start_month+months-1>12 then start_month+months-13 else start_month+months-1 end) disp_month,
           (case when start_month+months-1>12 then start_year+1 else start_year end) as disp_year,months as disp_month_id
    FROM 
    (
        SELECT m.fee_sub_id display_fee_id,d.sheet_id display_id,m.sheet_no display_no,remark display_remark,unnest(string_to_array(({fld}) ,','))::numeric display_left_money,
               unnest(string_to_array('01,02,03,04,05,06,07,08,09,10,11,12',','))::int months,to_char(m.start_time,'YYYY')::int start_year,
               to_char(m.start_time,'MM')::int start_month 
        FROM display_agreement_detail d 
        LEFT JOIN display_agreement_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID}
        WHERE 
            d.company_id = {companyID} and approve_time is not null and red_flag is null and supcust_id = {supcust_id} and items_id ='money' 
    ) t 
    where display_left_money > 0
) t
LEFT JOIN cw_subject c on c.sub_id = t.display_fee_id and c.company_id = {companyID} 
{dispCondi} 
GROUP BY display_fee_id,display_fee_name,display_remark,display_id,display_no;
                    ";
                displaySheets = await CDbDealer.GetRecordsFromSQLAsync(sql,cmd);
            }
            return new JsonResult(new { result = "OK" , displaySheets });
        }

        //[HttpGet]
        //public async Task<IActionResult> GetSubInfo(string operKey, string item_id)
        //{
        //    Security.GetInfoFromOperKey(operKey, out string companyID);
        //    // var model = new SaleSheetModel();
        //    string sql = $"select sub_id as from info_item_multi_unit where company_id={companyID} and item_id={item_id}";

        //    dynamic units = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
        //    //string items=Newtonsoft.Json.JsonConvert.SerializeObject(rec); 

        //    return new JsonResult(new { result = "OK", item = new { item_id, units } });
        //}
        [HttpGet]
        public async Task<IActionResult> GetClientAccountInfo(string operKey, string supcust_id)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            var dispCondi = " where t.disp_month<=(to_char(now(),'MM')::int) and disp_year=(to_char(now(),'YYYY')::int) ";
            string sql = $"select rights->'delicacy'->'allowAdvanceDisplayFee'->'value' allow_advance_disp_fee from info_operator o left join info_role r on r.role_id= o.role_id where o.company_id={companyID} and oper_id={operID}";
            dynamic advanceDisp = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            string restrictPaywayCondi = $"(t.sub_id::text IN(SELECT json_array_elements_text(avail_pay_ways) AS individual_value FROM info_operator WHERE company_id = {companyID} and oper_id = {operID} and restrict_pay_ways = TRUE) OR (select COUNT(*) from info_operator where company_id = {companyID} and oper_id = {operID} and restrict_pay_ways = TRUE) = 0)";
            if (advanceDisp != null && advanceDisp.allow_advance_disp_fee == "true")
                dispCondi = $" ";
            SQLQueue QQ = new SQLQueue(cmd);
            sql = @$"select t.sub_id,sub_name,sum(disp_left_money) disp_amount from 
                            (select sub_id,disp_left_money,
                                (case when start_month+months-1>12 then start_month+months-13 else start_month+months-1 end) disp_month,
                                (case when start_month+months-1>12 then start_year+1 else start_year end) as disp_year,months as disp_month_id
                                from (
                                select m.fee_sub_id sub_id,d.sheet_id,items_id,items_name,unnest(string_to_array((								
                                        COALESCE(month1_qty,0)-COALESCE(month1_given,0)||','||COALESCE(month2_qty,0)-COALESCE(month2_given,0)||','||
                                        COALESCE(month3_qty,0)-COALESCE(month3_given,0)||','||COALESCE(month4_qty,0)-COALESCE(month4_given,0)||','||
                                        COALESCE(month5_qty,0)-COALESCE(month5_given,0)||','||COALESCE(month6_qty,0)-COALESCE(month6_given,0)||','||
                                        COALESCE(month7_qty,0)-COALESCE(month7_given,0)||','||COALESCE(month8_qty,0)-COALESCE(month8_given,0)||','||
                                        COALESCE(month9_qty,0)-COALESCE(month9_given,0)||','||COALESCE(month10_qty,0)-COALESCE(month10_given,0)||','||
                                        COALESCE(month11_qty,0)-COALESCE(month11_given,0)||','||COALESCE(month12_qty,0)-COALESCE(month12_given,0)) ,','))::numeric disp_left_money,
                                        unnest(string_to_array('01,02,03,04,05,06,07,08,09,10,11,12',','))::int months,to_char(m.start_time,'YYYY')::int start_year,
                                        to_char(m.start_time,'MM')::int start_month 
                                from display_agreement_detail d left join display_agreement_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID}
                                where d.company_id = {companyID} and approve_time is not null and red_flag is null and supcust_id = {supcust_id} and items_id ='money' ) t
                            where disp_left_money > 0 ) t
                    left join cw_subject c on c.sub_id = t.sub_id and c.company_id = {companyID} 
                    {dispCondi} and {restrictPaywayCondi} GROUP BY t.sub_id,sub_name;
                    ";
            QQ.Enqueue("disp", sql);
            sql = @$"
select sup.mobile,sup.sup_addr,sup.boss_name,sup.license_no,sup.acct_cust_id,acct.sup_name acct_cust_name
from info_supcust sup
LEFT JOIN info_supcust acct on acct.company_id = {companyID} and sup.acct_cust_id = acct.supcust_id  
where sup.company_id = {companyID} and sup.supcust_id = {supcust_id}
";
            QQ.Enqueue("info", sql);
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            var disp = new List<ExpandoObject>();
            dynamic info = null;
            while (QQ.Count > 0)
            {
                string sqlName = QQ.Dequeue();
                if (sqlName == "disp")
                {
                    disp = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "info")
                {
                    info = CDbDealer.Get1RecordFromDr(dr, false);
                }
            }
            QQ.Clear();
            return new JsonResult(new { result = "OK", disp, info.mobile, info.sup_addr, info.boss_name, info.license_no,info.acct_cust_id,info.acct_cust_name});
        }
        public class test {
            public string id = "", name = "", tt="";
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic dSheet)  //[FromBody] dynamic sheet)
        {
            string s = JsonConvert.SerializeObject(dSheet);
            SheetFeeOut sheet = (SheetFeeOut)JsonConvert.DeserializeObject<SheetFeeOut>(s);
            sheet.Init();
            sheet._httpClientFactory = this._httpClientFactory;
            await sheet.ProcessPcAppendix();
            string msg = await sheet.Save(cmd);
            string result = msg== "" ? "OK": "Error";
            return new JsonResult(new { result, msg,sheet.sheet_id,sheet.sheet_no});
        }

        [HttpPost]
        public async Task<IActionResult> SaveAndApprove([FromBody] dynamic dSheet)
        {
            string s = JsonConvert.SerializeObject(dSheet);
            SheetFeeOut sheet = (SheetFeeOut)JsonConvert.DeserializeObject<SheetFeeOut>(s);
            sheet.Init();
            sheet._httpClientFactory = this._httpClientFactory;
            await sheet.ProcessPcAppendix();
            string msg = " ";
            if (sheet.isRedAndChange)
            {
                if (!sheet.old_sheet_id.IsValid())
                {
                    msg = "修改时没有获取原单据的编号";
                }
                else
                {
                    msg = await sheet.RedAndChange<SheetFeeOut>(cmd);
                }
            }
            else
            {
                msg = await sheet.SaveAndApprove(cmd);
            }

            string fee_to_income_sheet_id = "";//费用单生成的收入单（代厂家支付）
            string fee_to_income_sheet_no = "";
            if (sheet.incomeSheetInfo != "")
            {
                fee_to_income_sheet_id = JsonConvert.DeserializeObject<IncomeSheetInfo>(sheet.incomeSheetInfo).income_sheet_id;
                fee_to_income_sheet_no = JsonConvert.DeserializeObject<IncomeSheetInfo>(sheet.incomeSheetInfo).income_sheet_no;
            }

            string result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg , sheet.sheet_id,sheet.sheet_no,sheet.happen_time,sheet.approve_time, fee_to_income_sheet_id, fee_to_income_sheet_no });  
        }



        [HttpPost]
        public async Task<IActionResult> Red([FromBody] dynamic data)
        {
            string sheet_id = data.sheet_id;
            string operKey = data.operKey;
            string redBrief = data.redBrief;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetFeeOut sheet = new SheetFeeOut(SHEET_FEE_OUT.EMPTY, LOAD_PURPOSE.SHOW);
            sheet._httpClientFactory = this._httpClientFactory;
            string msg = await sheet.Red(cmd, companyID, sheet_id, operID,redBrief);

            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time });
        }

        [HttpPost]
        public async Task<IActionResult> Delete([FromBody] dynamic data)
        {
            string sheet_id = data.sheet_id;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetFeeOut sheet = new SheetFeeOut(SHEET_FEE_OUT.EMPTY, LOAD_PURPOSE.SHOW);

            string msg = await sheet.Delete(cmd, companyID, sheet_id, operID);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time });
        }

        [HttpPost]
        public async Task<IActionResult> AppendBrief([FromBody] dynamic data)
        {
            string sheetID = data.sheetID;
            string newBrief = data.newBrief;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetFeeOut sheet = new SheetFeeOut(SHEET_FEE_OUT.EMPTY, LOAD_PURPOSE.APPROVE);

            string msg = await sheet.AppendBrief(cmd, companyID, sheetID, newBrief);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, });
        }

    }


}