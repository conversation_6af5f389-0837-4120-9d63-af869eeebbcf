﻿using ArtisanManage.Models;
using ArtisanManage.MyCW;
using ArtisanManage.Pages.CwPages;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using myJXC;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;

namespace ArtisanManage.MyJXC
{
    public class SheetRowArrears : SheetRowBase
    {

        [SaveToDB] [FromFld] public string mm_sheet_id { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string mm_sheet_no { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string mm_seller_name { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string order_sheet_no { get; set; }
        [FromFld("s.order_sheet_id",LOAD_PURPOSE.SHOW)] public string order_sheet_id { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string mm_sheet_time { get; set; }
        public string mm_sheet_date
        {
            get
            {
                return CPubVars.GetDateTextNoTime(mm_sheet_time);
            }
        }
        [FromFld(LOAD_PURPOSE.SHOW)] public string mm_make_brief { get; set; }

        [FromFld("s.sup_name")] public string shop_name { get; set; }
        [SaveToDB] [FromFld] public string m_sheet_type { get; set; }
        public string mm_sheet_type_name { get {return m_sheet_type.Replace("CT", "采购退货").Replace("X" , "销售").Replace("T","退货").Replace("DH","定货会").Replace("YS","预收").Replace("ZC","费用支出").Replace("YF","预付").Replace("CG","采购").Replace("SR","其他收入");} }
        [SaveToDB] [FromFld] public decimal sheet_amount { get; set; }
        [SaveToDB] [FromFld] public decimal paid_amount { get; set; }
        [SaveToDB] [FromFld] public decimal disc_amount { get; set; }
        [SaveToDB] [FromFld] public decimal now_pay_amount { get; set; }
        [SaveToDB] [FromFld] public decimal now_disc_amount { get; set; }
        [SaveToDB] [FromFld] public decimal left_amount { get; set; }
        [SaveToDB] [FromFld] public override string remark { get; set; }


    }

    public enum SHEET_GET_ARREARS
    {
        EMPTY,
        IS_GET,
        NOT_GET

    }
    public class SheetGetArrears : SheetBase<SheetRowArrears>
    {
        public List<string> appendixPhotos { get; set; } = new List<string>();
        public string appendix_photos { get; set; } = "";
        [SaveToDB] [FromFld] public string visit_id { get; set; } = "";
        [SaveToDB][FromFld] public string review_time { get; set; } = "";
        [SaveToDB][FromFld] public string reviewer_id { get; set; } = "";
        public bool bReview { get; set; } = false;

        [FromFld(LOAD_PURPOSE.SHOW)] public string reviewer_name { get; set; } = "";
        [SaveToDB] [FromFld] public string supcust_id { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string sup_name { get; set; } = "";
        [SaveToDB] [FromFld] public override int money_inout_flag { get; set; }
        [SaveToDB] [FromFld] public decimal sheet_amount { get; set; }
        [SaveToDB] [FromFld] public decimal paid_amount { get; set; }
        [SaveToDB] [FromFld] public decimal disc_amount { get; set; }
        [SaveToDB] [FromFld] public decimal now_pay_amount { get; set; }
        [SaveToDB] [FromFld] public decimal now_disc_amount { get; set; }
        [SaveToDB] [FromFld] public decimal left_amount { get; set; }

        [SaveToDB] [FromFld] public string payway1_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway1_name { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway1_type { get; set; } = "";
        [SaveToDB] [FromFld] public decimal payway1_amount { get; set; }
        [SaveToDB] [FromFld] public string payway2_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway2_name { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway2_type { get; set; } = "";
        [SaveToDB] [FromFld] public decimal payway2_amount { get; set; }
        [SaveToDB][FromFld] public string payway3_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway3_name { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway3_type { get; set; } = "";
        [SaveToDB][FromFld] public decimal payway3_amount { get; set; }
       
        [SaveToDB] [FromFld] public string getter_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string getter_name { get; set; } = "";
        [SaveToDB] [FromFld] public override SHEET_TYPE sheet_type { get; set; }
        internal string mmSheetTable = "";
        [SaveToDB]
        [FromFld]
        public virtual string sheet_attribute
        {
            get
            {
                Dictionary<string, string> sheetAttribute = new Dictionary<string, string>();
                if (appendix_photos != "" && appendix_photos != "[]")
                {
                    sheetAttribute.Add("appendixPhotos", appendix_photos);
                }


                string s = "";
                if (sheetAttribute.Count > 0) s = Newtonsoft.Json.JsonConvert.SerializeObject(sheetAttribute);

                return s;
            }
            set
            {
                if (!string.IsNullOrEmpty(value))
                {
                    dynamic sheetAttr = JsonConvert.DeserializeObject(value);

                    if (sheetAttr.appendixPhotos != null)
                    {
                        this.appendix_photos = sheetAttr.appendixPhotos;
                    }
                }
            }
        }
        [SaveToDB] [FromFld] public override string is_imported { get; set; } = "";
        // 对账单要恢复
        [SaveToDB][FromFld] public string order_sheet_id { get; set; } = "";
        [SaveToDB][FromFld] public string order_sheet_no { get; set; } = "";
        /*
        public SheetGetArrears() : base("sheet_get_arrears_main", "sheet_get_arrears_detail", LOAD_PURPOSE.ALL)
        {

        }*/
        public SheetGetArrears(SHEET_GET_ARREARS sheetGetArrears, LOAD_PURPOSE loadPurpose) : base("sheet_get_arrears_main", "sheet_get_arrears_detail", loadPurpose)
        {
            sheet_type = sheetGetArrears == SHEET_GET_ARREARS.NOT_GET ? SHEET_TYPE.SHEET_PAY_MONEY : SHEET_TYPE.SHEET_GET_MONEY;
            ConstructFun();
        }
        private void ConstructFun()
        {
            mmSheetTable = "sheet_sale_main";
            if (sheet_type == SHEET_TYPE.SHEET_PAY_MONEY) mmSheetTable = "sheet_buy_main";
            money_inout_flag = sheet_type == SHEET_TYPE.SHEET_PAY_MONEY ? -1 : 1;

            if (LoadPurpose == LOAD_PURPOSE.SHOW)
            {
                MainLeftJoin = @" left join info_supcust c on t.supcust_id=c.supcust_id and c.company_id=~COMPANY_ID
                                  left join (select oper_id,oper_name as getter_name from info_operator where company_id=~COMPANY_ID) getter on t.getter_id=getter.oper_id
                                  left join (select oper_id,oper_name as maker_name from info_operator where company_id=~COMPANY_ID) maker on t.maker_id=maker.oper_id
                                  left join  (select oper_id,oper_name as approver_name from info_operator where company_id=~COMPANY_ID) approver on t.approver_id=approver.oper_id
                                  left join (select sub_id,sub_name as payway1_name,sub_type payway1_type from cw_subject where company_id=~COMPANY_ID) pw1 on t.payway1_id=pw1.sub_id
                                  left join (select sub_id,sub_name as payway2_name,sub_type payway2_type from cw_subject where company_id=~COMPANY_ID) pw2 on t.payway2_id=pw2.sub_id
                                  left join (select sub_id,sub_name as payway3_name,sub_type payway3_type from cw_subject where company_id=~COMPANY_ID) pw3 on t.payway3_id=pw3.sub_id
                                  left join (select oper_id,oper_name as reviewer_name,company_id from info_operator where company_id=~COMPANY_ID) reviewer on t.reviewer_id=reviewer.oper_id  
              
              ";
                DetailLeftJoin = @$"
left join
(
   select a.*,b.sup_name from 
            (
                        select supcust_id, sheet_id,sheet_no as mm_sheet_no,ifs.oper_name mm_seller_name,seller_id,order_sheet_no,sm.order_sheet_id,supcust_id as mm_supcust_id,happen_time as mm_sheet_time,sheet_type as m_sheet_type,make_brief mm_make_brief,total_amount as mm_sheet_amount,money_inout_flag as mm_money_inout_flag 
                           from sheet_sale_main sm left join (select oper_id,oper_name,company_id from info_operator where company_id=~COMPANY_ID) ifs on sm.seller_id=ifs.oper_id 
                           left join 
                           (
                              select sheet_id order_sheet_id,sheet_no order_sheet_no from sheet_sale_order_main 
                              where company_id=~COMPANY_ID
                           ) om on sm.order_sheet_id=om.order_sheet_id 
                           where sm.company_id=~COMPANY_ID and sm.sheet_id in (VAR_sale_sheets_id)
                           UNION 
                              select supcust_id,sheet_id,sheet_no as mm_sheet_no,ifs.oper_name mm_seller_name,seller_id,order_sheet_no,sm.order_sheet_id,supcust_id as mm_supcust_id,happen_time as mm_sheet_time,sheet_type as m_sheet_type,make_brief mm_make_brief,total_amount as mm_sheet_amount,money_inout_flag as mm_money_inout_flag 
                            from sheet_buy_main sm left join (select oper_id,oper_name,company_id from info_operator where company_id=~COMPANY_ID) ifs on sm.seller_id=ifs.oper_id 
                             left join 
                           (
                              select sheet_id order_sheet_id,sheet_no order_sheet_no from sheet_buy_order_main 
                              where company_id=~COMPANY_ID
                           ) om on sm.order_sheet_id=om.order_sheet_id
                              where sm.company_id=~COMPANY_ID and sm.sheet_id in (VAR_buy_sheets_id)
                           UNION
                              select supcust_id,sheet_id,sheet_no as mm_sheet_no,null mm_seller_name,null seller_id,null order_sheet_no,null order_sheet_id,supcust_id as mm_supcust_id,happen_time as mm_sheet_time,sheet_type as m_sheet_type,make_brief mm_make_brief,total_amount as mm_sheet_amount,money_inout_flag as mm_money_inout_flag from sheet_prepay
                              where company_id=~COMPANY_ID and sheet_id in (VAR_prepay_sheets_id)
                           UNION
                              select supcust_id,sheet_id,sheet_no as mm_sheet_no,null mm_seller_name,null seller_id,null order_sheet_no,null order_sheet_id,supcust_id as mm_supcust_id,happen_time as mm_sheet_time,sheet_type as m_sheet_type,make_brief mm_make_brief,total_amount as mm_sheet_amount,money_inout_flag as mm_money_inout_flag from sheet_fee_out_main 
                              where company_id=~company_id and sheet_id in (VAR_fee_sheets_id)
            ) a left join info_supcust b on a.supcust_id=b.supcust_id and b.company_id=~company_id
)
s on t.mm_sheet_id=s.sheet_id and t.m_sheet_type = s.m_sheet_type
                            
                    ";
            }
        }
        public override async Task BeforeLoad(CMySbCommand cmd, string companyID, string sheetID)
        {
            List<System.Dynamic.ExpandoObject> lstSheets = await CDbDealer.GetRecordsFromSQLAsync($"select mm_sheet_id,m_sheet_type from sheet_get_arrears_detail where company_id={companyID} and sheet_id in ({sheetID});", cmd);
            Variables = new Dictionary<string, string>() {
                { "sale_sheets_id","null" },{ "buy_sheets_id","null" },{ "prepay_sheets_id","null" },{ "fee_sheets_id","null" }

            };

            foreach (dynamic sht in lstSheets)
            {
                string mmSheetsID = "";
                string key = "";
                switch (sht.m_sheet_type)
                {
                    case "X":
                    case "T":
                        key = "sale_sheets_id"; break;
                    case "CG":
                    case "CT":
                        key = "buy_sheets_id"; break;
                    case "YS":
                    case "YF":
                    case "DH":
                        key = "prepay_sheets_id"; break;
                    case "SR":
                    case "ZC":
                        key = "fee_sheets_id"; break;
                }
                if (this.Variables.ContainsKey(key))
                {
                    mmSheetsID = this.Variables[key];
                }
                if (mmSheetsID != "") mmSheetsID += ",";
                mmSheetsID += sht.mm_sheet_id;
                this.Variables[key] = mmSheetsID;
            }
        }
        public SheetGetArrears(LOAD_PURPOSE loadPurpose) : base("sheet_get_arrears_main", "sheet_get_arrears_detail", loadPurpose)
        {
            sheet_type = SHEET_TYPE.SHEET_GET_MONEY;
            ConstructFun();
        }
        public SheetGetArrears() : base("sheet_get_arrears_main", "sheet_get_arrears_detail", LOAD_PURPOSE.SHOW)
        {
            sheet_type = SHEET_TYPE.SHEET_GET_MONEY;
            ConstructFun();
        }

        protected override void InitForSave()
        {
            base.InitForSave();
            if (getter_id == "") getter_id = OperID;

        }

        protected override async Task<string> CheckSaveSheetValid(CMySbCommand cmd = null)
        {
            var check = await base.CheckSaveSheetValid(cmd);
            if (check != "OK") return check;
            if (supcust_id == "") return "必须指定客户";
            //if (getter_id == "" && IsFromWeb) return "必须指定业务员";
             if (payway1_id == "") return "必须指定支付方式";
            if (SheetRows.Count == 0) return "单据不存在明细行";
            if (Math.Abs(now_pay_amount -payway1_amount - payway2_amount -payway3_amount)>0.05m)
            {
                string payways = payway1_name;
                if (payway2_name != "")
                {
                    payways += ",";
                    payways += payway2_name;
                }
                if (payway3_name != "")
                {
                    payways += ",";
                    payways += payway3_name;
                }
                return payways + "支付的金额和本次应该支付的金额不一致";
            }
            decimal total_now_pay_amount = 0;
            decimal total_now_disc_amount = 0;
            decimal total_now_left_amount = 0;
            foreach (SheetRowArrears row in SheetRows)
            {
                total_now_pay_amount += row.now_pay_amount;
                total_now_disc_amount += row.now_disc_amount;
                total_now_left_amount += row.left_amount;
                var row_left_amount = row.sheet_amount - row.paid_amount - row.disc_amount - row.now_disc_amount - row.now_pay_amount;
                if (Math.Abs(row_left_amount - row.left_amount) > 0.1m) 
                      return $"单据{row.mm_sheet_no}的尚欠金额与实际尚欠不等";
                //if (!IsImported && row.now_pay_amount / row.sheet_amount < 0 )
                //{
                //   if( row.sheet_amount > 0)  return $"单据{row.mm_sheet_no}的本次支付金额必须是正数";
                //   else return $"单据{row.mm_sheet_no}的本次支付金额必须是负数";
                //}
                /*if (!IsImported && row.now_disc_amount * row.sheet_amount < 0)
                {
                   if (row.sheet_amount > 0) return $"单据{row.mm_sheet_no}的本次优惠金额必须是正数"; 
                   else return $"单据{row.mm_sheet_no}的本次优惠金额必须是负数";
                }*/
            }

            if (Math.Abs(total_now_pay_amount - now_pay_amount) > 0.1m) return "明细行本次支付合计与总的本次支付不等";
          
            if (Math.Abs(total_now_disc_amount - now_disc_amount) > 0.1m) return "明细行本次优惠合计与总的本次优惠不等";

            if (Math.Abs(total_now_left_amount - left_amount) > 0.1m)
                return "明细行尚欠合计与总的尚欠不等";

            if (this.happen_time != "" )
            {
                if(!this.happen_time.Contains(":"))
                   this.happen_time += " 23:59:59"; 
                if (this.happen_time.Contains("00:00:00"))
                {
                    this.happen_time = this.happen_time.Replace("00:00:00", "23:59:59");
				}

			}
            return "OK";
        }

        protected override async Task<string> CheckSheetValid(CMySbCommand cmd = null)
        {
            var check = await base.CheckSheetValid(cmd);
            if (check != "OK") return check;


            List<SheetRowArrears> lstToRemove = new List<SheetRowArrears>();
            sheet_amount = 0;
            paid_amount = 0;
            disc_amount = 0;
            left_amount = 0;

            foreach (SheetRowArrears row in SheetRows)
            {
                if (row.now_disc_amount == 0 && row.now_pay_amount == 0)
                {
                    lstToRemove.Add(row);
                }
                else
                {
                    paid_amount += row.paid_amount;
                    disc_amount += row.disc_amount;
                    left_amount += row.left_amount;
                    sheet_amount += row.sheet_amount;
                }
            }
            disc_amount = Math.Round(disc_amount, 2);
            paid_amount = Math.Round(paid_amount, 2);
            left_amount = Math.Round(left_amount, 2);
            sheet_amount = Math.Round(sheet_amount, 2);
            foreach (var row in lstToRemove)
            {
                SheetRows.Remove(row);
            }


            return "OK";
        }
        
        public override string GetSheetCharactor()
        {
            string res =  this.company_id + "_" + this.sheet_id + "_" + this.supcust_id + "_";
            foreach (var row in SheetRows)
            {
                res += row.mm_sheet_id+"_";
            }
            return res;
        }
        protected override async Task<string> CheckForRed(CMySbCommand cmd)
        {
            return await CheckForRed_MoneySheet(cmd);
        }
        class CInfoForApprove : CInfoForApproveBase
        {
            //public string ArrearBalance = "";
            public string subID = "";
            public float ChangeBal = 0;
            public float Balance = 0;
            public float paidAmount = 0;
            public float discAmount = 0;
            public bool RoleAllowNegativePrepay = false; //预收款是否允许负
            //public string ErrorMsg = "";//基类里已有ErrMsg,这里不需要再定义一个,两个就容易混淆，造成BUG
            //  public bool BalanceExist = false;

            public Dictionary<string, SellerArrearBalance> SellersBalance = new Dictionary<string, SellerArrearBalance>();
            public List<Subject> PrepaySubjects = new List<Subject>();
            public List<Subject> PaywaysInfo = new List<Subject>();

        }
        protected class Subject
        {
            public string sub_id { get; set; }
            public string sub_name { get; set; }
            public string balance { get; set; }
            public string sub_type { get; set; }
        }
        protected override void NeedUpdateClientHistory(out string supcustID, out bool updateArrears, out string updatePrepaySubIDs)
        {
            supcustID = supcust_id;
            updateArrears = true;
            updatePrepaySubIDs = "";
            if (payway1_type == "YS" || payway1_type == "YF")
            {
                if (updatePrepaySubIDs != "") updatePrepaySubIDs += ',';
                updatePrepaySubIDs += payway1_id;
            }
            if (payway2_type == "YS" || payway2_type == "YF")
            {
                if (updatePrepaySubIDs != "") updatePrepaySubIDs += ',';
                updatePrepaySubIDs += payway2_id;
            }
            if (payway3_type == "YS" || payway3_type == "YF")
            {
                if (updatePrepaySubIDs != "") updatePrepaySubIDs += ',';
                updatePrepaySubIDs += payway3_id;
            }

        }
        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            base.GetInfoForApprove_SetQQ(QQ);
            string sql;
            //sql = GetSqlForArrearsQQ(supcust_id, getter_id);
            //QQ.Enqueue("arrear_balance", sql);
            sql = $"select balance from arrears_balance where company_id={company_id} and supcust_id={supcust_id}";
            QQ.Enqueue("arrears_balance", sql);
            sql = $"select sub_id from cw_subject where company_id={company_id} and sub_type='QK'";
            QQ.Enqueue("sub_id", sql);
            string sub_ids = payway1_id;
            if (this.payway1_name == "预收款")
            {

            }
            if (this.sheet_no == "SK230608197001")
            {

            }
           if (payway2_id != "")
            {
                if (sub_ids != "") sub_ids += ","; sub_ids += payway2_id;
            }
            if (payway3_id != "")
            {
                if (sub_ids != "") sub_ids += ","; sub_ids += payway3_id;
            }
            if (sub_ids != "")
            {
                sql = $"select s.sub_id,sub_name,balance,sub_type from cw_subject s left join (select sub_id,balance from prepay_balance where supcust_id={supcust_id}) b on s.sub_id=b.sub_id  where sub_type in ('YS','YF');";
                QQ.Enqueue("prepay_sub", sql);
                sql = $"select sub_id, sub_type, sub_name from cw_subject where company_id={company_id} and sub_id in ({sub_ids});";
                QQ.Enqueue("payway_type", sql);
            }
            if (OperID != "")
            {
                sql = $"select rights->'delicacy'->'allowNegativeStock'->'value' role_allow_negative_stock,rights->'delicacy'->'allowNegativePrepay'->'value' role_allow_negative_prepay,rights->'delicacy'->'allowNoStockHH'->'value' role_allow_no_stock_hh from info_operator o left join info_role r on r.role_id= o.role_id where o.company_id={company_id} and oper_id={OperID}";
                QQ.Enqueue("role_rights", sql);
            }
            // var mmSheetIDs = "";
            sql = "";
			if (SheetRows.Count == 0)
			{
                throw new Exception("单据没有明细行");
			}
            foreach (var row in SheetRows)
            { 
                if (sql != "") sql += " union ";
                string mmSheetTable = GetTableFromSheetType(row.m_sheet_type);
                if(!mmSheetTable.IsValid())
				{
                    throw new Exception($"单据类型" + row.m_sheet_type+"是未知单据类型");
				}
                string flag = "";
                if (sheet_type == SHEET_TYPE.SHEET_PAY_MONEY) flag = "*(-1)";
                string sellerFld = "seller_id";
                if (",ZC,SR,DH,YS,YF,".Contains("," + row.m_sheet_type + ",")) sellerFld = "getter_id seller_id";
               
                sql += $"select sheet_id,sheet_no,{sellerFld},red_flag,paid_amount*money_inout_flag {flag} as paid_amount ,disc_amount * money_inout_flag {flag} as disc_amount from {mmSheetTable} where company_id = {company_id} and sheet_id = {row.mm_sheet_id}";
                //  mmSheetIDs += row.mm_sheet_id; * money_inout_flag as paid_amount;;* money_inout_flag as disc_amount 
            }
            sql = @$"
select tb.*,o.oper_name seller_name,o.seller_max_arrears,aba.auxiliary_balance seller_balance from ({sql}) tb 
left join info_operator o on tb.seller_id=o.oper_id and o.company_id={company_id}
left join arrears_balance_auxiliary aba on tb.seller_id=aba.auxiliary_id and aba.auxiliary_type='seller' and aba.company_id={company_id}
;";
   
          //  sql = $"selecst sheet_id,paid_amount,disc_amount from {mmSheetTable} where company_id = {company_id} and sheet_id in ({mmSheetIDs});";
            QQ.Enqueue("mm_sheet", sql);
            if (!IsImported)
			{
                SetQQForWeChatInfo(QQ, supcust_id);
            }
            
        }
        class SellerArrearBalance
        {
            public string seller_name = "";
            public decimal max_arrears = 0m;
            public decimal balance = 0m;
            public decimal change_balance = 0m;
        }
        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            base.GetInfoForSave_ReadData(dr, sqlName, bForRed);
        }
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
         
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;
            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);
            
            bool isRed = false;
            if (bForRed) isRed = true;
            if (sqlName == "arrears_balance")
            {
                //DealArrearReadQQ(dr, info, left_amount, supcust_id);
                dynamic record = CDbDealer.Get1RecordFromDr(dr, false);
                if (record != null)
                {
                   // info.BalanceExist = true;
                    info.ArrearsBalance = record.balance;
                }
            }
            else if (sqlName == "sub_id")
            {
                dynamic record = CDbDealer.Get1RecordFromDr(dr, false);

                if (record != null)
                {
                    info.subID = record.sub_id;
                }
            }
            else if (sqlName == "prepay_sub")
            {
                info.PrepaySubjects = CDbDealer.GetRecordsFromDr<Subject>(dr, false);
            }
            else if (sqlName == "mm_sheet")
            {
                List<System.Dynamic.ExpandoObject> sheetList = CDbDealer.GetRecordsFromDr(dr, false);
                if (sheetList != null  )
                {                   
                     
                    foreach (var row in SheetRows)
                    {
                        dynamic queryRow = sheetList.Find(queryRow => ((dynamic)queryRow).sheet_id == row.mm_sheet_id);
                        if (queryRow != null)
                        {
                            if (!isRed)
                            {
                                if (queryRow.red_flag != "")
                                {
                                    info.ErrMsg = $"单据{queryRow.sheet_no}已被红冲, 请删除此单";
                                    return;
                                }

                                decimal paid_amount = CPubVars.ToDecimal(queryRow.paid_amount);
                                decimal disc_amount = CPubVars.ToDecimal(queryRow.disc_amount);

                                decimal nDiff = 0.1m;
                                if(queryRow.sheet_no== "XS22042825008")
								{

								}
                                if (IsImported) nDiff = 3m; //导入舟谱数据时差异会比较大
                                if (Math.Abs(paid_amount - row.paid_amount) > nDiff)
                                {
                                    if (SheetType == "SK") info.ErrMsg = $"单号  {queryRow.sheet_no}   的已支付金额已经由{row.paid_amount}变为{paid_amount},请重新开此收款单";
                                    if (SheetType == "FK") info.ErrMsg = $"单号{queryRow.sheet_no}的已支付金额已经{row.paid_amount}变为{paid_amount},请重新开此付款单";
                                    return;
                                }

                                if (Math.Abs(disc_amount - row.disc_amount) > nDiff)
                                {
                                    info.ErrMsg = "";
                                    info.ErrMsg = $"单{queryRow.sheet_no}的已优惠金额已由{row.disc_amount}变为{disc_amount},请重新开此收款单";
                                    if (SheetType == "FK") info.ErrMsg = info.ErrMsg.Replace("收款单", "付款单");
                                    return;
                                }
                            }

                            if (queryRow.seller_max_arrears != "")
                            {
                                SellerArrearBalance bal;
                                if (!info.SellersBalance.ContainsKey(queryRow.seller_id))
                                {
                                    bal = new SellerArrearBalance();
                                    info.SellersBalance.Add(queryRow.seller_id, bal);
                                    bal.seller_name = queryRow.seller_name;
                                    if (queryRow.seller_balance != "")
                                    {
                                        bal.balance = CPubVars.ToDecimal(queryRow.seller_balance); 
                                        bal.max_arrears = CPubVars.ToDecimal(queryRow.seller_max_arrears);
                                    }
                                }
                                else
                                {
                                    bal = info.SellersBalance[queryRow.seller_id]; 
                                }
                                bal.change_balance += -money_inout_flag * (row.now_pay_amount + row.now_disc_amount);
                               
                            }
                        }
                    }
                    if (red_flag == "2")
                    {
                        foreach (var kp in info.SellersBalance)
                        {
                            var bal = kp.Value;
                            var new_balance = bal.balance + bal.change_balance;
                            if (new_balance > bal.max_arrears)
                            {
                                info.ErrMsg = $"业务员{bal.seller_name}欠款超出了最大限额{bal.max_arrears}";
                            }
                        }
                    }
                   
                }
            }
            else if (sqlName == "payway_type")
            {
                info.PaywaysInfo = CDbDealer.GetRecordsFromDr<Subject>(dr, false);
            }
            else if (sqlName == "role_rights")
            {
                dynamic right = CDbDealer.Get1RecordFromDr(dr);
                if (right != null)
                {
                    string r = right.role_allow_negative_prepay;
                    if (r.ToLower() == "true") info.RoleAllowNegativePrepay = true;
                }
            }

            ReadQQDataForWeChatInfo(sqlName, dr, info);
        }
        public async Task ProcessPcAppendix()
        {
            string images = appendix_photos;
            if (!string.IsNullOrEmpty(images) && images != "[]" && images.Contains("photos"))
            {
                // 使用 Newtonsoft.Json 解析 JSON 字符串
                var jsonObject = JsonConvert.DeserializeObject<Dictionary<string, List<string>>>(images);

                // 提取 photos 列表
                List<string> photos = jsonObject["photos"];
                appendix_photos = await ProcessAppendixPicsRetDBStr(photos);
            }
        }
        public async Task<string> ProcessAppendixPicsRetDBStr(List<string> appendix_pictures_base64)
        {
            var result = await CommonTool.ProcessAppendixPicsRetDBStr(_httpClientFactory, appendix_pictures_base64, company_id);
            return result;
        }
        private string GetTableFromSheetType(string sheetType)
        {
            string mmSheetTable = "";
            switch (sheetType)
            {
                case "ZC": case "SR": mmSheetTable = "sheet_fee_out_main"; break;
                case "X":case "T":  mmSheetTable = "sheet_sale_main"; break;
                case "CG": case "CT": mmSheetTable = "sheet_buy_main"; break;
                case "YS": case "YF": case "DH": mmSheetTable = "sheet_prepay"; break;

            }
            return mmSheetTable;
        }
       
        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1)
        {
            string preSql = "";
            string redSql = "";
            string sql = "";
            CInfoForApprove info = (CInfoForApprove)info1;
            //info.ChangeBal *= (-1);getApproveSQL那已经计算好了，这里不必要再乘以-1了
          
            decimal nChangeBal = (now_disc_amount + now_pay_amount) * money_inout_flag * (-1);
            decimal arrearsBal = 0;
            
            if (info.ArrearsBalance != "")
            {
                arrearsBal = CPubVars.ToDecimal(info.ArrearsBalance);
                
                //if (SheetType == "SK")
                //{
                //    if (arrearsBal + nChangeBal < -0.01)
                //    {
                //        info1.ErrMsg = "应收款余额为负，请检查单据！";
                //        return;
                //    }
                //}
                //else
                //{
                //    if (arrearsBal + nChangeBal > 0.01)
                //    {
                //        info1.ErrMsg = "应付款款余额为负，请检查单据！";
                //        return;
                //    }
                //}

                sql +=
                        $"update arrears_balance set balance=balance+({nChangeBal}) where company_id={company_id} and supcust_id={supcust_id};";
            }
            else
            {
                sql += $"insert into arrears_balance (company_id,supcust_id,balance) values ({company_id},{supcust_id},{nChangeBal});";
            }
            arrearsBal += nChangeBal;
          //  info.Balance = bal;

            foreach (var row in SheetRows)
            {
                decimal changePaidAmount = row.now_pay_amount;
                decimal changeDiscAmount = row.now_disc_amount;

                // if (row.now_pay_amount >=0) changePayAmount = "+" + row.now_pay_amount.ToString(); 
                string mmSheetTable = GetTableFromSheetType(row.m_sheet_type);
                string moneyGetTime = "";
                int updateFlag = 1;
                if (SheetType == "SK")
                {
                    if (row.m_sheet_type == "T" || row.m_sheet_type == "CG" || row.m_sheet_type == "ZC") updateFlag *= -1;
                }
                else if (SheetType == "FK")
                {
                    if (row.m_sheet_type == "X" || row.m_sheet_type == "CT" || row.m_sheet_type == "SR") updateFlag *= -1;
                }

                if (red_flag == "2")
                {
                    updateFlag *= -1;
                }

                if (row.m_sheet_type == "X" || row.m_sheet_type == "T" || row.m_sheet_type == "DH" || row.m_sheet_type == "YS")//在销售主表中记录结清欠款的时间
                {
                    if (red_flag == "2")
                    {
                        moneyGetTime = ",settle_time=null ";
                    }
                    else if (Math.Abs(row.left_amount) < 0.05m)
                    {
                        moneyGetTime = $",settle_time='{happen_time}'";
                    }
                }
                //var flag = 1;
                //if (SheetType == "SK" && row.m_sheet_type == "CG") flag *= -1;
                //if (SheetType == "FK" && (row.m_sheet_type == "X"||row.m_sheet_type=="ZC")) flag *= -1;

                //sql += $"update {mmSheetTable} set paid_amount=round((paid_amount + ({flag}*{changePaidAmount}*({updateFlag})) )::numeric,2),disc_amount=round((disc_amount+({changeDiscAmount}*({updateFlag})) )::numeric,2) {moneyGetTime} where company_id={company_id} and sheet_id={row.mm_sheet_id} ;";
                
                sql += $"update {mmSheetTable} set paid_amount=round((paid_amount + ({changePaidAmount}*({updateFlag})) )::numeric,2),disc_amount=round((disc_amount+({changeDiscAmount}*({updateFlag})) )::numeric,2) {moneyGetTime} where company_id={company_id} and sheet_id={row.mm_sheet_id} ;";
                if (Math.Abs(row.left_amount) < 0.02m)
                {
                    if(this.red_flag=="2")
						sql += $"update client_account_history set settle_time = null                 where company_id={company_id} and sheet_type='{row.m_sheet_type}' and sheet_id={row.mm_sheet_id};";
                    else 
					    sql += $"update client_account_history set settle_time = '{this.happen_time}' where company_id={company_id} and sheet_type='{row.m_sheet_type}' and sheet_id={row.mm_sheet_id};";
                }

                // 对账单修改
                string sheetSql = $"select d.sheet_id,m.real_left_amount from sheet_get_arrears_order_detail d left join sheet_get_arrears_order_main  m on d.sheet_id = m.sheet_id and d.company_id = m.company_id  where mm_sheet_id = {row.mm_sheet_id} and m_sheet_type = '{row.m_sheet_type}' and d.company_id={company_id} and red_flag is null and approve_time is not null order by m.happen_time desc ";
                dynamic sheetId = await CDbDealer.Get1RecordFromSQLAsync(sheetSql, cmd);
                string removeArrearsOrderId = "";
                if (sheetId != null )
                {
                    int reverse_flag = 1;
                    if (red_flag != "")
                    {
                        reverse_flag = -1;
                        // 红冲则还原标记
                        removeArrearsOrderId = $"update {mmSheetTable} set arrears_order_sheet_id = {sheetId.sheet_id} where company_id={company_id} and sheet_id={row.mm_sheet_id};";
                        sql += removeArrearsOrderId;
                    }else if (Math.Abs(row.left_amount) > 0.02m)
                    {
                        // 还有欠款就抹掉标记
                        removeArrearsOrderId = $"update {mmSheetTable} set arrears_order_sheet_id = null where company_id={company_id} and sheet_id={row.mm_sheet_id};";
                        sql += removeArrearsOrderId;
                    }
                    //sql += $"update sheet_get_arrears_order_main set real_left_amount = real_left_amount - ({changePaidAmount}*({updateFlag})) - ({changeDiscAmount}*({updateFlag})) where sheet_id = {sheetId.sheet_id} and company_id = {company_id}; ";
                    sql += $"update sheet_get_arrears_order_main set real_left_amount = real_left_amount - ({changePaidAmount}*{reverse_flag}) - ({changeDiscAmount}*{reverse_flag}) where sheet_id = {sheetId.sheet_id} and company_id = {company_id}; ";
                }
                 //row.mm_sheet_time

            }

 

			foreach (var kp in info.SellersBalance)
            {
                var bal = kp.Value;
                sql += $"update arrears_balance_auxiliary set auxiliary_balance = auxiliary_balance + ({bal.change_balance}) where company_id={company_id} and auxiliary_type='seller' and auxiliary_id={kp.Key};";
            }
                       
            string sRedFlag = "null";
            if (red_flag.IsValid())
            {
                sRedFlag = "'" + red_flag + "'";
                redSql = @$"update client_account_history set red_flag = '1' where company_id = {company_id} and sheet_id = {red_sheet_id} and sheet_type = '{SheetType}';";
                // 对账单标记位修改
                // redSql += $@"update sheet_get_arrears_order_main set order_status = 'red'where company_id = {company_id} and sheet_id = {order_sheet_id}";
            }

            sql += @$"
insert into client_account_history
        (company_id,  happen_time,                            approve_time,                         sheet_type,  sheet_id,change_amount,now_balance,  now_balance_happen_time,supcust_id,sub_type,red_flag) 
values ({company_id},'{CPubVars.GetDateText(happen_time)}','{CPubVars.GetDateText(approve_time)}', '{SheetType}',{sheetID},{nChangeBal},{arrearsBal},{arrearsBal-info.AccountHistoryArrearsChange},{supcust_id},'QK',   {sRedFlag});";
            if (!HappenNow)
                sql += $"update client_account_history set now_balance_happen_time=now_balance_happen_time+{nChangeBal} where company_id={company_id} and sub_type='QK' and supcust_id={supcust_id} and happen_time>'{CPubVars.GetDateText(happen_time)}' ;";


            if (info.PrepaySubjects != null)
            {
                decimal prepayTotalBalance = 0m;
                foreach (var sub in info.PrepaySubjects)
                { 
                    if (sub.balance.IsValid())
                    {
                        prepayTotalBalance += CPubVars.ToDecimal(sub.balance);
                    }
                }

                foreach (var sub in info.PrepaySubjects)
                {
                    decimal prepayAmt = 0;
                    if (payway1_id == sub.sub_id)
                    {
                        prepayAmt = payway1_amount;
                    }
                    else if (payway2_id == sub.sub_id)
                    {
                        prepayAmt = payway2_amount;
                    }
                    else if(payway3_id == sub.sub_id)
                    {
                        prepayAmt = payway3_amount;
                    }

                    if (prepayAmt != 0)
                    {  
                        int flag = 1;
                        if (red_flag == "2") flag = -1;
                        decimal prepayBalChange = -1 * prepayAmt * flag;//默认inoutFlag=1
                        //var prepayBalance = CPubVars.ToDecimal(sub.balance);
                        var prepayBalance = sub.balance == null ? 0 : CPubVars.ToDecimal(sub.balance);
                        prepayBalance += prepayBalChange;
                        prepayTotalBalance += prepayBalChange;

                        if (!info.RoleAllowNegativePrepay && prepayBalance < 0)
                        {
                            info1.ErrMsg = "预收款余额不能小于0";
                            return;
                        }

                        if (sub.balance.IsValid())
                        {
                            sql += $"update prepay_balance set balance=balance+({prepayBalChange}) where company_id={company_id} and supcust_id={supcust_id} and sub_id={sub.sub_id};";
                        }
                        else
                        {
                            sql += $"insert into prepay_balance (company_id,supcust_id,sub_id,balance) values ({company_id},{supcust_id},{sub.sub_id},{prepayBalChange});";
                        }
                         
                        if (red_flag.IsValid())
                        { 
                            redSql += @$"update client_account_history set red_flag = '1' where company_id = {company_id} and sheet_id = {red_sheet_id} and sheet_type = '{SheetType}' and sub_type = '{sub.sub_type}';";
                        }
                        GetAccountHistoryHappenTimePrepayBalance(info, supcust_id, prepayBalance, prepayTotalBalance, sub.sub_id, out string balance, out string totalBalance);
                        sql += @$"
insert into client_account_history(company_id  ,happen_time                          ,approve_time                          ,sheet_type   ,sheet_id ,change_amount    ,now_balance    ,now_prepay_balance  ,now_balance_happen_time,now_prepay_balance_happen_time,supcust_id  ,sub_id      ,sub_type        ,red_flag  ) 
                           values ({company_id},'{CPubVars.GetDateText(happen_time)}','{CPubVars.GetDateText(approve_time)}','{SheetType}',{sheetID},{prepayBalChange},{prepayBalance},{prepayTotalBalance},{balance},{totalBalance},{supcust_id},{sub.sub_id},'{sub.sub_type}',{sRedFlag});";
                        if (!HappenNow)
                        {
                            sql += $"update client_account_history set now_balance_happen_time=now_balance_happen_time+{prepayBalChange} where company_id={company_id} and supcust_id={supcust_id} and sub_type='{sub.sub_type}' and sub_id={sub.sub_id} and happen_time>'{CPubVars.GetDateText(happen_time)}' ;";
                            sql += $"update client_account_history set now_prepay_balance_happen_time=now_prepay_balance_happen_time+{prepayBalChange} where company_id={company_id} and supcust_id={supcust_id} and sub_type='{sub.sub_type}' and happen_time>'{CPubVars.GetDateText(happen_time)}' ;";
                        }
                            
                    }
                }
            }
            #region 陈列协议兑付
            if (info.PaywaysInfo != null && info.PaywaysInfo.Count > 0)
            {
                string zcSubIDs = "";
                foreach (var sub in info.PaywaysInfo)
                {
                    if (sub.sub_type == "ZC")
                    {
                        if (zcSubIDs != "") zcSubIDs += ",";
                        zcSubIDs += sub.sub_id;
                    }
                }
                if (zcSubIDs != "")
                {
                    string months = "";
                    string monthCondi = "";
                    string allGivenCondi = " and all_given is not true";
                    for (int i = 1; i <= 12; i++)
                    {
                        months += $",coalesce(month{i}_qty,0) as month{i}_qty,coalesce(month{i}_given,0) as month{i}_given";
                        if (monthCondi != "") monthCondi += " or ";
                        monthCondi += $" coalesce(month{i}_qty,0) - coalesce(month{i}_given,0) >0.01 ";
                    }
                    monthCondi = "(" + monthCondi + ")";
                    if (money_inout_flag == -1)
                    {
                        allGivenCondi = " ";
                        monthCondi = monthCondi.Replace("0.01", "-0.01");
                    }
                    string sqlQuery = @$"select d.sheet_id,start_time,end_time, fee_sub_id, flow_id {months} from display_agreement_detail d left join display_agreement_main m on m.sheet_id = d.sheet_id   where d.company_id = {company_id} and red_flag is null and approve_time is not null and supcust_id = {supcust_id} and items_id='money' and m.fee_sub_id in ({zcSubIDs}) and {monthCondi} {allGivenCondi};";

                    List<ExpandoObject> lstFlow = await CDbDealer.GetRecordsFromSQLAsync(sqlQuery, cmd);

                    for (int i = 1; i <= 3; i++)
                    {
                        string payway_id = ""; decimal payway_amount = 0;
                        if (i == 1) { payway_id = payway1_id; payway_amount = payway1_amount; }
                        else if (i == 2) { payway_id = payway2_id; payway_amount = payway2_amount; }
                        else if (i == 3) { payway_id = payway3_id; payway_amount = payway3_amount; }
                        if (payway_amount < 0)
                        {
                            info1.ErrMsg = "当支付金额小于零，不能用支出账户支付";
                            break;
                        }
                        decimal leftAmt1 = payway_amount;
                        if (money_inout_flag == 1)
                        {
                            foreach (dynamic flow in lstFlow)
                            {
                                decimal stillLeftAmt = 0;
                                string flowFields = "";
                                JObject jFlow = JsonConvert.DeserializeObject<JObject>(JsonConvert.SerializeObject(flow));
                                for (int m = 1; m <= 12; m++)
                                {
                                    string sQty = jFlow[$"month{m}_qty"].ToString();
                                    decimal qty = CPubVars.ToDecimal(sQty);
                                    string sGiven = jFlow[$"month{m}_given"].ToString();
                                    decimal given = CPubVars.ToDecimal(sGiven);
                                    decimal monthLeft = qty - given;
                                    var subID = flow.fee_sub_id;

                                    var DispflowID = flow.flow_id;
                                    if (payway_id == subID)
                                    {
                                        decimal useAmt = 0;
                                        if (leftAmt1 >= monthLeft) useAmt = monthLeft; else useAmt = leftAmt1;

                                        useAmt *= money_inout_flag;
                                        leftAmt1 -= useAmt;
                                        string fldName = $"month{m}_given";
                                        if (leftAmt1 >= 0 && useAmt != 0)
                                        {
                                            if (flowFields != "") flowFields += ",";
                                            flowFields += $"{fldName} =coalesce({fldName},0) +({useAmt})";
                                        }
                                        stillLeftAmt += qty - given - useAmt;
                                    }
                                }
                                if (flowFields != "")
                                {
                                    string allGiven = "true";
                                    if (stillLeftAmt > 0.01m) allGiven = "false";
                                    flowFields += $",all_given={allGiven}";
                                    AddExecSQL($"update display_agreement_detail set {flowFields} where company_id={company_id} and flow_id={flow.flow_id};");
                                }
                            }
                        }
                        else if (money_inout_flag == -1)
                        {

                            foreach (dynamic flow in lstFlow)
                            {
                                decimal stillLeftAmt = 0m;
                                string flowFields = "";
                                JObject jFlow = JsonConvert.DeserializeObject<JObject>(JsonConvert.SerializeObject(flow));
                                for (int m = 12; m >= 1; m--)
                                {
                                    string sQty = jFlow[$"month{m}_qty"].ToString();
                                    decimal qty = CPubVars.ToDecimal(sQty);
                                    string sGiven = jFlow[$"month{m}_given"].ToString();
                                    decimal given = CPubVars.ToDecimal(sGiven);
                                    decimal monthLeft = qty - given;
                                    var subID = flow.fee_sub_id;

                                    var DispflowID = flow.flow_id;
                                    if (payway_id == subID && given > 0)
                                    {
                                        decimal useAmt = 0;
                                        if (leftAmt1 >= given) useAmt = given; else useAmt = leftAmt1;

                                        //useAmt *= money_inout_flag;
                                        leftAmt1 -= useAmt;
                                        string fldName = $"month{m}_given";
                                        if (leftAmt1 >= 0 && useAmt != 0)
                                        {
                                            if (flowFields != "") flowFields += ",";
                                            flowFields += $"{fldName} =coalesce({fldName},0) - ({useAmt})";
                                        }
                                        stillLeftAmt += qty - given - (useAmt * (-1));
                                    }
                                }
                                if (flowFields != "")
                                {
                                    string allGiven = "false";
                                    if (stillLeftAmt < 0.01m) allGiven = "true";
                                    flowFields += $",all_given={allGiven}";
                                    AddExecSQL($"update display_agreement_detail set {flowFields} where company_id={company_id} and flow_id={flow.flow_id};");
                                }
                            }
                        }
                    }

                }
            }
            #endregion
            #region 更新现金银行余额
            cmd.CommandText = GetExecSQL();
            if (cmd.CommandText != "")
            {
                await cmd.ExecuteNonQueryAsync();
            }
            string sql_cb = "";
            if (info.BizStartPeriod != "" && info.PaywaysInfo != null && !IsImported)
            {
                Dictionary<string, decimal> pws = new Dictionary<string, decimal>();
                Subject pw1 = info.PaywaysInfo.Find(p => p.sub_id == payway1_id && p.sub_type == "QT");
                if (pw1 != null && payway1_amount != 0)
                {
                    if (!pws.ContainsKey(payway1_id)) pws.Add(payway1_id, payway1_amount);
                    else pws[payway1_id] += payway1_amount;
                }
                Subject pw2 = info.PaywaysInfo.Find(p => p.sub_id == payway2_id && p.sub_type == "QT");
                if (pw2 != null && payway2_amount != 0)
                {
                    if (!pws.ContainsKey(payway2_id)) pws.Add(payway2_id, payway2_amount);
                    else pws[payway2_id] += payway2_amount;
                }
                Subject pw3 = info.PaywaysInfo.Find(p => p.sub_id == payway3_id && p.sub_type == "QT");
                if (pw3 != null && payway3_amount != 0)
                {
                    if (!pws.ContainsKey(payway3_id)) pws.Add(payway3_id, payway3_amount);
                    else pws[payway3_id] += payway3_amount;
                }
                if (pws.Count() > 0)
                {
                    sql_cb = base.UpdateCashBankBalance(pws);
                }
            }
            #endregion


            cmd.CommandText = sql + redSql + sql_cb;
            await cmd.ExecuteNonQueryAsync();
            if (!this.IsImported && this.SheetType == "SK")
            {
                if (info.WeChatInfo != null && info.WeChatInfo.Count > 0)
                {
                    await SendSheetSimple(cmd,_httpClientFactory,
                    info.WeChatInfo,
                    this.SheetType,
                    this.now_disc_amount.ToString(),
                    this.left_amount.ToString(),
                    this.payway1_name,
                    this.payway1_amount.ToString(),
                    this.payway2_name,
                    this.payway2_amount.ToString(),
                    this.payway3_name,
                    this.payway3_amount.ToString(),
                    this.supcust_id);
                }
            }
            if (this.SheetType == "SK")
            {

               dynamic rec =await CDbDealer.Get1RecordFromSQLAsync(@$"
select sheet_id,happen_time,sheet_type,sheet_no from client_account_history ch
where company_id={company_id} and supcust_id={this.supcust_id} and red_flag is null and sheet_type in ('X','DH','YS') and change_amount>0 AND settle_time is null and sub_type='QK' order by happen_time limit 1;", cmd);

                string first_arrears_time = "null", first_arrears_sheet_id = "null", first_arrears_sheet_no = "null", first_arrears_sheet_type = "null";

				if (rec != null)
                {
                    first_arrears_time = $"'{rec.happen_time}'";
					first_arrears_sheet_id = $"'{rec.sheet_id}'";
					first_arrears_sheet_no = $"'{rec.sheet_no}'";
					first_arrears_sheet_type = $"'{rec.sheet_type}'";
				}
				cmd.CommandText = $@"
update arrears_balance set first_arrears_time={first_arrears_time},first_arrears_sheet_id={first_arrears_sheet_id},first_arrears_sheet_no={first_arrears_sheet_no},first_arrears_sheet_type={first_arrears_sheet_type} where
company_id={company_id} and supcust_id={this.supcust_id}";
				await cmd.ExecuteNonQueryAsync();


			}
            

        }
        public override string GetWeChatMsgHead()
        {
            string sheetTypeName = "";
            string first = "";
            switch (this.SheetType) {
                case "SK" :  sheetTypeName = "收款单"; break;
            }
            switch (this.red_flag)
            {
                case "": first = $"您有新的【{sheetTypeName}】,来自【{this.company_name}】,请注意查收"; break;
                case "2": first = $"您的【{sheetTypeName}】【被红冲】,来自【{this.company_name}】,请注意查收"; break;
            }
            return first;
        }
        public override string GetWeChatMsgTail()
        {
            string remark = "";
            if (this.sheet_no != "") remark += "单据编号：" + this.sheet_no + "\n";
            return remark;
        }
        public override async Task LoadInfoForPrint(CMySbCommand cmd, bool smallUnitBarcode, bool bLoadCompanySetting = true, dynamic printTemplate = null)
        {
            await base.LoadInfoForPrint(cmd, smallUnitBarcode, bLoadCompanySetting);

        }

        protected override string GetSQLForTemplates(string companyID, string mainTable, string sheetIDs)
        {
            string sql =
            @$"select m.sheet_id,st.setting->>'companyName' as company_name,st.setting->>'contactTel' as company_tel,st.setting->>'companyAddress' as company_address, t.template_id,t.template_name, c.client_group_id,c.client_id from {mainTable} m
left join info_supcust ic on m.supcust_id=ic.supcust_id and ic.company_id={companyID}
left join print_template t on m.sheet_type=t.sheet_type and t.company_id={companyID}
left join print_template_choose c on c.company_id={companyID} and t.template_id=c.template_id and (m.supcust_id=c.client_id or ic.sup_group = c.client_group_id or (c.client_id=0 and c.client_group_id=0))
left join company_setting st on m.company_id=st.company_id
where m.company_id={companyID} and m.sheet_id in ({sheetIDs}) order by case when c.client_id is null then -1 else c.client_id end desc, case when c.client_group_id is null then -1 else c.client_group_id end desc, c.template_id, order_index;";

            return sql;
        }



        /*public override async Task<JsonResult> ToVoucherRows(CMySbCommand cmd, string sheetID, SheetCwVoucher sheetCwVoucher, Dictionary<string, decimal> payways)
        {
            string subsID = "";
            string condi = $@"( select sub_id,sub_name,sub_code,direction,status from cw_subject where company_id = {company_id} and substr(sub_code::text,1,4)='1122' and level=(select Max(level) from cw_subject where company_id={company_id} and substr(sub_code::text,1,4)='1122')  order by sub_code limit 1 )";//应收账款
            if (SheetType == "FK") condi = $@"( select sub_id,sub_name,sub_code,direction,status from cw_subject where company_id = {company_id} and substr(sub_code::text,1,4)='2202' and level=(select Max(level) from cw_subject where company_id={company_id} and substr(sub_code::text,1,4)='2202')  order by sub_code limit 1 )";//应付账款
            int subLen = 1;
            if (payways.Count == 0)
            {
                if (payway1_id != "" && payway1_amount != 0) payways.Add(payway1_id, payway1_amount * money_inout_flag);
                if (payway2_id != "" && payway2_amount != 0) payways.Add(payway2_id, payway2_amount * money_inout_flag);
                if (payway3_id != "" && payway3_amount != 0) payways.Add(payway3_id, payway3_amount * money_inout_flag);
                if (now_disc_amount != 0) payways.Add("disc", now_disc_amount * money_inout_flag);
                if ((now_pay_amount + now_disc_amount) != 0) payways.Add("total", (now_pay_amount + now_disc_amount) * money_inout_flag);
            }
            if (payways == null || payways.Count == 0)
            {
                return new JsonResult(new { result = "OK", msg = "", sheetCwVoucher });
            }
            foreach (var payway in payways)
            {
                if (payway.Key == "total") continue;
                else if (payway.Key == "left") continue;
                else if (payway.Key == "disc") condi += $@" union all ( select sub_id,sub_name,sub_code,direction,status from cw_subject where company_id = {company_id} and sub_code=560304 )";
                else
                {
                    if (subsID != "") subsID = subsID + ",";
                    subsID += payway.Key;
                }
                subLen++;
            }
            string payCondi = "";
            if (subsID != "") payCondi += $"union all ( select sub_id,sub_name,sub_code,direction,status from cw_subject where company_id = {company_id} and sub_id in ({subsID}) )";
            
            string sql = $"{condi} {payCondi}";
            dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            if (records == null || records.Count < subLen) return new JsonResult(new { result = "Error", msg = "缺少生成凭证的相关科目，请添加" });
            if (records.Count > subLen) return new JsonResult(new { result = "Error", msg = "生成凭证的相关科目有重复科目代码，请修改" });

            foreach (var rec in records)
            {
                if (rec.status == null || rec.status == "") rec.status = 1;
                if (Convert.ToInt16(rec.status) == 0) return new JsonResult(new { result = "Error", msg = "相关科目已停用，请检查" });
                CwRowVoucher cwRow = new CwRowVoucher();
                cwRow.business_sheet_type = SheetType;
                cwRow.business_sheet_id = sheetID;
                cwRow.sub_id = rec.sub_id;
                cwRow.remark = SheetType == "SK" ? "收应收款" : "付应付款";
                if (red_flag == "2") cwRow.remark = SheetType == "SK" ? "红冲收应收款" : "红冲付应付款";
                decimal changeAmt = 0;
                foreach (var payway in payways)
                {
                    changeAmt = payway.Value;

                    if (payway.Key == rec.sub_id || (payway.Key == "disc" && rec.sub_code == "560304"))
                    {
                        if (changeAmt >= 0) cwRow.debit_amount = changeAmt.ToString();// SK正常开单: changeAmt>0  借payway，贷应收； FK开单: changeAmt<0  借应付账款，贷payway
                        else cwRow.credit_amount = Math.Abs(changeAmt).ToString();
                        cwRow.change_amount = changeAmt.ToString();
                        break;
                    }
                    else if ((rec.sub_code.ToString().Substring(0, 4) == "1122" || rec.sub_code.ToString().Substring(0, 4) == "2202") && payway.Key == "total") // 应收账款
                    {
                        if (changeAmt >= 0) cwRow.credit_amount = changeAmt.ToString();
                        else cwRow.debit_amount = Math.Abs(changeAmt).ToString();
                        cwRow.change_amount = (-1 * changeAmt).ToString();
                        break;
                    }
                }
                
                sheetCwVoucher.SheetRows.Add(cwRow);
            }

            return new JsonResult(new { result = "OK", msg = "", sheetCwVoucher });
        }*/


    }
}
