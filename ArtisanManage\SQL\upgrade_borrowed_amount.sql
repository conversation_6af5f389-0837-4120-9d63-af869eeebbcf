-- 升级脚本：为借货功能添加按金额借还货支持（按借货单维度管理）
-- 执行日期：2024-12-19
-- 说明：创建基于借货单的余额管理系统，支持借A单还B单的场景
-- 注意：独立借货单没有明细表，只有sheet_sale_main记录

-- 1. 创建借货单余额表（按借货单维度管理）
CREATE TABLE IF NOT EXISTS borrow_sheet_balance (
    company_id integer NOT NULL,
    borrow_sheet_id integer NOT NULL,        -- 借货单ID
    supcust_id integer NOT NULL,             -- 客户ID
    borrow_mode varchar(10) NOT NULL,        -- 借货模式：QTY=按数量，AMT=按金额
    total_amount numeric(15,2) DEFAULT 0,    -- 借货总金额
    returned_amount numeric(15,2) DEFAULT 0, -- 已还金额
    balance_amount numeric(15,2) DEFAULT 0,  -- 剩余金额
    borrow_time timestamp NOT NULL,         -- 借货时间
    last_return_time timestamp,             -- 最后还货时间
    status varchar(20) DEFAULT 'ACTIVE',    -- 状态：ACTIVE=有效，CLOSED=已结清，CANCELLED=已取消
    CONSTRAINT pk_borrow_sheet_balance PRIMARY KEY (company_id, borrow_sheet_id)
);

-- 2. 创建还货记录表（记录每次还货对应的借货单）
CREATE TABLE IF NOT EXISTS return_borrow_mapping (
    company_id integer NOT NULL,
    return_sheet_id integer NOT NULL,       -- 还货单ID
    return_detail_id integer NOT NULL,      -- 还货明细ID
    borrow_sheet_id integer NOT NULL,       -- 对应的借货单ID
    return_amount numeric(15,2) DEFAULT 0,  -- 本次还货金额
    return_time timestamp DEFAULT now(),
    CONSTRAINT pk_return_borrow_mapping PRIMARY KEY (company_id, return_sheet_id, return_detail_id, borrow_sheet_id)
);

-- 3. 添加表注释
COMMENT ON TABLE borrow_sheet_balance IS '借货单余额表（按借货单维度管理）';
COMMENT ON COLUMN borrow_sheet_balance.borrow_sheet_id IS '借货单ID';
COMMENT ON COLUMN borrow_sheet_balance.borrow_mode IS '借货模式：QTY=按数量，AMT=按金额';
COMMENT ON COLUMN borrow_sheet_balance.balance_amount IS '剩余可还金额';

COMMENT ON TABLE return_borrow_mapping IS '还货与借货单对应关系表';
COMMENT ON COLUMN return_borrow_mapping.return_sheet_id IS '还货单ID';
COMMENT ON COLUMN return_borrow_mapping.borrow_sheet_id IS '对应的借货单ID';

-- 4. 创建索引
CREATE INDEX IF NOT EXISTS idx_borrow_sheet_balance_supcust
ON borrow_sheet_balance(company_id, supcust_id, status);

CREATE INDEX IF NOT EXISTS idx_borrow_sheet_balance_time
ON borrow_sheet_balance(company_id, borrow_time);

CREATE INDEX IF NOT EXISTS idx_return_borrow_mapping_return
ON return_borrow_mapping(company_id, return_sheet_id);

CREATE INDEX IF NOT EXISTS idx_return_borrow_mapping_borrow
ON return_borrow_mapping(company_id, borrow_sheet_id);

-- 5. 为借货单主表添加借货模式字段
ALTER TABLE sheet_sale_main
ADD COLUMN IF NOT EXISTS borrow_mode varchar(10) DEFAULT NULL;

COMMENT ON COLUMN sheet_sale_main.borrow_mode IS '借货模式：QTY=按数量，AMT=按金额，NULL=非借货单';

-- 6. 为借货单明细表添加借货模式字段
ALTER TABLE sheet_sale_detail
ADD COLUMN IF NOT EXISTS borrow_mode varchar(10) DEFAULT NULL;

COMMENT ON COLUMN sheet_sale_detail.borrow_mode IS '借货模式：QTY=按数量，AMT=按金额，NULL=非借货';

-- 7. 数据迁移策略说明
-- 由于现有borrowed_cust_items表只记录数量，没有价格信息，无法准确计算借货金额
-- 提供以下几种迁移方案：

-- 方案1：使用当前商品价格估算（可能不准确，仅供参考）
-- 注意：这种方式计算的金额可能与实际借货时的价格不符
/*
INSERT INTO borrow_balance (company_id, supcust_id, balance, init_balance, init_time)
SELECT
    company_id,
    cust_id as supcust_id,
    SUM(borrowed_qty *
        COALESCE((SELECT wholesale_price::numeric FROM info_item_multi_unit
                  WHERE company_id = bci.company_id AND item_id = bci.item_id
                  AND unit_type = 's' LIMIT 1), 0)
    ) as balance,
    0 as init_balance,
    now() as init_time
FROM borrowed_cust_items bci
WHERE borrowed_qty > 0
GROUP BY company_id, cust_id
ON CONFLICT (company_id, supcust_id) DO UPDATE SET
    balance = EXCLUDED.balance,
    last_update_time = now();
*/

-- 方案2：从历史借货单据中重新计算（推荐）
-- 这种方式可以获得相对准确的借货金额
/*
INSERT INTO borrow_balance (company_id, supcust_id, balance, init_balance, init_time)
SELECT
    sm.company_id,
    sm.supcust_id,
    SUM(CASE
        WHEN sd.trade_type = 'J' THEN sd.sub_amount
        WHEN sd.trade_type = 'H' THEN -sd.sub_amount
        ELSE 0
    END) as balance,
    0 as init_balance,
    now() as init_time
FROM sheet_sale_main sm
JOIN sheet_sale_detail sd ON sm.company_id = sd.company_id AND sm.sheet_id = sd.sheet_id
WHERE sm.approve_time IS NOT NULL
  AND sm.red_flag IS NULL
  AND sd.trade_type IN ('J', 'H')
  AND sm.happen_time >= '2024-01-01'  -- 根据需要调整起始时间
GROUP BY sm.company_id, sm.supcust_id
HAVING SUM(CASE
    WHEN sd.trade_type = 'J' THEN sd.sub_amount
    WHEN sd.trade_type = 'H' THEN -sd.sub_amount
    ELSE 0
END) > 0
ON CONFLICT (company_id, supcust_id) DO UPDATE SET
    balance = EXCLUDED.balance,
    last_update_time = now();
*/

-- 方案3：手工设置期初余额（最安全）
-- 建议：先不自动迁移，让用户手工设置重要客户的借货余额
-- 可以通过以下SQL查看现有借货情况，然后手工处理：

-- 查看现有借货数量统计
SELECT
    bci.company_id,
    bci.cust_id,
    sc.sup_name,
    COUNT(*) as item_count,
    SUM(bci.borrowed_qty) as total_qty,
    STRING_AGG(ip.item_name || '(' || bci.borrowed_qty || ')', ', ') as items_detail
FROM borrowed_cust_items bci
LEFT JOIN info_supcust sc ON bci.company_id = sc.company_id AND bci.cust_id = sc.supcust_id
LEFT JOIN info_item_prop ip ON bci.company_id = ip.company_id AND bci.item_id = ip.item_id
WHERE bci.borrowed_qty > 0
GROUP BY bci.company_id, bci.cust_id, sc.sup_name
ORDER BY total_qty DESC;

-- 建议的迁移步骤：
-- 1. 先执行上面的查询，导出现有借货情况
-- 2. 与业务人员确认重要客户的实际借货金额
-- 3. 手工录入准确的借货余额
-- 4. 新系统上线后，所有新的借还货都会准确记录

-- 8. 验证升级结果
DO $$
BEGIN
    -- 检查表是否创建成功
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'borrow_balance') THEN
        RAISE EXCEPTION '借货余额表创建失败';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'borrow_history') THEN
        RAISE EXCEPTION '借货历史表创建失败';
    END IF;

    RAISE NOTICE '借货按金额功能数据库升级完成！支持借A还B场景。';
END $$;
