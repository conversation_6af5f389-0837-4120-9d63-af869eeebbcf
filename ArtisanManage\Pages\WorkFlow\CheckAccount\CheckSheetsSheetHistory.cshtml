﻿@page
@model ArtisanManage.Pages.WorkFlow.CheckSheetsSheetHistoryModel
@{
    Layout = null;

}
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>历史交账单</title>
    <script src="~/js/Vue.js"></script>
    <link href="~/lib/element-ui/theme/index.css" rel="stylesheet" />
    <script src="~/lib/element-ui/index.min.js"></script>
    <script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>
    <link rel="stylesheet" href="~/jqwidgets/jqwidgets/styles/jqx.base.css" type="text/css" />
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxcore.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdatetimeinput.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxcalendar.js"></script>
    <link rel="stylesheet" type="text/css" href="~/css/CheckSheetsSheet.css" />
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/globalization/globalize.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/globalization/globalize.culture.zh-CN.js"></script>
    <script src="~/MiniJsLib/MiniJsLibPC.js?v=@Html.Raw(Model.Version)"></script>
    <script src="~/js/jQuery.print.js"></script>
    <style>
        * {
            font-family: "微软雅黑"
        }

        [v-cloak] {
            display: none;
        }

        .el-tag {
            margin: 5px;
        }

        .el-input--medium .el-input__inner {
            height: 25px
        }

        .depart .el-select {
            margin-left: 5px;
        }

        .depart .el-input .el-input__inner {
            margin-top: -15px;
            border: none;
            box-shadow: none;
            height: 25px;
            border-bottom: 1px solid #e2e2e2;
            border-radius: 0;
            width: 160px;
        }

        .depart .el-icon-arrow-up{
            display:none;
        }

        .depart .el-select__caret.el-icon-circle-close {
            margin-top:5px;
            font-size:20px;
        }
        .el-input .el-input__clear {
            font-size: 20px;
            margin-top: -8px;
        }

        .el-select-dropdown__item::-webkit-scrollbar {
            display:none;
        }

        .el-tree-node__label {
            color: #606266;
            font-size: 14px;
            font-weight: normal;
        }
    </style>
</head>
<body>
    <div id="pages" class="" v-cloak>
        <div class="pages_title">
        </div>
        <div class="pages_query">
            <div class="query_item">
                <label class="item_name">
                    员工
                    <el-autocomplete size="medium" style="margin-left:5px;" class="inline-input" clearable v-model="FormData.sellerName" :fetch-suggestions="querySearch" placeholder="请输入员工"></el-autocomplete>
                </label>
            </div>
            <div class="query_item">
                <label class="item_name depart" style="display:flex;">
                    部门
                    <el-select v-model="FormData.departPath.label" placeholder="请输入部门" clearable>
                        <el-option style="height:150px;overflow:auto;padding:0;" :value="FormData.departPath.lanel">
                            <el-tree :default-expand-all="true" :data="departmentTreeData" :props="defaultProps" @@node-click="handleNodeClick"></el-tree>
                        </el-option>
                    </el-select>

                </label>
            </div>
            <div class="query_item">
                <label class="item_name">
                    收账人
                    <el-autocomplete size="medium" style="margin-left:5px" class="inline-input" clearable v-model="FormData.operName" :fetch-suggestions="querySearch" placeholder="请输入收账人"></el-autocomplete>
                </label>
            </div>
            <div class="query_item"><label class="item_name">单号<input class="item_input" v-model="FormData.sheetNo" placeholder="请输入单号" /></label></div>
            <div class="query_item"><label class="item_name">开始时间</label><div id="jqxdatetimeinputStart" style="width: 100px"></div></div>
            <div class="query_item"><label class="item_name">结束时间</label><div id="jqxdatetimeinputEnd" style="width: 100px"></div></div>
            <div style=" display: flex; margin-left: 20px;">
                <div class="dropdown">
                    <button class="dropbtn">{{dateType}}</button>
                    <div class="dropdown-content">
                        <div v-for="(item,index) in dateColumns" :key="index" @@click="selectDateType(item)">{{item.titles}}</div>
                    </div>
                </div>
            </div>



            <el-button type="danger" @@click="query" style="width:80px;margin: 5px 0 0 40px;font-size: 14px;display: flex; align-items: center;justify-content: center">查询</el-button>
            <input type="button" value="清空" @@click="cleanSearch" style="width:80px;margin: 5px 0 0 20px; cursor: pointer" />
            <input type="button" value="打印" @@click="printPage()" style="width:80px;margin: 5px 0 0 20px;cursor: pointer" />


        </div>
        <div class="pages_content">
            <table class="dataTable_noscroll" style="table-layout: fixed; word-break: break-all;">
                <thead>
                    <tr>
                        <th align="left" width=55>序号</th>
                        <th align="left" width=150>员工</th>
                        <th align="left" width=150>收账人</th>
                        <th align="center" width=150>金额</th>
                        <th align="center" width=460>交账汇总</th>
                        <th align="right" width=150>交账日期</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(sheet, index) in filteredHistoryCheckSheets" :key="index" class="tableTd_noscroll" @@click="showSheet(sheet) ">
                        <td style="width: 55px">{{index+1}}</td>
                        <td><span class="showSummaryLink">{{sheet.seller_name}}</span></td>
                        <td><span>{{sheet.oper_name}}</span></td>
                        <td>
                            <div>   
                                <div>{{fix(Number(sheet.sale_amount)+Number(sheet.get_arrears)+Number(sheet.get_prepay)+Number(sheet.get_preget)+Number(sheet.fee_out)+Number(sheet.income))}}</div>
                                <div v-for="(payway, i) in sheet.payway_info" style="font-size: small;">{{payway}}</div>
                            </div>
                        </td>
                        <td style="display:flex;justify-content:center">
                            <div class="summary" style="display:flex;flex-direction:column;justify-content:center;align-items:center;">
                                <div class="summary_top" style="display:flex;">
                                    <el-tag style="color:#409eff;background-color: #ecf5ff;border: 1px solid #d9ecff;min-width:460px"><div>销售实收{{fix(Number(sheet.sale_amount))}} = 销:{{fix(Number(sheet.sale_total_amount))}} - 退:{{fix(Number(sheet.return_amount))}} - 预:{{fix(Number(sheet.sale_prepay_amount))}} - 欠:{{fix(Number(sheet.sale_left_amount))}} - 惠:{{fix(Number(sheet.sale_disc_amount))}}</div></el-tag>

                                </div>
                                <div class="summary_top" style="display:flex;">
                                    <el-tag type="success" style="border: 1px solid #d9ecff;min-width:460px"><div>收预收款{{fix(Number(sheet.get_preget))}} = 预:{{fix(Number(sheet.preget_total_amount))}} - 欠:{{fix(Number(sheet.preget_left_amount))}} - 惠:{{fix(Number(sheet.preget_disc_amount))}}</div></el-tag>
                                </div>
                                <div class="summary_bottom" style="display:flex;">
                                    <el-tag style="min-width:225px"><div>付预付款{{fix(Number(sheet.get_prepay))}} = 预:{{fix(Number(sheet.prepay_total_amount))}} - 欠:{{fix(Number(sheet.prepay_left_amount))}} - 惠:{{fix(Number(sheet.prepay_disc_amount))}}</div></el-tag>

                                    <el-tag type="success" style="min-width:225px"><span>其他收入{{fix(Number(sheet.income))}} = 总:{{fix(Number(sheet.income_total_amount))}} - 惠:{{fix(Number(sheet.income_disc_amount))}} - 欠:{{fix(Number(sheet.income_left_amount))}}</span></el-tag>
                                </div>
                                <div class="summary_bottom" style="display:flex;">
                                    <el-tag type="warning" style="min-width:225px"> <span>收欠款{{fix(Number(sheet.get_arrears))}} 惠:{{fix(Number(sheet.arrears_disc_amount))}}</span></el-tag>
                                    <el-tag type="danger" style="min-width: 225px"><span>费用支出{{fix(Number(sheet.fee_out))}}= 总:{{fix(Number(sheet.fee_out_total_amount))}} - 惠:{{fix(Number(sheet.fee_out_disc_amount))}} - 欠:{{fix(Number(sheet.fee_out_left_amount))}}</span> </el-tag>

                                </div>
                                <div class="summary_bottom" style="display:flex;">
                                    <el-tag type="success" style="min-width:225px"> <span>销售净额:{{fix(Number(sheet.sale_total_amount))}}</span></el-tag>
                                    <el-tag type="warning" style="min-width: 225px"><span>退货总额:{{fix(Number(sheet.return_amount))}}</span> </el-tag>
                                </div>

                            </div>
                        </td>
                        <td>{{sheet.happen_time}}</td>

                    </tr>
                    <tr class="tableTd" style="font-weight: bold">
                        <td style="width: 55px"></td>
                        <td></td>
                        <td>合计</td>
                        <td>
                            <p>
                                <span> {{fix(Number(total))}} </span>
                            </p>
                        </td>
                        <td style="display:flex;justify-content:center">



                            <div class="summary" style="display:flex;flex-direction:column;justify-content:center;align-items:center;">
                                <div class="summary_top" style="display:flex;">
                                    <el-tag style="color:#409eff;background-color: #ecf5ff;border: 1px solid #d9ecff;min-width:460px"><div>销售实收{{fix(Number(totalObj.sale_amount))}} = 销:{{fix(Number(totalObj.sale_total_amount))}} - 退:{{fix(Number(totalObj.return_amount))}} - 预:{{fix(Number(totalObj.sale_prepay_amount))}} - 欠:{{fix(Number(totalObj.sale_left_amount))}} - 惠:{{fix(Number(totalObj.sale_disc_amount))}}</div></el-tag>

                                </div>
                                <div class="summary_top" style="display:flex;">
                                    <el-tag type="success" style="border: 1px solid #d9ecff;min-width:460px">
                                        <div>收预收款{{fix(Number(totalObj.get_preget))}} = 预:{{fix(Number(totalObj.preget_total_amount))}} - 欠:{{fix(Number(totalObj.preget_left_amount))}} - 惠:{{fix(Number(totalObj.preget_disc_amount))}}</div>
                                    </el-tag>

                                </div>
                                <div class="summary_bottom" style="display:flex;">
                                    <el-tag style="min-width:225px"><div>付预付款{{fix(Number(totalObj.get_prepay))}} = 预:{{fix(Number(totalObj.prepay_total_amount))}} - 欠:{{fix(Number(totalObj.prepay_left_amount))}} - 惠:{{fix(Number(totalObj.prepay_disc_amount))}}</div></el-tag>
                                    <el-tag type="success" style="min-width:225px"><span>其他收入{{fix(Number(totalObj.income))}}</span></el-tag>
                                </div>
                                <div class="summary_bottom" style="display:flex;">
                                    <el-tag type="warning" style="min-width:225px"> <span>收欠款{{fix(Number(totalObj.get_arrears))}} 惠:{{fix(Number(totalObj.arrears_disc_amount))}}</span></el-tag>
                                    <el-tag type="danger" style="min-width: 225px"><span>费用支出{{fix(Number(totalObj.fee_out))}}</span> </el-tag>

                                </div>
                                <div class="summary_bottom" style="display:flex;">
                                    <el-tag type="success" style="min-width:225px"> <span>销售净额 销:{{fix(Number(totalObj.sale_total_amount))}}</span></el-tag>
                                    <el-tag type="warning" style="min-width: 225px"><span>退货总额 退:{{fix(Number(totalObj.return_amount))}}</span> </el-tag>
                            </div>

                        </td>
                        <td></td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div style="height:50px"></div>


    </div>
    <script>
        var g_operKey = '@Model.OperKey';
    </script>
    <script type="module">
        var app = new Vue({
            el: "#pages",
            data() {
                return {
                    title: '历史交账单',
                    pageSize: 1000,
                    startRow: 0,
                    startDate: '',
                    endDate: '',
                    FormData: {
                        sellerName: '',
                        departPath: { value: '', label: '' },
                        operName: '',
                        sheetNo: ''
                    },
                    departmentTreeData: [],

                    defaultProps: {
                        children: 'children',
                        label: 'label'
                    },
                    historyCheckSheets: [],
                    restaurants: [],
                    total: 0,
                    queryData: {},
                    sheets: {},
                    dateType: '今天',
                    dateColumns: [
                        //{
                        //    titles: '全部',
                        //    ids: '0days'
                        //},
                        {
                            titles: '今天',
                            ids: '1days'
                        },
                        {
                            titles: '昨天',
                            ids: 'yesterday'
                        },
                        {
                            titles: '前天',
                            ids: 'beforeYesterday'
                        },
                        {
                            titles: '近2天',
                            ids: '2days'
                        },
                        {
                            titles: '近3天',
                            ids: '3days'
                        },
                        {
                            titles: '近7天',
                            ids: '7days'
                        }
                    ],
                }
            },
            computed: {
                totalObj() {
                    var totalAll = {
                        sale_amount: 0,
                        sale_total_amount: 0,
                        return_amount: 0,
                        sale_prepay_amount: 0,
                        sale_left_amount: 0,
                        sale_disc_amount: 0,
                        fee_out: 0,
                        get_prepay: 0,
                        prepay_total_amount: 0,
                        prepay_left_amount: 0,
                        prepay_disc_amount: 0,

                        get_preget: 0,
                        preget_total_amount: 0,
                        preget_left_amount: 0,
                        preget_disc_amount: 0,


                        get_arrears: 0,
                        arrears_disc_amount: 0,
                        income: 0,

                        fee_out_left_amount: 0,
                        fee_out_disc_amount: 0,
                        income_left_amount: 0,
                        income_disc_amount: 0,
                        income_disc_amount: 0

                    }
                    if (this.historyCheckSheets) {
                        this.historyCheckSheets.forEach(sheet => {
                            totalAll.sale_amount += Number(sheet.sale_amount);
                            totalAll.sale_total_amount += Number(sheet.sale_total_amount);
                            totalAll.return_amount += Number(sheet.return_amount);
                            totalAll.sale_prepay_amount += Number(sheet.sale_prepay_amount);
                            totalAll.sale_left_amount += Number(sheet.sale_left_amount);
                            totalAll.sale_disc_amount += Number(sheet.sale_disc_amount);
                            totalAll.fee_out += Number(sheet.fee_out);
                            totalAll.get_prepay += Number(sheet.get_prepay);
                            totalAll.prepay_total_amount += Number(sheet.prepay_total_amount);
                            totalAll.prepay_left_amount += Number(sheet.prepay_left_amount);
                            totalAll.prepay_disc_amount += Number(sheet.prepay_disc_amount);
                            totalAll.get_preget += Number(sheet.get_preget);
                            totalAll.preget_total_amount += Number(sheet.preget_total_amount);
                            totalAll.preget_left_amount += Number(sheet.preget_left_amount);
                            totalAll.preget_disc_amount += Number(sheet.preget_disc_amount);
                            // 只累计收款单
                            if(sheet.sheet_no && sheet.sheet_no.substr(0,2) === 'SK') {
                                totalAll.get_arrears += Number(sheet.get_arrears);
                                totalAll.arrears_disc_amount += Number(sheet.arrears_disc_amount);
                            }
                            // 付欠款总额单独保留
                            if(sheet.sheet_no && sheet.sheet_no.substr(0,2) === 'FK') {
                                totalAll.get_arrears_FK = (totalAll.get_arrears_FK || 0) + Number(sheet.get_arrears);
                            }
                            totalAll.income += Number(sheet.income);
                            totalAll.fee_out_left_amount += Number(sheet.fee_out_left_amount);
                            totalAll.fee_out_disc_amount += Number(sheet.fee_out_disc_amount);
                            totalAll.income_left_amount += Number(sheet.income_left_amount);
                            totalAll.income_disc_amount += Number(sheet.income_disc_amount);
                            totalAll.income_total_amount += Number(sheet.income_total_amount);
                        })
                    }
                    return totalAll;

                },
                filteredHistoryCheckSheets() {
                    return this.historyCheckSheets.filter(sheet => !sheet.sheet_no || sheet.sheet_no.substr(0,2) !== 'FK');
                }
            },

            // watch: {
            //     deep: true,//深度监听
            //     'historyCheckSheets': {
            //         handler: function (newVal, oldVal) {
            //             var temp = 0
            //             for (let i = 0, len = newVal.length; i < len; i++) {
            //                 temp = Number(newVal[i].sale_amount) + Number(newVal[i].get_arrears) + Number(newVal[i].get_prepay) + Number(newVal[i].get_preget) + Number(newVal[i].fee_out) + Number(newVal[i].income)
            //                 this.total += temp
            //             }
            //         }
            //     }
            // },
            mounted() {
                var sheetNo = this.getQueryVariable("sheetNo")
                if (sheetNo != "") this.FormData.sheetNo = sheetNo

                this.onloadData()
                this.onloadSeller({ operKey: g_operKey })
            },
            methods: {
                handleNodeClick(data) {
                    this.FormData.departPath.label = data.label
                    this.FormData.departPath.value = data.value
                },
                getQueryVariable(variable) {
                    var query = window.location.search.substring(1);
                    console.log(query)
                    var vars = query.split("&");
                    console.log(vars)
                    for (var i = 0; i < vars.length; i++) {
                        var pair = vars[i].split("=");
                        if (pair[0] == variable) { return pair[1]; }
                    }
                    return (false);
                },
                formatDate(date) {
                    return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
                },
                computeDate(date, days) {
                    var d = new Date(date);
                    d.setDate(d.getDate() + days);    //如果加月就是d.getMonth(); 以此类推
                    var m = d.getMonth() + 1;
                    return d.getFullYear() + '-' + m + '-' + d.getDate();// + " " + d.getHours() + ":" + d.getMinutes() + ":" + d.getSeconds();  //返回格式 2016-4-27 13:18:00 （可根据自己需要自行设定返回内容）
                },
                querySearch(queryString, cb) {
                    var restaurants = this.restaurants;
                    var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
                    // 调用 callback 返回建议列表的数据
                    cb(results);
                },
                createFilter(queryString) {
                    return (restaurant) => {
                        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
                    };
                },
                //初始化页面
                onloadData() {
                    var startTime = this.getQueryVariable("startDay")
                    if (startTime == "") {
                        startTime = this.computeDate(new Date(), 0)
                    } else {
                        this.startDate = startTime
                    }
                    var endTime = this.computeDate(new Date(), 0) + ' 23:59:59'
                    let params = {
                        strSearch: this.FormData.name,
                        sellerName: this.FormData.sellerName,
                        departPath: this.FormData.departPath.value,
                        operName: this.FormData.operName,
                        sheetNo: this.FormData.sheetNo,
                        operID: '',
                        startRow: this.startRow,
                        pageSize: this.pageSize,
                        startTime: startTime,
                        endTime: endTime,
                        operKey: g_operKey

                    }
                    $("#jqxdatetimeinputStart").jqxDateTimeInput('setDate', startTime);
                    this.loadAccountHistory(params)
                },
                // 首页查询
                query() {
                    let params = {
                        strSearch: this.FormData.sellerName,
                        sellerName: this.FormData.sellerName,
                        departPath: this.FormData.departPath.value,
                        operName: this.FormData.operName,
                        sheetNo: this.FormData.sheetNo,
                        operID: '',
                        startRow: this.startRow,
                        pageSize: this.pageSize,
                        startTime: this.startDate,
                        endTime: this.endDate,
                        operKey: g_operKey
                    }
                    console.log("loadAccountHistory", params)
                    this.loadAccountHistory(params)
                },
                insertTreeData(childNodes, parentNodes, currentNodes) {//初始currentNodes为空，parentNodes为顶层，childNodes为除去顶层节点后的所有子节点
                    if (!childNodes.length) return parentNodes
                    if (!currentNodes.length) {
                        parentNodes.forEach(p => {
                            childNodes = childNodes.filter(c => {
                                if (c.mid === p.id) {
                                    let node = { value: p.value + c.id + "/", label: c.value, id: c.id, children: [] }
                                    p.children.push(node)//树形数据
                                    currentNodes.push(node)
                                }
                                return c.mid !== p.id
                            })
                        })
                    } else {
                        let cNodes = []
                        currentNodes.forEach(p => {
                            childNodes = childNodes.filter(c => {
                                if (c.mid === p.id) {
                                    let node = { value: p.value + c.id + "/", label: c.value, id: c.id, children: [] }
                                    p.children.push(node)//树形数据
                                    cNodes.push(node)
                                }
                                return c.mid !== p.id
                            })
                        })
                        currentNodes = cNodes
                    }

                    return this.insertTreeData(childNodes, parentNodes, currentNodes)
                },
                formatTreeData(arr) {

                    let rootNode = arr.find((e) => { return e.mid === "0" })
                    let parentNodes = []
                    parentNodes.push({ value: "/" + rootNode.id + "/", label: rootNode.value, id: rootNode.id, children: [] })
                    let childNodes = arr.filter((e) => e.mid !== "0")
                    return this.insertTreeData(childNodes, parentNodes, [])
                },
                onloadSeller(obj) {
                    $.ajax({
                        url: '/AppApi/SheetCheckAcount/GetSaleOrOrderSaleHistory',
                        type: 'get',
                        data: obj,
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json'
                    }).then(function (res) {
                        if (res.result === 'OK') {
                            console.log(res)
                            app.restaurants = res.opers
                            app.departmentTreeData = app.formatTreeData(res.departs)
                        }
                    });
                },
                // 清空条件
                cleanSearch() {
                    this.FormData = {
                        sellerName: '',
                        departPath: {value:'',label:''},
                        operName: '',
                        sheetNo: ''
                    }
                    this.pageSize = 1000
                    this.startRow = 0
                    $("#jqxdatetimeinputStart").jqxDateTimeInput('setDate', null);
                    $("#jqxdatetimeinputEnd").jqxDateTimeInput('setDate', null);
                    this.startDate = ''
                    this.endDate = ''
                    this.dateType = "全部"

                },
                myPadLeft(temp, len, charStr) {
                    var s = temp + '';
                    return new Array(len - s.length + 1).join(charStr, '') + s;
                },
                fix(amount) {
                    return toMoney(amount)
                    /* if (amount) amount = parseFloat(amount)
                     if (typeof (amount) == 'undefined') {
                         return amount
                     } else {
                         if (amount.toFixed)
                             amount = amount.toFixed(4)
                     }
                     amount = amount.toString()
                     if (amount.indexOf('.0000') > 0) {
                         amount = amount.replace('.0000', '')
                     }
                     var d = amount.indexOf('.')
                     if (d > 0) {
                         for (var i = 4; i >= 1; i--) {
                             var n = amount.lastIndexOf(this.myPadLeft('0',i, '0'))
                             if (n > d && n == amount.length - i) {
                                 amount = amount.substr(0, amount.length - i)
                             }
                         }
                         d = amount.indexOf('.')
                         if (d == amount.length - 1) amount = amount.substr(0, amount.length - 1)
                     }
                     return amount*/
                },
                // 获取历史交账单
                loadAccountHistory(obj) {

                    $.ajax({
                        url: '/AppApi/SheetCheckAcount/LoadAccountHistory',
                        type: 'get',
                        data: obj,
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json'
                    }).then(function (res) {
                        if (res.result == 'OK') {
                            console.log("返回", res.data);
                            app.total = 0
                            let tmpTotal = 0
                            app.historyCheckSheets = res.data
                            for (let i = 0, len = app.historyCheckSheets.length; i < len; i++) {
                                let sht = app.historyCheckSheets[i]
                                tmpTotal += (Number(sht.sale_amount) + Number(sht.get_arrears) + Number(sht.get_prepay) + Number(sht.get_preget) + Number(sht.fee_out) + Number(sht.income))
                            }
                            app.total = tmpTotal
                 
                            var hcs_length = app.historyCheckSheets.length;
                            for (var i = 0; i < hcs_length; i++) {
                                var info = '';
                                var sh = app.historyCheckSheets[i];
                                if (sh.payway) {
                                    try {
                                        let pw = JSON.parse(sh.payway)
                                        sh.payway_info = [];
                                        for (const p in pw) {
                                            const v = pw[p];
                                            var amt = toMoney(v.amt)
                                            sh.payway_info.push(`${v.name}:${amt}`)
                                        }
                                    }
                                    catch (err) {
                                        console.error(err);
                                    }
                                }
                            }

                            if (app.startRow == 0) {
                                app.sheetsNum = Number(res.itemCount)
                            }
                            // app.startRow = app.historyCheckSheets.length
                            if (app.sheetsNum <= app.historyCheckSheets.length)
                                app.finished = true
                            // console.log('load finished', this.sheetsNum)

                        }
                    });
                },
                // 选择特定时间快捷
                selectDateType(item) {
                    window.dateTypeIds = item.ids
                    if (item.ids == '0days') {
                        $("#jqxdatetimeinputStart").jqxDateTimeInput('setDate', null)
                        $("#jqxdatetimeinputEnd").jqxDateTimeInput('setDate', null)
                        this.startDate = ""
                        this.endDate = ""
                    }
                    else {
                        this.endDate = this.computeDate(new Date(), 0) + ' 23:59:59'
                        if (item.ids == '1days') {
                            this.startDate = this.computeDate(new Date(), 0)
                        }
                        else if (item.ids == 'yesterday') {
                            this.startDate = this.computeDate(new Date(), -1)
                            this.endDate = this.computeDate(new Date(), -1)
                        }
                        else if (item.ids == 'beforeYesterday') {
                            this.startDate = this.computeDate(new Date(), -2)
                            this.endDate = this.computeDate(new Date(), -2)
                        }
                        else if (item.ids == '2days') {
                            this.startDate = this.computeDate(new Date(), -1)
                        }
                        else if (item.ids == '3days') {
                            this.startDate = this.computeDate(new Date(), -2)
                        }
                        else if (item.ids == '7days') {
                            this.startDate = this.computeDate(new Date(), -6)
                        }
                        var d1 = new Date(this.startDate.replace(/\-/g, "\/"));
                        var d2 = new Date(this.endDate.replace(/\-/g, "\/"));
                        $("#jqxdatetimeinputStart").jqxDateTimeInput('setDate', new Date(d1.getFullYear(), d1.getMonth(), d1.getDate()))
                        $("#jqxdatetimeinputEnd").jqxDateTimeInput('setDate', new Date(d2.getFullYear(), d2.getMonth(), d2.getDate()))
                    }
                    this.dateType = item.titles
                },
                showSheet(sheet) {
                    console.log("41515", sheet)
                    let obj = {
                        sheetID: sheet.sheet_id,
                        operKey: g_operKey,
                        happenTime: sheet.happen_time,
                        getterID: sheet.seller_id,
                        dateTypeIds: window.dateTypeIds == 'alldays' ? '1days' : window.dateTypeIds
                    }
                    this.sheets.happen_time = sheet.happen_time
                    this.sheets.sheet_id = sheet.sheet_id
                    this.sheets.sellerName = sheet.seller_name

                    //  window.sellerID = item.oper_id
                    window.summCheckSheet = obj
                    window.g_showAccountTitle = sheet.seller_name
                    this.loadCheckSheet(obj)
                },
                loadCheckSheet(obj) {
                    $.ajax({
                        url: '/AppApi/SheetCheckAcount/LoadCheckSheet',
                        type: 'get',
                        data: obj,
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json'
                    }).then(function (res) {
                        if (res.result === 'OK') {
                            var count = 0;
                            var checked = true
                            updateSheetsCheck(res.saleSheets, checked)
                            updateSheetsCheck(res.borrowSheets, checked)
                            updateSheetsCheck(res.prepaySheets, checked)
                            updateSheetsCheck(res.pregetSheets, checked)
                            updateSheetsCheck(res.getArrearSheets, checked)
                            updateSheetsCheck(res.feeOutSheets, checked)
                            updateSheetsCheck(res.incomeSheets, checked)
                            app.sheets = res
                            app.sheets.sheet_id = app.sheets.sheet_id
                            app.sheets.happen_time = app.sheets.happen_time
                            app.sheets.isRed = false
                            app.sheets.startDate = app.startDate
                            app.sheets.endDate = app.endDate
                            // app.sheets = {
                            //     ...res,
                            //     sheet_id: app.sheets.sheet_id,
                            //     happen_time: app.sheets.happen_time,
                            //     isRed: false,
                            //     startDate: app.startDate,
                            //     endDate: app.endDate
                            // }
                            window.checkedSheetsCount = count;
                            window.g_sheet = app.sheets
                            function updateSheetsCheck(sheets, check) {
                                sheets.forEach(function (sheet) {
                                    sheet.isChecked = check
                                    if (sheet.isChecked)
                                        count++
                                })
                            }
                            console.log("history", window.g_sheet);
                            console.log("historys", window);
                            window.parent.newTabPage("历史交账单详情", "WorkFlow/CheckAccount/ShowAccount", window)
                        }
                    })
                }, printPage() {
                    $("#pages_title").print({
                        globalStyles: true,//是否包含父文档的样式，默认为true
                        mediaPrint: false,//是否包含media='print'的链接标签。会被globalStyles选项覆盖，默认为false
                        stylesheet: null,//外部样式表的URL地址，默认为null
                        noPrintSelector: ".no-print",//不想打印的元素的jQuery选择器，默认为".no-print"
                        iframe: true,//是否使用一个iframe来替代打印表单的弹出窗口，true为在本页面进行打印，false就是说新开一个页面打印，默认为true
                        append: null,//将内容添加到打印内容的后面
                        prepend: null,//将内容添加到打印内容的前面，可以用来作为要打印内容
                        deferred: $.Deferred()//回调函数
                    });
                }
            }
        })
        window.addEventListener("scroll", function (event) {
            var scrollTop = document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop;
            if (document.documentElement.scrollHeight == document.documentElement.clientHeight + scrollTop) {
                app.pageSize = app.pageSize + 1000;
                app.query();
            }

        });
        $(document).ready(function () {
            $("#jqxdatetimeinputStart").jqxDateTimeInput({ culture: 'zh-CN', width: '150px', height: '25px', formatString: "yyyy-MM-dd" });
            $("#jqxdatetimeinputEnd").jqxDateTimeInput({ culture: 'zh-CN', width: '150px', height: '25px', formatString: "yyyy-MM-dd" });

            if (app.startDate == "") {
                $("#jqxdatetimeinputStart").jqxDateTimeInput('setDate', new Date(app.computeDate(new Date(), 0)));
                app.startDate = app.computeDate(new Date(), 0)
                app.dateType = "今天"
            } else {
                $("#jqxdatetimeinputStart").jqxDateTimeInput('setDate', app.startDate);
                app.dateType = "指定日期"
            }


            $("#jqxdatetimeinputEnd").jqxDateTimeInput('setDate', new Date(app.computeDate(new Date(), 0)));
            app.endDate = app.computeDate(new Date(), 0) + ' 23:59:59'


        });
        $('#jqxdatetimeinputStart').on('change', function (event) {
            var beginDate = app.computeDate(event.args.date, 0);
            var endDate = app.endDate;
            var d1 = new Date(beginDate.replace(/\-/g, "\/"));
            var d2 = new Date(endDate.replace(/\-/g, "\/"));
            if (beginDate != "" && endDate != "" && d1 > d2) {
                bw.toast("开始时间不能大于结束时间！");
                $("#jqxdatetimeinputStart").jqxDateTimeInput('setDate', new Date(d2.getFullYear(), d2.getMonth(), d2.getDate()))
                app.startDate = app.computeDate(d2, 0);
            } else {
                app.startDate = app.computeDate(event.args.date, 0);
            }
            app.dateType = "指定日期"

        });
        $('#jqxdatetimeinputEnd').on('change', function (event) {
            var beginDate = app.startDate;
            var endDate = app.computeDate(event.args.date, 0);
            var d1 = new Date(beginDate.replace(/\-/g, "\/"));
            var d2 = new Date(endDate.replace(/\-/g, "\/"));
            if (beginDate != "" && endDate != "" && d1 > d2) {
                bw.toast("结束时间不能小于结束时间！");
                $("#jqxdatetimeinputEnd").jqxDateTimeInput('setDate', new Date(d1.getFullYear(), d1.getMonth(), d1.getDate()))
                app.endDate = beginDate + ' 23:59:59';
            } else {
                app.endDate = app.computeDate(event.args.date, 0) + ' 23:59:59';
            }
            app.dateType = "指定日期"

        });

    </script>


</body>
</html>