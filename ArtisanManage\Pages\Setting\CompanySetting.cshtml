@page
@model ArtisanManage.CompanySettingModel
@{
    Layout = null;    
    var FeeOutSubs = await Model.cmd.QueryWhereAsync<Subject_CW>($" where company_id={Model.Token.CompanyID} and sub_type = 'ZC' and sub_code>100");    
}

<!DOCTYPE html>

<html>

<head>
    <meta name="viewport" content="width=device-width" />
    <title>CompanySetting</title>
    <script src="~/js/jquery.min.js"></script>
    <link rel="stylesheet" href="~/css/DataForm.css?v=@Html.Raw(Model.Version)" type="text/css" />
    <link href="~/css/component.css" rel="stylesheet" />

  <link rel="stylesheet" href="~/ChosenSelect/chosen.css"/>

  <link rel="stylesheet" href="~/jqwidgets/jqwidgets/styles/jqx.base.css" type="text/css"/>

  <script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>
  <script src="~/js/commission.js"></script>
  <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxcore.js"></script>
  <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdatetimeinput.js"></script>
  <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxcalendar.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxwindow.js"></script>

  <script type="text/javascript" src="~/jqwidgets/jqwidgets/globalization/globalize.js"></script>
  <script type="text/javascript" src="~/jqwidgets/jqwidgets/globalization/globalize.culture.zh-CN.js"></script>
  <script src="~/ChosenSelect/chosen.jquery.js"></script>
  <script src="~/ChosenSelect/docsupport/init.js"></script>
  <script src="~/ChosenSelect/docsupport/prism.js"></script>
  <link rel="stylesheet" href="~/MiniJsLib/MiniJsLibPC.css?v=@Html.Raw(Model.Version)">
  <script src="~/MiniJsLib/MiniJsLibPC.js?v=@Html.Raw(Model.Version)"></script>
  <link href="~/NiceWidgets/NiceWidgets.css" rel="stylesheet" type="text/css"/>

  <script type="text/javascript">

        $(document).ready(function () {
            
            if ($("#openTicketAccessSys").prop("checked")) {
                $("#ticketAccessSysForm").css("display", "block") 
            } else {
                $("#ticketAccessSysForm").css("display", "none")
            }
            if ($("#limitVisitDistance").prop("checked")) {
                $("#visitDistances").css("display", "block")
            } else {
                $("#visitDistances").css("display", "none")
            }
            if ($("#autoVisitEnd").prop("checked")) {
                $("#autoVisitEndDistances").css("display", "block")
            } else {
                $("#autoVisitEndDistances").css("display", "none")
            }
            if ($("#forceDisplayPic").prop("checked")) {
                $("#displayPicCounts").css("display", "block")
            } else {
                $("#displayPicCounts").css("display", "none")
            }

            $('.my-tab>div:first-child>div').on("click", function (i) {
              
                var tt = $(this).attr('name');

                $(".my-tab>div:first-child>div").each(function (i) {
                    if ($(this).attr('name') == tt) {
                        $(this).css("background", "#fff");
                    } else {
                        $(this).css("background", "#e8e8e8");
                    }
                })
                $(".my-tab>div:last-child>div").each(function (i) {
                    console.log('aa');
                    if ($(this).attr('name') == tt) {
                        $(this).css("display", "block");
                    } else {
                        $(this).css("display", "none");
                    }
                })
            })
            
            $("#uploadLeft").on("click", function () {
                $("#upload-official-head").trigger("click");
            })
            $("#uploadRight").on("click", function () {
                $("#upload-official-seal").trigger("click");
            })
            $("#uploadRight2").on("click", function () {
                $("#upload-official-seal-2").trigger("click");
            })
        })
    </script>
  <style>
       
        #scrollbar {
            width: 20px;
            height: 300px;
            margin: 0 auto;
        }


        /*下拉进度条**/
        input[type=range] { /*滑动条背景*/
            transform: rotate( 90deg );
            -webkit-appearance: none;
            background: #ededed;
            height: 1.5rem;
            background-size: 0% 100%;
            outline: none;
            cursor: pointer;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
            border-radius: 10px;
        }

        input[type="range"]::-webkit-slider-thumb { /*滑动条操作按钮*/
            -webkit-appearance: none;
            border: solid 1px #e8e8e8;
            border-radius: 8px;
            height: 20px;
            width: 100px;
            border-radius: 10px;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
            background: #535353;
        }

        #grid {
            margin-top: 15px;
        }

            #grid thead tr {
                height: 45px;
            }

            #grid td {
                border-bottom: 1px solid #d0d0d0;
                width: 250px;
            }

        input {
            outline: none
        }

        #grid tbody {
            text-align: center;
            line-height: 2.1;
        }

            #grid tbody tr {
                height: 35px;
                cursor: pointer;
            }

        #grid tr:hover td {
            background: #ededed
        }

        #dia {
            overflow: hidden;
        }

        #dia .dialog_body {
                overflow: hidden;
        }

        #dia .dialog_foot {
                display: none;
         }

        .text {
            font-size: 15px;
            line-height: 1.6;
        }

        #unit_no {
            width: 40px;
            position: relative;
            right: 50px;
            text-align: center;
        }

        #dialog .dialog_body {
            padding: 2.5rem 4rem;
        }

        #dialog .dialog_foot {
            padding-top: 12px;
        }

        .popItem {
            width: 1000px;
            height: 750px;
            z-index: 10000;
        }

        .myClass {
            margin: 0 auto;
            width: 600px;
            height: 400px;
        }

        .row {
            height: 30px;
            padding: 10px;
            font-size: 18px;
        }

        .itemName {
            white-space: nowrap;
        }

        #item_name {
            padding-right: 1.5rem;
        }

        .title {
            width: 100px;
            text-align: left;
            line-height: 1.6;
        }

        body {
            display: flex;
            justify-content: left;
            background-color: #fff;
        }

        .chosen-select-no-single {
        }

        .company_setting {
            margin-top: 10px;
            margin-left: 30px;
            display: block;
            overflow-y: auto;
            
            width:100%;
        }

        .form-item {
            margin-top: 20px;
            display: flex;
            height: 38px;
        }

        .form-item-check {
            margin-top: 20px;
            display: flex;
            height: 38px;
        }

        .form-item > div:first-child {
            float: left;
           
            /*padding: 9px 15px;*/
            font-size: 16px;
            color:#000;
            font-weight: 400;
            line-height: 36px;
            text-align: right;
            width: 200px;
        }

        .form-item > div:nth-child(2) {
            margin-left: 20px;
            min-height: 36px;
            position: relative;
        }

            .form-item > div:nth-child(2) > label {
                display: block;
                margin-left: 20px;
                margin-top: 10px;
            }

        .company-setting .form-item::after,
        .company-setting .form-item::before {
            content: '\20';
            clear: both;
            display: block;
            height: 0;
            zoom: 1;
        }
         
       .btn-white {
            color: #000;
            background-color: #fff;
            border:1px solid #ddd;
            font-size: 13px;
            border-radius: 4px;
            letter-spacing: 1.2px;
        }

        .btn-white:hover {
            background-color: #eee; 
        }

        .btn-white:active {
            background-color: #ddd;
        }
        #left-container {
            width: 70px;
            background: #e8e8e8;
            margin-left:0px;
            height:calc(100% + 1px);
        }

        .tab-head {
            width: 70px;
            height: 50px;
            line-height: 50px;
            text-align: center;
            font-size: 16px;
            color:#000;
            cursor: pointer;
        }
        .chosen-single{
            text-align:center;
        }
        .my-tab {
            display: flex;
            height: calc(100vh - 80px);
            background-color: #fff;
            margin-top: 60px;
            margin-left: 10px;
            width: calc(100vw - 20px);
            border: 2px solid #e8e8e8;
            border-top-width:1.5px;
            border-bottom-width:1.5px;

            position: fixed;
            font-family: 微软雅黑；
        }

        .hidden {
            display: none;
        }

        .visible {
            display: flex; 
        }

    </style>
</head>

<body>
    <div id="popChooseClearType" style="display:none">
        <div id="clearTypeCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择清除类别</span></div>
        <div  style="overflow:hidden;">
            <div id="stepone" style="display:none;">
                <div>
                    客户<input type="checkbox" value="client" name="clearDataType" />
                    商品<input type="checkbox" value="item" name="clearDataType" />
                    单据<input type="checkbox" value="sheet" name="clearDataType" />
                </div>
                <button id="confirmClearNext" class="btnOk  main-button">确认</button>
            </div>
            <div id="steptwo" style="display:none;">
                <div id="bossVerifyInput" style="display:none">
                    <div>请输入老板验证码(已发送至手机号<span id="boss_mobile"></span>)</div>
                    <input type="text" id="bossVerifyCodeInput" />
                </div>
                <div>
                    <div>请输入服务人员验证码(请联系专属服务人员<span id="staff_name"></span><span id="staff_mobile"></span>获取)</div>
                    <input type="text" id="staffVerifyCodeInput" />
                </div>
                <div>声明信息：该操作会导致<span id="clear-data-type"></span>数据清空且无法恢复，确定继续该操作？</div>
                <div>请在下方填写：<span style="color:#c40000;">确认清空</span></div>
                <div style="display:flex;flex-direction:column;">
                    <input style="width:200px;" type="text" id="commitmentInput" />
                    <button id="confirmClearButton" class="btnOk  main-button">确认清空</button>
                </div>

            </div>
        </div>

</div>
<div style="margin-top:10px;margin-left:20px;height:40px;position:fixed;right:14px;">
  <button class="btnOk  main-button" name="save" save="">保存</button>

</div>
<div class="my-tab">
<div id="left-container">
  <div name="base" class="tab-head" style="background:#fff">基本</div>
  <div name="visit" class="tab-head" style="background:#e8e8e8">外勤</div>
  <div name="sale" class="tab-head" style="background:#e8e8e8">业务</div>
  <div name="print" class="tab-head" style="background:#e8e8e8">打印</div>
  <div name="necessary" class="tab-head" style="background:#e8e8e8">档案</div>
  <div name="mall" class="tab-head" style="background:#e8e8e8">商城</div>

</div>
<div class="company_setting">
<div name="base" style="display:block">
    <div class="form-item">
      <div >
        公司名称
      </div>
      <div>
        <input class="magic-input" id="companyName" style="width:300px" value="@(Model.companyName)"/>
      </div>
    </div>
    <div class="form-item">
      <div>
        公司地址
      </div>
      <div >
        <input class="magic-input" id="companyAddress"style="width:300px" value="@(Model.companyAddress)"/>
      </div>
    </div>
    <div class="form-item">
      <div>
        联系电话
      </div>
      <div >
        <input class="magic-input" id="contactTel"style="width:300px" value="@(Model.contactTel)"/>

      </div>
    </div>
    <div class="form-item">
         <div>图片清晰度</div>
         <div style="margin-left:40px">
                        <select id="imageClarityLevel" class="chosen-select-no-single" tabindex="1" style="width: 110px; ">
                            <option value="high" selected="@(Model.imageClarityLevel == "high")">高清</option>
                            <option value="middle" selected="@(Model.imageClarityLevel == "middle")">中等</option>
                            <option value="normal" selected="@(Model.imageClarityLevel == "normal")">普通</option>
                        </select>
         </div>
    </div>
    <div>
         <div id="clearDataButton" style="background:#c40000;color:#f0f0f0;width:160px;text-align:center;padding:10px;margin-left:120px;margin-top:20px;border-radius:20px;">
             清除数据
         </div>
    </div>
  </div>
    <div name="visit" style="display:none">
        <div class="form-item">
            <div> APP拜访后才能开单 </div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="forceVisit" @(Model.forceVisit == "true" ? "checked" : "") style="margin-left: 0px;"/>
                <label for="forceVisit" style="display: block;"></label>
            </div>

        </div>
        <div class="form-item">
            <div> 拜访距离限制 </div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="limitVisitDistance" @(Model.limitVisitDistance == "true" ? "checked" : "") style="margin-left: 0px;" /> 
                <label for="limitVisitDistance"></label>
            </div>
            <div id="visitDistances"><input class="magic-input" id="visitDistance" style="width:40px" value="@(Model.visitDistance)" /><span style="color:#bbb;">米</span> </div>
        </div>
        <div class="form-item">
            <div>开启自动签退</div><div><input class="magic-checkbox" type="checkbox" id="autoVisitEnd" @(Model.autoVisitEnd == "true" ? "checked" : "") style="margin-left: 0px;" /><label for="autoVisitEnd" ></label></div>
            <div id="autoVisitEndDistances"> <input class="magic-input" id="autoVisitEndDistance" style="width:40px" value="@(Model.autoVisitEndDistance)" /><span style="color:#bbb;">米自动签退</span></div>
        </div>
        <div class="form-item">
            <div>强制门头照</div><div><input class="magic-checkbox" type="checkbox" id="forceDoorPic" @(Model.forceDoorPic == "true" ? "checked" : "") style="margin-left: 0px;" /><label for="forceDoorPic"></label></div>
        </div>
        <div class="form-item">
            <div>强制陈列照</div><div><input class="magic-checkbox" type="checkbox" id="forceDisplayPic" @(Model.forceDisplayPic == "true" ? "checked" : "") style="margin-left: 0px;" /><label for="forceDisplayPic" ></label></div>
            <div id="displayPicCounts"><input class="magic-input" id="displayPicCount" style="width:40px" value="@(Model.displayPicCount)" /><span style="color:#bbb;">张</span></div>
        </div>
        <div class="form-item">
            <div>强制备注</div><div><input class="magic-checkbox" type="checkbox" id="forceRemark" @(Model.forceRemark == "true" ? "checked" : "") style="margin-left: 0px;" /><label for="forceRemark" ></label></div>
        </div>
        <div class="form-item">
            <div>
                外勤时间段
            </div>
            <div>
                <input class="magic-input" id="positionPeriodStart" style="width:60px" value="@(Model.positionPeriodStart)" />
                <span>-</span>
                <input class="magic-input" id="positionPeriodEnd" style="width:60px" value="@(Model.positionPeriodEnd)" />
            </div>
        </div>
        <div class="form-item">
                <div>
                        访店最短
                </div>
                <div>
                        <input class="magic-input" id="minVisitDuration" style="width:60px" value="@(Model.minVisitDuration)" />
                </div>
                <div>秒</div>
        </div>
    </div>
    <div name="sale" style="display:none">
        <div style="height:30px;"></div>
            <div>
                    <div style="  font-weight: 800; ">
                访单流程
                <div style="background-image: linear-gradient(to right, #aaa 0%, #aaa 10%, transparent 30%); width: 650px; height: 1px; margin-top: 7px; background-size: 7px 1px; background-repeat: repeat-x;margin-top:10px;"></div>
            </div>
            <div class="form-item">
                <div>启用装车流程</div>
                <div>
                    <input class="magic-checkbox" type="checkbox" id="flowExistMove" @(Model.flowExistMove == "true" ? "checked" : "") style="margin-left: 0px;"/>
                    <label for="flowExistMove" style="display: block;"></label>
                </div>
            </div>
            <div class="form-item">
                <div>订单复核后才能装车</div>
                <div>
                    <input class="magic-checkbox" type="checkbox" id="reviewOrderBeforeAssignVan" @(Model.reviewOrderBeforeAssignVan == "true" ? "checked" : "") style="margin-left: 0px;"/>
                    <label for="reviewOrderBeforeAssignVan" style="display: block;"></label>
                </div>
            </div>

            <div class="form-item">
                <div>订单支付成功后才能装车</div>
                <div>
                            <input class="magic-checkbox" type="checkbox" id="ignorePayFailedSheetOnAssignVan" @(Model.ignorePayFailedSheetOnAssignVan == "true" ? "checked" : "") style="margin-left: 0px;" />
                            <label for="ignorePayFailedSheetOnAssignVan" style="display: block;"></label>
                </div>
            </div>

            <div class="form-item">
                <div>订单支付成功后才能转单</div>
                <div>
                            <input class="magic-checkbox" type="checkbox" id="ignorePayFailedSheetOnOrderToSale" @(Model.ignorePayFailedSheetOnOrderToSale == "true" ? "checked" : "") style="margin-left: 0px;" />
                            <label for="ignorePayFailedSheetOnOrderToSale" style="display: block;"></label>
                </div>
            </div>

            <div class="form-item">
                <div>多仓库同时装车</div>
                <div>
                    <input class="magic-checkbox" type="checkbox" id="canMultiBranchAssign" @(Model.canMultiBranchAssign == "true" ? "checked" : "") style=" margin-left: 0px;"/>
                    <label for="canMultiBranchAssign" style="display: block;"></label>
                </div>
            </div>
            <div class="form-item">
                <div>启用回库流程</div>
                <div>
                    <input class="magic-checkbox" type="checkbox" id="flowExistMoveBack" @(Model.flowExistMoveBack == "true" ? "checked" : "") style="margin-left: 0px;"/>
                    <label for="flowExistMoveBack" style="display: block;"></label>
                </div>
            </div>
            <div class="form-item">
                <div>打印汇总单</div>
                <div>
                    <input class="magic-checkbox" type="checkbox" id="printCheckWhole" @(Model.printCheckWhole == "true" ? "checked" : "") style="margin-left: 0px;"/>
                    <label for="printCheckWhole" style="display: block;"></label>
                </div>
            </div>
            <div class="form-item">
                <div>打印拆零单</div>
                <div>
                    <input class="magic-checkbox" type="checkbox" id="printCheckOpenStock" @(Model.printCheckOpenStock == "true" ? "checked" : "") style="margin-left: 0px;"/>
                    <label for="printCheckOpenStock" style="display: block;"></label>
                </div>
            </div>

            <div class="form-item">
                        <div>冲改单子是否保留打印次数</div>
                        <div>
                            <input class="magic-checkbox" type="checkbox" id="reservePrintCountOnRedChange" @(Model.reservePrintCountOnRedChange == "true" ? "checked" : "") style="margin-left: 0px;" />
                            <label for="reservePrintCountOnRedChange" style="display: block;"></label>
                        </div>
                    </div>

            <div class="form-item">
                <div>允许多次回库</div>
                <div>
                    <input class="magic-checkbox" type="checkbox" id="allowRepeatedBackBranch" @(Model.allowRepeatedBackBranch == "true" ? "checked" : "") style="margin-left: 0px;" />
                    <label for="allowRepeatedBackBranch" style="display: block;"></label>
                </div>
            </div>
            <div class="form-item">
                    <div>
                       销售订单默认仓库
                    </div>
                    <div style="margin-left:40px">
                    <select id="orderBranch" data-placeholder="请选择..."
                            class="form-control chosen-select-no-single" tabindex="3" style="width: 110px;">
                        <option value=""></option>
                        @foreach (dynamic branch in Model.BranchesList)
                        {
                           <option value="@branch.branch_id" selected="@(Model.orderBranch == branch.branch_id.ToString())">@branch.branch_name</option>
                        }
                    </select>
                    </div>
             </div>
                    <div class="form-item">
                        <div>
                            退货订单默认仓库
                        </div>
                        <div style="margin-left:40px">
                            <select id="branchForOrderReturn" data-placeholder="请选择..."
                                    class="form-control chosen-select-no-single" tabindex="4" style="width: 110px;">
                                <option value=""></option>
                                @foreach (dynamic branch in Model.BranchesList)
                                {
                                    <option value="@branch.branch_id" selected="@(Model.branchForOrderReturn == branch.branch_id.ToString())">@branch.branch_name</option>
                                }
                            </select>
                        </div>
                    </div>
             <div class="form-item">
                    <div>
                       退货单默认仓库
                    </div>
                    <div style="margin-left:40px">
                            <select id="branchForReturn" data-placeholder="请选择..."
                            class="form-control chosen-select-no-single" tabindex="4" style="width: 110px;">
                        <option value=""></option>
                                @foreach (dynamic branch in Model.BranchesList)
                        {
                                    <option value="@branch.branch_id" selected="@(Model.branchForReturn == branch.branch_id.ToString())">@branch.branch_name</option>
                        }
                    </select>
                    </div>
             </div>
              <div class="form-item">
                    <div>
                       退货默认仓库类型
                    </div>
                    <div style="margin-left:40px">
                            <select id="backBranchType" data-placeholder="请选择..."
                            class="form-control chosen-select-no-single" tabindex="4" style="width: 110px;">
                        <option value=""></option>
                                @foreach (dynamic backBranchType in Model.BranchTypeList)
                        {
                                    <option value="@backBranchType.Key" selected="@(Model.backBranchType == backBranchType.Key)">@backBranchType.Value</option>
                        }
                    </select>
                    </div>
             </div>
              <div class="form-item">
                    <div>
                       退货默认库位类型
                    </div>
                    <div style="margin-left:40px">
                            <select id="backBranchPositionType" data-placeholder="请选择..."
                            class="form-control chosen-select-no-single" tabindex="4" style="width: 110px;">
                        <option value=""></option>
                                @foreach (dynamic branchPositionType in Model.BranchPositionTypeList)
                        {
                                    <option value="@branchPositionType.type_id" selected="@(Model.backBranchPositionType == branchPositionType.type_id.ToString())">@branchPositionType.type_name</option>
                        }
                    </select>
                    </div>
             </div>
             <div class="form-item">
                    <div>
                       开单默认库位类型
                    </div>
                    <div style="margin-left:40px">
                            <select id="defaultBranchPositionType" data-placeholder="请选择..."
                            class="form-control chosen-select-no-single" tabindex="4" style="width: 110px;">
                        <option value=""></option>
                                @foreach (dynamic defaultBranchPositionType in Model.BranchPositionTypeList)
                        {
                                    <option value="@defaultBranchPositionType.type_id" selected="@(Model.defaultBranchPositionType == defaultBranchPositionType.type_id.ToString())">@defaultBranchPositionType.type_name</option>
                        }
                    </select>
                    </div>
             </div>

        </div>
        <div style="margin-top:30px">
            <div style="font-weight:800 ">
                票证通
                <div style="background-image: linear-gradient(to right, #aaa 0%, #aaa 10%, transparent 30%); width: 650px; height: 1px; margin-top: 7px; background-size: 7px 1px; background-repeat: repeat-x;margin-top:10px;"></div>
            </div>
            <div class="form-item">
                <div>对接票证通</div>
                <div>
                    <input class="magic-checkbox" type="checkbox" id="openTicketAccessSys" @(Model.openTicketAccessSys == "true" ? "checked" : "") style="margin-left: 0px;"/>
                    <label for="openTicketAccessSys"></label>
                </div>
            </div>
            <div id="ticketAccessSysForm">
                <div class="form-item">
                    <div> 账号 </div>
                    <div>
                        <input class="magic-input" id="ticketAccessSysAccount" style="width:300px" value="@(Model.ticketAccessSysAccount)"/>
                    </div>
                </div>
                <div class="form-item">
                    <div>密码 </div>
                    <div>
                        <input class="magic-input" id="ticketAccessSysPwd" style="width:300px" value="@(Model.ticketAccessSysPwd)"/>
                    </div>
                </div>

                <div class="form-item">
                    <div> APIKEY </div>
                    <div>
                        <input class="magic-input" id="ticketAccessSysKey" style="width:300px" value="@(Model.ticketAccessSysKey)"/>
                    </div>
                </div>
                <div class="form-item">
                    <div> 执照号 </div>
                    <div>
                        <input class="magic-input" id="ticketAccessSysLisense" style="width:300px" value="@(Model.ticketAccessSysLisense)"/>
                    </div>
                </div>
            </div>
        </div>
        <div style="margin-top:30px">
            <div style="font-weight:800 ">
                交账单
                <div style="background-image: linear-gradient(to right, #aaa 0%, #aaa 10%, transparent 30%); width: 650px; height: 1px; margin-top: 7px; background-size: 7px 1px; background-repeat: repeat-x;margin-top:10px;"></div>
            </div>
            <div class="form-item">
                    <div>
                       交账单收款方式
                    </div>
                    <div style="margin-left:40px">
                    <select id="checkAccountPayWayType" data-placeholder="请选择..."
                            class="form-control chosen-select-no-single" tabindex="3" style="width: 110px;">
                        <option value=""></option>
                        @foreach (dynamic opt in Model.CheckAccountPayWayOptions)
                        {
                            <option value="@opt.Key" selected="@(Model.checkAccountPayWayType == opt.Key)">@opt.Value</option>
                        }
                    </select>
                    </div>
             </div>
        </div>
        <div style="margin-top:45px">
            <div style="  font-weight: 800; ">
                生产日期/批次
                <div style="background-image: linear-gradient(to right, #aaa 0%, #aaa 10%, transparent 30%); width: 650px; height: 1px; margin-top: 7px; background-size: 7px 1px; background-repeat: repeat-x;margin-top:10px;"></div>
            </div>

             <div class="form-item">
                <div>
                    订单转销售单后单据号不变
                </div>
                <div>
                    <input class="magic-checkbox" type="checkbox" id="saleSheetNoSameAsOrder" @(Model.saleSheetNoSameAsOrder == "true" ? "checked" : "")/>
                    <label for="saleSheetNoSameAsOrder" style="display: block;"></label>
                </div>

            </div>

            <div class="form-item">
                <div>
                    生产日期类型
                </div>
                <div style="margin-left:40px">
                    <select id="batchType" class="chosen-select-no-single" tabindex="1" style="width: 110px; ">
                        @foreach (var opt in Model.batchTypeOptions)
                        {
                            <option value="@opt.Key" selected="@(Model.batchType == opt.Key)">@opt.Value</option>
                        }
                    </select>
                </div>
                        <div style="color:#aaa;font-size:14px; padding:0px 8px;line-height:38px;">
                    选择严格产期或批次类型时，商品档案将可以管理商品的严格产期和批次
                </div>
            </div>
            <div class="form-item">
                <div>
                    记忆生产日期(用于非严格)
                </div>
                <div style="margin-left:40px">
                    <input class="magic-checkbox" type="checkbox" id="loadVirtualProduceDate" @(Model.loadVirtualProduceDate == "false" ? "" : "checked") />
                    <label for="loadVirtualProduceDate" style="display: block;"></label>
                </div>
            </div>
            <div class="form-item">
                <div>
                    开单展示负库存批次
                </div>
                <div>
                    <input class="magic-checkbox" type="checkbox" id="showNegativeStock" @(Model.showNegativeStock == "true" ? "checked" : "") />
                    <label for="showNegativeStock" style="display: block;"></label>
                </div>
            </div>
            <div class="form-item">
                <div>复核时检查产期/批次</div>
                <div>
                    <input class="magic-checkbox" type="checkbox" id="needBatchOnReview" @(Model.needBatchOnReview == "true" ? "checked" : "") style="margin-left: 0px;" />
                    <label for="needBatchOnReview" style="display: block;"></label>
                </div>
            </div>
        </div>
        <div style="margin-top:45px">
            <div style="  font-weight: 800; ">
                一单多仓
                <div style="background-image: linear-gradient(to right, #aaa 0%, #aaa 10%, transparent 30%); width: 650px; height: 1px; margin-top: 7px; background-size: 7px 1px; background-repeat: repeat-x;margin-top:10px;"></div>
            </div>
            <div class="form-item">
                <div>启用占位单</div>
                <div>
                    <input class="magic-checkbox" type="checkbox" id="openPlaceholderSheet" @(Model.openPlaceholderSheet == "true" ? "checked" : "") style="margin-left: 0px;" />
                    <label for="openPlaceholderSheet" style="display: block;"></label>
                </div>
                <div style="color:#aaa;font-size:14px; padding:0px 8px;line-height:38px;">
                    占位单产生于装车时修改装车数量，以及开启占位单后销售订单的审核。占位单与销售订单可能区别于商品产期、批次、库位信息，其余完全复制销售订单。若销售订单存在占位单，单据产生的库存变化将取决于占位单。
                </div>
            </div>
            <div class="form-item">
                <div>
                    手机App开启一单多仓
                </div>
                <div>
                    <input class="magic-checkbox" type="checkbox" id="appOpenBranchsForSheet" @(Model.appOpenBranchsForSheet == "true" ? "checked" : "") />
                    <label for="appOpenBranchsForSheet" style="display: block;"></label>
                </div>
            </div>
        </div>
        <div style="margin-top:45px">
            <div style="  font-weight: 800; ">
                杂项
                <div style="background-image: linear-gradient(to right, #aaa 0%, #aaa 10%, transparent 30%); width: 650px; height: 1px; margin-top: 7px; background-size: 7px 1px; background-repeat: repeat-x;margin-top:10px;"></div>
            </div>

            <div class="form-item">
                <div>
                    销售单送货员必选
                </div>
                <div>
                            <input class="magic-checkbox" type="checkbox" id="saleSheetNeedSender" @(Model.saleSheetNeedSender == "true" ? "checked" : "") />
                            <label for="saleSheetNeedSender" style="display: block;"></label>
                </div>

            </div>

            <div class="form-item">
                  <div>
                       销售订单送货员必选
                  </div>
                  <div>
                       <input class="magic-checkbox" type="checkbox" id="saleOrderSheetNeedSender" @(Model.saleOrderSheetNeedSender == "true" ? "checked" : "") />
                       <label for="saleOrderSheetNeedSender" style="display: block;"></label>
                  </div>

            </div>


            <div class="form-item">
                <div>
                    销售单按送货员交账
                </div>
                <div>
                    <input class="magic-checkbox" type="checkbox" id="checkAccountBySender" @(Model.checkAccountBySender == "true" ? "checked" : "")/>
                    <label for="checkAccountBySender" style="display: block;"></label>
                </div>

            </div>

             <div class="form-item">
                <div>
                    电脑开单检索商品展示属性子商品
                </div>
                <div>
                    <input class="magic-checkbox" type="checkbox" id="showSonItems" @(Model.showSonItems == "true" ? "checked" : "")/>
                    <label for="showSonItems" style="display: block;"></label>
                </div>

            </div>

              <div class="form-item">
                <div>
                    单据交易时间默认为保存时间
                </div>
                <div>
                    <input class="magic-checkbox" type="checkbox" id="happenTimeOnSave" @(Model.happenTimeOnSave == "true" ? "checked" : "")/>
                    <label for="happenTimeOnSave" style="display: block;"></label>
                </div>

            </div>

            <div class="form-item">
                <div>
                    销售单审核后开启新单据
                </div>
                <div>
                    <input class="magic-checkbox" type="checkbox" id="newSheetAfterApprove" @(Model.newSheetAfterApprove == "true" ? "checked" : "")/>
                    <label for="newSheetAfterApprove" style="display:block;"></label>
                </div>
            </div>

            <div class="form-item">
                <div>
                    销售单保存后开启新单据
                </div>
                <div>
                    <input class="magic-checkbox" type="checkbox" id="newSheetAfterSave" @(Model.newSheetAfterSave == "true" ? "checked" : "")/>
                    <label for="newSheetAfterSave" style="display:block;"></label>
                </div>
            </div>
            <div class="form-item">
                <div>
                    新版交账单固定显示金额明细
                </div>
                <div>
                            <input class="magic-checkbox" type="checkbox" id="newCheckAccountShowLineAmountDetail" @(Model.newCheckAccountShowLineAmountDetail == "true" ? "checked" : "") />
                            <label for="newCheckAccountShowLineAmountDetail" style="display:block;"></label>
                </div>
            </div>
            <div class="form-item">
                        <div>
                            销售订单审核后开启新单据
                        </div>
                        <div>
                            <input class="magic-checkbox" type="checkbox" id="newSheetAfterSaleOrderSheetApprove" @(Model.newSheetAfterSaleOrderSheetApprove == "true" ? "checked" : "") />
                            <label for="newSheetAfterSaleOrderSheetApprove" style="display:block;"></label>
                        </div>
            </div>

            <div class="form-item">
                        <div>
                            销售订单保存后开启新单据
                        </div>
                        <div>
                            <input class="magic-checkbox" type="checkbox" id="newSheetAfterSaleOrderSheetSave" @(Model.newSheetAfterSaleOrderSheetSave == "true" ? "checked" : "") />
                            <label for="newSheetAfterSaleOrderSheetSave" style="display:block;"></label>
                        </div>
            </div>

             <div class="form-item">
                <div>
                    APP开单展示改前价
                </div>
                <div>
                    <input class="magic-checkbox" type="checkbox" id="appPriceBeforeChange" @(Model.appPriceBeforeChange == "true" ? "checked" : "")/>
                    <label for="appPriceBeforeChange" style="display: block;"></label>
                </div>
            </div>

            <div class="form-item">
                <div>
                    查单页切换时刷新
                </div>
                <div>
                    <input class="magic-checkbox" type="checkbox" id="requerySheetsOnTabClick" @(Model.requerySheetsOnTabClick == "true" ? "checked" : "")/>
                    <label for="requerySheetsOnTabClick" style="display: block;"></label>
                </div>

            </div>
            <div class="form-item">
                <div>
                    APP开单可选送货员
                </div>
                <div>
                    <input class="magic-checkbox" type="checkbox" id="appSaleUseSender" @(Model.appSaleUseSender == "true" ? "checked" : "")/>
                    <label for="appSaleUseSender" style="display: block;"></label>
                </div>
            </div>
            <div class="form-item">
                <div>
                    APP开单可录序列号
                </div>
                <div>
                    <input class="magic-checkbox" type="checkbox" id="appSaleUseSn" @(Model.appSaleUseSn == "true" ? "checked" : "")/>
                    <label for="appSaleUseSn" style="display: block;"></label>
                </div>
            </div>

        <div class="form-item">
        <div>
        参考成本价
        </div>
            <div style="margin-left:40px">
            <select id="costPriceType" class="chosen-select-no-single" tabindex="1" style="width: 110px; ">
            @foreach (var opt in Model.costPriceTypeOptions)
            {
                <option value="@opt.Key" selected="@(Model.costPriceType == opt.Key)">@opt.Value</option>
            }
            </select>
            <input style="width:90px;height:32px;" class="btn-white showDialogA magic-input" type="button" value="重设成本价"/>
            </div>
        </div>
        <div class="form-item">
            <div>
                            最近平均进价计入次数
            </div>
            <div style="margin-left:40px">
            <select id="recentPriceTime" class="chosen-select-no-single" tabindex="1" style="width: 110px; ">
            @foreach (var opt in Model.recentPriceTimeOptions)
            {
                <option value="@opt.Key" selected="@(Model.recentPriceTime == opt.Key)">@opt.Value</option>
            }
            </select>
            </div>
        </div>
        <div class="form-item">
        <div>
        客损科目
        </div>
        <div style="margin-left:40px">
        <select id="feeOutSubForKS" data-placeholder="请选择..."
            class="form-control chosen-select-no-single" tabindex="3" style="width: 110px;">
        @foreach (var sub in FeeOutSubs)
        {
        <option value="@sub.Id" selected="@(Model.feeOutSubForKS == sub.Id.ToString())">@sub.Name</option>
        }
        </select>
        </div>
        </div>

            <div class="form-item">
                <div>
                    销售开单默认单位
                </div>
                <div style="margin-left:40px">
                    <select id="saleSheetDefaultUnit" data-placeholder="请选择..."
                            class="form-control chosen-select-no-single" tabindex="2" style="width: 110px; ">

                        @foreach (var opt in Model.saleSheetDefaultUnitOptions)
                        {
                            <option value="@opt.Key" selected="@(Model.saleSheetDefaultUnit == opt.Key)">@opt.Value</option>
                        }
                    </select>
                </div>
            </div>
            <div class="form-item">
                <div>
                    调拨开单默认单位
                </div>
                <div style="margin-left:40px">
                    <select id="moveSheetDefaultUnit" data-placeholder="请选择..."
                            class="form-control chosen-select-no-single" tabindex="2" style="width: 110px; ">

                        @foreach (var opt in Model.moveSheetDefaultUnitOptions)
                        {
                            <option value="@opt.Key" selected="@(Model.moveSheetDefaultUnit == opt.Key)">@opt.Value</option>
                        }
                    </select>
                </div>
            </div>
            <div class="form-item">
                <div>
                    采购开单默认单位
                </div>
                <div style="margin-left:40px">
                    <select id="buySheetDefaultUnit" data-placeholder="请选择..."
                            class="form-control chosen-select-no-single" tabindex="2" style="width: 110px; ">

                        @foreach (var opt in Model.buySheetDefaultUnitOptions)
                        {
                            <option value="@opt.Key" selected="@(Model.buySheetDefaultUnit == opt.Key)">@opt.Value</option>
                        }
                    </select>
                </div>
            </div>
            <div class="form-item">
                <div>
                    WEB开单点击价格自动弹出价格列表
                </div>
                <div style="margin-left:40px">
                            <input class="magic-checkbox" type="checkbox" id="sheetShowPriceList" @(Model.sheetShowPriceList != "false" ? "checked" : "") />
                            <label for="sheetShowPriceList" style="display:block;"></label>
                </div>
            </div>
            <div class="form-item">
                <div>
                    app开单展示条码
                </div>
                <div style="margin-left:40px">
                    <select id="showBarcode" data-placeholder="请选择..."
                            class="form-control chosen-select-no-single" tabindex="2" style="width: 110px; ">

                        @foreach (var opt in Model.showBarcodeOptions)
                        {
                            <option value="@opt.Key" selected="@(Model.showBarcode == opt.Key)">@opt.Value</option>
                        }
                    </select>
                </div>
            </div>
            <div class="form-item">
                <div>
                    app开单展示规格
                </div>
                <div style="margin-left:40px">
                    <input class="magic-checkbox" type="checkbox" id="appSheetShowItemSpec" @(Model.appSheetShowItemSpec == "true" ? "checked" : "")/>
                    <label for="appSheetShowItemSpec" style="display:block;"></label>
                </div>
            </div>
             <div class="form-item">
                    <div>
                        app开单商品数量自动合并
                    </div>
                    <div style="margin-left:40px">
                         <input class="magic-checkbox" type="checkbox" id="appSheetQtyMerge" @(Model.appSheetQtyMerge == "true" ? "checked" : "") />
                         <label for="appSheetQtyMerge" style="display:block;"></label>                         
                    </div>
             </div>
             
             <div class="form-item">
                        <div style="width:380px">
                        app开单使用辅助数量(商品数量会自动合并成小单位)
                    </div>
                    <div style="margin-left:40px">
                         <input class="magic-checkbox" type="checkbox" id="appSheetUseAssistQty" @(Model.appSheetUseAssistQty == "true" ? "checked" : "") />
                         <label for="appSheetUseAssistQty" style="display:block;"></label>                         
                    </div>
             </div>

             <div class="form-item">
                    <div style="width:310px">
                        app开单常用展示借还货、陈列协议、定货
                    </div>
                    <div style="margin-left:40px">
                         <input class="magic-checkbox" type="checkbox" id="show_DH_CL_JH_inOftenItem" @(Model.show_DH_CL_JH_inOftenItem != "false" ? "checked" : "") />
                         <label for="show_DH_CL_JH_inOftenItem" style="display:block;"></label>                         
                    </div>
             </div>

            <div class="form-item">
                <div>
                    app开单商品展示风格
                </div>
                <div style="margin-left:40px">
                    <select id="appSheetItemShowStyle" data-placeholder="请选择..."
                            class="form-control chosen-select-no-single" tabindex="2" style="width: 110px; ">

                        @foreach (var opt in Model.appSheetItemShowStyleOptions)
                        {
                            <option value="@opt.Key" selected="@(Model.appSheetItemShowStyle == opt.Key)">@opt.Value</option>
                        }
                    </select>
                </div>
            </div>
             <div class="form-item">
                    <div>
                        app调拨单选品时负库仍可保存
                    </div>
                    <div style="margin-left:40px">
                         <input class="magic-checkbox" type="checkbox" id="appDbNoStockSave" @(Model.appDbNoStockSave == "true" ? "checked" : "") />
                         <label for="appDbNoStockSave" style="display:block;"></label>                         
                    </div>
             </div>
             <div class="form-item">
                    <div>
                        app销售单据选品时负库仍可保存
                    </div>
                    <div style="margin-left:40px">
                         <input class="magic-checkbox" type="checkbox" id="appXsNoStockSave" @(Model.appXsNoStockSave == "true" ? "checked" : "") />
                         <label for="appXsNoStockSave" style="display:block;"></label>                         
                    </div>
             </div>
              <div class="form-item">
                    <div>
                        app单据分享显示零售价
                    </div>
                    <div style="margin-left:40px">
                            <input class="magic-checkbox" type="checkbox" id="appShareRetailPrice" @(Model.appShareRetailPrice == "false" ? "" : "checked") />
                            <label for="appShareRetailPrice" style="display:block;"></label>
                    </div>
              </div>
              
            <div class="form-item">
                <div>
                    开单时按关键字精确查询
                </div>
                <div style="margin-left:40px">
                            <input class="magic-checkbox" type="checkbox" id="exactItemNameSearch" @(Model.exactItemNameSearch == "true" ? "checked" : "") />
                            <label for="exactItemNameSearch" style="display:block;"></label>
                </div>
              </div>
             <div class="form-item">
                <div>
                    app开单常用商品获取时间
                </div>
                <div style="margin-left:40px">
                    <select id="oftenItemMonths" data-placeholder="请选择..."
                            class="form-control chosen-select-no-single" tabindex="2" style="width: 110px; ">

                        @foreach (var opt in Model.oftenItemMonthsOptions)
                        {
                            <option value="@opt.Key" selected="@(Model.oftenItemMonths == opt.Key)">@opt.Value</option>
                        }
                    </select>
                </div>
            </div>
            <div class="form-item">
                <div>
                    默认打印条码类型
                </div>
                <div style="margin-left:40px">
                    <select id="sheetShowBarcodeStyle" data-placeholder="请选择..."
                            class="form-control chosen-select-no-single" tabindex="2" style="width: 110px; ">
                        @foreach (var opt in Model.sheetShowBarcodeStyleOptions)
                        {
                            <option value="@opt.Key" selected="@(Model.sheetShowBarcodeStyle == opt.Key)">@opt.Value</option>
                        }
                    </select>
                </div>
            </div>
                    <div class="form-item">
                        <div>
                            定货会选项
                        </div>
                        <div style="margin-left:40px">
                            <select id="orderItemSheet" data-placeholder="请选择..."
                                    class="form-control chosen-select-no-single" tabindex="2" style="width: 110px; ">
                                @foreach (var opt in Model.orderItemSheetOptions)
                                {
                                    <option value="@opt.Key" selected="@(Model.orderItemSheet == opt.Key)">@opt.Value</option>
                                }
                            </select>
                        </div>
                    </div>
            <div class="form-item">
                <div>
                    app兑付陈列单独开单
                </div>
                <div style="margin-left:40px">
                    <input class="magic-checkbox" type="checkbox" id="separateDisplayAndSale" @(Model.separateDisplayAndSale == "true" ? "checked" : "")/>
                    <label for="separateDisplayAndSale" style="display:block;"></label>
                </div>
            </div>
            <div class="form-item">
                <div style="width:310px">
                    APP开单属性子商品独立展示
                </div>
                <div style="margin-left:40px">
                    <input class="magic-checkbox" type="checkbox" id="noStockAttrSplitShow" @(Model.noStockAttrSplitShow == "true" ? "checked" : "")/>
                    <label for="noStockAttrSplitShow" style="display:block;"></label>
                </div>
            </div>
            <div class="form-item">
                <div style="width:310px">
                    销售单有欠款时，需要标记是否收回欠条
                </div>
                <div style="margin-left:40px">
                     <input class="magic-checkbox" type="checkbox" id="saleNeedMarkIOU" @(Model.saleNeedMarkIOU == "true" ? "checked" : "") />
                     <label for="saleNeedMarkIOU" style="display:block;"></label>
                </div>
            </div>
            <div class="form-item">
                <div style="width:310px">
                    开启严格产期时，是否自动带出无产期
                </div>
                <div style="margin-left:40px">
                     <input class="magic-checkbox" type="checkbox" id="showNoProduceDate" @(Model.showNoProduceDate == "true" ? "checked" : "") />
                     <label for="showNoProduceDate" style="display:block;"></label>
                </div>
            </div>
          @*   <div class="form-item">
                <div>
                    挂账退货单不统计欠款天数
                </div>
                <div style="margin-left:40px">
                            <input class="magic-checkbox" type="checkbox" id="noCountArrearsDayFromReturnOrder" @(Model.noCountArrearsDayFromReturnOrder == "true" ? "checked" : "") />
                            <label for="noCountArrearsDayFromReturnOrder" style="display:block;"></label>
                </div>
            </div> *@
            <div class="form-item" style="display:none">
                <div>
                    单据总额精度
                </div>
                <div>
                    <select id="sheetAmountRound" data-placeholder="请选择..."
                            class="form-control chosen-select-no-single" tabindex="4" style="width: 200px;">
                        @foreach (var opt in Model.sheetAmountRoundOptions)
                        {
                            <option value="@opt.Key" selected="@(Model.sheetAmountRound == opt.Key)">@opt.Value</option>
                        }
                    </select>
                </div>
            </div>
        </div>
        <div style="height:30px;"></div>
    </div>
    <div name="print" style="display:none">
        <div class="form-item">
            <div>
                打印小单位价格
            </div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="receiptShowSmallUnitPrice" @(Model.receiptShowSmallUnitPrice == "true" ? "checked" : "") />
                <label for="receiptShowSmallUnitPrice" style="display: block;"></label>
            </div>
        </div>
        <div class="form-item">
            <div>
                打印小单位数量
            </div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="receiptShowSmallUnitNumber" @(Model.receiptShowSmallUnitNumber == "true" ? "checked" : "") />
                <label for="receiptShowSmallUnitNumber" style="display: block;"></label>
            </div>
        </div>
        <div class="form-item">
            <div>
                调拨单商品行虚线间隔
            </div>
            <div>
                        <input class="magic-checkbox" type="checkbox" id="lineBetweenRowsMove" @(Model.lineBetweenRowsMove == "true" ? "checked" : "") />
                        <label for="lineBetweenRowsMove" style="display: block;"></label>
            </div>
        </div>
        <div class="form-item">
            <div>
                销售单商品行虚线间隔
            </div>
            <div>
                        <input class="magic-checkbox" type="checkbox" id="lineBetweenRowsSale" @(Model.lineBetweenRowsSale == "true" ? "checked" : "") />
                        <label for="lineBetweenRowsSale" style="display: block;"></label>
            </div>
        </div>

        <div class="form-item">
            <div>
                销售单小票品名打印位置
            </div>
            <div style="margin-left:40px">
                <select id="appPrtItemNamePos" style="width:200px;" data-placeholder="请选择..."
                        class="form-control chosen-select-no-single" tabindex="2" style="width: 110px; ">
                    @foreach (var opt in Model.appPrtItemNamePosOptions)
                            {
                    <option value="@opt.Key" selected="@(Model.appPrtItemNamePos == opt.Key)">@opt.Value</option>
                            }
                </select>
            </div>
        </div>

         <div class="form-item">
              <div>
                     小票打印退货商品单独展示
              </div>
              <div>
                        <input class="magic-checkbox" type="checkbox" id="receiptReturnItemsAtTail" @(Model.receiptReturnItemsAtTail == "true" ? "checked" : "") />
                <label for="receiptReturnItemsAtTail" style="display: block;"></label>
              </div>
        </div> 


        <div class="form-item">
            <div>
                小票打印业务员信息
            </div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="receiptShowSellerInfo" @(Model.receiptShowSellerInfo == "false" ? "" : "checked") />
                <label for="receiptShowSellerInfo" style="display: block;"></label>
            </div>
                </div>
        <div class="form-item">
            <div>
                小票打印送货员信息
            </div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="receiptShowSenderInfo" @(Model.receiptShowSenderInfo == "true" ? "checked" : "") />
                <label for="receiptShowSenderInfo" style="display: block;"></label>
            </div>
        </div>

		<div class="form-item">
		    <div>
			小票打印审核时间
		    </div>
			<div>
				<input class="magic-checkbox" type="checkbox" id="receiptShowApproveTime" @(Model.receiptShowApproveTime == "false" ? "" : "checked") />
				<label for="receiptShowApproveTime" style="display: block;"></label>
			</div>
		</div>

		<div class="form-item">
			<div>
				小票打印客户电话
			</div>
			<div>
				<input class="magic-checkbox" type="checkbox" id="receiptShowClientTel" @(Model.receiptShowClientTel == "false" ? "" : "checked") />
				<label for="receiptShowClientTel" style="display: block;"></label>
			</div>
		</div>	
        
        <div class="form-item">
            <div>
                小票打印仓库信息
            </div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="receiptShowBranchInfo" @(Model.receiptShowBranchInfo == "false" ? "" : "checked") />
                <label for="receiptShowBranchInfo" style="display: block;"></label>
            </div>
        </div>
        <div class="form-item">
            <div>
                小票打印客户地址
            </div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="receiptShowClientAddr" @(Model.receiptShowClientAddr == "false" ? "" : "checked") />
                <label for="receiptShowClientAddr" style="display: block;"></label>
            </div>
        </div>
        <div class="form-item">
            <div>
                小票打印商品规格
            </div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="receiptShowItemModel" @(Model.receiptShowItemModel == "true" ? "checked" : "") />
                <label for="receiptShowItemModel" style="display: block;"></label>
            </div>
        </div>

        <div class="form-item">
            <div>
                小票打印商品价格
            </div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="receiptShowItemRealPrice" @(Model.receiptShowItemRealPrice == "false" ? "" : "checked") />
                <label for="receiptShowItemRealPrice" style="display: block;"></label>
            </div>
        </div>
        <div class="form-item">
            <div>
                小票打印小计
            </div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="receiptShowItemSubAmount" @(Model.receiptShowItemSubAmount == "false" ? "" : "checked") />
                <label for="receiptShowItemSubAmount" style="display: block;"></label>
            </div>
        </div>
        <div class="form-item">
            <div>
                小票打印商品零售价
            </div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="receiptShowItemRetailPrice" @(Model.receiptShowItemRetailPrice == "true" ? "checked" : "") />
                <label for="receiptShowItemRetailPrice" style="display: block;"></label>
            </div>
        </div>
        <div class="form-item">
            <div>
                小票打印生产日期
            </div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="receiptShowVirtualProduceDate" @(Model.receiptShowVirtualProduceDate == "false" ? "" : "checked") />
                <label for="receiptShowVirtualProduceDate" style="display:block;"></label>
            </div>
        </div>


        <div class="form-item">
            <div>
                小票打印保质期
            </div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="receiptShowValidDays" @(Model.receiptShowValidDays == "true" ? "checked" : "") />
                <label for="receiptShowValidDays" style="display:block;"></label>
            </div>
        </div>
        <div class="form-item">
            <div>
                小票打印应收款余额
            </div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="receiptShowArrearsBalance" @(Model.receiptShowArrearsBalance == "true" ? "checked" : "") />
                <label for="receiptShowArrearsBalance" style="display: block;"></label>
            </div>
        </div>
                <div class="form-item">
            <div>
                小票打印此前欠款
            </div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="receiptShowBeforeArrears" @(Model.receiptShowBeforeArrears == "true" ? "checked" : "") />
                <label for="receiptShowBeforeArrears" style="display: block;"></label>
            </div>
        </div>
        <div class="form-item">
            <div>
                小票打印预收款余额
            </div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="receiptShowPrepayBalance" @(Model.receiptShowPrepayBalance == "true" ? "checked" : "") />
                <label for="receiptShowPrepayBalance" style="display: block;"></label>
            </div>
        </div>
        <div class="form-item">
            <div>
                小票打印定货会余额
            </div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="receiptShowOrderItemsBalance" @(Model.receiptShowOrderItemsBalance == "true" ? "checked" : "") />
                <label for="receiptShowOrderItemsBalance" style="display: block;"></label>
            </div>
        </div>
        <div class="form-item">
            <div>
                仅打印本次兑付商品
            </div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="receiptShowSheetOrderItemsBalance" @(Model.receiptShowSheetOrderItemsBalance == "true" ? "checked" : "") />
                <label for="receiptShowSheetOrderItemsBalance" style="display: block;"></label>
            </div>
        </div>
        <div class="form-item">
            <div>交账单打印单据日期</div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="printAccountSheetDate" @(Model.printAccountSheetDate == "true" ? "checked" : "") style="margin-left: 0px;" />
                <label for="printAccountSheetDate" style="display: block;"></label>
            </div>
        </div>
        <div class="form-item">
            <div>小票显示打印时间</div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="printSheetPrintTime" @(Model.printSheetPrintTime == "true" ? "checked" : "") style="margin-left: 0px;" />
                <label for="printSheetPrintTime" style="display: block;"></label>
            </div>
		</div>
		<div class="form-item">
			<div>小票打印条码图后不加换行符</div>
			<div>
				<input class="magic-checkbox" type="checkbox" id="printNoWrapAfterBarcode" @(Model.printNoWrapAfterBarcode == "true" ? "checked" : "") style="margin-left: 0px;" />
				<label for="printNoWrapAfterBarcode" style="display: block;"></label>
			</div>
		</div>
		<div class="form-item">
			<div>小票打印总额使用更大字体</div>
			<div>
				<input class="magic-checkbox" type="checkbox" id="printBiggerSubAmount" @(Model.printBiggerSubAmount == "true" ? "checked" : "") style="margin-left: 0px;" />
				<label for="printBiggerSubAmount" style="display: block;"></label>
			</div>
		</div>
		<div class="form-item">
			<div>小票打印支付方式使用更大字体</div>
			<div>
				<input class="magic-checkbox" type="checkbox" id="printBiggerPayway" @(Model.printBiggerPayway == "true" ? "checked" : "") style="margin-left: 0px;" />
				<label for="printBiggerPayway" style="display: block;"></label>
			</div>
		</div>
        <div class="form-item">
            <div>
                交账单明细行虚线间隔
            </div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="lineBetweenAcctRows" @(Model.lineBetweenAcctRows == "true" ? "checked" : "") />
                <label for="lineBetweenAcctRows" style="display: block;"></label>
            </div>
        </div>
        <div style="display: flex; height: 100px;margin-top:22px">
            <div class="form-item">
                <div class="form-label">
                    打印页眉图片
                </div>
                <div>
                    <input class="magic-checkbox" type="checkbox" id="printBillHeadImage" @(Model.printBillHeadImage == "true" ? "checked" : "") />
                    <label for="printBillHeadImage" style="display: block;"></label>
                </div>
            </div>
            <div class="form-item" style="margin-left:90px">
                <button id="uploadLeft">上传 </button>
                <form class="input-block" id="formHeadImage">
                    <input id="upload-official-head" type="file" name="file" accept="image/png,image/bmp,image/jpeg" style=" display:none" />
                </form>
            </div>
            <img id="upload-official-head-img" src="@Html.Raw(Model.ObsBucketLinkHref)/uploads@(Model.billHeadImage)?v=@(Model.UrlRandom)" onerror="this.hidden='true'" style="width:100px; margin-left:35px" />

        </div>
        <div style="display: flex; height:100px;margin-top:22px ">
            <div class="form-item">
                <div class="form-label">
                    打印页尾图片
                </div>
                <div>
                    <input class="magic-checkbox" type="checkbox" id="printBillTailImage" @(Model.printBillTailImage == "true" ? "checked" : "") />
                    <label for="printBillTailImage" style="display: block;"></label>
                </div>
            </div>

            <div class="form-item" style="margin-left:90px">
                <button id="uploadRight">上传 </button>
                <form class="input-block" id="formTailImage">

                    <div>
                        <input id="upload-official-seal" type="file" name="file" accept="image/png,image/bmp,image/jpeg" style="display: none" />
                    </div>
                </form>
            </div>
            <img id="upload-official-seal-img" src="@Html.Raw(Model.ObsBucketLinkHref)/uploads@(Model.billTailImage??Model.company_cachet)" onerror="this.hidden='true'" style="width:100px;margin-left:35px" />
             <div style="border-right:1px dashed;"></div>
            <div class="form-item" style="margin-left:90px">
                       <div style="display:flex;flex-direction:column;align-items:center">
                            <button id="uploadRight2">上传 </button>
                            <div style="font-size:14px;color:#777;">公司名称2公章(可选)</div>
                       </div>
                        <form class="input-block" id="formTailImage2">

                            <div>
                                <input id="upload-official-seal-2" type="file" name="file" accept="image/png,image/bmp,image/jpeg" style="display: none" />
                            </div>
                        </form>
            </div>
            <img id="upload-official-seal-img-2" src="@Html.Raw(Model.ObsBucketLinkHref)/uploads@(Model.billTailImage2)" onerror="this.hidden='true'" style="width:100px;margin-left:35px" />
            <div id="preview-image" style="display:none">
                        <div id="itemCaption" style="padding:10px 0;background-color:#fff;display:flex;justify-content:center"><span style="font-size:20px;">照片</span></div>
                        <div style="padding:10px;overflow:hidden;display:flex;justify-content:center"> </div>
                    </div>
              </div>

        <div class="form-item" style="height:80px;">
            <div>
                小票页尾文字
            </div>
            <div>
                <textarea id="receiptTail" style="margin-left: 20px; width: 300px; height: 75px; border-color: #ddd">@(Model.receiptTail)</textarea>
            </div>
        </div>
        <div class="form-item" style="height:80px;">
            <div>
                打印公司名称(一行一个)
            </div>
            <div>
                <textarea id="companyNamesToPrint" style="margin-left: 20px; width: 300px; height: 75px; border-color: #ddd">@(Model.companyNamesToPrint)</textarea>
            </div>
        </div>


        <div class="form-item">
            <div>
                调拨单小票品名打印位置
            </div>
            <div style="margin-left:40px">
                <select id="appPrtMoveItemNamePos" style="width:200px;" data-placeholder="请选择..."
                        class="form-control chosen-select-no-single" tabindex="2">
                    @foreach (var opt in Model.appPrtItemNamePosOptions)
                {
                    <option value="@opt.Key" selected="@(Model.appPrtMoveItemNamePos == opt.Key)">@opt.Value</option>
                }
                </select>
            </div>
        </div>

        <div class="form-item">
            <div>
                调拨单小票打印单位换算
            </div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="appPrtMoveUnitRelation" @(Model.appPrtMoveUnitRelation == "true" ? "checked" : "") />
                <label for="appPrtMoveUnitRelation" style="display: block;"></label>
            </div>
        </div>
        <div class="form-item">
            <div>
                调拨单小票打印商品价格和金额
            </div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="appPrtMovePrice" @(Model.appPrtMovePrice == "false" ? "" : "checked") />
                <label for="appPrtMovePrice" style="display: block;"></label>
            </div>
        </div>
        <div class="form-item">
            <div>
                销售(订)单小票打印本次欠款
            </div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="appPrintLeftAmt" @(Model.appPrintLeftAmt != "false" ? "checked" : "") />
                <label for="appPrintLeftAmt" style="display: block;"></label>
            </div>
        </div>

    </div>
    @* <div name="mall" style="display:none"> *@
    @*     <div class="form-item"> *@
    @*         <div>商城仓库</div> *@
    @*         <div style="margin-left:40px"> *@
    @*             <select id="mallBranch" data-placeholder="请选择..." *@
    @*                     class="form-control chosen-select-no-single" tabindex="3" style="width: 110px;"> *@
    @*                 @foreach (dynamic branch in Model.BranchesList) *@
    @*                 { *@
    @*                     <option value="@branch.branch_id" selected="@(Model.mallBranch == branch.branch_id.ToString())">@branch.branch_name</option> *@
    @*                 } *@
    @*             </select> *@
    @*         </div> *@
    @*     </div> *@
    @*     <div class="form-item"> *@
    @*         <div>客户门店二维码有效期</div> *@
    @*         <div style="display: flex;align-items: center"> *@
    @*             <input class="magic-input" id="qrCodeExpiresTime" style="width:100px" value="@(Model.qrCodeExpiresTime)"/> *@
    @*             <div>(分钟), 空则二维码长期有效</div> *@
    @*         </div> *@
    @*     </div> *@
    @* </div> *@
     <div name="necessary" style="display:none">
    <div style="height:30px;"></div>
    <div>
        <div style="font-weight:800;">
            客户档案
            <div style="background-image: linear-gradient(to right, #aaa 0%, #aaa 10%, transparent 30%); width: 650px; height: 1px; margin-top: 7px; background-size: 7px 1px; background-repeat: repeat-x;margin-top:10px;"></div>
        </div>
        <div class="form-item">
            <div>老板姓名必填</div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="clientBossNecessary" @(Model.clientBossNecessary == "true" ? "checked" : "") style="margin-left: 0px;" />
                <label for="clientBossNecessary" style="display: block;"></label>
            </div>
        </div>

          <div class="form-item">
            <div>客户编号自增</div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="supcustNoSerial" @(Model.supcustNoSerial == "true" ? "checked" : "") style="margin-left: 0px;" />
                <label for="supcustNoSerial" style="display: block;"></label>
            </div>
        </div>

        <div class="form-item">
            <div>手机号必填</div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="clientMobileNecessary" @(Model.clientMobileNecessary == "true" ? "checked" : "") style="margin-left: 0px;" />
                <label for="clientMobileNecessary" style="display: block;"></label>
            </div>
        </div>

        @*<div class="form-item">
            <div>片区必填</div>
            <div>
                <input class="magic-checkbox" type="checkbox" id="clientRegionNecessary" @(Model.clientRegionNecessary != "false" ? "checked" : "") style="margin-left: 0px;" />
                <label for="clientRegionNecessary" style="display: block;"></label>
            </div>
        </div>*@

                    <div class="form-item">
                        <div>门头照必填</div>
                        <div>
                            <input class="magic-checkbox" type="checkbox" id="doorPicNecessary" @(Model.doorPicNecessary == "true" ? "checked" : "") style="margin-left: 0px;" />
                            <label for="doorPicNecessary" style="display: block;"></label>
                        </div>
                    </div>

                    <div class="form-item">
                        <div>定位必填</div>
                        <div>
                            <input class="magic-checkbox" type="checkbox" id="clientLocationNecessary" @(Model.clientLocationNecessary == "true" ? "checked" : "") style="margin-left: 0px;" />
                            <label for="clientLocationNecessary" style="display: block;"></label>
                        </div>
                    </div>

                <div class="form-item">
                    <div>渠道必填</div>
                    <div>
                        <input class="magic-checkbox" type="checkbox" id="clientGroupNecessary"  @(Model.clientGroupNecessary == "true" ? "checked" : "") style="margin-left: 0px;" />
                        <label for="clientGroupNecessary" style="display: block;"></label>
                    </div>
                </div>
                <div class="form-item">
                    <div>客户等级必填</div>
                    <div>
                        <input class="magic-checkbox" type="checkbox" id="clientLevelNecessary" @(Model.clientLevelNecessary == "true" ? "checked" : "") style="margin-left: 0px;" />
                        <label for="clientLevelNecessary" style="display: block;"></label>
                    </div>
                </div>
      
                <div class="form-item">
                    <div>客户关联业务员</div>
                    <div>
                        <input class="magic-checkbox" type="checkbox" id="clientRelateSeller" @(Model.clientRelateSeller == "true" ? "checked" : "") style="margin-left: 0px;" />
                        <label for="clientRelateSeller" style="display: block;"></label>
                    </div>
                </div>
       

                </div>

            <div>
                <div style="font-weight:800;">
                    陈列协议
                <div style="background-image: linear-gradient(to right, #aaa 0%, #aaa 10%, transparent 30%); width: 650px; height: 1px; margin-top: 7px; background-size: 7px 1px; background-repeat: repeat-x;margin-top:10px;"></div>
                </div>
                <div class="form-item">
                    <div>必须选择模板</div>
                    <div>
                        <input class="magic-checkbox" type="checkbox" id="dispTemplateNecessary" @(Model.dispTemplateNecessary == "true" ? "checked" : "") style="margin-left: 0px;" />
                        <label for="dispTemplateNecessary" style="display: block;"></label>
                    </div>
                </div>                
            </div>
            
            <div style="height:30px;"></div>
            <div>
                <div style="font-weight:800;">
                    商品档案
                    <div style="background-image: linear-gradient(to right, #aaa 0%, #aaa 10%, transparent 30%); width: 650px; height: 1px; margin-top: 7px; background-size: 7px 1px; background-repeat: repeat-x;margin-top:10px;"></div>
                </div>
                 
                <div class="form-item">
                    <div>默认保质期类型</div>
                    <div style="margin-left:40px">
                        <select id="validDayType" class="chosen-select-no-single" tabindex="1" style="width: 110px; "> 
                            <option value="d" selected="@(Model.validDayType == "d")">天</option>
                            <option value="m" selected="@(Model.validDayType == "m")">月</option> 
                            <option value="y" selected="@(Model.validDayType == "y")">年</option> 
                        </select>
                    </div>
                </div>

                 <div class="form-item">
                    <div>多单位价格联动</div>
                    <div>
                        <input class="magic-checkbox" type="checkbox" id="unitPriceRelated" @(Model.unitPriceRelated != "false" ? "checked" : "") style="margin-left: 0px;" />
                        <label for="unitPriceRelated" style="display: block;"></label>
                    </div>
                </div>        
                 
            </div>

             <div style="height:30px;"></div>
            <div>
                <div style="font-weight:800;">
                    属性管理
                    <div style="background-image: linear-gradient(to right, #aaa 0%, #aaa 10%, transparent 30%); width: 650px; height: 1px; margin-top: 7px; background-size: 7px 1px; background-repeat: repeat-x;margin-top:10px;"></div>
                </div>
                 
                <div class="form-item">
                    <div>区分库存</div>
                    <div>
                            <input class="magic-checkbox" type="checkbox" id="isDistinctStock" @((Model.isDistinctStock == "true" || Model.isDistinctStock == "false") ? "disabled" : "") @(Model.isDistinctStock == "true" ? "checked" : "") onchange="needRememberSonItemPriceCheckbox(this)" style="margin-left: 0px; " />
                        <label for="isDistinctStock" style="display: block;"></label>
                    </div>
                </div>

                 <div class="form-item">
                    <div>属性名称</div>
                    <div>
                    <div>
                        <input class="magic-input" id="attrName" style="width:300px" value="@(Model.attrName)"/>
                     </div>
                    </div>
                </div>       
                
                <div class="form-item">
                    <div>属性商品打印主商品</div>
                    <div>
                        <input class="magic-checkbox" type="checkbox" id="isCombinePrint" @(Model.isCombinePrint == "true" ? "checked" : "") style="margin-left: 0px;" />
                        <label for="isCombinePrint" style="display: block;"></label>
                    </div>
                </div>

                    <div class="form-item" id="needPriceRemember" class="hidden">
                        <div>记忆属性子商品价格</div>
                    <div>
                            <input class="magic-checkbox" type="checkbox" id="isRememberSonItemPrice" @(Model.isRememberSonItemPrice =="true" ? "checked" : "") style="margin-left: 0px;" />
                            <label for="isRememberSonItemPrice" style="display: block;"></label>
                    </div>
                </div>
                 
            </div>
  </div>
  <!--
    <div name="mall" style="display:none">
                <div class="form-item">
                    <div>
                        商城仓库
                    </div>
                    <div style="margin-left:40px">
                        <select id="mallBranch" data-placeholder="请选择..."
                                class="form-control chosen-select-no-single" tabindex="3" style="width: 110px;">
                            @foreach (dynamic branch in Model.BranchesList)
                            {
                                <option value="@branch.branch_id" selected="@@(Model.mallBranch == branch.branch_id.ToString())">@@branch.branch_name</option>
                            }
                        </select>
                    </div>
                </div>
            </div>
            -->
</div>
</div>

 
    <script type="text/x-template" id="dialogTmpl">

        <form>
            <div>
                <div class="row">
                    <div id="itemid" class="title" style="float:left">商品名称：</div>

                    <div style="float:left">
                        <input class="magic-input" id="item_id" hidden />
                        <input class="magic-input text" id="item_name"  validator="required"  placeholder="点击放大镜，选择商品"   /><img class="showDialogB" style="position:relative;right:23px;top:4.5px;  width:18px;height:18px;cursor:pointer;" src="/jqwidgets/images/search_lg.png">
                    </div>
                </div>
                <div class="row">
                    <div class="title" style="float:left">日     期：</div>
                    <div style="float:left">
                        <div id="jqxdatetimeStartTime" style="float:left"></div>
                        <label style="float:left">~</label>
                        <div id="jqxdatetimeEndTime" style="float:left"></div>
                    </div>
                </div>
                <div class="row" id="changePrice">
                    <div class="title" style="float:left">设定价格：</div>
                    <div style="float:left;">
                        <input class="magic-input text"   validator="required,number"  id="buyPriceReset" />
                        <input class="magic-input text"   id="unit_no" readonly />

                    </div>
                    <div>
                    </div>
                </div>
                <div class="row">
                    <span style="color:#9c9c9c;font-size:15px">提示：点击放大镜，选择商品带出单位，填写价格后，再点击确定；重算成本指重算指定时间段内销售单中的成本价；</span>
                </div>
            </div>
        </form>

    </script>
<script type="text/template" id="popItem" style="display:none">
        <div id="queryBox">
            <label class="searcher">

            </label>
            <label style="float:left;margin:2px 10px;">商品名称:</label>
            <div style="float:left;margin-right:10px">
                <input class="magic-input"   type="text" bind="searchString" placeholder="请输入简拼/名称/条码" keycode="13" />
            </div>
        </div>
        <div id="grid"></div>
    </script>
<script type="text/javascript">
        app = new App('@Model.operKey', {
            setup() {
                this.add('Dialog')
                    .add('Grid')
                    .add('Validator')
                    .add('QueryBox')
                    .add("Window")

                let windowHeight = document.body.offsetHeight - 50
                let windowWidth = document.body.offsetWidth - 80
                $("#popChooseClearType").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 400, width: 600, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

                $("#clearDataButton").click(() => {
                    $('#popChooseClearType').jqxWindow('open');
                    $("#stepone").css("display", "block")
                    $("#steptwo").css("display", "none")
                })
                $("#confirmClearNext").click(() => {
                    var selectClearTypesChinese = ""
                    var selectClearTypes = []
                    const dic = { 'item': "商品", "client": "客户", "sheet": "单据" }
                    const clearType = $("[name='clearDataType']:checked").each((e, i) => { 
                        selectClearTypes.push(i.value)
                    })
                    selectClearTypesChinese = selectClearTypes.map(type => {return dic[type] }).join(",")
                    $("#clear-data-type").html(selectClearTypesChinese)
                    console.log(clearType)
                    $.ajax({
                        url: "/api/CompanySetting/SendClearDataMessage?operKey=@Model.operKey",
                        method:"POST",
                        data: {
                            operKey: "@Model.operKey"
                        },
                        success: (res) => {
                            if (res.result = "OK") {

                                $("#boss_mobile").html(res.boss_mobile)
                                $("#staff_mobile").html(res.staff_mobile)
                                $("#staff_name").html(res.staff_name)
                                $("#stepone").css("display","none")
                                $("#steptwo").css("display", "block")
                                if (res.needNotifyBoss) {
                                    $("#bossVerifyInput").css("display", "block")
                                }
                            }
                        }
                    })
                })
                $("#confirmClearButton").click(() => {
                    var selectClearTypesChinese = ""
                    var selectClearTypes = []
                    const dic = { 'item': "商品", "client": "客户", "sheet": "单据" }
                    const clearType = $("[name='clearDataType']:checked").each((e, i) => {
                        selectClearTypes.push(i.value)
                    })
                    var bossVerifyCode  = $("#bossVerifyCodeInput").val()
                    var staffVerifyCode = $("#staffVerifyCodeInput").val()
                    var commitment = $("#commitmentInput").val()
                    var actualCommitmentText = "确认清空"
                    if (commitment != actualCommitmentText) {
                        bw.toast("承诺填写错误！请仔细核对！")
                        return
                    }
                    if (confirm("是否执行清除操作？点击【确定】按钮，将立刻执行按钮") == true) {
                        $.ajax({
                            url: "/api/CompanySetting/ClearData?operKey=@Model.operKey",
                            method: "POST",
                            data: {
                                operKey: "@Model.operKey",
                                bossVerifyCode,
                                staffVerifyCode,
                                clearType: selectClearTypes.join(",")
                            },
                            success: (res) => {
                                bw.toast(res.msg)
                            }
                        })
                    }
                   
                })
            },
            Query() {
                this.queryBox.Query((result) => {
                    app.storeGrid(Object.values(result.rows));
                });
            },
            '[]': {
                click: {
                    save(e) {
                        const visitDistance = $("#visitDistance")[0].value
                        const limitVisitDistance = $("#limitVisitDistance")[0].checked
                        if (limitVisitDistance&&!visitDistanceTypeIsValid(visitDistance)) {
                            bw.toast("拜访距离只能设置成数字")
                            return
                        }
                        const positionPeriodStart = $("#positionPeriodStart")[0].value
                        if (positionPeriodStart && !checkInputTimeIsValid(positionPeriodStart)) {
                            bw.toast("外勤时间段起始时间设置有误，正确格式应为 xx:xx(如9:30)")
                            return
                        }
                        const positionPeriodEnd = $("#positionPeriodEnd")[0].value
                        if (positionPeriodEnd && !checkInputTimeIsValid(positionPeriodEnd)) {
                            bw.toast("外勤时间段终止时间设置有误，正确格式应为 xx:xx(如9:30)")
                            return
                        }
                        const minVisitDuration = $("#minVisitDuration")[0].value
                        if (minVisitDuration && !isNumber(minVisitDuration)) {
                            bw.toast("外勤访问最短时间设置格式有误，请输入数字")
                            return
                        }
                        var formData,form
                        var tailFileImage = $('#upload-official-seal');
                        
                        if (tailFileImage.val()) {
                            if (tailFileImage[0].files[0].size > 1024 * 200) {
                                    bw.toast("文件不要超出200k");
                                    return;
                            }
                           
                            
                            form =  $('#formTailImage')
                            formData = new FormData(form[0]);
                            $.ajax({
                                url: '/api/CompanySetting/ImportBillImage?operKey=@Model.operKey&&type=tailImage',
                                    type: "post",
                                    data: formData,
                                    processData: false,
                                    contentType: false,
                                    success: function(res) {
                                        console.log(res);
                                        bw.toast(res.msg);
                                    },
                                    error: function(err) {
                                        bw.toast("页尾图片上传失败");
                                       
                                    }
                                });
                        }
                        var tailFileImage2 = $('#upload-official-seal-2');

                        if (tailFileImage2.val()) {
                            if (tailFileImage2[0].files[0].size > 1024 * 200) {
                                bw.toast("文件不要超出200k");
                                return;
                            }

                            form = $('#formTailImage2')
                            formData = new FormData(form[0]);
                            $.ajax({
                                url: '/api/CompanySetting/ImportBillImage?operKey=@Model.operKey&&type=tailImage2',
                                type: "post",
                                data: formData,
                                processData: false,
                                contentType: false,
                                success: function (res) {
                                    console.log(res);
                                    bw.toast(res.msg);
                                },
                                error: function (err) {
                                    bw.toast("页尾图片上传失败");

                                }
                            });
                        }
                        var headImageFile = $('#upload-official-head');
                        if (headImageFile.val()) {
                               if (headImageFile[0].files[0].size > 1024 * 200) {
                                    bw.toast("文件不要超出200k");
                                    return;
                                }
                            form =  $('#formHeadImage')
                            formData = new FormData(form[0]);
                            $.ajax({
                                url: '/api/CompanySetting/ImportBillImage?operKey=@Model.operKey&&type=headImage',
                                    type: "post",
                                    data: formData,
                                    processData: false,
                                    contentType: false,
                                    success: function(res) {
                                        console.log(res);
                                        bw.toast(res.msg);
                                    },
                                    error: function(err) {
                                        bw.toast("页眉图片上传失败");
                                       
                                    }
                                });
                        }
                        console.log($("#imageClarityLevel").val())
                        var args = {
                            operKey: '@Model.operKey',
                            printBillHeadImage: $("#printBillHeadImage")[0].checked,
                            printBillTailImage: $("#printBillTailImage")[0].checked,
                            costPriceType: $("#costPriceType").val(),
                            recentPriceTime: $("#recentPriceTime").val(),
                            validDayType: $("#validDayType").val(),                            
                            sheetShowBarcodeStyle: $("#sheetShowBarcodeStyle").val(),
                            orderItemSheet: $("#orderItemSheet").val(),
                            appPrtItemNamePos: $("#appPrtItemNamePos").val(), 
                            printSheetPrintTime:$("#printSheetPrintTime")[0].checked,
                            printNoWrapAfterBarcode: $('#printNoWrapAfterBarcode')[0].checked,
                            printBiggerSubAmount: $('#printBiggerSubAmount')[0].checked,
                            printBiggerPayway: $('#printBiggerPayway')[0].checked,
                            saleSheetDefaultUnit: $("#saleSheetDefaultUnit").val(),
                            moveSheetDefaultUnit: $("#moveSheetDefaultUnit").val(),
                            buySheetDefaultUnit: $("#buySheetDefaultUnit").val(),
                            showBarcode: $("#showBarcode").val(),
                            checkAccountPayWayType: $("#checkAccountPayWayType").val(),
                            
                            sheetShowPriceList: $("#sheetShowPriceList")[0].checked,
                            appSheetShowItemSpec: $("#appSheetShowItemSpec")[0].checked,
                            appSheetQtyMerge: $("#appSheetQtyMerge")[0].checked,
                            appSheetUseAssistQty: $("#appSheetUseAssistQty")[0].checked,
                               show_DH_CL_JH_inOftenItem: $("#show_DH_CL_JH_inOftenItem")[0].checked,
                            appDbNoStockSave: $("#appDbNoStockSave")[0].checked,
                            appXsNoStockSave: $("#appXsNoStockSave")[0].checked,
                            appShareRetailPrice: $("#appShareRetailPrice")[0].checked,
                            imageClarityLevel: $("#imageClarityLevel").val(),
                            separateDisplayAndSale: $("#separateDisplayAndSale")[0].checked,
                            noStockAttrSplitShow: $("#noStockAttrSplitShow")[0].checked,
                            saleNeedMarkIOU: $("#saleNeedMarkIOU")[0].checked,
                            showNoProduceDate: $("#showNoProduceDate")[0].checked,
                            //noCountArrearsDayFromReturnOrder: $("#noCountArrearsDayFromReturnOrder")[0].checked,
                                
                            appSheetItemShowStyle:$("#appSheetItemShowStyle").val(), 
                            oftenItemMonths:$("#oftenItemMonths").val(),
                            feeOutSubForKS: $("#feeOutSubForKS").val(),
                            //mallBranch: $("#mallBranch").val(),
                            orderBranch: $("#orderBranch").val(),
                            branchForReturn: $("#branchForReturn").val(),
                            branchForOrderReturn: $("#branchForOrderReturn").val(),
                            backBranchPositionType :$("#backBranchPositionType").val(),
                            backBranchType: $("#backBranchType").val(),
                            defaultBranchPositionType: $("#defaultBranchPositionType").val(),
                            sheetAmountRound: $("#sheetAmountRound").val(),
                            receiptShowSmallUnitPrice: $("#receiptShowSmallUnitPrice")[0].checked,
                            lineBetweenRowsMove: $("#lineBetweenRowsMove")[0].checked,
                            lineBetweenRowsSale: $("#lineBetweenRowsSale")[0].checked,
                            lineBetweenAcctRows: $("#lineBetweenAcctRows")[0].checked,
                                                           
                            receiptShowSmallUnitNumber:$("#receiptShowSmallUnitNumber")[0].checked,
                            receiptReturnItemsAtTail: $("#receiptReturnItemsAtTail")[0].checked,
                            receiptShowSellerInfo: $("#receiptShowSellerInfo")[0].checked,
                            receiptShowSenderInfo: $("#receiptShowSenderInfo")[0].checked,
                            receiptShowApproveTime: $("#receiptShowApproveTime")[0].checked, //获取小票打印审核时间复选框选中状态
                            receiptShowClientTel: $("#receiptShowClientTel")[0].checked,     //获取小票打印客户电话复选框选中状态
                            receiptShowBranchInfo: $("#receiptShowBranchInfo")[0].checked,
                            receiptShowClientAddr: $("#receiptShowClientAddr")[0].checked,
                            printAccountSheetDate: $("#printAccountSheetDate")[0].checked,
                            receiptTail: $("#receiptTail")[0].value,
                            companyNamesToPrint: $("#companyNamesToPrint")[0].value,
                                
                                companyName: $("#companyName")[0].value,
                                contactTel: $("#contactTel")[0].value,
                                companyAddress: $("#companyAddress")[0].value,
                                positionPeriodStart: $("#positionPeriodStart")[0].value,
                                positionPeriodEnd: $("#positionPeriodEnd")[0].value,
                                minVisitDuration: $("#minVisitDuration")[0].value,
                                visitDistance: $("#visitDistance")[0].value,
                                limitVisitDistance: $("#limitVisitDistance")[0].checked,
                                forceVisit: $("#forceVisit")[0].checked,
                                forceDoorPic: $("#forceDoorPic")[0].checked,
                                forceRemark: $("#forceRemark")[0].checked,
                                forceDisplayPic: $("#forceDisplayPic")[0].checked,
                                displayPicCount: $("#displayPicCount")[0].value,
                                autoVisitEnd: $("#autoVisitEnd")[0].checked,
                                autoVisitEndDistance: $("#autoVisitEndDistance")[0].value,
                            receiptShowItemModel: $("#receiptShowItemModel")[0].checked,
                            receiptShowItemRealPrice: $("#receiptShowItemRealPrice")[0].checked,
                            receiptShowItemSubAmount: $("#receiptShowItemSubAmount")[0].checked,
                            receiptShowItemRetailPrice: $("#receiptShowItemRetailPrice")[0].checked,
                            receiptShowValidDays: $("#receiptShowValidDays")[0].checked,
                            receiptShowVirtualProduceDate:$("#receiptShowVirtualProduceDate")[0].checked,
                            receiptShowArrearsBalance: $("#receiptShowArrearsBalance")[0].checked,
                            receiptShowBeforeArrears: $("#receiptShowBeforeArrears")[0].checked,
                            receiptShowPrepayBalance: $("#receiptShowPrepayBalance")[0].checked,
                            receiptShowOrderItemsBalance: $("#receiptShowOrderItemsBalance")[0].checked,
                            receiptShowSheetOrderItemsBalance: $("#receiptShowSheetOrderItemsBalance")[0].checked,

                            appPrtMoveItemNamePos: $("#appPrtMoveItemNamePos").val(),
                            appPrtMovePrice: $("#appPrtMovePrice")[0].checked,
                            appPrintLeftAmt: $("#appPrintLeftAmt")[0].checked,
                            appPrtMoveUnitRelation: $("#appPrtMoveUnitRelation")[0].checked,
                            
                            flowExistMove: $("#flowExistMove")[0].checked,
                            openPlaceholderSheet: $("#openPlaceholderSheet")[0].checked,
                            showNegativeStock: $("#showNegativeStock")[0].checked, 
                            needBatchOnReview: $("#needBatchOnReview")[0].checked,
                            reviewOrderBeforeAssignVan: $("#reviewOrderBeforeAssignVan")[0].checked, 
                            ignorePayFailedSheetOnAssignVan: $("#ignorePayFailedSheetOnAssignVan")[0].checked,
                            ignorePayFailedSheetOnOrderToSale: $("#ignorePayFailedSheetOnOrderToSale")[0].checked,
                            canMultiBranchAssign: $("#canMultiBranchAssign")[0].checked, 
                                flowExistMoveBack: $("#flowExistMoveBack")[0].checked,
                                printCheckWhole: $("#printCheckWhole")[0].checked,
                                printCheckOpenStock: $("#printCheckOpenStock")[0].checked,
                            reservePrintCountOnRedChange: $("#reservePrintCountOnRedChange")[0].checked,
                            allowRepeatedBackBranch: $("#allowRepeatedBackBranch")[0].checked,
                                openTicketAccessSys: $("#openTicketAccessSys")[0].checked,
                                ticketAccessSysAccount: $("#ticketAccessSysAccount")[0].value,
                                ticketAccessSysPwd: $("#ticketAccessSysPwd")[0].value,
                                ticketAccessSysKey: $("#ticketAccessSysKey")[0].value,
                                ticketAccessSysLisense: $("#ticketAccessSysLisense")[0].value,
                                newSheetAfterApprove:$("#newSheetAfterApprove")[0].checked,
                                newSheetAfterSave:$("#newSheetAfterSave")[0].checked,
                                newCheckAccountShowLineAmountDetail:$("#newCheckAccountShowLineAmountDetail")[0].checked,
                            newSheetAfterSaleOrderSheetApprove: $("#newSheetAfterSaleOrderSheetApprove")[0].checked,
                            newSheetAfterSaleOrderSheetSave: $("#newSheetAfterSaleOrderSheetSave")[0].checked,
                                 appPriceBeforeChange:$("#appPriceBeforeChange")[0].checked,
                                appOpenBranchsForSheet: $("#appOpenBranchsForSheet")[0].checked,
                            saleSheetNeedSender: $("#saleSheetNeedSender")[0].checked,
                            saleOrderSheetNeedSender: $("#saleOrderSheetNeedSender")[0].checked,
                                checkAccountBySender: $("#checkAccountBySender")[0].checked,
                                saleSheetNoSameAsOrder: $("#saleSheetNoSameAsOrder")[0].checked,
                                 showSonItems: $("#showSonItems")[0].checked,
                                happenTimeOnSave : $("#happenTimeOnSave")[0].checked,
                                requerySheetsOnTabClick: $("#requerySheetsOnTabClick")[0].checked,
                              //  appUseVirtualProduceDate: $("#appUseVirtualProduceDate")[0].checked,
                                //useProduceDate: $("#useProduceDate")[0].checked,
                                loadVirtualProduceDate: $("#loadVirtualProduceDate")[0].checked,
                                appSaleUseSender: $("#appSaleUseSender")[0].checked,
                                appSaleUseSn: $("#appSaleUseSn")[0].checked,
                                clientBossNecessary: $("#clientBossNecessary")[0].checked,
                                supcustNoSerial: $("#supcustNoSerial")[0].checked,
                               clientMobileNecessary: $("#clientMobileNecessary")[0].checked,

                            doorPicNecessary: $("#doorPicNecessary")[0].checked,
                            clientLocationNecessary: $("#clientLocationNecessary")[0].checked,

                               clientGroupNecessary: $("#clientGroupNecessary")[0].checked,
                               clientLevelNecessary:$("#clientLevelNecessary")[0].checked,
                               clientRelateSeller: $("#clientRelateSeller")[0].checked,
                               dispTemplateNecessary: $("#dispTemplateNecessary")[0].checked,
                               unitPriceRelated:$("#unitPriceRelated")[0].checked,
                            
                                 isDistinctStock:$("#isDistinctStock")[0].checked,
                                 attrName: $("#attrName")[0].value,
                                  isCombinePrint:$("#isCombinePrint")[0].checked,
                            isRememberSonItemPrice: $("#isRememberSonItemPrice")[0].checked,
                            loadVirtualProduceDate: $("#loadVirtualProduceDate")[0].checked,
                              
                               // qrCodeExpiresTime: $("#qrCodeExpiresTime")[0].value,
                               //clientRegionNecessary: $("#clientRegionNecessary")[0].checked,
                               clientGroupNecessary: $("#clientGroupNecessary")[0].checked,
                                useAccounting: @(Model.useAccounting),
                                autoCreateVoucher:@(Model.autoCreateVoucher),
                            batchType: $("#batchType").val(),
                            exactItemNameSearch: $("#exactItemNameSearch")[0].checked,
                        };

                            $.ajaxSetup({ contentType: "application/json" });
                            $.post("/api/CompanySetting/Save", JSON.stringify(args)).then(result => {
                                console.log(result);      
                                if (result.result == "OK") {
                                    bw.toast("保存成功");
                                } else {
                                    bw.toast(result.msg);
                                }
                            });
                    },
                    'show-win'() {
                        alert('click show-win')
                    },

                }
            },
            '.': {
                click: {
                    btnQuery() {
                        app.Query();
                    },

                },

            },
            '': {
                click: {
                    'tbody'(e) {
                        var data = app.view(e.target);
                        debugger
                        $('#item_id').val(data.i);
                        $('#item_name').val(data.n);
                        $('#unit_no').val('/'+data.s_unit_no);
                        $('#dia').fadeOut(500);
                    },

                }
            }
        }).ready(function () {
            app.useDebug(true);


            var sel = $("#costPriceType")[0];
            var flag = $("#recentPriceTime")[0];



            app.useDialog({
                id: 'dialog',
                tmpl: 'dialogTmpl',
                caller:'.showDialogA',
                title: '重置' + sel.options[sel.selectedIndex].text,
                class: 'normal myClass',
                okTitle: "确定",
                beforeShow() {
                    var title = '调整'+sel.options[sel.selectedIndex].text;
                    $('#dialog #dialogTitle').text(title);
                    //$('#changePrice').show()
                    //if (sel.value !='3') $('#changePrice').hide()  //3--预设进价
                },
                okEvent(me) {
                    var data = {
                        item_id: $("#item_id").val(),
                        start_time: $("#jqxdatetimeStartTime").val(),
                        end_time: $("#jqxdatetimeEndTime").val(),
                        cost_price_type: sel.value,
                        recent_price_time: flag.value,
                        price: $("#buyPriceReset").val() || 0,
                        GetRowsCount: true,
                    };
                    var d = app.validator.verify('#dialog')

                    if (data.price) {

                    }

                    $.ajax({
                        url: '/api/CompanySetting/ResetCostPrice?operKey=@Model.operKey',
                        type: "post",
                        contentType: "application/json",
                        data: JSON.stringify(data),
                        success: function (res) {
                            if (res.result == 'OK') {
                                console.log(res);
                                bw.toast("重置成功");
                                me.Hide();
                            }
                            else bw.toast(res.msg, 3000)
                        },
                        error: function (err) {
                            console.log(err);
                            bw.toast("重置失败,请刷新重试");
                        }
                    })

                }
            });

            app.useDialog({
                id: 'dia',
                tmpl: 'popItem',
                caller: '.showDialogB',
                title: '选择商品',
                class: "normal popItem",
                beforeShow() {
                    var windowHeight = $(window).height();
                    var popupHeight = windowHeight * 0.8;
                    var gridHeight = windowHeight * 0.6;
                    // 设置弹窗和表格区域的高度,临时性方案
                    $('#dia .dialog_container.normal.popItem').css('height', popupHeight);
                    $('#dia .dialog_container.normal.popItem .dialog_body #grid').css('height', gridHeight);
                    $('#dia .dialog_container.normal.popItem .dialog_body #grid table tbody').css('height', gridHeight);
                    app.Query();
                },
                afterShow() {
                    app.grid.scrollBar.alignPreHeight(20);
                },
                okTitle: ""
            });
            app.useQueryBox('queryBox', {
                url: '/api/ItemsView/GetQueryRecords',
                args: {
                    gridID: 'gridItems',
                    startRow: 0,
                    endRow: 1000,
                    GetRowsCount: true,
                },
            });

            app.useGrid({
                buildTable(table) {
                    table.child('thead', {
                        cols: ['商品名称', '小条码', '批发价', '状态']
                    }).child('tbody', {
                        size: 16,
                        cols: ['itemName', 'barcode', 's_wholesale_price', 'status'],
                        class: "tableTd",
                        render: {
                            itemName(data, i) {
                                return `<label style="text-decoration:underline;cursor:pointer;" >${data.n}</label>`;

                            }
                            //input() {
                            //    return `<input class="magic-input"   type='checkbox'/>`
                            //}
                        }
                    })
                },
                '.': {
                    click: {
                        itemName() {
                            var data = app.view(this);
                            debugger
                            $('#item_id').val(data.i);
                            $('#item_name').val(data.n);
                            $('#unit_no').val('/'+data.s_unit_no);

                            $('#dia').fadeOut(500);
                        },

                    }
                }
            });

            app.useValidator({

            }).watch('#dialog form');
            $("#openTicketAccessSys").on("change", function (event) {
                const openTicketAccessSys = event.currentTarget.checked;
                if (!openTicketAccessSys) {
                    $("#ticketAccessSysForm").css("display", "none")
                } else {
                    $("#ticketAccessSysForm").css("display", "block")
                }
            })
            $("#limitVisitDistance").on("change", function (event) {
                const limitVisitDistance = event.currentTarget.checked;
                if (!limitVisitDistance) {
                    $("#visitDistances").css("display", "none")
                } else {
                    $("#visitDistances").css("display", "block")
                }
            })
            $("#autoVisitEnd").on("change", function (event) {
                const autoVisitEnd = event.currentTarget.checked;
                if (!autoVisitEnd) {
                    $("#autoVisitEndDistances").css("display", "none") 
                } else {
                    $("#autoVisitEndDistances").css("display", "block")
                }
            })
            $("#forceDisplayPic").on("change", function (event) {
                const forceDisplayPic = event.currentTarget.checked;
                if (!forceDisplayPic) {
                    $("#displayPicCounts").css("display", "none")
                } else {
                    $("#displayPicCounts").css("display", "block")
                }
            })
            $("#upload-official-seal").on('change',function (event) {
                    var fileDom = document.getElementById('upload-official-seal')
                    var showDom = document.getElementById("upload-official-seal-img")
                    previewImage(fileDom, showDom)
                  $('#upload-official-seal-img').removeAttr("hidden")
            });
            $("#upload-official-seal-img").on("click", function (event) {
                myPreview($("#upload-official-seal-img"))
            })
            $("#upload-official-seal-2").on('change', function (event) {
                var fileDom = document.getElementById('upload-official-seal-2')
                var showDom = document.getElementById("upload-official-seal-img-2")
                previewImage(fileDom, showDom)
                $('#upload-official-seal-img-2').removeAttr("hidden")
            });
            $("#upload-official-seal-img-2").on("click", function (event) {
                myPreview($("#upload-official-seal-img-2"))
            })
            $("#upload-official-head").on('change',function (event) {
                    var fileDom = document.getElementById('upload-official-head')
                    var showDom = document.getElementById("upload-official-head-img")
                    previewImage(fileDom, showDom)
                  $('#upload-official-head-img').removeAttr("hidden")
                });
            $("#upload-official-head-img").on("click", function (event) {
                myPreview($("#upload-official-head-img"))
            })
            $("#jqxdatetimeStartTime").jqxDateTimeInput({ culture: 'zh-CN', width: '150px', height: '25px', formatString: "yyyy-MM-dd HH:mm"});
            $("#jqxdatetimeEndTime").jqxDateTimeInput({ culture: 'zh-CN', width: '150px', height: '25px', formatString: "yyyy-MM-dd HH:mm" });
            var s = $('#jqxdatetimeEndTime').jqxDateTimeInput('val').toString();
            if (s != '') {
                s = s.replace('00:00', '23:59');
                $('#jqxdatetimeEndTime').jqxDateTimeInput('val', s);
            }

        });
        // document.addEventListener("DOMContentLoaded", function () {
        //     var isDistinctStock = @(Model.isDistinctStock == "true" ? "true" : "false");

        //     if (isDistinctStock) {
        //         var container = document.getElementById('needPriceRemember');
        //         container.classList.remove('hidden');
        //         container.classList.add('visible');
        //     } else {
        //         document.getElementById('needPriceRemember').classList.add('hidden');
        //     }
        // });
        function needRememberSonItemPriceCheckbox(checkbox) {
            var sonItemPriceCheckboxContainer = document.getElementById('needPriceRemember');
            if (checkbox.checked) {
                sonItemPriceCheckboxContainer.classList.remove('hidden');
                
            } else {
                sonItemPriceCheckboxContainer.classList.add('hidden');
                var rememberSonItemPriceCheckbox = document.getElementById('isRememberSonItemPrice');
                rememberSonItemPriceCheckbox.checked = false;
            }
        }

        document.addEventListener("DOMContentLoaded", function () {
            var isDistinctStockCheckbox = document.getElementById('isDistinctStock');
            needRememberSonItemPriceCheckbox(isDistinctStockCheckbox);
        });
        function myPreview(clickDom) {
            var url = clickDom.attr("src")
            $('#preview-image').jqxWindow({
                width: "600px",
                height: "600px",
                resizable: true,
                draggable: true,
                showCloseButton: true,
                autoOpen: false,
                zIndex: 9999,
            })

            $("#preview-image").jqxWindow('setContent', `
                                <img style='width:100%;' src='${url}'/>
                            `);
            $('#preview-image').jqxWindow('open');


        }
        function visitDistanceTypeIsValid(distance) {
            if (distance !== '' && isNaN(Number(distance))) {
                return false
            }
            return true
        };
        function previewImage(uploadDocument,showDocument) {
            var reads = new FileReader();
            var file = uploadDocument.files[0];
            console.log(file)
            reads.readAsDataURL(file)
            reads.onload = function (e) {
                console.log(e)
                showDocument.src = e.currentTarget.result
               
            }
        }


        function checkInputTimeIsValid(timeStr) {
            if (timeStr.indexOf(':') === -1) {
                return false
            }
            const hourNum = Number(timeStr.split(':')[0])
            const minuteNum = Number(timeStr.split(':')[1])
            if (isNaN(hourNum) || hourNum < 0 || hourNum > 24) {
                return false
            }
            if (isNaN(minuteNum) || minuteNum < 0 || minuteNum > 59) {
                return false
            }
            return true
        }
    </script>
</body>
</html>
