﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using HuaWeiObsController;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using static ArtisanManage.Models.DataItem;

namespace ArtisanManage.Pages.BaseInfo
{
    public class GetArrearsSheetViewModel : PageQueryModel
    { 
        public GetArrearsSheetViewModel(CMySbCommand cmd) : base(Services.MenuId.sheetGetArrears)
        {
            this.cmd = cmd;
            cmd.ActiveDatabase = "";
            CanQueryByApproveTime = true;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="am.happen_time", CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期", FldArea="divHead",CtrlType="jqxDateTimeInput", SqlFld="am.happen_time",   CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"depart_path",new DataItem(){Title="部门",Hidden=true, FldArea="divHead",LabelFld="depart_path_label",TreePathFld="path", CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=true,DropDownWidth="150", CompareOperator="like",LikeWrapper="/",
                    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"
                }},
           {"other_region",new DataItem(){Title="片区",MumSelectable=true, FldArea="divHead",LabelFld="region_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MaxRecords="500", TreePathFld="region_path",CompareOperator="like",
                    SqlForOptions="select region_id as v,region_name as l,mother_id as pv from info_region order by order_index , region_id",
                    DealQueryItem = (value) =>
                    {
                        return "/"+value+"/";
                    }
                }},
           
                {"sheet_type",new DataItem(){FldArea="divHead",Title="单据类型",LabelFld="sheet_type_name",ButtonUsage="list",Source = "[{v:'SK',l:'收款单'},{v:'FK',l:'付款单'},{v:'',l:'所有'}]",Value="SK",Label="收款单", CompareOperator="="}},
                {"supcust_id",new DataItem(){FldArea="divHead",Title="客户名称",LabelFld="sup_name",ButtonUsage="list",SqlFld="am.supcust_id",CompareOperator="=",
                    SqlForOptions=CommonTool.selectSupcust,Checkboxes = true } },
                {"seller_id",new DataItem(){FldArea="divHead",Title="业务员",LabelFld="seller_name",SqlFld="seller.oper_id",ButtonUsage="list",SqlForOptions=CommonTool.selectSellers,CompareOperator="=",Checkboxes = true}},//SqlForOptions="select oper_id as v,oper_name as l,py_str as z from info_operator"
                {"maker_id",new DataItem(){FldArea="divHead",Title="制单人",LabelFld="maker_name",SqlFld="am.maker_id",ButtonUsage="list",Hidden=true, SqlForOptions=CommonTool.selectGetters,CompareOperator="="}},
                {"status",new DataItem(){FldArea="divHead",Title="单据状态",LabelFld = "status_name",ButtonUsage = "list",CompareOperator="=",Value="normal",Label="正常单据",
                        Source = @"[{v:'normal',l:'正常单据',condition:""am.red_flag is null""},
                                   {v:'unapproved',l:'未审核单据',condition:""am.approve_time is null""},
                                   {v:'approved',l:'已审核单据',condition:""am.approve_time is not null and red_flag is null""},
                                   {v:'toReview',l:'待复核单据',condition:""am.approve_time is not null and red_flag is null and am.review_time is null""},
                                   {v:'reviewed',l:'已复核单据',condition:""am.review_time is not null and red_flag is null""},
                                   {v:'red',l:'红冲单',condition:""am.red_flag in ('1','2') ""},
                                   {v:'all',l:'所有',condition:""true""}]"
                }},
                {"sheet_no",new DataItem(){FldArea="divHead",Title="单号", CompareOperator="like"}},
                {"make_brief",new DataItem(){FldArea="divHead",Title="整单备注", CompareOperator="like"}},
                {"byHappenTime",new DataItem(){FldArea="divHead",Title="按交易时间查询",CtrlType="jqxCheckBox",ForQuery=false}}
            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true,
                     IdColumn="sheet_id",
                     HasCheck=true,
                     Sortable=true,
                     PageByOverAgg=false,
                     ColumnsHeight=15,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"sheet_id",   new DataItem(){Title="sheet_id", Hidden=true, Linkable=true, Width="80",HideOnLoad = true}},
                       {"sheet_no",   new DataItem(){Title="单据编号",  Linkable=true, Width="150"}},
                       {"sheet_type", new DataItem(){Title="单据类型",  Width="80",SqlFld="(case WHEN am.sheet_type='SK' THEN '收款单' ELSE '付款单' END)"}},
                       {"sheet_status", new DataItem(){Title="状态",  Width="80",SqlFld="(case when am.red_flag='2' then '红字单' when am.red_flag='1' then '已红冲' when am.approve_time is null then '未审' when am.review_time is null then '已审'  else '已复核' END)"}},
                       {"sup_name",   new DataItem(){Title="结算单位",   Width="150"}},
                       {"supcust_no",   new DataItem(){Title="客户编号",  Width="100", Hidden=true}},
                       {"happen_time",   new DataItem(){Title="收款时间",  Width="150"}},
                       {"approve_time",   new DataItem(){Title="审核时间",  Width="150"}},

                       {"seller_name", new DataItem(){Title="收款人",  Width="80"}},
                       {"left_amount", new DataItem(){Title="收款前本单内欠款",  Width="80",SqlFld="am.left_amount+am.now_pay_amount+am.now_disc_amount"}},
                       {"now_disc_amount", new DataItem(){Title="本次优惠",  Width="80",ShowSum=true}},
					   {"now_pay_amount", new DataItem(){Title="本次收款",  Width="80",ShowSum=true}},
					   {"pay_ways",  new DataItem(){Title="收款账户", Width="100",
                           FuncGetSubColumns = async (col) =>
                           {
                                ColumnsResult result=new ColumnsResult();
                                Dictionary<string,DataItem> subColumns=new Dictionary<string,DataItem>();
                                List<System.Dynamic.ExpandoObject> payways =  await CDbDealer.GetRecordsFromSQLAsync($"select sub_id,sub_name from cw_subject where company_id={company_id} and sub_type in ('QT','YS') order by sub_code::text;",cmd);
                                string flds="";
                                foreach(dynamic pw in payways)
                                {
                                   var subcol=new DataItem();
                                   subcol.Width=col.Width;
                                   subcol.CellsAlign="right";
                                   subcol.ShowSum=true;
                                   subcol.Title=pw.sub_name;
                                   string colKey="pw_"+ (string)pw.sub_id;
                                   subColumns.Add(colKey, subcol);
                                   string fld=$"  ((case when payway1_id='{pw.sub_id}' then coalesce(payway1_amount,0) else 0 end) + (case when payway2_id='{pw.sub_id}' then coalesce(payway2_amount,0) else 0 end) + (case when payway3_id='{pw.sub_id}' then coalesce(payway3_amount,0) else 0 end))*money_inout_flag  as {colKey}";
                                   if(flds!="") flds+=",";
                                   flds+=fld;
                                }
                                result.FldsSQL=flds;
                                result.Columns=subColumns;
                                return result;
                           }
                       }} ,
                       {"arrears", new DataItem(){Title="收款后本单内欠款",  Width="80",SqlFld="left_amount"}},
                       {"check_appendix_photos", new DataItem(){Title="附件", Width="100",SqlFld="case when sheet_attribute->>'appendixPhotos' is not null and sheet_attribute->>'appendixPhotos' <> '[]'  then '查看' end",Linkable=true,}},
                       { "appendix_photos",new DataItem(){Title="照片对象",SqlFld="sheet_attribute->>'appendixPhotos'", Hidden=true,HideOnLoad=true} },
                       {"make_brief", new DataItem(){Title="备注", Width="100"}},
                     },
                     QueryFromSQL=@"
from sheet_get_arrears_main am 
LEFT JOIN info_supcust s on am.supcust_id = s.supcust_id and s.company_id = ~COMPANY_ID
LEFT JOIN (select sub_id,sub_name as payway1_name from cw_subject where company_id= ~COMPANY_ID) pw1 on am.payway1_id = pw1.sub_id
LEFT JOIN (select sub_id,sub_name as payway2_name from cw_subject where company_id= ~COMPANY_ID) pw2 on am.payway2_id = pw2.sub_id
LEFT JOIN (select sub_id,sub_name as payway2_name from cw_subject where company_id= ~COMPANY_ID) pw3 on am.payway3_id = pw3.sub_id
left join (select region_id,region_name region1_name from info_region where company_id= ~COMPANY_ID) r1 on split_part(s.other_region,'/',3) = r1.region_id::text
left join (select region_id,region_name region2_name from info_region where company_id= ~COMPANY_ID) r2 on split_part(s.other_region,'/',4) = r2.region_id::text
left join (select region_id,region_name region3_name from info_region where company_id= ~COMPANY_ID) r3 on split_part(s.other_region,'/',5) = r3.region_id::text
LEFT JOIN (select oper_id from info_operator where company_id= ~COMPANY_ID) maker on am.maker_id = maker.oper_id
LEFT JOIN (select oper_id,oper_name as seller_name, depart_path from info_operator where company_id= ~COMPANY_ID) seller on am.getter_id = seller.oper_id where am.company_id= ~COMPANY_ID ~SQL_VARIABLE1",
                     
                     QueryOrderSQL=" order by happen_time desc"
                  }
                } 
            }; 
        }
        public async Task OnGet()
        {
            string forPayOrGet = CPubVars.RequestV(Request, "forPayOrGet");
            if (forPayOrGet!=null && forPayOrGet.ToLower() == "true")
            {
                DataItems["sheet_type"].Value = "FK";
                DataItems["sheet_type"].Label = "付款单";
                DataItems["supcust_id"].Title = "供应商";
                DataItems["supcust_id"].SqlForOptions=DataItems["supcust_id"].SqlForOptions.Replace("'C'", "'S'");
                DataItems["supcust_id"].OtherQueryStrForOptions = "sheet_type=FK";
                 var gridItems = Grids["gridItems"].Columns;
                gridItems["happen_time"].Title = "付款时间";
                gridItems["seller_name"].Title = "付款人";
                gridItems["left_amount"].Title = "付款前欠款";
                gridItems["pay_ways"].Title = "付款账户";
                gridItems["pay_ways"].FuncGetSubColumns = async (col) =>
                {
                    ColumnsResult result = new ColumnsResult();
                    Dictionary<string, DataItem> subColumns = new Dictionary<string, DataItem>();
                    List<System.Dynamic.ExpandoObject> payways = await CDbDealer.GetRecordsFromSQLAsync($"select sub_id,sub_name from cw_subject where company_id={company_id} and sub_type in ('QT','YF') order by sub_code::text;", cmd);
                    string flds = "";
                    foreach (dynamic pw in payways)
                    {
                        var subcol = new DataItem();
                        subcol.Width = col.Width;
                        subcol.CellsAlign = "right";
                        subcol.ShowSum = true;
                        subcol.Title = pw.sub_name;
                        string colKey = "pw_" + (string)pw.sub_id;
                        subColumns.Add(colKey, subcol);
                        string fld = $"  ((case when payway1_id='{pw.sub_id}' then coalesce(payway1_amount,0) else 0 end) + (case when payway2_id='{pw.sub_id}' then coalesce(payway2_amount,0) else 0 end) + (case when payway3_id='{pw.sub_id}' then coalesce(payway3_amount,0) else 0 end))*money_inout_flag  as {colKey}";
                        if (flds != "") flds += ",";
                        flds += fld;
                    }
                    result.FldsSQL = flds;
                    result.Columns = subColumns;
                    return result;
                };

            }
           
            await InitGet(cmd);
           
            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }
    }



    [Route("api/[controller]/[action]")]
    public class GetArrearsSheetViewController : QueryController
    { 
        public GetArrearsSheetViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value,string sheet_type,string availValues)
        {
            GetArrearsSheetViewModel model = new GetArrearsSheetViewModel(cmd);
            if (sheet_type == "FK")
            {
                model.DataItems["supcust_id"].SqlForOptions = model.DataItems["supcust_id"].SqlForOptions.Replace("'C'", "'S'");
            }
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }

        [HttpGet]
        public async Task<object> GetQueryRecords(string operKey, string sheet_type)
        {
            GetArrearsSheetViewModel model = new GetArrearsSheetViewModel(cmd);
            Security.GetInfoFromOperKey(operKey, out string companyID);
            if (sheet_type == "FK")
            {
                var gridItem = model.Grids["gridItems"].Columns;
                gridItem["pay_ways"].FuncGetSubColumns = async (col) =>
                {
                    ColumnsResult result = new ColumnsResult();
                    Dictionary<string, DataItem> subColumns = new Dictionary<string, DataItem>();
                    List<System.Dynamic.ExpandoObject> payways = await CDbDealer.GetRecordsFromSQLAsync($"select sub_id,sub_name from cw_subject where company_id={companyID} and sub_type in ('QT','YF') order by sub_code::text;", cmd);
                    string flds = "";
                    foreach (dynamic pw in payways)
                    {
                        var subcol = new DataItem();
                        subcol.Width = col.Width;
                        subcol.CellsAlign = "right";
                        subcol.ShowSum = true;
                        subcol.Title = pw.sub_name;
                        string colKey = "pw_" + (string)pw.sub_id;
                        subColumns.Add(colKey, subcol);
                        string fld = $"  ((case when payway1_id='{pw.sub_id}' then coalesce(payway1_amount,0) else 0 end) + (case when payway2_id='{pw.sub_id}' then coalesce(payway2_amount,0) else 0 end) + (case when payway3_id='{pw.sub_id}' then coalesce(payway3_amount,0) else 0 end))*money_inout_flag  as {colKey}";
                        if (flds != "") flds += ",";
                        flds += fld;
                    }
                    result.FldsSQL = flds;
                    result.Columns = subColumns;
                    return result;
                };
            }
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            GetArrearsSheetViewModel model = new GetArrearsSheetViewModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }
        [HttpGet]
        public async Task<JsonResult> GetSheetsToApprove(string operKey, string sheetIDs)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);

            string msg = "";
            string[] sheetID = sheetIDs.Split(',');

            foreach (var ID in sheetID)
            {
                SheetGetArrears sheet = new SheetGetArrears(LOAD_PURPOSE.SHOW);
                await sheet.Load(cmd, companyID, ID);
                sheet.OperID = operID;
                sheet.OperKey = operKey;
                string aa = await sheet.SaveAndApprove(cmd);
                if (aa != "")
                {
                    msg += sheet.sheet_no + aa;
                }
            }

            if (msg != "")
            {
                return Json(new { result = $"{msg}" });
            }
            else
            {
                return Json(new { result = "OK" });
            }
        }
        [HttpGet]
        public async Task<JsonResult> GetSheetsToReview(string operKey, string sheetIDs)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            string now = CPubVars.GetDateText(DateTime.Now);
            string msg = "";
            string[] sheetID = sheetIDs.Split(',');

            foreach (var ID in sheetID)
            {
                SheetGetArrears sheet = new SheetGetArrears(LOAD_PURPOSE.SHOW);
                await sheet.Load(cmd, companyID, ID);
                sheet.OperID = operID;
                sheet.OperKey = operKey;
                dynamic rec = await CDbDealer.Get1RecordFromSQLAsync($"select approve_time,red_flag from sheet_get_arrears_main where company_id={companyID} and sheet_id={ID};", cmd);
                if (rec == null)
                {
                    msg += sheet.sheet_no + "单据不存在";
                }
                else if (rec.red_flag != "")
                {
                    msg += sheet.sheet_no + "被红冲单据不能复核";
                }
                else if (rec.approve_time == "")
                {
                    msg += sheet.sheet_no + "单据未审核不能复核";
                }
                else
                {
                    cmd.CommandText = $"update sheet_get_arrears_main set review_time ='{now}',reviewer_id={operID} where company_id={companyID} and sheet_id={ID};";
                    await cmd.ExecuteNonQueryAsync();
                }
            }

            if (msg != "")
            {
                return Json(new { result = $"{msg}" });
            }
            else
            {
                return Json(new { result = "OK" });
            }
        }

        [HttpGet]
        public async Task<JsonResult> GetMultiSheetsToPrint(string operKey, string sheetIDs, bool bPrintSum, bool bPrintEach, string clientVersion, string sortColumn, string sortDirection)
        {
            try
            {
                Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);

                // 创建收款单实例
                SheetGetArrears sheet = new SheetGetArrears(SHEET_GET_ARREARS.IS_GET, LOAD_PURPOSE.SHOW);

                // 加载多个单据
                List<SheetGetArrears> sheets = await sheet.LoadMultiSheets<SheetGetArrears>(cmd, companyID, sheetIDs, sortColumn, sortDirection);

                // 为打印加载额外信息
                foreach (var sht in sheets)
                {
                    await sht.LoadInfoForPrint(cmd, false, true);
                }

                // 构建返回结果
                var sheetGroup = new List<dynamic>();

                if (bPrintEach)
                {
                    var eachGroup = new
                    {
                        groupType = "EACH",
                        sheets = sheets.Cast<dynamic>().ToList(),
                        template = (object)null
                    };
                    sheetGroup.Add(eachGroup);
                }

                var result = new
                {
                    result = "OK",
                    msg = "",
                    sheetGroup = sheetGroup,
                    sheetIDs = sheetIDs,
                    templVariables = new { }
                };

                return new JsonResult(result);
            }
            catch (Exception ex)
            {
                return new JsonResult(new { result = "Error", msg = ex.Message });
            }
        }


    }
}
