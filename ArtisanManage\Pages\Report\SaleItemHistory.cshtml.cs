using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.POIFS.Crypt.Dsig;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using NPOI.SS.Formula.Functions;
using NPOI.XSSF.Streaming.Values;
using static System.Runtime.InteropServices.JavaScript.JSType;
using ArtisanManage.AppController;
namespace ArtisanManage.Pages.BaseInfo
{
    public class SaleItemHistoryModel : PageQueryModel
    {
        public SaleItemHistoryModel(CMySbCommand cmd) : base(Services.MenuId.salesDetaill)
        {
            this.UsePostMethod = true;
            this.EnableBigDataMode = true;
            this.cmd = cmd;
            this.PageTitle = "销售明细表";
            this.NotQueryHideColumn = false;
            this.CoalesceDataItems = new List<string> { "depart_id", "depart_id,supcust_id" };
            this.CanQueryByApproveTime = false;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead",SqlFld="sd.happen_time", CompareOperator=">=", CtrlType="jqxDateTimeInput",ForQuery=true, Value=CPubVars.GetDateText(DateTime.Now.Date.AddMonths(-12))+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead",SqlFld="sd.happen_time", CompareOperator="<=",CtrlType="jqxDateTimeInput",ForQuery=true, Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"queryTimeAccord",new DataItem(){
                    FldArea="divHead", Title="时间类型",Hidden=true, LabelFld = "time_status_name", ButtonUsage = "list",
                    CompareOperator="=",Value="byHappenTime",Label="交易时间",ForQuery=false, AutoRemember=true,
                    Source = @"[
                                {v:'byHappenTime',l:'交易时间'},
                                {v:'byMakeTime',l:'制单时间'},
                                {v:'byApproveTime',l:'审核时间'},
                                {v:'byCheckedTime',l:'交账时间'},
                                {v:'bySendTime',l:'送货时间'},
                                {v:'byClearArrearsTime',l:'欠款结清时间'}
                               ]"
                }},
                {"forItemHistory",new DataItem(){FldArea="divHead",Title="forItemHistory",CtrlType="jqxCheckBox",ForQuery=false,Hidden=true,HideOnLoad=true,CONDI_DATA_ITEM="sheet_type"}},
                
            
	             {"brand_id", CommonTool.GetDataItem("brand_id", new DataItemChange(){SqlFld="ip.item_brand"})},

				{"group_id",new DataItem(){SqlAreaToPlace="",Title="渠道",Checkboxes=true, Hidden=true,FldArea="divHead",LabelFld="group_name",ButtonUsage="list",CompareOperator="=",SqlFld="sc.sup_group",
                    SqlForOptions = CommonTool.selectGroups}},
                {"item_id",new DataItem(){SqlAreaToPlace="",Title="商品名称",FldArea="divHead",Hidden=true,LabelFld="item_name",ButtonUsage="event",CompareOperator="=",SqlFld="sd.item_id",DropDownWidth="300",QueryByLabelLikeIfIdEmpty=true,
                   SearchFields=CommonTool.itemSearchFields,
                SqlForOptions =CommonTool.selectItemWithBarcode  }},
                {"supcust_id",new DataItem(){SqlAreaToPlace="", FldArea="divHead",Title="客户",LabelFld="sup_name", Checkboxes=true,ButtonUsage="event",QueryByLabelLikeIfIdEmpty=true, CompareOperator="=",SqlFld="sm.supcust_id",
                    SqlForOptions="select supcust_id as v,sup_name as l,py_str as z from info_supcust where supcust_flag like '%C%' and company_id=~COMPANY_ID "}},
                {"acct_cust_id",new DataItem(){SqlAreaToPlace="MAIN", Hidden=true, FldArea="divHead",Title="结算单位",LabelFld="acct_name", Checkboxes=true,ButtonUsage="event",QueryByLabelLikeIfIdEmpty=false, CompareOperator="=",SqlFld="acct_cust_id",
                    SqlForOptions="select supcust_id as v,sup_name as l,py_str as z from info_supcust where supcust_flag like '%C%' and company_id=~COMPANY_ID "}},
                 
               
                {"depart_id",new DataItem(){SqlAreaToPlace="MAIN",Title="部门",Hidden=true, FldArea="divHead",LabelFld="depart_path_label",SqlFld="depart_id", CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=true,DropDownWidth="150", CompareOperator="=",
                    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"
                }},
                 {"department_id",new DataItem(){SqlAreaToPlace="MAIN",Title="所属部门",Hidden=true,TreePathFld="department_path", FldArea="divHead",LabelFld="department_id_label", CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=false,DropDownWidth="150", CompareOperator="=",
                    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"
                }},
                {"seller_id",new DataItem(){SqlAreaToPlace="MAIN",Title="业务员",Checkboxes=true, Hidden=true, FldArea="divHead",LabelFld="seller_name",ButtonUsage="list",CompareOperator="=",SqlFld="sm.seller_id",
                    SqlForOptions ="select oper_id as v,oper_name as l from info_operator where company_id=~COMPANY_ID and is_seller and (status=1 or status is null)"}},
                //{"branch_id",new DataItem(){SqlAreaToPlace="MAIN",Title="仓库",  FldArea="divHead",LabelFld="branch_name",Checkboxes=true,ButtonUsage="list",CompareOperator="=",SqlFld="sm.branch_id",
                //    SqlForOptions ="select branch_id as v,branch_name as l from info_branch"}},
                {"branch_id",new DataItem(){Title="仓库",FldArea = "divHead",LabelFld="branch_name",Hidden=true,Checkboxes=true, ForQuery=false,ButtonUsage="list",CompareOperator="=",
                    SqlForOptions=CommonTool.selectBranch } },

                {"branch_position",new DataItem(){Title="库位",FldArea="divHead",LabelFld="branch_position_name",Hidden=true,Checkboxes=true,ForQuery=false,ButtonUsage="list",CompareOperator="=",
                    SqlForOptions=CommonTool.selectBranchPosition }},
                {"produce_date",new DataItem(){Title="生产日期",FldArea="divHead",ForQuery=false,Hidden=true, CtrlType="jqxDateTimeInput",ShowTime=false}},
                {"batch_no",new DataItem(){Title="批次",FldArea="divHead",ForQuery=false,Hidden=true}},
                //{"group_id",new DataItem(){Title="渠道",Checkboxes=true, FldArea="divHead",LabelFld="branch_name",ButtonUsage="list",Hidden=false,CompareOperator="=",SqlFld="sc.sup_group",
                    //SqlForOptions ="select group_id as v,group_name as l from info_supcust_group where company_id=~COMPANY_ID"}},

                {"make_brief",new DataItem(){FldArea="divHead",SqlFld="sm.make_brief",SqlAreaToPlace="",Title="整单备注",CompareOperator="like"} },
                {"remark",new DataItem(){SqlAreaToPlace="",Title="行备注",FldArea="divHead",EmptyConditionValue="-1",LabelFld="remark_name",ButtonUsage="list",ForQuery=false,CompareOperator="like",Checkboxes=true,SqlFld="remark||''",
                    SqlForOptions ="select -1 as v,'无备注' as l union select brief_id as v,brief_text as l from info_sheet_detail_brief where sheet_type='X' order by v"}},
              
                {"sale_way",new DataItem(){SqlAreaToPlace="MAIN", FldArea="divHead",  Title="销售方式",LabelFld="sale_way_label", ButtonUsage = "list" ,Hidden=true, CompareOperator="=",Value="",Label="",
                        Source = @"[{v:'all',l:'所有',condition:""1=1""},
                                   {v:'directSale',l:'车销',condition:""order_sheet_id is null""},
                                   {v:'byOrder',l:'访销',condition:""order_sheet_id is not null""}]"

                }},
              {"cost_price_type",new DataItem(){FldArea="divHead",Title="成本核算",Hidden=true,ForQuery=false,  LabelFld="cost_price_type_name",ButtonUsage="list",Source = "[{v:'2',l:'加权平均成本'},{v:'3',l:'预设进价'},{v:'1',l:'预设成本'},{v:'4',l:'最近平均进价'}]", CompareOperator="=" }},
               
                 {"sheetType",new DataItem(){Title="",FldArea="divHead",Hidden=true,ForQuery=false,HideOnLoad = true} },
                {"sys_hide_duplicate_cells",new DataItem(){
                    FldArea="divHead", Title="主表展示1行", LabelFld = "", CtrlType="jqxCheckBox",Hidden=true,
                     Value="false",Label="",ForQuery=false, AutoRemember=true

                }},
                {"status",new DataItem(){FldArea="divHead", SqlAreaToPlace="MAIN",Title="单据状态",Hidden=true,Checkboxes=true,ButtonUsage = "list",CompareOperator="=",Value="approved",Label="已审核",
                        Source = @"[{v:'normal',l:'所有',condition:""sm.red_flag is null""},
                                   {v:'unapproved',l:'未审核',condition:""sm.approve_time is null""},
                                   {v:'approved',l:'已审核',condition:""sm.approve_time is not null and sm.red_flag is null""}]"

                }},
                {"price_filter_type",new DataItem(){FldArea="divHead",Hidden=true,Title="单价＜",ForQuery=false,LabelFld="price_filter_type_name",ButtonUsage="list",Source = "[{v:'belowLastSellingPrice',l:'上次售价'},{v:'belowWholesalePrice',l:'批发价'}]", CompareOperator="<" }},
              
             
                //{"detail_table",new DataItem(){Title="",FldArea="divHead",Hidden=true,ForQuery=false,} }
            
            };

            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                      ShowAggregates = true,
                      Sortable=true,
                      PageByOverAgg=false,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"sheet_id",new DataItem(){IsIDColumn = true,SqlFld = "sheet_id",Hidden=true,HideOnLoad = true}},
                       {"row_index",new DataItem(){IsIDColumn = false,SqlFld = "row_index",Hidden=true,HideOnLoad = true}},
                     //  {"placeholder_sheet_id",new DataItem(){SqlFld="placeholder_sheet_id",Hidden=true,HideOnLoad = true}},
                       {"sheet_no",     new DataItem(){MightDuplicate=true,HideDuplicateCells=true, Title="单据编号", Sortable=true,    Width="100",SqlFld="sheet_no" ,Linkable = true}},
                     //  {"placeholder_sheet_no",     new DataItem(){Title="占位单编号",Hidden=true,HideOnLoad = true, Sortable=true,    Width="100",SqlFld="placeholder_sheet_no" ,Linkable = true}},
                       {"sheet_type1",   new DataItem(){MightDuplicate=true,HideDuplicateCells=true, Title="单据类型",     Width="80",SqlFld="(case WHEN sheet_type='X' THEN '销售单' when sheet_type='T' then '退货单' when sheet_type='XD' then '销售订单' when sheet_type='TD' then '退货订单' END)"}},
                       {"sheet_type",   new DataItem(){MightDuplicate=true,HideDuplicateCells=true, Title="单据类型", Hidden=true,    Width="80",SqlFld="",HideOnLoad = true}},
                      // {"make_time",     new DataItem(){MightDuplicate=true,HideDuplicateCells=true, Title="制单时间", Sortable=true, Width="150",SqlFld="make_time"}},
                       {"happen_time",     new DataItem(){MightDuplicate=true,HideDuplicateCells=true, Title="交易时间", Sortable=true, Width="150",SqlFld="happen_time"}},
                    //   {"approve_time",     new DataItem(){MightDuplicate=true,HideDuplicateCells=true, Title="审核时间", Sortable=true, Width="150",SqlFld="approve_time"}},
                       {"sup_name",     new DataItem(){MightDuplicate=true,HideDuplicateCells=true, Title="客户", Sortable=true,   Hidden=true,    Width="150",SqlFld="sup_name"}},
                      // {"mobile",     new DataItem(){MightDuplicate=true,HideDuplicateCells=true, Title="客户电话",  Sortable=true,       Width="90",SqlFld="mobile",Hidden = true}},
                      // {"sup_addr",     new DataItem(){MightDuplicate=true,HideDuplicateCells=true, Title="客户地址",         Width="150",SqlFld="sup_addr",Hidden = true}},
                      // {"boss_name",     new DataItem(){MightDuplicate=true,HideDuplicateCells=true, Title="老板姓名",         Width="150",SqlFld="boss_name",Hidden = true}},
                       {"trade_type", new DataItem(){Title="交易类型",Hidden=true, Width="70",SqlFld="case when sheet_type='T' then '退货' else case coalesce(trade_type,'X') when 'J' then '借货' when 'H' then '还货' when 'DH' then '定货会' when 'CL' then '陈列' when 'HR' then '换入' when 'HC' then '换出' when 'T' then '退货' else '销售' end end"}},
                      
                       //{"supcust_no",     new DataItem(){Title="客户编号", Sortable=true,       Width="100",Hidden=true}},
					  // {"region_name",     new DataItem(){MightDuplicate=true,HideDuplicateCells=true, Title="片区",      Width="100",SqlFld="region_name",Hidden = true}},
                      // {"oper_name",    new DataItem(){MightDuplicate=true,HideDuplicateCells=true, Title="业务员",     Width="70",SqlFld="oper_name"}},
                     //       {"total_amount",    new DataItem(){MightDuplicate=true, HideDuplicateCells=true,  ShowSum=true, Title="总额",SqlFld="total_amount * money_inout_flag" , CellsAlign="right",Sortable=true, Width="70", Hidden=true}},
                     //  {"paid_amount",    new DataItem(){MightDuplicate=true, HideDuplicateCells=true, ShowSum=true, Title="已收金额", SqlFld="paid_amount * money_inout_flag" , CellsAlign="right",Sortable=true, Width="70",Hidden=true}},
                      // {"disc_amount",    new DataItem(){ MightDuplicate=true, HideDuplicateCells=true, ShowSum=true, Title="已惠金额", SqlFld="disc_amount::numeric * money_inout_flag" , CellsAlign="right",Sortable=true, Width="70",Hidden=true}},
                      // {"left_amount",    new DataItem(){MightDuplicate=true, HideDuplicateCells=true, ShowSum=true, Title="尚欠金额", SqlFld="(total_amount -paid_amount - disc_amount) * money_inout_flag" ,CellsAlign="right", Sortable=true, Width="70",Hidden=true}},
                      // {"now_pay_amount",    new DataItem(){MightDuplicate=true, HideDuplicateCells=true, ShowSum=true, Title="本单收款", SqlFld="now_pay_amount * money_inout_flag" ,CellsAlign="right", Sortable=true, Width="70",Hidden=true}},
                      // {"now_disc_amount",    new DataItem(){MightDuplicate=true, HideDuplicateCells=true, ShowSum=true, Title="本单优惠", SqlFld="now_disc_amount::numeric * money_inout_flag" ,CellsAlign="right", Sortable=true, Width="70",Hidden=true}},
                      // {"now_left_amount",    new DataItem(){ MightDuplicate=true, HideDuplicateCells=true,ShowSum=true, Title="本单欠款", SqlFld="(total_amount -now_pay_amount - now_disc_amount) * money_inout_flag" ,CellsAlign="right", Sortable=true, Width="70",Hidden=true}},
                      // {"later_paid_amount",    new DataItem(){MightDuplicate=true, HideDuplicateCells=true, ShowSum=true, Title="后续收款", SqlFld="(paid_amount -now_pay_amount) * money_inout_flag" ,CellsAlign="right", Sortable=true, Width="70",Hidden=true}},

                       {"item_name",    new DataItem(){Title="商品名称",  Sortable=true, Width="200",SqlFld="item_name", Hidden=true}},

                       
                      // {"unit_conv",    new DataItem(){Title="单位换算",    Width="70"}},
                      // {"item_class",    new DataItem(){Title="类别",    Width="70",SqlFld="other_class_name",Hidden = true}},
                    //   {"brand_name",    new DataItem(){Title="品牌",  Sortable=true, Width="200",SqlFld="brand_name"}},
                      // {"barcode",new DataItem(){Title = "条码",Width="90" ,SqlFld="itu.barcode",Hidden=true} },
                      // {"s_barcode",new DataItem(){Title = "条码(小)",Width="90"} },
                      // {"m_barcode",new DataItem(){Title = "条码(中)",Width="90",Hidden=true} },
                      // {"b_barcode",new DataItem(){Title = "条码(大)",Width="90",Hidden=true} },
                       /*{"qty_no_unit",     new DataItem() {Title="纯数量", Width="70",Hidden=true,ShowSum=true,

                        SqlFld=" sd.quantity * sd.inout_flag *(-1) ",CellsAlign="right",
                        FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},*/
                       {"unit_no",     new DataItem() {Title="单位",Hidden=true, Width="70",SqlFld=" unit_no ",CellsAlign="right",}},
                       {"quantity",     new DataItem() {Title="数量", Sortable=true, Width="70",
                           SqlFld="concat((case when sheet_type in ('X','XD') then quantity else -quantity end),unit_no)",CellsAlign="right",
                           FuncDealMe=(value)=>{return value=="0"?"":value; },
                           /*FuncGetSumValue = (sumColumnValues) =>
                           {
                               string sQty ="";
                               if(sumColumnValues["quantity_b"]!="") sQty+= sumColumnValues["quantity_b"]+"大";
                               if(sumColumnValues["quantity_m"]!="") sQty+= sumColumnValues["quantity_m"]+"中";
                               if(sumColumnValues["quantity_s"]!="") sQty+= sumColumnValues["quantity_s"]+"小";
                               return sQty;
                           }*/
                       }},

                     // {"unit_type",   new DataItem(){Title="大小", Hidden=true,HideOnLoad = true,Sortable=true,CellsAlign="right", Width="80",SqlFld="(case when itu.unit_type ='s'  then '小' else (case when itu.unit_type ='b' then '大' else '中' end )  end)"}},
                  
                      
                       //{"type",   new DataItem(){Title="大小", Hidden=true,HideOnLoad = true,Sortable=true,CellsAlign="right", Width="7%",SqlFld="(case when itu.unit_type ='s'  then '小' else (case when itu.unit_type ='b' then '中' else '大' end )  end)"}},
                        {"orig_price", new DataItem(){Title="原价", Sortable=true, CellsAlign="right", Width="80",SqlFld="orig_price",Hidden=true}},
					  
					    {"real_price", new DataItem(){Title="单价", Sortable=true, CellsAlign="right", Width="80",SqlFld="real_price"}},
						{"sum_amount", new DataItem(){Title = "金额", Sortable=true,CellsAlign = "right",Hidden=true,Width = "80",SqlFld = " (case when sheet_type in ('X' ,'XD') then (sub_amount) else (-sub_amount) end )  ",ShowSum = true}},


					   {"sale_sheet_rebate_price",    new DataItem(){Title="被补差价",  Sortable=true, Width="60", Hidden=true }},
					   {"sale_sheet_after_rebate_price",    new DataItem(){Title="被补差后价",  Sortable=true, Width="60", Hidden=true}},

					   {"before_rebate_price",    new DataItem(){Title="补差前价",  Sortable=true, Width="60" ,Hidden=true}},
					   {"rebate_price",    new DataItem(){Title="补差价",  Sortable=true, Width="60" }},
					   {"after_rebate_price",    new DataItem(){Title="补差后价",  Sortable=true, Width="60" ,Hidden=true}},
					   {"sheet_price_avg",    new DataItem(){Title="原单均价",    Width="60", Hidden=true }},
					   {"orig_sheet_after_rebat_price",    new DataItem(){Title="原单补差后价", Hidden=true, Sortable=true, Width="60" }},


					    
                       {"cost_amount_hasfree",  new DataItem(){ProfitRelated=true,Hidden=true, Title="销售成本（含赠）", CellsAlign="right",   Width="150", Sortable=true,ShowSum=true,
                       FuncDealMe=(value)=>{
                           if (string.IsNullOrEmpty(value))
                           {
                               return "0";
                           }
                            return Convert.ToString(Math.Round(CPubVars.ToDecimal(value),2));
                       }

                       }},
                       {"profit_hasfree",new DataItem(){ProfitRelated=true,Title = "销售利润（含赠）",Hidden=true, Sortable=true,CellsAlign = "right",Width = "150",ShowSum = true}},
                       {"profit_rate_hasfree",new DataItem(){ProfitRelated=true,Title = "利润率(%)（含赠）",Hidden=true, Sortable=true,CellsAlign = "right",Width = "150",ShowAvg = true,
                       FuncGetSumValue = (sumColumnValues) =>
                           {
                               string s_profit_hasfree =sumColumnValues["profit_hasfree"];
                               string s_net_amount =sumColumnValues["sum_amount"];

                               double profit_hasfree=s_profit_hasfree!=""?Convert.ToDouble(s_profit_hasfree) : 0.0;
                               double net_amount=s_net_amount!=""?Convert.ToDouble(s_net_amount) : 0.0;
                               string rate="";
                               if (net_amount != 0)
                               {
                                   rate=CPubVars.FormatMoney(profit_hasfree/net_amount*100,1);
                               }
                               return rate;
                           }
                       }},
                       {"cost_amount_hasfree_cl_jh",  new DataItem(){ProfitRelated=true,Title="销售成本（含赠|陈列|借还）",Hidden=true, CellsAlign="right",    Width="200", Sortable=true,ShowSum=true}},
                       {"profit_hasfree_cl_jh",new DataItem(){ProfitRelated=true,Title = "销售利润（含赠|陈列|借还）", Hidden=true,Sortable=true,CellsAlign = "right",Width = "200",ShowSum = true}},
                       {"profit_rate_hasfree_cl_jh",new DataItem(){ProfitRelated=true,Title = "利润率(%)（含赠|陈列|借还）",Hidden=true, Sortable=true,CellsAlign = "right",Width = "200",ShowAvg = true,
                       FuncGetSumValue = (sumColumnValues) =>
                           {
                               string s_profit_hasfree_cl_jh =sumColumnValues["profit_hasfree_cl_jh"];
                               string s_net_amount =sumColumnValues["sum_amount"];

                               double profit_hasfree_cl_jh=s_profit_hasfree_cl_jh!=""?Convert.ToDouble(s_profit_hasfree_cl_jh) : 0.0;
                               double net_amount=s_net_amount!=""?Convert.ToDouble(s_net_amount) : 0.0;
                               string rate="";
                               if (net_amount != 0)
                               {
                                   rate=CPubVars.FormatMoney(profit_hasfree_cl_jh/net_amount*100,1);
                               }
                               return rate;
                           }
                       }},
                       {"free_cost_amount",new DataItem(){ProfitRelated=true,Title="赠品成本",CellsAlign="right", Width="100",SqlFld="",ShowSum=true,Hidden=true,FuncDealMe=(value)=>{return value=="0"?"":value; }, } },
                       {"cost_amount",  new DataItem(){ProfitRelated=true,Title="销售成本", CellsAlign="right",  Hidden=true,  Width="100", Sortable=true,ShowSum=true}},
                       {"cost_price",  new DataItem(){ProfitRelated=true,Title="成本价", CellsAlign="right",     Width="100",Hidden=true, Sortable=true,ShowSum=true}},
                       {"wholesale_price",new DataItem(){Title="批发价", Width="60",Sortable=true,
                            SubColumns=new Dictionary<string,DataItem>()
                            {
                                {"b_wholesale_price",new DataItem(){Title = "大",InnerTitle="批发价大",OrigTitle="批发价大",Hidden = true,Width = "70",CellsAlign="right",Sortable=true,SqlFld="b_wholesale_price::numeric"}},
                                {"m_wholesale_price",new DataItem(){Title="中",Hidden=true,Width="70",CellsAlign="right"} },
                                {"s_wholesale_price",new DataItem(){Title="小",Hidden=true,Width="70",CellsAlign="right",Sortable=true,SqlFld="s_wholesale_price::numeric"} },
                            }
                       }},
                       {"supplier_fee_cost_amount",new DataItem(){ProfitRelated=true,Title="厂家费用成本",Hidden=true,CellsAlign="right",Width="100",SqlFld="",ShowSum=true, FuncDealMe=(value)=>{return value=="0"?"":value; }, } },

                       {"profit",new DataItem(){ProfitRelated=true,Title = "销售利润", Hidden=true,Sortable=true,CellsAlign = "right",Width = "100",ShowSum = true}},
                       {"profit_rate",new DataItem(){ProfitRelated=true,Title = "利润率(%)", Hidden=true,Sortable=true,CellsAlign = "right",Width = "100",ShowAvg = true,
                       FuncGetSumValue = (sumColumnValues) =>
                           {
                               string s_profit_hasfree =sumColumnValues["profit"];
                               string s_net_amount =sumColumnValues["sum_amount"];

                               double profit_hasfree=s_profit_hasfree!=""?Convert.ToDouble(s_profit_hasfree) : 0.0;
                               double net_amount=s_net_amount!=""?Convert.ToDouble(s_net_amount) : 0.0;
                               string rate="";
                               if (net_amount != 0)
                               {
                                   rate=CPubVars.FormatMoney(profit_hasfree/net_amount*100,1);
                               }
                               return rate;
                           }
                       }},

                       {"row_disc_amount", new DataItem(){Title="折扣金额",Hidden=true,SqlFld="case when trade_type !='J' then  round(((orig_price-real_price)*money_inout_flag*quantity)::numeric,2) else 0 end", Width="90",CellsAlign="right",ShowSum=true}},

                      // {"sn_code", new DataItem(){Title="序列号", Sortable=true,Width="200"} },
                       {"make_brief",new DataItem(){Title="整单备注", Sortable=true,Width="100",JSCellRender="briefRender"} },
                       {"remark",   new DataItem(){Title="商品备注", Sortable=true, CellsAlign="left", Width="100",SqlFld="remark",JSCellRender="remarkRender"}} ,
                     //  {"item_no",new DataItem(){Title="商品编号", Hidden=true, Width="100",}},
                       {"produce_date",new DataItem(){Title="生产日期",Hidden=true, Width="100",}},
                      // {"valid_days",new DataItem(){Title="保质期",Hidden = true,Width="150"}},
                      // {"item_spec",new DataItem(){Title="规格",Hidden=true, Width="100",}},
                       {"s_unit_price",   new DataItem(){Title="小单位价格", Hidden=true,Sortable=true, CellsAlign="right", Width="100",
                          SqlFld="case when (unit_factor >0) then (real_price / unit_factor)  else 0   end"
                      }},
                       
                        
                     },
                     QueryFromSQL=@"
FROM 
(
    select sm.happen_time happen_time,make_time,make_brief,approve_time,total_amount,money_inout_flag,trade_type,sd.inout_flag,sd.row_index row_index,sd.item_id item_id,sd.sheet_id sheet_id,sm.sheet_type sheet_type,item_name,sd.unit_no,sd.unit_factor, sup_name,sm.supcust_id supcust_id,sheet_no,quantity,orig_price,real_price,sub_amount,remark,b_wholesale_price::numeric,m_wholesale_price::numeric,s_wholesale_price::numeric,rebate_price*sd.unit_factor sale_sheet_rebate_price, (real_price - coalesce(rebate_price,0)*sd.unit_factor) sale_sheet_after_rebate_price,   null rebate_price,null before_rebate_price,null after_rebate_price, null sheet_price_avg,                    null orig_sheet_after_rebat_price, sd.cost_price_recent, sd.cost_price_buy,sd.cost_price_avg,sd.cost_price_prop,relate_supplier_fee,ib.produce_date
    from sheet_sale_detail sd
    left join sheet_sale_main  sm on sm.company_id=~COMPANY_ID and sm.sheet_id=sd.sheet_id 
    left join info_item_prop ip on ip.company_id=~COMPANY_ID and ip.item_id=sd.item_id
    left join info_supcust isc on isc.company_id=~COMPANY_ID and isc.supcust_id=sm.supcust_id
    left join
    (
        select item_id,s_barcode, m_barcode,b_barcode,m_unit_no,s_unit_no,b_unit_no,b_unit_factor,m_unit_factor,s_wholesale_price,m_wholesale_price,b_wholesale_price,
        yj_get_unit_relation(b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no) as unit_conv
        from 
        (
            select item_id,(s->>'f1')::real as s_unit_factor,s->>'f2' as s_unit_no,s->>'f3' as s_barcode,s->>'f4' as s_wholesale_price,
                            (m->>'f1')::real as m_unit_factor,m->>'f2' as m_unit_no,m->>'f3' as m_barcode,m->>'f4' as m_wholesale_price,
                            (b->>'f1')::real as b_unit_factor,b->>'f2' as b_unit_no,b->>'f3' as b_barcode,b->>'f4' as b_wholesale_price
            from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,barcode,wholesale_price)) as json from info_item_multi_unit where company_id = ~COMPANY_ID ORDER BY item_id',$$values('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s jsonb,m jsonb, b jsonb) 
        ) g
    ) d on sd.item_id = d.item_id
    left join info_sheet_detail_brief isdb on isdb.company_id=~COMPANY_ID and isdb.brief_text=sd.remark
    left join info_item_batch ib on ib.company_id=~COMPANY_ID and ib.batch_id=sd.batch_id
    WHERE sd.company_id=~COMPANY_ID and sm.red_flag is null and ~QUERY_CONDITION 

 UNION 
       
    SELECT sm.happen_time happen_time,make_time,make_brief,approve_time,total_amount,money_inout_flag,'BC' trade_type,sd.inout_flag,sd.row_index row_index,sd.item_id item_id,sd.sheet_id sheet_id,sm.sheet_type sheet_type,item_name,sd.unit_no,sd.unit_factor,sup_name,sm.supcust_id supcust_id,sheet_no,quantity,null orig_price,null real_price,sub_amount,remark,null b_wholesale_price,null m_wholesale_price,null s_wholesale_price,                         null sale_sheet_rebate_price, null                                               sale_sheet_after_rebate_price,      rebate_price,       before_rebate_price,  after_rebate_price, sheet_price_avg, sheet_price_avg-rebate_price orig_sheet_after_rebat_price, null cost_price_recent,null cost_price_buy,null cost_price_avg,null cost_price_prop,null relate_supplier_fee,null produce_date
    FROM  sheet_price_rebate_detail sd
    LEFT JOIN sheet_price_rebate_main sm on sm.company_id=~COMPANY_ID and sd.sheet_id=sm.sheet_id  
    LEFT JOIN info_item_prop ip on ip.company_id=~COMPANY_ID and ip.item_id=sd.item_id 
    LEFT JOIN info_supcust isc on isc.company_id=~COMPANY_ID and isc.supcust_id=sm.supcust_id
    WHERE sd.company_id=~COMPANY_ID and sm.red_flag is null and ~QUERY_CONDITION
) temp
               ",
                      //这个地方很困惑，指定客户筛选查询时很慢，) d on sd.item_id = d.item_id 后加了 and sd.company_id=~COMPANY_ID 速度就快了

                      

                      QueryOrderSQL=" order by happen_time desc,sheet_id,row_index"
                  }
                }
            };
        }

        public async Task OnGet(string sheetType)
        {


            await InitGet(cmd);


            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 

            //string sheetType = DataItems["sheetType"].Value;

        }

        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            var cost_price_type = DataItems["cost_price_type"].Value;

            // 价格过滤
            var price_filter_type = DataItems["price_filter_type"].Value;
            Console.WriteLine($"price_filter_type: {price_filter_type}");

         

            var queryTimeAccord = DataItems["queryTimeAccord"].Value;
            var startDay = DataItems["startDay"].Value;
            var endDay = DataItems["endDay"].Value;

            if (endDay.IsValid() && !endDay.Contains(":")) endDay += " 23:59:59";
            DataItems["endDay"].Value = endDay;
            SQLVariables["detail_happen_time"] = "sd.happen_time";
            SQLVariables["STATUS_ORDER"] = "";

            string sheetType = DataItems["sheetType"].Value;
            var main_table = "sheet_sale_main";
            var other_leftJoin = "";
            var other_field = "";
            var other_condi = "sm.sheet_id";
     
            SQLVariables["IS_DEL_CONDI"] = "";
 
            
 
            //默认happen_time提前三个月，利用分区表缩小查询范围，byClearArrearsTime三个月不够，需要现查最早的单据happen_time,
            //byHappenTime不需要限制MAIN_HAPPEN_TIME_LIMIT，因为SQL中已经有了
            DateTime tmStart = Convert.ToDateTime(startDay);
            tmStart = tmStart.AddMonths(-3);
            DateTime tmEnd = Convert.ToDateTime(endDay);
            tmEnd = tmEnd.AddMonths(3);
            SQLVariables["DETAIL_HAPPEN_TIME_LIMIT"] = @$" sd.happen_time >= '{CPubVars.GetDateText(tmStart)}' and sd.happen_time <= '{CPubVars.GetDateText(tmEnd)}'";
            SQLVariables["MAIN_HAPPEN_TIME_LIMIT"] = $" sm.happen_time >= '{CPubVars.GetDateText(tmStart)}' and sm.happen_time <= '{CPubVars.GetDateText(tmEnd)}'";

            if (queryTimeAccord == "byClearArrearsTime")
            {
                SQLVariables["happen_time"] = "sm.settle_time";
                string preSql = $"select min(happen_time) min_happen_time from sheet_sale_main where company_id={company_id} and red_flag is null and settle_time >= '{startDay}' AND settle_time <='{endDay}' and  approve_time is not null";
                dynamic rec = await CDbDealer.Get1RecordFromSQLAsync(preSql, cmd);
                string min_happen_time = rec.min_happen_time;

                SQLVariables["DETAIL_HAPPEN_TIME_LIMIT"] = @$" sd.happen_time >= '{startDay}'";
                if (min_happen_time != "")
                {
                    SQLVariables["DETAIL_HAPPEN_TIME_LIMIT"] = $" sd.happen_time >= '{min_happen_time}' ";
                    SQLVariables["MAIN_HAPPEN_TIME_LIMIT"] = $" sm.happen_time >= '{min_happen_time}'";

                }
            }
            else if (queryTimeAccord == "byMakeTime")
            {

                SQLVariables["happen_time"] = "sm.make_time";
            }
            else if (queryTimeAccord == "byApproveTime")
            {
                SQLVariables["happen_time"] = "sm.approve_time";
            }
            else if (queryTimeAccord == "bySendTime")
            {
                SQLVariables["happen_time"] = "sm.send_time";
            }
            else if (queryTimeAccord == "byCheckedTime")
            {
                SQLVariables["happen_time"] = "sm.check_account_time";
            }
            else//HAPPEN_TIME
            {
                //默认happen_time提前三个月，利用分区表缩小查询范围
                SQLVariables["happen_time"] = "sm.happen_time";
                SQLVariables["DETAIL_HAPPEN_TIME_LIMIT"] = "  sd.happen_time >= '~VAR_startDay' AND sd.happen_time <='~VAR_endDay'";
                SQLVariables["MAIN_HAPPEN_TIME_LIMIT"] = $" true ";

            }
            string remark = DataItems["remark"].Label;

            if (string.IsNullOrEmpty(remark) || remark == ",")
            {
                SQLVariables["remark"] = " and true ";
            }
            else
            {
                string[] keywords = remark.Split(','); // 使用逗号作为分隔符来分割字符串
                keywords = keywords.Where(k => !string.IsNullOrWhiteSpace(k) && k != "无备注").ToArray();
                string dynamicArray = $"ARRAY[{string.Join(", ", keywords.Select(k => $"'%{k}%'"))}]";
                if (remark.Equals("无备注,") || remark.Equals("无备注") || remark.Equals(",无备注,"))
                {
                    SQLVariables["remark"] = @$" and (sd.remark= '' or sd.remark is null)";
                }
                else if (remark.Contains("无备注"))
                {
                    SQLVariables["remark"] = @$" and (sd.remark||'' ILIKE ANY ({dynamicArray}) OR (sd.remark= '' or sd.remark is null)";
                }
                else
                {
                    SQLVariables["remark"] = @$" and (sd.remark||'' ILIKE ANY ({dynamicArray})) ";
                }
            }

            SQLVariables["startDay"] = DataItems["startDay"].Value;
            SQLVariables["endDay"] = DataItems["endDay"].Value;

            var columns = Grids.GetValueOrDefault("gridItems").Columns;
            ////含赠
            //var cost_amount_hasfree = columns.GetValueOrDefault("cost_amount_hasfree");
            //var profit_hasfree = columns.GetValueOrDefault("profit_hasfree");
            //var profit_rate_hasfree = columns.GetValueOrDefault("profit_rate_hasfree");
            ////赠品
            //var cost_price = columns.GetValueOrDefault("cost_price");
            ////去赠
            //var profit = columns.GetValueOrDefault("profit");
            //var profit_rate = columns.GetValueOrDefault("profit_rate");
            //var free_cost_amount = columns.GetValueOrDefault("free_cost_amount");

            //var cost_price = columns.GetValueOrDefault("cost_price");
            var costPrice = "cost_price_buy";//当前进价
            switch (cost_price_type)
            {
                case "4"://最近平均进价
                    costPrice = "cost_price_recent";
                    break;
                case "3"://当前进价
                    costPrice = "cost_price_buy";
                    break;
                case "2"://加权价
                    costPrice = "cost_price_avg";
                    break;
                case "1"://预设成本
                    costPrice = "cost_price_prop";
                    break;
            }
            // var sheetType = DataItems["sheetType"].Value;


            //Console.WriteLine("Detail页面的OnQueryConditionGot方法获取的costPrice:" + costPrice);

            var brand_id = DataItems["brand_id"].Value;
            if (brand_id == "-1")
            {
                DataItems["brand_id"].Value = "null";
            }

            columns["cost_amount_hasfree_cl_jh"].SqlFld = $"round( (quantity*unit_factor*inout_flag*(-1)*{costPrice} )::numeric,2) ";
            columns["profit_hasfree_cl_jh"].SqlFld = $"round((case when sheet_type in ('X' ,'XD') then (quantity*real_price) else (-quantity*real_price) end ):: NUMERIC, 2 )-round( ( quantity*unit_factor*inout_flag*(-1)*{costPrice})::numeric, 2)";
            columns["profit_rate_hasfree_cl_jh"].SqlFld = @$"
round ( (
	(
    100*(round((case when sheet_type in ('X' ,'XD') then (quantity*real_price) else (-quantity*real_price) end ):: NUMERIC, 2 )-round((  quantity*unit_factor*inout_flag*(-1)*{costPrice}  )::numeric,2))
)/
	(
		case when (case when sheet_type in ('X' ,'XD') then (quantity*real_price) else (-quantity*real_price) end ) <>0 
		     then (case when sheet_type in ('X' ,'XD') then (quantity*real_price) else (-quantity*real_price) end )
					else null 
		end
		) :: NUMERIC
	):: NUMERIC, 2)

";
            columns["cost_amount_hasfree"].SqlFld = $"(case when COALESCE(trade_type,'') !='J' then  quantity*unit_factor*inout_flag*(-1)*{costPrice} else 0 end) ";
            columns["profit_hasfree"].SqlFld = $"round((case when sheet_type in ('X' ,'XD') then (sub_amount) else (-sub_amount) end ):: NUMERIC, 2 )-round((case when COALESCE(trade_type,'')  !='J' and not coalesce(relate_supplier_fee,false) then quantity*unit_factor*inout_flag*(-1)*{costPrice} else 0 end)::numeric,2)";
            columns["profit_rate_hasfree"].SqlFld = @$"
round ( (
	(
    100*(round((case when sheet_type in ('X' ,'XD') then (sub_amount) else (-sub_amount) end ):: NUMERIC, 2 )-round((case when COALESCE(trade_type,'')  !='J' and not coalesce(relate_supplier_fee,false) then  quantity*unit_factor*inout_flag*(-1)*{costPrice} else 0 end)::numeric,2))
)/
	(
		case when (case when sheet_type in ('X' ,'XD') then (sub_amount) else (-sub_amount) end ) <>0 
		     then (case when sheet_type in ('X' ,'XD') then (sub_amount) else (-sub_amount) end )
					else null 
		end
		) :: NUMERIC
	):: NUMERIC, 2)

";
            columns["free_cost_amount"].SqlFld = $"round(((case when quantity!=0 and sub_amount=0 and COALESCE(trade_type,'') !='J'  then quantity*(-1)*unit_factor*inout_flag else 0 end)*{costPrice})::numeric,2) ";
            columns["supplier_fee_cost_amount"].SqlFld = $"round(((case when quantity!=0 and sub_amount=0 and COALESCE(trade_type,'') !='J' and relate_supplier_fee  then quantity*(-1)*unit_factor*inout_flag else 0 end)*{costPrice})::numeric,2) ";

            columns["cost_price"].SqlFld = $"{costPrice}*unit_factor";
            columns["cost_amount"].SqlFld = $" round((case when COALESCE(trade_type,'') !='J' then  quantity*unit_factor*inout_flag*(-1)*{costPrice} else 0 end)::numeric,2)-round(((case when quantity!=0 and sub_amount=0 and COALESCE(trade_type,'')  !='J' then quantity*(-1)*unit_factor*inout_flag else 0 end)*{costPrice})::numeric,2)";
            columns["profit"].SqlFld = $"round((case when sheet_type in ('X' ,'XD') then (sub_amount) else (-sub_amount) end ):: NUMERIC, 2 )-round((case when COALESCE(trade_type,'')  !='J' and not coalesce(relate_supplier_fee,false) then quantity*unit_factor*inout_flag*(-1)*{costPrice} else 0 end)::numeric,2)+round(((case when quantity!=0 and sub_amount=0 and COALESCE(trade_type,'')  !='J'  then quantity*(-1)*unit_factor*inout_flag else 0 end)*{costPrice})::numeric,2)";
            columns["profit_rate"].SqlFld = @$"
round ( (
	(
		100*(round((case when sheet_type in ('X' ,'XD') then (quantity*real_price) else (-quantity*real_price) end ):: NUMERIC, 2 )-round((case when COALESCE(trade_type,'')  !='J' then  quantity*unit_factor*inout_flag*(-1)*{costPrice} else 0 end)::numeric,2)+round(((case when quantity!=0 and sub_amount=0 and COALESCE(trade_type,'')  !='J' and not coalesce(relate_supplier_fee,false)  then quantity*(-1)*unit_factor*inout_flag else 0 end)*{costPrice})::numeric,2))
	)/
	(	case when (case when sheet_type in ('X' ,'XD') then (sub_amount) else (-sub_amount) end ) <>0 
		     then (case when sheet_type in ('X' ,'XD') then (sub_amount) else (-sub_amount) end )
					else null 
		end
		) :: NUMERIC
	):: NUMERIC, 2)

";

            if (DataItems["branch_id"].Value != "")
            {
                SQLVariables["branch_id"] = $" and COALESCE(sd.branch_id,sm.branch_id) in ( {DataItems["branch_id"].Value})";

            }
            else
            {
                SQLVariables["branch_id"] = " ";
            }
            string branchPositionStr = DataItems["branch_position"].Label;

            string branchs_position = "";
            if (branchPositionStr != "")
            {
                var branchPositionList = branchPositionStr.Split(',');
                foreach (var branchPosition in branchPositionList)
                {
                    if (branchs_position == "")
                    {
                        if (branchPosition == "默认库位")
                        {
                            branchs_position += $@" and (branch_position = 0";
                            continue;
                        }
                        branchs_position = $@" and (branch_position_name ='{branchPosition}'";
                    }
                    else
                    {
                        if (branchPosition == "默认库位")
                        {
                            branchs_position += $@" or branch_position = 0";
                            continue;
                        }
                        branchs_position += $@" or branch_position_name ='{branchPosition}'";
                    }
                }
                if (branchs_position != "") branchs_position += ")";
                SQLVariables["branch_position"] = branchs_position;
            }
            else
            {
                SQLVariables["branch_position"] = $@"";
            }
            //这边如果是从库存表跳转过来，需要限定一下条件。
          
        }

        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            string requestFlag = CPubVars.RequestV(Request, "queryTimeAccord");
            if (requestFlag == "")
            {
                JObject jsonOptions = JsonConvert.DeserializeObject<JObject>(JsonOptionsRemembered);
                JObject pageRemembered = (JObject)jsonOptions["page_SaleSheetView"];
                bool hasByHappenTimeOld = false;
                bool hasQueryTimeAccord = false;
                if (pageRemembered != null)
                {
                    foreach (var item in pageRemembered)
                    {
                        if (item.Key == "byHappenTime") hasByHappenTimeOld = true;
                        if (item.Key == "queryTimeAccord") hasQueryTimeAccord = true;
                    }
                }
                if (hasByHappenTimeOld && !hasQueryTimeAccord)
                {
                    string value = (string)pageRemembered["byHappenTime"]["value"];
                    if (value == "true")
                    {
                        DataItems["queryTimeAccord"].Value = "byHappenTime";
                        DataItems["queryTimeAccord"].Label = "交易时间";
                    }
                    else
                    {
                        DataItems["queryTimeAccord"].Value = "byApproveTime";
                        DataItems["queryTimeAccord"].Label = "审核时间";
                    }
                }
            }
            else
            {
                if (requestFlag == "byHappenTime")
                {
                    DataItems["queryTimeAccord"].Value = "byHappenTime";
                    DataItems["queryTimeAccord"].Label = "交易时间";
                }
                else if (requestFlag == "byMakeTime")
                {
                    DataItems["queryTimeAccord"].Value = "byMakeTime";
                    DataItems["queryTimeAccord"].Label = "制单时间";
                }
                else if (requestFlag == "byCheckedTime")
                {
                    DataItems["queryTimeAccord"].Value = "byCheckedTime";
                    DataItems["queryTimeAccord"].Label = "交账时间";
                }
            }

            var costPriceType = "3";
            var costPriceTypeName = "预设进价";
            if (JsonCompanySetting.IsValid())
            {
                dynamic setting = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonCompanySetting);
                if (setting != null && setting.costPriceType != null) costPriceType = setting.costPriceType;
            }
            if (costPriceType == "1") costPriceTypeName = "预设成本";
            else if (costPriceType == "2") costPriceTypeName = "加权平均成本";
            else if (costPriceType == "4") costPriceTypeName = "最近平均进价";
            DataItems["cost_price_type"].Value = costPriceType;
            DataItems["cost_price_type"].Label = costPriceTypeName;

            //Console.WriteLine("Detail页面的OnPageInitedWithDataAndRight方法获取的costPriceType:" + costPriceType);
            var columns = Grids["gridItems"].Columns;
            var sheetType = DataItems["sheetType"].Value;
            if (sheetType == "xd")
            {
                DataItems["arrears_status"].Hidden = true;

                DataItems["sn_code"].Hidden = true;
                DataItems["sn_code"].HideOnLoad = true;
                columns.Remove("sn_code");
            }
            if (sheetType == "x")
            {
                DataItems["transfer_status"].Hidden = true;
                DataItems["move_stock"].Hidden = true;
                DataItems["move_to_van"].Hidden = true;

                DataItems["transfer_status"].HideOnLoad = true;
                DataItems["move_stock"].HideOnLoad = true;
                DataItems["move_to_van"].HideOnLoad = true;
            }

            bool seeInPrice = false;
            if (JsonOperRights.IsValid())
            {
                dynamic operRights = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonOperRightsOrig);
                if (operRights?.delicacy?.seeInPrice?.value is not null)
                    seeInPrice = ((string)operRights.delicacy.seeInPrice.value).ToLower() == "true";
            }

            if (!seeInPrice)
            {
                columns["cost_amount_hasfree"].HideOnLoad = columns["cost_amount_hasfree"].Hidden = true;
                columns["profit_hasfree"].HideOnLoad = columns["profit_hasfree"].Hidden = true;
                columns["profit_rate_hasfree"].HideOnLoad = columns["profit_rate_hasfree"].Hidden = true;
                columns["cost_amount_hasfree_cl_jh"].HideOnLoad = columns["cost_amount_hasfree_cl_jh"].Hidden = true;
                columns["profit_hasfree_cl_jh"].HideOnLoad = columns["profit_hasfree_cl_jh"].Hidden = true;
                columns["profit_rate_hasfree_cl_jh"].HideOnLoad = columns["profit_rate_hasfree_cl_jh"].Hidden = true;
                columns["free_cost_amount"].HideOnLoad = columns["free_cost_amount"].Hidden = true;
                columns["profit"].HideOnLoad = columns["profit"].Hidden = true;
                columns["profit_rate"].HideOnLoad = columns["profit_rate"].Hidden = true;
                columns["cost_amount"].HideOnLoad = columns["cost_amount"].Hidden = true;
                columns["cost_price"].HideOnLoad = columns["cost_price"].Hidden = true;

            }


            var forItemHistory = DataItems["forItemHistory"].Value;
         

       
            //处理订单明细表与销售明细表的差异；订单不展示，销售明细隐藏
            string requestSheetType = "";
            if (CPubVars.RequestV(Request, "sheetType") != "")
            {
                requestSheetType = Convert.ToString(CPubVars.RequestV(Request, "sheetType"));
                if (requestSheetType == "x")
                {
                    DataItems["queryTimeAccord"].Source = @"[{v:'byApproveTime',l:'审核时间'},
                                {v:'byHappenTime',l:'交易时间'},
                                {v:'byMakeTime',l:'制单时间'},
                                {v:'byCheckedTime',l:'交账时间'},
                                {v:'bySendTime',l:'送货时间'},
                                {v:'byClearArrearsTime',l:'欠款结清时间'}]";
                }
                else
                {
                    DataItems["queryTimeAccord"].Source = @"[{v:'byApproveTime',l:'审核时间'},
                            {v:'byHappenTime',l:'交易时间'},
                            {v:'byMakeTime',l:'制单时间'},
                            {v:'bySendTime',l:'送货时间'}]";
                }

            }
        }

    }



    [Route("api/[controller]/[action]")]
    public class SaleItemHistoryController : QueryController
    {
        public SaleItemHistoryController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<JsonResult> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            SaleItemHistoryModel model = new SaleItemHistoryModel(cmd);
            return await PageBaseModel.GetDataItemOptionsJson(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);

        }

        [HttpPost]
        public async Task<object> GetQueryRecords([FromBody] dynamic data)
        {
            string sheetType = data.sheetType;
            string endDay = data.endDay + "";
            // if(!Regex.IsMatch(endDay,"23:59:00"))
            // {
            //     data.endDay = data.endDay+ " 23:59:59";
            // }
            string item_id= data.item_id     ;
            string supcust_id=data.supcust_id  ;
            string startDay = data.startDay + "";
            SaleItemHistoryModel model = new SaleItemHistoryModel(cmd);
            string condi = "";
            string another_condi = "";
            if (item_id.IsValid())
            {
                condi += $" and sd.item_id in ({item_id})";
                another_condi += $" and sprd.item_id in ({item_id})";
            }

            if (supcust_id.IsValid())
            {
                condi += $" and sm.supcust_id in ({supcust_id})";
                another_condi += $" and sprm.supcust_id in ({supcust_id})";
            }
            if (sheetType == "XSBC")
            {
           
               
               
                model.Grids["gridItems"].Columns["quantity"].SqlFld = "concat((case when sheet_type in ('X','XD','BC') then quantity else -quantity end),unit_no)";
                model.Grids["gridItems"].Columns["quantity"].FuncGetSumValue= null;
                 
         
            }

            object records = await model.GetRecordFromQuerySQL(Request, cmd, data);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            string sParams = Request.Form["params"];
            sParams = System.Web.HttpUtility.UrlDecode(sParams);
            dynamic queryParams = JsonConvert.DeserializeObject(sParams);
            SaleItemHistoryModel model = new SaleItemHistoryModel(cmd);
            return await model.ExportExcel(Request, cmd, queryParams);
        }
    }
}
