﻿using ArtisanManage.Models;
using ArtisanManage.Pages.Sheets;
using ArtisanManage.Services;
using ArtisanManage.Services.BaiduOCR;
using ArtisanManage.WebAPI;
using ArtisanManage.YingJiangMallMini.Model;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Microsoft.VisualBasic;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Configuration;
using System.DrawingCore;
using System.DrawingCore.Imaging;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Reflection;
using System.Runtime.InteropServices.JavaScript;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
namespace ArtisanManage.AppController
{

    [Route("AppApi/[controller]/[action]")]
    public class LoginController : QueryController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        public LoginController(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd;  _httpClientFactory = httpClientFactory;
        }

        /// <summary>
        /// 心跳检测
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> HeartBeat()
        {
            int i = CMySbConnection.LstConnections.Count;

            string result = "OK";
            return Json(new { result,server_time=DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")  });
        }

		/// 闪退上报
		/// </summary>
		/// <returns></returns>
		[HttpPost]
		public async Task<JsonResult> AndroidDumpReport(string data)
		{
			using (StreamReader reader = new StreamReader(Request.Body))
			{
				var result = "OK";
				string text = await reader.ReadToEndAsync();
				MyLogger.LogMsg(@$"Android Native Error,{text}", "-1");
				return Json(new { result, server_time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") });
			}


		}
		/// <summary>
		/// 通过手机号获取用户公司列表
		/// </summary>
		/// <param name="mobile">用户手机号</param>
		/// <returns></returns>
		[HttpGet]
        public async Task<JsonResult> GetUserCompanies(string mobile)
        {
            if (!mobile.IsValid()) return null;
            var result = "OK";
            var msg = "";
            var sql = @$"select gc.company_id,company_name,cs.setting ->> 'companyName' as company_name_setting,gs.server_uri 
                        from g_operator o
	                    left JOIN g_company gc ON o.company_id = gc.company_id left JOIN company_setting cs on gc.company_id = cs.company_id
                        left join g_server gs on gc.server_id = gs.server_id      
                        where mobile = '{mobile}' and gc.company_id is not null and coalesce(o.can_login,true) and coalesce(o.oper_status,'1')='1'
                ;";
            var list = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            if (list.Count == 0)
            {
                result = "Failed";
                msg = "账户不存在";
            }
#if (DEBUG)
            foreach(dynamic r in list)
            {
                r.server_uri = "";
            } 
#endif
            var data = list.Select(x=>x.ToJson());
            return new JsonResult(new { result, msg, data});
        }

        //[HttpPost] 
        //public Response Login([FromBody] dynamic request)
        //{
        //   return  Respond((res) =>
        //    {
        //        throw new NotImplementedException("此方法未实现");
        //    });
        //}
        [HttpPost]
        public async Task<JsonResult> SendResetPwdSms([FromBody] dynamic request)
        {
            string mobile = request.mobile;
            string verifyCode = ((int)(new Random().NextDouble() * 10000)).ToString();
            string msg = await SmsSender.SendVeriCodeSMS(_httpClientFactory,mobile, verifyCode);
            string result = "OK";
            if (msg != "")
            {
                result = "Error";
            }
            else
            {
                //await RedisHelper.SetAsync("forgetPwdMobile_" + mobile, verifyCode, 5 * 60);
                await RedisHelper.SetAsync("forgetPwdMobile_" + mobile, verifyCode, 5 * 60);
                msg = "发送成功";
            }
            return Json(new{ result,msg });

        }

        [HttpPost]
        public async Task<JsonResult> ForgetResetPwd([FromBody] dynamic request)
        {
            //string server_uri = request.server_uri;
            string companyID = request.companyId;
            string newPwd=request.passwordNew;
            string mobile = request.mobile;
            string accurateVerifyCode = await RedisHelper.GetAsync("forgetPwdMobile_"+request.mobile);
            string msg = "";
            string result = "OK";
         
            Console.WriteLine("verifyCode" + request.verifyCode);
            if (request.verifyCode!=accurateVerifyCode)
            {
                result = "fail";
                msg = "验证码错误，请重试";
                return Json(new {result, msg });
            }
         
            string sql = $"update g_operator set oper_pw='{newPwd}' where company_id = {companyID} and mobile='{mobile}' returning oper_id";
            cmd.CommandText = sql;
            msg = "修改成功";
            object ov=await cmd.ExecuteScalarAsync();
            string operID = "";
            if (ov!=DBNull.Value && ov != null)
            {
                operID = ov.ToString();
                await TokenChecker.UpdateOperPwd(operID,(string) request.passwordNew);
            }
            string operKey = new Token
            {
                OperID = operID,
                CompanyID = companyID,
                Pwd = newPwd
            }.ToString(); 

            return Json(new { result,msg, operKey });
            
        }
        /* [HttpPost]
         public async Task<JsonResult> SaveTicketAccessToken([FromBody] dynamic request)
         {
             Response.Cookies.Append("AccessTokenCookie", request.accessTokenCookie);
             var token = request.accessTokenCookie;
             return Json(new { token });
         }
         */
        #region 电商专用授权登录

        [HttpPost]
        public async Task<JsonResult> LoginFromPdd([FromBody] dynamic request)
        {
            string pddAuthCode = request.pddAuthCode;
            if (pddAuthCode.IsInvalid())
            {
                return Json(new { result = "Failed", msg = "未发现拼多多授权码" });
            }
            JsonResult loginResult = await Login(request);
            dynamic logRes = loginResult.Value;
            if (logRes.result != "OK")
            {
                return loginResult;
            }
            else
            {
                string operKey = logRes.operKey;
                Security.GetInfoFromOperKey(operKey, out string company_id);
                EmartAuthController eac = new EmartAuthController(cmd,_httpClientFactory);
                var auth_result = await eac.Pdd_ReceiveAuthDataWithInnerSys(_httpClientFactory,pddAuthCode, company_id);
                dynamic res = auth_result.Value;
                if(res.result == "OK")
                {
                    return loginResult;
                }
                else
                {
                    return auth_result;
                }
            }
        }

        [HttpPost]
        public async Task<JsonResult> LoginFromTb([FromBody] dynamic request)
        {
            string tbAuthCode = request.tbAuthCode;
            if (tbAuthCode.IsInvalid())
            {
                return Json(new { result = "Failed", msg = "未发现淘宝授权码" });
            }
            JsonResult loginResult = await Login(request);
            dynamic logRes = loginResult.Value;
            if (logRes.result != "OK")
            {
                return loginResult;
            }
            else
            {
                string operKey = logRes.operKey;
                Security.GetInfoFromOperKey(operKey, out string company_id);
                EmartAuthController eac = new EmartAuthController(cmd, _httpClientFactory);
                var auth_result = await eac.Tb_ReceiveAuthDataWithInnerSys(_httpClientFactory, tbAuthCode, company_id);
                dynamic res = auth_result.Value;
                if (res.result == "OK")
                {
                    return loginResult;
                }
                else
                {
                    return auth_result;
                }
            }
        }
        #endregion
        [HttpPost]
        public async Task<JsonResult> Login([FromBody] dynamic request)
        {
            //string expireMsg = "";
            var result = "Failed"; var msg = ""; var operKey = "";
            string fromPC = request.fromPC;
            string clientType = request.clientType;
            string regID = request.regID;
            string companyID = request.companyId;
            if (regID == null) regID = "";
            string clientVersion = "";
            string serverUri = "";
            string coolieUri = "";
            if (fromPC == "1")
            {
                clientVersion = request.clientVersion;
            }
            else
            {

            }

            string newVersion = "";
            string updateUrl = "";
            string companyName = "";
            string postByWebPage = "";
            //DateTime expireTime=new DateTime();
            //string expireMsg = "";

            var sql = @$"
select 
       o.oper_id,
       o.oper_name,
       reg_id,
       o.oper_pw,
       o.can_login,
       company_name,
       o.mobile,
       s.server_uri,cs.server_uri coolie_uri,
       is_admin,
       o.oper_status, 
       g_app_ver,
       g_pc_ver,
       g_pc_mb_ver,
       app_ver,
       pc_ver,
       pc_mb_ver,
       c.expire_time,
       io.depart_path,
       io.role_id,
       cst.setting->>'postByWebPage' post_by_web_page
from g_operator o
left join g_company c on o.company_id = c.company_id
left join company_setting cst on cst.company_id= {request.companyId}
left join g_server s on s.server_id = c.server_id              
left join g_server cs on s.coolie_server=cs.server_id 
left join g_version on true
LEFT JOIN info_operator io on  o.company_id = io.company_id and o.oper_id= io.oper_id 
left join g_push_oper_reg gp on o.company_id = gp.company_id and o.oper_id=gp.oper_id  and gp.reg_id='{regID}'                          
where o.company_id = '{request.companyId}' and o.mobile = '{request.mobile}' and o.oper_pw = '{request.password}' ;";
            // left join info_role r on g_operator.role_id = o.role_id
            dynamic data = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);

            //Dictionary<string, dynamic> sellersDic = new Dictionary<string, dynamic>();
            //var data = query.Fileds;
            if (data == null)
            {
                msg = "账号或密码不正确";
            }
            else if (data.oper_status == "0")
            {
                msg = "该账号已被停用或删除";
            }
            else if (data.can_login == "False")
            {
                msg = "该账号不能登录";
            }
            else
            {
                //var rsSellerSql = $@"SELECT rs.* , op.oper_id,op.oper_name,op.is_admin,op.oper_pw  FROM rs_seller rs  
                //LEFT JOIN g_operator op on op.company_id = rs.reseller_company_id and op.mobile = '{request.mobile}'
                //WHERE rs.company_id = '{request.companyId}'";
                //dynamic rsSellers = await CDbDealer.GetRecordsFromSQLAsync(rsSellerSql, cmd);
                
                //if (rsSellers.Count > 0)
                //{
                //    foreach(dynamic rs in rsSellers)
                //    {
                //        string rs_company_id = rs.reseller_company_id;
                //        dynamic rsSeller = rs;
                //        var rsOperKey = new Token
                //        {
                //            OperID = rs.oper_id.ToString(),
                //            CompanyID = rs_company_id.ToString(),
                //            IsAdmin = bool.Parse(rs.is_admin),
                //            Pwd = rs.oper_pw.ToString()
                //        }.ToString();
                //        rsSeller.rsOperKey = rsOperKey;
                //        sellersDic.Add(rs_company_id, rsSeller);
                //    }
                    
                    
                //}
                operKey = new Token
                {
                    OperID = data.oper_id.ToString(),
                    CompanyID = request.companyId.ToString(),
                    IsAdmin = bool.Parse(data.is_admin),
                    Pwd = data.oper_pw.ToString()
                }.ToString();
                /*
                if (request.platform != null && request.regID != null && data.reg_id == "")
                {
                    PushService pushService = new PushService(cmd);
                    // bool isExist = await pushService.IsExist(data.oper_id.ToString(), request.companyId.ToString(), request.platform.ToString(), request.regID.ToString());
                    // if (!isExist)
                    {
                        await pushService.AddRegIDToDB(request.companyId.ToString(), data.oper_id.ToString(), request.regID.ToString(), request.platform.ToString());
                    }

                }
                */
                await TokenChecker.UpdateOperPwd(data.oper_id, data.oper_pw);

                companyName = data.company_name;
                serverUri = data.server_uri;
                coolieUri = data.coolie_uri;
                postByWebPage = data.post_by_web_page;
                if (coolieUri != "")
                {
                    if (!coolieUri.EndsWith("/")) coolieUri += "/";
                }
#if (DEBUG)
               serverUri="http://127.0.0.1:8082";
               coolieUri = "http://127.0.0.1:8082";
#endif
                DateTime expireTime = new DateTime();
                if (data.expire_time != "")
                {
                    expireTime = Convert.ToDateTime(data.expire_time);
                    if (expireTime > DateTime.MinValue)
                    {
                        TimeSpan ts = expireTime - DateTime.Now;
                       // var leftDays = ts.Days;
                        var leftHours = ts.Hours;
                        if (leftHours < 0)
                        {                           
                            msg = "该账号已过期,请及时续费"; 
                        }

                        //else if (deadHour >= 0 && deadHour <= 15)
                        //{
                        //    if (deadDays == 0)
                        //    {
                        //        expireMsg = "还有" + deadHour + "小时到期!";
                        //    }
                        //    else
                        //    {
                        //        expireMsg = "还有" + deadDays + "天到期!";
                        //    }
                        //}
                    }

                }


                if (clientVersion.IsValid())
                {
                    float nClientVersion = Convert.ToSingle(clientVersion);
                    float g_app_ver = 0;
                    if (data.g_app_ver != "") g_app_ver = Convert.ToSingle(data.g_app_ver);

                    float g_pc_ver = 0;
                    if (data.g_pc_ver != "") g_pc_ver = Convert.ToSingle(data.g_pc_ver);

                    float pc_ver = 0;
                    if (data.pc_ver != "") pc_ver = Convert.ToSingle(data.pc_ver);

                    //float g_pc_mb_ver = 0;
                    // if (data.g_pc_mb_ver != "") g_pc_ver = Convert.ToSingle(data.g_pc_mb_ver);

                    // float pc_mb_ver = 0;
                    // if (data.pc_mb_ver != "") pc_ver = Convert.ToSingle(data.pc_mb_ver);


                    float app_ver = 0;
                    if (data.app_ver != "") app_ver = Convert.ToSingle(data.app_ver);

                    if (g_app_ver > app_ver) app_ver = g_app_ver;

                    if (g_pc_ver > pc_ver) pc_ver = g_pc_ver;
                    //  if (g_pc_mb_ver > pc_mb_ver) pc_mb_ver = g_pc_mb_ver;
                    if (fromPC == "1")
                    {
                        if (msg == "")
                        {
                            if (pc_ver > nClientVersion)
                            {
                                 updateUrl = $"https://www.yingjiang168.com/download/YingJiangClient_{pc_ver}.rar";
                               // updateUrl = $"https://yingjiang.obs.cn-east-3.myhuaweicloud.com/download/YingJiangClient_{pc_ver}.rar";
                            
                                newVersion = pc_ver.ToString();
                            }
                        }
                    }
                }
            }
            if (msg == "") result = "OK";
            string serverTime = CPubVars.GetDateText(DateTime.Now);
            //return Json(new { result, msg, data, operKey, companyName, newVersion, updateUrl, serverUri });
            return Json(new { result, msg, data, operKey,companyID, companyName, newVersion, updateUrl, serverUri, coolieUri, serverTime, postByWebPage});
        }
        public async Task<JsonResult> Login_old([FromBody] dynamic request)
        {
            //string expireMsg = "";
            var result = "Failed"; var msg = ""; var operKey = "";
            string fromPC = request.fromPC;
            string clientType = request.clientType;
            string regID = request.regID;
            if (regID == null) regID = "";
            string clientVersion = "";
            string serverUri = "";
            string coolieUri = "";
            if (fromPC == "1")
            {
                clientVersion = request.clientVersion;
            }

            string newVersion = "";
            string updateUrl = "";
            string companyName = "";
            //DateTime expireTime=new DateTime();
            //string expireMsg = "";
            
            var sql = @$"
select o.oper_id,o.oper_name,reg_id,o.oper_pw,o.can_login,company_name,o.mobile,server_uri,is_admin,case when io.status is not null then io.status when io.oper_id is null then 0  end status ,g_app_ver,g_pc_ver,g_pc_mb_ver, app_ver,pc_ver,pc_mb_ver,c.expire_time
from g_operator o
left join g_company c on o.company_id = c.company_id
left join g_server s on s.server_id = c.server_id                        
left join g_version on true
LEFT JOIN info_operator io on  o.company_id = io.company_id and o.oper_id= io.oper_id 
left join g_push_oper_reg gp on o.company_id = gp.company_id and o.oper_id=gp.oper_id  and gp.reg_id='{regID}'                          
where o.company_id = '{request.companyId}' and o.mobile = '{request.mobile}' and o.oper_pw = '{request.password}' ;";
            // left join info_role r on g_operator.role_id = o.role_id
            dynamic data = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            

            //var data = query.Fileds;
            if (data == null)
            {
                msg = "账号或密码不正确";
            }
            else if (data.status == "0")
            {
                msg = "该账号已被停用或删除";
            }
            else if (data.can_login == "False")
            {
                msg = "该账号不能登录";
            }
            else
            {
                
                operKey = new Token
                {
                    OperID = data.oper_id.ToString(),
                    CompanyID = request.companyId.ToString(),
                    IsAdmin = bool.Parse(data.is_admin),
                    Pwd= data.oper_pw.ToString()                    
                }.ToString();
                if (request.platform!=null && request.regID!=null && data.reg_id == "")
                { 
                    PushService pushService = new PushService(cmd);
                   // bool isExist = await pushService.IsExist(data.oper_id.ToString(), request.companyId.ToString(), request.platform.ToString(), request.regID.ToString());
                   // if (!isExist)
                    {
                        await pushService.AddRegIDToDB(request.companyId.ToString(), data.oper_id.ToString(), request.regID.ToString(), request.platform.ToString());
                    }
                   
                }

                await TokenChecker.UpdateOperPwd(data.oper_id, data.oper_pw);

                companyName = data.company_name;
                serverUri = data.server_uri;
                coolieUri = data.coolie_uri;
                #if (DEBUG)
                    serverUri="";
#endif
                DateTime expireTime = new DateTime();
                if (data.expire_time != "")
                {
                    expireTime = Convert.ToDateTime(data.expire_time);
                    if (expireTime > DateTime.MinValue)
                    {
                        TimeSpan deadLine = expireTime - DateTime.Now;
                        var deadDays = deadLine.Days;
                        var deadHour = deadLine.Hours;
                        if (deadHour < 0)
                        {
                            deadDays = -deadDays;
                            msg = "该账号已过期,请及时续费";
                        }
                        //else if (deadHour >= 0 && deadHour <= 15)
                        //{
                        //    if (deadDays == 0)
                        //    {
                        //        expireMsg = "还有" + deadHour + "小时到期!";
                        //    }
                        //    else
                        //    {
                        //        expireMsg = "还有" + deadDays + "天到期!";
                        //    }
                        //}
                    }

                }


                if (clientVersion.IsValid())
                {
                    float nClientVersion = Convert.ToSingle(clientVersion);
                    float g_app_ver = 0;
                    if (data.g_app_ver != "") g_app_ver = Convert.ToSingle(data.g_app_ver);

                    float g_pc_ver = 0;
                    if (data.g_pc_ver != "") g_pc_ver = Convert.ToSingle(data.g_pc_ver);

                    float pc_ver = 0;
                    if (data.pc_ver != "") pc_ver = Convert.ToSingle(data.pc_ver);

                    //float g_pc_mb_ver = 0;
                    // if (data.g_pc_mb_ver != "") g_pc_ver = Convert.ToSingle(data.g_pc_mb_ver);

                    // float pc_mb_ver = 0;
                    // if (data.pc_mb_ver != "") pc_ver = Convert.ToSingle(data.pc_mb_ver);


                    float app_ver = 0;
                    if (data.app_ver != "") app_ver = Convert.ToSingle(data.app_ver);



                    if (g_app_ver > app_ver) app_ver = g_app_ver;

                    if (g_pc_ver > pc_ver) pc_ver = g_pc_ver;
                    //  if (g_pc_mb_ver > pc_mb_ver) pc_mb_ver = g_pc_mb_ver;
                    if (fromPC == "1")
                    {   
                        if (msg == "")
                        {
                            if (pc_ver > nClientVersion)
                            {
                                updateUrl = $"https://www.yingjiang168.com/download/YingJiangClient_{pc_ver}.rar";
                                newVersion = pc_ver.ToString();
                            }
                        } 
                    }
                }
               
            }
            if(msg=="") result = "OK";
            //return Json(new { result, msg, data, operKey, companyName, newVersion, updateUrl,noticeStr, serverUri });
            return Json(new { result, msg, data, operKey, companyName, newVersion, updateUrl, serverUri, coolieUri });
        }
        /*
        [HttpGet]
        public async Task<JsonResult> GetRights(string operKey)
        {
            var result = "Failed"; var msg = ""; dynamic data = null;
            if (Token.TryParse(operKey, out Token token))
            {
                var sql = @$"select rights,mobile,oper_name,company_cachet
                    FROM info_operator
                    LEFT JOIN info_role ON info_role.role_id = info_operator.role_id 
                    left join company_setting cs on cs.company_id = info_operator.company_id

                    WHERE info_operator.oper_id = {token.OperID}
                    ;";
                data = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                result = "OK";
            }

            return Json(new { result, msg, data });
        }*/
        private static string ImgToBase64String(Bitmap bmp)
        {
            using (MemoryStream ms = new MemoryStream())
            {
                try
                {
                    bmp.Save(ms,  ImageFormat.Jpeg);
                    byte[] arr = new byte[ms.Length];
                    ms.Position = 0;
                    ms.Read(arr, 0, (int)ms.Length);
                    return Convert.ToBase64String(arr);
                }
                catch (Exception)
                {
                    return null;
                }
                finally
                {
                    ms.Close();
                    bmp.Dispose();
                }
            }        
        }
        private string BitmapToBase64_NOT_USED(Bitmap bmp)
        {
            try
            {
                using (MemoryStream ms = new MemoryStream())
                {
                    bmp.Save(ms,  ImageFormat.Jpeg);
                    byte[] arr = new byte[ms.Length];
                    ms.Position = 0;
                    ms.Read(arr, 0, (int)ms.Length);
                    ms.Close();
                    String strbaser64 = Convert.ToBase64String(arr);
                    return strbaser64;
                } 
                
            }
            catch (Exception)
            { 
                return "";
            }
        }

        [HttpPost]
        public async Task<JsonResult> ReportLog([FromBody] dynamic data)
        {
            var result = "OK"; var msg = ""; var operKey = "";
            operKey = data.operKey;
            string log = data.log;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);

            cmd.CommandText = $"insert into log_report (company_id,oper_id,log,report_time) values({companyID},{operID},'{log}','{CPubVars.GetDateText(DateTime.Now)}');";
            await cmd.ExecuteNonQueryAsync();  
            return Json(new { result, msg});
        }
   
        /*public String GetTicketSysToken(dynamic setting)
        {
            if (setting != null&&setting.openTicketAccessSys=="True"&&setting.ticketAccessSysAccount!=null&&setting.ticketAccessSysPwd!=null&&setting.ticketAccessSysKey!=null) {
                TicketAccessService ticketAccess = new TicketAccessService();
                var ticketAccessSysAccount = setting.ticketAccessSysAccount;
                var ticketAccessSysPwd = setting.ticketAccessSysPwd;
                var ticketAccessSysKey = setting.ticketAccessSysKey;

                dynamic loginData = new {
                        ticketAccessSysAccount,
                        ticketAccessSysPwd,
                        ticketAccessSysKey

                };

                dynamic resp= ticketAccess.login(loginData);
                return resp.result.token;
                }
            return "";
        }*/

        /*static string BuildWSSEHeader(string appKey, string appSecret)
        {
            string now = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ssZ"); //Created
            string nonce = Guid.NewGuid().ToString().Replace("-", ""); //Nonce

            byte[] material = Encoding.UTF8.GetBytes(nonce + now + appSecret);
            byte[] hashed = SHA256Managed.Create().ComputeHash(material);
            string hexdigest = BitConverter.ToString(hashed).Replace("-", "");
            string base64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(hexdigest)); //PasswordDigest

            return String.Format("UsernameToken Username=\"{0}\",PasswordDigest=\"{1}\",Nonce=\"{2}\",Created=\"{3}\"",
                            appKey, base64, nonce, now);
        }*/
      
        [HttpPost]
        public async Task<JsonResult> GetOperRights(string operKey)
        {
            var result = "Failed"; var msg = ""; dynamic data;

            dynamic operRights = null,  setting = null;
            List<int> operRegions = null;
            List<ExpandoObject> regions = null;
            List<ExpandoObject> availRegions = new List<ExpandoObject>();
             
            string roleId = "";
            string visit_schedule_id = "", mobile = "",company_name="", oper_name = "", oper_pw = "", depart_id = "", log_report = "", oper_status = "", brands_id = "", expireMsg = "", is_sender = "", depart_path = "";
            float app_version = 0;
            var company_cachet = ""; //var app_menus = "";
            bool manual_goback = false;
            dynamic branchRights = null;
            bool restrict_branches = false;
            dynamic branches = null;
            string serverUri = "";
            string coolieUri = "";
            string position_way = "";
            string expire_time = "";
            bool expired = false;
            string version_db="", version = "";
            string oem_logo_url = "";
            string oem_name = "";
            string oem_support_tel = "";
            string oem_company_name = "";

			if (string.IsNullOrEmpty(operKey))
            {
                return Json(new { result = "Error", msg = "operKey为空" });
            }

            if (Token.TryParse(operKey, out Token token))
            {
                var sql = @$"
select rights,o.mobile,o.oper_name,o.depart_path,go.oper_pw,g_company.version_db,s.version, visit_schedule_id,o.depart_id,dp.brands_id,avail_brands_id,log_report,company_cachet,setting,oper_regions,manual_goback,status,g_company.position_way,g_company.app_ver as c_app_ver,g_version.g_app_ver as g_app_ver,s.server_uri,coo.server_uri coolie_uri, expire_time,go.trial_till_date, is_sender,is_seller,o.role_id ,company_name, COALESCE(restrict_branches,false) as restrict_branches
    ,oem.oem_logo_url,oem.oem_name,oem.oem_company_name,oem.oem_support_tel,oem.oem_addr
FROM info_operator o
LEFT JOIN info_role ON o.company_id=info_role.company_id and o.role_id=info_role.role_id
LEFT JOIN g_operator go ON o.company_id=go.company_id and o.oper_id = go.oper_id 
LEFT JOIN company_setting cs on o.company_id = cs.company_id
LEFT JOIN g_company on o.company_id=g_company.company_id
left join g_server s on g_company.server_id = s.server_id      
left join g_server coo on s.coolie_server = coo.server_id      
LEFT JOIN g_version on true
LEFT JOIN info_department dp on o.company_id=dp.company_id and o.depart_id=dp.depart_id
left join g_oem_company oem on g_company.oem_id= oem.oem_id   
                    WHERE o.oper_id = {token.OperID};";
                SQLQueue QQ = new SQLQueue(cmd);
                QQ.Enqueue("oper_rights", sql);

                sql = $"select branch_id,branch_name,allow_negative_stock,allow_negative_stock_order from info_branch where company_id={token.CompanyID}";
                QQ.Enqueue("branches", sql);

                sql = $"select region_id,region_name from info_region where company_id={token.CompanyID}";
                QQ.Enqueue("regions", sql);

                sql = $"select obr.*,ib.branch_name from oper_branch_rights obr left join info_branch ib on ib.branch_id = obr.branch_id and ib.company_id = obr.company_id where oper_id={token.OperID}";
                QQ.Enqueue("branch_rights", sql);



                CMySbDataReader dr = await QQ.ExecuteReaderAsync();
                while (QQ.Count > 0)
                {
                    //operRights = Newtonsoft.Json.JsonConvert.DeserializeObject(data.rights);
                    //mobile = data.mobile;
                    //oper_name = data.oper_name;
                    //var Cachet = data.company_cachet;
                    //company_cachet = $"{Environment.CurrentDirectory}/wwwroot/uploads/{Cachet}".ToBase64();

                    //app_menus = System.IO.File.ReadAllTextAsync(Environment.CurrentDirectory + "/JsonFiles/appMenu.json", Encoding.UTF8).Result;
                    //app_menus = JsonConvert.SerializeObject(JsonConvert.DeserializeObject<List<AppMenuGroup>>(app_menus));
                    //appMenuGroups.RemoveAll(appMenuGroup =>
                    //{
                    //    appMenuGroup.subsetAttr.RemoveAll(menu =>
                    //    {
                    //        if (menu.MenuId == 0) return false;
                    //        foreach (var operRight in operRights as Rights)
                    //        {
                    //            if (operRight.Value.TryGetValue(menu.MenuId, out BaseRights keyValuePairs)) return keyValuePairs[MenuId.see] != "True";
                    //        }
                    //        return  true;
                    //    });
                    //    return appMenuGroup.subsetAttr.Count == 0;
                    //});

                    string tbl = QQ.Dequeue();
                    if (tbl == "oper_rights")
                    {
                        data = CDbDealer.Get1RecordFromDr(dr, false);

                        if (data != null)
                        {
                            operRights = JsonConvert.DeserializeObject(data.rights);
                            var oper_regions = data.oper_regions;
                            if (oper_regions == "") oper_regions = "[]";
                            operRegions = JsonConvert.DeserializeObject<List<int>>(oper_regions);
                            //operRegions = JsonConvert.DeserializeObject(oper_regions);

                            string ss = data.setting;
                            setting = JsonConvert.DeserializeObject(ss);
                            mobile = data.mobile;
                            oper_pw = data.oper_pw;
                            visit_schedule_id = data.visit_schedule_id;
                            oper_name = data.oper_name;
                            oper_status = data.status;
							oem_logo_url = data.oem_logo_url;
                            oem_name = data.oem_name;
                            oem_support_tel = data.oem_support_tel;
                            oem_company_name = data.oem_company_name;
							if (data.restrict_branches!="" && Convert.ToBoolean(data.restrict_branches)) restrict_branches = true;
                            depart_path = data.depart_path;
                            company_name = data.company_name;
                            float c_app_ver = 0f;
                            float g_app_ver = 0f;
                            if (data.c_app_ver != "") c_app_ver = Convert.ToSingle(data.c_app_ver);
                            if (data.g_app_ver != "") g_app_ver = Convert.ToSingle(data.g_app_ver);
                            app_version = c_app_ver;
                            if (g_app_ver > c_app_ver) app_version = g_app_ver;

                            log_report = data.log_report;
                            depart_id = data.depart_id;
                            brands_id = data.brands_id;
							string avail_brands_id = data.avail_brands_id;
                            if (avail_brands_id != "") brands_id = avail_brands_id;

							is_sender = data.is_sender;
                            position_way = data.position_way;
                            roleId = data.role_id;
                            string s_manual_goback = data.manual_goback;
                            if (s_manual_goback == "True") manual_goback = true;
                            var cachet = data.company_cachet;
                            if (cachet != "")
                            {
								try
								{
                                    var file = $"{Environment.CurrentDirectory}/wwwroot/uploads/{cachet}";
                                    if (System.IO.File.Exists(file))
                                    {
                                        using (Bitmap bmp = (Bitmap)Bitmap.FromFile(file))
                                        {
                                            company_cachet = ImgToBase64String(bmp);
                                        }

                                    }
                                }
								catch (Exception)
								{

								}
                                
                            }

                            serverUri = data.server_uri;
                            coolieUri = data.coolie_uri;
                            if (coolieUri != "")
                            {
                                if (!coolieUri.EndsWith("/")) coolieUri += "/";
                            }

#if (DEBUG)
                            serverUri = "";
                            coolieUri = "";
#endif
                            version_db = data.version_db;
                            version = data.version;
                          
                            if (setting != null)
                            {

                                string path = setting.billHeadImage;
                                if (!string.IsNullOrEmpty(path))
                                {
                                    if (path.StartsWith("/")) path = path.Substring(1, path.Length - 1);
                                    string file = $"{HuaWeiObsController.HuaWeiObs.BucketLinkHref}/uploads/{path}";

                                    try
                                    {
                                        var client = CPubVars.GetHttpClientFromFactory(_httpClientFactory);
                                        var uri = new Uri(file);
                                        var ms = await client.GetStreamAsync(uri);

                                        using (var bmp = new Bitmap(ms))
                                        {
                                            setting.billHeadImage = ImgToBase64String(bmp);
                                        }
                                    }
                                    catch (Exception e)
                                    {
                                        setting.billHeadImage = "";
                                    }
                                }
                                path = setting.billTailImage;
                                if (!string.IsNullOrEmpty(path))
                                {
                                    if (path.StartsWith("/")) path = path.Substring(1, path.Length - 1);
                                    string file = $"{HuaWeiObsController.HuaWeiObs.BucketLinkHref}/uploads/{path}";

                                    try
                                    {
                                        var client = CPubVars.GetHttpClientFromFactory(_httpClientFactory); 
                                        var uri = new Uri(file);
                                        var ms = await client.GetStreamAsync(uri);

                                        using (var bmp = new Bitmap(ms))
                                        {
                                            setting.billTailImage = ImgToBase64String(bmp);
                                        }
                                    }
                                    catch (Exception e)
                                    {
                                        NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                                        logger.Error("in getOperRights. error:" + e.Message);
                                        setting.billTailImage = "";
                                    }

                                }
                                path = setting.billTailImage2;
                                if (!string.IsNullOrEmpty(path))
                                {


                                    if (path.StartsWith("/")) path = path.Substring(1, path.Length - 1);
                                    string file = $"{HuaWeiObsController.HuaWeiObs.BucketLinkHref}/uploads/{path}";

                                    try
                                    {
                                        var client = CPubVars.GetHttpClientFromFactory(_httpClientFactory);
                                        var uri = new Uri(file);
                                        var ms = await client.GetStreamAsync(uri);

                                        using (var bmp = new Bitmap(ms))
                                        {
                                            setting.billTailImage2 = ImgToBase64String(bmp);
                                        }
                                    }
                                    catch (Exception e)
                                    {
                                        NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                                        logger.Error("in getOperRights. error:" + e.Message);
                                        setting.billTailImage2 = "";
                                    }

                                }
                                // setting.ticketAccessToken=this.GetTicketSysToken(setting);
                            }

                            DateTime expireTime = new DateTime();
                           
                            if (data.expire_time != "")
                            {
                                expire_time = data.expire_time;
                                expireTime = Convert.ToDateTime(data.expire_time);
                                if (expireTime > DateTime.MinValue)
                                {
                                    TimeSpan ts = expireTime - DateTime.Now;
                                    var leftDays = ts.Days;
                                    var leftHours = ts.Hours;
                                    if (leftHours < 0)
                                    {                                       
                                        expireMsg = "账号已过期,请及时续费";
                                        expired = true;
                                    }
                                    else if (leftDays >= 0 && leftDays <= 3)
                                    {
                                        if (leftDays == 0)
                                        {
                                            expireMsg = "系统服务" + leftHours + "小时后到期";
                                        }
                                        else
                                        {
                                            expireMsg = "系统服务" + leftDays + "天后到期";
                                        }
                                    }
                                }
                            }

                            if (expireMsg == "")
                            {
								if (data.trial_till_date != "")
								{
									expire_time = data.trial_till_date;
									expireTime = Convert.ToDateTime(expire_time);
									if (expireTime > DateTime.MinValue)
									{
										TimeSpan ts = expireTime - DateTime.Now;
										var leftDays = ts.Days;
										var leftHours = ts.Hours;
										if (leftHours < 0)
										{
											expireMsg = "账号试用已过期, 请及时续费";
											expired = true;
										}
										else if (leftDays >= 0 && leftDays <= 3)
										{
											if (leftDays == 0)
											{
												expireMsg = "系统试用" + leftHours + "小时后到期";
											}
											else
											{
												expireMsg = "系统试用" + leftDays + "天后到期";
											}
										}
									}
								}
							}

                        }
                    }
                    else if (tbl == "branches")
                    {
                        branches = CDbDealer.GetRecordsFromDr(dr, false);
                    }
                    else if (tbl == "branch_rights")
                    {
                        branchRights = CDbDealer.GetRecordsFromDr(dr, false);
                    }
                    else if (tbl == "regions")
                    {
                        regions = CDbDealer.GetRecordsFromDr(dr, false);
                    }

                }
                QQ.Clear();
                if (operRegions != null)
                {
                    foreach(int region_id in operRegions)
                    {
                        dynamic region = regions.Find(r=>((dynamic)r).region_id== region_id.ToString());
                        if (region!=null)
                        {
                            availRegions.Add(region);
                        }
                    }
                }
                result = "OK";
            }

           
            if (token == null)
			{
                return Json(new { result = "Error", msg = "token为空" });
            }
            if (token.Pwd != oper_pw)
            {
                return Json(new { result = "Error", msg = "密码不对,请重新登录" });
            }
            List<dynamic> lstSellers = new List<dynamic>();
            var rsSellerSql = $@"
                SELECT rs.reseller_company_id company_id, rs.reseller_name company_name , op.oper_id,op.oper_name,op.is_admin,op.oper_pw  FROM rs_seller rs  
            LEFT JOIN g_operator op on op.company_id = rs.reseller_company_id and op.mobile = '{mobile}'
            WHERE rs.company_id = '{token.CompanyID}' and op.oper_id is not null;";
            if (!token.IsAdmin)
            {
                rsSellerSql = $@"

						SELECT rs.company_id,rs.company_name, op.oper_id,op.oper_name,op.is_admin,op.oper_pw  FROM rs_seller rs 
                        LEFT JOIN g_operator op on op.company_id = rs.company_id and op.mobile = '{mobile}'
                        WHERE rs.reseller_company_id = '{token.CompanyID}' and op.oper_id is not null
                        UNION 
                        SELECT rs.reseller_company_id company_id, rs.reseller_name company_name , op.oper_id,op.oper_name,op.is_admin,op.oper_pw  FROM rs_seller rs  
                        LEFT JOIN g_operator op on op.company_id = rs.reseller_company_id and op.mobile = '{mobile}'
                        WHERE rs.company_id in(SELECT company_id FROM rs_seller WHERE reseller_company_id = '{token.CompanyID}') and rs.reseller_company_id <>'{token.CompanyID}' and op.oper_id is not null;";
            }
            dynamic rsSellers = await CDbDealer.GetRecordsFromSQLAsync(rsSellerSql, cmd);
            if (rsSellers.Count > 0)
            {
                lstSellers.Add(new { company_id = token.CompanyID,company_name = company_name, oper_id = token.OperID, oper_name= oper_name,is_admin= token.IsAdmin,oper_pw = token.Pwd, rsOperKey = operKey });
                foreach (dynamic rs in rsSellers)
                {
                    string rsCompanyID = rs.company_id;
                    dynamic rsSeller = rs;
                    var rsOperKey = new Token
                    {
                        OperID = rs.oper_id.ToString(),
                        CompanyID = rsCompanyID.ToString(),
                        IsAdmin = bool.Parse(rs.is_admin),
                        Pwd = rs.oper_pw.ToString()
                    }.ToString();
                    rsSeller.rsOperKey = rsOperKey;
                    lstSellers.Add( rsSeller);

                }
            }
            float verDb = 0, verServer=0;
            if (version_db != "")  verDb = Convert.ToSingle(version_db);
            if (version != "") verServer = Convert.ToSingle(version);
            verServer = Convert.ToSingle(CPubVars.g_Version);

			if (verDb < verServer)
            {
                string redisKey =$"UPDATE_DB_{token.CompanyID}";
                string redisValue = await RedisHelper.GetAsync(redisKey);
          
             
                if (redisValue != "1")
                {

                    await RedisHelper.SetAsync(redisKey, "1");
                    int expireSeconds = 120;
                    await RedisHelper.ExpireAsync(redisKey, expireSeconds);
                    Task.Run(async () =>
                    {
                        CMySbConnection conn = null;

                        try
                        {

                            conn = CMySbConnection.getEmptyConnection();


                            CMySbCommand cmd = new CMySbCommand("", conn);
                            var dd = await CommonTool.CheckToUpdateDb(cmd, token.CompanyID, "", true);

                            cmd.Dispose();
                            conn.Close();
                            await RedisHelper.DelAsync(redisKey);

                        }
                        catch (Exception e)
                        {
                            if (conn != null) conn.Close();
                            NLogger.Error("in GetOperRight.Error WHILE UPDATE VERSION " + e.Message + "stackTrace:" + e.StackTrace + " msg:" + msg);
                        }
                    });
                }
              

              
            }
           

            

            return Json(new { result, msg,companyID=token.CompanyID,company_name, roleId, restrict_branches, branches, position_way, operRights, operRegions, availRegions, setting, branchRights, mobile, oper_name, oper_status, is_sender, depart_id, brands_id, expire_time, expired, expireMsg, visit_schedule_id, company_cachet, oper_id = token.OperID, manual_goback, app_version, serverUri, coolieUri, oem_name, oem_logo_url, bucketLinkHref = HuaWeiObsController.HuaWeiObs.BucketLinkHref, depart_path, lstSellers });
        }

    
        /// <summary>
        /// 注册账号//苹果审核使用
        /// </summary>
        /// <returns></returns>
        //[HttpGet]
        //public Response Signup_main(string mobile,string userName,string password,int companyId)
        //{
        //    return Respond(async response => {
        //        if (mobile.IsInvalid()) throw new MyException("请填写手机号码");
        //        if (userName.IsInvalid()) throw new MyException("请填写姓名");
        //        if (password.IsInvalid()) throw new MyException("请填写密码");

        //        cmd.CommandText = $"select count(*) from g_operator where Company_Id={companyId} and Mobile='{mobile}'";
        //        Int64 exist = (Int64)(await cmd.ExecuteScalarAsync());
        //        if (exist > 0) throw new MyException("手机号码已注册");

        //        var user_G = new User_g
        //        {
        //            company_Id = companyId,
        //            Mobile = mobile,
        //            UserName = userName,
        //            Password = password
        //        };
        //        user_G.UserId = (await new List<User_g> { user_G }.ToDataTable().SaveAsync(cmd, "oper_id"))[0];

        //        var url = "http://www.yingjiang168.com/AppApi/Login/Signup_subServer";
        //        var result = await WebHelper.PostAsync(url, user_G);

        //        if (result["result"] != "OK")
        //        {
        //            throw new MyException(result["msg"]);
        //        }


        //    });
        //}

        [HttpPost]
        public async Task<Response> Signup_subServer([FromBody] User_g user_G)
        {
            Response response = new Response();
            var user = new User
            {
                company_Id = user_G.company_Id,
                Mobile = user_G.Mobile,
                UserId = user_G.UserId,
                UserName = user_G.UserName,
                RoleId = 1,
                IsSeller = true,
                DepartId = 1,
            };
            try
            {
                await new List<User> { user }.ToDataTable().SaveAsync(cmd);
            }
            catch(Exception ex)
            {
                response["result"] = "Error";
                response["msg"] = ex.Message;
            }
            return response;
        }


        [HttpGet]
        public async Task<JsonResult> GetInfoRegionName(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            var sql = @$"
SELECT region_id, region_name FROM info_region 
WHERE region_id in 
(
    SELECT json_array_elements_text
    (
       (
            SELECT oper_regions FROM info_operator WHERE oper_id = '{operID}' and company_id = '{companyID}'
       ) :: json 
    ) ::BIGINT
)  and company_id = '{companyID}'";
            var data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });
        }

    }
}
