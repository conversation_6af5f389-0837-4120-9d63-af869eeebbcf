﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using ArtisanManage.Models;
using ArtisanManage.Services;
using ArtisanManage.MyJXC;
using static ArtisanManage.MyJXC.SheetSale;
using System.Dynamic;
using Newtonsoft.Json;

namespace ArtisanManage.Pages
{
    public class BuyOrderSheetModel : PageSheetModel<SheetRowBuyOrder>
    { 

        public string SheetTitle = "";
        public BuyOrderSheetModel(CMySbCommand cmd) : base(Services.MenuId.sheetBuyOrder)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"sheet_no",new DataItem(){Title="单号",UseJQWidgets=false}},
                {"sheet_id",new DataItem(){Title="sheet_id", CtrlType="hidden", FldArea="divHead"}},
                {"red_flag",new DataItem(){Title="red_flag", CtrlType="hidden", FldArea="divHead"}},
                {"sheetType",new DataItem(){Title="sheet_type", CtrlType="hidden", FldArea="divHead"}},
                {"sheet_type",new DataItem(){Title="sheet_type", CtrlType="hidden", FldArea="divHead"}},
                {"supcust_id",new DataItem(){FldArea="divHead",Title="供应商",LabelFld="sup_name",ButtonUsage="list",CompareOperator="=",SqlFld="sc.supcust_id",
                    SqlForOptions=CommonTool.selectSuppliers}},
                {"acct_type",new DataItem(){FldArea="divHead",Title="结账方式",Hidden=true, HideOnLoad=true}},
                {"seller_id",new DataItem(){FldArea="divHead",Title="经手人",LabelFld="seller_name",ButtonUsage="list",SqlForOptions=CommonTool.selectSellers}},//SqlForOptions="select oper_id as v,oper_name as l,py_str as z from info_operator"
                  
                {"branch_id",new DataItem(){FldArea="divHead",Title="仓    库",LabelFld="branch_name", ButtonUsage="list",SqlForOptions=CommonTool.selectBranch,AlwaysShow=true}},
                //{"oper_date",new DataItem(){title="制单日期",ctrlType="jqxDateTimeInput"}},
                {"happen_time",new DataItem(){FldArea="divHead",Title="交易日期",CtrlType="jqxDateTimeInput",PlaceHolder="当前时间" }},
                {"make_brief",new DataItem(){FldArea="divHead",Title="备    注"}},
                {"total_quantity",new DataItem(){FldArea="divTail", Hidden=true, Title="合计数量",Width="80"}},
                {"total_amount",new DataItem(){FldArea="divTail", Hidden=true, Title="合计金额",Width="80",Value="0"}},
                {"now_disc_amount",new DataItem(){FldArea="divTail",Title="优惠金额",Width="80",Value="0"}},
                {"no_disc_amount",new DataItem(){FldArea="divTail",Title="惠后合计",Width="80"}},
                //{"payway1_id",new DataItem(){FldArea="divTail",HideGroup="payway1", HideClass="payway",Title="",InnerTitle="支付方式1", LabelFld="payway1_name",ClassName="itemLeft",PlaceHolder="支付方式",ButtonUsage="list",SqlForOptions="select s.sub_id as v,s.sub_name as l,s.py_str as z,(case when payway_type in ('YS','YF') then 't'  else 'f' end) hide from cw_subject s left join info_pay_way p on s.sub_id=p.sub_id where p.company_id=~COMPANY_ID and payway_type in ('QT','YF') ORDER BY s.order_index",Width="80",GetOptionsOnLoad=true,FirstOptionAsDefault=true,AlwaysShow=true}},
                {"payway1_id",new DataItem(){FldArea="divTail",HideGroup="payway1", HideClass="payway",Title="",InnerTitle="支付方式1", LabelFld="payway1_name",ClassName="itemLeft",PlaceHolder="支付方式",ButtonUsage="list",SqlForOptions=CommonTool.selectPayWay,Width="80",GetOptionsOnLoad=true,FirstOptionAsDefault=true,AlwaysShow=true}},
                {"payway1_type",new DataItem(){FldArea="divTail",Title="",InnerTitle="支付类型1",Hidden=true,HideOnLoad=true, Width="80"}},

                {"payway1_amount",new DataItem(){FldArea="divTail",HideGroup="payway1",HideClass="payway", Title="",InnerTitle="支付金额1", ClassName="itemRight",PlaceHolder="支付金额",Width="80",AlwaysShow=true}},
                {"payway2_id",new DataItem(){FldArea="divTail",HideGroup="payway2",HideClass="payway", Title="",InnerTitle="支付方式2", LabelFld="payway2_name",ClassName="titleItem",PlaceHolder="支付方式", ButtonUsage="list",SqlForOptions=CommonTool.selectPayWay,Width="80",GetOptionsOnLoad=true,Hidden=true}},
                {"payway2_type",new DataItem(){FldArea="divTail", Title="",InnerTitle="支付类型2",Hidden=true,HideOnLoad=true, Width="80"}},
                {"payway2_amount",new DataItem(){FldArea="divTail",HideGroup="payway2",HideClass="payway", Title="",InnerTitle="支付金额2", PlaceHolder="支付金额",Width="80",Hidden=true}},
                {"payway3_id",new DataItem(){FldArea="divTail",HideGroup="payway3",HideClass="payway", Title="",LabelFld="payway3_name",Hidden=true,InnerTitle="支付方式3",ClassName="titleItem",PlaceHolder="支付方式", ButtonUsage="list",SqlForOptions=CommonTool.selectPayWay,Width="80",GetOptionsOnLoad=true}},
                {"payway3_type",new DataItem(){FldArea = "divTail", Title = "", InnerTitle = "支付类型3", Hidden = true, HideOnLoad = true, Width =     "80"}},
                {"payway3_amount",new DataItem(){FldArea="divTail",HideGroup="payway3",HideClass="payway", Title="",InnerTitle="支付金额3",PlaceHolder="支付金额",Hidden=true, Width="80"}},


                {"left_amount",new DataItem(){FldArea="divTail",Title="欠款",Width="80",Disabled=true}},
                {"maker_id",new DataItem(){UseJQWidgets=false, Title="制单人", Width="80"}},
                {"maker_name",new DataItem(){UseJQWidgets=false, Title="制单人", Width="80"}},
                {"make_time",new DataItem(){UseJQWidgets=false, Title="审核时间"}},
                {"approver_id",new DataItem(){UseJQWidgets=false, Title="审核人", Width="80"}},
                {"approver_name",new DataItem(){UseJQWidgets=false, Title="审核人", Width="80"}},
                {"approve_time",new DataItem(){UseJQWidgets=false, Title="审核时间"}},
                {"prepay_sub_ids",new DataItem(){Title="prepay_sub_ids", CtrlType="hidden", FldArea="divHead"}},
                {"appendix_photos",new DataItem(){Title="appendix_photos", CtrlType="hidden", FldArea="divHead"}},
               // {"branch_state",new DataItem(){title="状态",labelFld="branch_status_name",labelInDB=false,value="1",label="正常", buttonUsage="list", source = "[{v:1,l:'正常'},{v:0,l:'停用'}]"}}
            };

            // m_idFld = "sheet_id"; 
            // m_tableName = "sheet_item_master";
            //  m_selectFromSQL = "from sheet_item_master sht left join info_supcust on sht.supcust_id=info_supcust.supcust_id left join info_branch on sht.branch_id=info_branch.brand_id left join (select oper_id,oper_name as order_man_name from info_operator) tb_order_man on sht.order_man=tb_order_man.oper_id where sheet_id='~ID'";
            /*
            Grids = new Dictionary<string, FormDataGrid>()
            {
                {"gridUnit" ,new FormDataGrid(){
                   Columns = new Dictionary<string, DataItem>()
                   {
                       //{"unit_no",new DataItem(){title="单位",width="100",url="../api/ItemEdit/GetUnits"}},
                       {"unit_no",new DataItem(){title="单位",width="100",SqlForOptions="select unit_no from info_item_unit",getOptionsOnLoad=true,buttonUsage="list"}},
                       {"unit_factor",new DataItem(){title="包装率",width="80"}},
                   },
                   TableName="info_item_multi_unit",
                   IdFld="item_no",
                   SelectFromSQL="from info_item_multi_unit where item_no='~ID'"
                }}
            };*/
        }

        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            //if (DataItems["payway1_id"].Value == "")
            //{
            //    string sql = $"select  p.sub_id as v,s.sub_name as l from info_pay_way p left join cw_subject s on p.sub_id=s.sub_id where p.company_id={company_id} and payway_type = 'QT' order by p.payway_index";

            //    dynamic record = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            //    if (record != null)
            //    {
            //        DataItems["payway1_id"].Value = record.v;
            //        DataItems["payway1_id"].Label = record.l;
            //    }
            //}
            string sqlPrepay = $"select string_agg(sub_id::text,',') sub_ids  from cw_subject where company_id = {company_id} and sub_type in ('YF','YS','ZC') and is_order is not true";
            dynamic recordPrepay = await CDbDealer.Get1RecordFromSQLAsync(sqlPrepay, cmd);
            if (recordPrepay != null) DataItems["prepay_sub_ids"].Value = recordPrepay.sub_ids;
         

        }

        public async Task OnGet(bool forReturn)
        {
            SheetBuyOrder sheet = new SheetBuyOrder(forReturn ? SHEET_RETURN.IS_RETURN : SHEET_RETURN.NOT_RETURN, LOAD_PURPOSE.SHOW);
            if (forReturn)
            {
           //     PageMenuID = MenuId.sheetb;
            }
            await InitGet(cmd, sheet);
            //string items_id = "";
            //foreach (SheetRowItem sheetRow in sheet.SheetRows)
            //{
            //    if (items_id != "") items_id += ","; items_id += sheetRow.item_id;
            //}
            //await GetItemsInfoBySheetInfo(this.company_id, items_id, new { sheet.supcust_id, sheet.branch_id,branch_position="0" });
             List<dynamic> sheetRows = JsonConvert.DeserializeObject<List<dynamic>>(JsonConvert.SerializeObject(sheet.SheetRows));
            await GetItemsInfoBySheetRowsInfo(this.company_id,  sheetRows,new {  branch_id=sheet.branch_id, supcust_id = sheet.supcust_id });
            SheetTitle = (sheet.sheet_type == SHEET_TYPE.SHEET_BUY_DD_RETURN) ? "采购退货订单" : "采购订单";
            //SheetRowsJson = Newtonsoft.Json.JsonConvert.SerializeObject(sheet.SheetRows);
        }


    
        public override async Task<JsonResult> GetItemsInfo(string companyID,string items_id,string searchStr, bool bGetAttrs, dynamic otherInfo) 
        { 
            string supcust_id = otherInfo.supcust_id;
            string branch_id = otherInfo.branch_id;

            if (supcust_id.IsInvalid()) supcust_id = "-1";
            if (branch_id.IsInvalid()) branch_id = "-1";
            string branchPositionType = otherInfo.branchPositionType;
            bool needQueryPosition = otherInfo.needQueryPosition;
            string queryPosition = otherInfo.queryPosition;
            SQLQueue QQ = new SQLQueue(cmd);


            string sql = @$"
select mu.item_id,ip.item_name,ip.item_no,ip.mum_attributes,mu.unit_no,unit_factor,unit_type,stock.stock_qty,ip.batch_level,rpd.produce_date as virtual_produce_date,
                                   unit_from_s_to_bms ((stock.stock_qty::numeric),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) stock_qty_unit,
                                   mu.buy_price,mu.barcode,s_buy_price,recent_price,coalesce(recent_orig_price,mu.buy_price)  recent_orig_price,item_spec,valid_days,mu.weight
                              from info_item_multi_unit mu
left join (select item_id,(b->>'f1')::real as b_unit_factor,(m->>'f1')::real as m_unit_factor,(s->>'f1')::real as s_unit_factor,
                                                         b->>'f2' as b_unit_no,m->>'f2' as m_unit_no,s->>'f2' as s_unit_no,s->>'f3' as s_buy_price
                                            from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,buy_price)) as json from info_item_multi_unit where company_id={companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
                                                as errr(item_id int, s jsonb,m jsonb,b jsonb)) mu1 on mu.item_id = mu1.item_id

left join company_setting s on s.company_id = mu.company_id
left join (select item_id,produce_date from item_recent_produce_date where company_id={companyID}) rpd on mu.item_id=rpd.item_id
left join (select item_id,sum(COALESCE(stock_qty,0)) stock_qty from stock where company_id = {companyID} and branch_id={branch_id} and item_id in ({items_id})  group by item_id,branch_id) stock on stock.item_id = mu.item_id
 left join 
(
  select * from supplier_recent_prices where company_id = {companyID} and supcust_id = {supcust_id} 
) rp on rp.item_id = mu.item_id and rp.unit_no = mu.unit_no  
 and 
    (
      (s.setting->>'rememberBuyPriceByBranch'='true' and branch_id = {branch_id})  
       or 
      (  (s.setting->>'rememberBuyPriceByBranch' is null or s.setting->>'rememberBuyPriceByBranch'<>'true') and branch_id = 0 )
    )
left join info_item_prop ip on mu.item_id=ip.item_id where mu.company_id={companyID} and mu.item_id in ({items_id}) order by mu.item_id,mu.unit_factor desc;";
            QQ.Enqueue("items", sql);
            sql = @$" 
SELECT array_to_json(array_agg(row_to_json(t))) attr_options FROM
(
    SELECT opt_id, opt_name, attr.attr_id FROM info_attr_opt opt left join info_attribute attr on opt.attr_id = attr.attr_id where opt.company_id ={ companyID} and not attr.spec_opt_in_item order by opt.order_index
) t";
            QQ.Enqueue("attr_options", sql);

            if (bGetAttrs)
            {
                sql = $"select attr_id,attr_name,distinct_stock,order_index from info_attribute where company_id={companyID} order by order_index";
                QQ.Enqueue("attrs", sql);
            }
            if (needQueryPosition)
            {
                sql = @$"select branch_id,branch_position, branch_position_name from info_branch_position
where company_id = {companyID} and branch_id = {branch_id} and type_id = {branchPositionType} and COALESCE(position_status,'1')='1' ORDER BY branch_position;";
                QQ.Enqueue("branch_position", sql);
            }


            List<ExpandoObject> units = null;
            List<ExpandoObject> attributes = null;
            dynamic attrOptions = null;
            dynamic branchPosition = null;
            List<ExpandoObject> batchStock = null;
            Dictionary<string, List<Dictionary<string, string>>> batchStockTotal = new Dictionary<string, List<Dictionary<string, string>>>();
            Dictionary<string, List<Dictionary<string, string>>> batchStockForShow = new Dictionary<string, List<Dictionary<string, string>>>();
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                string tbl = QQ.Dequeue();
                if (tbl == "items")
                {
                    units = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (tbl == "attr_options")
                {
                    dr.Read();
                    string s = CPubVars.GetTextFromDr(dr, "attr_options");
                    if (s != "")
                    {
                        attrOptions = JsonConvert.DeserializeObject(s);
                    }
                }
                else if (tbl == "attrs")
                {
                    attributes = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if(tbl== "branch_position")
                {
                    branchPosition = CDbDealer.Get1RecordFromDr(dr, false);
                }
            }
            QQ.Clear();
            
            Dictionary<string, dynamic> items = new Dictionary<string, dynamic>();
            foreach (dynamic unit in units)
            {
                if (unit.unit_factor == "") continue;
                dynamic item = null;
                if (items.ContainsKey(unit.item_id))
                {
                    item = items[unit.item_id];
                }
                else
                {
                    item = new ExpandoObject();
                    item.item_id = unit.item_id;
                    item.item_name = unit.item_name;
                    item.item_no = unit.item_no;
                    item.item_spec = unit.item_spec;
                    item.valid_days = unit.valid_days;
                    item.virtual_produce_date = unit.virtual_produce_date;
                    item.mum_attributes = unit.mum_attributes;
                    item.stock_qty = unit.stock_qty.Replace("-0", "0");
                    item.stock_qty_unit = (unit.stock_qty_unit).Replace("-0", "0");
                    item.batch_level = unit.batch_level;
                    items.Add((string)item.item_id, item);
                    item.units = new List<dynamic>();
                }
                item.s_buy_price = unit.s_buy_price;
                List<dynamic> itemUnits = item.units;
                string price = "";
                if (unit.recent_price != "") price = unit.recent_price;
                    else if (unit.buy_price != "") price = unit.buy_price;
                
                itemUnits.Add(new { unit.unit_no, unit.unit_factor, unit.unit_type, price, unit.recent_price, unit.recent_orig_price, unit.barcode,unit.weight });
            }
            bool needQueryBatchStock = false;
            foreach(dynamic unit in units)
            {
                if(unit.batch_level!="")
                {
                    needQueryBatchStock = true;
                    break;
                }
            }
            string batchCondi = "";
            if (needQueryBatchStock || (!needQueryPosition && queryPosition!="0") || (needQueryPosition && branchPosition != null))
            {
                if (!needQueryPosition)
                {
                    batchCondi = $" and s.branch_position = {queryPosition}";
                }
                else
                {
                    if(branchPosition != null)
                    {
                        batchCondi = $" and s.branch_position ={branchPosition.branch_position} ";
                    }
                    else
                    {
                        batchCondi = $" and s.branch_position = 0";
                    }
                }
                if (items_id != "")
                {
                    batchCondi += $@" and s.item_id in ({items_id})";
                }
                string batchSql = $@"select COALESCE(s.batch_id,0)::int as batch_id,COALESCE(batch_no,'') as batch_no,COALESCE(SUBSTRING(produce_date::text,1,10),'') as produce_date,COALESCE(stock_qty,0) as stock_qty,s.branch_id,s.branch_position,COALESCE(branch_position_name,'') as branch_position_name,
unit_from_s_to_bms (COALESCE(stock_qty,0)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) as stock_qty_unit,
unit_from_s_to_bms (COALESCE(sell_pend_qty,0)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) as sell_pend_qty_unit,
unit_from_s_to_bms (COALESCE(stock_qty,0)::numeric-COALESCE(sell_pend_qty,0)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) as usable_stock_qty_unit,s.item_id
                    from stock s 
                    left join info_branch_position ibp on ibp.company_id = {companyID} and ibp.branch_id = s.branch_id and ibp.branch_position = s.branch_position
                    left join info_item_batch itb on itb.batch_id = s.batch_id and itb.company_id = s.company_id
                    left join (select item_id,(b->>'f1')::real as b_unit_factor,(m->>'f1')::real as m_unit_factor,(s->>'f1')::real as s_unit_factor,
                                                    b->>'f2' as b_unit_no,m->>'f2' as m_unit_no,s->>'f2' as s_unit_no,s->>'f3' as s_buy_price
                                    from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,buy_price)) as json from info_item_multi_unit where company_id={companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
                                        as errr(item_id int, s jsonb,m jsonb,b jsonb)) mu1 on s.item_id = mu1.item_id
                    where s.company_id = {companyID} and s.branch_id = {branch_id} {batchCondi}";
                batchStock = await CDbDealer.GetRecordsFromSQLAsync(batchSql, cmd);
                bool isShowNegativeStock = otherInfo.isShowNegativeStock;
                if (batchStock.Count > 0)
                {
                    List<Dictionary<string, string>> newData = JsonConvert.DeserializeObject<List<Dictionary<string, string>>>(JsonConvert.SerializeObject(batchStock));
                    foreach(Dictionary<string, string> v in newData)
                    {
                        string key = v["item_id"].ToString();
                        decimal stockQty = CPubVars.ToDecimal(v["stock_qty"]);
                        if (batchStockTotal.ContainsKey(key)) batchStockTotal[key].Add(v);
                        else
                        {
                            batchStockTotal[key] = new List<Dictionary<string, string>>();
                            batchStockTotal[key].Add(v);
                        }
                        if (!isShowNegativeStock && stockQty>0)
                        {
                            if (batchStockForShow.ContainsKey(key)) batchStockForShow[key].Add(v);
                            else
                            {
                                batchStockForShow[key] = new List<Dictionary<string, string>>();
                                batchStockForShow[key].Add(v);
                            }
                        }
                    }
                    if (isShowNegativeStock) batchStockForShow = batchStockTotal;
                }
                
            }
            return new JsonResult(new { result = "OK", items, attrOptions, attributes,branchPosition,batchStockTotal,batchStockForShow });

        }
        public override async Task<JsonResult> GetItemsInfoBySheetRows(string companyID, bool bGetAttrs,List<dynamic> sheetRows,dynamic otherInfo)
        {
            string condi = "";
            string orderBy = "";
            string items_id = "";
            string branchs_id = "";
            string sqlForSheetRow = "";
              string supcust_id = otherInfo.supcust_id;
            string branch_id = otherInfo.branch_id;
           foreach (dynamic sheetRow in sheetRows)
            {
                string rowBranchId = sheetRow.branch_id.ToString() == ""? branch_id : sheetRow.branch_id;
                string rowBranchPosition = sheetRow.branch_position.ToString() == "" ? "0" : sheetRow.branch_position;
                string rowBatchId = sheetRow.batch_id.ToString() == ""? "0" : sheetRow.batch_id;
                if (items_id != "") items_id += ","; 
                items_id += sheetRow.item_id;
                 if (branchs_id != "") branchs_id += ","; 
                branchs_id += rowBranchId;
                if (sqlForSheetRow != "")
                {
                    sqlForSheetRow += $@"or (branch_id = {rowBranchId} and branch_position = {rowBranchPosition} and batch_id = {rowBatchId} and item_id = {sheetRow.item_id})";
                }
                else
                {
                    sqlForSheetRow = $@"(branch_id = {rowBranchId} and branch_position = {rowBranchPosition} and batch_id = {rowBatchId} and item_id = {sheetRow.item_id})";
                }
            }
            if (sqlForSheetRow != "") sqlForSheetRow = "and (" + sqlForSheetRow + ")";
            SQLQueue QQ = new SQLQueue(cmd);
            string sql = @$"
select mu.item_id,ip.item_name,ip.item_no,ip.mum_attributes,mu.unit_no,unit_factor,unit_type,stock.stock_qty,ip.batch_level,rpd.produce_date as virtual_produce_date,branch_position,stock.branch_id,batch_id,
                                   unit_from_s_to_bms ((stock.stock_qty::numeric),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) stock_qty_unit,
                                   mu.buy_price,mu.barcode,s_buy_price,recent_price,coalesce(recent_orig_price,mu.buy_price)  recent_orig_price,item_spec,valid_days 
from info_item_multi_unit mu
left join (select item_id,(b->>'f1')::real as b_unit_factor,(m->>'f1')::real as m_unit_factor,(s->>'f1')::real as s_unit_factor,
                                                         b->>'f2' as b_unit_no,m->>'f2' as m_unit_no,s->>'f2' as s_unit_no,s->>'f3' as s_buy_price
                                            from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,buy_price)) as json from info_item_multi_unit where company_id={companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
                                                as errr(item_id int, s jsonb,m jsonb,b jsonb)) mu1 on mu.item_id = mu1.item_id              
left join company_setting s on s.company_id = mu.company_id
left join (select item_id,branch_id,branch_position,batch_id,stock_qty,sell_pend_qty from stock where company_id = {companyID} {sqlForSheetRow}) stock on stock.item_id = mu.item_id
 left join 
(
  select * from supplier_recent_prices where company_id = {companyID} and supcust_id = {supcust_id} 
) rp on rp.item_id = mu.item_id and rp.unit_no = mu.unit_no  
 and 
    (
      (s.setting->>'rememberBuyPriceByBranch'='true' and rp.branch_id in ({branchs_id}))  
       or 
      (  (s.setting->>'rememberBuyPriceByBranch' is null or s.setting->>'rememberBuyPriceByBranch'<>'true') and rp.branch_id = 0 )
    )
left join info_item_prop ip on mu.item_id=ip.item_id
left join (select item_id,produce_date from item_recent_produce_date where company_id={companyID}) rpd on mu.item_id=rpd.item_id
where mu.company_id={companyID} and mu.item_id in ({items_id})
order by mu.item_id,mu.unit_factor desc;";
            QQ.Enqueue("items", sql);
            sql = @$" 
                    SELECT array_to_json(array_agg(row_to_json(t))) attr_options FROM
                    (
                        SELECT opt_id, opt_name, attr.attr_id FROM info_attr_opt opt left join info_attribute attr on opt.attr_id = attr.attr_id where opt.company_id ={companyID} and not attr.spec_opt_in_item order by opt.order_index
                    ) t";
                        sql = @$" 
SELECT array_to_json(array_agg(row_to_json(t))) attr_options FROM
(
    SELECT opt_id, opt_name, attr.attr_id FROM info_attr_opt opt left join info_attribute attr on opt.attr_id = attr.attr_id where opt.company_id ={ companyID} and not attr.spec_opt_in_item order by opt.order_index
) t";
            QQ.Enqueue("attr_options", sql);

            if (bGetAttrs)
            {
                sql = $"select attr_id,attr_name,distinct_stock,order_index from info_attribute where company_id={companyID} order by order_index";
                QQ.Enqueue("attrs", sql);
            }

            List<ExpandoObject> units = null;
            List<ExpandoObject> attributes = null;
            dynamic attrOptions = null;
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                string tbl = QQ.Dequeue();
                if (tbl == "items")
                {
                    units = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (tbl == "attr_options")
                {
                    dr.Read();
                    string s = CPubVars.GetTextFromDr(dr, "attr_options");
                    if (s != "")
                    {
                        attrOptions = JsonConvert.DeserializeObject(s);
                    }
                }
                else if (tbl == "attrs")
                {
                    attributes = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();
            
            Dictionary<string, dynamic> items = new Dictionary<string, dynamic>();
            foreach (dynamic unit in units)
            {
                if (unit.unit_factor == "") continue;
                dynamic item = null;
                 string key = unit.item_id.ToString() +","+ unit.branch_id.ToString() +"_"+ unit.branch_position.ToString()+"_"+ unit.batch_id.ToString();
                if (items.ContainsKey(key))
                {
                    item = items[key];
                }
                else
                {
                    item = new ExpandoObject();
                    item.item_id = unit.item_id;
                    item.item_name = unit.item_name;
                    item.item_no = unit.item_no;
                    item.branch_id = unit.branch_id;
                    item.branch_position = unit.branch_position;
                    item.batch_id = unit.batch_id;
                    item.item_spec = unit.item_spec;
                    item.valid_days = unit.valid_days;
                    item.virtual_produce_date = unit.virtual_produce_date;
                    item.mum_attributes = unit.mum_attributes;
                    item.stock_qty = unit.stock_qty.Replace("-0", "0");
                    item.stock_qty_unit = (unit.stock_qty_unit).Replace("-0", "0");
                    item.batch_level = unit.batch_level;
                    items.Add(key, item);
                    item.units = new List<dynamic>();
                }
                item.s_buy_price = unit.s_buy_price;
                List<dynamic> itemUnits = item.units;
                string price = "";
                if (unit.recent_price != "") price = unit.recent_price;
                    else if (unit.buy_price != "") price = unit.buy_price;
                
                itemUnits.Add(new { unit.unit_no, unit.unit_factor, unit.unit_type, price, unit.recent_price, unit.recent_orig_price, unit.barcode });
            }
            return new JsonResult(new { result = "OK", items, attrOptions, attributes });
        }

    }
    [ApiController]
    [Route("api/[controller]/[action]")]
    public class BuyOrderSheetController : QueryController
    { 
        public BuyOrderSheetController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<JsonResult> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            var model = new BuyOrderSheetModel(cmd);
            return await PageBaseModel.GetDataItemOptionsJson(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);

        }
        /*
        [HttpGet]
        public async Task<IActionResult> GetItems(string operKey, string query)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            //var model = new SaleSheetModel(cmd);
            // string sql = $"select  distinct item_name as name,i.item_id as id,py_str as zjm,u.barcode as code from info_item_prop i left join info_item_multi_unit u on i.item_id=u.item_id where i.company_id={companyID} and u.company_id={companyID} and (item_name ilike '%{query}%' or py_str ilike '%{query}%' or u.barcode like '%{query}%' or item_name ilike '%{query}%') limit 30";
            string sql = $"select distinct item_name as name,i.item_id as id,py_str as zjm,string_agg(u.barcode,',') as code,item_no from info_item_prop i left join info_item_multi_unit u on i.item_id=u.item_id where i.company_id={companyID} and u.company_id={companyID} and (item_name ilike '%{query}%' or py_str ilike '%{query}%' or u.barcode ilike '%{query}%' or item_no ilike '%{query}%') and (i.status is null or i.status='1') group by item_name,i.item_id,py_str,item_no limit 30";

            dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            return new JsonResult(new { result = "OK", records });
        }*/
        [HttpGet]
        public async Task<IActionResult> GetItems(string operKey, string query)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            //var model = new SaleSheetModel(cmd);
            // string sql = $"select  distinct item_name as name,i.item_id as id,py_str as zjm,u.barcode as code from info_item_prop i left join info_item_multi_unit u on i.item_id=u.item_id where i.company_id={companyID} and u.company_id={companyID} and (item_name ilike '%{query}%' or py_str ilike '%{query}%' or u.barcode like '%{query}%' or item_name ilike '%{query}%') limit 30";
            string barcodeRight = "";
            if (query.Length >= 6)
                barcodeRight = "";

            string brcodeCondi = "";
            if (query.Length >= 4 && CPubVars.IsNumeric(query))
            {
                brcodeCondi = $" or u.barcode like '%{query}{barcodeRight}' or i.mum_attributes::text ilike '%{query}%'";
            }
            string flexStr = CPubVars.GetFlexLikeStr(query);
            string py_flexStr = CPubVars.GetPyStrFlexLikeStr(query);
            //if (query.Length >= 3) py_flexStr = flexStr;
            string sql = $"select distinct item_name as name,i.item_id as id,py_str as zjm,string_agg(u.barcode,',') as code,item_no from info_item_prop i left join info_item_multi_unit u on i.item_id=u.item_id where i.company_id={companyID} and u.company_id={companyID} and (item_name ilike '%{flexStr}%' or py_str ilike '%{py_flexStr}%'  or item_no ilike '%{query}%' {brcodeCondi}) and (i.status is null or i.status='1') group by item_name,i.item_id,py_str,item_no limit 30";

            dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            return new JsonResult(new { result = "OK", records });
        }
        [HttpGet]
        public async Task<IActionResult> GetUnits(string operKey, string item_id, string query)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            // var model = new SaleSheetModel();
            string sql = $"select  distinct unit_no,unit_factor from info_item_multi_unit where company_id={companyID} and item_id={item_id} and unit_no like '%{query}%' limit 30";

            dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            //string items=Newtonsoft.Json.JsonConvert.SerializeObject(rec); 
            return new JsonResult(new { result = "OK", records });
        }
        public class test
        {
            public string id = "", name = "", tt = "";
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody] SheetBuyOrder sheet)  //[FromBody] dynamic sheet)
        {
            var s = Newtonsoft.Json.JsonConvert.SerializeObject(sheet);
            sheet.Init();
            string msg = await sheet.Save(cmd);
            string result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no });
        }
        // 测试存储方法 TODO DELETE
        [HttpPost]
        public async Task<IActionResult> SaveAsSaleOrderSheet([FromBody] SheetBuyOrder oriSheet)  //[FromBody] dynamic sheet)
        {
            
            SheetSaleOrder sheet = JsonConvert.DeserializeObject<SheetSaleOrder>(JsonConvert.SerializeObject(oriSheet));
            // SheetBuyOrder sheet = new SheetBuyOrder();
            // sheet.SheetRows = oriSheet.SheetRows;
            // TODO 替换为client_id对应的company_id
            string operKey = sheet.OperKey;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            sheet.Init();
            sheet.SYNCHRONIZE_SHEETS = true;
            // sheet.company_id = "";
            // TODO 操作员ID 暂定为1
            sheet.OperID = "1";
            // TODO 客户 此处为分销商 client
            string resellerInfoSql = $"select * from rs_seller where reseller_company_id={companyID} and supplier_id = {oriSheet.supcust_id}";
            dynamic resellerInfo = await CDbDealer.GetRecordsFromSQLAsync(resellerInfoSql, cmd);
            
            sheet.company_id = (string)resellerInfo[0].company_id;
            sheet.supcust_id = (string)resellerInfo[0].client_id;

            sheet.senders_id = "";
            sheet.senders_name = "";
            sheet.branch_id = "";
            sheet.sheet_id = "";
            sheet.sheet_no = "";
            sheet.sheet_type = SHEET_TYPE.SHEET_SALE_DD;
            sheet.maker_id = "1";
            sheet.maker_name = "";

            // TODO delete

            foreach (SheetRowSaleOrder row in sheet.SheetRows)
            {
                string itemId = row.item_id;
                string itemIdSql = $"select rs_mum_id from info_item_prop where item_id = {itemId}";
                dynamic mumItemId = await CDbDealer.Get1RecordFromSQLAsync(itemIdSql, cmd);
                string querySql = $"select item_id,item_class,other_class from info_item_prop where item_id = {mumItemId.rs_mum_id}";
                dynamic itemInfo = await CDbDealer.Get1RecordFromSQLAsync(querySql, cmd);
                row.item_id = itemInfo.item_id;
                row.classId = itemInfo.item_class;
                row.other_class = itemInfo.other_class;

            }

            string msg = await sheet.Save(cmd);
            string result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no });
        }

        [HttpPost]
        public async Task<IActionResult> SaveAndApprove([FromBody] SheetBuyOrder sheet)
        {
            sheet.Init();
            string msg = await sheet.SaveAndApprove(cmd);
            string result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time,sheet.approve_time });
        }

        [HttpPost]
        public async Task<IActionResult> Red([FromBody] dynamic data)
        {
            string sheet_id = data.sheet_id;
            string operKey = data.operKey;
            string redBrief = data.redBrief;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetBuyOrder sheet = new SheetBuyOrder(SHEET_RETURN.EMPTY, LOAD_PURPOSE.SHOW);
            string msg = await sheet.Red(cmd, companyID, sheet_id, operID,redBrief);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time });
        }

        [HttpPost]
        public async Task<IActionResult> AppendBrief([FromBody] dynamic data)
        {
            string sheetID = data.sheetID;
            string newBrief = data.newBrief;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetBuyOrder sheet = new SheetBuyOrder(SHEET_RETURN.EMPTY, LOAD_PURPOSE.APPROVE);

            string msg = await sheet.AppendBrief(cmd, companyID, sheetID, newBrief);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, });
        }
        [HttpGet]
        public async Task<IActionResult> GetItemsInfo(string operKey, string items_id,string searchStr, string supcust_id, string branch_id,bool bGetAttrs,string branchPositionType,bool isShowNegativeStock,bool needQueryPosition,string queryPosition)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            BuyOrderSheetModel sheetModel = new BuyOrderSheetModel(cmd);
            var rr = await sheetModel.GetItemsInfo( companyID, items_id, searchStr, bGetAttrs, new { supcust_id, branch_id ,branchPositionType,isShowNegativeStock,needQueryPosition,queryPosition});
            return rr;
        }


        [HttpPost]
        public async Task<IActionResult> Delete([FromBody] dynamic data)
        {
            string sheet_id = data.sheet_id;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetBuyOrder sheet = new SheetBuyOrder(SHEET_RETURN.EMPTY, LOAD_PURPOSE.SHOW);

            string msg = await sheet.Delete(cmd, companyID, sheet_id, operID);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time });
        }

        [HttpGet]
        public async Task<IActionResult> GetSheetToPrint(string operKey, string sheet_id,bool smallUnitBarcode, string printTemplate)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetBuyOrder sheet = new SheetBuyOrder(SHEET_RETURN.EMPTY, LOAD_PURPOSE.SHOW);
            cmd.ActiveDatabase = "";
            await sheet.Load(cmd, companyID, sheet_id);
 
			dynamic dTemplate = null;
			if (printTemplate.IsValid())
			{
				dTemplate = JsonConvert.DeserializeObject(printTemplate);
			}
			await sheet.LoadInfoForPrint(cmd, smallUnitBarcode, true, dTemplate);


			string msg = "";
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, sheet });
        }

    }
}