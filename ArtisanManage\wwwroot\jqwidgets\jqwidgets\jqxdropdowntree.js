﻿/*
jQWidgets v5.6.0 (2018-Feb)
Copyright (c) 2011-2017 jQWidgets.
License: https://jqwidgets.com/license/
*/

(function (a) {
    a.jqx.jqxWidget("jqxDropDownTree", "", {});
    a.extend(a.jqx._jqxDropDownTree.prototype, {

        defineInstance: function () {
            var b = { borderShape: "bottomLine", mumSelectable: false, checkboxes: false, url: null, source: null, disabled: false, readonly: false, width: null, height: null, arrowSize: 19, enableHover: true, openDelay: 250, closeDelay: 300, animationType: "default", enableBrowserBoundsDetection: false, dropDownHorizontalAlignment: "left", dropDownVerticalAlignment: "bottom", popupZIndex: 2000, dropDownContainer: "default", autoOpen: false, rtl: false, initContent: null, dropDownWidth: null, dropDownHeight: 300, focusable: true, template: "default", touchMode: false, aria: { "aria-disabled": { name: "disabled", type: "boolean" } }, events: ["open", "close", "opening", "closing"], placeHolder: null };
            if (this === a.jqx._jqxDropDownTree.prototype) { return b } a.extend(true, this, b); return b
        }, createInstance: function (j) {
            var g = this; if (!g.width) {
              //  g.width = 200
            }
            //if (!g.height) { g.height = 25 }
            g.isanimating = false;
            var c = a("<div style='background-color: transparent; -webkit-appearance: none; outline: none; width:100%; height: 100%; padding: 0px; margin: 0px; border: 0px; position: relative;'><div id='dropDownButtonWrapper' style='outline: none; background-color: transparent; border: none; float: left; width:100%; height: 100%; position: relative;'><input id='dropDownButtonContent' autocomplete='off' unselectable='on' style='outline: none; background-color:transparent; border: none; float: left; position: relative;'/><div id='dropDownButtonArrow' unselectable='on'  style='background-color: transparent; border: none; float: right; position: relative;'><div unselectable='on'></div></div></div></div>");
            if (g.host.attr("tabindex")) {
                c.attr("tabindex", g.host.attr("tabindex")); g.host.removeAttr("tabindex");
            } else {
                c.attr("tabindex", 0);
            }
            //if (!g.focusable) {
                c.removeAttr("tabIndex");
           // }
            a.jqx.aria(this);

            g.host.attr("role", "button");

            g.touch = a.jqx.mobile.isTouchDevice(); g.dropDownButtonStructure = c;
            g.host.append(c); g.dropDownButtonWrapper = g.host.find("#dropDownButtonWrapper"); g.firstDiv = g.dropDownButtonWrapper.parent(); g.dropDownButtonArrow = g.host.find("#dropDownButtonArrow"); g.arrow = a(g.dropDownButtonArrow.children()[0]); g.dropDownButtonContent = g.host.find("#dropDownButtonContent");
            g.dropDownButtonContent.addClass(g.toThemeProperty("jqx-dropdownlist-content"));
            g.dropDownButtonWrapper.addClass(g.toThemeProperty("jqx-disableselect"));
            if (g.rtl) { g.dropDownButtonContent.addClass(g.toThemeProperty("jqx-rtl")) }
            var m = this; if (g.host.parents()) { g.addHandler(g.host.parents(), "scroll.dropdownbutton" + g.element.id, function (e) { var n = m.isOpened(); if (n) { m.close() } }) }
            g.addHandler(g.dropDownButtonWrapper, "selectstart", function () { return false });
            g.dropDownButtonWrapper[0].id = "dropDownButtonWrapper" + g.element.id; g.dropDownButtonArrow[0].id = "dropDownButtonArrow" + g.element.id; g.dropDownButtonContent[0].id = "dropDownButtonContent" + g.element.id;
            //var m = this;
            g.propertyChangeMap.disabled = function (e, o, n, p) {
                if (p) {
                    e.host.addClass(m.toThemeProperty("jqx-dropdownlist-state-disabled"));
                    e.host.addClass(m.toThemeProperty("jqx-fill-state-disabled"));
                    e.dropDownButtonContent.addClass(m.toThemeProperty("jqx-dropdownlist-content-disabled"))
                } else {
                    e.host.removeClass(m.toThemeProperty("jqx-dropdownlist-state-disabled")); e.host.removeClass(m.toThemeProperty("jqx-fill-state-disabled")); e.dropDownButtonContent.removeClass(m.toThemeProperty("jqx-dropdownlist-content-disabled"))
                }
                a.jqx.aria(e, "aria-disabled", e.disabled)
            };
            if (g.disabled) {
                g.host.addClass(g.toThemeProperty("jqx-dropdownlist-state-disabled"));
                g.host.addClass(g.toThemeProperty("jqx-fill-state-disabled"));
                g.dropDownButtonContent.addClass(g.toThemeProperty("jqx-dropdownlist-content-disabled"))
            }
            if (g.readonly || g.disabled) g.arrow.hide(); else g.arrow.show()
            var i = g.toThemeProperty("jqx-rc-all") + " " + g.toThemeProperty("jqx-widget") + " " + g.toThemeProperty("jqx-widget-content") + " " + g.toThemeProperty("jqx-dropdownlist-state-normal");// g.toThemeProperty("jqx-fill-state-normal") + " " +
            g.host.addClass(i);
            
            g.arrow.addClass(g.toThemeProperty("jqx-icon-arrow-down")); g.arrow.addClass(g.toThemeProperty("jqx-icon"));
            if (g.template) {
                g.host.addClass(g.toThemeProperty("jqx-" + g.template))
            }

            if (g.borderShape == "bottomLine" || g.borderShape == "none") {
                g.host.css("border-left-style", "none");
                g.host.css("border-right-style", "none");
                g.host.css("border-top-style", "none");
                g.host.css("border-radius", "0");
            }
            if (g.borderShape == "none") {
                g.host.css("border-bottom-style", "none");
            }
            g._setSize();
            g.render();
            if (a.jqx.browser.msie && a.jqx.browser.version < 8) {
                g.container.css("display", "none");
                if (g.host.parents(".jqx-window").length > 0) {
                    var l = g.host.parents(".jqx-window").css("z-index"); b.css("z-index", l + 10); g.container.css("z-index", l + 10)
                }
            }
            if (g.placeHolder) {
                g.dropDownButtonContent.attr('placeholder', g.placeHolder);
            }
        },
        getTreePath: function () {

        },
        createDropDown: function () {
            var g = this;
            if (!g.popupContent) {
                // g.popupContent = g.host.children();
                //  g.host.attr("role", "button");

                //g.popupContent = a("<div>" + encodeURI('加载中...') + "</div>"); g.popupContent.css("display", "block");
                g.popupContent = a("<div>" + '...................' + "</div>");
                g.popupContent.css("display", "block");
                g.popupContent.css("overflow-y", "scroll");
                g.popupContent.css("border-color", "transparent");
                g.popupContent.css("background-color", "#f4f4f4");
                //g.element.innerHTML = ""; 


                var k = this;
                g.addHandler(g.host, "loadContent", function (e) {
                    k._arrange();
                });
                try {
                    var f = "dropDownButtonPopup" + g.element.id;
                    var d = a(a.find("#" + f)); if (d.length > 0) {
                        //  d.remove();
                    }
                    a.jqx.aria(this, "aria-haspopup", true);
                    a.jqx.aria(this, "aria-owns", f);
                    var b = a("<div class='dropDownButton' style='overflow: hidden; border-color:#ccc; left: -1000px; top: -1000px; position: absolute;' id='dropDownButtonPopup" + g.element.id + "'></div>");
                    b.addClass(g.toThemeProperty("jqx-widget-content")); b.addClass(g.toThemeProperty("jqx-dropdownbutton-popup")); b.addClass(g.toThemeProperty("jqx-popup")); b.addClass(g.toThemeProperty("jqx-rc-all"));
                    if (g.dropDownContainer !== "element") {
                        b.css("z-index", g.popupZIndex)
                    }
                    if (a.jqx.browser.msie) {
                        b.addClass(g.toThemeProperty("jqx-noshadow"))
                    }
                    g.popupContent.appendTo(b);
                    if (g.dropDownContainer === "element") {
                        b.appendTo(g.host);
                    } else {
                        b.appendTo(document.body);
                    } g.container = b;
                    g.container.css("visibility", "hidden");
                } catch (h) { }
            }
        },
        trySelect: function (way) {
            var g = this;
            if (g.checkboxes) {
                // if (way == 'enter') {
                g.close();
                //}
            } else {
                var slt = g.popupContent.jqxTree('getSelectedItem');
                if (slt) {
                    var items = g.popupContent.jqxTree('getItems');
                    var isMum = false;
                    for (var i = 0; i < items.length; i++) {
                        var c = items[i];
                        if (c.parentItem == slt) {
                            isMum = true;
                        }
                    }
                    var curNode = slt; var path = "";
                    for (var i = 0; i < 10; i++) { 
                        path = curNode.value + "/" + path;
                        curNode = curNode.parentItem;
                        if (curNode == null) {
                            break;
                        }
                    }
                    path = "/" + path;
                    if (!(isMum && !g.mumSelectable)) {
                        g.value = { value: slt.value, label: slt.label };
                        g.m_treePath = path;
                        g.dropDownButtonContent[0].value = slt.label;
                        g.dropDownButtonContent[0].focus();
                        g.close();
                    }
                }
            }
        },
        treePath: function (path) {
            var bInvalidP = (path != null && typeof (path) === "object" && (!path.label || !path.value));
            if (arguments.length == 0 || bInvalidP) {
                var $input = $(this.element).find('input')
                if (!$input.val()) {
                    return ""
                } 
                return this.m_treePath; 
            }
            else {
                this.m_treePath = path;
            }
        },
        createTree: function (bFilter) {
            var g = this;
            if (g.treeCreated) {
                if (bFilter) {
                    g.filterNode();
                }
                return;
            }
            var fun = function (records) {
                if (records) {
                    g.source = records;
                }
                var treeSource =
                    {
                        datatype: 'json',
                        /* datafields: [
                             { name: 'v' },
                             { name: 'pv' },
                             { name: 'l' },
                             { name: 'o' }
                         ],
                         id: 'v',*/
                        localdata: g.source
                    };

                var dataAdapter = new $.jqx.dataAdapter(treeSource);
                dataAdapter.dataBind();
                var ds = dataAdapter.getRecordsHierarchy('v', 'pv', 'items', [{ name: 'l', map: 'label' }, { name: 'v', map: 'value' }, { name: 'z', map: 'z' }]);
                g.popupContent.jqxTree({ source: ds, width: g.dropDownWidth, height: g.dropDownHeight, checkboxes: g.checkboxes });
                var vvv = g.popupContent.jqxTree('getItems');
                g.popupContent.on('select', function (event) {
                    var args = event.args;
                    if (args.type == 'mouse') {
                        g.trySelect('mouse');
                    }
                });
                g.popupContent.on('checkChange', function (event) {
                    g.updateCheckValue();
                });

                g.popupContent.jqxTree('expandAll');
                g.treeCreated = true;
                if (bFilter) {
                    g.filterNode();
                }
                /*g.popupContent.jqxTree("onKeyDown", function (k) {
                    if (k == 13) {
                        var slt = g.popupContent.jqxTree('getSelectedItem');
                        if (slt) {

                            g.close();
                        }
                    }
                });*/


            };
            if (g.source) {
                fun();
            }
            else if (g.url) {
                $.ajax({
                    // url: '../../../ajax.html',
                    url: g.url,
                    type: 'GET',
                    contentType: 'application/json',
                    // data: JSON.stringify({ uid: uid, pwd: pwd }),
                    //data: JSON.stringify({}),

                    success: function (res) {
                       // var rec = JSON.parse(data); 
                        var b = res.data
                        if (!b)
                            b = JSON.parse(res);

                        fun(b);
                    }
                });
            }

        },
        updateCheckValue: function () {
            var g = this;
            if (!g.checkboxes) return;
            if (!g.popupContent) return;
            var items = g.popupContent.jqxTree('getItems');
            var lbs = '';
            var vs = '';
            var ps = ''; // 增加记录values的变量vs和记录paths的变量ps    
            
            for (var i = 0; i < items.length; i++) {
                var node = items[i];
                if (node.checked) {
                    var c = node; var pc = false; var curNode = c; var path = "";
                    while (c.parentItem) {
                        if (c.parentItem.checked) {
                            pc = true;
                            break;
                        }
                        c = c.parentItem;
                    }
                    if (!pc) {
                        while (curNode) {
                            path = curNode.value + "/" + path;
                            curNode = curNode.parentItem;
                            
                        }
                        path = "/" + path;
                        if (lbs != '') { lbs += ','; }
                        if (vs != '') { vs += ','; }
                        if (ps != '') { ps += ','; }
                        lbs += node.label;
                        vs += node.value;
                        ps += path;

                    }
                }
            }
            g.m_treePath = ps;  // 不设置无法通过treePath取到path
            g.value = { value: vs, label: lbs };    // 不设置的话无法通过val正常取到dataItem的value
            g.dropDownButtonContent[0].value = lbs;
        },
        getCheckedItems: function () {
            var g = this;
            if (!g.popupContent) return null;
            return g.popupContent.jqxTree('getCheckedItems');
        },
        getSelectedItem: function () {
            var g = this;
            if (!g.popupContent) return null;
            if (g.checkboxes) return null;
            return g.popupContent.jqxTree('getSelectedItem');
        },
        setCheckedItems: function (items) {
            var g = this;
            if (!g.popupContent) return null;
            for (var i = 0; i < nodes.length; i++) {
                var n = nodes[i];
                for (var j = 0; j < items.length; j++) {
                    var item = items[j];
                    if (item == n.id) {
                        g.popupContent.jqxTree('checkItem', n, true);
                    }
                }
            }

        },
        filterNode: function () {
            var g = this;
            var f = null;
            var t = g.dropDownButtonContent[0].value;
            var arr = t.split(',');
            var nodes = g.popupContent.jqxTree("getItems");
            for (var i = 0; i < nodes.length; i++) {
                var n = nodes[i];
                n.isVisible = "n";
            }
            for (var i = 0; i < nodes.length; i++) {
                var n = nodes[i]; var mat = false;
                for (var j = 0; j < arr.length; j++) {
                    var ct = arr[j];
                    if (n.label.toLowerCase().indexOf(ct.toLowerCase()) >= 0) {
                        mat = true;
                    }
                    if (n.z && n.z.toLowerCase().indexOf(ct.toLowerCase()) >= 0) {
                        mat = true;
                    }
                }
                if (mat) {
                    var c = n;
                    if (!f) {
                        f = c;
                    }
                    c.isVisible = "y";
                    while (c.parentItem) {
                        c.parentItem.isVisible = "y";
                        c = c.parentItem;
                    }
                }
            }
            for (var i = 0; i < nodes.length; i++) {
                var n = nodes[i];
                if (n.isVisible === "y") {
                    n.element.style.display = "block";
                } else if (n.isVisible === "n") {
                    n.element.style.display = "none";
                }
            }
            if (f) {
                g.popupContent.jqxTree('selectItem', f);
            }
        },
        open: function (bFilter) {
            var i = this; var o = this;
            if (!i.isOpened()) {
                a.jqx.aria(this, "aria-expanded", true);
                i.createDropDown();
                if ((i.dropDownWidth === null || i.dropDownWidth === "auto") && i.width !== null && i.width.indexOf && i.width.indexOf("%") !== -1) {
                    var q = i.host.width(); i.container.width(parseInt(q))
                }
                o._raiseEvent("2"); var c = i.popupContent;
                var f = a(window).scrollTop(); var g = a(window).scrollLeft();
                var m = parseInt(i._findPos(i.host[0])[1]) + parseInt(i.host.outerHeight()) - 1 + "px";
                var e, p = parseInt(Math.round(i.host.coord(true).left)); e = p + "px";
                var u = a.jqx.mobile.isSafariMobileBrowser() || a.jqx.mobile.isWindowsPhone();
                i.ishiding = false; i.tempSelectedIndex = i.selectedIndex;
                if ((u !== null && u)) {
                    e = a.jqx.mobile.getLeftPos(i.element); m = a.jqx.mobile.getTopPos(i.element) + parseInt(i.host.outerHeight()); if (a("body").css("border-top-width") !== "0px") { m = parseInt(m) - i._getBodyOffset().top + "px" } if (a("body").css("border-left-width") !== "0px") { e = parseInt(e) - i._getBodyOffset().left + "px" }
                }
                c.stop();
               // i.host.addClass(i.toThemeProperty("jqx-dropdownlist-state-selected"));
               // i.host.addClass(i.toThemeProperty("jqx-fill-state-pressed"));
                i.arrow.addClass(i.toThemeProperty("jqx-icon-arrow-down-selected"));
                var h = false;
                if (a.jqx.browser.msie && a.jqx.browser.version < 8) { h = true }
                if (h) {
                    i.container.css("display", "block")
                }
                i.container.css("left", e); i.container.css("top", m);
                var d = true; var b = false;
                var s = function () {
                    if (i.dropDownHorizontalAlignment === "right" || i.rtl) { var v = i.container.width(); var t = Math.abs(v - i.host.width()); if (v > i.host.width()) { i.container.css("left", parseInt(Math.round(p)) - t + "px") } else { i.container.css("left", parseInt(Math.round(p)) + t + "px") } }
                };
                s.call(this);
                if (i.dropDownVerticalAlignment === "top") {
                    var r = c.height(); b = true; i.container.height(c.outerHeight()); c.addClass(this.toThemeProperty("jqx-popup-up")); var l = parseInt(i.host.outerHeight()); var k = parseInt(m) - Math.abs(r + l); if (i.interval) { clearInterval(i.interval) } i.interval = setInterval(function () { if (c.outerHeight() !== i.container.height()) { i.container.height(c.outerHeight()); var v = parseInt(m) - Math.abs(c.height() + l); i.container.css("top", v) } }, 50); c.css("top", 23); i.container.css("top", k)
                }
                if (i.enableBrowserBoundsDetection) {
                    var j = i.testOffset(c, { left: parseInt(i.container.css("left")), top: parseInt(m) }, parseInt(i.host.outerHeight())); if (parseInt(i.container.css("top")) !== j.top) { b = true; i.container.height(c.outerHeight()); c.css("top", 23); if (i.interval) { clearInterval(i.interval) } i.interval = setInterval(function () { if (c.outerHeight() !== o.container.height()) { var t = o.testOffset(c, { left: parseInt(i.container.css("left")), top: parseInt(m) }, parseInt(i.host.outerHeight())); i.container.css("top", t.top); i.container.height(c.outerHeight()) } }, 50) } else { c.css("top", 0) } i.container.css("top", j.top); if (parseInt(i.container.css("left")) !== j.left) { i.container.css("left", j.left) }
                }
                if (i.animationType === "none") {
                    i.container.css("visibility", "visible"); a.data(document.body, "openedJQXButtonParent", o); a.data(document.body, "openedJQXButton" + i.element.id, c); c.css("margin-top", 0); c.css("opacity", 1); i._raiseEvent("0"); s.call(o)
                } else {
                    i.container.css("visibility", "visible"); var n = c.outerHeight(); o.isanimating = true;
                    if (i.animationType === "fade") {
                        c.css("margin-top", 0); c.css("opacity", 0); c.animate({ opacity: 1 }, i.openDelay, function () {
                            a.data(document.body, "openedJQXButtonParent", o);
                            a.data(document.body, "openedJQXButton" + o.element.id, c); o.ishiding = false; o.isanimating = false; o._raiseEvent("0")
                        }); s.call(o)
                    } else {
                        c.css("opacity", 1); if (b) { c.css("margin-top", n) } else { c.css("margin-top", -n) } s.call(o);
                        if (b) {
                            c.animate({ "margin-top": 0 }, i.openDelay, function () { a.data(document.body, "openedJQXButtonParent", o); a.data(document.body, "openedJQXButton" + o.element.id, c); o.ishiding = false; o.isanimating = false; o._raiseEvent("0") });
                        } else { c.animate({ "margin-top": 0 }, i.openDelay, function () { a.data(document.body, "openedJQXButtonParent", o); a.data(document.body, "openedJQXButton" + o.element.id, c); o.ishiding = false; o.isanimating = false; o._raiseEvent("0") }) }
                    }
                }
                if (!b) {
                    i.host.addClass(i.toThemeProperty("jqx-rc-b-expanded")); i.container.addClass(i.toThemeProperty("jqx-rc-t-expanded"));
                } else {
                    i.host.addClass(i.toThemeProperty("jqx-rc-t-expanded")); i.container.addClass(i.toThemeProperty("jqx-rc-b-expanded"))
                } if (i.focusable) {
                    // i.firstDiv.focus();
                    //setTimeout(function () { o.firstDiv.focus() }, 10)
                }
                i.container.addClass(i.toThemeProperty("jqx-fill-state-focus")); i.host.addClass(o.toThemeProperty("jqx-dropdownlist-state-focus")); i.host.addClass(o.toThemeProperty("jqx-fill-state-focus"))
            }
            i.createTree(bFilter);

            // g.findNode("frap");
            // g.popupContent.jqxTree('focus');
        },
        setContent: function (b) {
            // var c = this; c.dropDownButtonContent.children().remove(); c.dropDownButtonContent[0].innerHTML = ""; c.dropDownButtonContent.append(b)
        },
        val: function (b) {
            var bInvalidB = (b != null && typeof (b) === "object" && (b.label == undefined || b.value == undefined));
            if (arguments.length === 0 || bInvalidB) {//get value 
                var $input = $(this.element).find('input')
                if (!$input.val()) {
                    return ""
                } 
                return this.value;
                //return b.dropDownButtonContent.text(); 
            }
            else {
                this.value = b;
                this.dropDownButtonContent[0].value = b.label;
                if (b.value === '' && b.label === '' && this.popupContent) {
                    this.clearSelection()
                }
                
                //b.dropDownButtonContent.html(c);
            }
        },
        clearSelection: function () {
            var g = this;
            if (!g.popupContent) return null;
            var items = g.popupContent.jqxTree('getItems');
            for (var i = 0; i < items.length; i++) {
                var n = items[i];
                g.popupContent.jqxTree('checkItem', n, false);
              
            }
        },
        getContent: function () {
            //  var b = this; if (b.dropDownButtonContent.children().length > 0) { return b.dropDownButtonContent.children() } return b.dropDownButtonContent.text()
        },
        _setSize: function () {
            var c = this; if (c.width !== null && c.width.toString().indexOf("px") !== -1) { c.host[0].style.width = c.width } else { if (c.width !== undefined && !isNaN(c.width)) { c.host[0].style.width = parseInt(c.width) + "px" } }
            if (c.height !== null && c.height.toString().indexOf("px") !== -1) {
                c.host[0].style.height = c.height
            } else { if (c.height !== undefined && !isNaN(c.height)) { c.host[0].style.height = parseInt(c.height) + "px" } } var d = false; if (c.width !== null && c.width.toString().indexOf("%") !== -1) { d = true; c.host.width(c.width) }
            if (c.height !== null && c.height.toString().indexOf("%") !== -1) {
                d = true; c.host.height(c.height)
            }
            var b = this; if (d) { c.refresh(false) }
            a.jqx.utilities.resize(c.host, function () { b._arrange() })
        }, isOpened: function () {
            var c = this; var b = a.data(document.body, "openedJQXButton" + c.element.id); if (b !== null && b != undefined && b === c.popupContent) { return true } return false
        }, focus: function () {
            var c = this; try { c.host.focus() } catch (b) {
            }
        },
        render: function () {
            var d = this; d.removeHandlers(); var b = this; var c = false;
            if (!d.touch) {
                d.addHandler(d.host, "mouseenter", function () {
                    if (!b.disabled && b.enableHover) {
                        c = true;
                        //b.host.addClass(b.toThemeProperty("jqx-dropdownlist-state-hover"));
                        //b.host.addClass(b.toThemeProperty("jqx-fill-state-hover"))
                        b.arrow.addClass(b.toThemeProperty("jqx-icon-arrow-down-hover")); 
                    }
                });
                d.addHandler(d.host, "mouseleave", function () {
                    if (!b.disabled && b.enableHover) { b.host.removeClass(b.toThemeProperty("jqx-dropdownlist-state-hover")); b.host.removeClass(b.toThemeProperty("jqx-fill-state-hover")); b.arrow.removeClass(b.toThemeProperty("jqx-icon-arrow-down-hover")); c = false }
                })
            }
            if (b.autoOpen) {
                d.addHandler(d.host, "mouseenter", function () {
                    var e = b.isOpened(); if (!e && b.autoOpen) { b.open(); b.host.focus() }
                });
                d.addHandler(a(document), "mousemove." + b.element.id, function (h) { var g = b.isOpened(); if (g && b.autoOpen) { var l = b.host.coord(); var k = l.top; var j = l.left; var i = b.container.coord(); var e = i.left; var f = i.top; canClose = true; if (h.pageY >= k && h.pageY <= k + b.host.height()) { if (h.pageX >= j && h.pageX < j + b.host.width()) { canClose = false } } if (h.pageY >= f && h.pageY <= f + b.container.height()) { if (h.pageX >= e && h.pageX < e + b.container.width()) { canClose = false } } if (canClose) { b.close() } } })
            }
            d.addHandler(d.dropDownButtonWrapper, "mousedown", function (f) {
                if (!b.disabled) {
                    var e = (b.container && b.container.css("visibility") === "visible");
                    if (!b.isanimating) {
                        if (e) {
                            b.close();
                            return false
                        } else {
                            b.open();
                            if (!b.focusable) { if (f.preventDefault) { f.preventDefault() } }
                        }
                    }
                }
            });
            if (d.touch) {
                d.addHandler(a(document), a.jqx.mobile.getTouchEventName("touchstart") + "." + d.element.id, b.closeOpenedDropDown, { me: this, popup: d.container, id: d.element.id })
            }
            d.addHandler(a(document), "mousedown." + d.element.id, b.closeOpenedDropDown, {
                me: this, popup: d.container, id: d.element.id
            });
            d.addHandler(d.host, "keydown", function (f) { 
                if (d.disabled === true) {
                    return false;
                }
                var e = (b.container && b.container.css("visibility") === "visible");
                if (b.host.css("display") === "none") {
                    return true
                }
                if (!d.popupContent) return;
                var slt = d.popupContent.jqxTree('getSelectedItem');
                if (f.keyCode === 13) {
                    if (!b.isanimating) {
                        if (e) {
                            d.trySelect('enter');
                        }
                    }
                }
                if (f.keyCode === 115) { if (!b.isanimating) { if (!b.isOpened()) { b.open() } else { if (b.isOpened()) { b.close() } } } return false }

                if (b.host.css("display") === "block") {
                    var nextItem = null;
                    var items = d.popupContent.jqxTree('getItems');
                    if (f.keyCode === 38) {
                        if (!b.isOpened()) { b.open() }
                        if (slt) {
                            nextItem = d.popupContent.jqxTree('getPrevItem', slt);
                        }
                        else if (items && items.length > 0) {
                            nextItem = items[items.length - 1];
                        }
                        d.popupContent.jqxTree('selectItem', nextItem);
                        d.popupContent.jqxTree('ensureVisible', nextItem.element);
                    } else if (f.keyCode === 40) {
                        if (!b.isOpened()) { b.open() }
                        if (slt) {
                            nextItem = d.popupContent.jqxTree('getNextItem', slt);
                        }
                        else if (items && items.length > 0) {
                            nextItem = items[0];
                        }
                        d.popupContent.jqxTree('selectItem', nextItem);
                        d.popupContent.jqxTree('ensureVisible', nextItem.element);
                    }
                    else if (f.keyCode == 32 && b.isOpened() )//space
                    {
                        if (d.checkboxes) {
                            d.popupContent.jqxTree('checkItem', slt, !slt.checked);
                            return false;
                        } else {
                            // single select without checkboxes
                            d.popupContent.jqxTree('selectItem', slt,'mouse');
                            return false;
                        }
                    }
                }

                if (f.keyCode === "27") { if (!b.ishiding) { b.close(); if (b.tempSelectedIndex !== undefined) { b.selectIndex(b.tempSelectedIndex) } } }
            });
            d.addHandler(d.host, "keyup", function (f) {
                if (d.disabled === true) {
                    return false;
                }
                //if (!d.popupContent) return;
               // var slt = d.popupContent.jqxTree('getSelectedItem');
                switch (f.keyCode) {
                    case 40: case 38: case 16: case 17: case 18: break; case 9: case 13: case 32:
                        if (!d.isOpened()) { return }
                        // this.select(c, this, "keyboard");
                        break;

                    case 27: if (!d.isOpened()) { return } d.close(); break;
                    default:
                        if (d.timer) { clearTimeout(this.timer) }
                        if (!d.treeCreated) {
                            d.open(true);
                        }
                        d.timer = setTimeout(function () {
                            if (d.treeCreated) {
                                // if (!d.isOpened()) {
                                d.open(true);
                                //}
                            }
                        }, 300);
                }
                f.stopPropagation(); f.preventDefault();
            });
            d.addHandler(d.firstDiv, "focus", function () {
                b.host.addClass(b.toThemeProperty("jqx-dropdownlist-state-focus")); b.host.addClass(b.toThemeProperty("jqx-fill-state-focus"))
            }); 
            d.addHandler(d.dropDownButtonContent[0], "blur", function (f) {
                d.updateCheckValue();
                b.host.removeClass(b.toThemeProperty("jqx-dropdownlist-state-focus")); b.host.removeClass(b.toThemeProperty("jqx-fill-state-focus"));
                var ll = $(f.relatedTarget).closest(b.container).length;
                if (ll ==0) {
                    b.close();
                }
            });
        },
        removeHandlers: function () {
            var c = this; var b = this;
            c.removeHandler(c.dropDownButtonWrapper, "mousedown");
            c.removeHandler(c.host, "keydown"); c.removeHandler(c.firstDiv, "focus"); c.removeHandler(c.firstDiv, "blur"); c.removeHandler(c.host, "mouseenter"); c.removeHandler(c.host, "mouseleave"); if (c.autoOpen) { c.removeHandler(c.host, "mouseenter"); c.removeHandler(c.host, "mouseleave") } c.removeHandler(a(document), "mousemove." + b.element.id)
        }, _findPos: function (c) { while (c && (c.type === "hidden" || c.nodeType !== 1 || a.expr.filters.hidden(c))) { c = c.nextSibling } var b = a(c).coord(true); return [b.left, b.top] }, testOffset: function (h, f, c) { var j = this; var g = h.outerWidth(); var k = h.outerHeight(); var i = a(window).width() + a(window).scrollLeft(); var e = a(window).height() + a(window).scrollTop(); if (f.left + g > i) { if (g > j.host.width()) { var d = j.host.coord().left; var b = g - j.host.width(); f.left = d - b + 2 } } if (f.left < 0) { f.left = parseInt(j.host.coord().left) + "px" } f.top -= Math.min(f.top, (f.top + k > e && e > k) ? Math.abs(k + c + 22) : 0); return f }, _getBodyOffset: function () { var c = 0; var b = 0; if (a("body").css("border-top-width") !== "0px") { c = parseInt(a("body").css("border-top-width")); if (isNaN(c)) { c = 0 } } if (a("body").css("border-left-width") !== "0px") { b = parseInt(a("body").css("border-left-width")); if (isNaN(b)) { b = 0 } } return { left: b, top: c } },

        close: function () {
            
            var g = this;
            a.jqx.aria(this, "aria-expanded", false);
            if (g.popupContent) {

                var e = g.popupContent;
                var d = g.container;
                var f = this;
                f._raiseEvent("3");
                var c = false; if (a.jqx.browser.msie && a.jqx.browser.version < 8) { c = true } if (!g.isOpened()) { return } a.data(document.body, "openedJQXButton" + g.element.id, null); if (g.animationType === "none") { g.container.css("visibility", "hidden"); if (c) { g.container.css("display", "none") } } else {
                    if (!f.ishiding) {
                        f.isanimating = true;
                        e.stop(); var b = e.outerHeight(); e.css("margin-top", 0); var h = -b; if (parseInt(g.container.coord().top) < parseInt(g.host.coord().top)) { h = b } if (g.animationType === "fade") { e.css({ opacity: 1 }); e.animate({ opacity: 0 }, g.closeDelay, function () { d.css("visibility", "hidden"); f.isanimating = false; f.ishiding = false; if (c) { d.css("display", "none") } }) } else { e.animate({ "margin-top": h }, g.closeDelay, function () { d.css("visibility", "hidden"); f.isanimating = false; f.ishiding = false; if (c) { d.css("display", "none") } }) }
                    }
                }
            }
            g.ishiding = true; g.host.removeClass(g.toThemeProperty("jqx-dropdownlist-state-selected")); g.host.removeClass(g.toThemeProperty("jqx-fill-state-pressed")); g.arrow.removeClass(g.toThemeProperty("jqx-icon-arrow-down-selected")); g.host.removeClass(g.toThemeProperty("jqx-rc-b-expanded"));
            g.host.removeClass(g.toThemeProperty("jqx-rc-t-expanded"));
            if (g.container) {
                g.container.removeClass(g.toThemeProperty("jqx-rc-t-expanded"));
                g.container.removeClass(g.toThemeProperty("jqx-rc-b-expanded")); g.container.removeClass(g.toThemeProperty("jqx-fill-state-focus"));
            }
            // g.host.removeClass(g.toThemeProperty("jqx-dropdownlist-state-focus"));
            //g.host.removeClass(g.toThemeProperty("jqx-fill-state-focus"));
            g._raiseEvent("1")
     
            
        },
        closeOpenedDropDown: function (e) {
            var c = e.data.me; var b = a(e.target); if (a(e.target).ischildof(e.data.me.host)) { return true }
            if (a(e.target).ischildof(e.data.me.popupContent)) { return true }
            var f = c; var d = false;
            a.each(b.parents(), function () {
                if (this.className !== "undefined") {
                    if (this.className.indexOf && this.className.indexOf("dropDownButton") !== -1) {
                        d = true; return false
                    }
                    if (this.className.indexOf && this.className.indexOf("jqx-popup") !== -1) { d = true; return false }
                }
            });
            if (!d) { c.close() } return true
        }, refresh: function () { var b = this; b._arrange() },
        _arrange: function () {
            var g = this;
            var f = parseInt(g.host.width()); var b = parseInt(g.host.height()); var e = g.arrowSize; var d = g.arrowSize; var h = 3; var c = f - d - 2 * h; if (c > 0) { g.dropDownButtonContent[0].style.width = c + "px" } g.dropDownButtonContent[0].style.height = parseInt(b) + "px"; g.dropDownButtonContent[0].style.left = "0px"; g.dropDownButtonContent[0].style.top = "0px"; g.dropDownButtonArrow[0].style.width = parseInt(d) + "px"; g.dropDownButtonArrow[0].style.height = parseInt(b) + "px"; if (g.rtl) { g.dropDownButtonArrow.css("float", "left"); g.dropDownButtonContent.css("float", "right"); g.dropDownButtonContent.css("left", -h) }
            if (g.container) {
                if (g.dropDownWidth !== null) {
                    if (g.dropDownWidth.toString().indexOf("%") >= 0) {
                        f = (parseInt(g.dropDownWidth) * g.host.width()) / 100; g.container.width(f)
                    } else { g.container.width(g.dropDownWidth) }
                }
                else {
                    g.container.width(g.host.width());
                }
                if (g.dropDownHeight !== null) { g.container.height(g.dropDownHeight) }
            }
        },
        destroy: function () {
            a.jqx.utilities.resize(this.host, null, true); var b = this; if (b.interval) { clearInterval(b.interval) } b.removeHandler(b.dropDownButtonWrapper, "selectstart"); b.removeHandler(b.dropDownButtonWrapper, "mousedown"); b.removeHandler(b.host, "keydown"); b.host.removeClass(); b.removeHandler(a(document), "mousedown." + b.element.id, self.closeOpenedDropDown); b.host.remove(); b.container.remove()
        }, _raiseEvent: function (g, c) { var f = this; if (c === undefined) { c = { owner: null } } if (g === 2 && !f.contentInitialized) { if (f.initContent) { f.initContent(); f.contentInitialized = true } } var d = f.events[g]; args = c; args.owner = this; var e = new a.Event(d); e.owner = this; if (g === 2 || g === 3 || g === 4) { e.args = c } var b = f.host.trigger(e); return b },
        resize: function (c, b) { var d = this; d.width = c; d.height = b; d._setSize(); d._arrange() },
        propertiesChangedHandler: function (b, c, d) { if (d.width && d.height && Object.keys(d).length === 2) { b._setSize(); b._arrange(); b.close() } }, propertyChangedHandler: function (b, c, f, e) {
            var d = this; if (d.isInitialized === undefined || d.isInitialized === false) { return } if (b.batchUpdate && b.batchUpdate.width && b.batchUpdate.height && Object.keys(b.batchUpdate).length === 2) { return } if (c === "template") {
                b.host.removeClass(b.toThemeProperty("jqx-" + f + ""));
                b.host.addClass(b.toThemeProperty("jqx-" + b.template + ""))
            }
            if (c === "rtl") {
                if (e) {
                    b.dropDownButtonArrow.css("float", "left"); b.dropDownButtonContent.css("float", "right")
                } else { b.dropDownButtonArrow.css("float", "right"); b.dropDownButtonContent.css("float", "left") }
            } if (c === "autoOpen") { b.render() } if (c === "theme" && e !== null) { a.jqx.utilities.setTheme(f, e, b.host) } if (c === "width" || c === "height") { b._setSize(); b._arrange() }
        }
    })
})(jqxBaseFramework);

