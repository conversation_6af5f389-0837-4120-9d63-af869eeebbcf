using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using static ArtisanManage.Models.PageQueryModel;

namespace ArtisanManage.Pages.Report
{
    public class BorrowHistoryModel : PageQueryModel
    {
        public BorrowHistoryModel(CMySbCommand cmd) : base(Services.MenuId.borrowHistory)
        {
            this.cmd = cmd;
            this.PageTitle = "客户借货历史";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead",CtrlType="jqxDateTimeInput", SqlFld="bh.happen_time",ForQuery = false, CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.AddDays(-30))+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead",CtrlType="jqxDateTimeInput", SqlFld="bh.happen_time",ForQuery = false,   CompareOperator="<",Value = CPubVars.GetDateText(DateTime.Now.Date) + " 23:59"}},
                {"supcust_id",CommonTool.GetDataItem("supcust_id",new DataItemChange{ForQuery=true,Width="200", AlwaysShow=true,ButtonUsage="event"}) },
                {"sheet_type",new DataItem(){Title="单据类型",ForQuery=true,ButtonUsage="list",
                    Source="[{v:'',l:'全部'},{v:'SHEET_BORROW_ITEM',l:'借货单'},{v:'SHEET_RETURN_ITEM',l:'还货单'}]",Value="",Label="全部"}},
            };
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true,
                     Sortable=true,
            Columns = new Dictionary<string, DataItem>()
            {
                {"happen_time",new DataItem(){Title="发生时间",Width="150",SqlFld="bh.happen_time"}},
                {"sheet_type_name",new DataItem(){Title="单据类型",Width="100",SqlFld="(case bh.sheet_type when 'SHEET_BORROW_ITEM' then '借货单' when 'SHEET_RETURN_ITEM' then '还货单' else bh.sheet_type end)"}},
                {"sheet_no",new DataItem(){Title="单据号",Width="150",SqlFld="sm.sheet_no"}},
                {"sup_name",new DataItem(){Title="客户名称",Width="200",SqlFld="sc.sup_name"}},
                {"change_amount",new DataItem(){Title="金额变化",Width="120",SqlFld="bh.change_amount",ShowSum=true}},
                {"now_balance",new DataItem(){Title="当时余额",Width="120",SqlFld="bh.now_balance"}},
                {"make_brief",new DataItem(){Title="备注",Width="200",SqlFld="bh.make_brief"}},
                {"red_flag_name",new DataItem(){Title="状态",Width="80",SqlFld="(case when bh.red_flag = 2 then '已红冲' else '正常' end)"}},
            },

            QueryFromSQL = @"
from borrow_history bh
left join info_supcust sc on bh.company_id = sc.company_id and bh.supcust_id = sc.supcust_id
left join sheet_sale_main sm on bh.company_id = sm.company_id and bh.sheet_id = sm.sheet_id
where bh.company_id = ~COMPANY_ID 
  ~VAR_startDay
  ~VAR_endDay
  ~VAR_supcust_id
  ~VAR_sheet_type
",

            // 查询条件处理
            /*QueryConditions = new Dictionary<string, QueryCondition>()
            {
                {"supcust_id", new QueryCondition{SqlFld="bh.supcust_id", CompareOperator="="}},
                {"sheet_type", new QueryCondition{SqlFld="bh.sheet_type", CompareOperator="="}},
                {"startDay", new QueryCondition{SqlFld="bh.happen_time", CompareOperator=">="}},
                {"endDay", new QueryCondition{SqlFld="bh.happen_time", CompareOperator="<="}},
            },*/

            QueryOrderSQL = "bh.happen_time desc",
            }
           }
        };
       }

        public async Task OnGet()
        {
            await InitGet(cmd);
        }
    }
}
