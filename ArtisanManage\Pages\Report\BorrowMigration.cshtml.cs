using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using static ArtisanManage.Models.DataItem;

namespace ArtisanManage.Pages.Report
{
    public class BorrowMigrationModel : PageQueryModel
    {
        public BorrowMigrationModel(CMySbCommand cmd) : base(Services.MenuId.borrowMigration)
        {
            this.cmd = cmd;
            this.PageTitle = "借货数据迁移助手";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"migration_type",new DataItem(){Title="迁移方式",ButtonUsage="list",
                    Source="[{v:'CURRENT_PRICE',l:'使用当前价格估算'},{v:'HISTORY_SHEET',l:'从历史单据计算'},{v:'MANUAL',l:'手工设置'}]",
                    Value="HISTORY_SHEET",Label="从历史单据计算",Width="150",ForQuery=true}},
                {"start_date",new DataItem(){Title="起始日期",CtrlType="jqxDateTimeInput",
                    Value=CPubVars.GetDateText(DateTime.Now.AddMonths(-12))+" 00:00",Width="150",ForQuery=true}},
                {"min_amount",new DataItem(){Title="最小金额",CtrlType="jqxNumberInput",
                    Value="100",Width="100",ForQuery=true}},
            };

            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true,
                     Sortable=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                        {"company_id",new DataItem(){Title="公司ID",Width="80",SqlFld="bci.company_id",Hidden=true}},
                        {"cust_id",new DataItem(){Title="客户ID",Width="80",SqlFld="bci.cust_id",Hidden=true}},
                        {"sup_name",new DataItem(){Title="客户名称",Width="200",SqlFld="sc.sup_name"}},
                        {"item_count",new DataItem(){Title="商品种类",Width="100",SqlFld="COUNT(*)"}},
                        {"total_qty",new DataItem(){Title="借货总数量",Width="120",SqlFld="SUM(bci.borrowed_qty)",ShowSum=true}},
                        {"estimated_amount",new DataItem(){Title="估算金额",Width="120",SqlFld="SUM(bci.borrowed_qty * COALESCE(ip.s_retail_price, 0))",ShowSum=true}},
                        {"items_detail",new DataItem(){Title="商品明细",Width="300",SqlFld="STRING_AGG(ip.item_name || '(' || bci.borrowed_qty || ')', ', ')"}},
                        {"last_borrow_time",new DataItem(){Title="最后借货时间",Width="150",SqlFld="MAX(bh.happen_time)"}},
                        {"migration_status",new DataItem(){Title="迁移状态",Width="100",SqlFld="CASE WHEN bb.supcust_id IS NOT NULL THEN '已迁移' ELSE '未迁移' END"}},
                     }
                  }
                }
            };

            // 设置查询SQL
            string sql = @"
            SELECT bci.company_id, bci.cust_id, sc.sup_name,
                   COUNT(*) as item_count,
                   SUM(bci.borrowed_qty) as total_qty,
                   SUM(bci.borrowed_qty * COALESCE(ip.s_retail_price, 0)) as estimated_amount,
                   STRING_AGG(ip.item_name || '(' || bci.borrowed_qty || ')', ', ') as items_detail,
                   MAX(bh.happen_time) as last_borrow_time,
                   CASE WHEN bb.supcust_id IS NOT NULL THEN '已迁移' ELSE '未迁移' END as migration_status
            FROM borrowed_cust_items bci
            LEFT JOIN info_supcust sc ON bci.company_id = sc.company_id AND bci.cust_id = sc.supcust_id
            LEFT JOIN info_item_prop ip ON bci.company_id = ip.company_id AND bci.item_id = ip.item_id
            LEFT JOIN borrow_history bh ON bci.company_id = bh.company_id AND bci.cust_id = bh.supcust_id
            LEFT JOIN borrow_balance bb ON bci.company_id = bb.company_id AND bci.cust_id = bb.supcust_id
            WHERE bci.borrowed_qty > 0
            GROUP BY bci.company_id, bci.cust_id, sc.sup_name, bb.supcust_id
            HAVING SUM(bci.borrowed_qty * COALESCE(ip.s_retail_price, 0)) >= {min_amount}
            ORDER BY estimated_amount DESC";

            Grids["gridItems"].QueryFromSQL = sql;
        }

        public void OnGet(string operKey)
        {
            OperKey = operKey;
        }

        /// <summary>
        /// 获取迁移统计信息
        /// </summary>
        public async Task<object> GetMigrationStats()
        {
            string sql = @"
            SELECT 
                COUNT(*) as total_customers,
                SUM(borrowed_qty) as total_qty,
                COUNT(DISTINCT item_id) as total_items,
                SUM(borrowed_qty * COALESCE(ip.s_retail_price, 0)) as estimated_total_amount
            FROM borrowed_cust_items bci
            LEFT JOIN info_item_prop ip ON bci.company_id = ip.company_id AND bci.item_id = ip.item_id
            WHERE bci.borrowed_qty > 0";

            var stats = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            return stats;
        }

        /// <summary>
        /// 执行数据迁移
        /// </summary>
        public async Task<object> ExecuteMigration(string migrationType, string startDate, decimal minAmount)
        {
            try
            {
                string migrationSql = "";
                
                switch (migrationType)
                {
                    case "CURRENT_PRICE":
                        migrationSql = GetCurrentPriceMigrationSql(minAmount);
                        break;
                    case "HISTORY_SHEET":
                        migrationSql = GetHistorySheetMigrationSql(startDate, minAmount);
                        break;
                    case "MANUAL":
                        // 手工模式只导出数据，不执行迁移
                        return new { result = "OK", message = "请使用导出功能获取数据后手工处理" };
                }

                if (!string.IsNullOrEmpty(migrationSql))
                {
                    cmd.CommandText = migrationSql;
                    await cmd.ExecuteNonQueryAsync();
                    return new { result = "OK", message = "数据迁移完成" };
                }
                else
                {
                    return new { result = "Error", message = "未知的迁移方式" };
                }
            }
            catch (Exception ex)
            {
                return new { result = "Error", message = "迁移失败：" + ex.Message };
            }
        }

        private string GetCurrentPriceMigrationSql(decimal minAmount)
        {
            return $@"
            INSERT INTO borrow_balance (company_id, supcust_id, balance, init_balance, init_time)
            SELECT 
                bci.company_id,
                bci.cust_id as supcust_id,
                SUM(bci.borrowed_qty * COALESCE(ip.s_retail_price, 0)) as balance,
                SUM(bci.borrowed_qty * COALESCE(ip.s_retail_price, 0)) as init_balance,
                now() as init_time
            FROM borrowed_cust_items bci
            LEFT JOIN info_item_prop ip ON bci.company_id = ip.company_id AND bci.item_id = ip.item_id
            WHERE bci.borrowed_qty > 0
            GROUP BY bci.company_id, bci.cust_id
            HAVING SUM(bci.borrowed_qty * COALESCE(ip.s_retail_price, 0)) >= {minAmount}
            ON CONFLICT (company_id, supcust_id) DO UPDATE SET
                balance = EXCLUDED.balance,
                init_balance = EXCLUDED.init_balance,
                init_time = EXCLUDED.init_time;";
        }

        private string GetHistorySheetMigrationSql(string startDate, decimal minAmount)
        {
            return $@"
            INSERT INTO borrow_balance (company_id, supcust_id, balance, init_balance, init_time)
            SELECT
                sm.company_id,
                sm.supcust_id,
                SUM(CASE
                    WHEN sd.trade_type = 'J' THEN sd.sub_amount
                    WHEN sd.trade_type = 'H' THEN -sd.sub_amount
                    ELSE 0
                END) as balance,
                0 as init_balance,
                now() as init_time
            FROM sheet_sale_main sm
            JOIN sheet_sale_detail sd ON sm.company_id = sd.company_id AND sm.sheet_id = sd.sheet_id
            WHERE sm.approve_time IS NOT NULL
              AND sm.red_flag IS NULL
              AND sd.trade_type IN ('J', 'H')
              AND sm.happen_time >= '{startDate}'
            GROUP BY sm.company_id, sm.supcust_id
            HAVING SUM(CASE
                WHEN sd.trade_type = 'J' THEN sd.sub_amount
                WHEN sd.trade_type = 'H' THEN -sd.sub_amount
                ELSE 0
            END) >= {minAmount}
            ON CONFLICT (company_id, supcust_id) DO UPDATE SET
                balance = EXCLUDED.balance,
                init_time = EXCLUDED.init_time;";
        }
    }

    [ApiController]
    [Route("api/[controller]/[action]")]
    public class BorrowMigrationController : QueryController
    {
        public BorrowMigrationController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            BorrowMigrationModel model = new BorrowMigrationModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }

        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            BorrowMigrationModel model = new BorrowMigrationModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpGet]
        public async Task<object> GetMigrationStats(string operKey)
        {
            BorrowMigrationModel model = new BorrowMigrationModel(cmd);
            return await model.GetMigrationStats();
        }

        [HttpPost]
        public async Task<object> ExecuteMigration([FromBody] dynamic request)
        {
            BorrowMigrationModel model = new BorrowMigrationModel(cmd);
            string migrationType = request.migrationType;
            string startDate = request.startDate;
            decimal minAmount = request.minAmount;
            
            return await model.ExecuteMigration(migrationType, startDate, minAmount);
        }
    }
}
