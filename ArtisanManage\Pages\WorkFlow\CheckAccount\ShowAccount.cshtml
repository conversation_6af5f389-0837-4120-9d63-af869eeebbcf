﻿@page
@model ArtisanManage.Pages.WorkFlow.CheckAccount.ShowAccountModel

@{
    Layout = null;

}
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>交账详情</title>
    <script src="~/js/Vue.js"></script>

    <script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>
    <link rel="stylesheet" href="~/jqwidgets/jqwidgets/styles/jqx.base.css" type="text/css" />
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxcore.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdatetimeinput.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxcalendar.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/globalization/globalize.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/globalization/globalize.culture.zh-CN.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxwindow.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxcheckbox.js"></script>
    <link href="~/NiceWidgets/NiceWidgets.css" rel="stylesheet" type="text/css" />

    <link rel="stylesheet" href="~/MiniJsLib/jquery.dialog.css">
    <script src="~/MiniJsLib/jquery.dialog.js"></script>

    <link rel="stylesheet" href="~/MiniJsLib/MiniJsLibPC.css?v=@Html.Raw(Model.Version)">
    <script src="~/MiniJsLib/MiniJsLibPC.js?v=@Html.Raw(Model.Version)"></script>

    <link rel="stylesheet" type="text/css" href="~/css/CheckSheetsSheet.css" />
    <script src="~/js/jQuery.print.js"></script>
    <script type="text/javascript" src="~/lib/element-ui/index.min.js"></script>
    <link rel="stylesheet" type="text/css" href="~/lib/element-ui/index.min.css" />

    <style>
        @@page {
            margin: 20px auto;
        }
        [v-cloak] {
            display: none;
        }
        .receiveLi{
            /*background-color: rgba(204, 22, 58, 0.1)!important;*/
            background-color: rgba(224,250,255,0.3) !important;
        }
        .activeClass{
        /*outline: 2px solid #FADAD8 !important;*/
        background-color: #e8faff !important;
        }
        .activeSheetNo{
            color: #ff99aa;
        }
        .el-icon-loading {
            color: #dc2626 !important;
            font-size: 28px !important;
        }
        .el-loading-text {
            color: #dc2626 !important;
            font-size: 28px !important;
        }
        .el-checkbox__input.isfocus .el-checkbox__inner {
            border-color:#dcdfe6;
        }
        .el-checkbox.is-checked.is-bordered,.el-checkbox__inner:hover{
            border-color:#606266;
        }
        .el-checkbox__input.is-checked + .el-checkbox__label{
            color:#606266;
        }
        .el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner {
           background-color: #ff99aa;
           border-color: #ff99aa;
        }
    /*    .el-dialog__headerbtn .el-dialog__close:hover {
            color:#606266;
        }*/
    </style>

</head>
<body>
    <div id="pages" style="height:100vh;overflow-y:auto" class="" v-cloak>
        <div class="account_title" style="padding-bottom: 10px;" >
            【{{title}}】对账详情
        </div>
        <div class="tabsMain">
            <div style="display: flex;justify-content:space-between">
                <div class="btn_footer" style="display: flex; justify-content: center; margin-left: 20px">
                    <el-button v-if="!sheets.sheet_id"  @@click="onSubmitCheckAccount" class="check-account-button" style="width: 80px;font-size: 14px;display: flex; align-items: center;justify-content: center;background-color: #ffcccc;border-color: #ffcccc;">交账</el-button>
                    @*<el-button v-if="!sheets.sheet_id" color="#ffcccc" class="main-button" @@click="onSubmitCheckAccount" style="font-size: 14px;display: flex; align-items: center;justify-content: center;">交账</el-button>*@
                    <el-button type="danger" v-else-if="!sheets.reg_flag" v-bind:disabled="!redRight" @@click="redCheckSheet" style="font-size: 14px;display: flex; align-items: center;justify-content: center">红冲</el-button>
                    <button disabled plain v-else>已红冲</button>
                    @* <button style="margin-left:40px;width: 100px" @@click="showDoPrint">{{curTab==0?'打印汇总':curTab==1?'打印单据':curTab==2?'打印商品':''}}</button>*@
                    <el-button plain text class="common-plain-button" style="margin-left:40px;width: 80px;font-size: 14px;display: flex; align-items: center;justify-content: center;" @@click="showDoPrint">打印</el-button>
                    <div class="query_item" v-if="!sheets.sheet_id"><label class="item_name">交账日期</label>
                        <div id='jqxdatetimeHappen_time' style="width: 100px"></div></div>
                </div>
                <div v-if="sheets.sheet_id" class="date_t" style=" text-align: center; padding: 10px 30px 5px 45px; font-size: 16px; background-color: #ffffff;">交账日期: {{sheets.happen_time}}</div>
                <div class="date_t">对账日期: {{this.sheets.start_time}} ~ {{this.sheets.end_time}}</div>
                <div v-if="!sheets.sheet_id" class="date_t">交账日期:{{happen_time}}</div>
                <div v-if="sheets.sheet_id " >
                <span>当日有{{dayCheckSheetsID.split(',').length}}张交账单</span>
                <button style="margin-left: 20px;background-color: #fff;border: 1px solid #aaa;border-radius: 5px"
                        v-show="!title.includes(this.isAllSheet) && dayCheckSheetsID.split(',').length >= 2" @@click="showAllNew">查看汇总</button>
              </div>
            </div>
            <ul class="tabsList">
                <li :class="activeName == item.code ? 'active': ''" v-for="(item,index) of tabList" :key="index" @@click="handleClick(item,index)">
                    {{item.name}}
                </li>
            </ul>
            <div class="content">
                <div v-if="activeName == 'tab-1'">
                    <div class="public_box4" style="margin-top: 10px; border: 1px solid #aaa; border-radius: 10px">

                        <div class="public_box4_t">
                            <div style="display:flex;font-size:18px;">
                                <div style=" text-align: left; width:34%;padding-left:10px;">收款来源</div>
                                <div style=" text-align: center; width: 33%;">单数</div>
                                <div style="text-align: right; width: 33%; padding-right: 10px; ">金额</div>
                            </div>
                        </div>
                        <ul class="receive">
                            <li class="active"style="height : 65px">
                                <div class="receive_t">
                                    <div>
                                        <span>销售上交</span>
                                    </div>
                                    <div>{{saleSum.total_count}}</div>
                                    <div>{{fix(saleSum.real_get_amount)}}</div>
                                </div>
                                <div class="receive_b">
                                    <div class="receive_b_compute">
                                        <span v-if="saleSum.left_amount || saleSum.prepay_amount || saleSum.return_amount || saleSum.disc_amount"> =销:{{fix(saleSum.sale_amount)}}</span>
                                        <span v-if="saleSum.return_amount"> - 退:{{fix(saleSum.return_amount)}}</span>
                                        <span v-if="saleSum.prepay_amount"> - 预:{{fix(saleSum.prepay_amount)}}</span>
                                        <span v-if="saleSum.left_amount"> - 欠:{{fix(saleSum.left_amount)}}</span>
                                        <span v-if="saleSum.disc_amount"> - 惠:{{fix(saleSum.disc_amount)}}</span>
                                        <span v-if="saleSum.feeout_amount"> - 支:{{fix(saleSum.feeout_amount)}}</span>
                                        <div style="display: flex; justify-content: center;">
                                            <span v-if="saleSum.real_get_amount">销售净额{{fix(saleSum.sale_amount-saleSum.return_amount)}}=</span>
                                            <span v-if="saleSum.left_amount || saleSum.prepay_amount || saleSum.return_amount || saleSum.disc_amount"> 销售总额:{{fix(saleSum.sale_amount)}}</span>
                                            <span v-if="saleSum.return_amount">-退货总额:{{saleSum.return_amount}}</span>
                                        </div>
                                    </div>
                                   
                                </div>
                            </li>
                            <li>
                                <div class="receive_t">
                                    <div>
                                        <span>收预收款</span>
                                    </div>
                                    <div>{{pregetSheetSum.count}}</div>
                                    <div>{{fix(pregetSheetSum.real_get_amount)}}</div>
                                </div>
                                <div class="receive_b">
                                    <div class="receive_b_compute">
                                        <span v-if="pregetSheetSum.disc_amount || pregetSheetSum.left_amount">=预:{{fix(pregetSheetSum.preget_amount)}} - 欠:{{fix(pregetSheetSum.left_amount)}} - 惠:{{fix(pregetSheetSum.disc_amount)}}</span>
                                    </div>
                                </div>
                            </li>
                            <li>
                                <div class="receive_t">
                                    <div>
                                        <span>付预付款</span>
                                    </div>
                                    <div>{{prepaySheetSum.count}}</div>
                                    <div>{{fix(prepaySheetSum.real_get_amount)}}</div>
                                </div>
                                <div class="receive_b">
                                    <div class="receive_b_compute">
                                        <span v-if="prepaySheetSum.disc_amount || prepaySheetSum.left_amount || prepaySheetSum.qtsr_amount">
                                         = 合:{{prepaySheetSum.real_get_amount + prepaySheetSum.disc_amount + prepaySheetSum.left_amount + prepaySheetSum.qtsr_amount}}
                                         - 欠:{{prepaySheetSum.left_amount}}
                                         - 惠:{{prepaySheetSum.disc_amount}}
                                          - 其它收入:{{prepaySheetSum.qtsr_amount}}
</span>
                                    </div>
                                </div>
                            </li>
                            <li>
                                <div class="receive_t">
                                    <div>
                                        <span>收欠款</span>
                                    </div>
                                    <div>{{getArrearSheetSum.count}}</div>
                                    <div>{{fix(getArrearSheetSum.real_get_amount)}}</div>
                                </div>
                                <div class="receive_b">
                                    <div class="receive_b_compute">
                                    <span v-if="getArrearSheetSum.disc_amount || getArrearSheetSum.prepay_amount">= 合:{{fix(Number(getArrearSheetSum.real_get_amount) + Number(getArrearSheetSum.disc_amount) + Number(getArrearSheetSum.prepay_amount))}} - 惠:{{fix(getArrearSheetSum.disc_amount)}} - 预：{{fix(getArrearSheetSum.prepay_amount)}}</span>
                                    </div>
                                </div>
                            </li>
                            <li>
                                <div class="receive_t">
                                    <div>
                                        <span>费用支出</span>
                                    </div>
                                    <div>{{feeOutSheetSum.count}}</div>
                                    <div>{{fix(feeOutSheetSum.real_get_amount)}}</div>
                                </div>
                            </li>
                            <li>
                                <div class="receive_t">
                                    <div>
                                        <span>其他收入</span>
                                    </div>
                                    <div>{{incomeSheetSum.count}}</div>
                                    <div>{{fix(incomeSheetSum.real_get_amount)}}</div>
                                </div>
                            </li>
                            <li>
                                <div class="receive_t">
                                    <div>
                                        <span>实收</span>
                                    </div>
                                    <div></div>
                                    <div>{{fix(totalSum)}}</div>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div style="margin-top: 20px; border: 1px solid #aaa; border-radius: 10px">
                        <div class="title3">
                            <div style=" text-align: left;padding-left:20px">实收账户</div>

                            <div style=" text-align: right;padding-right: 20px">金额</div>
                        </div>
                        <ul class="pay_ul">
                            <li v-for="(payway_amount,payway_name) in paywaySum.generalPaywayList" :key="payway_name">
                                <div class="pay_ul_t">
                                    <div>{{payway_name}}</div>
                                    <div>{{fix(payway_amount)}}</div>
                                </div>
                            </li>
                            @*  <li class="sum"> *@
                            @*     <div class="pay_ul_t"> *@
                            @*         <div>陈列费</div> *@
                            @*         <div>{{fix(displaySheetsSum)}}</div> *@
                            @*     </div> *@
                            @* </li> *@
                            <li class="sum">
                                <div class="pay_ul_t">
                                    <div>实收合计</div>
                                    <div>{{fix(paywaySum.totalAmount)}}</div>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div style="margin-top: 20px; border: 1px solid #aaa; border-radius: 10px">
                          <div class="title3">
                              <div style=" text-align: left;padding-left:20px">其他账户</div>

                              <div style=" text-align: right;padding-right: 20px">金额</div>
                          </div>
                          <div style="height: 15px"></div>
                            <ul class="pay_ul">
                                <li class="credit" v-if="paywaySum.creditNote.get_count || paywaySum.creditNote.debt_count">
                                    <div class="pay_ul_t">
                                        <div>欠条</div>
                                        <div>{{paywaySum.creditNote.get_count}}张<span v-if="paywaySum.creditNote.debt_count!=0">({{paywaySum.creditNote.debt_count}}张)</span></div>
                                        <div>{{fix(paywaySum.creditNote.get_amount)}}<span v-if="paywaySum.creditNote.debt_amount!=0">({{fix(paywaySum.creditNote.debt_amount)}})</span></div>
                                    </div>
                                </li>
                                <li class="prepay" v-for="(payway_amount,payway_name) in paywaySum.pregetList" :key="payway_name">
                                    <div class="pay_ul_t">
                                        <div>{{payway_name}}</div>
                                        <div>{{fix(payway_amount)}}</div>
                                    </div>
                                </li>
                                <li class="zc-pay" v-for="(payway_amount,payway_name) in paywaySum.zcPaywayList" :key="payway_name">
                                    <div class="pay_ul_t">
                                        <div>{{payway_name}}</div>
                                        <div>{{fix(payway_amount)}}</div>
                                    </div>
                                </li>
                                <li class="zc-pay" v-for="(payway_amount,payway_name) in paywaySum.qtsrPaywayList" :key="payway_name">
                                      <div class="pay_ul_t">
                                          <div>{{payway_name}}</div>
                                          <div>{{fix(payway_amount)}}</div>
                                      </div>
                                  </li>      
                            </ul>
                        </div>
                </div>
                <div v-if="activeName == 'tab-2'">
                    <div style="margin-top: 20px; border: 1px solid #aaa; border-radius: 10px">
                       <div class="title3">
                           <div style=" text-align: left;padding-left:20px">实收账户</div>

                           <div style=" text-align: right;padding-right: 20px">金额</div>
                       </div>
                       <ul class="pay_ul">
                           <li v-for="(payway_amount,payway_name) in paywaySum.generalPaywayList" :key="payway_name">
                               <div class="pay_ul_t">
                                   <div>{{payway_name}}</div>
                                   <div>{{fix(payway_amount)}}</div>
                               </div>
                           </li>
                           @*  <li class="sum"> *@
                           @*     <div class="pay_ul_t"> *@
                           @*         <div>陈列费</div> *@
                           @*         <div>{{fix(displaySheetsSum)}}</div> *@
                           @*     </div> *@
                           @* </li> *@
                           <li class="sum">
                               <div class="pay_ul_t">
                                   <div>实收合计</div>
                                   <div>{{fix(paywaySum.totalAmount)}}</div>
                               </div>
                           </li>
                       </ul>
                   </div>
                   <div style="margin-top: 20px; border: 1px solid #aaa; border-radius: 10px">
                         <div class="title3">
                             <div style=" text-align: left;padding-left:20px">其他账户</div>

                             <div style=" text-align: right;padding-right: 20px">金额</div>
                         </div>
                         <div style="height: 15px"></div>
                           <ul class="pay_ul">
                               <li class="credit" v-if="paywaySum.creditNote.get_count || paywaySum.creditNote.debt_count">
                                   <div class="pay_ul_t">
                                       <div>欠条</div>
                                       <div>{{paywaySum.creditNote.get_count}}张<span v-if="paywaySum.creditNote.debt_count!=0">({{paywaySum.creditNote.debt_count}}张)</span></div>
                                       <div>{{fix(paywaySum.creditNote.get_amount)}}<span v-if="paywaySum.creditNote.debt_amount!=0">({{fix(paywaySum.creditNote.debt_amount)}})</span></div>
                                   </div>
                               </li>
                               <li class="prepay" v-for="(payway_amount,payway_name) in paywaySum.pregetList" :key="payway_name">
                                   <div class="pay_ul_t">
                                       <div>{{payway_name}}</div>
                                       <div>{{fix(payway_amount)}}</div>
                                   </div>
                               </li>
                               <li class="zc-pay" v-for="(payway_amount,payway_name) in paywaySum.zcPaywayList" :key="payway_name">
                                   <div class="pay_ul_t">
                                       <div>{{payway_name}}</div>
                                       <div>{{fix(payway_amount)}}</div>
                                   </div>
                               </li>
                           </ul>
                       </div>
                    <div class="public_box4_m" style="margin-top: 5px">
                        <div class="public_box4_t">
                            <div class="search-container">
                                <div class="left-search">
                                    <div class="search-input">
                                        <div>
                                            <i class="el-icon-search" style="font-size: 18px;margin-top: 5px"></i>
                                        </div>
                                        <input
                                            type="text"
                                            placeholder="单号/客户名"
                                            style="border-top-width: 0;border-left-width: 0;border-right-width: 0;border-bottom: 1px solid;width: 180px;height: 30px;font-size: 14px;outline: none;box-shadow: none;margin-left: 10px;"
                                            class="input-item"
                                            ref="searchInputRef"
                                            id="search-input"
                                            v-model="searchCondition"
                                            @@keydown="handleTableKeyDown"
                                            @@input="handleSearchInput"
                                            autocomplete="off">  
                                    </div>
                                    <div class="search-text">按空格键勾选单据</div>
                                </div>
                                <div class="right-detail">
                                    <div class="detail-card">
                                        <!-- 这里不能直接用selectedData.length，因为selectedData代表加上检索条件后的选中数据 -->
                                        <div class="detail-text">
                                            已选中单据： {{ this.allSheets.filter(item => item.isChecked).length }}条/{{ this.allSheets.length }}条
                                        </div>
                                    </div>
                                    <div class="detail-card">
                                        <div class="detail-text">
                                            当前总金额： {{ Number(fix(this.allSheets.filter(item => item.isChecked).map(item => Number(item.real_get_amount)).reduce((sum, cur) => sum + cur, 0))) }} 元
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="title3" style="margin: 10px 30px;width: calc(100% - 80px);">
                                <div style=" text-align: left;flex:4;padding: 0 8px 0 10px;display: flex;align-items: center">
                                    <div style="font-weight: bold;flex: 1">序号</div>
                                    <el-checkbox v-model="isChecked" @@change="selectAll(isChecked)" style="zoom: 150%;flex: 1"></el-checkbox>
                                </div>
                                <div style=" text-align: left;flex: 15;font-weight: bold;padding-top: 5px">客户名</div>
                                <div style=" text-align: center;flex: 15;font-weight: bold;padding-top: 5px">单据类</div>
                                <div style=" text-align: center;flex: 15;font-weight: bold;padding-top: 5px">欠款</div>
                                <div style=" text-align: right;flex: 15;font-weight: bold;padding-top: 5px">金额</div>
                            </div>
                            <div>
                            </div>
                        <div class="receive_boxs" style="margin: 0 30px" >
                        <ul class="receive" tabindex="0" @@keydown="handleTableKeyDown">
                        <li v-for="(sheet,index) in combinedSheets" :key="sheet.sheet_no + '_' + sheet.sheet_id + '-' + index" :class="{receiveLi:hoverIndex===index,activeClass: isActive===index}" @@click="isActive=index" @@mouseover="hoverIndex = index" @@mouseout="hoverIndex=null" style="background-color:#fff">
                        <template v-if="sheet.type_sheet === 'sale'">
                            <div style="flex:1; display:flex;justify-content:center;align-items:center;">
                                <div style="text-align:left;display: flex;align-items: center">
                                    <div style="flex: 2">{{index + 1}}</div>
                                    <el-checkbox v-model="sheet.isChecked" @@change="checkChange(sheet)" style="zoom: 150%;flex: 3"></el-checkbox>
                                </div>
                            </div>
                            <div style="flex: 15">
                                <div class="receive_item">
                                    <div>
                                        {{sheet.sup_name}}
                                    </div>
                                    <div>
                                        <span>{{sheet.sheetType === 'X' ? '销售单' : sheet.sheetType === 'T' ? '退货单':'未知'}}</span>
                                    </div>
                                    <div>
                                        <span v-if="fix(Number(sheet.left_amount))!=0">{{fix(Number(sheet.left_amount))}}</span>
                                    </div>
                                    <div>
                                        <template v-if="sheet.sheetType === 'X'">
                                            {{(fix(fix(sheet.real_get_amount) - handleGetSheetFeeOut(sheet)))}}
                                        </template>
                                        <template v-else-if="sheet.sheetType === 'T'">
                                            {{fix(sheet.real_get_amount)}}
                                        </template>
                                    </div>
                                </div>
                                <div class="receive_son_bottom">
                                    <div class="receive_son_bottom_flex" style="text-align:left">
                                        <span>{{getSheetPayway(sheet)}}</span>                                                     
                                    </div>
                                    <div class="receive_son_bottom_flex" style="text-align:right">
                                        <span>= 销:{{fix(sheet.sale_amount)}}</span>
                                        <span v-if="Number(sheet.return_amount)"> - 退:{{fix(Number(sheet.return_amount))}}</span>
                                        <span v-if="Number(sheet.left_amount)"> - 欠:{{fix(Number(sheet.left_amount))}}</span>
                                        <span v-if="Number(sheet.prepay_amount)"> - 预:{{fix(Number(sheet.prepay_amount))}}</span>
                                        <span v-if="Number(sheet.now_disc_amount)"> - 惠:{{fix(Number(sheet.now_disc_amount))}}</span>
                                        <span v-if="Number(handleGetSheetFeeOut(sheet)) && sheet.sheetType === 'X'"> - 支:{{handleGetSheetFeeOut(sheet)}}</span>
                                    </div>

                                </div>
                                <div class="receive_item_c">
                                    <span class="receive_item_underline" :class="{activeSheetNo: isSheetActive.includes(index) }"  @@click="onSaleSheetNoClick($event,sheet,index)">
                                        {{sheet.sheet_no}}
                                        <span v-if="sheet.bj" style="margin-left:2px;background:#e6214a;color:white;border-radius:5px;width:16px;font-size:10px;text-align:center;line-height:16px;display:inline-block;">变</span>
                                        <span v-if="sheet.free" style="margin-left:2px;background:#e6214a;color:white;border-radius:5px;width:16px;font-size:10px;text-align:center;line-height:16px;display:inline-block;">赠</span>
                                    </span>
                                    <span>{{sheet.happen_time}}</span>
                                </div>
                                <div class="receive_item_c" v-if="sheet.make_brief" style="padding:0">
                                    <span>{{sheet.make_brief}}</span>
                                </div>
                            </div>
                        </template>

                                        <template v-if="sheet.type_sheet === 'borrow'">
                                            <div style="flex:1; display:flex;justify-content:center;align-items:center;">
                                                <div style="text-align:left;display: flex;align-items: center">
                                                    <div style="flex: 2">{{index + 1}}</div>
                                                    <el-checkbox v-model="sheet.isChecked" @@change="checkChange(sheet)" style="zoom: 150%;flex: 3"></el-checkbox>
                                                </div>
                                            </div>
                                            <div style="flex: 15">
                                                <div class="receive_item">
                                                    <div>
                                                        {{sheet.sup_name}}
                                                    </div>
                                                    <div>
                                                        <span>{{sheet.sheetType === 'JH' ? '借货单' : sheet.sheetType === 'HH' ? '还货单':'未知'}}</span>
                                                    </div>
                                                    <div>
                                                         
                                                    </div>
                                                    <div>
                                                        
                                                    </div>
                                                </div>
                                               
                                                <div class="receive_item_c">
                                                    <span class="receive_item_underline" :class="{activeSheetNo: isSheetActive.includes(index) }" @@click="onBorrowSheetNoClick($event,sheet,index)"> {{sheet.sheet_no}}</span>
                                                    <span>{{sheet.happen_time}}</span>
                                                </div>
                                                <div class="receive_item_c" v-if="sheet.make_brief" style="padding:0">
                                                    <span>{{sheet.make_brief}}</span>
                                                </div>
                                            </div>
                                        </template>
              
                        <template v-if="sheet.type_sheet === 'preget'">
                            <div style="flex:1; display:flex;justify-content:center;align-items:center;">
                                <div style="text-align:left;display: flex;align-items: center">
                                    <div style="flex: 2">{{index + 1}}</div>
                                    <el-checkbox v-model="sheet.isChecked" @@change="checkChange(sheet)" style="zoom: 150%;flex: 3"></el-checkbox>
                                </div>
                            </div>
                            <div style="flex: 15">
                                <div class="receive_item">
                                    <div>{{sheet.sup_name}}</div>
                                    <div>
                                        <span>{{sheet.sheet_no.substr(0,2) === "DH" ? '定货单' : '预收款单'}}</span>
                                    </div>
                                    <div>
                                        <span v-if="fix(Number(sheet.left_amount))!=0">{{fix(Number(sheet.left_amount))}}</span>
                                    </div>
                                    <div style="text-align:right">{{fix(Number(sheet.real_get_amount) - Number(sheet.left_amount) - Number(sheet.disc_amount))}}</div>
                                </div>
                                <div class="receive_son_bottom">
                                    <div class="receive_son_bottom_flex" style="text-align:left">
                                        <span>{{getSheetPayway(sheet)}}</span>                                                    
                                    </div>
                                    <div class="receive_son_bottom_flex" style="text-align:right" v-if="Number(sheet.disc_amount) || Number(sheet.left_amount)">
                                        <span v-if="Number(sheet.disc_amount) || Number(sheet.left_amount)">=预:{{fix(Number(sheet.real_get_amount))}}  - 欠:{{fix(Number(sheet.left_amount))}} - 惠:{{fix(Number(sheet.disc_amount))}}</span>
                                    </div>
                                </div>
                                <div class="receive_item_c">
                                    <span @@click="onPrepaySheetNoClick($event,sheet,index)" class="receive_item_underline" :class="{activeSheetNo: isSheetActive.includes(sheets.saleSheets.length + index + 1) }">{{sheet.sheet_no}}</span>
                                    <span>{{sheet.happen_time}}</span>
                                </div>
                                <div class="receive_item_c" v-if="sheet.make_brief" style="padding:0">
                                    <span>{{sheet.make_brief}}</span>
                                </div>
                            </div></template>
                                
                        <template v-if="sheet.type_sheet === 'prepay'">
                            <div style="flex:1; display:flex;justify-content:center;align-items:center;">
                                <div style="text-align:left;display: flex;align-items: center">
                                    <div style="flex: 2">{{index + 1}}</div>
                                    <el-checkbox v-model="sheet.isChecked" @@change="checkChange(sheet)" style="zoom: 150%;flex: 3"></el-checkbox>
                                </div>
                            </div>
                            <div style="flex: 15">
                                <div class="receive_item">
                                    <div>{{sheet.sup_name}}</div>
                                    <div>
                                        <span>预付款单</span>
                                    </div>
                                    <div>
                                        <span v-if="fix(Number(sheet.left_amount))!=0">{{fix(Number(sheet.left_amount))}}</span>
                                    </div>
                                    <div style="text-align:right">{{fix(Number(sheet.real_get_amount))}}</div>
                                </div>
                                <div class="receive_son_bottom">
                                    <div class="receive_son_bottom_flex" style="text-align:left">                                                     
                                        <span>{{getSheetPayway(sheet)}}</span>                                                    
                                    </div>
                                    <div class="receive_son_bottom_flex" style="text-align:right" v-if="Number(sheet.disc_amount) || Number(sheet.left_amount) ||Number(sheet.qtsr_amount)">
                                        <span>= 合:{{Number(sheet.real_get_amount ) +Number(sheet.disc_amount) +Number(sheet.left_amount) +Number(sheet.qtsr_amount)}}  - 欠:{{Number(sheet.left_amount)}} - 惠:{{Number(sheet.disc_amount)}}   {{ sheet.qtsr_amount ? `- 其他收入:${sheet.qtsr_amount}` : ''   }} </span>  
                                    </div>
                                </div>
                                <div class="receive_item_c">
                                    <span @@click="onPrepayGetSheetNoClick($event,sheet,index)" class="receive_item_underline" :class="{activeSheetNo: isSheetActive.includes(sheets.saleSheets.length + sheets.pregetSheets.length + index + 1) }">{{sheet.sheet_no}}</span>
                                    <span>{{sheet.happen_time}}</span>
                                </div>
                                <div class="receive_item_c" v-if="sheet.make_brief" style="padding:0">
                                    <span>{{sheet.make_brief}}</span>
                                </div>
                            </div></template>
                                  

                        <template v-if="sheet.type_sheet === 'getArrear'">
                            <div style="flex:1; display:flex;justify-content:center;align-items:center;">
                                <div style="text-align:left;display: flex;align-items: center">
                                    <div style="flex: 2">{{index + 1}}</div>
                                    <el-checkbox v-model="sheet.isChecked" @@change="checkChange(sheet)" style="zoom: 150%;flex: 3"></el-checkbox>
                                </div>
                            </div>
                            <div style="flex: 15">

                                <div class="receive_item">
                                    <div>{{sheet.sup_name}}</div>
                                    <div>
                                        <span>{{sheet.sheet_no.substr(0,2) === "SK" ? '收款单' : '付款单'}}</span>
                                    </div>
                                    <div>
                                    </div>
                                    <div style="text-align:right">{{fix(sheet.real_get_amount)}}</div>
                                </div>
                                <div class="receive_son_bottom">
                                    <div class="receive_son_bottom_flex" style="text-align:left">
                                        <span>{{getSheetPayway(sheet)}}</span>
                                    </div>
                                    <div class="receive_son_bottom_flex" style="text-align:right" v-if="Number(sheet.a_now_disc) || Number(sheet.prepay_amount)">
                                        <span>= 合:{{Number(sheet.real_get_amount )+ Number(sheet.a_now_disc) + Number(sheet.prepay_amount)}} - 惠:{{sheet.a_now_disc}} - 预:{{sheet.prepay_amount}} </span>
                                    </div>
                                </div>
                                <div class="receive_item_c">
                                    <span @@click="onGetArrearsSheetNoClick($event,sheet,index)" class="receive_item_underline" :class="{activeSheetNo: isSheetActive.includes(sheets.saleSheets.length + sheets.pregetSheets.length +sheets.prepaySheets.length + index + 1) }">{{sheet.sheet_no}}</span>
                                    <span>{{sheet.happen_time}}</span>
                                </div>
                                <div class="receive_item_c" v-if="sheet.make_brief" style="padding:0">
                                    <span>{{sheet.make_brief}}</span>
                                </div>
                            </div></template>

                        <template v-if="sheet.type_sheet === 'feeOut'">
                            <div style="flex:1; display:flex;justify-content:center;align-items:center;">
                                <div style="text-align:left;display: flex;align-items: center">
                                    <div style="flex: 2">{{index + 1}}</div>
                                    <el-checkbox v-model="sheet.isChecked" @@change="checkChange(sheet)" style="zoom: 150%;flex: 3"></el-checkbox>
                                </div>
                            </div>
                            <div style="flex: 15">
                                <div class="receive_item">
                                    <div>{{sheet.fee_sub_name}}<span v-if="sheet.sup_name">(客户:{{sheet.sup_name}})</span></div>
                                    <div>
                                        <span>支出单</span>
                                    </div>
                                    <div>
                                        <span v-if="fix(Number(sheet.left_amount))!=0">{{fix(Number(sheet.left_amount))}}</span>
                                    </div>
                                    <div style="text-align:right">{{fix(Number(sheet.real_get_amount))}}</div>
                                </div>
                                <div class="receive_son_bottom">
                                    <div class="receive_son_bottom_flex" style="text-align:left">
                                        <span>{{getSheetPayway(sheet)}}</span>
                                    </div>
                                    <div class="receive_son_bottom_flex" style="text-align:right">
                                        <span>=支出:{{fix(sheet.total_amount)}}</span>
                                        <span v-if="Number(sheet.now_disc_amount)">-优惠:{{fix(sheet.now_disc_amount)}}</span>
                                        <span v-if="Number(sheet.left_amount)">-欠款:{{fix(sheet.left_amount)}}</span>
                                    </div>

                                </div>
                                <div class="receive_item_c">
                                    <span @@click="onFeeOutSheetNoClick($event,sheet,index)" class="receive_item_underline" :class="{activeSheetNo: isSheetActive.includes(sheets.saleSheets.length + sheets.pregetSheets.length+sheets.prepaySheets.length + sheets.getArrearSheets.length + index + 1) }">{{sheet.sheet_no}}</span>
                                    <span>{{sheet.happen_time}}</span>
                                </div>
                                <div class="receive_item_c" v-if="sheet.make_brief" style="padding:0">
                                    <span>{{sheet.make_brief}}</span>
                                </div>
                            </div></template>

                        <template v-if="sheet.type_sheet === 'income'">
                            <div style="flex:1; display:flex;justify-content:center;align-items:center;">
                                <div style="text-align:left;display: flex;align-items: center">
                                    <div style="flex: 2">
                                        {{sheets.saleSheets.length + sheets.pregetSheets.length +sheets.prepaySheets.length + sheets.getArrearSheets.length + sheets.feeOutSheets.length + index + 1}}
                                    </div>
                                    <el-checkbox v-model="sheet.isChecked" @@change="checkChange(sheet)" style="zoom: 150%;flex: 3"></el-checkbox>
                                </div>
                            </div>
                            <div style="flex: 15">
                                <div class="receive_item">
                                    <div>{{sheet.fee_sub_name}}<span v-if="sheet.sup_name">(客户:{{sheet.sup_name}})</span></div>
                                    <div>
                                        <span>其他收入单</span>
                                    </div>
                                    <div>
                                        <span v-if="fix(Number(sheet.left_amount))!=0&&fix(Number(sheet.now_disc_amount))!=0">{{fix(sheet.left_amount)}}</span>
                                    </div>
                                    <div style="text-align:right">{{fix(Number(sheet.real_get_amount))}}</div>
                                </div>
                                <div class="receive_son_bottom">
                                    <div class="receive_son_bottom_flex" style="text-align:left">
                                        <span>{{getSheetPayway(sheet)}}</span>
                                    </div>
                                    <div class="receive_son_bottom_flex" style="text-align:right">
                                        <span>=收入:{{fix(sheet.total_amount)}}</span>
                                        <span v-if="Number(sheet.now_disc_amount)"> -优惠:{{fix(sheet.now_disc_amount)}}</span>
                                        <span v-if="Number(sheet.left_amount)">-欠款:{{fix(sheet.left_amount)}}</span>
                                    </div>

                                </div>
                                <div class="receive_item_c">
                                    <span @@click="onInComeSheetClick($event,sheet,index)" class="receive_item_underline" :class="{activeSheetNo: isSheetActive.includes(sheets.saleSheets.length + sheets.pregetSheets.length +sheets.prepaySheets.length + sheets.getArrearSheets.length + sheets.feeOutSheets.length + index + 1) }">{{sheet.sheet_no}}</span>
                                    <span>{{sheet.happen_time}}</span>
                                </div>
                                <div class="receive_item_c" v-if="sheet.make_brief" style="padding:0">
                                    <span>{{sheet.make_brief}}</span>
                                </div>
                            </div></template>
                        </li>

                        <li style="background: #fff">
                             
                            <div style="display:flex;justify-content:flex-end;flex-wrap:wrap;width:100%">
                                <h3>合计：</h3>
                                <h3 style="margin-right:20px" v-if="Number(saleSum.sale_amount) !== 0">销售总额{{fix(saleSum.sale_amount)}}</h3>
                                <h3 style="margin-right:20px" v-if="Number(saleSum.return_amount) !== 0">退货总额{{fix(saleSum.return_amount)}}</h3>
                                <h3 style="margin-right:20px" v-if="Number(saleSum.feeout_amount) !== 0">支出总额{{fix(saleSum.feeout_amount)}}</h3>
                                <h3 style="margin-right:20px" v-if="Number(pregetSheetSum.real_get_amount_Pre) !== 0">预收款总额{{pregetSheetSum.real_get_amount_Pre}}</h3>
                                <h3 style="margin-right:20px" v-if="Number(pregetSheetSum.real_get_amount_DH) !== 0">定货款总额{{pregetSheetSum.real_get_amount_DH}}</h3>
                                <h3 style="margin-right:20px" v-if="Number(prepaySheetSum.real_get_amount) !== 0">预付款总额{{prepaySheetSum.real_get_amount}}</h3>
                                <h3 style="margin-right:20px" v-if="Number(getArrearSheetSum.real_get_amount_SK) !== 0">收欠款总额{{getArrearSheetSum.real_get_amount_SK}}</h3>
                                <h3 style="margin-right:20px" v-if="Number(getArrearSheetSum.real_get_amount_FK) !== 0">付欠款总额{{getArrearSheetSum.real_get_amount_FK}}</h3>
                                <h3 style="margin-right:20px" v-if="Number(feeOutSheetSum.real_get_amount) !== 0">支出总额{{fix(feeOutSheetSum.real_get_amount)}}</h3>
                                <h3 style="margin-right:20px" v-if="Number(incomeSheetSum.real_get_amount) !== 0">收入总额{{fix(incomeSheetSum.real_get_amount)}}</h3>
                            </div>

                        </li>

                        </ul>
                        </div>
                        
                        <!--  空白的目的是：当搜索结果比较少时页面不会因为表格变短而整体移动，影响体验  -->
                        <div v-if="combinedSheets.length < 8" style="background-color: white;width: 90%;height: 500px"></div>

                        </div>
                    </div>
                </div>
                <div v-if="activeName == 'tab-3'">
                    <div>
                        <div class="title_line" v-if="itemSortOut.saleItemsTree.sub_amount">销售: ￥{{fix(itemSortOut.saleItemsTree.sub_amount)}}</div>
                        <div class="title_line" v-else>销售: ￥0</div>
                        <div class="title_line" v-if="itemSortOut.saleItemsTree.quantity">大【{{fix(itemSortOut.saleItemsTree.quantity.nums[0])}}】中【{{fix(itemSortOut.saleItemsTree.quantity.nums[1])}}】小【{{fix(itemSortOut.saleItemsTree.quantity.nums[2])}}】</div>
                        <div class="class_sum">
                            <div v-for="(row, index) in itemSortOut.saleItemsTree.rows" :key="index" class="show_class">
                                <h4>【{{row.item_name}}】</h4>
                                <div v-for="(item, index) in row.rows" :key="index" class="show_item">
                                    <div class="item_name"><div>
                                        <template v-if ="item.remark">
                                            【{{item.remark}}】
                                        </template>
                                    {{item.item_name}}    
                                    </div></div>
                                    <div class="item_data" style="padding-top:1px">
                                        <div v-if="itemSortOut.saleItemsTree.type != 'free'">￥{{fix(item.sub_amount)}}</div>
                                    </div>
                                    <div class="item_data item_1"><div>{{item.quantity.detailStr()}}</div></div>
                                </div>
                                <div class="show_item sum">
                                    <div class="item_name"><div>合计</div></div>
                                    <div class="item_data" style="padding-top:1px"><div v-if="itemSortOut.saleItemsTree.type != 'free'">￥{{fix(row.sub_amount)}}</div></div>
                                    <div class="item_data"><div>{{row.quantity.sumStr()}}</div></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="title_line" v-if="itemSortOut.returnItemsTree.sub_amount">退货: ￥{{fix(itemSortOut.returnItemsTree.sub_amount)}}</div>
                        <div class="title_line" v-else>退货: ￥ 0</div>
                        <div class="title_line" v-if="itemSortOut.returnItemsTree.quantity">大【{{fix(itemSortOut.returnItemsTree.quantity.nums[0])}}】中【{{fix(itemSortOut.returnItemsTree.quantity.nums[1])}}】小【{{fix(itemSortOut.returnItemsTree.quantity.nums[2])}}】</div>
                        <div class="class_sum">
                            <div v-for="(row, index) in itemSortOut.returnItemsTree.rows" :key="index" class="show_class">
                                <h4>【{{row.item_name}}】</h4>
                                <div v-for="(item, index) in row.rows" :key="index" class="show_item">
                                    <div span="10" class="item_name"><div>
                                        <template v-if ="item.remark">
                                            【{{item.remark}}】
                                        </template>
                                    {{item.item_name}}    
                                    </div></div>
                                    <div span="5" class="item_data" style="padding-top:1px"><div v-if="itemSortOut.returnItemsTree.type != 'free'">￥{{fix(item.sub_amount)}}</div></div>
                                    <div span="7" class="item_data"><div>{{item.quantity.detailStr()}}</div></div>
                                </div>
                                <div class="show_item sum">
                                    <div span="10" class="item_name"><div>合计</div></div>
                                    <div span="5" class="item_data" style="padding-top:1px"><div v-if="itemSortOut.returnItemsTree.type != 'free'">￥{{fix(row.sub_amount)}}</div></div>
                                    <div span="7" class="item_data"><div>{{row.quantity.sumStr()}}</div></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="title_line">赠送</div>
                        <div class="title_line" v-if="itemSortOut.freeItemsTree.quantity">大【{{fix(itemSortOut.freeItemsTree.quantity.nums[0])}}】中【{{fix(itemSortOut.freeItemsTree.quantity.nums[1])}}】小【{{fix(itemSortOut.freeItemsTree.quantity.nums[2])}}】</div>
                        <div class="class_sum">
                            <div v-for="(row, index) in itemSortOut.freeItemsTree.rows" :key="index" class="show_class">
                                <h4>【{{row.item_name}}】</h4>
                                <div v-for="(item, index) in row.rows" :key="index" class="show_item">
                                    <div span="10" class="item_name"><div>

                                        <template v-if ="item.remark">
                                            【{{item.remark}}】
                                        </template>
                                        {{item.item_name}}
                                    </div></div>
                                    <div span="5" class="item_data" style="padding-top:1px"><div v-if="itemSortOut.freeItemsTree.type != 'free'">￥{{fix(item.sub_amount)}}</div></div>
                                    <div span="7" class="item_data"><div>{{item.quantity.detailStr()}}</div></div>
                                </div>
                                <div class="show_item sum">
                                    <div span="10" class="item_name"><div>合计</div></div>
                                    <div span="5" class="item_data" style="padding-top:1px"><div v-if="itemSortOut.freeItemsTree.type != 'free'">￥{{fix(row.sub_amount)}}</div></div>
                                    <div span="7" class="item_data"><div>{{row.quantity.sumStr()}}</div></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="title_line" v-if="itemSortOut.feeOutKSItemsTree.sub_amount">客损: ￥{{fix(itemSortOut.feeOutKSItemsTree.sub_amount)}}</div>
                        <div class="title_line" v-else>客损: ￥0</div>
                        <div class="title_line" v-if="itemSortOut.feeOutKSItemsTree.quantity">大【{{fix(itemSortOut.feeOutKSItemsTree.quantity.nums[0])}}】中【{{fix(itemSortOut.feeOutKSItemsTree.quantity.nums[1])}}】小【{{fix(itemSortOut.feeOutKSItemsTree.quantity.nums[2])}}】</div>
                        <div class="class_sum">
                            <div v-for="(row, index) in itemSortOut.feeOutKSItemsTree.rows" :key="index" class="show_class">
                                <h4>【{{row.item_name}}】</h4>
                                <div v-for="(item, index) in row.rows" :key="index" class="show_item">
                                    <div class="item_name">
                                        <div>
                                            <template v-if="item.remark">
                                                【{{item.remark}}】
                                            </template>
                                            {{item.item_name}}
                                        </div></div>
                                    <div class="item_data" style="padding-top:1px">
                                        <div v-if="itemSortOut.feeOutKSItemsTree.type != 'free'">￥{{fix(item.sub_amount)}}</div>
                                    </div>
                                    <div class="item_data item_1">
                                        <div>{{item.quantity.detailStr()}}</div></div>
                                </div>
                                <div class="show_item sum">
                                    <div class="item_name">
                                        <div>合计</div></div>
                                    <div class="item_data" style="padding-top:1px">
                                        <div v-if="itemSortOut.feeOutKSItemsTree.type != 'free'">￥{{fix(row.sub_amount)}}</div></div>
                                    <div class="item_data">
                                        <div>{{row.quantity.sumStr()}}</div></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>

        <!-- 以下部分为打印-->

        <div class="print_content" v-show="isPrintCss">
            <div class="print_content_title">
                <div class="title_maker_info" style="display:flex;">
                    <div style="flex:1" v-if="print_title.oper_name">制单人:【{{print_title.oper_name}}】</div>
                    <div style="flex:1" v-else>制单人:【未交账】</div>
                    <div style="flex:1" v-if="print_title.start_time && print_title.end_time">
                        对账时间:{{convertDateFromString(print_title.start_time)}} ~ {{convertDateFromString(print_title.end_time)}}
                    </div>
                    <div style="flex: 1" v-else>对账时间:【全部】</div>
                </div>

                <div class="title_oper_info" style="display:flex; justify-content:space-around">
                    <div style="flex:1">业务员:【{{title}}】</div>
                    <div style="flex:1" v-if="sheets.happen_time">交账日期:{{sheets.happen_time}}</div>
                    <div style="flex:1" v-else>交账日期:【未交账】</div>
                </div>


            </div>

            <div class="print_summary" v-show="show_print_summary" style="margin: 10px;">
                <div class="printf_title" style="display:flex;justify-content:center;padding-bottom: 15px">
                    <div>交账详情 - 汇总单</div>
                </div>
                <div class="print_summary_content">

                    <div class="print_summary_left" style="margin-left: 5px;border: 1px solid #EBEEF5; border-radius: 10px">
                        <div>
                            <div class="print_summary_left_title">
                                <div style=" text-align: left;padding-left:20px">支付类型</div>

                                <div style=" text-align: right;padding-right: 20px">金额</div>
                            </div>
                            <ul class="print_pay_ul">
                                <li v-for="(payway_amount,payway_name) in paywaySum.generalPaywayList" :key="payway_name">
                                    <div class="print_pay_ul_t">
                                        <div>{{payway_name}}</div>
                                        <div>{{fix(payway_amount)}}</div>
                                    </div>
                                </li>
                                <li class="sum">
                                    <div class="print_pay_ul_t">
                                        <div>实收合计</div>
                                        <div>{{fix(paywaySum.totalAmount)}}</div>
                                    </div>
                                </li>
                                <li class="credit" v-if="paywaySum.creditNote.get_count || paywaySum.creditNote.debt_count">
                                    <div class="print_pay_ul_t">
                                        <div>欠条</div>
                                        <div>{{paywaySum.creditNote.get_count}}张<span v-if="paywaySum.creditNote.debt_count!=0">({{paywaySum.creditNote.debt_count}}张)</span></div>
                                        <div>{{fix(paywaySum.creditNote.get_amount)}}<span v-if="paywaySum.creditNote.debt_amount!=0">({{fix(paywaySum.creditNote.debt_amount)}})</span></div>
                                    </div>
                                </li>
                                <li class="prepay" v-for="(payway_amount,payway_name) in paywaySum.pregetList" :key="payway_name">
                                    <div class="print_pay_ul_t">
                                        <div>{{payway_name}}</div>
                                        <div>{{fix(payway_amount)}}</div>
                                    </div>
                                </li>
                                 <li class="zc-pay" v-for="(payway_amount,payway_name) in paywaySum.zcPaywayList" :key="payway_name">
                                        <div class="pay_ul_t">
                                            <div>{{payway_name}}</div>
                                            <div>{{fix(payway_amount)}}</div>
                                        </div>
                                 </li> 
                                <li class="zc-pay" v-for="(payway_amount,payway_name) in paywaySum.qtsrPaywayList" :key="payway_name">
                                    <div class="pay_ul_t">
                                        <div>{{payway_name}}</div>
                                        <div>{{fix(payway_amount)}}</div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="print_summary_right">
                        <div class="print_summary_left_title">
                            <div style=" text-align: left;padding-left:20px">收款来源</div>
                            <div style=" text-align: center;">单数</div>
                            <div style=" text-align: right;padding-right: 20px">金额</div>
                        </div>
                        <ul class="print_receive">
                            <li style="display: inline">
                                <div class="print_receive_t">
                                    <div>
                                        <span>销售上交</span>
                                    </div>
                                    <div>{{saleSum.total_count}}</div>
                                    <div>{{fix(saleSum.real_get_amount)}}</div>
                                </div>
                                <div class="print_receive_b" style="clear:both">
                                    <div class="print_receive_b_compute">
                                        <span v-if="saleSum.left_amount || saleSum.prepay_amount || saleSum.return_amount || saleSum.disc_amount || saleSum.feeout_amount"> =销:{{fix(saleSum.sale_amount)}}</span>
                                        <span v-if="saleSum.return_amount"> - 退:{{fix(saleSum.return_amount)}}</span>
                                        <span v-if="saleSum.prepay_amount"> - 预:{{fix(saleSum.prepay_amount)}}</span>
                                        <span v-if="saleSum.left_amount"> - 欠:{{fix(saleSum.left_amount)}}</span>
                                        <span v-if="saleSum.disc_amount"> - 惠:{{fix(saleSum.disc_amount)}}</span>
                                        <span v-if="saleSum.feeout_amount"> - 支:{{fix(saleSum.feeout_amount)}}</span>
                                    </div>
                                </div>
                            </li>


                            <li style="display: inline">
                                <div class="print_receive_t">
                                    <div>
                                        <span>收预收款</span>
                                    </div>
                                    <div>{{pregetSheetSum.count}}</div>
                                    <div>{{fix(pregetSheetSum.real_get_amount)}}</div>
                                </div>
                                <div class="print_receive_b" style="clear:both">
                                    <div class="print_receive_b_compute">
                                        <span v-if="pregetSheetSum.disc_amount || pregetSheetSum.left_amount">=预:{{fix(pregetSheetSum.preget_amount)}} - 欠:{{fix(pregetSheetSum.left_amount)}} - 惠:{{fix(pregetSheetSum.disc_amount)}}</span>
                                    </div>
                                </div>
                            </li>
                           <li style="display: inline">
                                <div class="print_receive_t">
                                    <div>
                                        <span>付预付款</span>
                                    </div>
                                    <div>{{prepaySheetSum.count}}</div>
                                    <div>{{fix(prepaySheetSum.real_get_amount)}}</div>
                                </div>
                                <div class="print_receive_b" style="clear:both">
                                    <div class="receive_b_compute" >
                                        <span v-if="prepaySheetSum.disc_amount || prepaySheetSum.left_amount || prepaySheetSum.qtsr_amount">
                                             = 合:{{prepaySheetSum.real_get_amount + prepaySheetSum.disc_amount + prepaySheetSum.left_amount + prepaySheetSum.qtsr_amount}}
                                             - 欠:{{prepaySheetSum.left_amount}}
                                             - 惠:{{prepaySheetSum.disc_amount}}
                                              - 其它收入:{{prepaySheetSum.qtsr_amount}}
                                        </span>
                                    </div>
                                </div>
                            </li>

                            <li>
                                <div class="print_receive_t">
                                    <div>
                                        <span>收欠款</span>
                                    </div>
                                    <div>{{getArrearSheetSum.count}}</div>
                                    <div> <span style="font-size: 13px;padding-right:4px" v-if="getArrearSheetSum.disc_amount">[惠:{{fix(getArrearSheetSum.disc_amount)}},预：{{fix(getArrearSheetSum.prepay_amount)}}]</span>{{fix(getArrearSheetSum.real_get_amount)}}</div>
                                </div>
                            </li>
                            <li>
                                <div class="print_receive_t">
                                    <div>
                                        <span>费用支出</span>
                                    </div>
                                    <div>{{feeOutSheetSum.count}}</div>
                                    <div>{{fix(feeOutSheetSum.real_get_amount)}}</div>
                                </div>
                            </li>
                            <li>
                                <div class="print_receive_t">
                                    <div>
                                        <span>其他收入</span>
                                    </div>
                                    <div>{{incomeSheetSum.count}}</div>
                                    <div>{{fix(incomeSheetSum.real_get_amount)}}</div>
                                </div>
                            </li>

                            <li>
                                <div class="print_receive_t">
                                    <div>
                                        <span>实收</span>
                                    </div>
                                    <div></div>
                                    <div>{{fix(totalSum)}}</div>
                                </div>
                            </li>
                        </ul>
                    </div>

                </div>
            </div>
            <div class="print_detailed" v-show="show_print_detailed">
                <div class="printf_title" style="display:flex;flex-direction:column">
                    <div style="flex:1">交账详情 - 明细单 </div>
                    <div style="flex:1">
                        <span v-if="Number(saleSum.sale_amount) !== 0">【销售总额{{fix(saleSum.sale_amount)}}】</span>
                        <span v-if="Number(saleSum.return_amount) !== 0">【退货总额{{fix(saleSum.return_amount)}}】</span>
                        <span v-if="Number(saleSum.return_amount) !== 0">【支出总额{{fix(saleSum.feeout_amount)}}】</span>
                        <span v-if="Number(pregetSheetSum.real_get_amount_Pre) !== 0">【预收款总额{{pregetSheetSum.real_get_amount_Pre}}】</span>
                        <span v-if="Number(pregetSheetSum.real_get_amount_DH) !== 0">【定货款总额{{pregetSheetSum.real_get_amount_DH}}】</span>
                        <span v-if="Number(prepaySheetSum.real_get_amount) !== 0">【预付款总额{{prepaySheetSum.real_get_amount}}】</span>
                        <span v-if="Number(getArrearSheetSum.real_get_amount_SK) !== 0">收欠款总额{{getArrearSheetSum.real_get_amount_SK}}</span>
                        <span v-if="Number(getArrearSheetSum.real_get_amount_FK) !== 0">付欠款总额{{getArrearSheetSum.real_get_amount_FK}}</span>
                        <span v-if="Number(feeOutSheetSum.real_get_amount) !== 0">【支出总额{{fix(feeOutSheetSum.real_get_amount)}}】</span>
                        <span v-if="Number(incomeSheetSum.real_get_amount) !== 0">【收入总额{{fix(incomeSheetSum.real_get_amount)}}】</span>
                    </div>

                </div>
                <div class="print_detailed_content">
                    <div class="print_summary_left_title">
                        <div style=" text-align: left;flex:1">序号</div>
                        <div style=" text-align: left;padding-left:10px;flex: 5">客户名</div>
                        <div style=" text-align: center;flex: 5;padding-left:20px">单据类</div>
                        <div style=" text-align: center;flex: 5;padding-left:10px">欠款</div>
                        <div style=" text-align: right;padding-right: 20px;flex: 5">金额</div>
                    </div>
                    <div class="print_receive_boxs">
                        <ul class="print_receive">
                            <li v-for="(sheet,index) in checkedSheets" :class="sheet.isChecked?'':'unchecked-sheet'" :key="sheet.sheet_no + '__' + index">
                                <template v-if="sheet.type_sheet === 'sale'" @@click="onViewDetails(sheet)" style="background: #fff">
                                <div style="flex:1; display:flex;justify-content:center;align-items:center;">
                                    <div style="text-align:left">{{index + 1}}</div>
                                </div>
                                <div style="flex: 15">
                                    <div class="print_receive_item">
                                        <div>{{sheet.sup_name}}</div>
                                        <div>
                                            <span>{{sheet.sheetType === 'X' ? '销售单' : sheet.sheetType === 'T' ? '退货单':'未知'}}</span>
                                        </div>
                                        <div>
                                            <span v-if="fix(Number(sheet.left_amount))!=0">{{fix(Number(sheet.left_amount))}}</span>
                                        </div>
                                        <div>
                                            <template v-if="sheet.sheetType === 'X'">
                                                 {{(fix(fix(sheet.real_get_amount) - handleGetSheetFeeOut(sheet)))}}
                                            </template>
                                            <template v-else-if="sheet.sheetType === 'T'">
                                                {{fix(sheet.real_get_amount)}}
                                            </template>
                                        </div>
                                    </div>
                                    <div class="item_c_son_wrapper">
                                        <div class="print_receive_item_c">
                                            <div>{{sheet.sheet_no}}<span v-if="sheet.bj" style="margin-right:3px;background:#e6214a;color:white;border-radius:5px;width:16px;font-size:10px;text-align:center;line-height:16px;display:inline-block;">变</span><span v-if="sheet.free" style="margin-right:3px;background:#e6214a;color:white;border-radius:5px;width:16px;font-size:10px;text-align:center;line-height:16px;display:inline-block;">赠</span></div>
                                            <div style="text-align: center">{{sheet.happen_time}}</div>
                                        </div>
                                        <div class="print_receive_son_bottom">
                                            <span style="margin-right:10px">
                                                 <span>{{getSheetPayway(sheet)}}</span>
                                            </span>
                                            <span>= 销:{{fix(sheet.sale_amount)}}</span>
                                            <span v-if="Number(sheet.return_amount)"> - 退:{{fix(sheet.return_amount)}}</span>
                                            <span v-if="Number(sheet.left_amount)"> - 欠:{{fix(Number(sheet.left_amount))}}</span>
                                            <span v-if="Number(sheet.prepay_amount)"> - 预:{{fix(Number(sheet.prepay_amount))}}</span>
                                            <span v-if="Number(sheet.now_disc_amount)"> - 惠:{{fix(Number(sheet.now_disc_amount))}}</span>
                                             <span v-if="Number(handleGetSheetFeeOut(sheet)) && sheet.sheetType === 'X'"> - 支:{{handleGetSheetFeeOut(sheet)}}</span>
                                        </div>
                                    </div>
                                    <div class="item_c_son_wrapper">
                                        {{sheet.make_brief}}
                                    </div>

                                </div>
                                </template>

                                <template v-if="sheet.type_sheet === 'borrow'" @@click="onViewDetails(sheet)" style="background: #fff">
                                    <div style="flex:1; display:flex;justify-content:center;align-items:center;">
                                        <div style="text-align:left">{{index + 1}}</div>
                                    </div>
                                    <div style="flex: 15">
                                        <div class="print_receive_item">
                                            <div>{{sheet.sup_name}}</div>
                                            <div>
                                                <span>{{sheet.sheetType === 'JH' ? '借货单' : sheet.sheetType === 'HH' ? '还货单':'未知'}}</span>
                                            </div>
                                            <div>
                                                
                                            </div>
                                            <div> 
                                            </div>
                                        </div>
                                        <div class="item_c_son_wrapper">
                                            <div class="print_receive_item_c">
                                                <div>{{sheet.sheet_no}}</div>
                                                <div style="text-align: center">{{sheet.happen_time}}</div>
                                            </div>
                                           
                                        </div>
                                        <div class="item_c_son_wrapper">
                                            {{sheet.make_brief}}
                                        </div>

                                    </div>
                                </template>


                                <template v-else-if="sheet.type_sheet === 'preget'" @@click="onViewDetails(sheet)" style="background: #fff">
                                <div style="flex:1; display:flex;justify-content:center;align-items:center;">
                                    <div style="text-align:left">{{index + 1}}</div>
                                </div>
                                <div style="flex: 15">

                                    <div class="print_receive_item">
                                        <div>{{sheet.sup_name}}</div>
                                        <div>
                                            <span>{{sheet.sheet_no.substr(0,2) === "DH" ? '定货单' : '预收款单'}}</span>
                                        </div>
                                         <div>
                                            <span v-if="fix(Number(sheet.left_amount))!=0">{{fix(Number(sheet.left_amount))}}</span>
                                         </div>
                                        <div>{{fix(Number(sheet.real_get_amount) - Number(sheet.disc_amount) - Number(sheet.left_amount))}}</div>

                                    </div>
                                    <div class="item_c_son_wrapper">
                                        <div class="print_receive_item_c">
                                            <div>{{sheet.sheet_no}}</div>
                                            <div style="text-align: center">{{sheet.happen_time}}</div>
                                        </div>
                                        <div class="print_receive_son_bottom">
                                            <span style="margin-right:10px">
                                                <span>{{getSheetPayway(sheet)}}</span>
                                            </span>

                                            <span v-if="Number(sheet.disc_amount) || Number(sheet.left_amount)">=预:{{fix(Number(sheet.real_get_amount))}} - 欠:{{fix(Number(sheet.left_amount))}} - 惠:{{fix(Number(sheet.disc_amount))}}</span>
                                        </div>
                                    </div>
                                    <div class="item_c_son_wrapper">
                                        {{sheet.make_brief}}
                                    </div>
                                </div></template>


                                <template v-else-if="sheet.type_sheet === 'prepay'" @@click="onViewDetails(sheet)" style="background: #fff">
                                <div style="flex:1; display:flex;justify-content:center;align-items:center;">
                                    <div style="text-align:left">{{index + 1}}</div>
                                </div>
                                <div style="flex: 15">

                                    <div class="print_receive_item">
                                        <div>{{sheet.sup_name}}</div>
                                        <div>
                                            <span>预付款单</span>
                                        </div>
                                         <div>
                                            <span v-if="fix(Number(sheet.left_amount))!=0">{{fix(Number(sheet.left_amount))}}</span>
                                         </div>
                                        <div>{{fix(Number(sheet.real_get_amount))}}</div>

                                    </div>
                                    <div class="item_c_son_wrapper">
                                        <div class="print_receive_item_c">
                                            <div>{{sheet.sheet_no}}</div>
                                            <div style="text-align: center">{{sheet.happen_time}}</div>
                                        </div>
                                        <div class="print_receive_son_bottom">
                                            <span style="margin-right:10px">
                                                <span>{{getSheetPayway(sheet)}}</span>
                                            </span>

                                         <span v-if="prepaySheetSum.disc_amount || prepaySheetSum.left_amount || prepaySheetSum.qtsr_amount">
                                              = 合:{{prepaySheetSum.real_get_amount + prepaySheetSum.disc_amount + prepaySheetSum.left_amount + prepaySheetSum.qtsr_amount}}
                                              - 欠:{{prepaySheetSum.left_amount}}
                                              - 惠:{{prepaySheetSum.disc_amount}}
                                               - 其它收入:{{prepaySheetSum.qtsr_amount}}
                                         </span>
                                        </div>
                                    </div>
                                    <div class="item_c_son_wrapper">
                                        {{sheet.make_brief}}
                                    </div>
                                </div></template>


                                <template v-else-if="sheet.type_sheet === 'getArrear'" @@click="onViewDetails(sheet)" style="background: #fff">
                                <div style="flex:1; display:flex;justify-content:center;align-items:center;">
                                    <div style="text-align:left">{{index + 1}}</div>
                                </div>
                                <div style="flex: 15">
                                    <div class="print_receive_item">
                                        <div>{{sheet.sup_name}}</div>
                                        <div>
                                            <span>{{sheet.sheet_no.substr(0,2) === "SK" ? '收款单' : '付款单'}}</span>
                                        </div>
                                         <div>
                                            @*<span v-if="fix(Number(sheet.left_amount))!=0">{{fix(Number(sheet.left_amount))}}</span>*@
                                         </div>
                                        <div>{{fix(sheet.real_get_amount)}}</div>
                                    </div>

                                    <div class="item_c_son_wrapper">
                                        <div class="print_receive_item_c">
                                            <div>{{sheet.sheet_no}}</div>
                                            <div style="text-align: center">{{sheet.happen_time}}</div>
                                        </div>

                                        <div class="print_receive_son_bottom">
                                            <span style="margin-right:10px">
                                                <span>{{getSheetPayway(sheet)}}</span>
                                            </span>
                                            <span v-if="Number(sheet.a_now_disc) || Number(sheet.prepay_amount)">= 合:{{Number(sheet.real_get_amount )+ Number(sheet.a_now_disc) + Number(sheet.prepay_amount)}} - 惠:{{sheet.a_now_disc}} - 预:{{sheet.prepay_amount}}</span>
                                        </div>

                                    </div>
                                </div>
                                <div class="item_c_son_wrapper">
                                    {{sheet.make_brief}}
                                </div></template>
                            

                                <template v-else-if="sheet.type_sheet === 'feeOut'" @@click="onViewDetails(sheet)" style="background: #fff">
                                <div style="flex:1; display:flex;justify-content:center;align-items:center;">
                                    <div style="text-align:left">{{index + 1}}</div>
                                </div>
                                <div style="flex: 15">
                                    <div class="print_receive_item">
                                        <div>{{sheet.fee_sub_name}}<span v-if="sheet.sup_name">(客户:{{sheet.sup_name}})</span></div>
                                        <div>
                                            <span>支出单</span>
                                        </div>
                                         <div>
                                            <span v-if="fix(Number(sheet.left_amount))!=0">{{fix(Number(sheet.left_amount))}}</span>
                                         </div>
                                        <div>{{fix(Number(sheet.real_get_amount))}}</div>
                                    </div>
                                    <div class="item_c_son_wrapper">
                                        <div class="print_receive_item_c">
                                            <div>{{sheet.sheet_no}}</div>
                                            <div style="text-align: center">{{sheet.happen_time}}</div>
                                        </div>
                                        <div class="print_receive_son_bottom">
                                            <span>
                                               <span>{{getSheetPayway(sheet)}}</span>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="item_c_son_wrapper">
                                        {{sheet.make_brief}}
                                    </div>
                                </div></template>
                            

                                <template v-else-if="sheet.type_sheet === 'income'" @@click="onViewDetails(sheet)" style="background: #fff">
                                <div style="flex:1; display:flex;justify-content:center;align-items:center;">
                                    <div style="text-align:left">{{index + 1}}</div>
                                </div>
                                <div style="flex: 15">
                                    <div class="print_receive_item">
                                        <div>{{sheet.fee_sub_name}}<span v-if="sheet.sup_name">(客户:{{sheet.sup_name}})</span></div>
                                        <div>
                                            <span>其他收入单</span>
                                        </div>
                                        <div>
                                            <span v-if="fix(Number(sheet.left_amount))!=0">{{fix(Number(sheet.left_amount))}}</span>
                                        </div>
                                        <div>{{fix(Number(sheet.real_get_amount))}}</div>
                                    </div>
                                    <div class="item_c_son_wrapper">
                                        <div class="print_receive_item_c">
                                            <div>{{sheet.sheet_no}}</div>
                                            <div style="text-align: center">{{sheet.happen_time}}</div>
                                        </div>
                                        <div class="print_receive_son_bottom">
                                            <span>
                                               <span>{{getSheetPayway(sheet)}}</span>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="item_c_son_wrapper">
                                        {{sheet.make_brief}}
                                    </div>
                                </div></template>
                            </li>

                        </ul>
                    </div>
                </div>
            </div>
            <div class="print_goods" v-show="show_print_goods">
                <div class="printf_title" style="display:flex;justify-content:center">
                    <div>交账详情 - 商品单</div>
                </div>
                <div>

                    <div class="title_line" v-if="itemSortOut.saleItemsTree.sub_amount">销售: ￥{{fix(itemSortOut.saleItemsTree.sub_amount)}}</div>
                    <div class="title_line" v-else>销售: ￥0</div>
                    <div class="title_line" v-if="itemSortOut.saleItemsTree.quantity" style=" border-left: 50px solid #ddd !important; border-right: 50px solid #ddd !important;">大【{{fix(itemSortOut.saleItemsTree.quantity.nums[0])}}】中【{{fix(itemSortOut.saleItemsTree.quantity.nums[1])}}】小【{{fix(itemSortOut.saleItemsTree.quantity.nums[2])}}】</div>
                    <div class="print_class_sum">
                        <div v-for="(row, index) in itemSortOut.saleItemsTree.rows" :key="index" class="show_class">
                            <div class="show_item" style="font-weight:bold">
                                <div class="item_name">【{{row.item_name}}】</div>
                                <div class="item_data" v-if="itemSortOut.saleItemsTree.type != 'free'">￥{{fix(row.sub_amount)}}</div>
                                <div class="item_data item_1">{{row.quantity.sumStr()}}</div>
                            </div>
                            <div v-for="(item, index) in row.rows" :key="index" class="show_item">
                                <div class="item_name"><div>
                                    <template v-if ="item.remark">
                                            【{{item.remark}}】
                                        </template>
                                        {{item.item_name}}

                                </div></div>
                                <div class="item_data" style="padding-top:1px">
                                    <div v-if="itemSortOut.saleItemsTree.type != 'free'">￥{{fix(item.sub_amount)}}</div>
                                </div>
                                <div class="item_data item_1"><div>{{item.quantity.detailStr()}}</div></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <div class="title_line" v-if="itemSortOut.returnItemsTree.sub_amount">退货: ￥{{fix(itemSortOut.returnItemsTree.sub_amount)}}</div>
                    <div class="title_line" v-else>退货: ￥ 0</div>
                    <div class="title_line" v-if="itemSortOut.returnItemsTree.quantity" style=" border-left: 50px solid #ddd !important; border-right: 50px solid #ddd !important;">大【{{fix(itemSortOut.returnItemsTree.quantity.nums[0])}}】中【{{fix(itemSortOut.returnItemsTree.quantity.nums[1])}}】小【{{fix(itemSortOut.returnItemsTree.quantity.nums[2])}}】</div>
                    <div class="print_class_sum">
                        <div v-for="(row, index) in itemSortOut.returnItemsTree.rows" :key="index" class="show_class">

                            <div class="show_item" style="font-weight:bold">
                                <div class="item_name">【{{row.item_name}}】</div>
                                <div class="item_data" v-if="itemSortOut.returnItemsTree.type != 'free'">￥{{fix(row.sub_amount)}}</div>
                                <div class="item_data item_1">{{row.quantity.sumStr()}}</div>
                            </div>

                            <div v-for="(item, index) in row.rows" :key="index" class="show_item">
                                <div span="10" class="item_name"><div>
                                    <template v-if ="item.remark">
                                            【{{item.remark}}】
                                        </template>
                                        {{item.item_name}}

                                </div></div>
                                <div span="5" class="item_data" style="padding-top:1px"><div v-if="itemSortOut.returnItemsTree.type != 'free'">￥{{fix(item.sub_amount)}}</div></div>
                                <div span="7" class="item_data"><div>{{item.quantity.detailStr()}}</div></div>
                            </div>

                        </div>
                    </div>
                </div>
                <div>
                    <div class="title_line">赠送</div>
                    <div class="title_line" v-if="itemSortOut.freeItemsTree.quantity" style=" border-left: 50px solid #ddd !important; border-right: 50px solid #ddd !important;">大【{{fix(itemSortOut.freeItemsTree.quantity.nums[0])}}】中【{{fix(itemSortOut.freeItemsTree.quantity.nums[1])}}】小【{{fix(itemSortOut.freeItemsTree.quantity.nums[2])}}】</div>
                    <div class="print_class_sum">
                        <div v-for="(row, index) in itemSortOut.freeItemsTree.rows" :key="index" class="show_class">
                            <div class="show_item" style="font-weight:bold">
                                <div class="item_name">【{{row.item_name}}】</div>
                                <div class="item_data" v-if="itemSortOut.freeItemsTree.type != 'free'">￥{{fix(row.sub_amount)}}</div>
                                <div class="item_data item_1">{{row.quantity.sumStr()}}</div>
                            </div>
                            <div v-for="(item, index) in row.rows" :key="index" class="show_item">
                                <div span="10" class="item_name"><div>
                                    <template v-if ="item.remark">
                                        【{{item.remark}}】
                                    </template>
                                    {{item.item_name}}
                                </div></div>
                                <div span="5" class="item_data" style="padding-top:1px"><div v-if="itemSortOut.freeItemsTree.type != 'free'">￥{{fix(item.sub_amount)}}</div></div>
                                <div span="7" class="item_data"><div>{{item.quantity.detailStr()}}</div></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                <div class="title_line" v-if="itemSortOut.feeOutKSItemsTree.sub_amount">客损: ￥{{fix(itemSortOut.feeOutKSItemsTree.sub_amount)}}</div>
                <div class="title_line" v-else>客损: ￥0</div>
                <div class="title_line" v-if="itemSortOut.feeOutKSItemsTree.quantity">大【{{fix(itemSortOut.feeOutKSItemsTree.quantity.nums[0])}}】中【{{fix(itemSortOut.feeOutKSItemsTree.quantity.nums[1])}}】小【{{fix(itemSortOut.feeOutKSItemsTree.quantity.nums[2])}}】</div>
                <div class="class_sum">
                    <div v-for="(row, index) in itemSortOut.feeOutKSItemsTree.rows" :key="index" class="show_class">
                        <h4>【{{row.item_name}}】</h4>
                        <div v-for="(item, index) in row.rows" :key="index" class="show_item">
                            <div class="item_name">
                                <div>
                                    <template v-if="item.remark">
                                        【{{item.remark}}】
                                    </template>
                                    {{item.item_name}}
                                </div></div>
                            <div class="item_data" style="padding-top:1px">
                                <div v-if="itemSortOut.feeOutKSItemsTree.type != 'free'">￥{{fix(item.sub_amount)}}</div>
                            </div>
                            <div class="item_data item_1">
                                <div>{{item.quantity.detailStr()}}</div></div>
                        </div>
                        <div class="show_item sum">
                            <div class="item_name">
                                <div>合计</div></div>
                            <div class="item_data" style="padding-top:1px">
                                <div v-if="itemSortOut.feeOutKSItemsTree.type != 'free'">￥{{fix(row.sub_amount)}}</div></div>
                            <div class="item_data">
                                <div>{{row.quantity.sumStr()}}</div></div>
                        </div>
                    </div>
                </div>
            </div>
                 

               <div>
                    <div class="title_line">借货</div>
                    <div class="title_line" v-if="itemSortOut.borrowJHItemsTree.quantity" style=" border-left: 50px solid #ddd !important; border-right: 50px solid #ddd !important;">大【{{fix(itemSortOut.borrowJHItemsTree.quantity.nums[0])}}】中【{{fix(itemSortOut.borrowJHItemsTree.quantity.nums[1])}}】小【{{fix(itemSortOut.borrowJHItemsTree.quantity.nums[2])}}】</div>
                    <div class="print_class_sum">
                        <div v-for="(row, index) in itemSortOut.borrowJHItemsTree.rows" :key="index" class="show_class">
                            <div class="show_item" style="font-weight:bold">
                                <div class="item_name">【{{row.item_name}}】</div>
                                <div class="item_data" v-if="itemSortOut.borrowJHItemsTree.type != 'free'"></div>
                                <div class="item_data item_1">{{row.quantity.sumStr()}}</div>
                            </div>
                            <div v-for="(item, index) in row.rows" :key="index" class="show_item">
                                <div span="10" class="item_name"><div>
                                    <template v-if ="item.remark">
                                        【{{item.remark}}】
                                    </template>
                                    {{item.item_name}}
                                </div></div>
                                <div span="5" class="item_data" style="padding-top:1px"></div>
                                <div span="7" class="item_data"><div>{{item.quantity.detailStr()}}</div></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <div class="title_line">还货</div>
                    <div class="title_line" v-if="itemSortOut.borrowHHItemsTree.quantity" style=" border-left: 50px solid #ddd !important; border-right: 50px solid #ddd !important;">大【{{fix(itemSortOut.borrowHHItemsTree.quantity.nums[0])}}】中【{{fix(itemSortOut.borrowHHItemsTree.quantity.nums[1])}}】小【{{fix(itemSortOut.borrowHHItemsTree.quantity.nums[2])}}】</div>
                    <div class="print_class_sum">
                        <div v-for="(row, index) in itemSortOut.borrowHHItemsTree.rows" :key="index" class="show_class">
                            <div class="show_item" style="font-weight:bold">
                                <div class="item_name">【{{row.item_name}}】</div>
                                <div class="item_data" v-if="itemSortOut.borrowHHItemsTree.type != 'free'"></div>
                                <div class="item_data item_1">{{row.quantity.sumStr()}}</div>
                            </div>
                            <div v-for="(item, index) in row.rows" :key="index" class="show_item">
                                <div span="10" class="item_name">
                                    <div>
                                        <template v-if="item.remark">
                                            【{{item.remark}}】
                                        </template>
                                        {{item.item_name}}
                                    </div>
                                </div>
                                <div span="5" class="item_data" style="padding-top:1px"></div>
                                <div span="7" class="item_data"><div>{{item.quantity.detailStr()}}</div></div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

        </div>


        <!-- 打印结束-->
        <!--弹窗选择-->
        <el-dialog title="打印选择"
                   :visible.sync="dialogVisible"
                   width="40%">
            <div style="display:flex;justify-content:center;height:200px;align-items:center">
                <el-checkbox  size="40" border fill="#ff99aa" text-color="#606266"  v-model="show_print_summary">汇总</el-checkbox>
                <el-checkbox   size="40" border v-model="show_print_detailed">明细</el-checkbox>
                <el-checkbox   size="40" border  v-model="show_print_goods">商品</el-checkbox>
            </div>
             <span slot="footer" class="dialog-footer" style="display: flex; justify-content: flex-end">
                <el-button color="#606266"  @@click="closeDialog" style="font-size: 14px;display: flex; align-items: center;justify-content: center">取 消</el-button>
                 <button @@click="printBtn" class="main-button" style="margin-left:10px;font-size: 14px;display: flex; align-items: center;justify-content: center;width:80px;cursor:pointer;border-radius:4px;">确定</button>
               @*<el-button type="primary" style="font-size: 14px;display: flex; align-items: center;justify-content: center" @@click="printBtn">确 定</el-button>*@
              </span>
        </el-dialog>
    </div>
    <script>
        //debugger;
        var g_operKey = '@Model.OperKey';
    </script>
    <style media='print'>
    </style>
    <script type="module">

            window.app = new Vue({
                el: "#pages",
                data() {
                    return {
                        redRight: true,
                        approveRight: true,
                        checkedSheetsCount : 0,
                        title: '个人交账单',
                        sheets: {},
                        activeName: 'tab-1',
                        tabList: [
                            { code: 'tab-1', name: '汇总' },
                            { code: 'tab-2', name: '明细' },
                            { code: 'tab-3', name: '商品' },
                        ],
                        curTab: 0,
                        isPrintCss: false,
                        show_print_summary: true,
                        show_print_detailed: true,
                        show_print_goods: true,
                        print_title: {
                            oper_name: '',
                            start_time: '',
                            end_time: ''
                        },
                        isChecked : true,
                        happen_time: this.dateFormat("YYYY-mm-dd HH:MM:SS", new Date()),
                        checkedSheetsCountFlag: 0,
                        dialogVisible: false,
                        dayCheckSheetsID:'',
                        isAllSheet: '日汇总',
                        summCheckSheet:{},
                        queryData:{},
                        isActive:"",
                        isSheetActive:[],
                        timeoutId: '',  // 防抖函数使用
                        hoverIndex:null,//表示当前hover的是第几个li 初始为 -1 或 null 不能为0 0表示第一个li
                        isSortBySheetType: false,//是否按照单据类型排序 这里预留变量之后可能做按钮,
                        checkedSheets:{},//用于打印
                        searchCondition: '',// 用于检索单据
                        debounceTimer:null,
                        selectedSheets:[],
                        unselectedSheets:[],
                    }
                },
                computed: {
                //     realGetSheets(){
                //          console.log("sheets",this.sheets)
                //          if(!this.sheets.saleSheets){
                //              return this.sheets
                //          }
                //          const realGetSaleSheets =  this.sheets.saleSheets.filter(sheet=>{
                //             var displaySheetNos = this.displaySheets.map(displaySheet=>{return displaySheet.sheet_no })
                //             if (displaySheetNos.indexOf(sheet.sheet_no)===-1 ){
                //                 return sheet
                //             }
                //             })
                //          let targetSheet = Object.assign({},this.sheets)
                //          targetSheet.saleSheets = realGetSaleSheets
                //          return targetSheet
                //     },
                // displaySheets(){
                // //陈列单据
                //     return this.sheets.saleSheets.filter(sheet=>sheet.payway1_name.indexOf("陈列")!==-1||sheet.payway2_name.indexOf("陈列")!==-1||sheet.payway3_name.indexOf("陈列")!==-1)
                // },
                // displaySheetsSum(){
                //     if(this.displaySheets.length === 0 ){
                //         return 0
                //     }
                //     //陈列单据
                //     return this.displaySheets.map(sheet=>{
                //         return sheet.payway1_amount + sheet.payway2_amount + sheet.payway3_amount||0
                //     }).reduce((prev,next)=>{
                //          return prev+next
                //     })
                //     },
                    saleSum() {
                        var touchFlag = this.checkedSheetsCountFlag
                        var saleSum = {
                            real_get_amount: 0, sale_amount: 0, return_amount: 0, prepay_amount: 0,
                            left_amount: 0, disc_amount: 0, total_count: 0, feeout_amount: 0
                        }

                        if (this.sheets.saleSheets) {
                            var that = this
                            this.sheets.saleSheets.forEach(sheet => {
                                if (sheet.isChecked) {
                                    if (sheet.sheetType == 'X') {
                                        saleSum.total_count++
                                        saleSum.disc_amount += Number(sheet.now_disc_amount)
                                        saleSum.left_amount += Number(sheet.left_amount)
                                        saleSum.prepay_amount += Number(sheet.prepay_amount)
                                        
                                        if (sheet.payway1_type === 'ZC') { saleSum.feeout_amount += Number(sheet.payway1_amount) }
                                        if (sheet.payway2_type === 'ZC') { saleSum.feeout_amount += Number(sheet.payway2_amount) }
                                        if (sheet.payway3_type === 'ZC') { saleSum.feeout_amount += Number(sheet.payway3_amount) }
                                        
                                        
                                        sheet.sheetRows.forEach(item => {
                                            if (Number(item.sub_amount) > 0 && item.trade_type !== 'KS') {
                                                saleSum.sale_amount += Number(item.sub_amount)
                                            }
                                            if (Number(item.sub_amount) < 0) {
                                                saleSum.return_amount -= Number(item.sub_amount)
                                            }
                                            if(item.trade_type !== 'KS') {
                                                saleSum.real_get_amount += Number(item.sub_amount)
                                            }
                                            
                                        })
                                    } else if (sheet.sheetType == 'T') {
                                        saleSum.total_count++
                                        saleSum.disc_amount += Number(sheet.now_disc_amount)
                                        saleSum.left_amount += Number(sheet.left_amount)
                                        saleSum.prepay_amount += Number(sheet.prepay_amount)
                                        
                                        if (sheet.payway1_type === 'ZC') { saleSum.feeout_amount -= Number(sheet.payway1_amount) * -1 }
                                        if (sheet.payway2_type === 'ZC') { saleSum.feeout_amount -= Number(sheet.payway2_amount)* -1 }
                                        if (sheet.payway3_type === 'ZC') { saleSum.feeout_amount -= Number(sheet.payway3_amount)* -1 }
                                        
                                        sheet.sheetRows.forEach(item => {
                                            if (Number(item.sub_amount) > 0) {
                                                saleSum.return_amount += Number(item.sub_amount)
                                                saleSum.real_get_amount -= Number(item.sub_amount)
                                            }
                                        })
                                    }
                                }
                            })
                            saleSum.real_get_amount -= Number(saleSum.disc_amount) + Number(saleSum.left_amount) + Number(saleSum.prepay_amount) + Number(saleSum.feeout_amount)
                        }
                        for (let property in saleSum) {
                            if (property != 'total_count') {
                                saleSum[property] = toMoney(saleSum[property])
                            }
                        }
                        return saleSum
                    },
                    pregetSheetSum() {
                        var touchFlag = this.checkedSheetsCountFlag
                        var sum = { sheet_title: '预收款单', preget_amount: 0, real_get_amount: 0, count: 0, disc_amount: 0, left_amount: 0,real_get_amount_DH:0,real_get_amount_Pre:0 }

                        if (this.sheets.pregetSheets) {
                            var that = this
                            this.sheets.pregetSheets.forEach(function (sheet) {
                                if (sheet.isChecked) {
                                    sum.count++
                                    sum.real_get_amount += Number(sheet.real_get_amount)
                                    sum.disc_amount += Number(sheet.disc_amount)
                                    sum.left_amount += Number(sheet.left_amount)
                                    sum.preget_amount = sum.real_get_amount
                                    sum.real_get_amount_DH += Number(sheet.sheet_no.substr(0,2) === "DH" ? Number(sheet.real_get_amount) : 0)
                                    sum.real_get_amount_Pre += Number(sheet.sheet_no.substr(0,2) !== "DH" ? Number(sheet.real_get_amount) : 0)
                                }
                            }) 
                            sum.real_get_amount -= sum.disc_amount + Number(sum.left_amount)
                            sum.real_get_amount_DH= toMoney(sum.real_get_amount_DH)
                            sum.real_get_amount_Pre = toMoney(sum.real_get_amount_Pre)
                            sum.real_get_amount = toMoney(sum.real_get_amount)
                        }
                        sum.real_get_amount = toMoney(sum.real_get_amount)
                        return sum
                    },
                    prepaySheetSum() {
                        var touchFlag = this.checkedSheetsCountFlag
                        var sum = { sheet_title: '预付款单', prepay_amount: 0, real_get_amount: 0, count: 0, disc_amount: 0, left_amount: 0,qtsr_amount:0  }

                        if (this.sheets.prepaySheets) {
                            var that = this
                            this.sheets.prepaySheets.forEach(function (sheet) {
                                if (sheet.isChecked) {
                                    sum.count++
                                     sum.real_get_amount += Number(sheet.real_get_amount)
                                   sum.disc_amount += Number(sheet.disc_amount)
                                   sum.left_amount += Number(sheet.left_amount)
                                   sum.prepay_amount += Number(sum.prepay_amount)
                                   sum.qtsr_amount += Number(sheet.qtsr_amount)
                                }
                            })
                        }
                        sum.real_get_amount = toMoney(sum.real_get_amount)
                        return sum
                    },
                    getArrearSheetSum() { 
                        var touchFlag = this.checkedSheetsCountFlag
                        var sum = { sheet_title: '收款单', real_get_amount: 0, count: 0, disc_amount: 0,real_get_amount_FK:0,real_get_amount_SK:0, prepay_amount: 0}
                        if (this.sheets.getArrearSheets) {
                            this.sheets.getArrearSheets.forEach(function (sheet) {
                                if (sheet.isChecked) {
                                    if(sheet.sheet_no.substr(0,2) === "SK") {
                                        sum.count++
                                        sum.real_get_amount += Number(sheet.real_get_amount)
                                        sum.disc_amount += Number(sheet.a_now_disc)
                                        sum.prepay_amount += Number(sheet.prepay_amount)
                                        sum.real_get_amount_SK += Number(sheet.real_get_amount)
                                    }
                                    if(sheet.sheet_no.substr(0,2) === "FK") {
                                        sum.real_get_amount_FK += Number(sheet.real_get_amount)
                                    }
                                }
                            })
                        }
                        sum.real_get_amount = toMoney(sum.real_get_amount)
                        sum.disc_amount = toMoney(sum.disc_amount)
                        sum.prepay_amount = toMoney(sum.prepay_amount)
                        sum.real_get_amount_SK = toMoney(sum.real_get_amount_SK)
                        sum.real_get_amount_FK = toMoney(sum.real_get_amount_FK)
                        return sum
                    },
                    feeOutSheetSum() {
                        var touchFlag = this.checkedSheetsCountFlag
                    var sum = { sheet_title: '费用支出单', real_get_amount: 0, count: 0, fee_out_left_amount: 0, fee_out_disc_amount: 0, fee_out_total_amount: 0 }

                        if (this.sheets.feeOutSheets) {
                            var that = this
                            this.sheets.feeOutSheets.forEach(function (sheet) {
                                if (sheet.isChecked) {
                                    sum.count++
                                    sum.real_get_amount += Number(sheet.real_get_amount);
                                    sum.fee_out_disc_amount += Number(sheet.now_disc_amount);
                                sum.fee_out_total_amount += Number(sheet.total_amount);
                                    sum.fee_out_left_amount += Number(sheet.left_amount)
                                }
                            })
                        }
                        sum.real_get_amount = toMoney(sum.real_get_amount)
                        return sum
                    },
                    incomeSheetSum() {
                        var touchFlag = this.checkedSheetsCountFlag
                    var sum = { sheet_title: '其他收入', real_get_amount: 0, count: 0, income_left_amount: 0, income_disc_amount: 0, income_total_amount:0}

                        if (this.sheets.incomeSheets) {
                            var that = this
                            this.sheets.incomeSheets.forEach(function (sheet) {
                                if (sheet.isChecked) {
                                    sum.count++
                                    sum.real_get_amount += Number(sheet.real_get_amount)
                                    sum.income_left_amount += Number(sheet.left_amount)
                                    sum.income_disc_amount += Number(sheet.now_disc_amount)
                                    sum.income_total_amount += Number(sheet.fee_sub_amount)
                                }
                            })
                        }
                        sum.real_get_amount = toMoney(sum.real_get_amount)
                        return sum
                    },
                    totalSum() {
                        return toMoney(Number(this.saleSum.real_get_amount) + Number(this.prepaySheetSum.real_get_amount) + Number(this.pregetSheetSum.real_get_amount)
                            + Number(this.getArrearSheetSum.real_get_amount) + Number(this.feeOutSheetSum.real_get_amount)+ Number(this.incomeSheetSum.real_get_amount))
                    },
                    allSheets() {
                        //合并单据并进行排序
                        let allSheets = [
                            ...this.sheets.saleSheets.map(sheet => ({ ...sheet, type_sheet: 'sale' })),
                            ...this.sheets.borrowSheets.map(sheet => ({ ...sheet, type_sheet: 'borrow' })),
                            ...this.sheets.pregetSheets.map(sheet => ({ ...sheet, type_sheet: 'preget' })),
                            ...this.sheets.prepaySheets.map(sheet => ({ ...sheet, type_sheet: 'prepay' })),
                            ...this.sheets.getArrearSheets.filter(sheet => sheet.sheet_no.substr(0,2) === 'SK').map(sheet => ({ ...sheet, type_sheet: 'getArrear' })),
                            ...this.sheets.feeOutSheets.map(sheet => ({ ...sheet, type_sheet: 'feeOut' })),
                            ...this.sheets.incomeSheets.map(sheet => ({ ...sheet, type_sheet: 'income' }))
                        ];
                        if (!this.isSortBySheetType) {
                            allSheets = allSheets.slice().sort((a, b) => {
                                //按时间排序
                                return b.happen_time.localeCompare(a.happen_time);
                            });
                        }

                        this.checkedSheets = allSheets.filter(sheet => sheet.isChecked);
                        return allSheets;
                    },
                    combinedSheets() {
                        return [...this.selectedSheets,...this.unselectedSheets]
                    },
                    paywaySum() {
                        var touchFlag = this.checkedSheetsCountFlag

                        var generalPaywayList = {}
                        var zcPaywayList = {}
                        var qtsrPaywayList = {}
                        var totalAmount = 0
                        var creditNote = { get_count: 0, get_amount: 0, debt_count: 0, debt_amount: 0 }
                        var pregetList = {}
                        var that = this

                        if (this.sheets.result == 'OK') {
                            //console.log('payway:', this.sheets)
                            this.sheets.saleSheets.forEach(sheet => {
                                if (sheet.isChecked) {
                                    if (sheet.sheetType == 'X' || sheet.sheetType == 'T') {
                                        if (Number(sheet.left_amount)) {
                                            if (sheet.sheetType == 'X') {
                                                creditNote.get_count++
                                                creditNote.get_amount += Number(sheet.left_amount)
                                            } else {
                                                creditNote.debt_count++
                                                creditNote.debt_amount += Number(sheet.left_amount)
                                            }
                                        }
                                        if (Number(sheet.payway1_amount)) {
                                            if (sheet.payway1_type === 'YS') {
                                                addPrepay(sheet.payway1_name, sheet.payway1_amount)
                                            } else if (sheet.payway1_type === 'ZC') {
                                               addZCPaywayList(sheet.payway1_name, sheet.payway1_amount)
                                           } 
                                             else {
                                                addPayway(sheet.payway1_name, sheet.payway1_amount)
                                            }
                                        }
                                        if (Number(sheet.payway2_amount)) {
                                            if (sheet.payway2_type === 'YS') {
                                                addPrepay(sheet.payway2_name, sheet.payway2_amount)
                                            } 
                                             else if (sheet.payway2_type === 'ZC') {
                                               addZCPaywayList(sheet.payway2_name, sheet.payway2_amount)
                                           }
                                            
                                            else {
                                                addPayway(sheet.payway2_name, sheet.payway2_amount)
                                            }
                                        }
                                        if (Number(sheet.payway3_amount)) {
                                            if (sheet.payway3_type === 'YS') {
                                                addPrepay(sheet.payway3_name, sheet.payway3_amount)
                                            }
                                             else if (sheet.payway3_type === 'ZC') {
                                               addZCPaywayList(sheet.payway3_name, sheet.payway3_amount)
                                           }
                                             else {
                                                addPayway(sheet.payway3_name, sheet.payway3_amount)
                                            }
                                        }
                                    }
                                }
                            })

                            sumSheets(this.sheets.getArrearSheets)
                            sumSheets(this.sheets.pregetSheets)
                            handleCreditNote(this.sheets.pregetSheets,true,false)
                            sumSheets(this.sheets.prepaySheets,-1)
                            handleCreditNote(this.sheets.prepaySheets,false,true)
                            sumSheets(this.sheets.feeOutSheets)
                            sumSheets(this.sheets.incomeSheets)
                        }

                        function sumSheets (sheets,flag = 1) {
                            sheets.forEach(sheet => {
                                if (sheet.isChecked) {
                                   if (Number(sheet.payway1_amount)) {
                                      if(sheet.payway1_type === 'YS') {
                                        addPrepay(sheet.payway1_name, Number(sheet.payway1_amount) * flag)
                                      } else if(sheet.payway1_type === 'ZC') {
                                             addZCPaywayList(sheet.payway1_name, Number(sheet.payway1_amount) * flag)
                                          }else if(sheet.payway1_type === 'QTSR') {
                                                addQtsrPaywayList(sheet.payway1_name, Number(sheet.payway1_amount) * flag)
                                             } else {
                                        addPayway(sheet.payway1_name, Number(sheet.payway1_amount) * flag)
                                      }
                                    }
                                    if (Number(sheet.payway2_amount)) {
                                      if(sheet.payway2_type === 'YS') {
                                        addPrepay(sheet.payway2_name, Number(sheet.payway2_amount) * flag)
                                      }  else if(sheet.payway2_type === 'ZC') {
                                                        addZCPaywayList(sheet.payway2_name, Number(sheet.payway2_amount) * flag)
                                                      } 
                                      else if(sheet.payway1_type === 'QTSR') {
                                              addQtsrPaywayList(sheet.payway2_name, Number(sheet.payway2_amount) * flag)
                                        }
                                      
                                      else {
                                        addPayway(sheet.payway2_name, Number(sheet.payway2_amount) * flag)
                                      }
                                    }
                                    if (Number(sheet.payway3_amount)) {
                                      if(sheet.payway3_type === 'YS') {
                                        addPrepay(sheet.payway3_name, Number(sheet.payway3_amount) * flag)
                                      }  else if(sheet.payway3_type === 'ZC') {
                                            addZCPaywayList(sheet.payway3_name, Number(sheet.payway3_amount) * flag)
                                        } else if(sheet.payway1_type === 'QTSR') {
                                            addQtsrPaywayList(sheet.payway3_name, Number(sheet.payway3_amount) * flag)
                                      } else {
                                        addPayway(sheet.payway3_name, Number(sheet.payway3_amount) * flag)
                                      }
                                    }
                                    // if (Number(sheet.payway1_amount)) addPayway(sheet.payway1_name, Number(sheet.payway1_amount) * flag)
                                    // if (Number(sheet.payway2_amount)) addPayway(sheet.payway2_name, Number(sheet.payway2_amount) * flag)
                                    // if (Number(sheet.payway3_amount)) addPayway(sheet.payway3_name, Number(sheet.payway3_amount) * flag)
                                }
                            })
                        }
                        function addPayway(payway_name, payway_amount) {
                            if (!generalPaywayList[payway_name]) generalPaywayList[payway_name] = 0
                            generalPaywayList[payway_name] += Number(payway_amount)
                            totalAmount += Number(payway_amount)
                        }
                        function addPrepay(payway_name, payway_amount) {
                            if (!pregetList[payway_name]) pregetList[payway_name] = 0
                            pregetList[payway_name] += Number(payway_amount)
                        }
                        function addZCPaywayList(payway_name, payway_amount) {
                            if (!zcPaywayList[payway_name]) zcPaywayList[payway_name] = 0
                            zcPaywayList[payway_name] += Number(payway_amount)
                        }
                        
                        function addQtsrPaywayList(payway_name, payway_amount) {
                                if (!qtsrPaywayList[payway_name]) qtsrPaywayList[payway_name] = 0
                                qtsrPaywayList[payway_name] += Number(payway_amount)
                            }
                        
                        
                        function handleCreditNote(sheets,getCountFlag,debtCount){
                        sheets.forEach(sheet => {
                            if (sheet.isChecked) {
                                if (Number(sheet.left_amount)) {
                                if (getCountFlag) {
                                    creditNote.get_count++
                                    creditNote.get_amount += Number(sheet.left_amount)
                                }
                                if(debtCount) {
                                    creditNote.debt_count++
                                    creditNote.debt_amount += Number(sheet.left_amount)
                                }
                                }
                            }
                        })
                        }
                        for (let property in generalPaywayList) {
                            generalPaywayList[property] = toMoney(generalPaywayList[property])
                        }
                        for (let property in pregetList) {
                            pregetList[property] = toMoney(pregetList[property])
                        }
                        for (let property in zcPaywayList) {
                            zcPaywayList[property] = toMoney(zcPaywayList[property])
                        }
                        totalAmount = toMoney(totalAmount)
                        creditNote.get_amount = toMoney(creditNote.get_amount)
                        creditNote.debt_amount = toMoney(creditNote.debt_amount)
                        //  console.log('res: ', { generalPaywayList, totalAmount, creditNote, pregetList })
                        return { generalPaywayList, totalAmount, creditNote, pregetList, zcPaywayList, qtsrPaywayList}
                    },
                    itemSortOut() {
                        var that = this
                        var touchFlag = this.checkedSheetsCountFlag
                        var itemClasses = this.sheets.itemClasses
                        //   console.log(this.sheets.saleSheets)
                        var saleSheets = this.sheets.saleSheets
                        
                        
                        var saleItemsTree = { type: 'sale' }
                        var borrowJHItemsTree = { type: 'borrowJH' }
                        var borrowHHItemsTree = { type: 'borrowHH' }
                        var returnItemsTree = { type: 'return' }
                        var freeItemsTree = { type: 'free' }
                        var feeOutKSItemsTree = { type: 'feeOutKS' }
                        var itemsTree
                      
                         var itemSheets=[]
                         if(this.sheets.saleSheets){
                              this.sheets.saleSheets.forEach(function(sheet){
                                itemSheets.push(sheet)
                              })
                         }
                         
                          if(this.sheets.borrowSheets ){
                            this.sheets.borrowSheets.forEach(function(sheet){
                              itemSheets.push(sheet)
                            })
                          }
                          if (itemSheets.length==0) return { saleItemsTree,borrowJHItemsTree,borrowHHItemsTree, returnItemsTree, freeItemsTree, feeOutKSItemsTree }


                        
                        // 遍历saleSheet（实际包括销售单与退货单）
                            itemSheets.forEach(sheet => {
                            var that = this
                            if (sheet.isChecked) {
                                // 判断是否是销售单
                                 
                                sheet.sheetRows.forEach(itemTemp => {
                                    let item = JSON.parse(JSON.stringify(itemTemp))
                                    // 将商品自身id也放进类别路径，以方便操作 
                                    if (typeof (item.other_class) == 'string' || !item.other_class) {
                                        item.other_class = getClasses(item.other_class)
                                        item.other_class.splice(2, item.other_class.length - 2)
                                        item.other_class.push(item.item_id)
                                    }
                                   
                                    // 根据标记判断应该将商品插入哪一棵树
                                    var sub_amount = Number(item.sub_amount)
                                    if (sheet.sheetType == 'X') {
                                        if (sub_amount > 0) {
                                            if(item.trade_type === 'KS') {
                                                itemsTree = feeOutKSItemsTree
                                            } else {
                                             itemsTree = saleItemsTree
                                            }
                                           
                                        }
                                            
                                        else if (sub_amount == 0)
                                            itemsTree = freeItemsTree
                                        else
                                            itemsTree = returnItemsTree

                                        this.sheetTotal += sub_amount
                                    } 
                                    else  if (sheet.sheetType == 'T')  {
                                        itemsTree = returnItemsTree
                                    }
                                    else  if (sheet.sheetType == 'JH')  {
                                        itemsTree = borrowJHItemsTree
                                        
                                    }
                                    else  if (sheet.sheetType == 'HH')  {
                                        itemsTree = borrowHHItemsTree
                                        
                                    }
                                    if (!itemsTree){
                                        console.warn('sheet:\n', sheet, '\nhas no itemsTree!')
                                    }
                                    // 根节点判断
                                    if (!itemsTree.class_id) {
                                        debugger
                                        itemsTree.class_id = item.other_class[0]
                                        itemsTree.item_name = getClassInfo(item.other_class[0]).item_name
                                        itemsTree.quantity = newQuantity(item)
                                        itemsTree.sub_amount = sub_amount
                                        itemsTree.rows = []
                                    } else {
                                       // if(!item.other_class) debugger
                                        if (itemsTree.class_id != item.other_class[0]) {
                                            item.other_class[0] = itemsTree.class_id
                                            itemsTree.sub_amount += sub_amount
                                            // return
                                        } else {
                                            itemsTree.sub_amount += sub_amount
                                           // itemsTree.quantity = addQuantity(itemsTree.quantity, item.quantity, item.unit_no, item.unit_type)
                                        }
                                    }
                                    // 根据商品的类别路径遍历树，边建树边插入
                                    for (var i = 1; i < item.other_class.length; ++i) {
                                        var class_id = item.other_class[i]
                                        var flag = false
                                        let bForItem = i == item.other_class.length - 1 ? true : false
                                        var tempTree = {}
                                        // 同时根据id排序
                                        for (var j = 0; j < itemsTree.rows.length; ++j) {
                                            tempTree = itemsTree
                                            if (itemsTree.rows[j].class_id == class_id) {
                                                itemsTree = itemsTree.rows[j]
                                                if (i === 1 ) {//此处没有考虑同商品多种备注分类汇总，
                                                     itemsTree.sub_amount += sub_amount
                                                   } 
                                                if(itemsTree.rows.length == 0) {
                                                    var tempArrpushFlag = true
                                                    for(var tempi = 0 ; tempi < tempTree.rows.length; tempi ++) {
                                                    if(
                                                        (((tempTree.rows[tempi].remark == null || tempTree.rows[tempi].remark == undefined) && (item.remark == null || item.remark == undefined)) || (item.remark == tempTree.rows[tempi].remark))
                                                        && item.item_name == tempTree.rows[tempi].item_name
                                                    ) {
                                                        itemsTree = tempTree.rows[tempi]
                                                        itemsTree.sub_amount += sub_amount
                                                        itemsTree.quantity = addQuantity(itemsTree.quantity,item)
                                                        tempArrpushFlag = false
                                                        break
                                                    }
                                                    }
                                                    if(tempArrpushFlag) {
                                                        let tempTreeItem = item
                                                        tempTreeItem.class_id = class_id
                                                        tempTreeItem.quantity = newQuantity(item,bForItem)
                                                        tempTreeItem.sub_amount = sub_amount
                                                        tempTreeItem.rows = []

                                                        tempTree.rows.push(tempTreeItem)
                                                    }
                                                }
                                                flag = true
                                                break
                                            }
                                            if (itemsTree.rows[j].class_id < class_id) {
                                                let tempSplice = (bForItem ? item : getClassInfo(class_id))
                                                tempSplice.class_id = class_id
                                                tempSplice.quantity = newQuantity(item, bForItem)
                                                tempSplice.sub_amount = sub_amount
                                                tempSplice.rows = []
                                                /*var tree= {                                                 
                                                    class_id: class_id,
                                                    quantity: newQuantity(item, bForItem),
                                                    sub_amount: sub_amount,
                                                    rows: [],
                                                }
                                              */
                                                itemsTree.rows.splice(j, 0,tempSplice)
                                                itemsTree = itemsTree.rows[j]
                                                flag = true
                                                break
                                            }
                                        }
                                        if (!flag) {
                                            let newRowPush = (bForItem ? item : getClassInfo(class_id))
                                            newRowPush.class_id = class_id
                                            newRowPush.quantity = newQuantity(item, bForItem)
                                            newRowPush.sub_amount = sub_amount
                                            newRowPush.rows = []
                                            let len = itemsTree.rows.push(newRowPush)
                                            itemsTree = itemsTree.rows[len - 1]
                                        }
                                    }
                                })
                            }
                        })
                        getClassQty(saleItemsTree)
                        getClassQty(returnItemsTree)
                        getClassQty(freeItemsTree)
                        getClassQty(feeOutKSItemsTree)
                        getClassQty(borrowJHItemsTree)
                        getClassQty(borrowHHItemsTree)

                        compareTreeArr(saleItemsTree,'remark')
                        compareTreeArr(returnItemsTree,'remark')
                        compareTreeArr(freeItemsTree,'remark')
                        compareTreeArr(feeOutKSItemsTree,'remark')

                        compareTreeArr(borrowJHItemsTree,'remark')
                        compareTreeArr(borrowHHItemsTree,'remark')

                            console.log('saleItemsTree', saleItemsTree)
                        console.log('feeOutKSItemsTree', feeOutKSItemsTree)

                        // 数据库可能保存商品的完整或不完整类别路径，所以需要在解析类别
                        // 路径字符串的基础上进行校验和补全
                        function getClasses(classesStr) {
                            var needVerify = false
                            if (typeof classesStr != 'string' || !classesStr) {
                                return ['0','0']
                            }
                            // 分隔并去除空串
                            var classes = classesStr.split('/').filter(i => i).map(i => i.replace(/\s+/, ''))

                            if (needVerify) {
                                // 校验（待完成）
                            }
                            return classes
                        };

                        function getClassInfo(class_id) {
                            if (class_id == 0) {
                                return {
                                    item_name: '其他',
                                    class_id: 0,
                                }
                            }
                        
                            var index = itemClasses.findIndex(c => c.class_id == class_id)
                            if (index == undefined || index == -1)
                                return {}
                            else
                                return {
                                    item_name: itemClasses[index].class_name,
                                    class_id: class_id,
                                }
                        }

                        function newQuantity(item, bForItem) {
      
                            var quantity = {
                                nums: [0, 0, 0],
                                s_quantity: 0,
                                unit_nos: ['', '', ''],
                                unit_types: ['大', '中', '小'],
                                sumStr: function () {
                                    var str = ''
                                    for (var i = 0; i < this.nums.length; ++i) {
                                        if (this.nums[i] != 0)
                                            str += this.nums[i] + this.unit_types[i]
                                    }
                                    return str
                                },
                                detailStr: function () {
                                    var str = ''
                                    for (var i = 0; i < this.nums.length; ++i) {
                                        if (this.nums[i] != 0)
                                            str += this.nums[i] + this.unit_nos[i]
                                    }
                                    return str
                                }
                            }
                            if (!bForItem) return quantity
                            return addQuantity(quantity, item)
                        }
                        function addQuantity(qty, item) {
                            //console.log("addQuantity", item)
                            qty.s_quantity += Math.abs(Number(item.quantity * item.unit_factor))
                            let leftQty = Number(qty.s_quantity)
                            if (item.b_unit_factor) {
                                qty.nums[0] = parseInt(leftQty / Number(item.b_unit_factor))
                                leftQty = leftQty % Number(item.b_unit_factor)
                            }
                            if (item.m_unit_factor) {
                                qty.nums[1] = parseInt(leftQty / Number(item.m_unit_factor))
                                leftQty = leftQty % Number(item.m_unit_factor)
                            }
                            qty.nums[2] = leftQty
                            //qty.nums[index] += Math.abs(Number(num))
                            qty.unit_nos[0] = item.b_unit_no
                            qty.unit_nos[1] = item.m_unit_no
                            qty.unit_nos[2] = item.s_unit_no
                            console.log(qty)
                            return qty
                        }


                        function getClassQty(cls) {
                            var num = [0, 0, 0]
                            if (cls.rows && cls.rows.length > 0) {
                                cls.rows.forEach(row => {
                                    var tempNum = getClassQty(row)
                                    num[0] += tempNum[0]
                                    num[1] += tempNum[1]
                                    num[2] += tempNum[2]
                                })
                                cls.quantity.nums = num
                                return num
                            } else {
                                return cls.quantity ? cls.quantity.nums : null
                            }
                        }
                        function compareArrObj(property) {
                            return function (obj1, obj2) {
                              let val1 = obj1[property]
                              let val2 = obj2[property]
                              if(val1 == null || val1 == undefined) {
                                val1 = ""
                              }
                              if(val2 == null || val2 == undefined) {
                                val2 = ""
                              }
                              return val1.localeCompare(val2)
                              // var val1 = obj1[prop];
                              // var val2 = obj2[prop];
                              // if (!isNaN(Number(val1)) && !isNaN(Number(val2))) {
                              //   val1 = Number(val1);
                              //   val2 = Number(val2);
                              // }
                              // if (val1 < val2) {
                              //   return -1;
                              // } else if (val1 > val2) {
                              //   return 1;
                              // } else {
                              //   return 0;
                              // }
                            }
                          }
                        function compareTreeArr(tree,prop) {
                            if(tree.rows !== undefined) {
                              tree.rows.forEach(rowsTemp => {
                                rowsTemp.rows.sort(compareArrObj(prop))

                              })
                            }
                          }
                        return {
                            saleItemsTree,
                            returnItemsTree,
                            borrowJHItemsTree,
                            borrowHHItemsTree,
                            freeItemsTree,
                            feeOutKSItemsTree
                        }
                    }
                },
                mounted() {
                    this.onloadData()
                    this.checkedSheetsCount = window.srcWindow.checkedSheetsCount
                    debugger
                    this.GetOperRights()
                   
                },
                methods: {
                    formatDate(date) {
                        return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
                    },
                    dateFormat(fmt, date) {
                        let ret;
                        var date = new Date(date);
                        const opt = {
                            "Y+": date.getFullYear().toString(),        // 年
                            "m+": (date.getMonth() + 1).toString(),     // 月
                            "d+": date.getDate().toString(),            // 日
                            "H+": date.getHours().toString(),           // 时
                            "M+": date.getMinutes().toString(),         // 分
                            "S+": date.getSeconds().toString()          // 秒
                            // 有其他格式化字符需求可以继续添加，必须转化成字符串
                        };
                        for (let k in opt) {
                            ret = new RegExp("(" + k + ")").exec(fmt);
                            if (ret) {
                                fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
                            };
                        };
                        return fmt;
                    },
                    convertDateFromString(dateString) {
                        if (dateString) {
                            var arr1 = dateString.split(" ");
                            var sdate = arr1[0].split('-');
                            var date = new Date(sdate[0], sdate[1] - 1, sdate[2]);
                            return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
                        }
                    },
                    getSheetPayway(sheet) {
                        var pw = ''
                        if (sheet.payway1_type == 'QT' && sheet.payway1_name && Number(sheet.payway1_amount) !== 0) {
                            pw+=sheet.payway1_name
                        }
                        if (sheet.payway2_type == 'QT' && sheet.payway2_name && Number(sheet.payway2_amount) !== 0) {
                            if (pw) pw += '/'
                            pw += sheet.payway2_name
                        }
                        if (sheet.payway3_type == 'QT' && sheet.payway3_name && Number(sheet.payway3_amount) !== 0) {
                            if (pw) pw += '/'
                            pw += sheet.payway3_name
                        }
                        return pw
                    },
                    computeDate(date, days) {
                        var d = new Date(date);
                        d.setDate(d.getDate() + days);    //如果加月就是d.getMonth(); 以此类推
                        var m = d.getMonth() + 1;
                        return d.getFullYear() + '-' + m + '-' + d.getDate();// + " " + d.getHours() + ":" + d.getMinutes() + ":" + d.getSeconds();  //返回格式 2016-4-27 13:18:00 （可根据自己需要自行设定返回内容）
                    },
                    myPadLeft(len, charStr) {
                        var s = this + '';
                        return new Array(len - s.length + 1).join(charStr, '') + s;
                    },
                    myPadLeft(temp, len, charStr) {
                        var s = temp + '';
                        return new Array(len - s.length + 1).join(charStr, '') + s;
                    },
                    handleClick(item, index) {
                        this.activeName = item.code
                        this.curTab = index
                    },
                    onloadData() {//jiazai 
                        this.sheets = window.srcWindow.g_sheet
                        this.selectedSheets = []
                        this.unselectedSheets = []
                        this.allSheets.forEach(sheet => {
                            if (sheet.isChecked) {
                                this.selectedSheets.push({...sheet, timestamp: new Date().getTime()})
                            } else {
                                this.unselectedSheets.push({...sheet, timestamp: new Date().getTime()})
                            }
                        })
                        this.title = window.srcWindow.g_showAccountTitle
                        this.queryData = window.srcWindow.g_checkSheets_queryData
                        this.print_title = this.sheets.printTitle || this.print_title
                        if(this.sheets.dayCheckSheetsID)
                           this.dayCheckSheetsID = this.sheets.dayCheckSheetsID
                    },
                    loadRefresh(){
                        this.NewCheckSheet(this.queryData)
                        
                    },
                    NewCheckSheet_notused(obj) {
                        console.log("app.sheets1",app.sheets);
                        $.ajax({
                            url: '/AppApi/SheetCheckAcount/NewCheckSheet',
                            type: 'get',
                            data: obj,
                            contentType: "application/json;charset=UTF-8",
                            dataType: 'json'
                        }).then(function (res) {
                            if (res.result === 'OK') {
                                var count = 0;
                                var checked = true
                                updateSheetsCheck(res.saleSheets, checked)
                                updateSheetsCheck(res.prepaySheets, checked)
                                updateSheetsCheck(res.pregetSheets, checked)
                                updateSheetsCheck(res.getArrearSheets, checked)
                                updateSheetsCheck(res.feeOutSheets, checked)
                                updateSheetsCheck(res.incomeSheets, checked)
                                
                                app.sheets = res
                              /*
                                app.sheets = { 
                                    sheet_id: '',
                                    happen_time: '',
                                    isRed: false,
                                    start_time: app.start_time,
                                    end_time: app.end_time
                                }*/

                                $.extend(app.sheets, res)

                               
                                function updateSheetsCheck(sheets, check) {
                                    sheets.forEach(function (sheet) {
                                       
                                        sheet.isChecked = check
                                        if (sheet.isChecked)
                                            count++
                                    })
                                }
                               

                            }
                        });
                    },
                GetOperRights() {
                    $.ajax({
                        url: `/AppApi/Login/GetOperRights?operKey=` + g_operKey,
                        type: 'post',
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json'
                    }).then(res => {
                        console.log('res.operRights');
                        console.log(res.operRights);
                        if (res && res.operRights && res.operRights.sale && res.operRights.sale.sheetCheckSheets) {
                            // 正确地引用 red 权限
                            this.redRight = res.operRights.sale.sheetCheckSheets.red;
                            this.approveRight = res.operRights.sale.sheetCheckSheets.approve;
                            console.log('Red right is:', this.redRight);
                        } else {
                            console.error('Failed to get operRights or the expected structure is not present.');
                        }
                    },)
                },



                    onViewDetails(sheet) {
                        //console.log("点击sheet",sheet)
                        //if (!this.sheets.sheet_id) {
                        //    if (sheet.isChecked) {
                        //        this.checkedSheetsCount--;
                        //    } else {
                        //        this.checkedSheetsCount++;
                        //    }
                        //    this.checkedSheetsCountFlag = this.checkedSheetsCount
                        //    sheet.isChecked = !sheet.isChecked
                        //}
                    },
                    selectAll(ischecked) {
                        this.isChecked = ischecked
                        this.checkedSheetsCount = 0
                        this.sheets.saleSheets.forEach(saleSheet => {
                            saleSheet.isChecked = this.isChecked
                            if (this.isChecked) {
                                this.checkedSheetsCount++;
                            }

                        })
                        this.sheets.borrowSheets.forEach(sheet => {
                            sheet.isChecked = this.isChecked
                                if (sheet.isChecked) {
                                this.checkedSheetsCount++;
                            }

                        })
                        this.sheets.pregetSheets.forEach(pregetSheet => {
                            pregetSheet.isChecked = this.isChecked
                            if (this.isChecked) {
                                this.checkedSheetsCount++;
                            }

                        })
                        this.sheets.prepaySheets.forEach(prepaySheet => {
                            prepaySheet.isChecked = this.isChecked
                            if (this.isChecked) {
                                this.checkedSheetsCount++;
                            }

                        })
                        this.sheets.getArrearSheets.forEach(getArrearSheet => {
                            getArrearSheet.isChecked = this.isChecked
                            if (this.isChecked) {
                                this.checkedSheetsCount++;
                            }

                        })
                        this.sheets.feeOutSheets.forEach(feeOutSheet => {
                            feeOutSheet.isChecked = this.isChecked
                            if (this.isChecked) {
                                this.checkedSheetsCount++;
                            }

                        })

                        this.sheets.incomeSheets.forEach(incomeSheet => {
                            incomeSheet.isChecked = this.isChecked
                            if (this.isChecked) {
                                this.checkedSheetsCount++;
                            }

                        })
                        this.checkedSheetsCountFlag = this.checkedSheetsCount
                        this.handleSearch()

                    },
                    checkChange(sheet) {
                        //console.log("点击checkbox",sheet)
                        //sheet.isChecked = !sheet.isChecked
                        if(!this.sheets.sheet_id){
                          if (sheet.isChecked) {
                              this.checkedSheetsCount++;
                          } else {
                              this.checkedSheetsCount--;
                          }
                        }
                        /**
                         * 
                         * sheets.saleSheets
                         * sheets.prepaySheets
                         * sheets.getArrearSheets
                         * sheets.feeOutSheets
                         * sheets.incomeSheets
                         */
                            var sheetNum = this.sheets.saleSheets.length +  this.sheets.borrowSheets.length +this.sheets.prepaySheets.length + this.sheets.pregetSheets.length +this.sheets.getArrearSheets.length +this.sheets.feeOutSheets.length +this.sheets.incomeSheets.length
                            this.checkedSheetsCountFlag = this.checkedSheetsCount

                        this.isChecked = (this.checkedSheetsCountFlag == sheetNum)

                        //合并展示 同步allSheets与this.sheets
                        let sheetsArray = this.sheets[`${sheet.type_sheet}Sheets`];
                        let originalSheet = sheetsArray.find(s => s.sheet_no === sheet.sheet_no);
                        if (originalSheet) {
                            originalSheet.isChecked = !originalSheet.isChecked;
                            originalSheet.timestamp = new Date().getTime()
                        }

                    },
                    // 获取单据ZC
                    handleGetSheetFeeOut(sheet) {
                        let feeOutTotal = 0;
                        if (sheet.payway1_type === 'ZC') { feeOutTotal += Number(sheet.payway1_amount) }
                        if (sheet.payway2_type === 'ZC') { feeOutTotal += Number(sheet.payway2_amount) }
                        if (sheet.payway3_type === 'ZC') { feeOutTotal +=  Number(sheet.payway3_amount) }
                        return this.fix(feeOutTotal)
                    },


                    onSaleSheetNoClick(e, sheet,index) {
                        e.preventDefault()
                        e.stopPropagation()
                        console.log(window)
                        //this.isSheetActive = index
                        this.isSheetActive.push(index)
                        this.isActive=index
                        window.parent.newTabPage(sheet.sheetType === 'X' ? '销售单' : sheet.sheetType === 'T' ? '退货单' : '未知', `Sheets/SaleSheet?sheet_id=${sheet.sheet_id}`,window);
                    },
                     onBorrowSheetNoClick(e, sheet,index) {
                        e.preventDefault()
                        e.stopPropagation()
                        console.log(window)
                        //this.isSheetActive = index
                        this.isSheetActive.push(index)
                        this.isActive=index
                        window.parent.newTabPage(sheet.sheetType === 'JH' ? '借货单' : sheet.sheetType === 'HH' ? '还货单' : '未知', `Sheets/BorrowItemSheet?sheet_id=${sheet.sheet_id}`,window);
                    },                     
                    onPrepaySheetNoClick(e, sheet,index) {
                        e.preventDefault()
                        e.stopPropagation()
                        //this.isSheetActive = index
                        this.isSheetActive.push(this.sheets.saleSheets.length + index + 1)
                        this.isActive=this.sheets.saleSheets.length + index + 1
                        if (sheet.sheet_no.substr(0, 2) === "DH") {
                            window.parent.newTabPage('定货单', `Sheets/OrderItemSheet?sheet_id=${sheet.sheet_id}`,window);
                        } else {
                            window.parent.newTabPage('预收款单', `Sheets/PrepaySheet?sheet_id=${sheet.sheet_id}`,window);
                        }


                    },
                    onPrepayGetSheetNoClick(e, sheet,index) {
                        e.preventDefault()
                        e.stopPropagation()
                        //this.isSheetActive = index
                        this.isSheetActive.push(this.sheets.saleSheets.length + this.sheets.pregetSheets.length + index + 1)
                        this.isActive=this.sheets.saleSheets.length + this.sheets.pregetSheets.length + index + 1
                        window.parent.newTabPage('预付款单', `Sheets/PrepaySheet?sheet_id=${sheet.sheet_id}`,window);
                    },
                    onGetArrearsSheetNoClick(e, sheet,index) {
                        e.preventDefault()
                        e.stopPropagation()
                         //this.isSheetActive = index
                        this.isSheetActive.push(this.sheets.saleSheets.length + this.sheets.pregetSheets.length + this.sheets.prepaySheets.length + index + 1)
                         this.isActive=this.sheets.saleSheets.length + this.sheets.pregetSheets.length +this.sheets.prepaySheets.length + index + 1
                        window.parent.newTabPage('收款单', `Sheets/GetArrearsSheet?sheet_id=${sheet.sheet_id}`,window);
                    },
                    onFeeOutSheetNoClick(e, sheet,index) {
                        e.preventDefault()
                        e.stopPropagation()
                         //this.isSheetActive = index
                        this.isSheetActive.push(this.sheets.saleSheets.length + this.sheets.pregetSheets.length + this.sheets.prepaySheets.length + this.sheets.getArrearSheets.length + index + 1)
                         this.isActive=this.sheets.saleSheets.length + this.sheets.pregetSheets.length+this.sheets.prepaySheets.length + this.sheets.getArrearSheets.length + index + 1
                        window.parent.newTabPage('支出单', `Sheets/FeeOutSheet?sheet_id=${sheet.sheet_id}`,window);
                    },
                    onInComeSheetClick(e, sheet,index) {
                        e.preventDefault()
                        e.stopPropagation()
                         //this.isSheetActive = index
                        this.isSheetActive.push(this.sheets.saleSheets.length + this.sheets.pregetSheets.length + this.sheets.prepaySheets.length + this.sheets.getArrearSheets.length + this.sheets.feeOutSheets.length + index + 1)
                         this.isActive=this.sheets.saleSheets.length + this.sheets.pregetSheets.length +this.sheets.prepaySheets.length + this.sheets.getArrearSheets.length + this.sheets.feeOutSheets.length + index + 1
                        window.parent.newTabPage('其他收入单', `Sheets/FeeOutSheet?forOutOrIn=false&&sheet_id=${sheet.sheet_id}`,window);
                    },


                    // 提交对账单


                    onSubmitCheckAccount() {
                        if (!this.approveRight) {
                            bw.toast("该账号无交账单审核权限", 3000)
                            return;
                        }

                        jConfirm('本次共交账' + this.checkedSheetsCount + '张单据，是否确认交账？', function () {
                            let sheetRows = []
                            console.log('from:', app.sheets)
                            var sellerID = app.sheets.sellerID
                            var getterID = window.srcWindow.g_sellerID
 

                            //console.log('test:', this.sheets.saleSheets.filter(sheet=>sheet.isChecked==true)
                            //.map(sheet=>({business_sheet_id:sheet.sheet_id, business_sheet_type:sheet.sheet_type})))
                            sheetRows = sheetRows.concat(app.sheets.saleSheets.filter(sheet => sheet.isChecked == true)
                                .map(sheet => ({ business_sheet_id: sheet.sheet_id, business_sheet_type: sheet.sheetType })))

                            sheetRows = sheetRows.concat(app.sheets.borrowSheets.filter(sheet => sheet.isChecked == true)
                                .map(sheet => ({ business_sheet_id: sheet.sheet_id, business_sheet_type: sheet.sheetType })))

                            sheetRows = sheetRows.concat(app.sheets.prepaySheets.filter(sheet => sheet.isChecked == true)
                                .map(sheet => ({ business_sheet_id: sheet.sheet_id, business_sheet_type: 'YF' })))
                            sheetRows = sheetRows.concat(app.sheets.pregetSheets.filter(sheet => sheet.isChecked == true)
                                .map(sheet => ({ business_sheet_id: sheet.sheet_id, business_sheet_type: sheet.sheet_type })))
                            sheetRows = sheetRows.concat(app.sheets.getArrearSheets.filter(sheet => sheet.isChecked == true)
                                .map(sheet => ({ business_sheet_id: sheet.sheet_id, business_sheet_type: sheet.sheet_type })))
                            sheetRows = sheetRows.concat(app.sheets.feeOutSheets.filter(sheet => sheet.isChecked == true)
                                .map(sheet => ({ business_sheet_id: sheet.sheet_id, business_sheet_type: 'ZC' })))
                            sheetRows = sheetRows.concat(app.sheets.incomeSheets.filter(sheet => sheet.isChecked == true)
                                .map(sheet => ({ business_sheet_id: sheet.sheet_id, business_sheet_type: 'SR' })))

                        console.log("4545454", sheetRows)
                            if (sheetRows.length > 0) {
                                let params = {
                                    operKey: g_operKey,
                                    getter_id: window.srcWindow.g_sellerID,

                                    sheetRows,
                                    start_time: app.sheets.start_time,
                                    end_time: app.sheets.end_time,
                                    happen_time: app.happen_time,

                                    sale_amount: app.saleSum.real_get_amount, // 销售上交
                                    sale_total_amount: app.saleSum.sale_amount,
                                    return_amount: app.saleSum.return_amount,
                                    sale_prepay_amount: app.saleSum.prepay_amount,
                                    sale_left_amount: app.saleSum.left_amount,
                                    sale_disc_amount: app.saleSum.disc_amount,

                                    get_prepay: app.prepaySheetSum.real_get_amount, // 收预收款
                                    prepay_total_amount: app.prepaySheetSum.prepay_amount,
                                    prepay_left_amount: app.prepaySheetSum.left_amount,
                                    prepay_disc_amount: app.prepaySheetSum.disc_amount,

                                    get_preget: app.pregetSheetSum.real_get_amount, // 收预收款
                                    preget_total_amount: app.pregetSheetSum.preget_amount,
                                    preget_left_amount: app.pregetSheetSum.left_amount,
                                    preget_disc_amount: app.pregetSheetSum.disc_amount,

                                    get_arrears: app.getArrearSheetSum.real_get_amount, // 收欠款
                                    arrears_disc_amount: app.getArrearSheetSum.disc_amount,
                                    arrears_prepay_amount: app.getArrearSheetSum.prepay_amount,
                                    arrears_total_amount: Number(app.getArrearSheetSum.real_get_amount) + Number(app.getArrearSheetSum.disc_amount) + Number(app.getArrearSheetSum.prepay_amount),

                                    fee_out_total_amount: app.feeOutSheetSum.fee_out_total_amount, //费用支出总额
                                    fee_out: app.feeOutSheetSum.real_get_amount, //费用支出(实际支出)
                                    fee_out_left_amount: app.feeOutSheetSum.fee_out_left_amount, //费用支出欠款
                                    fee_out_disc_amount: app.feeOutSheetSum.fee_out_disc_amount, //费用支出优惠
                                    
                                    income: app.incomeSheetSum.real_get_amount, //其他收入（实际收入）
                                income_total_amount: app.incomeSheetSum.income_total_amount, //总额
                                income_left_amount: app.incomeSheetSum.income_left_amount,  //欠款
                                income_disc_amount: app.incomeSheetSum.income_disc_amount //优惠

                                }
                                if(sellerID!==getterID){
                                    bw.toast("对账员工和单据内业务员不一致,请检查后再交账", 3000) 
                                }else{
                                    app.debounce(app.submitCheckAccount,JSON.stringify(params));
                                }
                                
                            } else {
                                bw.toast("未选择单据", 3000)
                                // Toast.fail('未选择单据')
                            }
                        }, "提示");
                    },
                    debounce(fn, params, delay = 1000) {
                        if (this.timeoutId) clearTimeout(this.timeoutId);
                        this.timeoutId =  setTimeout(function () {
                            if(params) {
                                   fn(params);
                            } else {
                                  fn();
                            }
                       
                      }, delay);
                    },
                
                    submitCheckAccount(obj) {
                        const loading = this.$loading({
                          lock: true,
                          text: '交账中,请稍后...',
                          spinner: 'el-icon-loading',
                          background: 'rgba(255, 255, 255, 0.7)',
                          customClass: 'el-loding-my'
                        });
                        $.ajax({
                            url: '/AppApi/SheetCheckAcount/SubmitCheckAccount',
                            type: 'post',
                            data: obj,
                            contentType: "application/json;charset=UTF-8",
                            dataType: 'json'
                        }).then(function (res) {
                            console.log("res", res)
                            loading.close();
                            if (res.result === "OK") {
                                bw.toast("交账成功", 3000)
                                app.sheets.sheet_id = res.sheet_id
                                app.sheets.happen_time = res.happen_time
                                //app.print_title = res.printTitle
                            //    app.notShow()
                                app.dayCheckSheetsID = res.daySheetsID
                                setTimeout(() => {
                                    window.srcWindow.app.query()
                                }, 300)
                            }
                            else {
                                loading.close();
                                bw.toast(res.msg, 5000);
                                //bw.toast("对账失败,单据列表里存在已红冲单据,请重新打开!", 3000)
                            }  

                        });
          
                    },
                    redCheckSheet() {
                        if (this.sheets.sheet_id) {
                            jConfirm('确认红冲？', function () {
                                let params = {
                                    operKey: g_operKey,
                                    sheet_id: app.sheets.sheet_id
                                }
                                app.sheetCheckRed(JSON.stringify(params))
                            }, "提示");
                        }

                    },
                    
                    sheetCheckRed(obj) {
                        $.ajax({
                            url: '/AppApi/SheetCheckAcount/RedCheckAccount',
                            type: 'post',
                            data: obj,
                            contentType: "application/json;charset=UTF-8",
                            dataType: 'json'
                        }).then(function (res) {
                            console.log("res", res)
                            if (res.result === "OK") {
                                if (res.result === 'OK') {
                                    bw.toast("红冲成功", 3000)
                                    app.sheets.red_flag = '1'
                                    app.title += '(已红冲)'
                                    setTimeout(() => {
                                        window.srcWindow.app.query()
                                    }, 300)
                                } else {
                                    bw.toast("红冲失败，请检查网络连接", 3000)
                                }
                            }
                        })
                    },
                    printType(item) {
                        console.log(item)
                    },
                    showDoPrint() {

                        this.dialogVisible = true
                    },
                    printBtn() {
                        this.isPrintCss = true
                        this.$nextTick(function () {
                            this.doPrint();
                        })
                    },
                    notShow() {
                        this.showAllCheckSheets()
                       
             
                    },
                    showAllNew() {
                        this.showAllCheckSheets()
                        this.title = this.title + '【' + this.isAllSheet+'】'
                        
                    },

                    showAllCheckSheets() {
                        if(this.title.includes(this.isAllSheet)) {
                            return
                        }
                        

                        let obj = {
                            //window.srcWindow
                            sheetID:this.dayCheckSheetsID || this.sheets.sheet_id,
                            operKey: g_operKey,
                            sellerName: this.title + '【' + this.isAllSheet+'】',
                            happenTime: '',
                            getterID:'',
                            dateTypeIds:window.srcWindow.dateTypeIds== 'alldays' ? '1days':window.srcWindow.dateTypeIds
                        }
                        if(window.srcWindow.summCheckSheet && window.srcWindow.summCheckSheet.happenTime){
                            obj.happenTime = window.srcWindow.summCheckSheet.happenTime 
                        } else {
                             obj.happenTime =  this.sheets.happen_time
                        }

                        if(window.srcWindow.summCheckSheet && window.srcWindow.summCheckSheet.getterID){
                            obj.getterID = window.srcWindow.summCheckSheet.getterID 
                        } else {
                             obj.getterID =  window.srcWindow.g_sellerID
                        }

                         window.g_showAccountTitle = obj.sellerName
                        this.loadCheckSheet(obj)
                        
                    },
                    loadCheckSheet_notused(obj) {
                        $.ajax({
                            url: '/AppApi/SheetCheckAcount/LoadCheckSheet',
                            type: 'get',
                            data: obj,
                            contentType: "application/json;charset=UTF-8",
                            dataType: 'json'
                        }).then(function (res) {
                            if (res.result === 'OK') {
                                var count = 0;
                                var checked = true
                                updateSheetsCheck(res.saleSheets, checked)
                                updateSheetsCheck(res.borrowSheets, checked)
                                updateSheetsCheck(res.prepaySheets, checked)
                                updateSheetsCheck(res.pregetSheets, checked)
                                updateSheetsCheck(res.getArrearSheets, checked)
                                updateSheetsCheck(res.feeOutSheets, checked)
                                updateSheetsCheck(res.incomeSheets, checked)

                                res.happen_time = obj.happen_time
                             
                                res.start_time = obj.start_time
                                res.end_time = obj.end_time
                                app.sheets = res
                               
                                // app.sheets = {
                                //     ...res,
                                //     sheet_id: app.sheets.sheet_id,
                                //     happen_time: app.sheets.happen_time,
                 
                                //     start_time: app.start_time,
                                //     end_time: app.end_time
                                // }
                                app.dayCheckSheetsID = res.dayCheckSheetsID
                                window.checkedSheetsCount = count;
                                window.g_sheet = app.sheets
                                function updateSheetsCheck(sheets, check) {
                                    sheets.forEach(function (sheet) {
                                        sheet.isChecked = check
                                        if (sheet.isChecked)
                                            count++
                                    })
                                }
                                console.log("history", window.g_sheet);
                                
                            }
                        })
                    },
                    handleSearch() {
                        this.selectedSheets = []
                        this.unselectedSheets = []
                        this.allSheets.forEach(item => {
                            //if (item.sheet_no?.includes(this.searchCondition) || item.sup_name?.includes(this.searchCondition)) {
                            //?. 这种写法会导致交账单在客户端打开报错
                        var hasSheetNo = false
                        if (item.sheet) hasSheetNo = item.sheet.includes(this.searchCondition)
                        var hasSupName = false
                        if (item.sup_name !== undefined) hasSupName = item.sup_name.includes(this.searchCondition)
                        if (hasSheetNo || hasSupName) {
                                if (item.isChecked) {
                                    this.selectedSheets.push(item)
                                } else {
                                    this.unselectedSheets.push(item)
                                }
                            }
                        })
                        //this.selectedSheets.sort((a, b) => b.timestamp - a.timestamp)//不要重新排序，点全选后会乱序
                        //this.unselectedSheets.sort((a, b) => b.timestamp - a.timestamp)
                        if (this.unselectedSheets.length) {
                            this.isActive = this.selectedSheets.length
                        } else if (this.selectedSheets.length) {
                            this.isActive = 0
                        }
                    },
                    handleSearchInput(event) {
                        // 清除之前的延时器
                        if (this.debounceTimer) {
                            clearTimeout(this.debounceTimer)
                        }
                        // 设置新延时器，事件0.5秒
                        this.debounceTimer = setTimeout(() => {
                            this.handleSearch()
                        }, 500)
                    },
                    handleTableKeyDown(event) {
                        const currentIndex = this.isActive
                        const allData = this.combinedSheets
                        if (event.key === 'ArrowDown') {
                            if (currentIndex === null || currentIndex === '') {
                                this.isActive = 0
                                this.handleSearch()
                            } else {
                                const nextIndex = currentIndex + 1
                                if (nextIndex < allData.length) {
                                    this.isActive = nextIndex
                                }
                            }
                            event.preventDefault()
                        } else if (event.key === 'ArrowUp') {
                            if (currentIndex === null) {
                                this.isActive = 0
                            } else {
                                const preIndex = currentIndex - 1
                                if (preIndex >= 0) {
                                    this.isActive = preIndex
                                }
                            }
                            event.preventDefault()
                        } else if (event.key === ' ') {
                            if (this.isActive !== null && this.isActive < allData.length) {
                                const target = this.allSheets.find(sheet => sheet.sheet_id === allData[this.isActive].sheet_id)
                                target.isChecked = !target.isChecked
                                target.timestamp = new Date().getTime()
                                allData[this.isActive].isChecked = target.isChecked
                            }
                            event.preventDefault()
                        } else if (event.key === 'Enter') {
                            this.handleSearch()
                        }
                    },
                    closeDialog() {
                        this.dialogVisible = false;
                        this.isPrintCss = false;
                        this.show_print_summary = true;
                        this.show_print_detailed = true;
                        this.show_print_goods = true;
                    },
                    doPrint() {
                        $(".print_content").print({
                            globalStyles: true,//是否包含父文档的样式，默认为true
                            mediaPrint: false,//是否包含media='print'的链接标签。会被globalStyles选项覆盖，默认为false
                            stylesheet: null,//外部样式表的URL地址，默认为null
                            noPrintSelector: ".no-print",//不想打印的元素的jQuery选择器，默认为".no-print"
                            iframe: true,//是否使用一个iframe来替代打印表单的弹出窗口，true为在本页面进行打印，false就是说新开一个页面打印，默认为true
                            append: null,//将内容添加到打印内容的后面
                            prepend: null,//将内容添加到打印内容的前面，可以用来作为要打印内容
                            deferred: $.Deferred()//回调函数
                        });
                        this.isPrintCss = false;
                        this.show_print_summary = true;
                        this.show_print_detailed = true;
                        this.show_print_goods = true;
                        this.dialogVisible = false;
                    },
                    fix(amount) {
                        return toMoney(amount)
                    }
                }

            });
            $(document).ready(function () {
             @*   $('#print_window').jqxWindow({ width: 300, height: 150, theme: 'summer', autoOpen: false, okButton: $('#okButton') });
                $("#print_summary_checkbox").jqxCheckBox({ width: 120, height: 25, checked: true });
                $("#print_detailed_checkbox").jqxCheckBox({ width: 120, height: 25, checked: true });
                $("#print_goods_checkbox").jqxCheckBox({ width: 120, height: 25, checked: true });*@
                $("#jqxdatetimeHappen_time").jqxDateTimeInput({ culture: 'zh-CN', width: '150px', height: '25px', formatString: "yyyy-MM-dd" });
                @* $('#print_window').on('close', function (event) {
                    app.isPrintCss = false;
                    app.show_print_summary = true;
                    app.show_print_detailed = true;
                    app.show_print_goods = true;
                  $('#print_summary_checkbox').jqxCheckBox({ checked: true });
                    $('#print_detailed_checkbox').jqxCheckBox({ checked: true });
                    $('#print_goods_checkbox').jqxCheckBox({ checked: true });
                });*@
    @*            $("#print_summary_checkbox").bind('change', function (event) {
                    var checked = event.args.checked;
                    app.show_print_summary = checked
                });
                $("#print_detailed_checkbox").bind('change', function (event) {
                    var checked = event.args.checked;
                    app.show_print_detailed = checked
                });
                $("#print_goods_checkbox").bind('change', function (event) {
                    var checked = event.args.checked;
                    app.show_print_goods = checked
                });*@


                app.happen_time = app.computeDate($("#jqxdatetimeHappen_time").jqxDateTimeInput('getDate'), 0) + app.dateFormat(" HH:MM:SS", new Date());
                $('#jqxdatetimeHappen_time').on('change', function (event) {
                    app.happen_time = app.dateFormat("YYYY-mm-dd", new Date(event.args.date));
                });
            });

        </script>
    <script>
        window.g_bRefreshOnTabClicked = true
        /*function QueryData() {
            window.app.loadRefresh()
        }*/
    </script>
</body>
</html>