﻿@page
@model ArtisanManage.Pages.BaseInfo.AttenanceSettingViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script src="//api.map.baidu.com/api?type=webgl&v=1.0&ak=@Html.Raw(Model.BaiduKey)"></script>

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.addEventListener('message', function (rs) {
            console.log(rs)
            if (rs.data.msgHead === "AttendanceSettingEdit") {
                console.log($("#popItem"))
                window.QueryData()
                $('#popItem').jqxWindow('close');
            }
        })
        $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                return false;
            });
        $(document).ready(function () {
            window.contextMenu1 = $("#jqxMenu_1").jqxMenu({ width: '120px', autoOpenPopup: false, mode: 'popup' });
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)
            QueryData();
            let windowHeight = document.body.offsetHeight-50
            let windowWidth = document.body.offsetWidth-50
            $("#popItem").jqxWindow({isModal: true, modalOpacity: 0.3, height: 650, width: 600,maxHeight:windowHeight,maxWidth:windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade'});})
            $('#popClient').jqxWindow('open'); 

       function groupNameRender(row, column, value, p4, p5, rowData) {
            return `
                <div style="display:flex;flex-direction:row;">
                    <div style="color:#409EFF;margin-left:5px;margin-top:5px;cursor:pointer;"  onclick='onGridRowEdit(${rowData.group_id})'>${rowData.group_name}</div>
                </div>`
        }
        function removeSchedule(scheduleID,groupID) {
             let param = {
                operKey: g_operKey,
                group_id:groupID,
                schedule_id:scheduleID
            }
            $.ajax({
                url: "/api/AttenanceSettingView/RemoveSchedule",
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(param),
                success: (res) => {
                    QueryData();
                }
            })
        }
        function editSchedule(scheduleID, groupID) {
            showAddScheduleDialog(groupID, scheduleID)
        }
       function scheduleRender(row, column, value, p4, p5, rowData) {
           var schedules = rowData.schedule
           if (!schedules) {
               schedules = []
           } else {
               schedules = JSON.parse(rowData.schedule)
           }
           var scheduleDiv = schedules.map((d,index) => {
                return `<div  style='margin-right:4px;padding:2px;border-radius:4px;display:flex;flex-direction:row;background:#f0f0f0;'>
                                    <div onclick='editSchedule("${d.id}","${rowData.group_id}")' style='margin-right:2px;'>${d.start_time}-${d.end_time}</div>
                            <div onclick='removeSchedule("${d.id}","${rowData.group_id}")'>
                                    <svg width="15" height="15" style="cursor:pointer;">
                                        <use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#close'  />
                                    </svg>
                            </div>
                        </div>
                      `
           }).join("")
                       return `
                <div style="display:flex;flex-direction:row;">
                    ${scheduleDiv}
                      <div onclick="showAddScheduleDialog('${rowData.group_id}','')" style='margin-left:10px;'>
                    <svg width="18" height="18" style="cursor:pointer;margin-left:10px;margin-top:4px;"  fill="#999" class='addEmployees'>
                         <use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#thinAdd'  />
                    </svg>
                </div>
                </div>
              `
        }
        function addressRender(row, column, value, p4, p5, rowData) {
            //return `<div style="margin-top:4px;margin-left:2px;">${rowData.check_addr}<span style="color:#409EFF;margin-top:5px;" onclick='onEditAddr(${rowData.group_id},"${rowData.check_addr}")'>编辑</span></div>`
        return `<div style="margin-top:4px;margin-left:2px;">${rowData.check_addr}</div>`
        }
        function membersNameRender(row, column, value, p4, p5, rowData) {
            const members_names = rowData.members_name.split(",")
            const memeberElements = members_names.filter(name=>name!='').map((member, index) => {
                return `
                <div style='display:flex;margin-left:6px;background-color:#eee;padding:2px;border-radius:4px;margin-top:3px;' onclick='removeMember("${rowData.group_id}","${index}","${rowData.members_name}","${rowData.members_id}")'>
                <div>${member}</div>
                            <svg width="15" height="15" style="cursor:pointer;">
                                        <use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#close'  />
                                    </svg>
                </div>
                `
            })
            return `<div style='display:flex;flex-direction:row;'>` + memeberElements.filter(element=>element!='').join("") +
                `       <div onclick="showAddOperatorDialog('${rowData.group_id}','${rowData.members_name}','${rowData.members_id}')" style='margin-left:10px;'>
                            <svg width="18" height="18" style="cursor:pointer;margin-left:10px;margin-top:4px;"  fill="#999" class='addEmployees'>
                                <use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#thinAdd'  />
                            </svg>
                        </div>
                   </div>`

        }
        function onRemoveGroup(group_id) {
            let param = {

                operKey: g_operKey,
                group_id
            }
            $.ajax({
                url: "/api/AttenanceSettingView/RemoveGroup",
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(param),
                success: (res) => {
                    QueryData();
                }
            })
        }
        function showAddOperatorDialog(group_id, members_name, members_id) {
            const groupInfo = { group_id, members_name, members_id }
            $.ajax({
                url: "/api/AttenanceSettingView/GetNoGroupOperators?operKey=" + g_operKey,
                method: 'GET',
                success: (res) => {
                   // 
                   // $("#noGroupOperator").jqxWindow({ height: 150, width: 250, theme: 'summer' });
                   // $("#noGroupOperator").jqxWindow("open");
                   $("#noGroupOperator").jqxWindow({isModal: true, modalOpacity: 0.3,height: 300, width: 250, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade'})
                   $('#noGroupOperator').jqxWindow('open');
                   $("#content").html(noGroupWindowTemplate(res, groupInfo ))
                }
            })
        }
        function showAddScheduleDialog(group_id,schedule_id) {
   
                   // $("#noGroupOperator").jqxWindow({ height: 150, width: 250, theme: 'summer' });
                   // $("#noGroupOperator").jqxWindow("open");
            $("#addSchedule").jqxWindow({isModal: true, modalOpacity: 0.3,height: 600, width: 600, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade'})
            $('#addSchedule').jqxWindow('open');
            $("[id^='form_']").each((i, e) => {
                $(e).val("")
            })
            $('#addSchedule').attr("edit-group-id",group_id)
            $('#addSchedule').attr("edit-schedule-id", schedule_id)
         
           //$("#addschedule-content").html(scheduleWindowTemplate(group_id ))
           onFixPositionChange()
            onCheckAddrChange()
            onCheckDistanceChange()
            onFixPositionChange()
            if (schedule_id) {
                $.ajax({
                    url: "/api/AttenanceSettingView/GetSchedule?operKey=@Model.OperKey",
                    method: "POST",
                    contentType: 'application/json',
                    data: JSON.stringify({
                        group_id,
                        schedule_id,
                        operKey: "@Model.OperKey"
                    }),
                    success: (res) => {
                        console.log(res.data)
                        document.getElementById("form_start_time").value = res.data.start_time
                        document.getElementById("form_end_time").value = res.data.end_time
                        if (res.data.check_longitude) {
                            $("#form_check_longitude").attr("value", res.data.check_longitude)
                        }
                        if (res.data.check_distance) {
                            document.getElementById("form_check_distance").value = res.data.check_distance
                        }
                        if (res.data.check_addr) {
                            document.getElementById("form_check_addr").value = res.data.check_addr
                        }
                        if (res.data.check_latitude) {
                            $("#form_check_latitude").attr("value", res.data.check_latitude)
                        }

                        document.getElementById("form_fix_in_position").checked = res.data.fix_in_position == "True"
                        document.getElementById("form_fix_out_position").checked = res.data.fix_out_position == "True"
                        initMap()
                    }
                })
            } else {
                initMap()
            }
        }
        function saveSchedule() { 
             var editGroupId =  $('#addSchedule').attr("edit-group-id")
            var editScheduleId = $('#addSchedule').attr("edit-schedule-id")
            var fixInPoistionChecked = document.getElementById("form_fix_in_position").checked
            var fixOutPositionCheck = document.getElementById("form_fix_out_position").checked
            var latitude = document.getElementById("form_check_latitude").value 
            var longitude = document.getElementById("form_check_longitude").value
            var hasPosition = longitude && latitude 
            if ((fixInPoistionChecked || fixOutPositionCheck) && !hasPosition) {
                bw.toast("请设置考勤地点")
                return
            }
            if (!longitude) {
                longitude = 0
            }
            if (!latitude) {
                latitude = 0
            }
            console.log("editScheduleId", editScheduleId)
             let param = {
                schedule_id: editScheduleId,
                start_time: document.getElementById("form_start_time").value.replace("：", ":"),
                end_time: document.getElementById("form_end_time").value.replace("：", ":"),
                check_longitude: longitude,
                check_distance: document.getElementById("form_check_distance").value,
                check_addr: document.getElementById("form_check_addr").value,
                check_latitude: latitude,
                fix_in_position: fixInPoistionChecked ? "True" : "False",
                fix_out_position: fixOutPositionCheck ? "True" : "False",
                operKey: g_operKey,
                group_id:editGroupId
            }
            $.ajax({
                url: "/api/AttenanceSettingView/AddSchedule",
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(param),
                success: (res) => {
                    QueryData();

                    $("#addSchedule").jqxWindow("close")
                }
            })
            return `
         
            `
        }
        function onEditAddr(group_id, check_address) {
            $("#editAddress").jqxWindow({ height:250, width: 500, theme: 'summer' });
            $("#editAddress").jqxWindow("open");
            $("#addressInput").val(check_address)
            $(addressInput).on("change", e => {
                $.ajax({
                    url: "/api/AttendanceSettingEdit/SearchPoi",
                    data: {
                        key: e.target.value,
                        operKey: window.g_operKey
                    },
                    success: (res) => {
                        const pois = res.result
                        var curIndex=0
                        const poisources = pois.map(poi => {
                            console.log({ poi })
                            curIndex++
                            return `<div style="padding:10px;background:${curIndex % 2 == 0 ? '#fff' : '#eee'}" onclick="editCheckAddress(${group_id},'${poi.province + poi.city + poi.district + poi.name}',${poi.location.lng},${poi.location.lat})">` + poi.province + poi.city + poi.district + poi.name + `</div>`
                        })
                        $("#addressResult").html(poisources.join(""))


                    }
                })
                console.log(e.target.value)
            })
        }
        function editCheckAddress(group_id,address,longitude,latitude) {
            let param = {
                check_addr: address,
                check_longitude: longitude,
                check_latitude: latitude,
                operKey: g_operKey,
                group_id
            }
            $.ajax({
                url: "/api/AttenanceSettingView/UpdateTheGroupCheckAddr",
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(param),
                success: (res) => {
                    QueryData();
                    $("#editAddress").jqxWindow("close")
                }
            })
        }
        function noGroupWindowTemplate(operators, groupData) {
            let opertorRowDivs = []
            console.log({ groupData })
            var curIndex=0
            operators.map((operator) => {
                if (!operator.group_id) {
                    let members_ids = groupData.members_id.split(",")
                    members_ids.push(operator.oper_id)
                    const newMembersID = members_ids.join(",")
                    let members_names = groupData.members_name.split(",")
                    members_names.push(operator.oper_name)
                    const newMembersName = members_names.join(",")
                    opertorRowDivs.push(`<div style="padding:10px;background:${curIndex % 2 == 0 ? '#fff' : '#eee'}" onclick="confirmAddOperator('${groupData.group_id}','${newMembersID}','${newMembersName}')"> ${operator.oper_id} ${operator.oper_name}</div>`)
                    curIndex++
                }
            })
            return "<div  class='operatorRow_item'>" + opertorRowDivs.join("") + "</div>"
            }

        function confirmAddOperator(group_id, members_id, members_name) {
            let param = {
                members_name: members_name,
                members_id: members_id,
                operKey: g_operKey,
                group_id
            }
            $.ajax({
                url: "/api/AttenanceSettingView/UpdateTheGroupMembers",
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(param),
                success: (res) => {
                    QueryData();
                    $("#noGroupOperator").jqxWindow("close")
                }
            })
        }
        function removeMember(group_id,index,members_name,members_id){
            let members_names = members_name.split(",")
            members_names.splice(Number(index), 1)
            let members_ids = members_id.split(",")
            members_ids.splice(Number(index),1)
            let param = {
                members_name: members_names.join(","),
                members_id: members_ids.join(","),
                operKey: g_operKey,
                group_id
            }
            $.ajax({
                url: "/api/AttenanceSettingView/UpdateTheGroupMembers",
                method: 'POST',
                contentType:'application/json',
                data: JSON.stringify(param),
                success: (res) => {
                    QueryData();
                }
            })
        }
        function onGridRowEdit(group_id) {
                $('#popItem').jqxWindow('open');
                $("#popItem").jqxWindow('setContent', `<iframe src="/BaseInfo/AttendanceSettingEdit?group_id=${group_id}&&operKey=${g_operKey}" width="200%" height="100%" scrolling="yes" frameborder="no"></iframe>`);
        }
        function btnAddGroup_click() {
            console.log("console.log(rowData.group_id)")
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', `<iframe src="/BaseInfo/AttendanceSettingEdit?operKey=${g_operKey}" width="100%" height="100%" scrolling="yes" frameborder="no"></iframe>`);
        }
        ////////////////////////
        function initMap() {
            var map = new BMapGL.Map("baidumap");    // 创建Map实例
            map.centerAndZoom(new BMapGL.Point(116.280190, 40.049191), 19);  // 初始化地图,设置中心点坐标和地图级别
            map.enableScrollWheelZoom(true);     //开启鼠标滚轮缩放
            g_map = map;
            var fix_position = $("#fix_position").val()
            console.log(fix_position)
            // if (fix_position==='False'){
            //     $("#div_check_addr").css("display","none")
            //     $("#div_check_distance").css("display","none")
            // }
            var addr_lng = $("#check_longitude > input").attr("data-value") || $("#form_check_longitude").attr("value")
            var addr_lat = $("#check_latitude > input").attr("data-value") || $("#form_check_latitude").attr("value")
            console.log(addr_lng, addr_lat)
            if (addr_lng && addr_lat) {
                renderPoiOnMap(addr_lng, addr_lat)
            }
            const check_addr = $("#check_addr  > input").attr("data-value") || document.getElementById("form_check_addr").value
            if (!check_addr) {
                const companyCity = localStorage.getItem("companyCity")
                $("#check_addr").val(companyCity)
                getPois(companyCity, res => {
                    const poi = res.result[0]
                    renderPoiOnMap(poi.location.lng, poi.location.lat)
                    $("#check_addr").val('')
                })
            }
        }
        ////////////////////////////////////////////
        function onFixPositionChange() {
            $("#div_fix_position").click((e) => {
                console.log("onFixPositionChange")
                var fixPosition = $("#fix_position").val()
                //  if (!fixPosition){
                // $("#div_check_addr").css("display","none")
                // $("#div_check_distance").css("display","none")
                //  }else{
                //  $("#div_check_addr").css("display","block")
                //  $("#div_check_distance").css("display","block")
                //  }
            })
        }
        function onCheckDistanceChange() {
            $("#form_check_distance").on("input propertychange", e => {
                const distance = e.target.value
                g_roundCircle.setRadius(Number(distance))
            })
        }
        function onCheckAddrChange() {
            $("#form_check_addr").on("input propertychange", e => {
                const checkAddr = e.target.value
                if (!checkAddr) {
                    return
                }
                getPois(checkAddr, res => {
                    const pois = res.result
                    if (pois.length == 0) {
                        $("#address_result").css("display", "block")
                        return
                    }
                    var curIndex = 0
                    const poisources = pois.map(poi => {
                        console.log({ poi })
                        curIndex++
                        return `<div style="padding:10px;cursor:pointer;background:${curIndex % 2 == 0 ? '#fff' : '#eee'}" onclick="attrAddressInput('${poi.province + poi.city + poi.district + poi.name}',${poi.location.lng},${poi.location.lat})">` + poi.province + poi.city + poi.district + poi.name + `</div>`
                    })
                    $("#address_result").html(poisources.join(""))
                    $("#address_result").css("display", "block")
                })
            })
        }
        function getPois(searchKey, cb) {
            $.ajax({
                url: "/api/AttendanceSettingEdit/SearchPoi",
                data: {
                    key: searchKey,
                    operKey: window.g_operKey
                },
                success: (res) => {
                    cb(res)
                }
            })
        }
        function attrAddressInput(address, addr_lng, addr_lat) {
            $("#form_check_addr").val(address)
            $("#form_check_longitude").val(addr_lng)
            $("#form_check_latitude").val(addr_lat)
            $("#address_result").css("display", "none")
            renderPoiOnMap(addr_lng, addr_lat)

        }
        function renderAttendanceRound(markerPoint, distance) {
            var circle = new BMapGL.Circle(markerPoint, Number(distance), { fillColor: "blue", strokeWeight: 1, fillOpacity: 0.3, strokeOpacity: 0.3 });
            g_map.addOverlay(circle);
            g_roundCircle = circle
        }
        function renderPoiOnMap(addr_lng, addr_lat) {
            g_map.clearOverlays()
            var markerPoint = new BMapGL.Point(addr_lng, addr_lat)
            var marker = new BMapGL.Marker(markerPoint, { enableDragging: true });
            marker.addEventListener("dragend", ({ latLng }) => {
                var dragCenterPoint = new BMapGL.Point(latLng.lng, latLng.lat)
                g_roundCircle.setCenter(dragCenterPoint)
                var geoc = new BMapGL.Geocoder();
                geoc.getLocation(dragCenterPoint, function (rs) {
                    var addComp = rs.addressComponents;
                    $("#check_addr").val(addComp.province + addComp.city + addComp.district + addComp.street + addComp.streetNumber);
                });
            })

            g_map.centerAndZoom(markerPoint, 15);
            g_map.addOverlay(marker);
            const checkDistance = $("#form_check_distance").attr("data-value")
            if (checkDistance > 0) {
                renderAttendanceRound(markerPoint, checkDistance)
            } else {
                renderAttendanceRound(markerPoint, 0.01)
            }
        }
    </script>
</head>


<body class='default' style="overflow:hidden;">
    <style>
        
        .jqx-popover {
            border-color: #e2e2e2;
            border-radius: 20px;
            box-shadow: 20px 20px 50px 0px rgba(0, 0, 0, 0.25);
        }
    </style>
    <div id="divHead" style="display:flex;margin-top:20px;">
        <div>
            <button onclick="btnAddGroup_click()" style="width:100px;">添加考勤组</button>
        </div>
        <!--
                <div>
            <button onclick="btnAddGroup_click()" style="width:100px;">添加班次</button>
        </div>
        -->
    
    </div>
    <div style="flex-grow:1;display:flex;width:100%;height:100%;">
        <div style="width:calc(100% - 200px);height:100%; margin-left:10px;">
            <div><div style="float:right;margin-right:50px;height:20px;font-size:12px;color:#999;">共<label id="rows_count">0</label>行</div></div>
            <div id="gridItems" style="margin-top:0px;margin-left:10px; margin-bottom:2px;width:calc(100% - 20px);height:calc(100% - 20px);"></div>
        </div>
    </div>
    <div id="popItem" style="display:none;">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">考勤组信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    <!--
    <div id='noGroupOperator'>
        <div>请选择业务员</div>
        <div id="content">Content</div>
    </div>-->
    <div id="noGroupOperator" style="display:none">
        <div  style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">请选择业务员</span></div>
        <div id="content" style="overflow-x:hidden;overflow-y:scroll;"> </div>
    </div>
    <div id="addSchedule" style="display:none">
        <div  style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">设置分段考勤时间</span></div>
        <div id="addSchedule-content" style="overflow-x:hidden;overflow-y:scroll;">
                   <div>
                    <div style="margin:10px;">签到时间：<input type='text' id="form_start_time"/></div>
                    <div style="margin:10px;">签退时间：<input type='text' id="form_end_time" /></div>
                    <div style="margin:10px;">考勤地址：<input type='text' id="form_check_addr" /></div>
                    <div style="margin:10px;" >
                        考勤距离<input type='text' id="form_check_distance" />
                        <input type="checkbox" id="form_fix_in_position" />固定签到位置
                        <input type="checkbox" id="form_fix_out_position" />固定签退位置
                    </div>
                <div style="display:none;">
                        <input type="text" id="form_check_longitude" value="" />
                        <input type="text" id="form_check_latitude" value=""/>
                </div>
                    <div style="width:300px;height:300px;" id="baidumap"></div>
                <div style='display:none;z-index:100;' class="address_result" id="address_result"></div>

    
                    <button onclick="saveSchedule()">保存</button>
                </div>
            </div>
    </div>
    <div id='jqxMenu_1' style="display:block;">
        <ul>
            <li >删除</li>
        </ul>
    </div>
    <div id="editAddress" style="display:none">
        <div  style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">编辑地址</span></div>
        <div>
            <div style="height='50px'">
                <input placeholder="请输入地址" id="addressInput" type="text" />
            </div>

            <div id="addressResult">
            </div>
        </div>

    </div>
</body>
</html>
<style type="text/css">
    .address_result {
        position: absolute;
        height: 100px;
        overflow-y: scroll;
        left: 90px;
        top: 150px;
        width: 300px;
    }

    #baidumap {
        height: 400px;
        margin: 10px;
        width: 100%;
        z-index: 1;
    }
</style>