using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using HuaWeiObsController;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.SS.UserModel.Charts;
using static ArtisanManage.Models.DataItem;
 

namespace ArtisanManage.Pages
{
    public class BorrowItemSheetViewModel : PageQueryModel
    {
        
        public BorrowItemSheetViewModel(CMySbCommand cmd) : base(Services.MenuId.sheetBorrowItem)
        {
            //Database = "";
            this.cmd = cmd;
            cmd.ActiveDatabase = "";
            this.PageName = "BorrowItemSheetView";
            this.PageTitle = "借货单";
            CanQueryByApproveTime = true;

            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="T.happen_time",   CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期", FldArea="divHead",CtrlType="jqxDateTimeInput", SqlFld="T.happen_time",   CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"queryTimeAccord",new DataItem(){FldArea="divHead",Title="时间类型",LabelFld = "time_status_name",ButtonUsage = "list",CompareOperator="=",Value="byApproveTime",Label="审核时间",ForQuery=false, AutoRemember=true,
                        Source = @"[{v:'byApproveTime',l:'审核时间'},
                                   {v:'byHappenTime',l:'交易时间'},
                                   {v:'byMakeTime',l:'制单时间'},
                                   {v:'byCheckedTime',l:'交账时间'},
                                    {v:'bySendTime',l:'送货时间'},
                                     ]"
                }},

                //{"department_id_s",new DataItem(){Title="业务部门（所属）",TreePathFld="department_path_s",Hidden=true, FldArea="divHead",SqlFld="(case when department_id is not null then department_id else department_id_s end)", LabelFld="department_id_label_s", CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=true,DropDownWidth="150", CompareOperator="=",
                //    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department" }},
                //{"depart_path_s",new DataItem(){Title="业务部门",PlaceHolder="含子部门", Hidden=true, FldArea="divHead",LabelFld="depart_path_label_s", CtrlType="jqxDropDownTree", DropDownHeight="200",MumSelectable=true,DropDownWidth="150", CompareOperator="like",LikeWrapper="/",
                //    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department" }},
                //{"department_id_m",new DataItem(){Title="制单部门（所属）",TreePathFld="department_path_m",Hidden=true, FldArea="divHead", LabelFld="department_id_label_m", CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=true,DropDownWidth="150", CompareOperator="=",
                //    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department" }},
                //{"depart_path_m",new DataItem(){Title="制单部门",PlaceHolder="含子部门", Hidden=true, FldArea="divHead",LabelFld="depart_path_label_m", CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=true,DropDownWidth="150", CompareOperator="like",LikeWrapper="/",
                //    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department" }},

                {"sheet_type",new DataItem(){FldArea="divHead",SqlFld="T.sheet_type", Title="单据类型",LabelFld="sheet_type_name",ButtonUsage="list",Source = "[{v:'JH',l:'借货单'},{v:'HH',l:'还货单'},{v:'',l:'所有'}]",CompareOperator="=", ForQuery=false}},
                {"borrow_mode",new DataItem(){FldArea="divHead",SqlFld="T.borrow_mode", Title="借还模式",LabelFld="borrow_mode_name",ButtonUsage="list",Source = "[{v:'QTY',l:'按数量'},{v:'AMT',l:'按金额'},{v:'',l:'所有'}]",CompareOperator="="}},
                {"supcust_id",CommonTool.GetDataItem("supcust_id",new DataItemChange{Checkboxes = true,SqlFld = "T.supcust_id,acct_cust_id"}) },
                
                
                {"seller_id",new DataItem(){FldArea="divHead",Title="业务员",Checkboxes=true, LabelFld="seller_name",ButtonUsage="list",SqlForOptions=CommonTool.selectSellers,CompareOperator="="}},//SqlForOptions="select oper_id as v,oper_name as l,py_str as z from info_operator"
                {"senders_id",new DataItem(){FldArea="divHead",Title="送货员",Checkboxes=false, LabelFld="senders_name",ButtonUsage="list",SqlForOptions=CommonTool.selectSenders,CompareOperator="like"}},//SqlForOptions="select oper_id as v,oper_name as l,py_str as z from info_operator"
                //{"getter_id",new DataItem(){FldArea="divHead",Title="收款人",Hidden=true, Checkboxes=true,  ButtonUsage="list",SqlForOptions=CommonTool.selectGetters,CompareOperator="="}},//SqlForOptions="select oper_id as v,oper_name as l,py_str as z from info_operator"
               
                {"other_region",new DataItem(){Title="片区",MumSelectable=true, FldArea="divHead",LabelFld="region_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MaxRecords="500", TreePathFld="region_path",CompareOperator="like", ForQuery=false,
                    SqlForOptions="select region_id as v,region_name as l,mother_id as pv from info_region order by order_index , region_id",
                    DealQueryItem = (value) =>
                    {
                        return "/"+value+"/";
                    }
                }},
                {"branch_id",new DataItem(){Title="仓库",FldArea="divHead",LabelFld="branch_name",ButtonUsage="list",CompareOperator="=",SqlFld="T.branch_id",
                    SqlForOptions=CommonTool.selectBranch}},         
                {"status",new DataItem(){FldArea="divHead",Title="单据状态",LabelFld = "status_name",ButtonUsage = "list",CompareOperator="=",Value="normal",Label="正常单据",
                        Source = @"[{v:'normal',l:'正常单据',condition:""T.red_flag is null""},
                                   {v:'unapproved',l:'未审核单据',condition:""T.approve_time is null""},
                                   {v:'approved',l:'已审核单据',condition:""T.approve_time is not null and red_flag is null""},
                                   {v:'toReview',l:'待复核单据',condition:""T.approve_time is not null and red_flag is null and T.review_time is null""},
                                   {v:'reviewed',l:'已复核单据',condition:""T.review_time is not null and red_flag is null""},
                                   {v:'red',l:'红冲单',condition:""T.red_flag in ('1','2') ""},
                                   {v:'all',l:'所有',condition:""true""}]"
                }},
                {"brand_id",new DataItem(){Title="品牌",FldArea="divHead",Checkboxes=true,LabelFld="brand_name",ButtonUsage="list",CompareOperator="=",ForQuery=false,DropDownWidth="300",QueryByLabelLikeIfIdEmpty=true,
                    SearchFields="['l']",SqlForOptions = CommonTool.selectBrands}},
                {"item_class",new DataItem(){Title="类别",FldArea="divHead",LabelFld="class_name",CtrlType="jqxDropDownTree",MumSelectable=true,CompareOperator="like",ForQuery=false,
                   SqlForOptions=CommonTool.selectClasses,MaxRecords="500"}},

                {"item_id",new DataItem(){Title="商品",FldArea="divHead",LabelFld="item_name",ButtonUsage="event",CompareOperator="=",ForQuery=false,DropDownWidth="300",QueryByLabelLikeIfIdEmpty=true,
                   SearchFields=CommonTool.itemSearchFields,Hidden=true,
                SqlForOptions =CommonTool.selectItemWithBarcode  }},
                {"make_brief",new DataItem(){FldArea="divHead",Title="备注", CompareOperator="like"}},
                {"remark",new DataItem(){FldArea="divHead",Title="行备注", CompareOperator="like",Hidden=true,ForQuery=false}},
                {"sheet_no",new DataItem(){FldArea="divHead",Title="单号", CompareOperator="like"}},

                {"sender_id_to_set",new DataItem(){Title="送货员",Checkboxes=true, LabelFld="sender_id_to_set_name",ButtonUsage="list",SqlForOptions=CommonTool.selectSenders,ForQuery=false}},//SqlForOptions=

                {"print_count",new DataItem(){FldArea="divHead",Hidden=true,QueryOnChange=true, Title="打印状态",LabelFld = "print_status_name",ButtonUsage = "list",CompareOperator="=",Value="all",Label="所有",
                        Source = @"[{v:'all',l:'所有',condition:""true""},
                                   {v:'unprinted',l:'未打印单据',condition:""ss.sheet_print_count is null and ss.sum_print_count is null""},
                                   {v:'printed',l:'已打印单据',condition:""(ss.sheet_print_count<>0 or ss.sum_print_count<>0)""} ]"

            }},

            };
            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true, 
                     IdColumn="sheet_id",
                     HasCheck=true,
                     Sortable=true,
                     PageByOverAgg=false,
               //      AllowMultiColumnsSort=true,
                     ColumnsHeight=15,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       //{"available",  new DataItem(){ threestatecheckbox=true, columntype="checkbox", Width="70",ForQuery=false } },
                       {"sheet_id", new DataItem(){Title="sheet_id", Hidden=true,HideOnLoad=true, Linkable=true, Width="80",SqlFld="T.sheet_id"}},
                       {"sheet_status", new DataItem(){Title="状态",  Width="55",SqlFld="(case when T.red_flag='2' then '红字单' when T.red_flag='1' then '已红冲' when T.approve_time is null then '未审' when T.review_time is null then '已审'  else '已复核' END)"}},
                       {"sheet_type", new DataItem(){Title="单据类型",  Width="120",SqlFld="(case WHEN (sheet_type = 'JH' or sheet_attribute ->> 'j' = 'true') THEN '借货单' ELSE '还货单' END)"}},
                       {"sheetType", new DataItem(){Title="类型",  Width="120",Hidden=true, SqlFld="sheet_type"}},
                       {"seller_name", new DataItem(){Title="业务员",  Width="80",Sortable=true}},
                       {"maker_name", new DataItem(){Title="制单人",  Width="80",Sortable=true}},

                       {"branch_name", new DataItem(){Title="仓库",  Width="80"}},
                       {"order_source", new DataItem(){Title="来源",Hidden=true,Width="100",SqlFld="(case when T.order_source='xcx' then '小程序' when T.order_source='cashier' then '收银系统' else '线下' end)"}},
                       //{"bj",new DataItem(){Title="变价",Hidden=true,SqlFld="(case when lower(coalesce(sheet_attribute->>'bj',''))='true' then '是' else '' end)", Width="80" } },
                       //{"forReturn",new DataItem(){Title="退货",Hidden=true,HideOnLoad=true,SqlFld="sheet_attribute->>'forReturn'" } },
                       {"free",new DataItem(){Title="赠品",Hidden=true,HideOnLoad=true, SqlFld="sheet_attribute->>'free'" } },
                       {"j",new DataItem(){Title="借货",Hidden=true,HideOnLoad=true, SqlFld="sheet_attribute->>'j'" } },
                       {"h",new DataItem(){Title="还货",Hidden=true,HideOnLoad=true, SqlFld="sheet_attribute->>'h'" } },
                       {"sheet_no",   new DataItem(){Title="单号",  Linkable=true, Width="200", SqlFld="sheet_no",Sortable=true,
                       JSCellRender=@"function (row, column, value,p4,p5,rowData) { 
                                    let bj=rowData.bj;let forReturn = rowData.forreturn;let free = rowData.free;let j = rowData.j;let h = rowData.h;
                                    let labels="""";                                                                    
                                    let html = `<div style = ""height:100%;display:flex;align-items:center;justify-content:flex-start;"" ><label style=""cursor:pointer;margin-left:4px;color:#49f;margin-right:2px"">${value}</label>${labels}</div>`
                                    return html;
                        }"
                       } },
                       {"sup_name",   new DataItem(){Title="客户",  Width="200",SqlFld="(case WHEN supcust_id=0 then '散客' ELSE sup_name END)",Sortable=true}},
                       {"mobile",   new DataItem(){Title="客户电话",  Width="200",Sortable=true,Hidden=true}},
                       {"supcust_no",   new DataItem(){Title="客户编号",  Width="200",Sortable=true}},
                       {"print_count", new DataItem(){Title="打印次数",  Width="100", Sortable=true,

                         FuncGetSubColumns = (col) =>
                            {
                                var result = new ColumnsResult();
                                var subColumns = new Dictionary<string, DataItem>();
                                var subKeyValuePairs = new Dictionary<string, string> { {"sheet_print_count", "印"}, {"sum_print_count", "汇"}};
                                var flds = "";
                                foreach (var key in subKeyValuePairs.Keys)
                                {
                                    subColumns.Add(key, new DataItem { Width = col.Width,Sortable=true,Title = subKeyValuePairs[key], CellsAlign = "center" });
                                    var fld = key;
                                    if(flds != "") flds += ",";
                                    flds += fld;
                                }
                                result.Columns = subColumns;
                                result.FldsSQL = flds;
                                return Task.FromResult(result);
                         }
                       }},
                       
                       {"senders_id", new DataItem(){Title = "senders_id", Hidden = true,HideOnLoad=true}},
                       {"senders_name", new DataItem(){Title = "送货员", Width = "80"}},
                       //{"getter_name", new DataItem(){Title = "收款人", Width = "80",Hidden=true}},
                       {"happen_time",   new DataItem(){Title="交易时间",   Width="160",Sortable=true}},
                       {"make_time",   new DataItem(){Title="制单时间",   Width="160",Sortable=true} },
                       {"approve_time",   new DataItem(){Title="审核时间",   Width="160",Sortable=true} }, 
                        {"send_time",   new DataItem(){Title="送货时间",Hidden=true,    Width="160"}},
                       {"total_quantity", new DataItem(){Title="总数量", Width="100",CellsAlign="right"}},
                       {"make_brief", new DataItem(){Title="备注", Width="100"}},
                     },

                      QueryFromSQL=@"
from (select sheet_id,sheet_no,borrow_mode,branch_id,company_id,seller_id, getter_id, maker_id, red_flag,order_source, approve_time, review_time, sheet_type, sheet_attribute, senders_id, senders_name, happen_time, make_time, send_time, total_quantity, make_brief, supcust_id
      from borrow_item_main
      where company_id = ~COMPANY_ID ~VAR_SHEET_ATTRIBUTE ~VAR_SHEET_ALLATTRIBUTE
      union
      select sheet_id,sheet_no,'QTY' as borrow_mode,branch_id,company_id,seller_id, getter_id, maker_id, red_flag,order_source, approve_time, review_time, sheet_type, sheet_attribute, senders_id, senders_name, happen_time, make_time, send_time, total_quantity, make_brief, supcust_id
      from sheet_sale_main
      where company_id = ~COMPANY_ID ~VAR_SHEET_ATTRIBUTE ~VAR_SHEET_ALLATTRIBUTE) T

         LEFT JOIN (select supcust_id as sup_id, sup_name, supcust_no, mobile, py_str, other_region, acct_cust_id
                    from info_supcust
                    where company_id = ~COMPANY_ID) s on T.supcust_id = s.sup_id
         LEFT JOIN info_branch b on T.branch_id = b.branch_id and b.company_id = T.company_id

         left join sheet_status_sale ss on T.sheet_id = ss.sheet_id and ss.company_id = T.company_id
         LEFT JOIN (select oper_id,
                           oper_name   as seller_name,
                           depart_id   as department_id_s,
                           depart_path as department_path_s,
                           depart_path as depart_path_s
                    from info_operator
                    where company_id = ~COMPANY_ID) seller on T.seller_id = seller.oper_id
         LEFT JOIN (select oper_id,
                           oper_name   as maker_name,
                           depart_id   as department_id_m,
                           depart_path as department_path_m,
                           depart_path as depart_path_m
                    from info_operator
                    where company_id = ~COMPANY_ID) maker on T.maker_id = maker.oper_id
         LEFT JOIN (select oper_id, oper_name as getter_name from info_operator where company_id = ~COMPANY_ID) getter
                   on T.getter_id = getter.oper_id ~VAR_DETAIL_JOIN
where T.company_id= ~COMPANY_ID ~VAR_SHEET_TYPE  ~VAR_DETAIL_CONDI ~VAR_OTHER_REGION",
                    


QueryOrderSQL=" order by happen_time desc"
                  }
                } 
            }; 
        }
        public static void SetCostInfo(PageQueryModel page)
        {
            var costPriceType = "3";
            var costPriceTypeName = "预设进价";
            if (page.JsonCompanySetting.IsValid())
            {
                dynamic setting = Newtonsoft.Json.JsonConvert.DeserializeObject(page.JsonCompanySetting);
                if (setting != null && setting.costPriceType != null) costPriceType = setting.costPriceType;
            }
            var columns = page.Grids["gridItems"].Columns;

            bool seeProfit = false;
            if (page.JsonOperRights.IsValid())
            {
                bool seeInPrice = false;
                dynamic operRights = Newtonsoft.Json.JsonConvert.DeserializeObject(page.JsonOperRightsOrig);
                if (operRights?.delicacy?.seeInPrice?.value is not null)
                    seeInPrice = ((string)operRights.delicacy.seeInPrice.value).ToLower() == "true";

                seeProfit = ((string)operRights?.delicacy?.seeProfit?.value)?.ToLower() != "false";
                if (!seeInPrice) seeProfit = false;

            }


            if (!seeProfit)
            {
                columns["total_cost_amount"].HideOnLoad = columns["total_cost_amount"].Hidden = true;
                columns["total_profit_amount"].HideOnLoad = columns["total_profit_amount"].Hidden = true;
            }
            if (costPriceType == "4") costPriceTypeName = "最近平均进价";
            else if (costPriceType == "1") costPriceTypeName = "预设成本";
            else if (costPriceType == "2") costPriceTypeName = "加权平均成本";

            page.DataItems["cost_price_type"].Value = costPriceType;
            page.DataItems["cost_price_type"].Label = costPriceTypeName;
        }
        public static void SetCostColumns(PageQueryModel page)
        {
            var totalCostAmount = "sm.cost_amount_buy";//当前进价
            var cost_price_type = page.DataItems["cost_price_type"].Value;
            switch (cost_price_type)
            {
                case "3"://预设进价
                    totalCostAmount = "sm.cost_amount_buy";
                    break;
                case "2"://加权价
                    totalCostAmount = "sm.cost_amount_avg";
                    break;
                case "1"://预设成本
                    totalCostAmount = "sm.cost_amount_prop";
                    break;
                case "4"://最近平均进价
                    totalCostAmount = "sm.cost_amount_recent";
                    break;
            }

            var columns = page.Grids.GetValueOrDefault("gridItems").Columns;
            if (columns.ContainsKey("total_cost_amount"))
            {
                columns["total_cost_amount"].SqlFld = $@"{totalCostAmount}";
            }
            if (columns.ContainsKey("total_profit_amount"))
            {
                columns["total_profit_amount"].SqlFld = $@"round((total_amount-disc_amount)::numeric-{totalCostAmount},2)";
            }

        }
        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            //SetCostColumns(this);
            string item_id = DataItems["item_id"].Value;
            string remark = DataItems["remark"].Value;
            string item_brand = DataItems["brand_id"].Value;
            string item_class = DataItems["item_class"].Value;
            string startDay = DataItems["startDay"].Value;
            string endDay = DataItems["endDay"].Value;
            string sheet_type = DataItems["sheet_type"].Value;
            string queryTimeAccord = DataItems["queryTimeAccord"].Value;
            string other_region = DataItems["other_region"].Value;
            SQLVariables["START_TIME"] = "";
            SQLVariables["END_TIME"] = "";
            SQLVariables["SHEET_TYPE"] = "";
            SQLVariables["SHEET_ATTRIBUTE"] = "";
            SQLVariables["SHEET_ALLATTRIBUTE"] = "";
            SQLVariables["OTHER_REGION"] = "";

            SQLVariables["START_TIME"] = startDay;
            SQLVariables["START_TIME"] = endDay;

            if (!sheet_type.IsInvalid())
            {
                //if (sheet_type == "JH") SQLVariables["SHEET_TYPE"] = $"and T.sheet_type in('JH', 'X') and sheet_attribute ->> 'j' = 'true'";
                //if (sheet_type == "HH") SQLVariables["SHEET_TYPE"] = $"and T.sheet_type in('HH', 'X') and sheet_attribute ->> 'h' = 'true'";

                if(sheet_type == "JH")
                {
                    SQLVariables["SHEET_TYPE"] = $"and T.sheet_type in('JH', 'X')";
                    SQLVariables["SHEET_ATTRIBUTE"] = $"and sheet_attribute ->> 'j' = 'true'";
                }
                if(sheet_type == "HH")
                {
                    SQLVariables["SHEET_TYPE"] = $"and T.sheet_type in('HH', 'X')";
                    SQLVariables["SHEET_ATTRIBUTE"] = $"and sheet_attribute ->> 'h' = 'true'";
                }
            } else
            {
                SQLVariables["SHEET_ALLATTRIBUTE"] = $"and (sheet_attribute ->> 'j' = 'true' or sheet_attribute ->> 'h' = 'true')";
            }
            if (queryTimeAccord == "byCheckedTime")
            {


            }
            if(other_region.IsValid())
            {
                SQLVariables["OTHER_REGION"] = $"and position('/{other_region}/' in other_region) >0";
            }
            else if (queryTimeAccord == "byApproveTime")
            {
                SQLVariables["SALESHEET_TIME_CONDI"] = "";
                SQLVariables["CHECKED_TIME_CONDI"] = "";
                SQLVariables["CHECKED_OUTER_TIME_CONDI"] = "";


                DateTime tmStart = Convert.ToDateTime(startDay);
                tmStart = tmStart.AddMonths(-3);
                startDay = CPubVars.GetDateText(tmStart);
            }
            else if (queryTimeAccord == "byMakeTime")
            {
                SQLVariables["SALESHEET_TIME_CONDI"] = "";
                SQLVariables["CHECKED_TIME_CONDI"] = "";
                SQLVariables["CHECKED_OUTER_TIME_CONDI"] = "";


                DateTime tmStart = Convert.ToDateTime(startDay);
                tmStart = tmStart.AddMonths(-3);
                startDay = CPubVars.GetDateText(tmStart);
            }
            else 
            {
                SQLVariables["CHECKED_TIME_CONDI"] = "";
                SQLVariables["SALESHEET_TIME_CONDI"] = "";
                SQLVariables["CHECKED_OUTER_TIME_CONDI"] = "";

            }
            this.SQLVariables["DETAIL_JOIN"] = "";
            this.SQLVariables["DETAIL_CONDI"] = "";
            if (item_id.IsValid() || remark.IsValid()|| item_brand.IsValid()|| item_class.IsValid())
            {
                string condi = "";
				if (item_id.IsValid())
				{
                    condi += $" and bsd.item_id in ({item_id})";
				}
                if (remark.IsValid())
                {
                    condi += $" and bsd.remark like '%{remark}%'";
                }
                if (item_brand.IsValid())
                {
                    condi += $" and bsd.item_brand in ({item_brand})";
                }
                if (item_class.IsValid())
                {
                    condi += $" and bsd.other_class like '%{item_class}%'";
                }
                this.SQLVariables["DETAIL_CONDI"] = condi;
                this.SQLVariables["DETAIL_JOIN"] = @$"
left join 
(
   select distinct sheet_id as detail_sheet_id, remark, b.* 
   from borrow_item_detail d
   left join (select item_brand,item_id,other_class from info_item_prop where company_id={company_id})b on b.item_id=d.item_id
   where company_id={company_id} and happen_time between '{startDay}' and '{endDay}'
) bsd on T.sheet_id=bsd.detail_sheet_id";

            } 
            

        }
     
        public async Task OnGet()
        {  
            await InitGet(cmd);
            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }
        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            //SetCostInfo(this);
            string requestFlag = CPubVars.RequestV(Request, "queryTimeAccord") ;
            if (requestFlag=="")
            {
                JObject jsonOptions = JsonConvert.DeserializeObject<JObject>(JsonOptionsRemembered);
                JObject pageRemembered = (JObject)jsonOptions["page_SaleSheetView"];
                bool hasByHappenTimeOld = false;
                bool hasQueryTimeAccord = false;
                if (pageRemembered != null)
                {
                    foreach (var item in pageRemembered)
                    {
                        if (item.Key == "byHappenTime") hasByHappenTimeOld = true;
                        if (item.Key == "queryTimeAccord") hasQueryTimeAccord = true;
                    }
                }
                if (hasByHappenTimeOld && !hasQueryTimeAccord)
                {
                    string value = (string)pageRemembered["byHappenTime"]["value"];
                    if (value == "true")
                    {
                        DataItems["queryTimeAccord"].Value = "byHappenTime";
                        DataItems["queryTimeAccord"].Label = "交易时间";
                    }
                    else
                    {
                        DataItems["queryTimeAccord"].Value = "byApproveTime";
                        DataItems["queryTimeAccord"].Label = "审核时间";
                    }
                }

            }
            else 
            {
                if (requestFlag == "byHappenTime")
                {
                    DataItems["queryTimeAccord"].Value = "byHappenTime";
                    DataItems["queryTimeAccord"].Label = "交易时间";
                }
                else if (requestFlag == "byMakeTime")
                {
                    DataItems["queryTimeAccord"].Value = "byMakeTime";
                    DataItems["queryTimeAccord"].Label = "制单时间";
                }
                else if (requestFlag == "byCheckedTime")
                {
                    DataItems["queryTimeAccord"].Value = "byCheckedTime";
                    DataItems["queryTimeAccord"].Label = "交账时间";
                }

              }



        }
    }

    [Route("api/[controller]/[action]")]
    public class BorrowItemSheetViewController : BaseController
    {
        public BorrowItemSheetViewController(CMySbCommand cmd)
        {
            Database = "";
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<JsonResult> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            BorrowItemSheetViewModel model = new BorrowItemSheetViewModel(cmd);
            //string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return await PageBaseModel.GetDataItemOptionsJson(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);

            //return data;
        }

        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            
            BorrowItemSheetViewModel model = new BorrowItemSheetViewModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }
        public static string GetUnitQtyFromSmallUnitQty(SheetRowMM row, float s_quantity, out float b_qty, out float m_qty, out float s_qty)
        {
            b_qty = 0f; m_qty = 0f; s_qty = 0f;

            float leftQty = s_quantity;
            string unitsQty = "";
            float absLeftQty = Math.Abs(leftQty);
            int flag = leftQty < 0 ? -1 : 1;
            if (row.b_unit_factor.IsValid())
            {
                b_qty = (int)(absLeftQty / Convert.ToSingle(row.b_unit_factor));
                absLeftQty = absLeftQty % Convert.ToSingle(row.b_unit_factor);
                if (b_qty < 0.001f) b_qty = 0;

                if (b_qty > 0)
                {
                    b_qty *= flag;
                    unitsQty += b_qty.ToString() + row.b_unit_no;
                }

            }

            if (row.m_unit_factor.IsValid())
            {
                m_qty = (int)(absLeftQty / Convert.ToSingle(row.m_unit_factor));
                absLeftQty = absLeftQty % Convert.ToSingle(row.m_unit_factor);
                if (m_qty < 0.001f) m_qty = 0;

                if (m_qty > 0)
                {
                    m_qty *= flag;
                    unitsQty += m_qty.ToString() + row.m_unit_no;
                }

            }

            s_qty = absLeftQty;
            if (s_qty < 0.001f) s_qty = 0;

            if (s_qty > 0)
            {
                s_qty *= flag; unitsQty += s_qty.ToString() + row.s_unit_no;
            }


            // row.quantity_unit_conv = unitsQty;
            return unitsQty;
        }

        [HttpGet]
        public async Task<JsonResult> GetMultiSheetsToPrint(string operKey, string sheetIDs, bool bPrintSum, bool bPrintReturnSum, bool bPrintEach,bool bPrintSheetsMainInfo, bool smallUnitBarcode, string clientVersion, string sortColumn, string sortDirection)
        {
            SheetBorrowItem.GetSheetsUsage usage = new SheetBorrowItem.GetSheetsUsage();
            usage.ForPrint = true;
            usage.GetEachSheet = bPrintEach;
            usage.GetSumSheet = bPrintSum;
            usage.GetReturnSumSheet = bPrintReturnSum;
            usage.GetSheetsMainInfo = bPrintSheetsMainInfo;

            SheetBorrowItem.GetSheetsResult  res = await SheetBorrowItem.GetItemSheets<SheetBorrowItem, SheetRowBorrowItem>(cmd, operKey, sheetIDs, usage, smallUnitBarcode, clientVersion, sortColumn, sortDirection);
            return new JsonResult(res);
            //return new JsonResult(new {result=res.result,msg=res.msg,printSheets=res.sheetGroup,sheetGroup=res.sheetGroup});
            // return await GetMultiSheetsToPrint_Inner<SheetSale,SheetRowSale>(cmd,operKey, sheetIDs, bPrintSum, bPrintEach, smallUnitBarcode, clientVersion, sortColumn, sortDirection);
        }
        [HttpGet]
        public async Task<JsonResult> BatchSetSheetsMakeBrief(string operKey, string sheetIDs, string makeBrief) 
        {
            string result = "";
            string msg = "";
            string sql = "";
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            if (!string.IsNullOrWhiteSpace(sheetIDs) && !string.IsNullOrWhiteSpace(makeBrief))
            {
                try
                {
                    sql = $@"update sheet_sale_main set make_brief = coalesce(make_brief,'') || '/' || '{makeBrief}' where company_id={companyID} and sheet_id in ({sheetIDs}) ";
                    cmd.CommandText = sql;
                    await cmd.ExecuteNonQueryAsync();
                    result = "OK";
                }
                catch (Exception e)
                {
                    result = "fail";
                    msg = "操作失败";
                    NLogger.Error(e.ToString()+"updateSql:"+ sql);
                }
            }
            else {
                result = "fail";
                msg = "操作失败";
            }
         
            return new JsonResult(new { result, msg});
        }
        [HttpGet]
        public async Task<JsonResult> GetSheetsToApprove(string operKey, string sheetIDs )
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);

            string msg = "";
            string[] sheetID = sheetIDs.Split(',');

            foreach (var ID in sheetID)
            {
                SheetBorrowItem sheet = new SheetBorrowItem(LOAD_PURPOSE.SHOW);
                await sheet.Load(cmd, companyID, ID);
                sheet.OperID = operID;
                sheet.OperKey = operKey;
                string aa = await sheet.SaveAndApprove(cmd);
                if (aa!="")
                {
                    msg += sheet.sheet_no + aa;
                }
            }

            if (msg != "")
            {
                return Json(new { result = $"{msg}" });
            }
            else
            {
                return Json(new { result = "OK" });
            }
        }



        [HttpGet]
        public async Task<JsonResult> GetSheetsToRed(string operKey, string sheetIDs)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);

            string msg = "";
            string[] sheetID = sheetIDs.Split(',');

            foreach (var ID in sheetID)
            {
                SheetBorrowItem sheet = new SheetBorrowItem(LOAD_PURPOSE.SHOW);
                await sheet.Load(cmd, companyID, ID);
                sheet.OperID = operID;
                sheet.OperKey = operKey;
                string aa = await sheet.Red(cmd, companyID, ID, operID, "");
                if (aa != "")
                {
                    msg += sheet.sheet_no + aa;
                }
            }

            if (msg != "")
            {
                return Json(new { result = $"{msg}" });
            }
            else
            {
                return Json(new { result = "OK" });
            }
        }




        [HttpPost]
        public async Task<JsonResult> SetSenderForSheets([FromBody] dynamic data)
        { 
            string operKey = data.operKey;
            string senders_id = data.senders_id;
            string senders_name = data.senders_name;
            string sheetIDs = data.sheetIDs;
            string msg = ""; 
            Security.GetInfoFromOperKey(operKey, out string companyID,out string operID);

            if (string.IsNullOrEmpty(sheetIDs)) msg = "请选择至少一张单据\r\n";
            if (string.IsNullOrEmpty(senders_id)) msg += "请选择送货员\r\n";
            if (msg != "")
                return new JsonResult(new { result = "Error", msg });
            cmd.CommandText = $"select oper_name from info_operator where company_id={companyID} and oper_id={operID}";
            object ov = await cmd.ExecuteScalarAsync();
            string operName = "";
            if(ov!=null && ov != DBNull.Value)
            {
                operName = ov.ToString();
            }

            string operInfo = CPubVars.GetDateText(DateTime.Now)+" "+ operName;
            string sql = $"update sheet_sale_main set senders_id='{senders_id}',senders_name='{senders_name}',approve_brief=COALESCE(approve_brief)||'{operInfo}将送货员'||senders_name||'调整为'||'{senders_name}' where company_id={companyID} and sheet_id in ({sheetIDs});";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync(); 
            return new JsonResult(new { result = "OK", msg }); 

        }
      

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            BorrowItemSheetViewModel model = new BorrowItemSheetViewModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }
      
    }
}
