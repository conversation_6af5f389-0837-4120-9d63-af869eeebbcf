﻿using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Net.Http;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using ArtisanManage.YingJiangCommon.Dao.CheckAccountDao;
using ArtisanManage.YingJiangCommon.Model;
using HuaWeiObsController;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace ArtisanManage.YingJiangCommon.Services
{
    public static class CheckAccountServices
    {

        public static async Task<dynamic> QueryCheckAccountBaseInfoService(dynamic paramModel, CMySbCommand cmd)
        {
            // 获取支付方式
            // 获取员工列表
            // 获取部门列表
            // 获取哪种交账方式
            string operKey = paramModel.OperKey;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            paramModel.companyID = companyID;
            return await CheckAccountDao.QueryCheckAccountBaseInfoDao(paramModel, cmd);
        }
        public static async Task<dynamic> QueryCompanySettingService(dynamic paramModel, CMySbCommand cmd)
        {
            string operKey = paramModel.OperKey;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            paramModel.companyID = companyID;
            return await CheckAccountDao.QueryCompanySettingDao(paramModel, cmd);
        }
        // 记忆新版交账单的列宽
        public static async Task<dynamic> MemoryColumnWidthService(dynamic paramModel, CMySbCommand cmd)
        {
            string operKey = paramModel.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            paramModel.companyID = companyID;
            paramModel.operId = operID;
            return await CheckAccountDao.MemoryColumnWidthDao(paramModel, cmd);
        }
        public static async Task<dynamic> QyeryTableColumnWidthService(string operKey, CMySbCommand cmd)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            return await CheckAccountDao.QyeryTableColumnWidthDao(companyID, operID, cmd);
        }
        public static async Task<dynamic> QueryCheckAccountPayWayInfoService(dynamic paramModel, CMySbCommand cmd)
        {
            string operKey = paramModel.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            paramModel.companyID = companyID;
            return await CheckAccountDao.QueryCheckAccountPayWayInfoDao(paramModel, cmd);
        }

        public static async Task<SummaryCommonListModel> QueryCheckAccountSummaryService(CheckAccountSummaryParamsModel paramModel, CMySbCommand cmd)
        {
            Security.GetInfoFromOperKey(paramModel.OperKey, out string companyID);
            paramModel.CompanyID = companyID;
            SummaryListModel daoResult = await CheckAccountDao.QueryCheckAccountSummaryDao(paramModel, cmd);
            SummaryCommonListModel result = new SummaryCommonListModel();
            result.SummaryCommonList = new List<TableRowModel>();
            result.PaidInPayWayInfo = new List<PayWayInfoModel>();
            result.OtherPayWayInfo = new List<PayWayInfoModel>();
            dynamic tempCheck = ToolClassificationSheetToUnCheckOrCheckedOrRedCheck(daoResult);
            List<TableRowModel> tempUnCheckSheetList = tempCheck.tempUnCheckSheetList;
            List<TableRowModel> tempCheckedSheetList = tempCheck.tempCheckedSheetList;
            List<TableRowModel> tempRedCheckSheetList = tempCheck.tempRedCheckSheetList;
            ToolSummarySheet(tempUnCheckSheetList, result.SummaryCommonList, "all", result);
            foreach (TableRowModel sheet in tempUnCheckSheetList)
            {
                ToolTableTitle(sheet, result);
            }
            foreach (TableRowModel sheet in tempCheckedSheetList)
            {
                ToolTableTitle(sheet, result);
            }
            foreach (TableRowModel sheet in tempRedCheckSheetList)
            {
                ToolTableTitle(sheet, result);
            }
            
            ToolAddSummarySheetForExpand(tempCheckedSheetList, result.SummaryCommonList, "all", "checked");
            ToolAddSummarySheetForExpand(tempRedCheckSheetList, result.SummaryCommonList, "all", "redCheck");
            
            return result;
        }
        public static async Task<SummaryCommonListModel> QueryCheckAccountSummaryBySellerGroupDayService(CheckAccountSummaryParamsModel paramModel, CMySbCommand cmd)
        {
            Security.GetInfoFromOperKey(paramModel.OperKey, out string companyID);
            paramModel.CompanyID = companyID;
            SummaryListModel daoResult = await CheckAccountDao.QueryCheckAccountSummaryBySellerGroupDayDao(paramModel, cmd);
            SummaryCommonListModel result = new SummaryCommonListModel();
            result.SummaryCommonList = new List<TableRowModel>();
            result.PaidInPayWayInfo = new List<PayWayInfoModel>();
            result.OtherPayWayInfo = new List<PayWayInfoModel>();
            dynamic tempCheck = ToolClassificationSheetToUnCheckOrCheckedOrRedCheck(daoResult);
            List<TableRowModel> tempUnCheckSheetList = tempCheck.tempUnCheckSheetList;
            List<TableRowModel> tempCheckedSheetList = tempCheck.tempCheckedSheetList;
            List<TableRowModel> tempRedCheckSheetList = tempCheck.tempRedCheckSheetList;
            ToolSummarySheet(tempUnCheckSheetList, result.SummaryCommonList, "day", result);
            ToolAddSummarySheetForExpand(tempCheckedSheetList, result.SummaryCommonList, "day" ,"checked");
            ToolAddSummarySheetForExpand(tempRedCheckSheetList, result.SummaryCommonList, "day", "redCheck");
            // 上述add控制不同类型
            result.SummaryCommonList = result.SummaryCommonList.OrderByDescending(item => DateTime.Parse(item.happen_time)).ToList();
            return result;
        }
        public static async Task<DetailDaySheetListModel> QueryCheckAccountSummaryDayDetailService(CheckAccountSummaryParamsModel paramModel, CMySbCommand cmd)
        {
            Security.GetInfoFromOperKey(paramModel.OperKey, out string companyID);
            paramModel.CompanyID = companyID;
            SummaryListModel daoResult = await CheckAccountDao.QueryCheckAccountSummaryBySellerGroupDayDao(paramModel, cmd);
            DetailDaySheetListModel result = new DetailDaySheetListModel();
            result.UnCheckSheetList = new List<TableRowModel>();
            result.CheckSheetList = new List<TableRowModel>();
            result.RedCheckSheetList = new List<TableRowModel>();
            dynamic tempCheck = ToolClassificationSheetToUnCheckOrCheckedOrRedCheck(daoResult);
            List<TableRowModel> tempUnCheckSheetList = tempCheck.tempUnCheckSheetList;
            List<TableRowModel> tempCheckedSheetList = tempCheck.tempCheckedSheetList;
            List<TableRowModel> tempRedCheckSheetList = tempCheck.tempRedCheckSheetList;
            ToolSummarySheet(tempUnCheckSheetList, result.UnCheckSheetList, "unchecksheet", null);
            ToolSummarySheet(tempCheckedSheetList, result.CheckSheetList, "checksheet", null);
            ToolSummarySheet(tempRedCheckSheetList, result.RedCheckSheetList, "redCheckSheet", null);
            result.UnCheckSheetList = result.UnCheckSheetList.OrderByDescending(item => DateTime.Parse(item.happen_time)).ToList();
            result.CheckSheetList = result.CheckSheetList.OrderByDescending(item => DateTime.Parse(item.happen_time)).ToList();
            result.RedCheckSheetList = result.RedCheckSheetList.OrderByDescending(item => DateTime.Parse(item.happen_time)).ToList();
            return result;
        }
        /// <summary>
        /// 查询已交帐/红冲交账情况
        /// </summary>
        /// <param name="paramModel"></param>
        /// <param name="cmd"></param>
        /// <returns></returns>
        public static async Task<List<TableRowModel>> QueryCheckedSheetListDetailService(CheckedSheetListParamsModel paramModel, CMySbCommand cmd)
        {
            Security.GetInfoFromOperKey(paramModel.OperKey, out string companyID);
            paramModel.CompanyID = companyID;
            List<SheetCheckSheetsDetail> checkSheetList = await CheckAccountDao.QueryCheckedSheetListDetailSheetIdListDao(paramModel, cmd);
            string saleSheetsInfo = string.Join(",", checkSheetList.Where(checkSheet => "X".Equals(checkSheet.business_sheet_type) || "T".Equals(checkSheet.business_sheet_type)).Select(checkSheet => checkSheet.business_sheet_id));
            string preGetSheetsInfo = string.Join(",", checkSheetList.Where(checkSheet => "YS".Equals(checkSheet.business_sheet_type) || "DH".Equals(checkSheet.business_sheet_type)).Select(checkSheet => checkSheet.business_sheet_id));
            string prePaySheetsInfo = string.Join(",", checkSheetList.Where(checkSheet => "YF".Equals(checkSheet.business_sheet_type)).Select(checkSheet => checkSheet.business_sheet_id));
            string getArrearsSheetsInfo = string.Join(",", checkSheetList.Where(checkSheet => "SK".Equals(checkSheet.business_sheet_type) || "FK".Equals(checkSheet.business_sheet_type)).Select(checkSheet => checkSheet.business_sheet_id));
            string feeOutSheetsInfo = string.Join(",", checkSheetList.Where(checkSheet => "ZC".Equals(checkSheet.business_sheet_type)).Select(checkSheet => checkSheet.business_sheet_id));
            string incomeSheetsInfo = string.Join(",", checkSheetList.Where(checkSheet => "SR".Equals(checkSheet.business_sheet_type)).Select(checkSheet => checkSheet.business_sheet_id));
            SheetCheckSheetsDetailGroupId sheetsDetailGroupId = new SheetCheckSheetsDetailGroupId(saleSheetsInfo,preGetSheetsInfo,prePaySheetsInfo,getArrearsSheetsInfo,feeOutSheetsInfo,incomeSheetsInfo);
            SummaryListModel daoResult = await CheckAccountDao.QueryCheckedSheetListDetailDao(paramModel, cmd, sheetsDetailGroupId);
            List<TableRowModel> summarySaleSheetsInfo = daoResult.summarySaleSheetsInfo;
            List<TableRowModel> summaryPreGetSheetsInfo = daoResult.summaryPreGetSheetsInfo;
            List<TableRowModel> summaryPrePaySheetsInfo = daoResult.summaryPrePaySheetsInfo;
            List<TableRowModel> summaryGetArrearsSheetsInfo = daoResult.summaryGetArrearsSheetsInfo;
            List<TableRowModel> summaryFeeOutSheetsInfo = daoResult.summaryFeeOutSheetsInfo;
            List<TableRowModel> summaryIncomeSheetsInfo = daoResult.summaryIncomeSheetsInfo;
            List<TableRowModel> result = new List<TableRowModel>();
            ToolSummarySheet(summarySaleSheetsInfo, result, "checkedOrRedCheckSheetList", null);
            ToolSummarySheet(summaryPreGetSheetsInfo, result, "checkedOrRedCheckSheetList", null);
            ToolSummarySheet(summaryPrePaySheetsInfo, result, "checkedOrRedCheckSheetList", null);
            ToolSummarySheet(summaryGetArrearsSheetsInfo, result, "checkedOrRedCheckSheetList", null);
            ToolSummarySheet(summaryFeeOutSheetsInfo, result, "checkedOrRedCheckSheetList", null);
            ToolSummarySheet(summaryIncomeSheetsInfo, result, "checkedOrRedCheckSheetList", null);
            return result;
        }
        /// <summary>
        /// 根据单据id获取具体信息
        /// </summary>
        /// <param name="paramModel"></param>
        /// <param name="cmd"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static async Task<dynamic> QuerySheetListDetailToCheckInfoService(QuerySheetDetailListToCheckParamsModel paramModel, CMySbCommand cmd)
        {
            Security.GetInfoFromOperKey(paramModel.OperKey, out string companyID);
            paramModel.CompanyID = companyID;
            // 获取交账 汇总
            dynamic daoResult = await CheckAccountDao.QuerySheetDetailListToCheckDao(paramModel, cmd);
            dynamic ItemClassInfo = daoResult.ItemClassInfo;
            List<SheetSale> saleSheetList  = daoResult.saleSheetList;
            SummaryListModel summaryResult = daoResult.result;
            dynamic tempCheck = ToolClassificationSheetToUnCheckOrCheckedOrRedCheck(summaryResult);
            List<TableRowModel> tableResult = new List<TableRowModel>();
            List<TableRowModel> tempUnCheckSheetList = tempCheck.tempUnCheckSheetList;
            ToolSummarySheet(tempUnCheckSheetList, tableResult, "checkSheetToAccount", null);
            // List<TableRowModel> summarySaleSheetsInfo = summaryResult.summarySaleSheetsInfo;
            // List<TableRowModel> summaryPreGetSheetsInfo = summaryResult.summaryPreGetSheetsInfo;
            // List<TableRowModel> summaryPrePaySheetsInfo = summaryResult.summaryPrePaySheetsInfo;
            // List<TableRowModel> summaryGetArrearsSheetsInfo = summaryResult.summaryGetArrearsSheetsInfo;
            // List<TableRowModel> summaryFeeOutSheetsInfo = summaryResult.summaryFeeOutSheetsInfo;
            // List<TableRowModel> summaryIncomeSheetsInfo = summaryResult.summaryIncomeSheetsInfo;
            // List<TableRowModel> tableResult = new List<TableRowModel>();
            // ToolSummarySheet(summarySaleSheetsInfo, tableResult, "checkSheetToAccount", null);
            // ToolSummarySheet(summaryPreGetSheetsInfo, tableResult, "checkSheetToAccount", null);
            // ToolSummarySheet(summaryPrePaySheetsInfo, tableResult, "checkSheetToAccount", null);
            // ToolSummarySheet(summaryGetArrearsSheetsInfo, tableResult, "checkSheetToAccount", null);
            // ToolSummarySheet(summaryFeeOutSheetsInfo, tableResult, "checkSheetToAccount", null);
            // ToolSummarySheet(summaryIncomeSheetsInfo, tableResult, "checkSheetToAccount", null);

            return new
            {
                tableResult,
                saleSheetList,
                ItemClassInfo
            };
        }
        /// <summary>
        /// 提交附件
        /// </summary>
        /// <param name="parameter"></param>
        /// <param name="_httpClientFactory"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static async Task<string> UploadCredentialService(dynamic parameter,IHttpClientFactory _httpClientFactory)
        {
            string operKey = parameter.operKey;
            string imgBase64 = parameter.imgBase64;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operId);
            DateTime dt = DateTime.Now;
            string mm = dt.ToString("yyyyMM");
            string ss = dt.ToString("yyyyMMddHHmmss");
            using (var image = HuaWeiObs.Base64ToImage(imgBase64))
            {
                string path = @$"checkAccount/{mm}/{companyID}_{operId}_{ss}.jpg";
                string err = await HuaWeiObs.Save(_httpClientFactory, image.Image, path);
                if (err != "")
                {
                    throw new Exception(err);
                }
                return path;
            }
        }
        
        /// <summary>
        /// 提交交账单
        /// </summary>
        /// <param name="parameter"></param>
        /// <param name="cmd"></param>
        /// <returns></returns>
        public static async Task<dynamic> SubmitCheckAccountService(SheetCheckSheetsMainDbModel parameter, CMySbCommand cmd)
        {
            string operKey = parameter.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operId);
            string now = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            string real_seller_id = parameter.getter_id;
            parameter.company_id = companyID;
            if (string.IsNullOrEmpty(parameter.happen_time))
            {
                parameter.happen_time = now;
            }

            if (string.IsNullOrEmpty(parameter.maker_id))
            {
                parameter.maker_id = operId;
            }

            if (string.IsNullOrEmpty(parameter.make_time))
            {
                parameter.make_time = now;
            }
            if (string.IsNullOrEmpty(parameter.approve_id))
            {
                parameter.approve_id = operId;
            }
            if (string.IsNullOrEmpty(parameter.approve_time))
            {
                parameter.approve_time = now;
            }
            List<SheetToCheckSheet>? SheetToCheckSheet = parameter.SheetToCheckSheet;
            foreach (SheetToCheckSheet sheetToCheckSheet in SheetToCheckSheet)
            {
                sheetToCheckSheet.company_id = companyID;
            }
            string redisKey = GetRedisLockKey(SheetToCheckSheet);
            if (redisKey != "")
            {
                string redisValue = await RedisHelper.GetSetAsync(redisKey, "1");
                Console.WriteLine(redisValue);
                await RedisHelper.ExpireAsync(redisKey, 30);
                if (redisValue == "1")
                {
                    throw new Exception("请勿重复提交,稍后再试");
                }
            }
            string msg = await CheckAccountDao.QueryCheckRedSheetAndCheckSheetExistDao(companyID,real_seller_id, SheetToCheckSheet, cmd);
            if (!string.IsNullOrEmpty(msg))
			{
                throw new Exception(msg);
            }
            dynamic result =  await CheckAccountDao.SubmitCheckAccountDao(parameter, cmd);
            
            string sheetId = result.sheet_id;
            string makerId = parameter.maker_id;
            string makerName = "";
            string approveId = parameter.approve_id;
            string approveName = "";
            dynamic queryOperInfo = result.queryOperInfo;
            foreach (dynamic operInfo in queryOperInfo)
            {
                string oper_id = operInfo.oper_id;
                string oper_name = operInfo.oper_name;
                if (makerId.Equals(oper_id) && string.IsNullOrEmpty(makerName))
                {
                    makerName = oper_name;
                }
                else if(approveId.Equals(oper_id) && string.IsNullOrEmpty(approveName))
                {
                    approveName = oper_name;
                }
            }
            // await RedisHelper.DelAsync(redisKey);
            return new
            {
                sheet_id = sheetId, 
                happen_time = parameter.happen_time,
                maker_id = makerId,
                maker_name = makerName,
                make_time = parameter.make_time,
                approve_id = approveId,
                approve_name = approveName,
                approve_time = parameter.approve_time,
            };
        }
        
        public static string GetRedisLockKey(dynamic sheetRows)
        {
            string redisKey = "checkAccount";
            foreach (dynamic row in sheetRows)
            {
                redisKey += $"{row.business_sheet_type}{row.business_sheet_id}";
            }
            return redisKey;
        }
        

        public static async Task<dynamic> RedCheckAccountService(dynamic parameter, CMySbCommand cmd)
        {
            string operKey = parameter.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            string now = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            parameter.companyID = companyID;
            parameter.reder_id = operID;
            parameter.red_time = now;
            return await CheckAccountDao.RedCheckAccountDao(parameter,cmd);
        }

        public static async Task<dynamic> QueryCheckAccountHistoryListService(dynamic parameter, CMySbCommand cmd)
        {
            string operKey = parameter.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            parameter.companyID = companyID;
            return await CheckAccountDao.QueryCheckAccountHistoryListDao(parameter, cmd);
        }

        public static async Task<dynamic> LoadHistoryCheckAccountSheetService(dynamic parameter, CMySbCommand cmd)
        {
            string operKey = parameter.operKey;
            string sheet_id = parameter.sheet_id;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            parameter.companyID = companyID;
            dynamic daoResult =  await CheckAccountDao.LoadHistoryCheckAccountSheetDao(parameter,cmd);
            SheetCheckSheetsMainDbModel result = new SheetCheckSheetsMainDbModel();
            result.company_id = daoResult.company_id;
            result.sheet_id = daoResult.sheet_id;
            result.getter_id = daoResult.getter_id;
            result.getter_name = daoResult.getter_name;
            result.happen_time = daoResult.happen_time;
            result.maker_id = daoResult.maker_id;
            result.maker_name = daoResult.maker_name;
            result.make_time = daoResult.make_time;
            result.approve_id = daoResult.approve_id;
            result.approve_time = daoResult.approve_time;
            result.approve_name = daoResult.approve_name;
            result.make_brief = daoResult.make_brief;
            if (!string.IsNullOrEmpty(daoResult.red_flag))
            {
                result.red_flag = Int32.Parse(daoResult.red_flag);
            }
            result.reder_name = daoResult.reder_name;
            result.start_time = daoResult.start_time;
            result.end_time = daoResult.end_time;
            if (!string.IsNullOrEmpty(daoResult.check_account_payway1_id))
            {
                result.check_account_payway1_id = Int32.Parse(daoResult.check_account_payway1_id);
                result.check_account_payway1_amount = Decimal.Parse(daoResult.check_account_payway1_amount);
            }
            if (!string.IsNullOrEmpty(daoResult.check_account_payway2_id))
            {
                result.check_account_payway2_id = Int32.Parse(daoResult.check_account_payway2_id);
                result.check_account_payway2_amount = Decimal.Parse(daoResult.check_account_payway2_amount);
            }
            if (!string.IsNullOrEmpty(daoResult.reder_id))
            {
                result.reder_id = Int32.Parse(daoResult.reder_id);
            }
            result.red_time = daoResult.red_time;
            result.credential = daoResult.credential;
            result.SheetToCheckSheet = new List<SheetToCheckSheet>();
            dynamic businessSheetList = daoResult.business_sheet_list;
            businessSheetList = JsonConvert.DeserializeObject(businessSheetList);
            foreach (var businessSheetItem in businessSheetList)
            {
                businessSheetItem.company_id = companyID;
                result.SheetToCheckSheet.Add(JsonConvert.DeserializeObject<SheetToCheckSheet>(JsonConvert.SerializeObject(businessSheetItem)));
            }
            
            // 获取交账单据详情
            QuerySheetDetailListToCheckParamsModel detailParams = new QuerySheetDetailListToCheckParamsModel();
            detailParams.OperKey = operKey;
            detailParams.CompanyID = companyID;
            List<string> saleSheetIds = new List<string>();
            List<string> preGetSheetIds = new List<string>();
            List<string> getArrearsSheetIds = new List<string>();
            List<string> prePaySheetIds = new List<string>();
            List<string> feeOutSheetIds = new List<string>();
            List<string> incomeSheetIds = new List<string>();
            foreach (SheetToCheckSheet sheet in result.SheetToCheckSheet)
            {
                switch (sheet.business_sheet_type)
                {
                    case "X":
                    case "T":
                        saleSheetIds.Add(sheet.business_sheet_id);
                        break;
                    case "YS":
                    case "DH":
                        preGetSheetIds.Add(sheet.business_sheet_id);
                        break;
                    case "SK":
                    case "FK":
                        getArrearsSheetIds.Add(sheet.business_sheet_id);
                        break;
                    case "YF":
                        prePaySheetIds.Add(sheet.business_sheet_id);
                        break; 
                    case "ZC":
                        feeOutSheetIds.Add(sheet.business_sheet_id);
                        break; 
                    case "SR":
                        incomeSheetIds.Add(sheet.business_sheet_id);
                        break; 
                }
            }
            detailParams.SaleSheetIds = string.Join(",", saleSheetIds);
            detailParams.PreGetSheetIds = string.Join(",", preGetSheetIds);
            detailParams.GetArrearsSheetIds = string.Join(",", getArrearsSheetIds);
            detailParams.PrePaySheetIds = string.Join(",", prePaySheetIds);
            detailParams.FeeOutSheetIds = string.Join(",", feeOutSheetIds);
            detailParams.IncomeSheetIds = string.Join(",", incomeSheetIds);
            
            dynamic daoDetailResult = await CheckAccountDao.QuerySheetDetailListToCheckDao(detailParams, cmd);
            dynamic ItemClassInfo = daoDetailResult.ItemClassInfo;
            List<SheetSale> saleSheetList  = daoDetailResult.saleSheetList;
            SummaryListModel summaryResult = daoDetailResult.result;

            if (summaryResult.summarySaleSheetsInfo != null)
            {
                summaryResult.summarySaleSheetsInfo = summaryResult.summarySaleSheetsInfo.Where(sheet => sheet.check_account_sheet_id.Equals(sheet_id)).ToList();
            }
            if (summaryResult.summaryPreGetSheetsInfo != null)
            {
                summaryResult.summaryPreGetSheetsInfo = summaryResult.summaryPreGetSheetsInfo.Where(sheet => sheet.check_account_sheet_id.Equals(sheet_id)).ToList();
            }
            if (summaryResult.summaryPrePaySheetsInfo != null)
            {
                summaryResult.summaryPrePaySheetsInfo = summaryResult.summaryPrePaySheetsInfo.Where(sheet => sheet.check_account_sheet_id.Equals(sheet_id)).ToList();
            }
            if (summaryResult.summaryGetArrearsSheetsInfo != null)
            {
                summaryResult.summaryGetArrearsSheetsInfo = summaryResult.summaryGetArrearsSheetsInfo.Where(sheet => sheet.check_account_sheet_id.Equals(sheet_id)).ToList();
            }
            if (summaryResult.summaryFeeOutSheetsInfo != null)
            {
                summaryResult.summaryFeeOutSheetsInfo = summaryResult.summaryFeeOutSheetsInfo.Where(sheet => sheet.check_account_sheet_id.Equals(sheet_id)).ToList();
            }
            if (summaryResult.summaryIncomeSheetsInfo != null)
            {
                summaryResult.summaryIncomeSheetsInfo = summaryResult.summaryIncomeSheetsInfo.Where(sheet => sheet.check_account_sheet_id.Equals(sheet_id)).ToList();
            }

            dynamic tempCheck = ToolClassificationSheetToUnCheckOrCheckedOrRedCheck(summaryResult);
            List<TableRowModel> tableResult = new List<TableRowModel>();
            List<TableRowModel> tempCheckSheetList = new List<TableRowModel>();
            if (result.red_flag == null)
            {
                tempCheckSheetList = tempCheck.tempCheckedSheetList;
            }
            else
            {
                tempCheckSheetList = tempCheck.tempRedCheckSheetList;
            }
            ToolSummarySheet(tempCheckSheetList, tableResult, "checkSheetToAccount", null);
            
            return new
            {
                sheetCheckSheetInfo = result,
                ItemClassInfo,
                saleSheetList,
                tableResult
            };

        }
        
        public static async Task<dynamic> LoadPrintTemplateService(dynamic parameter, CMySbCommand cmd)
        {
            string operKey = parameter.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            parameter.companyID = companyID;
            return await CheckAccountDao.LoadPrintTemplateDao(parameter, cmd);
        }
        
        
        private static dynamic ToolClassificationSheetToUnCheckOrCheckedOrRedCheck(SummaryListModel daoResult )
        {
            List<TableRowModel> saleSheetsInfo = daoResult.summarySaleSheetsInfo;
            List<TableRowModel> preGetSheetsInfo = daoResult.summaryPreGetSheetsInfo;
            List<TableRowModel> prePaySheetsInfo = daoResult.summaryPrePaySheetsInfo;
            List<TableRowModel> getArrearsSheetsInfo = daoResult.summaryGetArrearsSheetsInfo;
            List<TableRowModel> summaryFeeOutSheetsInfo = daoResult.summaryFeeOutSheetsInfo;
            List<TableRowModel> summaryIncomeSheetsInfo = daoResult.summaryIncomeSheetsInfo;
            
            List<TableRowModel> tempUnCheckSheetList = new List<TableRowModel>();
            List<TableRowModel> tempCheckedSheetList = new List<TableRowModel>();
            List<TableRowModel> tempRedCheckSheetList = new List<TableRowModel>();
            ToolClassificationSheetListCheckInfo(saleSheetsInfo, tempUnCheckSheetList,tempCheckedSheetList,tempRedCheckSheetList);
            ToolClassificationSheetListCheckInfo(preGetSheetsInfo, tempUnCheckSheetList,tempCheckedSheetList,tempRedCheckSheetList);
            ToolClassificationSheetListCheckInfo(prePaySheetsInfo, tempUnCheckSheetList,tempCheckedSheetList,tempRedCheckSheetList);
            ToolClassificationSheetListCheckInfo(getArrearsSheetsInfo, tempUnCheckSheetList,tempCheckedSheetList,tempRedCheckSheetList);
            ToolClassificationSheetListCheckInfo(summaryFeeOutSheetsInfo, tempUnCheckSheetList,tempCheckedSheetList,tempRedCheckSheetList);
            ToolClassificationSheetListCheckInfo(summaryIncomeSheetsInfo, tempUnCheckSheetList,tempCheckedSheetList,tempRedCheckSheetList);
            return new
            {
                tempUnCheckSheetList, tempCheckedSheetList, tempRedCheckSheetList
            };
        }

        private static void ToolClassificationSheetListCheckInfo(List<TableRowModel> sheetList,List<TableRowModel> unCheckSheetList, List<TableRowModel> checkedSheetList, List<TableRowModel> redCheckSheetList)
        {
            List<TableRowModel> tempRedCheckSheetList = new List<TableRowModel>();
            if (sheetList != null)
            {
                foreach (TableRowModel sheet in sheetList)
                {
                    if (string.IsNullOrEmpty(sheet.check_account_sheet_id) && string.IsNullOrEmpty(sheet.check_account_red_flag))
                    {
                        unCheckSheetList.Add(sheet);
                    } else if (!string.IsNullOrEmpty(sheet.check_account_sheet_id) && !string.IsNullOrEmpty(sheet.check_account_red_flag) && "1".Equals(sheet.check_account_red_flag))
                    {
                        tempRedCheckSheetList.Add(sheet);
                        redCheckSheetList.Add(sheet);
                    } else if (!string.IsNullOrEmpty(sheet.check_account_sheet_id) && string.IsNullOrEmpty(sheet.check_account_red_flag))
                    {
                        checkedSheetList.Add(sheet);
                    }
                }
                foreach (TableRowModel sheet in tempRedCheckSheetList)
                {
                    // 如果红冲单据没有被二次交账，那么应该可以重新交账
                    TableRowModel findObj = checkedSheetList.Find(findItem => findItem.sheet_id.Equals(sheet.sheet_id));
                    if (findObj == null)
                    { 
                        findObj = unCheckSheetList.Find(findItem => findItem.sheet_id.Equals(sheet.sheet_id));
                        if (findObj == null)
                        {
                            unCheckSheetList.Add(sheet);
                        }
                    }
                }
            }
        }
        /// <summary>
        /// 单据进行汇总
        /// </summary>
        /// <param name="sheetList"></param>
        /// <param name="result"></param>
        /// <param name="classification"></param>
        /// <param name="hasPayWay3"></param>
        private static void ToolSummarySheet(List<TableRowModel> sheetList, List<TableRowModel> resultList, string classification, SummaryCommonListModel result)
        {
            if(sheetList == null) return;
            foreach (TableRowModel sheet in sheetList)
            {
                TableRowModel findObj = resultList.Find(findItem => findItem.seller_id.Equals(sheet.seller_id));
                if ("day".Equals(classification))
                {
                    findObj = resultList.Find(findItem => findItem.seller_id.Equals(sheet.seller_id) && findItem.happen_time.Equals(sheet.happen_time));
                }
                else if ("unchecksheet".Equals(classification) || "checkedOrRedCheckSheetList".Equals(classification))
                {
                    findObj = null;
                }
                else if ("checksheet".Equals(classification) || "redCheckSheet".Equals(classification))
                {
                    findObj = resultList.Find(findItem => findItem.check_account_sheet_id.Equals(sheet.check_account_sheet_id));
                }
                if (findObj == null)
                {
                    findObj = new TableRowModel();
                    findObj.un_check_sheet_num = 0;
                    findObj.submitted_amount = 0;
                    findObj.not_submitted_amount = 0;
                    findObj.seller_id = sheet.seller_id;
                    findObj.sheet_id = sheet.sheet_id;
                    findObj.sheet_no = sheet.sheet_no;
                    findObj.sheet_type = sheet.sheet_type;
                    findObj.oper_name = sheet.oper_name;
                    findObj.sup_name = sheet.sup_name;
                    findObj.make_brief=sheet.make_brief;
                    findObj.happen_time = sheet.happen_time;
                    findObj.happen_time_origin = sheet.happen_time_origin;
                    findObj.check_account_sheet_id = sheet.check_account_sheet_id;
                    findObj.check_account_sheet_happen_time = sheet.check_account_sheet_happen_time;
                    findObj.check_account_sheet_maker_id = sheet.check_account_sheet_maker_id;
                    findObj.check_account_sheet_maker_name = sheet.check_account_sheet_maker_name;
                    findObj.check_account_sheet_approve_id = sheet.check_account_sheet_approve_id;
                    findObj.check_account_sheet_approve_name = sheet.check_account_sheet_approve_name;
                    findObj.check_account_sheet_make_brief = sheet.check_account_sheet_make_brief;
                    findObj.check_account_red_time = sheet.check_account_red_time;
                    findObj.check_account_red_flag = sheet.check_account_red_flag;
                    findObj.check_account_sheet_reder_id = sheet.check_account_sheet_reder_id;
                    findObj.check_account_sheet_reder_name = sheet.check_account_sheet_reder_name;
                    findObj.SummarySaleSheetModel = new BaseSheetModel();
                    findObj.SummarySaleSheetModel.modelIncludeSheetList = new List<BaseSheetModel>();
                    findObj.SummaryPreGetSheetModel = new BaseSheetModel();
                    findObj.SummaryPreGetSheetModel.modelIncludeSheetList = new List<BaseSheetModel>();
                    findObj.SummaryPrePaySheetModel = new BaseSheetModel();
                    findObj.SummaryPrePaySheetModel.modelIncludeSheetList = new List<BaseSheetModel>();
                    findObj.SummaryGetArrearsSheetModel = new BaseSheetModel();
                    findObj.SummaryGetArrearsSheetModel.modelIncludeSheetList = new List<BaseSheetModel>();
                    findObj.SummaryFeeOutSheetModel = new BaseSheetModel();
                    findObj.SummaryFeeOutSheetModel.modelIncludeSheetList = new List<BaseSheetModel>();
                    findObj.SummaryIncomeSheetModel = new BaseSheetModel();
                    findObj.SummaryIncomeSheetModel.modelIncludeSheetList = new List<BaseSheetModel>();
                    findObj.PaidInPayWayInfo = new List<PayWayInfoModel>();
                    findObj.OtherPayWayInfo = new List<PayWayInfoModel>();
                    findObj.IncludeSheetList = new List<IncludeBaseSheetInfo>();
                    resultList.Add(findObj);
                }
                // 记录本节点包含哪些单据
                findObj.IncludeSheetList.Add(new IncludeBaseSheetInfo(sheet.seller_id, sheet.sheet_id, sheet.sheet_no, sheet.sheet_type));
                // 统计未交单据数量
                findObj.un_check_sheet_num++;
                bool modelIncludeSheetList = "checkSheetToAccount".Equals(classification); // 交账需要单据本身
                // 统计单据未交
                findObj.not_submitted_amount += sheet.real_get_amount;
                if ("X".Equals(sheet.sheet_type) || "T".Equals(sheet.sheet_type))
                {
                    HandleSummaryMoney(findObj.SummarySaleSheetModel, findObj, sheet,modelIncludeSheetList);
                }
                else if ("YS".Equals(sheet.sheet_type) || "DH".Equals(sheet.sheet_type))
                {
                    HandleSummaryMoney(findObj.SummaryPreGetSheetModel, findObj, sheet,modelIncludeSheetList);
                }
                else if ("SK".Equals(sheet.sheet_type) || "FK".Equals(sheet.sheet_type))
                {
                    HandleSummaryMoney(findObj.SummaryGetArrearsSheetModel, findObj, sheet,modelIncludeSheetList);
                }
                else if ("YF".Equals(sheet.sheet_type))
                {
                    HandleSummaryMoney(findObj.SummaryPrePaySheetModel, findObj, sheet,modelIncludeSheetList);
                }
                else if ("ZC".Equals(sheet.sheet_type))
                {
                    HandleSummaryMoney(findObj.SummaryFeeOutSheetModel, findObj, sheet,modelIncludeSheetList);
                }
                else if ("SR".Equals(sheet.sheet_type))
                {
                    HandleSummaryMoney(findObj.SummaryIncomeSheetModel, findObj, sheet,modelIncludeSheetList);
                }
            }
        }

        /// <summary>
        /// 该部分数据不参与人、天汇总，为了方便进行展示添加
        /// </summary>
        /// <param name="sheetList"></param>
        /// <param name="resultList"></param>
        private static void ToolAddSummarySheetForExpand(List<TableRowModel> sheetList, List<TableRowModel> resultList, string classification, string listType)
        {
            if(sheetList == null) return;
            for (int i = 0; i < sheetList.Count; i++)
            {
                TableRowModel sheet = sheetList[i];
                TableRowModel findObj = resultList.Find(findItem => findItem.seller_id.Equals(sheet.seller_id));
                if ("day".Equals(classification))
                {
                    findObj = resultList.Find(findItem => findItem.seller_id.Equals(sheet.seller_id) && findItem.happen_time.Equals(sheet.happen_time));
                }
                if (findObj == null)
                {
                     findObj = new TableRowModel();
                     findObj.submitted_amount = 0;
                     findObj.not_submitted_amount = 0;
                    findObj.seller_id = sheet.seller_id;
                    findObj.sheet_id = sheet.sheet_id;
                    findObj.sheet_no = sheet.sheet_no;
                    findObj.sheet_type = sheet.sheet_type;
                    findObj.oper_name = sheet.oper_name;
                    findObj.sup_name = sheet.sup_name;
                    findObj.happen_time = sheet.happen_time;
                    findObj.happen_time_origin = sheet.happen_time_origin;
                    findObj.check_account_sheet_id = sheet.check_account_sheet_id;
                    findObj.check_account_sheet_happen_time = sheet.check_account_sheet_happen_time;
                    findObj.check_account_sheet_maker_id = sheet.check_account_sheet_maker_id;
                    findObj.check_account_sheet_maker_name = sheet.check_account_sheet_maker_name;
                    findObj.check_account_red_flag = sheet.check_account_red_flag;
                    findObj.SummarySaleSheetModel = new BaseSheetModel();
                    findObj.SummarySaleSheetModel.modelIncludeSheetList = new List<BaseSheetModel>();
                    findObj.SummaryPreGetSheetModel = new BaseSheetModel();
                    findObj.SummaryPreGetSheetModel.modelIncludeSheetList = new List<BaseSheetModel>();
                    findObj.SummaryPrePaySheetModel = new BaseSheetModel();
                    findObj.SummaryPrePaySheetModel.modelIncludeSheetList = new List<BaseSheetModel>();
                    findObj.SummaryGetArrearsSheetModel = new BaseSheetModel();
                    findObj.SummaryGetArrearsSheetModel.modelIncludeSheetList = new List<BaseSheetModel>();
                    findObj.SummaryFeeOutSheetModel = new BaseSheetModel();
                    findObj.SummaryFeeOutSheetModel.modelIncludeSheetList = new List<BaseSheetModel>();
                    findObj.SummaryIncomeSheetModel = new BaseSheetModel();
                    findObj.SummaryIncomeSheetModel.modelIncludeSheetList = new List<BaseSheetModel>();
                    findObj.PaidInPayWayInfo = new List<PayWayInfoModel>();
                    findObj.OtherPayWayInfo = new List<PayWayInfoModel>();
                    findObj.IncludeSheetList = new List<IncludeBaseSheetInfo>();
                    resultList.Add(findObj);
                }

                if (listType == "checked")
                {
                    findObj.submitted_amount += sheet.real_get_amount;
                }
            }
        }

        /// <summary>
        /// 处理单据信息
        /// </summary>
        /// <param name="sonSheetModel"></param>
        /// <param name="findObj"></param>
        /// <param name="sheet"></param>
        private static void HandleSummaryMoney(BaseSheetModel sonSheetModel, TableRowModel findObj, TableRowModel sheet, bool modelIncludeSheetList = false)
        {
            if (modelIncludeSheetList)
            {
                sonSheetModel.modelIncludeSheetList.Add(sheet);
            }
            sonSheetModel.seller_id = sheet.seller_id;
            sonSheetModel.sheet_id = sheet.sheet_id;
            sonSheetModel.sheet_no = sheet.sheet_no;
            sonSheetModel.sheet_type = sheet.sheet_type;
            sonSheetModel.oper_name = sheet.oper_name;
            sonSheetModel.happen_time = sheet.happen_time;
            sonSheetModel.happen_time_origin = sheet.happen_time_origin;
            sonSheetModel.real_get_amount += sheet.real_get_amount;
            sonSheetModel.total_amount += sheet.total_amount;
            sonSheetModel.sale_total_amount += sheet.sale_total_amount;
            sonSheetModel.return_total_amount += sheet.return_total_amount;
            sonSheetModel.now_disc_amount += sheet.now_disc_amount;
            sonSheetModel.left_total_amount += sheet.left_total_amount;
            sonSheetModel.preget_total_amount += sheet.preget_total_amount;
            sonSheetModel.feeout_total_amount += sheet.feeout_total_amount;
            sonSheetModel.payway_info_list = JsonConvert.DeserializeObject(sheet.payway_info_list);
            ToolPayWay(sonSheetModel, sonSheetModel);
            ToolPayWay(sonSheetModel, findObj);
            ToolPayWayForDiscLeft(sheet, findObj);
        }
        /// <summary>
        /// 处理支付方式
        /// </summary>
        /// <param name="origin"></param>
        /// <param name="target"></param>
        /// <typeparam name="T"></typeparam>
        private static void ToolPayWay<T>(T origin, T target) where T : BaseSheetModel
        {
            PayWayInfoModel payWayInfo = null;
            JToken paywayIdToken = null;
            int paywayId;
            decimal paywayAmount;
            string paywaySubType;
            string paywaySubName;
            foreach (JObject sheetPayWayInfo in origin.payway_info_list)
            {
                paywayIdToken = sheetPayWayInfo.GetValue("payway1_id");
                if (paywayIdToken != null && paywayIdToken.ToString() != "")
                {
                    paywayId = (int) (sheetPayWayInfo.GetValue("payway1_id"));
                    paywayAmount = CPubVars.ToDecimal(sheetPayWayInfo.GetValue("payway1_amount"));
                    paywaySubType = (string) sheetPayWayInfo.GetValue("payway1_sub_type");
                    paywaySubName = (string) sheetPayWayInfo.GetValue("payway1_sub_name");
                    HandlePayWayInfo(target, paywayId, paywayAmount, paywaySubType, paywaySubName);
                }

                paywayIdToken = sheetPayWayInfo.GetValue("payway2_id");
                if (paywayIdToken != null && paywayIdToken.ToString() != "")
                {
                    paywayId = (int) (sheetPayWayInfo.GetValue("payway2_id"));
                    paywayAmount = CPubVars.ToDecimal(sheetPayWayInfo.GetValue("payway2_amount"));
                    paywaySubType = (string) sheetPayWayInfo.GetValue("payway2_sub_type");
                    paywaySubName = (string) sheetPayWayInfo.GetValue("payway2_sub_name");
                    HandlePayWayInfo(target, paywayId, paywayAmount, paywaySubType, paywaySubName);
                }

                paywayIdToken = sheetPayWayInfo.GetValue("payway3_id");
                if (paywayIdToken != null && paywayIdToken.ToString() != "")
                {
                    paywayId = (int) (sheetPayWayInfo.GetValue("payway3_id"));
                    paywayAmount = CPubVars.ToDecimal(sheetPayWayInfo.GetValue("payway3_amount"));
                    paywaySubType = (string) sheetPayWayInfo.GetValue("payway3_sub_type");
                    paywaySubName = (string) sheetPayWayInfo.GetValue("payway3_sub_name");
                    HandlePayWayInfo(target, paywayId, paywayAmount, paywaySubType, paywaySubName);
                }
            }
        }

        private static void ToolPayWayForDiscLeft<T>(T origin, T target) where T : BaseSheetModel
        {
            target.LeftAmountPayWayInfo ??= new PayWayInfoModel(0, 0, "leftAmount", "欠款",0);
            target.DiscAmountPayWayInfo ??= new PayWayInfoModel(0, 0, "discAmount", "优惠",0);
            target.LeftAmountPayWayInfo.PayWayAmount += origin.left_total_amount;
            target.DiscAmountPayWayInfo.PayWayAmount += origin.now_disc_amount;
            if (origin.left_total_amount != 0)
            {
                target.LeftAmountPayWayInfo.OriginSheetNum++;
            }
            if (origin.now_disc_amount != 0)
            {
                target.DiscAmountPayWayInfo.OriginSheetNum++;
            }
        }

        /// <summary>
        /// 处理单据信息中的支付方式
        /// </summary>
        /// <param name="summaryModel">支付方式汇总到的model</param>
        /// <param name="payway_id"></param>
        /// <param name="payway_amount"></param>
        /// <param name="payway_sub_type"></param>
        /// <param name="payway_sub_name"></param>
        private static void HandlePayWayInfo<T>(T summaryModel, int payway_id, decimal payway_amount, string payway_sub_type, string payway_sub_name) where T : BaseSheetModel
        {
            summaryModel.PaidInPayWayInfo ??= new List<PayWayInfoModel>();
            summaryModel.OtherPayWayInfo ??= new List<PayWayInfoModel>();
            PayWayInfoModel payWayInfo =
                summaryModel.PaidInPayWayInfo.Find(paywayItem => paywayItem.PayWayId == payway_id);
            payWayInfo ??= summaryModel.OtherPayWayInfo.Find(paywayItem => paywayItem.PayWayId == payway_id);
            if (payWayInfo == null)
            {
                payWayInfo = new PayWayInfoModel();
                payWayInfo.PayWayId = payway_id;
                payWayInfo.SubType = payway_sub_type;
                payWayInfo.SubName = payway_sub_name;
                payWayInfo.PayWayAmount = 0;
                if ("QT".Equals(payWayInfo.SubType))
                {
                    summaryModel.PaidInPayWayInfo.Add(payWayInfo);
                }
                else
                {
                    summaryModel.OtherPayWayInfo.Add(payWayInfo);
                }
            }

            payWayInfo.PayWayAmount += payway_amount;
        }
        /// <summary>
        /// 将所有单据的支付方式进行汇总，供前端表头使用
        /// </summary>
        /// <param name="origin"></param>
        /// <param name="PaidInPayWayInfo"></param>
        /// <param name="OtherPayWayInfo"></param>
        private static void HandlePayWayTableTitle(SummaryCommonListModel origin, List<PayWayInfoModel> PaidInPayWayInfo, List<PayWayInfoModel> OtherPayWayInfo)
        {
            if (PaidInPayWayInfo != null)
            {
                foreach (PayWayInfoModel payWayItem in PaidInPayWayInfo)
                {
                    PayWayInfoModel findPayWayInfoModel = origin.PaidInPayWayInfo.Find(paidItem => paidItem.PayWayId.Equals(payWayItem.PayWayId));
                    if (findPayWayInfoModel == null)
                    {
                        PayWayInfoModel addPayWayItem = new PayWayInfoModel();
                        addPayWayItem.PayWayId = payWayItem.PayWayId;
                        addPayWayItem.SubType = payWayItem.SubType;
                        addPayWayItem.SubName = payWayItem.SubName;
                        addPayWayItem.PayWayAmount = 0; // 表头不需要数据
                        origin.PaidInPayWayInfo.Add(addPayWayItem);
                    }
                }
            }

            if (OtherPayWayInfo != null)
            {
                foreach (PayWayInfoModel payWayItem in OtherPayWayInfo)
                {
                    PayWayInfoModel findPayWayInfoModel = origin.OtherPayWayInfo.Find(paidItem => paidItem.PayWayId.Equals(payWayItem.PayWayId));
                    if (findPayWayInfoModel == null)
                    {
                        PayWayInfoModel addPayWayItem = new PayWayInfoModel();
                        addPayWayItem.PayWayId = payWayItem.PayWayId;
                        addPayWayItem.SubType = payWayItem.SubType;
                        addPayWayItem.SubName = payWayItem.SubName;
                        addPayWayItem.PayWayAmount = 0; // 表头不需要数据
                        origin.OtherPayWayInfo.Add(addPayWayItem);
                    }
                }
            }

            
        }
        private static void ToolTableTitle(TableRowModel sheet, SummaryCommonListModel result)
        {
            string json = JsonConvert.SerializeObject(sheet);
            TableRowModel sheetItem = JsonConvert.DeserializeObject<TableRowModel>(json);
            sheetItem.payway_info_list = JsonConvert.DeserializeObject(sheetItem.payway_info_list);
            ToolPayWay(sheetItem, sheetItem);
            HandlePayWayTableTitle(result, sheetItem.PaidInPayWayInfo, sheetItem.OtherPayWayInfo);
        }



    }
}