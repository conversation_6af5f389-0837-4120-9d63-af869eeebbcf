﻿using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using ArtisanManage.YingJiangBackstage.Pojo;
using ArtisanManage.YingJiangBackstage.Utils;
using ArtisanManage.YingJiangMallMini.Model;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Build.Framework;
using Newtonsoft.Json;
using NPOI.HSSF.Record;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Dynamic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using static ArtisanManage.Models.AttenceLeave;
using ArtisanManage.YingJiangMallMini.Controller;
using System.Text.RegularExpressions;

namespace ArtisanManage.WebAPI
{
    [Route("api/[controller]/[action]")]
    public class RedPacketController : BaseController
    {
        #region Predefs
        private readonly IHttpClientFactory _httpClientFactory;

        public RedPacketController(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }
        #endregion

        #region Constants
        private const string DbTableName_Setting = "red_packet_setting";
        private const string DbTableName_Balance = "red_packet_balance";
        private const string DbTableName_History = "red_packet_history";
        private const string DbTableName_Plan = "sheet_red_packet_plan";

        private const string CommonError_NotRegisted = "下单客户未注册或正处于零售模式";

        /// <summary> 公司红包设置表-红包使用限制 </summary>
        internal static class RedPacketUseLimitType
        {
            /// <summary> 无限制 </summary>
            public const string NoLimit = "";
            /// <summary> 固定数额限制 </summary>
            public const string Fixed = "fixed";
            /// <summary> 百分比限制 </summary>
            public const string Percent = "percent";
        }

        /// <summary> 客户红包详表-变动类型 </summary>
        internal static class RedPacketChangeType
        {
            /// <summary> 注册奖励(新客红包) </summary>
            public const string RegisterReward = "register_reward";
            /// <summary> 下单返利 </summary>
            public const string PurchaseReward = "purchase_reward";
            /// <summary> 下单消耗 </summary>
            public const string PurchaseCost = "purchase_cost";
            /// <summary> 下单消耗重算(转单时重算并补充差额) 正余额增加， 负数减少</summary>
            public const string PurchaseCostRecalculate = "purchase_cost_recalculate";
            /// <summary> 红冲退回(退回消耗就是正的,退回返利就是负的) </summary>
            public const string RedReturn = "red_return";
            /// <summary> 后台调整(用户手动) </summary>
            public const string AdjustByCompany = "adjust_by_company";
            /// <summary> 后台调整(营匠技术人员调整) </summary>
            public const string AdjustByServer = "adjust_by_server";
            /// <summary> 主动发送 </summary>
            public const string ProactivelySend = "proactively_send";
        }
        /// <summary> 获取红包列表</summary>
        public class GetRedPacketListParameter
        {
            public string operKey { get; set; }
            public int pageSize { get; set; }
            public int pageNo { get; set; }
            public int? status { get; set; }
            public string red_packet_name { get; set; }
            public string red_packet_id { get; set; }
        }
        public class GetRedPacketDetailParameter    
        {
            public string operKey { get; set; }
            public int pageSize { get; set; }
            public int pageNo { get; set; }
            public string supcust_id { get; set; }
            public string group_id { get; set; }
            public string region_id { get; set; }
        }

        public class GetRedPacketQueryParameter
        {
            public string operKey { get; set; }
            public int pageSize { get; set; }
            public int pageNo { get; set; }
            public string supcust_name { get; set; }
            public int supcust_id { get; set; }
            public string group_name { get; set; }
            public int group_id { get; set; }
            public string region_name { get; set; }
            public int region_id { get; set; }
        }
        public class RedPacketListResultModel
        {
            public int red_packet_id { get; set; }                   
            public string red_packet_name { get; set; }
            public string red_packet_amount { get; set; }             
            public string red_packet_type { get; set; }               
            public string amount_type { get; set; }                  
            public decimal reach_cost { get; set; }                         
            public string restrict_range { get; set; }                
            public string restrict_goods { get; set; }                
            public string restrict_time { get; set; }                 
            public int red_packet_num { get; set; }                   
            public int red_packet_max_num { get; set; }               
            public string red_packet_sum { get; set; }                
            public int sent_red_packet_num { get; set; }              
            public decimal sent_red_packet_sum { get; set; }          
            public decimal tran_amount { get; set; }                  
            public int status { get; set; }                           
            public string create_time { get; set; }                   
            public string upload_time { get; set; }                   
        }
        public class GetRedPacketDetailResultModel
        {
            public string sup_name { get; set; }
            public string region_name { get; set; }
            public string group_name { get; set; }
            public string red_packet_name { get; set; }
            public string red_packet_amount { get; set; }
            public string tran_amount { get; set; }
            public string upload_time { get; set; }
            public string change_amount { get; set; }
            public string happen_time { get; set; }
            public string change_type { get; set; }
        }
        public class GetRedPacketBalanceResultModel
        {
            public string sup_name { get; set; }
            public string region_name { get; set; }
            public string group_name { get; set; }
            public string balance { get; set; }
        }
        public struct RedPacketChange
        {
            public string ChangeType { get; set; }
            public decimal ChangeAmount { get; set; }
            public string RelatePlanId { get; set; }
            public string TranAmount { get; set; }
            public RedPacketChange(string changeType, decimal changeAmount, string relatePlanId, string tranAmount)
            {
                ChangeType = changeType;
                ChangeAmount = changeAmount;
                TranAmount = tranAmount;
                RelatePlanId = relatePlanId;
            }

        }
        #endregion

        /* 数据库设计
         * --------------------------------------------------------------------------------------------------
         * red_packet_setting   # 公司红包设置表
         * --------------------------------------------------------------------------------------------------
            * company_id int4           - primary, unique
            * register_reward decimal   # 新客红包奖励量
            * purchase_reward decimal   # 下单返利百分比
            * use_limit_type text       # 红包使用限制 - '':无; 'fixed':固定数额限制; 'percent':百分比限制;
            * use_limit_amount decimal  # 限制数额(金额或百分比)
         * --------------------------------------------------------------------------------------------------
         * red_packet_balance   # 客户红包主表
         * --------------------------------------------------------------------------------------------------
            * company_id int4           - primary
            * supcust_id int4           - primary
            * balance decimal           # 剩余红包金额
         * --------------------------------------------------------------------------------------------------
         * red_packet_history   # 客户红包详表 (使用/充值记录)
         * --------------------------------------------------------------------------------------------------
            * flow_id serial            - primary
            * company_id int4           - not null
            * supcust_id int4           - not null
            * happen_time timestamp     - not null
            * change_amount decimal     # 变动数额
            * change_type text          # 变动类型 - 'register_reward';'purchase_reward';'purchase_cost';'adjust'
            
            * relate_sheet_id int4      # 关联单据单号
            * relate_sheet_type text    # 关联单据类型
         * --------------------------------------------------------------------------------------------------
         * sheet_red_packet_plan   # 红包方案表 (使用/充值记录)
         * --------------------------------------------------------------------------------------------------
            * red_packet_plan_id serial            - primary
            * red_packet_plan_no string #方案编号
            * company_id int4           - not null
            * supcust_id int4           - not null
            * happen_time timestamp     - not null
            * change_amount decimal     # 变动数额
            * change_type text          # 变动类型 - 'register_reward';'purchase_reward';'purchase_cost';'adjust'
            
            * relate_sheet_id int4      # 关联单据单号
            * relate_sheet_type text    # 关联单据类型
         */

        #region Public: 申请新客红包 (for app post)-unused
        [HttpPost]
        public async Task<CallResult> ApplyRegisterReward([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            string supcustId = data.supcustId;
            Security.GetInfoFromOperKey(operKey, out string companyId);

            // 校验1. 客户id是否合法
            if (supcustId.IsInvalid() || supcustId == "-1")
                return new CallResult("Error", CommonError_NotRegisted);

            // 校验2. 是否已经领过注册红包
            var _sql_queue = new SQLQueue(cmd);
            var register_logs = new List<ExpandoObject>();
            string sql = $@"
                SELECT
                    *
                FROM
                    {DbTableName_History}
                WHERE
                    company_id = {companyId}
                    and supcust_id = {supcustId}
                    and change_type = '{RedPacketChangeType.RegisterReward}'
                LIMIT 10;
            ";
            _sql_queue.Enqueue("register_logs", sql);
            var _dr = await _sql_queue.ExecuteReaderAsync();
            while (_sql_queue.Count > 0)
            {
                var sqlName = _sql_queue.Dequeue();
                if (sqlName == "register_logs")
                    register_logs = CDbDealer.GetRecordsFromDr(_dr, false);
            }
            _dr.Close();
            _sql_queue.Clear();
            if (!(register_logs is null || register_logs.Count == 0))
                return new CallResult("Failed", "您已经领取过新客红包");

            // 获取公司设置,获得新客红包数量
            var companySettings = await LoadCompanySettings(cmd, companyId);
            if (!companySettings.IsOK) return companySettings;
            var companySettingData = JsonConvert.DeserializeObject<dynamic>(companySettings.data);
            decimal registerRewardAmount = companySettingData?.register_reward ?? 0;

            // 校验3. 是否没有新客红包
            if (registerRewardAmount <= 0)
                return new CallResult("Failed", "当前公司没有设置新客红包");

            // 记录红包变动
            var changes = new List<RedPacketChange>();
            changes.Add(new RedPacketChange(RedPacketChangeType.RegisterReward, registerRewardAmount, null,null));
            var logResult = await LogSheetRedPacketChange(cmd, null, null, companyId, supcustId, changes.ToList());
            if (!logResult.IsOK) return logResult;
            return new CallResult("OK", "", JsonConvert.SerializeObject(new { registerRewardAmount }));
        }
        #endregion

        #region Public: 发放新客红包 (Static) - unused
        /// <summary>
        /// 发放新客红包<br></br>
        /// 注意！函数内部未判断此客户是否为新客，将直接发放红包，请在调用前判断！
        /// </summary>
        public static async Task<CallResult> GrantRegisterReward(CMySbCommand cmd,
            string companyId, string supcustId)
        {
            if (supcustId.IsInvalid() || supcustId == "-1") { return new CallResult("Error", CommonError_NotRegisted); }
            var companySettings = await LoadCompanySettings(cmd, companyId);
            if (!companySettings.IsOK) return companySettings;
            var companySettingData = JsonConvert.DeserializeObject<dynamic>(companySettings.data);
            decimal registerRewardAmount = companySettingData?.register_reward ?? 0;
            if (registerRewardAmount <= 0) return new CallResult("OK", "");
            var changes = new List<RedPacketChange>();
            changes.Add(new RedPacketChange(
                RedPacketChangeType.RegisterReward, registerRewardAmount ,null,null
            ));
            var logResult = await LogSheetRedPacketChange(cmd, null, null, companyId, supcustId, changes);
            return logResult;
        }
        #endregion

        #region 小程序销售订单/销售单状态更改时的后台关联逻辑函数（待修改）
        /// <summary>
        /// 处理订单转单后审核的逻辑（待修改）
        /// </summary>
        public static async Task<CallResult> LogChangeOnSaleSheetApproved(CMySbCommand cmd,
            string companyId, string sheetId, string sheetType, string supcustId, string orderSheetId,
            decimal saleSheetPayAmount)
        {
            if (!AllValid(new List<string>() { companyId, sheetId, sheetType, supcustId, orderSheetId }))
                return new CallResult("Error", "单据信息不完整");

            // 加载订单信息
            var orderSheet = new SheetSaleOrder();
            await orderSheet.Load(cmd, companyId, orderSheetId);

            MallMinPurchaseRewardParameter parameter = new MallMinPurchaseRewardParameter();
            parameter.companyId=companyId;
            parameter.supcustId= Convert.ToInt32(orderSheet.supcust_id); 
            parameter.operKey= orderSheet.OperKey;
            foreach (var item in orderSheet.SheetRows)
            {
                GetPurchaseRewardRequiredSheetRow requiredSheetRow = new GetPurchaseRewardRequiredSheetRow();
                requiredSheetRow.item_id = item.item_id;
                requiredSheetRow.item_brand = item.brand_id;
                requiredSheetRow.item_class= item.other_class;
                requiredSheetRow.total_price = item.sub_amount;
                
                parameter.sheet_rows.Add(requiredSheetRow);
            }
            // 创建 MallMiniRedPacketController 的实例
            var mallMiniController = new MallMiniRedPacketController(cmd);
            // 调用实例方法
            var result = await mallMiniController.GetMallMiniRedPacket_PurchaseReward(parameter);
            var _sql_queue = new SQLQueue(cmd);
            var order_rplogs = new List<ExpandoObject>();

            var sql = $@"
                SELECT
                    change_amount, change_type
                FROM
                    {DbTableName_History}
                WHERE
                    company_id = {companyId}
                    and relate_sheet_id = {orderSheetId}
                    and relate_sheet_type = '{orderSheet.SheetType}';
            ";
            _sql_queue.Enqueue("load_history", sql);
            var _dr = await _sql_queue.ExecuteReaderAsync();
            while (_sql_queue.Count > 0)
            {
                var sqlName = _sql_queue.Dequeue();
                if (sqlName == "load_history")
                    order_rplogs = CDbDealer.GetRecordsFromDr(_dr, false);
            }
            _dr.Close();
            _sql_queue.Clear();

            var rph = new List<KeyValuePair<string, decimal>>();
            if (!(order_rplogs is null || order_rplogs.Count == 0))
            {
                foreach (dynamic log in order_rplogs)
                {
                    string change_type = log.change_type;
                    string change_amount_s = log.change_amount;
                    decimal change_amount = CPubVars.ToDecimal(change_amount_s);
                    rph.Add(new KeyValuePair<string, decimal>(change_type, change_amount));
                }
            }
            if (RphHasKey(RedPacketChangeType.PurchaseReward))
                return new CallResult("OK", "这个销售单来自旧版本的商城订单,不进行结算");

            var redpacketChanges = new List<RedPacketChange>();
            // 用户下单消耗了红包时,才进行红包消耗额的重算
            // 2024年6月28日 17点58分 push:chenyang, guide: xuchang 取消销售单审核的时候全是欠款，把销售订单消耗的红包退回的情况。不需要管退回，消耗就消耗。
            // if (RphHasKey(RedPacketChangeType.PurchaseCost))
            // {
            //     var orderAmount = orderSheet.now_pay_amount;
            //     if (saleSheetPayAmount < orderAmount) // 消耗掉的红包多退少不补
            //     {
            //         var oriCost = RphGetSum(RedPacketChangeType.PurchaseCost);
            //         var amountDiff = orderAmount - saleSheetPayAmount;
            //         var diff = oriCost * (amountDiff / orderAmount) * -1;
            //         if (diff > 0)
            //             redpacketChanges.TryAdd(RedPacketChangeType.PurchaseCostRecalculate, diff);
            //     }
            // }
            // 进行红包获取额的计算
            decimal purchaseReward = ((ArtisanManage.YingJiangMallMini.Model.GetPurchaseRewardResponse)((ArtisanManage.YingJiangBackstage.Pojo.ResultUtil<object>)result.Value).Result).total_purchase_reward_redpacket;
            var relatePlanId = "";
            var purchaseRewardDict = new Dictionary<int, decimal>();
            sql ="";
            if (purchaseReward > 0)
            {
                var details = ((ArtisanManage.YingJiangMallMini.Model.GetPurchaseRewardResponse)((ArtisanManage.YingJiangBackstage.Pojo.ResultUtil<object>)result.Value).Result).details;
                relatePlanId = string.Join(",", details.Select(d => d.red_packet_id).Distinct());
                var planIds = relatePlanId.Split(',').Select(int.Parse).ToList();
                var tranAmount = string.Join(",", planIds
                    .Select(id => details
                        .Where(d => d.red_packet_id == id.ToString())
                        .Sum(d => d.total_price)));
                purchaseRewardDict = planIds
                    .ToDictionary(
                        id => id,
                        id => details
                            .Where(d => d.red_packet_id == id.ToString())
                            .Sum(d => d.purchase_reward)
                    );
                redpacketChanges.Add(new RedPacketChange(RedPacketChangeType.PurchaseReward, purchaseReward, relatePlanId,tranAmount));
            }
            if (redpacketChanges.Count == 0)
                return new CallResult("OK", "未发生更改");
            
            var commitChangeResult = await LogSheetRedPacketChange(cmd,
                sheetId, sheetType, companyId, supcustId, redpacketChanges);
            if (commitChangeResult.result == "OK") {
                sql += $@"
                UPDATE sheet_red_packet_plan
                SET tran_amount = COALESCE(tran_amount, 0) + {saleSheetPayAmount}
                WHERE
                    company_id = {companyId}
                    AND red_packet_id IN ({relatePlanId});
            ";

            // 循环更新每个红包方案的发送统计
            foreach (var kvp in purchaseRewardDict)
            {
                sql += $@"
                    UPDATE sheet_red_packet_plan
                    SET sent_red_packet_sum = COALESCE(sent_red_packet_sum, 0) + {kvp.Value},
                        sent_red_packet_num = COALESCE(sent_red_packet_num, 0) + 1
                    WHERE
                        company_id = {companyId}
                        AND red_packet_id = {kvp.Key};
                ";
            }

            _sql_queue.Enqueue("update_red_packet_plan", sql);
            _dr = await _sql_queue.ExecuteReaderAsync();
            _dr.Close();
        }
            return commitChangeResult;
            bool RphHasKey(string key)
            {
                foreach (var h in rph)
                    if (h.Key == key) return true;
                return false;
            }
        }

        /// <summary>
        /// 订单冲改时搬迁订单红包记录的逻辑（正常启用）
        /// </summary>
        public static async Task<CallResult> MoveLogOnOrderRedAndChanged(CMySbCommand cmd,
            string companyId, string oldSheetId, string newSheetId, string sheetType, string supcustId)
        {
            if (!AllValid(new List<string>() { companyId, oldSheetId, newSheetId }))
                return new CallResult("Error", "信息不完整");

            var _sql_queue = new SQLQueue(cmd);
            var oldorder_rplogs = new List<ExpandoObject>();

            var sql = $@"
                SELECT
                    change_amount, change_type
                FROM
                    {DbTableName_History}
                WHERE
                    company_id = {companyId}
                    and relate_sheet_id = {oldSheetId}
                    and relate_sheet_type = '{sheetType}';
            ";
            _sql_queue.Enqueue("load_history", sql);

            var _dr = await _sql_queue.ExecuteReaderAsync();
            while (_sql_queue.Count > 0)
            {
                var sqlName = _sql_queue.Dequeue();
                if (sqlName == "load_history")
                    oldorder_rplogs = CDbDealer.GetRecordsFromDr(_dr, false);
            }
            _dr.Close();
            _sql_queue.Clear();

            if (oldorder_rplogs is null || oldorder_rplogs.Count == 0)
                return new CallResult("OK", "没有发生更改");

            var oldSheetRph = new List<RedPacketChange>();
            decimal totalChangeAmount = 0;
            foreach (dynamic log in oldorder_rplogs)
            {
                string change_type = log.change_type;
                string change_amount_s = log.change_amount;
                decimal change_amount = CPubVars.ToDecimal(change_amount_s);
                totalChangeAmount += change_amount;
                oldSheetRph.Add(new RedPacketChange(change_type, change_amount, null,null));
            }
            var rollBackOldSheetRph = new List<RedPacketChange>()
            {
                new RedPacketChange(RedPacketChangeType.RedReturn, totalChangeAmount * -1, null,null)
            };


            var msg = string.Empty;
            try
            {
                msg += (await LogSheetRedPacketChange(cmd, oldSheetId, sheetType, companyId, supcustId,
                    rollBackOldSheetRph)).msg;
                msg += (await LogSheetRedPacketChange(cmd, newSheetId, sheetType, companyId, supcustId,
                    oldSheetRph)).msg;
            }
            catch (Exception ex)
            {
                NLogger.Error(ex.ToString());
                msg = ex.Message;
            }

            var result = msg.Length > 0 ? "Error" : "OK";
            return new CallResult(result, msg);
        } 
        #endregion

        #region Public: 获取红包的基本信息 (for app post)-unused
        [HttpPost]
        public async Task<CallResult> AppGetRedPacketInfo([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyId);
            string supcustId = data.supcustId;
            var res = await LoadRedPacketInfo(cmd, companyId, supcustId);
            return res;
        }
        #endregion

        #region Public: 获取红包的基本信息 (Static)-unused
        /// <returns>
        /// 标准格式的CallResult，data为一个object的JSON字符串<br></br>
        /// object含有以下成员：register_reward;purchase_reward;use_limit_type;use_limit_amount;balance.<br></br>
        /// 须注意部分成员可能为null
        /// ！小程序获取接口调整到YingJiangMallMini下——>已修改为GetMallMiniRedPacketBaseInfo
        /// </returns>
        public static async Task<CallResult> LoadRedPacketInfo(CMySbCommand cmd,
            string companyId, string supcustId)
        {
            if (supcustId.IsInvalid() || supcustId == "-1") { return new CallResult("Error", CommonError_NotRegisted); }
            var _sql_queue = new SQLQueue(cmd);
            var redpacket_info = new List<ExpandoObject>();
            string sql = $@"
                SELECT
                    s.register_reward,
                    s.purchase_reward,
                    s.use_limit_type,
                    s.use_limit_amount,
                    m.balance,
                    (case when r.register_log_amount > 0 then true else false end) as user_registed
                FROM
                    {DbTableName_Setting} s
                LEFT JOIN
                    {DbTableName_Balance} m on
                        m.company_id = s.company_id
                        and m.supcust_id = {supcustId}
                LEFT JOIN (
		            select
                        count(*) as register_log_amount
                    from
                        {DbTableName_History}
                    where
                        company_id = {companyId}
                        and supcust_id = {supcustId}
                        and change_type = '{RedPacketChangeType.RegisterReward}'
                    limit 10
                ) r on true
                WHERE
                    s.company_id = {companyId};
            ";
            _sql_queue.Enqueue("load_base", sql);
            var _dr = await _sql_queue.ExecuteReaderAsync();
            while (_sql_queue.Count > 0)
            {
                var sqlName = _sql_queue.Dequeue();
                if (sqlName == "load_base")
                    redpacket_info = CDbDealer.GetRecordsFromDr(_dr, false);
            }
            _dr.Close();
            _sql_queue.Clear();

            string _data;
            if (redpacket_info is null || redpacket_info.Count == 0)
                _data = GetDefaultInfo();
            else
                _data = JsonConvert.SerializeObject(redpacket_info[0]);

            return new CallResult("OK", "", _data);

            static string GetDefaultInfo()
            {
                var _data = new
                {
                    register_reward = 0,
                    purchase_reward = 0,
                    use_limit_type = "",
                    use_limit_amount = 0,
                    balance = 0,
                    user_registed = false
                };
                return JsonConvert.SerializeObject(_data);
            }
        }
        #endregion

        #region 退回红包 (Static)（正常启用）
        /// <summary>
        /// 订单红冲或删除时,退回用户使用和获得的红包
        /// </summary>
        public static async Task<CallResult> ReturnSheetRedPackets(CMySbCommand cmd,
            string sheetId, string sheetType, string companyId, string supcustId)
        {
            if (sheetId.IsInvalid() || sheetType.IsInvalid() || companyId.IsInvalid() || supcustId.IsInvalid())
                return new CallResult("Error", "单据信息不完整");

            var _sql_queue = new SQLQueue(cmd);
            var redpackets = new List<ExpandoObject>();

            var sql = $@"
                SELECT
                    flow_id, change_amount, change_type
                FROM
                    {DbTableName_History}
                WHERE
                    company_id = '{companyId}'
                    and supcust_id = '{supcustId}'
                    and relate_sheet_id = '{sheetId}'
                    and relate_sheet_type = '{sheetType}'
                ORDER BY 
                    happen_time DESC;
            ";
            _sql_queue.Enqueue("sheet_redpackets", sql);

            var _dr = await _sql_queue.ExecuteReaderAsync();
            while (_sql_queue.Count > 0)
            {
                var sqlName = _sql_queue.Dequeue();
                if (sqlName == "sheet_redpackets")
                    redpackets = CDbDealer.GetRecordsFromDr(_dr, false);
            }
            _dr.Close();
            _sql_queue.Clear();

            if (redpackets is null || redpackets.Count == 0)
                return new CallResult("OK", "没有红包需要退回");

            var returnChanges = new List<RedPacketChange>();
            foreach (dynamic redpacket in redpackets)
            {
                string change_type = redpacket.change_type ?? "";
                string change_amount_s = redpacket.change_amount ?? "0";
                decimal change_amount = CPubVars.ToDecimal(change_amount_s);

                // 考虑一个订单被多次退回的情况(实际上应当不会发生)
                if (change_type == RedPacketChangeType.RedReturn)
                    break;

                returnChanges.Add(new RedPacketChange(
                    RedPacketChangeType.RedReturn, change_amount * -1, null,null
                ));
            }

            var callResult = await LogSheetRedPacketChange(cmd,
                sheetId, sheetType, companyId, supcustId, returnChanges);
            return callResult;
        }
        #endregion

        #region 调整红包余额 (Static)（正常启用）
        public static async Task<CallResult> AdjustBalance(CMySbCommand cmd,
            string companyId, string supcustId, decimal balance, string adjustType = RedPacketChangeType.AdjustByCompany)
        {
            string msg;
            try
            {
                var curr_balance = await GetRedPacketLeftAmount(cmd, companyId, supcustId);
                if (curr_balance == balance)
                    return new CallResult("OK", "");
                var adjust_amount = balance - curr_balance;

                var changes = new List<RedPacketChange>();
                changes.Add(new RedPacketChange(
                    adjustType, adjust_amount, null,null
                ));

                var log_result = await LogSheetRedPacketChange(cmd, null, null, companyId, supcustId, changes);
                msg = log_result.IsOK ? "" : log_result.msg;
            }
            catch (Exception ex)
            {
                NLogger.Error(ex.ToString());
                msg = ex.Message ?? "";
            }
            var result = msg.Length > 0 ? "Error" : "OK";
            return new CallResult(result, msg);
        }
        #endregion

        #region Public: 记录红包变动 (Static)(正常启用)
        public static async Task<CallResult> LogSheetRedPacketChange(CMySbCommand cmd,
            string sheetId, string sheetType,
            string companyId, string supcustId, List<RedPacketChange> changes)
        {
            if (changes.Count == 0)
                return new CallResult("OK", "");
            if (supcustId.IsInvalid() || supcustId == "-1")
                return new CallResult("Error", CommonError_NotRegisted);

            string msg = string.Empty;
            string sql = string.Empty;

            string values = string.Empty;
            decimal totalChangeAmount = 0;
     
            foreach (var kvp in changes)
            {
                string changeType = kvp.ChangeType;
                string relatePlanId = kvp.RelatePlanId;
                string tranAmount = kvp.TranAmount;
                decimal changeAmount = kvp.ChangeAmount.ToMoney();
                if (changeAmount == 0) continue;
                totalChangeAmount += changeAmount;
                if (values.Length > 0) values += ",";
                values += $"({companyId}, {supcustId}, '{CPubVars.GetDateText(DateTime.Now)}', {changeAmount}, '{changeType}', {cell(sheetId)}, {cell(sheetType)},'{relatePlanId}','{tranAmount}')";
            }

            if (values.Length == 0)
                return new CallResult("OK", "");

            sql += $@"
                INSERT INTO
                    {DbTableName_History}
                    (company_id, supcust_id, happen_time, change_amount, change_type, relate_sheet_id, relate_sheet_type,relate_plan_id,tran_amount)
                VALUES
                    {values}
                RETURNING
                    flow_id;
            ";
            if (totalChangeAmount != 0)
            {
                totalChangeAmount = totalChangeAmount.ToMoney();
                string _operator = totalChangeAmount < 0 ? "" : "+";
                sql += $@"
                    INSERT INTO
                        {DbTableName_Balance} as m
                        (company_id, supcust_id, balance)
                    VALUES
                        ({companyId}, {supcustId}, {totalChangeAmount})
                    ON CONFLICT (supcust_id)
                    DO UPDATE SET
                        balance = m.balance {_operator} {totalChangeAmount};
                ";
            }

            try
            {
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
            }
            catch (Exception ex)
            {
                NLogger.Error("[LogSheetRedPacketChange] 数据库操作失败，语句为" + sql);
                NLogger.Error(ex.ToString());
                msg = ex.Message;
            }

            string result = msg.Length > 0 ? "Error" : "OK";
            return new CallResult(result, msg);

            static string cell(string value)
            {
                return value.IsValid() ? $"'{value}'" : "null";
            }
        } 
        #endregion

        #region Public: 加载公司设置 (For app post)（正常启用）
        [HttpPost]
        public async Task<CallResult> LoadSettings([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyId);

            var loadResult = await LoadCompanySettings(cmd, companyId);
            return loadResult;
        }
        private static async Task<CallResult> LoadCompanySettings(CMySbCommand cmd, string companyId)
        {
            var _sql_queue = new SQLQueue(cmd);
            var redpacket_settings = new List<ExpandoObject>();
            string sql = $@"
                SELECT
                    register_reward, purchase_reward, use_limit_type, use_limit_amount
                FROM
                    {DbTableName_Setting}
                WHERE
                    company_id = {companyId};
            ";
            _sql_queue.Enqueue("load_settings", sql);
            var _dr = await _sql_queue.ExecuteReaderAsync();
            while (_sql_queue.Count > 0)
            {
                var sqlName = _sql_queue.Dequeue();
                if (sqlName == "load_settings")
                    redpacket_settings = CDbDealer.GetRecordsFromDr(_dr, false);
            }
            _dr.Close();
            _sql_queue.Clear();

            string _data;
            if (redpacket_settings is null || redpacket_settings.Count == 0)
                _data = GetDefaultSettings();
            else
                _data = JsonConvert.SerializeObject(redpacket_settings[0]);

            return new CallResult("OK", "", _data);

            static string GetDefaultSettings()
            {
                var _data = new { register_reward = 0, purchase_reward = 0, use_limit_type = "", use_limit_amount = 0 };
                return JsonConvert.SerializeObject(_data);
            }
        }
        #endregion

        #region Public: 保存公司设置 (For app post)（正常启用）
        [HttpPost]
        public async Task<CallResult> SaveSettings([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string company_id);
            decimal register_reward = data.register_reward ?? 0;
            decimal purchase_reward = data.purchase_reward ?? 0;
            string use_limit_type = data.use_limit_type ?? "";
            decimal use_limit_amount = data.use_limit_amount ?? 0;

            string msg = string.Empty;
            string sql = string.Empty;

            try
            {
                sql = $@"
                    INSERT INTO
                        {DbTableName_Setting}
                        (company_id, register_reward, purchase_reward, use_limit_type, use_limit_amount)
                    VALUES
                        ({company_id}, {register_reward}, {purchase_reward}, '{use_limit_type}', {use_limit_amount})
                    ON CONFLICT (company_id) 
                    DO UPDATE SET
                        register_reward = {register_reward},
                        purchase_reward = {purchase_reward},
                        use_limit_type = '{use_limit_type}',
                        use_limit_amount = {use_limit_amount};
                ";
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
            }
            catch (Exception ex)
            {
                NLogger.Error("[SaveSettings] 数据库操作失败，语句为" + sql);
                NLogger.Error(ex.ToString());
                msg = ex.Message;
            }
            string result = msg.Length > 0 ? "Error" : "OK";
            return new CallResult(result, msg);
        }
        #endregion

        #region Public: 获取红包余额 (For app post)（正常启用）
        [HttpPost]
        public async Task<CallResult> LoadLeftAmount([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            string supcustId = data.supcustId;
            if (supcustId.IsInvalid() || supcustId == "-1") { return new CallResult("Error", CommonError_NotRegisted); }
            Security.GetInfoFromOperKey(operKey, out string companyId);
            var leftAmount = await GetRedPacketLeftAmount(cmd, companyId, supcustId);
            return new CallResult("OK", "", leftAmount.ToString());
        }
        private async static Task<decimal> GetRedPacketLeftAmount(CMySbCommand cmd, string companyId, string supcustId)
        {
            var _sql_queue = new SQLQueue(cmd);
            var redpacket_leftamounts = new List<ExpandoObject>();
            string sql = $@"
                SELECT
                    balance
                FROM
                    {DbTableName_Balance}
                WHERE
                    company_id = {companyId}
                    and supcust_id = {supcustId};
            ";
            _sql_queue.Enqueue("load_leftamounts", sql);
            var _dr = await _sql_queue.ExecuteReaderAsync();
            while (_sql_queue.Count > 0)
            {
                var sqlName = _sql_queue.Dequeue();
                if (sqlName == "load_leftamounts")
                    redpacket_leftamounts = CDbDealer.GetRecordsFromDr(_dr, false);
            }
            _dr.Close();
            _sql_queue.Clear();

            decimal _data;
            if (redpacket_leftamounts is null || redpacket_leftamounts.Count == 0)
                _data = 0;
            else
            {
                string balanceStr = ((dynamic)redpacket_leftamounts[0])?.balance ?? "0";
                _data = CPubVars.ToDecimal(balanceStr);
            }

            return _data;
        }
        #endregion

        #region Public: 记录红包变动 (For app post) - unused
        [HttpPost]
        public async Task<CallResult> LogRedPacketChange([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            string supcustId = data.supcustId ?? "";
            decimal changeAmount = data.changeAmount ?? 0;
            string changeType = data.changeType ?? "";
            Security.GetInfoFromOperKey(operKey, out string companyId);
            if (supcustId.IsInvalid() || supcustId == "-1") { return new CallResult("Error", CommonError_NotRegisted); }

            string msg = string.Empty;
            string sql = string.Empty;

            string _operator = changeAmount < 0 ? "" : "+";

            if (changeAmount < 0)
            {
                var currLeftAmount = await GetRedPacketLeftAmount(cmd, companyId, supcustId);
                if (currLeftAmount + changeAmount < 0)
                    return new CallResult("Failed", "红包余额不足");
            }

            try
            {
                sql = $@"
                    INSERT INTO
                        {DbTableName_History}
                        (company_id, supcust_id, happen_time, change_amount, change_type)
                    VALUES
                        ({companyId}, {supcustId}, '{CPubVars.GetDateText(DateTime.Now)}', {changeAmount}, '{changeType}')
                    RETURNING
                        flow_id;

                    UPDATE
                        {DbTableName_Balance}
                    SET
                        balance = balance {_operator} {changeAmount}
                    WHERE
                        company_id = {companyId}
                        and supcust_id = {supcustId};
                ";
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
            }
            catch (Exception ex)
            {
                NLogger.Error("[LogRedPacketUse] 数据库操作失败，语句为" + sql);
                NLogger.Error(ex.ToString());
                msg = ex.Message;
            }

            string result = msg.Length > 0 ? "Error" : "OK";
            return new CallResult(result, msg);
        }
        #endregion

        #region 重新结算转单红包

        struct RedPacketLogForRecovery
        {
            public string supcust_id;
            public string order_sheet_id;
            public string order_sheet_no;
            public string sale_sheet_id;
            public string sale_sheet_no;
            public decimal order_sheet_pay_amount;
            public decimal sale_sheet_pay_amount;
            public int record_amount;
            public decimal total_money;

            public RedPacketLogForRecovery(dynamic log)
            {
                supcust_id = log.supcust_id ?? "";
                order_sheet_id = log.order_sheet_id ?? "";
                order_sheet_no = log.order_sheet_no ?? "";
                sale_sheet_id = log.sale_sheet_id ?? "";
                sale_sheet_no = log.sale_sheet_no ?? "";
                order_sheet_pay_amount = Convert.ToDecimal((string)log.order_sheet_pay_amount ?? "0");
                sale_sheet_pay_amount = Convert.ToDecimal((string)log.sale_sheet_pay_amount ?? "0");
                record_amount = Convert.ToInt32((string)log.record_amount ?? "0");
                total_money = Convert.ToDecimal((string)log.total_money ?? "0");
            }
        }
        [HttpPost]
        public async Task<CallResult> RecoverRedPackets([FromBody] dynamic data)
        {
            string companyId = data.companyId;
            return await RecoverRedPacketsStatic(cmd, companyId);
        }
        // 2024.06.26
        // 此函数针对此前出现因服务器问题而未在小程序订单转单审核时获得红包的情况
        // 对特定公司进行集中修复。
        public static async Task<CallResult> RecoverRedPacketsStatic(CMySbCommand cmd, string companyId)
        {
            var _sql_queue = new SQLQueue(cmd);
            var logs = new List<ExpandoObject>();
            var company_settings = new List<ExpandoObject>();
            string sql = $@"
                SELECT 
	                o.supcust_id,
	                o.sheet_id as order_sheet_id, o.sheet_no as order_sheet_no,
	                s.sheet_id as sale_sheet_id, s.sheet_no as sale_sheet_no,
	                o.now_pay_amount as order_sheet_pay_amount, s.now_pay_amount as sale_sheet_pay_amount,
	                rp.record_amount, rp.total_money
                FROM
	                sheet_sale_order_main o
	                LEFT JOIN sheet_sale_main s on o.sheet_id = s.order_sheet_id
	                LEFT JOIN (
		                SELECT
			                relate_sheet_id, count(*) as record_amount, sum(change_amount) as total_money
		                FROM
			                red_packet_history
		                WHERE
			                company_id = {companyId}
			                and change_type = 'purchase_reward'
			                and relate_sheet_id is not null
			                and relate_sheet_type = 'X'
		                GROUP BY
			                relate_sheet_id
	                ) rp on rp.relate_sheet_id = s.sheet_id
                WHERE
	                o.company_id = {companyId}
	                and o.order_source = 'xcx'
	                and s.sheet_id is not null
	                and (rp.record_amount is null or rp.record_amount = 0);
            ";
            _sql_queue.Enqueue("load_logs", sql);
            sql = $@"
                SELECT
                    purchase_reward
                FROM
                    {DbTableName_Setting}
                WHERE
                    company_id = {companyId};
            ";
            _sql_queue.Enqueue("load_settings", sql);
            var _dr = await _sql_queue.ExecuteReaderAsync();
            while (_sql_queue.Count > 0)
            {
                var sqlName = _sql_queue.Dequeue();
                if (sqlName == "load_logs")
                    logs = CDbDealer.GetRecordsFromDr(_dr, false);
                else if (sqlName == "load_settings")
                    company_settings = CDbDealer.GetRecordsFromDr(_dr, false);
            }
            _dr.Close();
            _sql_queue.Clear();

            if (logs is null || logs.Count == 0)
                return new("OK", "没有需要重新结算的红包");

            decimal purchaseRewardPercent;
            if (company_settings is null || company_settings.Count == 0)
                purchaseRewardPercent = 0;
            else
                purchaseRewardPercent = Convert.ToDecimal(((dynamic)company_settings[0])?.purchase_reward ?? "0");
            purchaseRewardPercent *= (decimal)0.01; // 数据库中保存的是返利百分比,实际运算需要考虑百分号

            if (purchaseRewardPercent <= 0)
                return new("OK", "返利比例为0，或者未设置返利比例，不进行红包结算");

            Console.WriteLine("开始结算工作...");
            // key: (sheet_id, supcust_id)
            // value: changes for `LogSheetRedPacketChange`
            var changes = new Dictionary<(string, string), List<RedPacketChange>>();
            foreach (dynamic log in logs)
            {
                var redPacketLog = new RedPacketLogForRecovery(log);
                var key = (redPacketLog.sale_sheet_id, redPacketLog.supcust_id);
                if (!changes.ContainsKey(key))
                    changes.Add(key, []);
                var change = new RedPacketChange(
                    RedPacketChangeType.PurchaseReward,
                    redPacketLog.sale_sheet_pay_amount * purchaseRewardPercent,
                    null,null
                );
                changes[key].Add(change);
            }
            Console.WriteLine("结算目标：");
            Console.WriteLine("单据ID | 客户ID | 变动内容");
            foreach (var key in changes.Keys)
            {
                Console.WriteLine($"{key.Item1} | {key.Item2} | {JsonConvert.SerializeObject(changes[key])}");
            }

            Console.WriteLine("开始提交...");
            foreach (var key in changes.Keys)
            {
                var changesList = changes[key];
                var _callResult = await LogSheetRedPacketChange(cmd, null, null, companyId, key.Item1, changesList);
                var _msg = _callResult.IsOK ? "成功" : "失败,因为" + _callResult.msg;
                Console.WriteLine($"单据{key.Item1}/{key.Item2}: 结算{_msg}");
            }
            Console.WriteLine("提交结束！");

            return new("OK", "红包结算完成");
        }

        #endregion

        #region Private Functions
        private static bool AllValid(List<string> parameters)
        {
            foreach (var param in parameters)
                if (param.IsInvalid()) return false;
            return true;
        }
        #endregion

        #region Public: 获取红包列表 (for app post)（正常启用-新增）
        [HttpPost]
        public async Task<object> GetRedPacketList([FromBody] GetRedPacketListParameter parameter)
        {
            List<RedPacketListResultModel> data = new List<RedPacketListResultModel>();
            var _sql_queue = new SQLQueue(cmd);
            int pageSize = parameter.pageSize;
            int pageNo = parameter.pageNo;
            string redPacketId = parameter.red_packet_id;
            string redPacketName = parameter.red_packet_name;
            int? status = parameter.status;
            string operKey = parameter.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyId);

            // 2. 构造查询条件
            var searchCondi = "";
            if (!redPacketId.IsInvalid())
                searchCondi += " and  red_packet_id ="+redPacketId ;
            if (status.HasValue)
                searchCondi += $" AND status = {status.Value}";
            if (!redPacketName.IsInvalid())
                searchCondi += $" AND red_packet_name LIKE '%{redPacketName}%'";

            // 3. 总数查询
            string countSql = $@"
                SELECT COUNT(*) 
                FROM sheet_red_packet_plan 
                WHERE company_id = {companyId} and status != CAST(-1 AS smallint) {searchCondi}";
            _sql_queue.Enqueue("count", countSql);
            int totalCount = 0;
            var countQueue = new SQLQueue(cmd);
            countQueue.Enqueue("count", countSql);
            var dr = await countQueue.ExecuteReaderAsync();
            if (await dr.ReadAsync())
                totalCount = Convert.ToInt32(dr[0]);
            dr.Close();
            countQueue.Clear();
            _sql_queue.Clear();

            // 4. 分页查询
            string dataSql = $@"
                SELECT * 
                FROM sheet_red_packet_plan 
                WHERE company_id = {companyId} and status != CAST(-1 AS smallint) {searchCondi}
                ORDER BY create_time DESC
                LIMIT {pageSize} 
                OFFSET {(pageNo - 1) * pageSize}";
            _sql_queue.Enqueue("records", dataSql);

            var resultList = new List<RedPacketListResultModel>();
            var recordsResult = await PageBaseModel.GetRecordsFromQQ(_sql_queue, cmd, 0, null);
            Dictionary<int, Dictionary<string, string>> rows = new Dictionary<int, Dictionary<string, string>>();
            foreach (var k in recordsResult)
            {
                rows = k.Value;
            }
            foreach (var row in rows.Values)
            {
                data.Add(new RedPacketListResultModel
                {
                    red_packet_id = Convert.ToInt32(row["red_packet_id"]),
                    red_packet_name = row["red_packet_name"]?.ToString() ?? "",
                    red_packet_amount = row["red_packet_amount"]?.ToString() ?? "",
                    red_packet_type = row["red_packet_type"]?.ToString() ?? "",
                    amount_type = row["amount_type"]?.ToString() ?? "",
                    reach_cost = row["reach_cost"] == "" ? 0m : Convert.ToDecimal(row["reach_cost"]),
                    restrict_range = row["restrict_range"]?.ToString() ?? "",
                    restrict_goods = row["restrict_goods"]?.ToString() ?? "",
                    restrict_time = row["restrict_time"]?.ToString() ?? "",
                    red_packet_num = Convert.ToInt32(row["red_packet_num"]),
                    red_packet_max_num = Convert.ToInt32(row["red_packet_max_num"]),
                    red_packet_sum = row["red_packet_sum"]?.ToString() ?? "",
                    sent_red_packet_num = row["sent_red_packet_num"] == "" ? 0 : Convert.ToInt32(row["sent_red_packet_num"]),
                    sent_red_packet_sum = row["sent_red_packet_sum"] == "" ? 0m : Convert.ToDecimal(row["sent_red_packet_sum"]),
                    tran_amount = row["tran_amount"] == "" ? 0m : Convert.ToDecimal(row["tran_amount"]),
                    status = Convert.ToInt32(row["status"]),
                    create_time = row["create_time"]?.ToString() ?? "",
                    upload_time = row["upload_time"]?.ToString() ?? ""
                });
            }
            var result = "OK";
            var msg = "";
            return Json(new { result, msg, data, itemCount=totalCount });
        }
        [HttpPost]
        public async Task<object> GetRedPacketDetail([FromBody] GetRedPacketDetailParameter parameter)
        {
            var _sql_queue = new SQLQueue(cmd);
            int pageSize = parameter.pageSize;
            int pageNo = parameter.pageNo;
            string groupId = parameter.group_id;
            string regionId = parameter.region_id;
            string supcustId = parameter.supcust_id;
            string operKey = parameter.operKey;
            var searchCondi = "";
            if (!groupId.IsInvalid())
            {
                searchCondi = " and  group_id in (" + groupId + ")";
            }
            if (!regionId.IsInvalid())
            {
                searchCondi += BuildRegionPrefixMatchSqlList(regionId);
            }
            if (!supcustId.IsInvalid())
                if (searchCondi != "") searchCondi += " and isu.supcust_id in (" + supcustId + ")";
                else searchCondi = " and isu.supcust_id in (" + supcustId + ")";
            StringBuilder condi = new StringBuilder();
            Security.GetInfoFromOperKey(operKey, out string companyId);

            // 添加总数查询
            string countSql = $@"
                SELECT COUNT(*)
                FROM red_packet_history rh
                LEFT JOIN info_supcust isu
                    ON rh.company_id = isu.company_id
                    AND rh.supcust_id = isu.supcust_id
                LEFT JOIN info_supcust_group isg
                    ON isu.company_id = isg.company_id
                    AND isu.sup_group = isg.group_id
                LEFT JOIN info_region ir
                    ON isu.company_id = ir.company_id
                    AND isu.region_id = ir.region_id
                CROSS JOIN LATERAL (
                    SELECT
                        plan_id::INT,
                        change_amount,
                        tran_amount
                    FROM
                        unnest(string_to_array(rh.relate_plan_id, ',')) WITH ORDINALITY AS plans(plan_id, ord)
                    LEFT JOIN
                        unnest(string_to_array(rh.tran_amount, ',')) WITH ORDINALITY AS trans(tran_amount, ord3)
                        ON plans.ord = trans.ord3
                ) AS split_data
                LEFT JOIN sheet_red_packet_plan rp
                    ON rp.company_id = rh.company_id
                    AND rp.red_packet_id = split_data.plan_id
                WHERE
                    rh.company_id = {companyId} {searchCondi};
                ";

            _sql_queue.Enqueue("count", countSql);
            int totalCount = 0;
            var countQueue = new SQLQueue(cmd);
            countQueue.Enqueue("count", countSql);
            var dr = await countQueue.ExecuteReaderAsync();
            if (await dr.ReadAsync())
                totalCount = Convert.ToInt32(dr[0]);
            dr.Close();
            countQueue.Clear();
            _sql_queue.Clear();

            string sql = $@"
                SELECT
                    isu.sup_name,
                    ir.region_name,
                    isg.group_name,
                    rp.red_packet_name,
                    split_data.change_amount::NUMERIC AS red_packet_amount,
                    rp.upload_time,
                    split_data.change_amount::NUMERIC AS change_amount,
                    COALESCE(split_data.tran_amount::DOUBLE PRECISION, 0) AS tran_amount,
                    rh.happen_time,
                    rh.change_type
                FROM red_packet_history rh
                LEFT JOIN info_supcust isu
                    ON rh.company_id = isu.company_id
                    AND rh.supcust_id = isu.supcust_id
                LEFT JOIN info_supcust_group isg
                    ON isu.company_id = isg.company_id
                    AND isu.sup_group = isg.group_id
                LEFT JOIN info_region ir
                    ON isu.company_id = ir.company_id
                    AND isu.region_id = ir.region_id
                CROSS JOIN LATERAL (
                    SELECT
                        plan_id::INT,
                        change_amount,
                        tran_amount
                    FROM
                        unnest(string_to_array(rh.relate_plan_id, ',')) WITH ORDINALITY AS plans(plan_id, ord)
                    LEFT JOIN
                        unnest(string_to_array(rh.tran_amount, ',')) WITH ORDINALITY AS trans(tran_amount, ord3)
                        ON plans.ord = trans.ord3
                ) AS split_data

                LEFT JOIN sheet_red_packet_plan rp
                    ON rp.company_id = rh.company_id
                    AND rp.red_packet_id = split_data.plan_id
                WHERE
                    rh.company_id = {companyId} {searchCondi}
                ORDER BY rh.happen_time DESC
                LIMIT {pageSize} 
                OFFSET {(pageNo - 1) * pageSize};
            ";

            _sql_queue.Enqueue("records", sql);
            Dictionary<int, Dictionary<string, string>> rows = new Dictionary<int, Dictionary<string, string>>();
            var recordsResult = await PageBaseModel.GetRecordsFromQQ(_sql_queue, cmd, 0, null);
            foreach (var k in recordsResult)
            {
                rows = k.Value;
            }
            List<GetRedPacketDetailResultModel> result = new List<GetRedPacketDetailResultModel>();

            foreach (var row in rows.Values)
            {
                result.Add(new GetRedPacketDetailResultModel
                {
                    sup_name = row["sup_name"]?.ToString() ?? "",
                    region_name = row["region_name"]?.ToString() ?? "",
                    group_name = row["group_name"]?.ToString() ?? "",
                    red_packet_name = row["red_packet_name"]?.ToString() ?? "",
                    red_packet_amount = row["red_packet_amount"]?.ToString() ?? "",
                    tran_amount = row["tran_amount"]?.ToString() ?? "",
                    upload_time = row["upload_time"]?.ToString() ?? "",
                    change_amount = row["change_amount"]?.ToString() ?? "",
                    happen_time = row["happen_time"]?.ToString() ?? "",
                    change_type = row["change_type"]?.ToString() ?? ""
                });

            }
            return Json(new { result = "OK", msg = "", data = result, itemCount=totalCount });
        }
        private string BuildRegionPrefixMatchSqlList(string regionIds)
        {
            if (string.IsNullOrWhiteSpace(regionIds)) return "";

            var regionList = regionIds
                .Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                .Select(r => r.Trim())
                .Where(r => !string.IsNullOrWhiteSpace(r))
                .ToList();

            if (regionList.Count == 0) return "";

            var conditions = regionList
                .Select(region => $"other_region LIKE {region} || '%'")
                .ToList();

            return " AND (" + string.Join(" OR ", conditions) + ")";
        }

        [HttpPost]
        public async Task<object> GetRedPacketBalance([FromBody] GetRedPacketDetailParameter parameter)
        {
            var _sql_queue = new SQLQueue(cmd);
            int pageSize = parameter.pageSize;
            int pageNo = parameter.pageNo;
            string groupId = parameter.group_id;
            string regionId = parameter.region_id;
            string supcustId = parameter.supcust_id;
            string operKey = parameter.operKey;
            var searchCondi = "";
            if (!groupId.IsInvalid())
            {
                searchCondi = " and  group_id in (" + groupId + ")";
            }
            if (!regionId.IsInvalid())
            {
                searchCondi += BuildRegionPrefixMatchSqlList(regionId);
            }
            if (!supcustId.IsInvalid())
                if (searchCondi != "") searchCondi += " and isu.supcust_id in (" + supcustId + ")";
                else searchCondi = " and isu.supcust_id in (" + supcustId + ")";
            StringBuilder condi = new StringBuilder();
            Security.GetInfoFromOperKey(operKey, out string companyId);

            // 添加总数查询
            string countSql = $@"
                SELECT COUNT(*)
                FROM red_packet_balance rb 
                LEFT JOIN info_supcust isu 
                    ON rb.company_id = isu.company_id 
                    AND rb.supcust_id = isu.supcust_id
                LEFT JOIN info_supcust_group isg 
                    ON isu.company_id = isg.company_id 
                    AND isu.sup_group = isg.group_id
                LEFT JOIN info_region ir 
                    ON isu.company_id = ir.company_id 
                    AND isu.region_id = ir.region_id
                WHERE rb.company_id = {companyId} {searchCondi}";

            _sql_queue.Enqueue("count", countSql);
            int totalCount = 0;
            var countQueue = new SQLQueue(cmd);
            countQueue.Enqueue("count", countSql);
            var dr = await countQueue.ExecuteReaderAsync();
            if (await dr.ReadAsync())
                totalCount = Convert.ToInt32(dr[0]);
            dr.Close();
            countQueue.Clear();
            _sql_queue.Clear();

            string sql = $@"
                select
                    sup_name,
                    region_name,
                    group_name,
                    balance
                from red_packet_balance rb left join info_supcust isu on rb.company_id=isu.company_id and rb.supcust_id=isu.supcust_id
                    left join info_supcust_group isg on isg.company_id=isu.company_id and isg.group_id=isu.sup_group
                    left join info_region ir on isg.company_id=ir.company_id and ir.region_id=isu.region_id
                WHERE
                    rb.company_id = {companyId} {searchCondi}
                ORDER BY create_time DESC
                LIMIT {pageSize} 
                OFFSET {(pageNo - 1) * pageSize};
            ";

            _sql_queue.Enqueue("records", sql);
            Dictionary<int, Dictionary<string, string>> rows = new Dictionary<int, Dictionary<string, string>>();
            var recordsResult = await PageBaseModel.GetRecordsFromQQ(_sql_queue, cmd, 0, null);
            foreach (var k in recordsResult)
            {
                rows = k.Value;
            }
            List<GetRedPacketBalanceResultModel> result = new List<GetRedPacketBalanceResultModel>();

            foreach (var row in rows.Values)
            {
                result.Add(new GetRedPacketBalanceResultModel
                {
                    sup_name = row["sup_name"]?.ToString() ?? "",
                    region_name = row["region_name"]?.ToString() ?? "",
                    group_name = row["group_name"]?.ToString() ?? "",
                    balance = row["balance"]?.ToString() ?? ""
                });

            }
            return Json(new { result = "OK", msg = "", data = result, itemCount=totalCount });
        }
        [HttpPost]
        public async Task<object> GetQueryRecords_Group([FromBody] dynamic data)
        {
            var _sql_queue = new SQLQueue(cmd);
            string operKey = data.operKey;
            string group_name = data.group_name;
            Security.GetInfoFromOperKey(operKey, out string companyId);
            string sql = $@"
                SELECT
                    group_id,group_name
                FROM
                    info_supcust_group
                WHERE
                    company_id = {companyId} and group_name like '%{group_name}%';
            ";
            _sql_queue.Enqueue("records", sql);
            Dictionary<int, Dictionary<string, string>> rows = new Dictionary<int, Dictionary<string, string>>();
            var recordsResult = await PageBaseModel.GetRecordsFromQQ(_sql_queue, cmd, 0, null);
            foreach (var k in recordsResult)
            {
                    rows = k.Value;
            }
            data = new { result = "OK", rows };
            return new JsonResult(data);
        }
        [HttpPost]
        public async Task<object> GetQueryRecords_Region([FromBody] dynamic data)
        {
            var _sql_queue = new SQLQueue(cmd);
            dynamic regionList = null;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyId);
            string sql = $@"
                SELECT id.region_id AS value,
                       id.mother_id AS parentId, 
                       id.region_id AS key, 
                       id.region_name AS label
                FROM info_region id
                WHERE id.company_id = {companyId}
            ";
            _sql_queue.Enqueue("records", sql);
            dynamic list = await CDbDealer.GetRecordsFromSQLAsync<TreeData_Common>(sql, cmd);
            regionList = TreeUtil.TreeDataToTree_Common(list);
            Dictionary<string, dynamic> result = new Dictionary<string, dynamic>();
            result.Add("regionList", regionList);
            return Json(new ResultUtil<dynamic>().CommonResult(0, "success", result));
        }
        [HttpPost]
        public async Task<object> GetQueryRecords_Brand([FromBody] dynamic data)
        {
            var _sql_queue = new SQLQueue(cmd);
            string operKey = data.operKey;
            string brand_name = data.brand_name;
            Security.GetInfoFromOperKey(operKey, out string companyId);
            string sql = $@"
                SELECT
                    brand_id,brand_name
                FROM
                    info_item_brand
                WHERE
                    company_id = {companyId} and brand_name like '%{brand_name}%';
            ";
            _sql_queue.Enqueue("records", sql);
            Dictionary<int, Dictionary<string, string>> rows = new Dictionary<int, Dictionary<string, string>>();
            var recordsResult = await PageBaseModel.GetRecordsFromQQ(_sql_queue, cmd, 0, null);
            foreach (var k in recordsResult)
            {
                rows = k.Value;
            }
            data = new { result = "OK", rows };
            return new JsonResult(data);
        }
        [HttpPost]
        public async Task<object> GetQueryRecords_Class([FromBody] dynamic data)
        {
            var _sql_queue = new SQLQueue(cmd);
            dynamic classList = null;
            string operKey = data.operKey;
            string class_name = data.class_name;
            Security.GetInfoFromOperKey(operKey, out string companyId);
            string sql = $@"
                SELECT ic.class_id AS value,
                       ic.mother_id AS parentId, 
                       ic.class_id AS key, 
                       ic.class_name AS label
                FROM info_item_class ic
                WHERE ic.company_id = {companyId}
            ";
            _sql_queue.Enqueue("records", sql);
            dynamic list = await CDbDealer.GetRecordsFromSQLAsync<TreeData_Common>(sql, cmd);
            classList = TreeUtil.TreeDataToTree_Common(list);
            Dictionary<string, dynamic> result = new Dictionary<string, dynamic>();
            result.Add("classList", classList);
            return Json(new ResultUtil<dynamic>().CommonResult(0, "success", result));
        }
        [HttpPost]
        public async Task<object> GetQueryRecords_Sup([FromBody] dynamic data)
        {
            var _sql_queue = new SQLQueue(cmd);
            string operKey = data.operKey;
            string sup_name = data.supcust_name;
            Security.GetInfoFromOperKey(operKey, out string companyId);
            string sql = $@"
                select supcust_id,sup_name,sup_group,isu.region_id,region_name,group_name 
                from info_supcust isu
                left join info_region ir on isu.company_id=ir.company_id and isu.region_id=ir.region_id
                left join info_supcust_group isg on isu.company_id=isg.company_id and isu.sup_group=isg.group_id
                WHERE
                    isu.company_id = {companyId} and sup_name like '%{sup_name}%';
            ";
            _sql_queue.Enqueue("records", sql);
            Dictionary<int, Dictionary<string, string>> rows = new Dictionary<int, Dictionary<string, string>>();
            var recordsResult = await PageBaseModel.GetRecordsFromQQ(_sql_queue, cmd, 0, null);
            foreach (var k in recordsResult)
            {
                rows = k.Value;
            }
            data = new { result = "OK", rows };
            return new JsonResult(data);
        }
        [HttpPost]
        public async Task<object> GetQueryRecords_Goods([FromBody] dynamic data)
        {
            var _sql_queue = new SQLQueue(cmd);
            string operKey = data.operKey;
            string item_name = data.item_name ?? "";
            string item_id = data.item_id ?? "";
            int startRow = data.startRow ?? 0;
            int endRow = data.endRow ?? 199;  // 默认取200条
            Security.GetInfoFromOperKey(operKey, out string companyId);
            var searchCondi = "";
            if (!item_id.IsInvalid())
            {
                searchCondi = " and  iip.item_id in (" + item_id + ")";
            }

            // 计算总记录数
            string countSql = $@"
                SELECT COUNT(*)
                FROM info_item_prop iip 
                LEFT JOIN info_item_multi_unit iimu 
                    ON iip.item_id = iimu.item_id 
                    AND iip.company_id = iimu.company_id 
                LEFT JOIN info_item_class ic 
                    ON iip.item_class = ic.class_id 
                    AND iip.company_id = ic.company_id
                LEFT JOIN info_item_brand ib 
                    ON iip.item_brand = ib.brand_id 
                    AND iip.company_id = ib.company_id
                WHERE iimu.unit_type = 's' 
                    AND iip.company_id = {companyId}
                    AND item_name LIKE '%{item_name}%'
                    {searchCondi}";

            _sql_queue.Enqueue("count", countSql);
            
            // 使用startRow和endRow进行分页查询
            string sql = $@"
                SELECT 
                    iip.item_id,
                    item_name,
                    item_spec,
                    cost_price_avg,
                    iimu.buy_price,
                    class_name,
                    brand_name
                FROM info_item_prop iip 
                LEFT JOIN info_item_multi_unit iimu 
                    ON iip.item_id = iimu.item_id 
                    AND iip.company_id = iimu.company_id 
                LEFT JOIN info_item_class ic 
                    ON iip.item_class = ic.class_id 
                    AND iip.company_id = ic.company_id
                LEFT JOIN info_item_brand ib 
                    ON iip.item_brand = ib.brand_id 
                    AND iip.company_id = ib.company_id
                WHERE iimu.unit_type = 's'
                    AND iip.company_id = {companyId}
                    AND item_name LIKE '%{item_name}%'
                    {searchCondi}
                ORDER BY iip.item_id
                LIMIT {endRow - startRow + 1} 
                OFFSET {startRow}";

            _sql_queue.Enqueue("records", sql);

            var rows = new Dictionary<int, Dictionary<string, string>>();
            var totalCount = 0;

            var dr = await _sql_queue.ExecuteReaderAsync();
            while (_sql_queue.Count > 0)
            {
                var sqlName = _sql_queue.Dequeue();
                if (sqlName == "count")
                {
                    if (dr.Read())
                    {
                        totalCount = Convert.ToInt32(dr[0]);
                    }
                }
                else if (sqlName == "records")
                {
                    var recordsList = CDbDealer.GetRecordsFromDr(dr, false);
                    rows = new Dictionary<int, Dictionary<string, string>>();
                    int index = 0;
                    foreach (dynamic record in recordsList)
                    {
                        var dict = new Dictionary<string, string>();
                        foreach (var prop in record as IDictionary<string, object>)
                        {
                            dict[prop.Key] = prop.Value?.ToString() ?? "";
                        }
                        rows[index++] = dict;
                    }
                }
            }
            dr.Close();
            _sql_queue.Clear();

            return new
            {
                result = "OK",
                total = totalCount,
                rows = rows
            };
        }
        #endregion

        #region Public: 保存红包 (For app post)（正常启用-新增）
        [HttpPost]
        public async Task<CallResult> SaveRedPacket([FromBody] dynamic data)
        {
            try
            {
                string operKey = data.operKey;
                Security.GetInfoFromOperKey(operKey, out string company_id);
                
                // 获取请求参数并处理可能的空值
                string red_packet_name = data.red_packet_name ?? "";
                string red_packet_amount = data.red_packet_amount ?? "0";
                string red_packet_type = data.red_packet_type ?? "";
                string amount_type = data.amount_type ?? "";
                decimal reach_cost = Convert.ToDecimal(data.reach_cost ?? 0);
                string restrict_range = data.restrict_range ?? new {};
                string restrict_goods = data.restrict_goods ?? new {};
                string restrict_time = data.restrict_time ?? "{}";
                int red_packet_num = Convert.ToInt32(data.red_packet_num ?? 0);
                int red_packet_max_num = Convert.ToInt32(data.red_packet_max_num ?? 1);
                string red_packet_sum = data.red_packet_sum ?? "0";
                int status = Convert.ToInt32(data.status ?? 0);

                string sql;
                if (data.red_packet_id.ToString() != "")
                {
                    // 更新现有记录
                    sql = $@"
                        UPDATE sheet_red_packet_plan
                        SET 
                            red_packet_name = '{red_packet_name}',
                            red_packet_amount = '{red_packet_amount}',
                            red_packet_type = '{red_packet_type}',
                            amount_type = '{amount_type}',
                            reach_cost = '{reach_cost}',
                            restrict_range = '{restrict_range}'::jsonb,
                            restrict_goods = '{restrict_goods}'::jsonb,
                            restrict_time = '{restrict_time}'::jsonb,
                            red_packet_num = '{red_packet_num}',
                            red_packet_max_num = '{red_packet_max_num}',
                            red_packet_sum = '{red_packet_sum}',
                            status = '{status}'
                        WHERE 
                            company_id = '{company_id}' 
                            AND red_packet_id = '{data.red_packet_id}'
                    ";
                }
                else
                {
                    // 插入新记录
                    sql = $@"
                        INSERT INTO sheet_red_packet_plan
                        (
                            company_id, red_packet_name, red_packet_amount, red_packet_type,
                            amount_type, reach_cost, restrict_range, restrict_goods, restrict_time,
                            red_packet_num, red_packet_max_num, red_packet_sum, status
                        )
                        VALUES
                        (
                            '{company_id}', 
                            '{red_packet_name}',
                            '{red_packet_amount}',
                            '{red_packet_type}',
                            '{amount_type}',
                            '{reach_cost}',
                            '{restrict_range}'::jsonb,
                            '{restrict_goods}'::jsonb,
                            '{restrict_time}'::jsonb,
                            '{red_packet_num}',
                            '{red_packet_max_num}',
                            '{red_packet_sum}',
                            '{status}'
                        )
                        RETURNING red_packet_id;
                    ";
                }

                cmd.CommandText = sql;
            
                
                if (!string.IsNullOrEmpty(Convert.ToString(data.red_packet_id)))
                {
                    cmd.Parameters.AddWithValue("@red_packet_id", Convert.ToInt32(data.red_packet_id));
                }
                object red_packet_id;
                if (data.red_packet_id.ToString() != "")
                {
                    await cmd.ExecuteScalarAsync();
                    red_packet_id = data.red_packet_id;
                }
                else
                {
                    red_packet_id = await cmd.ExecuteScalarAsync();
                }
                
                return new CallResult("OK", "", "red_packet_id:"+red_packet_id?.ToString());
            }
            catch (Exception ex)
            {
                NLogger.Error("[SaveRedPacket] 保存红包失败");
                NLogger.Error(ex.ToString());
                return new CallResult("Error", ex.Message);
            }
        }
        #endregion

        #region Public: 发放红包 (For app post)（正常启用-新增）
        [HttpPost]
        public async Task<CallResult> UploadRedPacket([FromBody] dynamic data)
        {
            try
            {
                string operKey = data.operKey;
                Security.GetInfoFromOperKey(operKey, out string company_id);
                
                // 获取请求参数并处理可能的空值
                int status = Convert.ToInt32(data.status ?? 0);

                string sql;
                sql = $@"
                    UPDATE sheet_red_packet_plan
                    SET 
                        status = '{status}',
                        upload_time = '{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}'
                    WHERE 
                        company_id = '{company_id}' 
                        AND red_packet_id = '{data.red_packet_id}'
                ";

                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
                return new CallResult("OK", "","");
            }
            catch (Exception ex)
            {
                NLogger.Error("[SaveRedPacket] 发放红包失败");
                NLogger.Error(ex.ToString());
                return new CallResult("Error", ex.Message);
            }
        }
        #endregion
    }
}
