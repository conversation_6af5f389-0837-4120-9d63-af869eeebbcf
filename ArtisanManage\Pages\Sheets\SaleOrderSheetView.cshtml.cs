﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using CSRedis;
using HuaWeiObsController;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.SS.UserModel.Charts;
using static ArtisanManage.Models.DataItem;


namespace ArtisanManage.Pages.BaseInfo
{
    public class SaleOrderSheetViewModel : PageQueryModel
    {
        public bool ForSelect;

        public SaleOrderSheetViewModel(CMySbCommand cmd) : base(Services.MenuId.sheetSaleOrder)
        {
            this.cmd = cmd;
            cmd.ActiveDatabase = "";
            this.PageTitle = "销售订单";
            CanQueryByApproveTime = true;

            DataItems = new Dictionary<string, DataItem>()
            {
                {"queryTimeAccord",new DataItem(){FldArea="divHead",Title="时间类型",LabelFld = "time_status_name",ButtonUsage = "list",CompareOperator="=",Value="byApproveTime",Label="审核时间",ForQuery=false, AutoRemember=true,
                        Source = @"[{v:'byApproveTime',l:'审核时间'},
                                   {v:'byHappenTime',l:'交易时间'},
                                   {v:'byMakeTime',l:'制单时间'},
                                   {v:'byCheckedTime',l:'交账时间'},
                                    {v:'bySendTime',l:'送货时间'},
                                    {v:'byPayTime',l:'在线支付时间'},
                                     ]"
                }},
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="sm.happen_time", CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期", FldArea="divHead",CtrlType="jqxDateTimeInput", SqlFld="sm.happen_time",   CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"depart_path",new DataItem(){Title="部门",Hidden=true, FldArea="divHead",LabelFld="depart_path_label", CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=true,DropDownWidth="150", CompareOperator="like",LikeWrapper="/",
                    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"
                }},
                {"sheet_type",new DataItem(){SqlFld="sm.sheet_type", FldArea="divHead",Title="单据类型",LabelFld="sheet_type_name",ButtonUsage="list",Source = "[{v:'XD',l:'销售订单'},{v:'TD',l:'退货订单'},{v:'',l:'所有'}]",CompareOperator="="}},
                //{"sup_name",new DataItem(){FldArea="divHead",Title="客户名称", CompareOperator="like"}},

                {"sheet_usage",new DataItem(){SqlFld="sm.sheet_usage", FldArea="divHead",Title="单据用途",ButtonUsage="list",
                    Source = "[{v:'D',l:'正常销售'},{v:'LQ',l:'处理临期'},{v:'T',l:'退货'},{v:'HH',l:'换货'},{v:'DH',l:'还定货'},{v:'CL',l:'陈列兑付'}]",CompareOperator="="}},

                {"supcust_id",CommonTool.GetDataItem("supcust_id",new DataItemChange{Checkboxes = true,SqlFld = "sm.supcust_id"}) },

                {"group_id",new DataItem(){Title="渠道",Checkboxes=true,FldArea="divHead", LabelFld="group_name",ButtonUsage="list",CompareOperator="=",SqlFld="s.sup_group", Hidden=true,
                    SqlForOptions ="select group_id as v,group_name as l from info_supcust_group"}},

                {"seller_id",new DataItem(){FldArea="divHead",Title="业务员",Checkboxes=true,LabelFld="seller_name",ButtonUsage="list", SqlForOptions=CommonTool.selectSellers, CompareOperator="="}},//SqlForOptions="select oper_id as v,oper_name as l,py_str as z from info_operator"
                 {
                    "senders_id",
                    new DataItem()
                    {
                        FldArea = "divHead", Title = "送货员",SqlFld = "','||COALESCE(oss.senders_id, sm.senders_id)||','", LabelFld = "sm.senders_name", ButtonUsage = "list",
                        DealQueryItem = status => ","+status+",",
                        SqlForOptions=CommonTool.selectSenders,  //SqlForOptions = "select oper_id as v,oper_name as l,py_str as z from info_operator",
                        CompareOperator = "like"
                    }
                },
                {"other_region",new DataItem(){Title="片区",MumSelectable=true, FldArea="divHead",LabelFld="region_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MaxRecords="500", TreePathFld="region_path",CompareOperator="like",Checkboxes=false,
                    SqlForOptions="select region_id as v,region_name as l,mother_id as pv from info_region order by order_index , region_id",
                    DealQueryItem = (value) =>
                    {
                        return "/"+value+"/";
                    }
                }},
                {"branch_id",new DataItem(){Title="仓库",FldArea="divHead",Checkboxes=true, LabelFld="branch_name",ButtonUsage="list",CompareOperator="=",SqlFld="sm.branch_id",SqlForOptions ="select branch_id as v,branch_name as l from info_branch"}},
                {"sheet_no", new DataItem(){Title = "单据号", FldArea="divHead" ,CompareOperator="like"
                }},

                {"status",new DataItem(){FldArea="divHead",Title="单据状态",Checkboxes=true, LabelFld = "status_name",ButtonUsage = "list",CompareOperator="=",Value="normal",Label="正常单据",
                    Source = @"[{v:'normal',l:'正常单据',condition:""sm.red_flag is null""},
                             {v:'unapproved',l:'未审核单据',condition:""sm.approve_time is null""},
                             {v:'approved',l:'已审核单据',condition:""sm.approve_time is not null and sm.red_flag is null""},
                             {v:'review',l:'已复核单据',condition:""sm.review_time is not null and sm.red_flag is null""},
                             {v:'unreview',l:'未复核单据',condition:""sm.review_time is null and sm.red_flag is null""},
                             {v:'deleted',l:'已删除单据',condition:""coalesce(sm.is_del, false) = true""},
                             {v:'red',l:'红冲单',condition:""sm.red_flag in ('1','2')""},
                             {v:'all',l:'所有',condition:""true""}]"

				}},
                {"brand_id",new DataItem(){Title="品牌",FldArea="divHead",Checkboxes=true,LabelFld="brand_name",ButtonUsage="list",CompareOperator="=",ForQuery=false,DropDownWidth="300",QueryByLabelLikeIfIdEmpty=true,
                    SearchFields="['l']",SqlForOptions = CommonTool.selectBrands}},


                {"item_class",new DataItem(){Title="类别",FldArea="divHead",LabelFld="class_name",CtrlType="jqxDropDownTree",MumSelectable=true,CompareOperator="like",ForQuery=false,
                   SqlForOptions=CommonTool.selectClasses,MaxRecords="500"}},

                 {"item_id",new DataItem(){Title="商品",FldArea="divHead",LabelFld="item_name",ButtonUsage="event",CompareOperator="=",ForQuery=false,DropDownWidth="300",QueryByLabelLikeIfIdEmpty=true,
                    SearchFields=CommonTool.itemSearchFields, Hidden=true,
                SqlForOptions =CommonTool.selectItemWithBarcode  }},
                {"make_brief",new DataItem(){FldArea="divHead",Title="备注", CompareOperator="like"}},
                {"remark",new DataItem(){FldArea="divHead",Title="行备注", CompareOperator="like",Hidden=true,ForQuery=false}},

                {"transfer_status",new DataItem(){FldArea="divHead",Title="转单情况",LabelFld = "transfer_status_name",ButtonUsage = "list",CompareOperator="=",
                    Source = @"[{v:'transfered',l:'已转单',condition:""oss.order_status = 'zd'and sale_approve_time is not null""},
                                    {v:'untransfered',l:'未转单',condition:""(oss.order_status <>'zd'or oss.order_status is null) and sale_sheet_no is null""},
                                    {v:'transfered_unapproved',l:'已转单（未审核）',condition:""(oss.order_status = 'zd'or oss.order_status is null) and sale_sheet_no is not null and sale_approve_time is null""},
                                    {v:'all',l:'所有',condition:""true""}]"

                }},
                {"print_count",new DataItem(){FldArea="divHead",Title="打单情况",QueryOnChange=true,LabelFld = "print_status_name",ButtonUsage = "list",CompareOperator="=",
                    Source = @"[{v:'printed',l:'已打单',condition:""oss.sheet_print_count >0""},
                                 {v:'unprinted',l:'未打单',condition:""(oss.sheet_print_count =  0 or oss.sheet_print_count is null)""},
                                 {v:'all',l:'所有',condition:""true""}]"

                }},
                {"load_status", new DataItem(){
                    FldArea = "divHead",
                    Title = "是否装车",
                    QueryOnChange = true,
                    LabelFld = "load_status_name",
                    ButtonUsage = "list",
                    CompareOperator = "=",
                    Source = @"[
                        {v:'loaded', l:'已装车', condition:""oss.van_id IS NOT NULL""},
                        {v:'unloaded', l:'未装车', condition:""(oss.van_id IS NULL)""}
                    ]"
                }},
                {"cost_price_type",new DataItem(){FldArea="divHead",Title="成本核算",ForQuery=false,LabelFld="cost_price_type_name",ButtonUsage="list",Source = "[{v:'3',l:'预设进价'},{v:'2',l:'加权平均价'},{v:'1',l:'预设成本'},{v:'4',l:'最近平均进价'}]", CompareOperator="=" }},
                {"work_brief_",new DataItem(){FldArea="divHead",Title="工作说明", CompareOperator="like",
                    ButtonUsage = "list", 
                    QueryByLabelLikeIfIdEmpty=true,
                    KeepNoValueLabel=true,
                    Source = @"[{v:'无',l:'无',condition:""(work_brief_ is null or work_brief_='')""},
                               {v:'有',l:'有',condition:""(work_brief_ is not null and work_brief_<>'')""}]"
                }},
                {"sender_id_to_set",new DataItem(){Title="送货员",Checkboxes=true, LabelFld="sender_id_to_set_name",ButtonUsage="list",SqlForOptions=CommonTool.selectSenders,ForQuery=false}},//SqlForOptions=
                {"order_source",new DataItem(){FldArea="divHead",Title="来源",QueryOnChange=true,LabelFld = "order_source_name",ButtonUsage = "list",CompareOperator="=",
                    Source = @"[{v:'offline',l:'线下',condition:""sm.order_source is null""},
                                 {v:'online',l:'小程序',condition:""(sm.order_source =  'xcx')""},
                                 {v:'all',l:'所有',condition:""true""}]"

                }},
                {"pay_status",new DataItem(){FldArea="divHead",Title="在线支付",QueryOnChange=true,LabelFld = "pay_status_name",ButtonUsage = "list",CompareOperator="=",Value="normal",
                    Source = @"[{v:'paid',l:'已支付',condition:""(pb.bill_status = 'paid')""},
                                {v:'toPay',l:'未支付',condition:""(pb.bill_status = 'toPay' or pb.bill_status = 'cancel')""},
                                {v:'return',l:'已退款',condition:""(pb.bill_status = 'return')""},
                                {v:'offline',l:'货到付款',condition:""(pb.bill_status is null)""},
                                {v:'normal',l:'正常',condition:""(pb.bill_status is null or pb.bill_status = 'paid')""},
                                {v:'all',l:'所有',condition:""true""}]"
                }},
                {"pay_time", new DataItem() {Title="在线支付时间",FldArea="divHead",CtrlType="jqxDateTimeInput",SqlFld="pb.pay_time",CompareOperator=">=",Value="",ForQuery=true}},
            };

            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true,
                     Sortable=true,
                     IdColumn="sheet_id",
                     HasCheck=true,
                     PageByOverAgg=false,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"sm.sheet_id",   new DataItem(){Title="sheet_id", Hidden=true, HideOnLoad =true,Linkable=true, Width="80"}},
                       {"sheet_status", new DataItem(){Title="状态",  Width="80",SqlFld="(case when sm.red_flag='2' then '红字单' when sm.red_flag='1' then '已红冲' when sm.is_del = true then '已删除' when sm.approve_time is null then '未审' when sm.review_time is null then '已审'  else '已复核' END)"}},
                       {"sheet_type", new DataItem(){Title="单据类型",  Width="100",SqlFld="(case WHEN sm.sheet_type='XD' THEN '销售订单' ELSE '退货订单' END)"}},
                       {"order_source", new DataItem(){Title="来源",Hidden=true,Width="80",SqlFld="(case when sm.order_source='xcx' then '小程序' else '线下' end)"}},
                       {"seller_name", new DataItem(){Title="业务员",  Width="100",Sortable=true}},
                       {"senders_name",    new DataItem(){Title="送货员", Sortable=true,     Width="100",SqlFld=" COALESCE(oss.senders_name, sm.senders_name) "}},
                       {"maker_name", new DataItem(){Title="制单人",  Width="80",Sortable=true}},
                       {"branch_name", new DataItem(){Title="仓库",  Width="100"}},
                       //{"bj",new DataItem(){Title="变价",Hidden=true,SqlFld="sheet_attribute->>'bj'",Width = "80"} },
                       //{"j",new DataItem(){Title="借货",Hidden=true,HideOnLoad=true, SqlFld="sheet_attribute->>'j'" } },
                       //{"h",new DataItem(){Title="还货",Hidden=true,HideOnLoad=true, SqlFld="sheet_attribute->>'h'" } },
                       //{"sheet_no",   new DataItem(){Title="单据编号",  Linkable=true, Width="170",Sortable=true,
                       //JSCellRender=@"function (row, column, value,p4,p5,rowData) { 
                       //             let bj=rowData.bj;let j = rowData.j;let h = rowData.h;
                       //             let labels="""";
                       //             if(bj)  labels += `<label style = ""margin-right:3px;background:#e6214a;color:white;border-radius:5px;width:16px;font-size:10px;text-align:center;line-height:16px"" >变</label>`;
                       //             if(j)  labels += `<label style = ""margin-right:3px;background:#e6214a;color:white;border-radius:5px;width:16px;font-size:10px;text-align:center;line-height:16px"" >借</label>`;
                       //             if(h)  labels += `<label style = ""margin-right:3px;background:#e6214a;color:white;border-radius:5px;width:16px;font-size:10px;text-align:center;line-height:16px"" >还</label>`;
                                    
                       //             let html = `<div style = ""height:100%;display:flex;align-items:center;justify-content:flex-start;"" ><label style=""cursor:pointer;margin-left:4px;color:#49f;margin-right:2px"">${value}</label>${labels}</div>`
                       //             return html;
                       // }"

                       //}},       
                       {"bj",new DataItem(){Title="变价",Hidden=true,SqlFld="(case when lower(coalesce(sheet_attribute->>'bj',''))='true' then '是' else '' end)", Width="80" } },
                       {"forReturn",new DataItem(){Title="退货",Hidden=true,HideOnLoad=true,SqlFld="sheet_attribute->>'forReturn'" } },
                       {"free",new DataItem(){Title="赠品",Hidden=true,HideOnLoad=true, SqlFld="sheet_attribute->>'free'" } },
                       {"j",new DataItem(){Title="借货",Hidden=true,HideOnLoad=true, SqlFld="sheet_attribute->>'j'" } },
                       {"h",new DataItem(){Title="还货",Hidden=true,HideOnLoad=true, SqlFld="sheet_attribute->>'h'" } },
                       {"dh",new DataItem(){Title="定货会",Hidden=true,HideOnLoad=true, SqlFld="sheet_attribute->>'dh'" } },
                       {"cl",new DataItem(){Title="陈列",Hidden=true,HideOnLoad=false, SqlFld="sheet_attribute->>'cl'" } },
                       {"sheet_no",   new DataItem(){Title="单据编号",  Linkable=true, Width="170",Sortable=true,
                       JSCellRender=@"function (row, column, value,p4,p5,rowData) { 
                                    let bj=rowData.bj;let forReturn = rowData.forreturn;let free = rowData.free;let j = rowData.j;let h = rowData.h;let cl = rowData.cl;let dh = rowData.dh;
                                    let labels="""";
                                    if(bj)  labels += `<label style = ""margin-right:3px;background:#e6214a;color:white;border-radius:5px;width:16px;font-size:10px;text-align:center;line-height:16px"" >变</label>`;
                                    if(forReturn)  labels += `<label style = ""margin-right:3px;background:#e6214a;color:white;border-radius:5px;width:16px;font-size:10px;text-align:center;line-height:16px"" >退</label>`;
                                    if(free && !j && !h)  labels += `<label style = ""margin-right:3px;background:#e6214a;color:white;border-radius:5px;width:16px;font-size:10px;text-align:center;line-height:16px"" >赠</label>`;
                                    if(j)  labels += `<label style = ""margin-right:3px;background:#e6214a;color:white;border-radius:5px;width:16px;font-size:10px;text-align:center;line-height:16px"" >借</label>`;
                                    if(h)  labels += `<label style = ""margin-right:3px;background:#e6214a;color:white;border-radius:5px;width:16px;font-size:10px;text-align:center;line-height:16px"" >还</label>`;
                                    if(dh)  labels += `<label style = ""margin-right:3px;background:#e6214a;color:white;border-radius:5px;width:32px;font-size:10px;text-align:center;line-height:16px"" >还定</label>`;
                                    if(cl)  labels += `<label style = ""margin-right:3px;background:#e6214a;color:white;border-radius:5px;width:16px;font-size:10px;text-align:center;line-height:16px"" >陈</label>`;
                                    let html = `<div style = ""height:100%;display:flex;align-items:center;justify-content:flex-start;"" ><label style=""cursor:pointer;margin-left:4px;color:#49f;margin-right:2px"">${value}</label>${labels}</div>`
                                    return html;
                        }"

                       }},
                        {"placeholder_sheet_id",   new DataItem(){Title="关联占位单", Hidden=true, HideOnLoad =true,Linkable=true, Width="80"}},
                        {"placeholder_sheet_no",   new DataItem(){Title="关联占位单",Linkable=true, Width="200"}},
                       {"sup_name",   new DataItem(){Title="客户",   Width="200",Sortable=true,IsChinese=true}},
                       {"group_name",   new DataItem(){Title="渠道",  Width="120",SqlFld="scg.group_name",Sortable=true,Hidden=true}},
                       {"mobile",   new DataItem(){Title="客户电话",   Width="200",Sortable=true,Hidden=true}},

                       {"print_count", new DataItem(){Title="打印次数",  Width="40", Sortable=true,


                         FuncGetSubColumns = (col) =>
                            {
                                var result = new ColumnsResult();
                                var subColumns = new Dictionary<string, DataItem>();
                                var subKeyValuePairs = new Dictionary<string, string> { {"sheet_print_count", "印"}, {"sum_print_count", "汇"}};
                                var flds = "";
                                foreach (var key in subKeyValuePairs.Keys)
                                {
                                    subColumns.Add(key, new DataItem { Width = col.Width,Sortable=true,Title = subKeyValuePairs[key], CellsAlign = "center" });
                                    var fld = key;
                                    if(flds != "") flds += ",";
                                    flds += fld;
                                }
                                result.Columns = subColumns;
                                result.FldsSQL = flds;
                                return Task.FromResult(result);
                         }
                       }},
                       {"happen_time",   new DataItem(){Title="交易时间",   Width="160"}},
                       {"approve_time",   new DataItem(){Title="审核时间",   Width="160"}},
                       {"make_time",   new DataItem(){Title="制单时间",Hidden=true,    Width="160"}},
                       {"send_time",   new DataItem(){Title="送货时间",Hidden=true,    Width="160"}},
                       {"pay_time", new DataItem(){Title="在线支付时间",Width="160" }},
                       {"pay_status",new DataItem(){Title="在线支付",SqlFld = "(case when pb.bill_status = 'paid' then '已支付' when pb.bill_status = 'toPay'or pb.bill_status = 'cancel' then '未支付' when pb.bill_status = 'return' then '已退款' when pb.bill_status is null then '货到付款' else '' end)",UseJQWidgets=false, Width="100"}},
                       {"sale_sheet_id",   new DataItem(){Title="", Hidden=true,SqlFld="to_sale_sheet_id",HideOnLoad=true}},

                       {"sale_sheet_no", new DataItem()
                       {
                           Title = "销售/退货单编号", Width = "150", Linkable = true, CellsAlign = "center",
                           SqlFld = "(case when sale_sheet_no is null and oss.receipt_status = 'js' then '' when sale_sheet_no is null and sm.sheet_type = 'XD' and approve_time is not null and sm.red_flag is null then '转销售单' when sale_sheet_no is null and sm.sheet_type = 'TD'  and approve_time is not null and sm.red_flag is null then '转退货单' else sale_sheet_no end)"
                       }},
					  {"receipt_status", new DataItem()
					   {
						   Title = "签收状态", Width = "100",Hidden=true, CellsAlign = "center",
						   SqlFld = "case when oss.receipt_status = 'zd' then '已签收' when  oss.receipt_status = 'bf' then '部分签收' when  oss.receipt_status = 'js' then '拒收' end"
					   }},
					   {"total_quantity", new DataItem(){Title="总数量", Width="100",CellsAlign="right"}},
                       {"total_amount", new DataItem(){Title="总额", SqlFld="total_amount*money_inout_flag", Width="100",CellsAlign="right",ShowSum=true}},
                       {"now_pay_amount", new DataItem(){Title="支付",SqlFld="now_pay_amount*money_inout_flag", Width="100",CellsAlign="right",ShowSum=true}},
                       {"arrears", new DataItem(){Title="欠款", Width="100",SqlFld="(total_amount-now_pay_amount-now_disc_amount)*money_inout_flag",CellsAlign="right",ShowSum=true}},
                       {"pay_ways",  new DataItem(){Title="支付方式", Width="80",
                           FuncGetSubColumns = async (col) =>
                           {
                                ColumnsResult result=new ColumnsResult();
                                Dictionary<string,DataItem> subColumns=new Dictionary<string,DataItem>();
                                List<System.Dynamic.ExpandoObject> payways =  await CDbDealer.GetRecordsFromSQLAsync($"select sub_id,sub_name from cw_subject where company_id={company_id} and sub_type in ('QT','YS') and is_order is not true order by sub_code::text;",cmd);
                                string flds="";
                                foreach(dynamic pw in payways)
                                {
                                   var subcol=new DataItem();
                                   subcol.Width=col.Width;
                                   subcol.Title=pw.sub_name;
                                   subcol.CellsAlign="right";
                                   subcol.ShowSum = true;
                                   string colKey="pw_"+ (string)pw.sub_id;
                                   subColumns.Add(colKey, subcol);
                                   string fld=$" case when payway1_id='{pw.sub_id}' then payway1_amount*money_inout_flag when payway2_id='{pw.sub_id}' then payway2_amount*money_inout_flag else null end as {colKey}";
                                   if(flds!="") flds+=",";
                                   flds+=fld;
                                }
                                result.FldsSQL=flds;
                                result.Columns=subColumns;
                                return result;
                           }
                       }} ,
					 {"print_time", new DataItem(){Title="最后打印时间",Hidden=true, Width="150"}},
					 {"make_brief", new DataItem(){Title="备注", Width="100"}},
					 {"work_brief",new DataItem(){Title="工作说明",Width="70",Hidden=true}},

					 {"appendix_photos", new DataItem(){Title="附件", Width="100",SqlFld="sheet_attribute->>'appendixPhotos'",FuncDealMe = jarray =>
                           {
                               if (jarray != "")
                               {
                                   try
                                   {
                                        var urls = JsonConvert.DeserializeObject<List<string>>(jarray);
                                        var div = new StringBuilder();
                                        var i = 1;
                                        urls.ForEach(url=>{
                                        div.Append($"<a target=\"_blank\" href='{HuaWeiObs.BucketLinkHref}/uploads{url}'>图片{i++} </a>" );
                                        });
                                        return div.ToString();
                                   }
                                   catch(Exception ex) 
                                   {
                                       return "";
                                       }
                                  
                               }
                               return "";
                           }
                       }
                      },
                     
                     {"total_cost_amount", new DataItem(){Title="成本金额",SqlFld="", Width="90", CellsAlign="right",ShowSum=true,Hidden=true}},
                     {"total_profit_amount", new DataItem(){Title="惠后利润",SqlFld="", Width="90", CellsAlign="right",ShowSum=true,Hidden=true}},
                     {"total_weight", new DataItem(){Title="重量(kg)", Width="90",SqlFld="(sheet_attribute->>'total_weight')::numeric", CellsAlign="right",ShowSum = true,Hidden=true,LabelInDB=false}},
                     {"total_volume", new DataItem(){Title="体积(m³)", Width="90",SqlFld="(sheet_attribute->>'total_volume')::numeric", CellsAlign="right",ShowSum = true,Hidden=true,LabelInDB=false}},

                         // 添加单据用途列
                     {"sheet_usage", new DataItem(){Title = "单据用途",Width = "120",
                         SqlFld = "case sm.sheet_usage when 'LQ' then '处理临期' when 'D' then '正常销售' when 'T' then '退货' when 'HH' then '换货' when 'DH' then '还定货' when 'CL' then '陈列兑付' when 'KS' then '客损' end ",
                         CellsAlign = "center"
                     }},

					 },
                     QueryFromSQL=@"
from sheet_sale_order_main sm 
left join (select sheet_id as placeholder_sheet_id,sheet_no as placeholder_sheet_no,sale_order_sheet_id,red_flag from sheet_placeholder_order_main where company_id =~COMPANY_ID ) pm on pm.sale_order_sheet_id = sm.sheet_id and COALESCE(pm.red_flag,0) = COALESCE(sm.red_flag,0)
LEFT JOIN sheet_status_order oss on sm.sheet_id = oss.sheet_id and oss.company_id= ~COMPANY_ID
LEFT JOIN (select order_sheet_id, sheet_id as to_sale_sheet_id, sheet_no as sale_sheet_no, approve_time sale_approve_time from sheet_sale_main where company_id= ~COMPANY_ID and red_flag IS NULL) m on sm.sheet_id = m.order_sheet_id
LEFT JOIN (select supcust_id as sup_id,sup_name,sup_group,mobile,other_region from info_supcust where company_id= ~COMPANY_ID) s on sm.supcust_id = s.sup_id
LEFT JOIN (select group_id,group_name from info_supcust_group where company_id= ~COMPANY_ID) scg on s.sup_group = scg.group_id
LEFT JOIN info_branch b on sm.branch_id = b.branch_id and b.company_id= ~COMPANY_ID
LEFT JOIN (select sub_id,sub_name as payway1_name from cw_subject where company_id= ~COMPANY_ID) pw1 on sm.payway1_id = pw1.sub_id
LEFT JOIN (select sub_id,sub_name as payway2_name from cw_subject where company_id= ~COMPANY_ID) pw2 on sm.payway2_id = pw2.sub_id
LEFT JOIN (select oper_id,oper_name as seller_name from info_operator where company_id= ~COMPANY_ID) seller on sm.seller_id = seller.oper_id
LEFT JOIN (select oper_id,depart_path , oper_name as maker_name from info_operator where company_id= ~COMPANY_ID) maker on sm.maker_id = maker.oper_id
LEFT JOIN pay_bill pb on pb.company_id= ~COMPANY_ID and pb.bill_id = sm.pay_bill_id and pb.sheet_type = sm.sheet_type and pb.sheet_id = sm.sheet_id
~VAR_DETAIL_JOIN
where sm.company_id= ~COMPANY_ID ~VAR_IS_DEL ~VAR_DETAIL_CONDI",

                     QueryOrderSQL=" order by happen_time desc,sm.sheet_id desc"
                  }
                }
            };
        }
        public static void SetCostInfo(PageQueryModel page)
        {
            var costPriceType = "3";
            var costPriceTypeName = "预设进价";
            if (page.JsonCompanySetting.IsValid())
            {
                dynamic setting = Newtonsoft.Json.JsonConvert.DeserializeObject(page.JsonCompanySetting);
                if (setting != null && setting.costPriceType != null) costPriceType = setting.costPriceType;
            }
            var columns = page.Grids["gridItems"].Columns;

            bool seeProfit = false;
            if (page.JsonOperRights.IsValid())
            {
                bool seeInPrice = false;
                dynamic operRights = Newtonsoft.Json.JsonConvert.DeserializeObject(page.JsonOperRightsOrig);
                if (operRights?.delicacy?.seeInPrice?.value is not null)
                    seeInPrice = ((string)operRights.delicacy.seeInPrice.value).ToLower() == "true";

                seeProfit = ((string)operRights?.delicacy?.seeProfit?.value)?.ToLower() != "false";
                if (!seeInPrice) seeProfit = false;

            }


            if (!seeProfit)
            {
                columns["total_cost_amount"].HideOnLoad = columns["total_cost_amount"].Hidden = true;
                columns["total_profit_amount"].HideOnLoad = columns["total_profit_amount"].Hidden = true;
            }
            if (costPriceType == "4") costPriceTypeName = "最近平均进价";
            else if (costPriceType == "1") costPriceTypeName = "预设成本";
            else if (costPriceType == "2") costPriceTypeName = "加权平均成本";

            page.DataItems["cost_price_type"].Value = costPriceType;
            page.DataItems["cost_price_type"].Label = costPriceTypeName;
        }
          public static void SetCostColumns(PageQueryModel page)
         {
            var totalCostAmount = "sm.cost_amount_buy";//当前进价
            var cost_price_type = page.DataItems["cost_price_type"].Value;
            switch (cost_price_type)
            {
                case "3"://预设进价
                    totalCostAmount = "sm.cost_amount_buy";
                    break;
                case "2"://加权价
                    totalCostAmount = "sm.cost_amount_avg";
                    break;
                case "1"://预设成本
                    totalCostAmount = "sm.cost_amount_prop";
                    break;
                case "4"://最近平均进价
                    totalCostAmount = "sm.cost_amount_recent";
                    break;
            }

            var columns = page.Grids.GetValueOrDefault("gridItems").Columns;
            if (columns.ContainsKey("total_cost_amount"))
            {
                columns["total_cost_amount"].SqlFld = $@"{totalCostAmount}";
            }
            if (columns.ContainsKey("total_profit_amount"))
            {
                columns["total_profit_amount"].SqlFld = $@"round((total_amount-disc_amount)::numeric-{totalCostAmount},2)";
            }

            }
        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            SetCostColumns(this);
            string item_id = DataItems["item_id"].Value;
            string item_brand = DataItems["brand_id"].Value;
            string item_class = DataItems["item_class"].Value;
            string remark = DataItems["remark"].Value;
            string startDay = DataItems["startDay"].Value;
            string endDay = DataItems["endDay"].Value;
            string queryTimeAccord = DataItems["queryTimeAccord"].Value;
            string status = DataItems["status"].Value;

            if (queryTimeAccord == "byApproveTime")
            {
                DateTime tmStart = Convert.ToDateTime(startDay);
                tmStart = tmStart.AddMonths(-3);
                startDay = CPubVars.GetDateText(tmStart);
            }

            this.SQLVariables["DETAIL_JOIN"] = "";
            this.SQLVariables["DETAIL_CONDI"] = "";
            this.SQLVariables["IS_DEL"] = "and coalesce(sm.is_del, false) = false";
            if(status == "deleted" || status == "all")
            {
                this.SQLVariables["IS_DEL"] = "";
            }
 
            if (item_id.IsValid() || remark.IsValid()||item_brand.IsValid()||item_class.IsValid())
            {
                string condi = "";
                if (item_id.IsValid())
                {
                    condi += $" and d.item_id in ({item_id})";
                }
                if (remark.IsValid())
                {
                    condi += $" and remark like '%{remark}%'";
                }
                if (item_brand.IsValid())
                {
                    if(item_brand.Contains("-1"))
                        condi += $" and (item_brand in ({item_brand}) or item_brand is null)";
                    else condi += $" and item_brand in ({item_brand})";
                }
                if (item_class.IsValid())
                {
                    condi += $" and other_class like '%{item_class}%'";
                }
                this.SQLVariables["DETAIL_JOIN"] = @$"
left join 
(
   select distinct sheet_id as detail_sheet_id 
   from sheet_sale_order_detail d
   left join (select item_brand,item_id,other_class from info_item_prop where company_id={company_id})b on b.item_id=d.item_id
   where company_id={company_id} and happen_time between '{startDay}' and '{endDay}' {condi}
) sd on sm.sheet_id=sd.detail_sheet_id
left join 
(
   select distinct sheet_id as detail_sheet_id 
    from sheet_placeholder_order_detail d
    left join (select item_brand,item_id,other_class from info_item_prop where company_id={company_id})b on b.item_id=d.item_id
    where company_id={company_id} and happen_time between '{startDay}' and '{endDay}' {condi}
) spd on pm.placeholder_sheet_id=spd.detail_sheet_id";
                this.SQLVariables["DETAIL_CONDI"] = " and sd.detail_sheet_id is not null ";

            }
            if (queryTimeAccord == "byPayTime")
            {
                this.SQLVariables["IS_DEL"] += " and pb.pay_time is not null"; // 确保只展示有值的数据
            }

        }
        public async Task OnGet()
        {
            await InitGet(cmd);
            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }

        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            SetCostInfo(this);
            string requestFlag = CPubVars.RequestV(Request, "queryTimeAccord");
            if (requestFlag == "")
            {
                JObject jsonOptions = JsonConvert.DeserializeObject<JObject>(JsonOptionsRemembered);
                JObject pageRemembered = (JObject)jsonOptions["page_SaleOrderSheetView"];
                bool hasByHappenTimeOld = false;
                bool hasQueryTimeAccord = false;
                if (pageRemembered != null)
                {
                    foreach (var item in pageRemembered)
                    {
                        if (item.Key == "byHappenTime") hasByHappenTimeOld = true;
                        if (item.Key == "queryTimeAccord") hasQueryTimeAccord = true;
                    }
                }
                if (hasByHappenTimeOld && !hasQueryTimeAccord)
                {
                    string value = (string)pageRemembered["byHappenTime"]["value"];
                    if (value == "true")
                    {
                        DataItems["queryTimeAccord"].Value = "byHappenTime";
                        DataItems["queryTimeAccord"].Label = "交易时间";
                    }
                    else
                    {
                        DataItems["queryTimeAccord"].Value = "byApproveTime";
                        DataItems["queryTimeAccord"].Label = "审核时间";
                    }
                }

            }
            else
            {
                if (requestFlag == "byHappenTime")
                {
                    DataItems["queryTimeAccord"].Value = "byHappenTime";
                    DataItems["queryTimeAccord"].Label = "交易时间";
                }
                else if (requestFlag == "byMakeTime")
                {
                    DataItems["queryTimeAccord"].Value = "byMakeTime";
                    DataItems["queryTimeAccord"].Label = "制单时间";
                }
                else if (requestFlag == "byCheckedTime")
                {
                    DataItems["queryTimeAccord"].Value = "byCheckedTime";
                    DataItems["queryTimeAccord"].Label = "交账时间";
                }
                else if (requestFlag == "bySendTime")
                {
                    DataItems["queryTimeAccord"].Value = "bySendTime";
                    DataItems["queryTimeAccord"].Label = "送货时间";
                }

            }

            bool openPlaceholderSheet = false;
            if (JsonCompanySetting.IsValid())
            {
                dynamic setting = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonCompanySetting);
                if (setting != null && setting.openPlaceholderSheet != null && setting.openPlaceholderSheet.ToString().ToLower() == "true") openPlaceholderSheet = true;
            }
            if (openPlaceholderSheet) Grids["gridItems"].Columns["placeholder_sheet_no"].Hidden = false;
            else Grids["gridItems"].Columns["placeholder_sheet_no"].Hidden = true;

            // 确保URL参数优先级最高，重新处理URL参数以覆盖记忆的选项
            var urlStartDay = CPubVars.RequestV(Request, "startDay");
            var urlEndDay = CPubVars.RequestV(Request, "endDay");
            var urlSupcustId = CPubVars.RequestV(Request, "supcust_id");
            var urlSupName = CPubVars.RequestV(Request, "sup_name");

            if (!string.IsNullOrEmpty(urlStartDay))
            {
                DataItems["startDay"].Value = urlStartDay;
            }
            if (!string.IsNullOrEmpty(urlEndDay))
            {
                DataItems["endDay"].Value = urlEndDay;
            }
            if (!string.IsNullOrEmpty(urlSupcustId))
            {
                DataItems["supcust_id"].Value = urlSupcustId;
            }
            if (!string.IsNullOrEmpty(urlSupName))
            {
                DataItems["supcust_id"].Label = urlSupName;
            }

        }
       
    }



    [Route("api/[controller]/[action]")]
    public class SaleOrderSheetViewController : BaseController
    { 
        public SaleOrderSheetViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<JsonResult> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            SaleOrderSheetViewModel model = new SaleOrderSheetViewModel(cmd);
            return await PageBaseModel.GetDataItemOptionsJson(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
 
        }

        [HttpGet]
        public async Task<object> GetQueryRecords()
        { 
            SaleOrderSheetViewModel model = new SaleOrderSheetViewModel(cmd);           
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            SaleOrderSheetViewModel model = new SaleOrderSheetViewModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }
        [HttpGet]
        public async Task<JsonResult> GetMultiSheetsToPrint(string operKey, string sheetIDs, bool bPrintSum, bool bPrintEach,bool bPrintSheetsMainInfo, bool smallUnitBarcode, string clientVersion, string sortColumn, string sortDirection)
        {
            SheetSaleOrder.GetSheetsUsage usage = new SheetSaleOrder.GetSheetsUsage();
            usage.ForPrint = true;
            usage.GetEachSheet = bPrintEach;
            usage.GetSumSheet = bPrintSum;
            usage.GetSheetsMainInfo = bPrintSheetsMainInfo;

            SheetSaleOrder.GetSheetsResult res = await SheetSaleOrder.GetItemSheets<SheetSaleOrder, SheetRowSaleOrder>(cmd, operKey, sheetIDs, usage, smallUnitBarcode, clientVersion, sortColumn, sortDirection);
            return new JsonResult(res);
            //return new JsonResult(new {result=res.result,msg=res.msg,printSheets=res.sheetGroup,sheetGroup=res.sheetGroup});
            // return await GetMultiSheetsToPrint_Inner<SheetSale,SheetRowSale>(cmd,operKey, sheetIDs, bPrintSum, bPrintEach, smallUnitBarcode, clientVersion, sortColumn, sortDirection);
        }
        [HttpPost]
        public async Task<JsonResult> SetSenderForSheets([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            string senders_id = data.senders_id;
            string senders_name = data.senders_name;
            string sheetIDs = data.sheetIDs;
            string msg = "";
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);

            if (string.IsNullOrEmpty(sheetIDs)) msg = "请选择至少一张单据\r\n";
            if (string.IsNullOrEmpty(senders_id)) msg += "请选择送货员\r\n";
            if (msg != "")
                return new JsonResult(new { result = "Error", msg });
            cmd.CommandText = $"select oper_name from info_operator where company_id={companyID} and oper_id={operID}";
            object ov = await cmd.ExecuteScalarAsync();
            string operName = "";
            if (ov != null && ov != DBNull.Value)
            {
                operName = ov.ToString();
            }

            string operInfo = CPubVars.GetDateText(DateTime.Now) + " " + operName;
            string sql = $"update sheet_sale_order_main set senders_id='{senders_id}',senders_name='{senders_name}',approve_brief=COALESCE(approve_brief)||'{operInfo}将送货员'||senders_name||'调整为'||'{senders_name}' where company_id={companyID} and sheet_id in ({sheetIDs});";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            return new JsonResult(new { result = "OK", msg });

        }
        [HttpPost]
        public async Task<JsonResult> BatchSetSheetsWorkBrief([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            string sheetIDs = data.sheetIDs;
            string workBrief = data.workBrief;
            string result = "";
            string msg = "";
            string sql = "";
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            if (!string.IsNullOrWhiteSpace(sheetIDs) && !string.IsNullOrWhiteSpace(workBrief))
            {
                try
                {
                    // 对工作备注进行SQL注入防护
                    workBrief = workBrief.Replace("'", "''");
                    sql = $@"update sheet_sale_order_main set work_brief = '{workBrief}' where company_id={companyID} and sheet_id in ({sheetIDs}) ";
                    cmd.CommandText = sql;
                    await cmd.ExecuteNonQueryAsync();
                    result = "OK";
                    msg = "批量修改工作备注成功";
                }
                catch (Exception e)
                {
                    result = "fail";
                    msg = "操作失败";
                    NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                    logger.Error(e.ToString()+"updateSql:"+ sql);
                }
            }
            else {
                result = "fail";
                msg = "参数不完整，操作失败";
            }

            return new JsonResult(new { result, msg});
        }
        [HttpGet]
        public async Task<JsonResult> GetSheetsToApprove(string operKey, string sheetIDs)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);

            string msg = "";
            string[] sheetID = sheetIDs.Split(',');

	        string redisKey="approveSaleOrderSheet:"+sheetIDs;
			bool lockAcquired = await RedisHelper.SetAsync(redisKey, "1", 20, RedisExistence.Nx);
			if (!lockAcquired)
			{
				msg = "请勿重复审核!";
				return Json(new { result = $"{msg}" });
			}

            foreach (var ID in sheetID)
            {
                SheetSaleOrder sheet = new SheetSaleOrder(LOAD_PURPOSE.SHOW);
                await sheet.Load(cmd, companyID, ID);
                sheet.OperID = operID;
                sheet.OperKey = operKey;
                var aa = await sheet.SaveAndApprove(cmd);
                if (aa != "")
                {
                    msg += sheet.sheet_no + aa;
                }
            }
            await RedisHelper.DelAsync(redisKey);

            if (msg != "")
            {
                return Json(new { result = $"{msg}" });
            }
            else
            {
                return Json(new { result = "OK" });
            }

        }
        [HttpGet]
        public async Task<JsonResult> GetSheetsToReview(string operKey, string sheetIDs)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            string now = CPubVars.GetDateText(DateTime.Now);
            string msg = "";
            string[] sheetID = sheetIDs.Split(',');

            foreach (var ID in sheetID)
            {
                SheetSaleOrder sheet = new SheetSaleOrder(LOAD_PURPOSE.SHOW);
                await sheet.Load(cmd, companyID, ID);
                sheet.OperID = operID;
                sheet.OperKey = operKey;
                dynamic rec = await CDbDealer.Get1RecordFromSQLAsync($"select approve_time,red_flag from sheet_sale_order_main where company_id={companyID} and sheet_id={ID};", cmd);
                if (rec == null)
                {
                    msg += sheet.sheet_no + "单据不存在";
                }
                else if (rec.red_flag != "")
                {
                    msg += sheet.sheet_no + "被红冲单据不能复核";
                }
                else if (rec.approve_time == "")
                {
                    msg += sheet.sheet_no + "单据未审核不能复核";
                }
                else
                {
                    cmd.CommandText = $"update sheet_sale_order_main set review_time ='{now}',reviewer_id={operID} where company_id={companyID} and sheet_id={ID};";
                    await cmd.ExecuteNonQueryAsync();
                }
            }

            if (msg != "")
            {
                return Json(new { result = $"{msg}" });
            }
            else
            {
                return Json(new { result = "OK" });
            }
        }


    }
}
