﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ArtisanManage.Pages.BaseInfo
{
    public class SalesSummaryBySellerModel : PageQueryModel
    { 
        public SalesSummaryBySellerModel(CMySbCommand cmd) : base(Services.MenuId.salesSummaryBySeller)
        {
            this.UsePostMethod = true;
            this.cmd = cmd;
            this.PageTitle = "销售汇总(业务员)";
            this.NotQueryHideColumn = true;
            CanQueryByApproveTime = true;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){SqlAreaToPlace="MAIN_CONDI,REBATE_CONDI", Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="sm.happen_time+sd.happen_time",CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){SqlAreaToPlace="MAIN_CONDI,REBATE_CONDI",Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="sm.happen_time+sd.happen_time",CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"status",new DataItem(){SqlAreaToPlace="MAIN_CONDI,REBATE_CONDI",FldArea="divHead",  Title="单据状态", ButtonUsage = "list",CompareOperator="=",Value="approved",Label="已审核",
                        Source = @"[{v:'normal',l:'所有',condition:""sm.red_flag is null""},
                                   {v:'unapproved',l:'未审核',condition:""sm.approve_time is null""},
                                   {v:'approved',l:'已审核',condition:""sm.approve_time is not null and sm.red_flag is null""}]"
                }},
                {"trade_type",new DataItem(){SqlAreaToPlace="MAIN_CONDI",FldArea="divHead",Checkboxes=true, Title="交易类型",ButtonUsage = "list",CompareOperator="=",Value="",Label="",
                        Source = @"[{v:'ALL',l:'所有',condition:""true""},
                                    {v:'X',l:'销售',condition:""coalesce(trade_type,'X')='X' and sd.quantity*sd.inout_flag<0""},
                                   {v:'T',l:'退货',condition:""coalesce(trade_type,'X')='T' and sd.quantity*sd.inout_flag>0""},
                                   {v:'XT',l:'销退',condition:""coalesce(trade_type,'X') in ('X','T','XD','TD')""},
                                   {v:'DH',l:'定货还货',condition:""trade_type='DH'""},
                                   {v:'JH',l:'借还货',condition:""trade_type in ('J','H')""},
                                   {v:'CL',l:'陈列兑付',condition:""trade_type ='CL'""},
                                   {v:'HH',l:'换货',condition:""trade_type in ('HR','HC')""}]"
                }},
                {"arrears_status",new DataItem(){SqlAreaToPlace="MAIN_CONDI",FldArea="divHead",Title="欠款情况",Hidden=true, Checkboxes=true, ButtonUsage = "list",CompareOperator="=",
                    Source = @"[{v:'cleared',l:'已结清',condition:""abs(total_amount-paid_amount-disc_amount)<0.1""},
                                 {v:'uncleared',l:'未结清',condition:""abs(total_amount-paid_amount-disc_amount)>0.1""},
                                 {v:'all',l:'所有',condition:""true""}]"
                }},
                {"brand_id", CommonTool.GetDataItem("brand_id", new DataItemChange(){ SqlAreaToPlace="MAIN_CONDI,REBATE_CONDI",SqlFld="ip.item_brand"})},
                {"class_id",new DataItem(){SqlAreaToPlace="MAIN_CONDI,REBATE_CONDI",Title="类别",FldArea="divHead",LabelFld="class_name",MaxRecords="1000",ButtonUsage="list",CompareOperator="like",SqlFld="ip.other_class",
                    SqlForOptions ="select class_id as v,class_name as l from info_item_class order by order_index,class_name,class_id"}},
                {"seller_id",new DataItem(){SqlAreaToPlace="MAIN_CONDI,REBATE_CONDI",Title="业务员",Checkboxes=true,FldArea="divHead",LabelFld="seller_name",ButtonUsage="list",CompareOperator="=",SqlFld="sm.seller_id",SqlForOptions=CommonTool.selectSellers } },
                

                {"senders_id",new DataItem(){SqlAreaToPlace="MAIN_CONDI",FldArea="divHead",Title="送货员",Checkboxes=true, LabelFld="senders_name",ButtonUsage="list",SqlForOptions=CommonTool.selectSenders,CompareOperator="like"}},//SqlForOptions="select oper_id as v,oper_name as l,py_str as z from info_operator"
                {"depart_path",new DataItem(){SqlAreaToPlace="MAIN_CONDI,REBATE_CONDI",Title="员工部门",FldArea="divHead",LabelFld="depart_path_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=true,DropDownWidth="150", TreePathFld="depart_path",CompareOperator="like",
                    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"
                }},
                 {"department_id",new DataItem(){SqlAreaToPlace="MAIN_CONDI,REBATE_CONDI",Title="部门",TreePathFld="department_path",Hidden=true, FldArea="divHead",LabelFld="department_id_label", CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=false,DropDownWidth="150", CompareOperator="=",
                    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"
                }},
                {"cost_price_type",new DataItem(){FldArea="divHead",Title="成本核算",ForQuery=false,LabelFld="cost_price_type_name",ButtonUsage="list",Source = "[{v:'3',l:'预设进价'},{v:'2',l:'加权平均价'},{v:'1',l:'预设成本'},{v:'4',l:'最近平均进价'}]", CompareOperator="=" }},
				{"queryTimeAccord",new DataItem(){
					FldArea="divHead", Title="时间类型", LabelFld = "time_status_name", ButtonUsage = "list",
					CompareOperator="=",Value="byHappenTime",Label="交易时间",ForQuery=false, AutoRemember=true,
					Source = @"[
                                {v:'byHappenTime',l:'交易时间'},
                                {v:'byMakeTime',l:'制单时间'},
                                {v:'byApproveTime',l:'审核时间'},
                                {v:'byCheckedTime',l:'交账时间'},
                                {v:'bySendTime',l:'送货时间'},
                                {v:'byClearArrearsTime',l:'欠款结清时间'}
                               ]"
				}},
				{"showRebateProfit",new DataItem(){FldArea="divHead",Title="显示补差后利润",CtrlType="jqxCheckBox",Hidden=true,ForQuery=false,Value="false"}},
                {"sheetType",new DataItem(){Title="",FldArea="divHead",Hidden=true,ForQuery=false,HideOnLoad = true} },
                //{"detail_table",new DataItem(){Title="",FldArea="divHead",Hidden=true,ForQuery=false,} }
            };

            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true, Sortable=true,
                    
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"seller_id",    new DataItem(){Title="业务员",IsIDColumn=true, SelectFieldEvenHidden=true, SqlFld="coalesce(sd.seller_id,rb.seller_id)", Width="150",HideOnLoad=true, Hidden=true,
                           SqlFldOnHiddenRelateColumn=new Dictionary<string, string>(){ { "rebate_sheet","sd.seller_id" } }
                       }},

                       {"seller_name",    new DataItem(){SqlFld="coalesce(sd.seller_name,rb.seller_name)", Title="业务员",Pinned=true,  Sortable=true,Linkable=true,   Width="200" ,
                           SqlFldOnHiddenRelateColumn=new Dictionary<string, string>(){ { "rebate_sheet","sd.seller_name" } }}},
                       {"x_quantity",   new DataItem(){SqlAreaToPlace="MAIN_FLD",Title="销售量",Sortable=true,  CellsAlign="right",  Width="100",SqlFld="round(sum(case when quantity*money_inout_flag>0 then quantity*sd.unit_factor else 0 end)::numeric,2)",ShowSum=true,Hidden=true}},
                       {"x_quantity_b",   new DataItem(){SqlAreaToPlace="MAIN_FLD",Title="销售量(大)", Sortable=true, CellsAlign="right",  Width="100",SqlFld="round(sum(case when quantity*money_inout_flag>0 then quantity*sd.unit_factor/t.b_unit_factor else 0 end)::numeric,2)",ShowSum=true,Hidden=true}},                      
                       {"t_quantity",   new DataItem(){SqlAreaToPlace="MAIN_FLD",Title="退货量",Sortable=true,  CellsAlign="right",  Width="80",SqlFld="round(sum(case when quantity*money_inout_flag<0 and sheet_type in ('X','XD') then quantity*sd.unit_factor*-1 when quantity*money_inout_flag<0 and sheet_type in ('T','TD') then quantity*sd.unit_factor else 0 end)::numeric,2)",ShowSum=true,Hidden=true}},
                       {"t_quantity_b",   new DataItem(){SqlAreaToPlace="MAIN_FLD",Title="退货量(大)", Sortable=true, CellsAlign="right",  Width="100",SqlFld="round(sum(case when quantity*money_inout_flag<0 and sheet_type in ('X','XD') then quantity*sd.unit_factor*-1/t.b_unit_factor when quantity*money_inout_flag<0 and sheet_type in ('T','TD') then quantity*sd.unit_factor/t.b_unit_factor else 0 end)::numeric,2)",ShowSum=true,Hidden=true}},
                       {"return_rate", new DataItem(){SqlAreaToPlace="MAIN_FLD",Title="退货率",  CellsAlign="center",ShowSum=true, Sortable=true,  Width="80", GetFromDb=false,Hidden=true,
                            SortFld=@"case when round(sum(case when quantity*money_inout_flag>0 then quantity*sd.unit_factor else 0 end)::numeric,2)<>0 then 
                                      round(sum(case when quantity*money_inout_flag<0 and sheet_type='X'then quantity*sd.unit_factor*-1 when quantity*money_inout_flag<0 and sheet_type='T'then quantity*sd.unit_factor else 0 end)::numeric,2)/ round(sum(case when quantity * money_inout_flag > 0 then quantity * sd.unit_factor else 0 end)::numeric,2) end",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                            RelyColumns="x_quantity,t_quantity",
                            FuncGetValueFromRowData=(colName,row)=>
                            {
                               string x_quantity_num = row["x_quantity"];
                               string t_quantity_num = row["t_quantity"];
                               string returnRate="";
                               if (x_quantity_num != "" && t_quantity_num!=""){
                                   decimal n=CPubVars.ToDecimal(x_quantity_num);
                                   if (n != 0)
                                   {
                                       decimal d=CPubVars.ToDecimal(t_quantity_num)/n*100;
                                     
                                       returnRate=CPubVars.FormatMoney(d,1)+"%";
                                   }
                               }
                               return returnRate;
                            },
                         
                        }},
                       {"net_quantity", new DataItem(){SqlAreaToPlace="MAIN_FLD",Title="净销量", Sortable=true,  CellsAlign="right",  Width="80",SqlFld="round(sum(quantity*sd.unit_factor*inout_flag*(-1))::numeric,2)",ShowSum=true,Hidden=true}},
                       {"net_quantity_b", new DataItem(){SqlAreaToPlace="MAIN_FLD",Title="净销量(大)", Sortable=true,  CellsAlign="right",  Width="100",SqlFld="round(sum(quantity*sd.unit_factor*inout_flag*(-1)/t.b_unit_factor)::numeric,2)",ShowSum=true,Hidden=true}},
                       {"free_quantity", new DataItem(){SqlAreaToPlace="MAIN_FLD",Title="赠品量", Sortable=true,  CellsAlign="right",  Width="80",SqlFld="round(sum(case when sd.sub_amount=0 and  coalesce(trade_type,'X') not  in ('CL','H' ,'J')  then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric,2)",ShowSum=true,Hidden=true}},
                       {"free_quantity_b", new DataItem(){SqlAreaToPlace="MAIN_FLD",Title="赠品量(大)", Sortable=true,  CellsAlign="right",  Width="100",SqlFld="round(sum(case when sd.sub_amount=0 and  coalesce(trade_type,'X') not  in ('CL','H' ,'J')  then sd.quantity*sd.unit_factor*sd.inout_flag*(-1)/t.b_unit_factor else 0 end)::numeric,2)",ShowSum=true,Hidden=true}},
                       {"J_quantity", new DataItem(){SqlAreaToPlace="MAIN_FLD",Title="借货量", Sortable=true,  CellsAlign="right",  Width="80",SqlFld="round(sum(case when  coalesce(trade_type,'X') = 'J'  then sd.quantity*sd.unit_factor*sd.inout_flag else 0 end)::numeric,2)",ShowSum=true,Hidden=true}},
                       {"H_quantity", new DataItem(){SqlAreaToPlace="MAIN_FLD",Title="还货量", Sortable=true,  CellsAlign="right",  Width="80",SqlFld="round(sum(case when  coalesce(trade_type,'X') = 'H'  then sd.quantity*sd.unit_factor*sd.inout_flag else 0 end)::numeric,2)",ShowSum=true,Hidden=true}},
                       {"weight", new DataItem(){SqlAreaToPlace="MAIN_FLD",Title="销售重量(kg)", Sortable=true, CellsAlign="right", Width="120", SqlFld="round(sum(case when sd.quantity*sd.inout_flag<0 then sd.quantity*sd.inout_flag*(-1)*mu2.weight else 0 end)::numeric,3)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; }, ShowSum=true, Hidden=true,
                       } },
                       {"return_weight", new DataItem(){SqlAreaToPlace="MAIN_FLD",Title="退货重量(kg)", Sortable=true, CellsAlign="right", Width="120", SqlFld="round(sum(case when sd.quantity*sd.inout_flag>0 then sd.quantity*sd.inout_flag*mu2.weight else 0 end)::numeric,3)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true, 
                       } },
                        {"x_amount",     new DataItem(){SqlAreaToPlace="MAIN_FLD",Title="销售金额", Sortable=true, CellsAlign="right",  Width="100",SqlFld="round(sum(case when sd.quantity*sd.inout_flag<0 then sd.inout_flag*(-1)*sd.sub_amount else 0 end)::numeric,2) ",ShowSum=true}},
                       {"t_amount",     new DataItem(){SqlAreaToPlace="MAIN_FLD",Title="退货金额", Sortable=true, CellsAlign="right",  Width="100",SqlFld="round(sum(case when sd.quantity*sd.inout_flag>0 then sd.sub_amount*sd.inout_flag else 0 end)::numeric,2)",ShowSum=true}},
                       {"net_amount",   new DataItem(){SqlAreaToPlace="MAIN_FLD",Title="销售净额", Sortable=true, CellsAlign="right",  Width="100",SqlFld="round(sum(sub_amount*inout_flag*(-1))::numeric,2)",ShowSum=true}},
					   {"now_disc_amount",  new DataItem(){SqlAreaToPlace="MAIN_FLD",Title="优惠金额", Sortable=true, CellsAlign="right",     Width="100",SqlFld="round(sum(CASE WHEN row_index = 1 THEN inout_flag * ( - 1 ) * COALESCE (now_disc_amount, 0 ) ELSE 0 END)::numeric,2)",ShowSum=true}},
					   {"disc_amount",  new DataItem(){SqlAreaToPlace="MAIN_FLD",Title="累计优惠", Sortable=true, CellsAlign="right",     Width="100",SqlFld="round(sum(CASE WHEN row_index = 1 THEN inout_flag * ( - 1 ) * COALESCE (disc_amount, 0 ) ELSE 0 END)::numeric,2)",ShowSum=true}},

					   { "rebate_amount", new DataItem() {SqlAreaToPlace="MAIN_FLD",Title = "销售单补差额", SqlFld="SUM(coalesce(rebate_price,0) * quantity*sd.unit_factor)", CellsAlign = "right", Width = "100", Hidden = true, Sortable = true,  ShowSum = true, FuncDealMe=(value)=>{return string.IsNullOrEmpty(value)?"":Math.Round(CPubVars.ToDecimal(value),2).ToString(); } }},
                       {"net_amount_cut_rebate",   new DataItem(){SqlAreaToPlace="MAIN_FLD", Title="销售净额(扣补差额)", Sortable=true,Hidden= true, CellsAlign="right",  Width="150",SqlFld="round(sum(sub_amount*inout_flag*(-1) - coalesce(rebate_price, 0)*quantity*sd.unit_factor)::numeric,2)",ShowSum=true}},

                       {"rebate_quantity",   new DataItem(){SqlAreaToPlace="MAIN_FLD",Title="补差数量", Sortable=true,  CellsAlign="right",  Width="100",SqlFld="round(sum(case when coalesce(rebate_price, 0) <> 0 then quantity*sd.unit_factor else 0 end)::numeric,2)",ShowSum=true,Hidden=true}},

                      
                       //{"disc_amount",  new DataItem(){Title="优惠金额", Sortable=true, CellsAlign="right",     Width="15%",SqlFld="round(sum((orig_price-real_price)*sd.quantity*sd.inout_flag*(-1))::numeric,2)",ShowSum=true}},
                       {"rebate_quantity_rbs",   new DataItem(){SqlAreaToPlace="REBATE_FLD",Title="补差单数量",Sortable=true,  CellsAlign="right",  Width="100",SqlFld="round(sum(quantity*sd.unit_factor)::numeric,2)",ShowSum=true,Hidden=true}},

                       {"rebate_amount_rbs", new DataItem() {SqlAreaToPlace="REBATE_FLD",SqlFld="round(sum(rebate_price*quantity)::numeric,2)", Title = "补差单金额", CellsAlign = "right", Width = "100", Hidden = true, Sortable = true,  ShowSum = true, FuncDealMe=(value)=>{return string.IsNullOrEmpty(value)?"":Math.Round(CPubVars.ToDecimal(value),2).ToString(); } }},
                       {"net_amount_cut_rebate_rbs",   new DataItem(){SqlAreaToPlace="",Title="销售净额(扣补差单金额)", Sortable=true, CellsAlign="right",  Width="100",RelyColumns="net_amount,rebate_amount_rbs", SqlFld="round((net_amount - coalesce(rebate_amount_rbs,0))::numeric,2)",ShowSum=true,Hidden=true}},
                       
                       /*{"cost_amount_hasfree",  new DataItem(){Title="成本(含赠)", Sortable=true,  CellsAlign="right",  Width="15%",ShowSum=true}},
                       {"profit_hasfree",  new DataItem(){Title="利润(含赠)", Sortable=true,  CellsAlign="right",  Width="15%",ShowSum=true}},
                       {"profit_rate_hasfree",new DataItem(){Title = "利润率(%)(含赠)", Sortable=true,CellsAlign = "right",Width = "7%",ShowAvg = true,
                           FuncGetSumValue = (sumColumnValues) =>
                           {
                               string s_profit_hasfree =sumColumnValues["profit_hasfree"];
                               string s_net_amount =sumColumnValues["net_amount"];

                               double profit_hasfree=s_profit_hasfree!=""?Convert.ToDouble(s_profit_hasfree) : 0.0;
                               double net_amount=s_net_amount!=""?Convert.ToDouble(s_net_amount) : 0.0;
                               string rate="";
                               if (net_amount != 0)
                               {
                                   rate=CPubVars.FormatMoney(profit_hasfree/net_amount*100,1);
                               }
                               return rate;
                           }}},
                       {"free_cost_amount",new DataItem(){Title="赠品成本",CellsAlign="right",Width="8%",SqlFld="",ShowSum=true,FuncDealMe=(value)=>{return value=="0"?"":value; }} },
                       {"cost_amount",  new DataItem(){Title="成本", CellsAlign="right",    Width="8%", Sortable=true,SqlFld="Will be changed by condition",ShowSum=true}},
                       {"profit",       new DataItem(){Title="利润", CellsAlign="right",    Width="8%", Sortable=true,ShowSum=true,FuncDealMe=(value)=>{return value=="0"?"":value; }}},
                       {"profit_rate",new DataItem(){ Title = "利润率(%)", Sortable=true,CellsAlign = "right",Width = "7%",ShowAvg = true,
                           FuncGetSumValue = (sumColumnValues) =>
                           {
                               string s_profit_hasfree =sumColumnValues["profit"];
                               string s_net_amount =sumColumnValues["net_amount"];

                               double profit_hasfree=s_profit_hasfree!=""?Convert.ToDouble(s_profit_hasfree) : 0.0;
                               double net_amount=s_net_amount!=""?Convert.ToDouble(s_net_amount) : 0.0;
                               string rate="";
                               if (net_amount != 0)
                               {
                                   rate=CPubVars.FormatMoney(profit_hasfree/net_amount*100,1);
                               }
                               return rate;
                           }}},*/
                     },
                  /*   QueryFromSQL=@"
FROM ~detailTable  sd
LEFT JOIN ~mainTable sm on sm.sheet_id = sd.sheet_id and sd.company_id=~COMPANY_ID
LEFT JOIN
(
    SELECT item_id,     (s->>'f1')::numeric as s_unit_factor,s->>'f2' as s_unit_no,s->>'f3' as s_barcode,
                        (b->>'f1')::numeric as b_unit_factor,b->>'f2' as b_unit_no,b->>'f3' as b_barcode,
                        (m->>'f1')::numeric as m_unit_factor,m->>'f2' as m_unit_no,m->>'f3' as m_barcode
    FROM crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,barcode,weight,volume)) as json FROM info_item_multi_unit where company_id= ~COMPANY_ID order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
    as errr(item_id int, s jsonb,m jsonb,b jsonb)
) t
ON sd.item_id=t.item_id
LEFT JOIN info_item_multi_unit mu2 on  sd.item_id = mu2.item_id and sd.unit_factor = mu2.unit_factor  and sd.unit_no = mu2.unit_no  and mu2.company_id = ~COMPANY_ID
LEFT JOIN info_operator o on sm.seller_id = o.oper_id and o.company_id=~COMPANY_ID
LEFT JOIN info_item_prop ip on sd.item_id = ip.item_id and ip.company_id=~COMPANY_ID 
WHERE sm.company_id= ~COMPANY_ID and sm.seller_id is not null ~VAR_IS_DEL",
                     */
                 QueryFromSQL=@"
FROM 
(
    SELECT seller_id,oper_name seller_name, ~AREA_MAIN_FLD
    FROM ~mainTable sm 
    LEFT JOIN ~detailTable sd on sm.sheet_id = sd.sheet_id and sd.company_id=~COMPANY_ID
    LEFT JOIN
    (
        SELECT item_id,     (s->>'f1')::numeric as s_unit_factor,s->>'f2' as s_unit_no,s->>'f3' as s_barcode,
                            (b->>'f1')::numeric as b_unit_factor,b->>'f2' as b_unit_no,b->>'f3' as b_barcode,
                            (m->>'f1')::numeric as m_unit_factor,m->>'f2' as m_unit_no,m->>'f3' as m_barcode
        FROM crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,barcode,weight,volume)) as json FROM info_item_multi_unit where company_id= ~COMPANY_ID order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
        as errr(item_id int, s jsonb,m jsonb,b jsonb)
    ) t
    ON sd.item_id=t.item_id
    LEFT JOIN info_item_multi_unit mu2 on  sd.item_id = mu2.item_id and sd.unit_factor = mu2.unit_factor  and sd.unit_no = mu2.unit_no  and mu2.company_id = ~COMPANY_ID
    LEFT JOIN info_operator o on sm.seller_id = o.oper_id and o.company_id=~COMPANY_ID
    LEFT JOIN info_item_prop ip on sd.item_id = ip.item_id and ip.company_id=~COMPANY_ID 
    WHERE sm.company_id= ~COMPANY_ID and sm.seller_id is not null ~VAR_IS_DEL ~AREA_MAIN_CONDI
    GROUP BY seller_id,seller_name
) sd

<RELATE_COLUMNS id=""rebate_sheet"" columns=""rebate_quantity_rbs,rebate_amount_rbs,net_amount_cut_rebate_rbs"">
FULL JOIN
(
    SELECT seller_id, oper_name seller_name, ~AREA_REBATE_FLD
    FROM      sheet_price_rebate_detail  sd
    LEFT JOIN sheet_price_rebate_main    sm on sm.sheet_id = sd.sheet_id and sd.company_id=~COMPANY_ID
    LEFT JOIN
    (
        SELECT item_id,     (s->>'f1')::numeric as s_unit_factor,s->>'f2' as s_unit_no,s->>'f3' as s_barcode,
                            (b->>'f1')::numeric as b_unit_factor,b->>'f2' as b_unit_no,b->>'f3' as b_barcode,
                            (m->>'f1')::numeric as m_unit_factor,m->>'f2' as m_unit_no,m->>'f3' as m_barcode
        FROM crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,barcode,weight,volume)) as json FROM info_item_multi_unit where company_id= ~COMPANY_ID order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
        as errr(item_id int, s jsonb,m jsonb,b jsonb)
    ) t
    ON sd.item_id=t.item_id
    LEFT JOIN info_item_multi_unit mu2 on  sd.item_id = mu2.item_id and sd.unit_factor = mu2.unit_factor  and sd.unit_no = mu2.unit_no  and mu2.company_id = ~COMPANY_ID
    LEFT JOIN info_operator o on sm.seller_id = o.oper_id and o.company_id=~COMPANY_ID
    LEFT JOIN info_item_prop ip on sd.item_id = ip.item_id and ip.company_id=~COMPANY_ID
    WHERE sm.company_id= ~COMPANY_ID and sm.seller_id is not null ~AREA_REBATE_CONDI
    GROUP BY seller_id,seller_name
) rb on sd.seller_id=rb.seller_id
 
</RELATE_COLUMNS>

"
,
                  

					// QueryGroupBySQL = " group by oper_id, oper_name",
                     QueryOrderSQL=" ORDER BY sd.seller_name"
                  }
                }
            };
			var origCols = Grids["gridItems"].Columns;
			var cols =SalesSummaryByItemModel.GetProfitColumns(origCols,true);
         
            foreach (var k in cols)
            {
                k.Value.SqlAreaToPlace = "MAIN_FLD";
                origCols.Add(k.Key, k.Value);
            }
        }

        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            SalesSummaryByItemModel.SetProfitColumns(this);
            var sheetType = DataItems["sheetType"].Value;
            this.SQLVariables["IS_DEL"] = "";
            if(sheetType.ToLower() == "xd")
            {
                this.SQLVariables["IS_DEL"] = "and coalesce(sm.is_del, false) = false";
            }
            /*
      var cost_price_type = DataItems["cost_price_type"].Value;
      var costPrice = "sd.cost_price_buy";//当前进价
      switch (cost_price_type)
      {
          case "3"://预设进价
              costPrice = "sd.cost_price_buy";
              break;
          case "2"://加权价
              costPrice = "sd.cost_price_avg";
              break;
          case "1"://预设成本
              costPrice = "sd.cost_price_prop";
              break;
          case "4"://最近平均进价
              costPrice = "sd.cost_price_recent";
              break;
      }
      //Console.WriteLine("Seller页面的OnQueryConditionGot方法获取的costPrice:" + costPrice);
      var columns = Grids.GetValueOrDefault("gridItems").Columns;




      var cost_price_type = DataItems["cost_price_type"].Value;


      var costPrice = "sd.cost_price_buy";//当前进价
      switch (cost_price_type)
      {
          case "3"://当前进价
              costPrice = "sd.cost_price_buy";
              break;
          case "2"://加权价
              costPrice = "sd.cost_price_avg";
              break;
          case "1"://预设成本
              costPrice = "sd.cost_price_prop";
              break;
      }

      var columns = Grids.GetValueOrDefault("gridItems").Columns;

      columns["cost_amount_hasfree"].SqlFld = $"round( SUM ( CASE WHEN trade_type !='J' THEN quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} ELSE 0 END ) :: NUMERIC, 2 ) ";
      columns["profit_hasfree"].SqlFld = $"round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-sum(case when row_index = 1 then inout_flag*(-1)*coalesce(disc_amount,0) else 0 end)-round(sum(quantity*sd.unit_factor*inout_flag*(-1)*{costPrice})::numeric,2)";
      columns["profit_rate_hasfree"].SqlFld = @$"
cast( 
(
(round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-sum(case when row_index = 1 then inout_flag*(-1)*coalesce(disc_amount,0) else 0 end)-round(sum(quantity*sd.unit_factor*inout_flag*(-1)*{costPrice})::numeric,2)	)*100
)
/
(
  case when round(sum(sub_amount*inout_flag*(-1))::numeric,2) <>0 
       then round(sum(sub_amount*inout_flag*(-1))::numeric,2)
              else null 
  end
) 

as numeric)";
      columns["free_cost_amount"].SqlFld = $"round(sum((case when sub_amount=0 and trade_type !='J' then -quantity*sd.unit_factor*inout_flag*{costPrice} else 0 end))::numeric,2) ";
      columns["cost_amount"].SqlFld = $"round( SUM ( CASE WHEN trade_type !='J' THEN quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} ELSE 0 END ) :: NUMERIC, 2 )-round(sum((case when sub_amount=0 and trade_type !='J' then -quantity*sd.unit_factor*inout_flag*{costPrice} else 0 end))::numeric,2)";
      columns["profit"].SqlFld = $"round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-sum(case when row_index = 1 then inout_flag*(-1)*coalesce(disc_amount,0) else 0 end)-round(sum(quantity*sd.unit_factor*inout_flag*(-1)*{costPrice})::numeric,2)+round(sum((case when sub_amount=0 and trade_type !='J' then -quantity*sd.unit_factor*inout_flag*{costPrice} else 0 end))::numeric,2)";
      columns["profit_rate"].SqlFld = @$"
cast( 
(
(round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-sum(case when row_index = 1 then inout_flag*(-1)*coalesce(disc_amount,0) else 0 end)-round(sum(quantity*sd.unit_factor*inout_flag*(-1)*{costPrice})::numeric,2)+round(sum((case when sub_amount=0 and trade_type !='J' then -quantity*sd.unit_factor*inout_flag*{costPrice} else 0 end))::numeric,2))*100
) /
(
  case when round(sum(inout_flag*(-1)*sub_amount)::numeric,2) <>0 
       then round(sum(inout_flag*(-1)*sub_amount)::numeric,2) 
              else null 
  end
) 

as numeric)";*/
        }


        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
           SalesSummaryByItemModel.SetCostInfo(this);
       

        }

        /*
        public static void SetCostInfo(PageQueryModel page)
        {
            var costPriceType = "3";
            var costPriceTypeName = "预设进价";
            if (page.JsonCompanySetting.IsValid())
            {
                dynamic setting = Newtonsoft.Json.JsonConvert.DeserializeObject(page.JsonCompanySetting);
                if (setting != null && setting.costPriceType != null) costPriceType = setting.costPriceType;
            }
            var columns = page.Grids["gridItems"].Columns;
            bool seeInPrice = false;
            bool seeProfit = false;
            if (page.JsonOperRights.IsValid())
            {
                dynamic operRights = Newtonsoft.Json.JsonConvert.DeserializeObject(page.JsonOperRightsOrig);
                if (operRights != null && operRights.delicacy != null) seeInPrice = ((string)operRights.delicacy.seeInPrice.value).ToLower() == "true";
                seeProfit = ((string)operRights?.delicacy?.seeProfit.value).ToLower() == "true";
            }
            if (!seeInPrice)
            {
                columns["free_cost_amount"].HideOnLoad = columns["free_cost_amount"].Hidden = true;
                columns["cost_amount"].HideOnLoad = columns["cost_amount"].Hidden = true;
                columns["profit"].HideOnLoad = columns["profit"].Hidden = true;
                columns["profit_rate"].HideOnLoad = columns["profit_rate"].Hidden = true;
                columns["cost_amount_hasfree"].HideOnLoad = columns["cost_amount_hasfree"].Hidden = true;
                columns["profit_hasfree"].HideOnLoad = columns["profit_hasfree"].Hidden = true;
                columns["profit_rate_hasfree"].HideOnLoad = columns["profit_rate_hasfree"].Hidden = true;

                columns["cost_amount_free_cl"].HideOnLoad = columns["cost_amount_free_cl"].Hidden = true;
                columns["profit_free_cl"].HideOnLoad = columns["profit_free_cl"].Hidden = true;
                columns["profit_rate_free_cl"].HideOnLoad = columns["profit_rate_free_cl"].Hidden = true;
                columns["profit_free_cl"].HideOnLoad = columns["profit_free_cl"].Hidden = true;
                columns["profit_rate_free_cl"].HideOnLoad = columns["profit_rate_free_cl"].Hidden = true;
                columns["profit_cut_disc"].HideOnLoad = columns["profit_cut_disc"].Hidden = true;
                columns["profit_rate_cut_disc"].HideOnLoad = columns["profit_rate_cut_disc"].Hidden = true;
                
            }
            if (!seeProfit)
            {
                columns["profit_hasfree"].HideOnLoad = columns["profit_hasfree"].Hidden = true;
                columns["profit_rate_hasfree"].HideOnLoad = columns["profit_rate_hasfree"].Hidden = true;
                columns["profit_free_cl"].HideOnLoad = columns["profit_free_cl"].Hidden = true;
                columns["profit_rate_free_cl"].HideOnLoad = columns["profit_rate_free_cl"].Hidden = true;
                columns["profit"].HideOnLoad = columns["profit"].Hidden = true;
                columns["profit_rate"].HideOnLoad = columns["profit_rate"].Hidden = true;
                columns["profit_cut_disc"].HideOnLoad = columns["profit_cut_disc"].Hidden = true;
                columns["profit_rate_cut_disc"].HideOnLoad = columns["profit_rate_cut_disc"].Hidden = true;
            }
            if (costPriceType == "4") costPriceTypeName = "最近平均进价";
            else if (costPriceType == "1") costPriceTypeName = "预设成本";
            else if (costPriceType == "2") costPriceTypeName = "加权平均成本";

            page.DataItems["cost_price_type"].Value = costPriceType;
            page.DataItems["cost_price_type"].Label = costPriceTypeName;


            //Console.WriteLine("Item页面的SetCostInfo方法获取的costPriceType:" + costPriceType);

        }

        */



        public async Task OnGet()
        { 
            await InitGet(cmd);
        }
    }



    [Route("api/[controller]/[action]")]
    public class SalesSummaryBySellerController : QueryController
    { 
        public SalesSummaryBySellerController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<JsonResult> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            SalesSummaryBySellerModel model = new SalesSummaryBySellerModel(cmd);
            return await PageBaseModel.GetDataItemOptionsJson(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);

        }
        [HttpPost]
        public async Task<object> GetQueryRecords([FromBody] dynamic data )
        {
            string sheetType = data.sheetType; 
            //string cost_price_type_name,
            var main_table = "sheet_sale_main";
            var detail_table = "sheet_sale_detail";
            if (sheetType == "xd")
            {
                main_table = "sheet_sale_order_main";
                detail_table = "sheet_sale_order_detail";
            }
            SalesSummaryBySellerModel model = new SalesSummaryBySellerModel(cmd);
            var sql = model.Grids["gridItems"].QueryFromSQL;
            sql = sql.Replace("~mainTable", main_table);
            sql = sql.Replace("~detailTable", detail_table);
            model.Grids["gridItems"].QueryFromSQL = sql;
            object records = await model.GetRecordFromQuerySQL(Request, cmd, data);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            string sParams = Request.Form["params"];
            sParams = System.Web.HttpUtility.UrlDecode(sParams);
            dynamic queryParams = JsonConvert.DeserializeObject(sParams);
            string sheetType = queryParams.sheetType;

            var main_table = "sheet_sale_main";
            var detail_table = "sheet_sale_detail";
            if (sheetType == "xd")
            {
                main_table = "sheet_sale_order_main";
                detail_table = "sheet_sale_order_detail";
            }
            SalesSummaryBySellerModel model = new SalesSummaryBySellerModel(cmd);
            var sql = model.Grids["gridItems"].QueryFromSQL;
            sql = sql.Replace("~mainTable", main_table);
            sql = sql.Replace("~detailTable", detail_table);
            model.Grids["gridItems"].QueryFromSQL = sql;
            return await model.ExportExcel(Request, cmd, queryParams);
        }

    }
}
