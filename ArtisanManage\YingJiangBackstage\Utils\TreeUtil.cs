﻿using System.Collections.Generic;
using System.Linq;
using ArtisanManage.Services;
using ArtisanManage.YingJiangBackstage.Pojo;

namespace ArtisanManage.YingJiangBackstage.Utils
{
    public static class TreeUtil
    {
        internal static List<TreeData> TreeDataToTree<T>(this List<T> arr) where T:TreeData
        {
            Dictionary<int, List<TreeData>> groups = new Dictionary<int, List<TreeData>>();
            foreach (T item in arr)
            {
                if (!groups.ContainsKey(item.ParentId))
                {
                    groups.Add(item.ParentId, new List<TreeData>());
                }

                if (groups.ContainsKey(item.Value))
                {
                    item.Children = groups[item.Value];
                }
                else
                {
                    item.Children = new List<TreeData>();
                    groups.Add(item.Value, item.Children);
                }
                List<TreeData> jarr = groups[item.ParentId];
                jarr.Add(item);
            }
            return groups[0];
        }
        internal static List<TreeData_Common> TreeDataToTree_Common<T>(this List<T> arr) where T : TreeData_Common
        {
            Dictionary<int, List<TreeData_Common>> groups = new Dictionary<int, List<TreeData_Common>>();
            foreach (T item in arr)
            {
                if (!groups.ContainsKey(item.ParentId))
                {
                    groups.Add(item.ParentId, new List<TreeData_Common>());
                }

                if (groups.ContainsKey(item.Value))
                {
                    item.Children = groups[item.Value];
                }
                else
                {
                    item.Children = new List<TreeData_Common>();
                    groups.Add(item.Value, item.Children);
                }
                List<TreeData_Common> jarr = groups[item.ParentId];
                jarr.Add(item);
            }
            return groups[0];
        }
        internal static List<ModuleWorkOrderTree> ModuleTreeDataToTree<T>(this List<T> arr) where T : ModuleWorkOrderTree
        {
            Dictionary<int, List<ModuleWorkOrderTree>> groups = new Dictionary<int, List<ModuleWorkOrderTree>>();
            foreach (T item in arr)
            {
                if (!groups.ContainsKey(item.ParentId))
                {
                    groups.Add(item.ParentId, new List<ModuleWorkOrderTree>());
                }

                if (groups.ContainsKey(item.Value))
                {
                    item.Children = groups[item.Value];
                }
                else
                {
                    item.Children = new List<ModuleWorkOrderTree>();
                    groups.Add(item.Value, item.Children);
                }
                List<ModuleWorkOrderTree> jarr = groups[item.ParentId];
                jarr.Add(item);
            }
            return groups[0];
        }
    }
}