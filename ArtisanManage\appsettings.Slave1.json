{
  "ConnectionString": {

    //"Postgresql": "slave|9PKrrXq3vyZ2xPf68kAEpIh3ym/FiVATDaxVhWDvMaTYbQxNDhdXfVkf75vH2uIz8LN8FPN1c0Zch/ccDjaUbORv6oT0DrvR/QhGC9/DQ7FzbKUDVTSCp9v19VEoGRP+|server=192.168.0.159;",
    //"Report": "slave|9PKrrXq3vyZ2xPf68kAEpIh3ym/FiVATDaxVhWDvMaTYbQxNDhdXfVkf75vH2uIz8LN8FPN1c0Zch/ccDjaUbORv6oT0DrvR/QhGC9/DQ7FzbKUDVTSCp9v19VEoGRP+|server=192.168.0.159;",
    "Postgresql": "root|9PKrrXq3vyZ2xPf68kAEpIh3ym/FiVATDaxVhWDvMaRiBOum4tesLwsS67YumlW5p41j0NuqTOzFfMxHN7nHrAG0X5Y0wncJNwgW8N5LLAI=|server=*************;",
    "Report": "root|9PKrrXq3vyZ2xPf68kAEpIh3ym/FiVATDaxVhWDvMaRiBOum4tesLwsS67YumlW5p41j0NuqTOzFfMxHN7nHrAG0X5Y0wncJNwgW8N5LLAI=|server=*************;",
    //"Report": "pool/reader|CuoCI4wTIxnkjEh+72nN52pBDF1WWY7xdeaaC8I8G5g0WOCsAlfAz+w1xuHlpc4u/HQK7pdcTIBmp74QyzNUH2EyEB9XAT140CsMa78vCY5XtldL0fSWqv7wUqILxY9R1VQUEBNdhEGppfq8Y+bk+d+hH4rXjY4d",
    //"SshAgent": "Ik1StHqNAhTskLEgik2SKjg7LKaQ7Xmaij0pwV1DwM9KJ9K+GMeM7TlFS7f0zTw9XE179HVHpKSxwXiuX8D13g=="
    "SshAgent": "host=************;port=22;user=root;pwd=*********; "
  },
  "RedisConnectionString": { //redis连接
    "Connection": "manage.yingjiang.co:6379,password=YingJiangYuChen_168168, abortConnect=false", //外网
    // "Connection": " r-2zedctlp8py8vllrrp.redis.rds.aliyuncs.com:6379,password=19liangpin_, abortConnect=false",//内网
    "InstanceName": "19liangpin_Redis"
  },
  //"WebRootUrl": "http://*************",
  "WebRootUrl": "http://127.0.0.1",
  "Obs": {
    "BucketLinkHref": "https://yingjiang.obs.cn-east-3.myhuaweicloud.com",
    "AccessKey": "0FXR34PKUZ25DPOPCSZA",
    "SecretKey": "87ivHMCeIUmkeRUu1wtKMSqBXQYQGrvIGo83790L",
    "CurrentEndPoint": "obs.cn-east-3.myhuaweicloud.com",
    "CurrentBucket": "yingjiang"
  },
  "General": {
    "LogDatabase": "server=************;port=5432;database=artisan_001;user id=postgres;password=*********;MaxPoolSize=200;",
    "LogSqlLevel": "0",
    "LogMsgLevel": "0"
  },
  "Other": {
    "SettingBrief": "Slave1",
    "BriefColor": "#f00"
  }
}
