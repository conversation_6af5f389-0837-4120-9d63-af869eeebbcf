
@page
@model ArtisanManage.Pages.BaseInfo.GetArrearsSheetViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head id="Head1" runat="server">
    <partial name="_QueryPageHead" model="Model.PartialViewModel"/>
    <script type="text/javascript">
           window.g_operKey = '@Html.Raw(Model.OperKey)';
        var Href = '@Html.Raw(Model.ObsBucketLinkHref)'
        var newCount = 1;

        var tmpsAndCprters = {};
        var tmp_sk = 0;
        var tmp_loaded = false;

    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)
            
                $("#gridItems").on("cellclick", function (event) {
                    // event arguments.
                    var args = event.args;
                    console.log(args.row);
                    if (args.datafield == "sheet_no") { 
                        var sheet_id = args.row.bounddata.sheet_id;
                        var sheet_type = args.row.bounddata.sheet_type;
                        var forPayOrGet = false
                        if (sheet_type == "付款单") forPayOrGet = true
                       // rowIndex = args.rowindex;
                   
                        //var sheet_type=$('#gridItems').jqxGrid('getcellvalue', rowIndex, 'sheet_type');
                        window.parent.newTabPage(sheet_type, `Sheets/GetArrearsSheet?sheet_id=${sheet_id}&forPayOrGet=${forPayOrGet}`,window); 
                    } else if (args.datafield == "check_appendix_photos") {
                        // 附件上传相关

                        var cellValue = args.row.bounddata.check_appendix_photos;
                        if (cellValue !== "查看") {
                            return; //无"查看"时直接退出，不触发弹窗
                        }

                        debugger
                        var sheet_id = args.row.bounddata.sheet_id;
                        var sheet_type_name = args.row.bounddata.sheet_type;
                        var sheet_type = null
                        debugger
                        if (sheet_type_name === "付款单") {

                            sheet_type = "FK"
                        } else if (sheet_type_name === "收款单") {

                            sheet_type = "SK"
                        }
                        var photo = args.row.bounddata.appendix_photos;
                        // var href = Href + '/'
                        var href = Href + '/uploads'
                        $('#popAppendix').jqxWindow('open');
                        if (photo) {
                            $("#popAppendix").jqxWindow('setContent', `<iframe id="iframeAppendix" src="/BaseInfo/AppendixPhotoEdit?operKey=${g_operKey}&sheetId=${sheet_id}&editable=false" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                            $('#iframeAppendix').on('load', function () {
                                // 获取 iframe 元素
                                let iframeElement = document.getElementById('iframeAppendix');
                                // 构造要发送的数据，有照片的情况下可以直接发送
                                iframeElement.contentWindow.postMessage({ msg: "loadPhotoView", photo: photo, href: href }, '*');

                            });
                        } else {
                            // 没有照片的情况下可以发送sheetId sheetType进行查找
                            $("#popAppendix").jqxWindow('setContent', `<iframe id="iframeAppendix" src="/BaseInfo/AppendixPhotoEdit?operKey=${g_operKey}&sheetId=${sheet_id}&sheetType=${sheet_type}&href=${href}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                        }
                    }
                });

                $("#gridItems").jqxGrid('beforeRowRender', function (divRow, rowData) {
                    if (rowData.sheet_status == '已红冲') 
                        divRow.style.color='#aaa' 
                    else if (rowData.sheet_status == '红字单')
                        divRow.style.color='#f00'                  
                    else
                        divRow.style.color='#000'

                })

                $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 300, width: 500, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
                $("#popAppendix").jqxWindow({ closeButtonAction: 'close', isModal: true, modalOpacity: 0.3, height: 600, width: 900, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

            QueryData();
            });

        function btnExportTable_click() {
            var bPrintEach = true, bPrintSum = false;

            var smallUnitBarcode = document.all.ckSmallUnitBarcode.checked;

            var checkedRows = window.g_checkedRows
            var sheetIDs = ''
            for (var id in checkedRows) {
                if (sheetIDs != '') sheetIDs += ","
                sheetIDs += id
            }
            if (!sheetIDs) {
                bw.toast('请至少勾选一个单据')
                return
            }
            $.ajax({
                url: '/api/GetArrearSheetView/GetMultiSheetsToPrint',
                type: 'GET',
                contentType: 'application/json',
                // data: JSON.stringify({sheet_id:'1',supcust_id:'2'}),
                data: {
                    operKey: g_operKey, sheetIDs: sheetIDs, bPrintSum: bPrintSum, bPrintEach: bPrintEach, smallUnitBarcode: smallUnitBarcode,
                    sortColumn: window.sortColumn || '', sortDirection: window.sortDirection || ''
                },
                success: function (result) {

                    if (result.result === 'OK') {
                        vm.templeSheets = result.sheetGroup;
                        vm.$nextTick(function () {
                            $('#divExportExcel .data-label').css('text-align', 'right')
                            $('#divExportExcel .data-value').css('text-align', 'left')
                            $('#divExportExcel .sheet-table').css('border', '2px solid #f6f0f0');//.css('background-color','#f9f9f9')
                            $('#divExportExcel .sheet-table tbody td').css('border', '0.5px solid #777')//.css('mso-number-format','\@@')
                            $('#divExportExcel .sheet-table tr td:first-child').css('border-left-style', 'none').css('border-bottom-style', 'none').css('border-top-style', 'none').css('width', '20px')

                            table2excel("divExportExcel");
                        });
                    }
                    else {
                        bw.toast(result.msg, 3000);
                    }
                },
                error: function (xhr) {
                    console.log("返回响应信息：" + xhr.responseText);
                }
            })
        }

        function btnTemplateSelected(type, value) {
            switch (type) {
                case 'sk':
                    tmp_sk = value;
                    localStorage.setItem('_GetArrearsSheetView_TemplateSelected_SK', value);
                    break;
                default: return;
            }
        }

        function btnPrintSheets_hover() {
            window.g_operRights = @Html.Raw(Model.JsonOperRightsOrig);
            var canApprove = window.getRightValue('money.sheetGetArrears.approve').toLowerCase() == "true"
            var canReview = window.getRightValue('money.sheetGetArrears.review').toLowerCase() == "true"
            if (!canApprove) $('#approveConfirm').css('display', 'none');
            if (!canReview) $('#reviewConfirm').css('display', 'none');

            if(!tmp_loaded){
                var dropSK = document.getElementById('tselect_sk');
                dropSK.options.length = 0;

                // Load Templates and CloudPrintersList.
                $.ajax({
                    url: '/api/PrintTemplate/GetTemplatesForMultiSheetsPrint',
                    type: 'GET',
                    contentType: 'application/json',
                    data: {
                        operKey: g_operKey
                    },
                    success: function (printInfo) {
                        if (tmp_loaded) return;
                        tmp_loaded = true;
                        tmpsAndCprters.cloudPrinters = printInfo.cloudPrinters;
                        tmpsAndCprters.tmps = {};
                        tmpsAndCprters.tmps.SKList = printInfo.templateList_SK;
                        console.log("Load Templates and CloudPrintersList Done.")
                        console.log(tmpsAndCprters)

                        // 填充收款单模板下拉框
                        for (var i = 0; i < tmpsAndCprters.tmps.SKList.length; i++) {
                            dropSK.options.add(new Option(tmpsAndCprters.tmps.SKList[i].template_name, i));
                        }

                        // Load Cache for SK
                        dropSK.value = 0;
                        var skCache = localStorage.getItem('_GetArrearsSheetView_TemplateSelected_SK');
                        if (skCache && skCache<dropSK.options.length) {
                            tmp_sk = skCache;
                            dropSK.value = skCache;
                        }

                        if (tmpsAndCprters.tmps.SKList.length == 0) {
                            tmp_sk = -1;
                        }
                    },
                    error: function (xhr) {
                        tmp_loaded = false;
                        bw.toast('读取模板/云打印机失败')
                        console.log("返回响应信息：" + xhr.responseText)
                    }
                })
            }
            var btnPrintSheets = $('#btnPrintSheets');
            var offset = btnPrintSheets.offset();
            var btnWidth = $("#btnPrintSheets").width()
            var popWidth = $("#popoverPrint").width()
            $("#popoverPrint").css('left', offset.left - popWidth + btnWidth);
            $("#popoverPrint").css('top', offset.top + btnPrintSheets.height());
            $("#popoverPrint").css('visibility', 'visible');
            $("#popoverPrint").show()
            $('#btnShowSender').show()
            $('#divSenderToSet').hide()
            $("#popoverPrint,#btnPrintSheets").on('mouseleave', (e) => {
                //   $("#popoverPrint").on('mouseleave', (e) => {
                if (e.relatedTarget) {
                    if (e.relatedTarget.id == 'popoverPrint' || e.relatedTarget.id == 'btnPrintSheets' || e.relatedTarget.id == '_popup') {
                        return;
                    }
                }
                $("#popoverPrint").css('visibility', 'hidden');
                $("#popoverPrint").hide()
            })

        }
        function approveConfirm_click() {

            var checkedRows = window.g_checkedRows
            var sheetIDs = ''
            for (var id in checkedRows) {
                if (sheetIDs != '') sheetIDs += ","
                sheetIDs += id
            }
            if (!sheetIDs) {
                bw.toast('请至少勾选一个单据')
                return
            }

            $.ajax({
                url: '/api/GetArrearsSheetView/GetSheetsToApprove',
                type: 'GET',
                contentType: 'application/json',
                data: {
                    operKey: g_operKey, sheetIDs: sheetIDs
                },
                success: function (data) {
                    console.log(data);
                    if (data.result === 'OK') {
                        bw.toast('操作成功');
                        QueryData()
                    }

                    else {
                        bw.toast(data.result, 3000);
                    }
                },
            })

        }
        function reviewConfirm_click() {
            var checkedRows = window.g_checkedRows
            var sheetIDs = ''
            for (var id in checkedRows) {
                if (sheetIDs != '') sheetIDs += ","
                sheetIDs += id
            }
            if (!sheetIDs) {
                bw.toast('请至少勾选一个单据')
                return
            }

            $.ajax({
                url: '/api/GetArrearsSheetView/GetSheetsToReview',
                type: 'GET',
                contentType: 'application/json',
                data: {
                    operKey: g_operKey, sheetIDs: sheetIDs
                },
                success: function (data) {
                    console.log(data);
                    if (data.result === 'OK') {
                        bw.toast('操作成功');
                        QueryData()
                    }

                    else {
                        bw.toast(data.result, 3000);
                    }
                },
            })

        }

        function btnPrintConfirm_click() {
            var bPrintEach = false, bPrintSum = false, bPrintSheetsMainInfo = false;

            if (document.all.ckPrintSheets.checked) {
                bPrintEach = true;
            }

            if (!bPrintEach) {
                bw.toast('请勾选打印勾选单据')
                return
            }

            var checkedRows = window.g_checkedRows
            var sheetIDs = ''
            for (var id in checkedRows) {
                if (sheetIDs != '') sheetIDs += ","
                sheetIDs += id
            }

            if (!sheetIDs) {
                bw.toast('请至少勾选一个单据')
                return
            }

            var clientVersion = 0
            if (window.parent.CefGlue) {
                clientVersion = window.parent.g_clientVersion
            }

            $.ajax({
                url: '/api/GetArrearsSheetView/GetMultiSheetsToPrint',
                type: 'GET',
                contentType: 'application/json',
                data: {
                    operKey: g_operKey,
                    sheetIDs: sheetIDs,
                    bPrintSum: bPrintSum,
                    bPrintEach: bPrintEach,
                    clientVersion: clientVersion,
                    sortColumn: window.sortColumn || '',
                    sortDirection: window.sortDirection || ''
                },
                success: function (data) {
                    console.log(data);
                    if (data.result === 'OK') {
                        var container = window.parent.CefGlue
                        window.parent.g_SheetsWindowForPrint = window
                        var sheetGroup = data.sheetGroup;

                        if(tmp_sk == -1){
                            bw.toast('没有收款单模板',3000)
                            return
                        }

                        var tmpSK = tmpsAndCprters.tmps.SKList[tmp_sk];

                        sheetGroup.forEach(grp => {
                            if (grp.groupType == 'EACH') {
                                grp.template = JSON.parse(tmpSK.template_content);
                                for (var i = 0; i < grp.sheets.length; i++) {
                                    var sht = grp.sheets[i]
                                    // 所有单据都使用收款单模板
                                    sht.printTemplate = JSON.parse(tmpSK.template_content);
                                }
                            }
                        })

                        console.log(sheetGroup)
                        if (!container || !container.printSheetByTemplate) {
                            bw.toast('在客户端程序中才可以打印', 3000)
                            return
                        }

                        let cloudPrinters = tmpsAndCprters.cloudPrinters;
                        var templVariables = data.templVariables;
                        var promise = container.printMultiSheets(sheetGroup, true, cloudPrinters, templVariables)
                    }
                    else {
                        bw.toast(data.msg, 3000);
                    }
                },
                error: function (xhr) {
                    console.log("返回响应信息：" + xhr.responseText);
                }
            })
        }

    </script>
</head>

<body style="overflow:hidden">
 
    <div style="display:flex;padding-top:20px;">

        <div id="divHead" class="headtail">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <button onclick="QueryData()" style="margin-left:20px;">查询</button>
        @* <button id="btnPrintSheets" onmouseenter="btnPrintSheets_hover()"  onclick="ExportExcel()" style="margin-left:20px;">导出</button> *@
        <button id="btnPrintSheets" onmouseenter="btnPrintSheets_hover()" style="margin-left:20px;">批量操作</button>
    </div>
   
    <div id="gridItems" style="margin-bottom:2px;width:calc(100% - 20px);height:calc(100% - 95px);"></div>
    <div id="divRowCount"><div style="float:right;margin-right:50px;height:20px;font-size:12px;color:#999;">共<label id="rows_count">0</label>行</div></div>
  

    <div id="popItem" style="display:none;">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">单位信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    <style>
        #popoverPrint > div {
            margin: 20px;
        }

        #popoverPrint a {
            color: #8af;
            cursor: pointer;
        }

        #divExportOuter > div {
            margin-top: 10px;
        }


    </style>
    <div id="popoverPrint" class="popForPrint" style="width: 250px; height: auto; position: absolute; z-index: 1000; background: #fff; border: 2px solid #ddd; border-radius: 10px; display: block; visibility: hidden; font-size: 15px;">

        <div style="margin:20px;margin-bottom:10px">
            <input class="magic-checkbox" type="checkbox" id="ckPrintSheets" /><label for="ckPrintSheets">打印勾选单据</label>
        </div>

        <div>
            <div id="SelectSKTmp" style="display:flex;flex-flow:wrap;margin-top:10px;margin-bottom:10px">
                收款单模板：
                <select id="tselect_sk" onchange="btnTemplateSelected('sk',this.value)">
                </select>
            </div>
        </div>

        <div id="divExportOuter" style="margin-bottom:20px;">
            <button id="btnPrintConfirm" onclick="btnPrintConfirm_click()">打印</button>
            <button id="approveConfirm" onclick="approveConfirm_click()">审核</button>
            <button id="reviewConfirm" onclick="reviewConfirm_click()">复核</button>
            <div>
                <a onclick="ExportExcel()">导出</a>
            </div>
        </div>

    </div>
    @* 附件查看弹窗 *@
    <div id="popAppendix" style="display:none;">
        <div id="appendixCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">上传附件</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
</body>
</html>