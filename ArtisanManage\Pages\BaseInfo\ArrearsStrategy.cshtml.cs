using ArtisanManage.Models;
using System.Collections.Generic;
using static ArtisanManage.Models.DataItem;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using ArtisanManage.Services;
using System;
using System.ComponentModel.Design;
using Microsoft.CodeAnalysis.Elfie.Diagnostics;

namespace ArtisanManage.Pages.BaseInfo
{
    public class ArrearsStrategyModel : PageFormModel
    {
        public bool ForSelect = false;
        public ArrearsStrategyModel(CMySbCommand cmd) : base(Services.MenuId.arrearsStrategy)
        {
            this.cmd = cmd;
            this.NoIDFld = true;
            Grids = new Dictionary<string, FormDataGrid>()
            {
                {
                    "gridItems", new FormDataGrid()
                    {
                        TableName = "arrears_strategy_class",
                        MinRows = 1,
                        AutoAddRow=true,
                        AllowDragRow=true,
                        AllowInsertRemoveRow=true,
                        Columns = new Dictionary<string, DataItem>()
                        {
                            {"client_class", new DataItem()
                                {
                                    Title = "客户类别",
                                    FuncGetSubColumns = async (col) =>
                                    {
                                        ColumnsResult result=new ColumnsResult();
                                        Dictionary<string,DataItem> subColumns=new Dictionary<string,DataItem>(){
                                            {"group_id",new DataItem(){Title="渠道",LabelFld="group_name",Width="160",SqlForOptions="select group_id,group_name from info_supcust_group",GetOptionsOnLoad=true,ButtonUsage="list"}},
                                            {"region_id",new DataItem(){Title="片区",LabelFld="region_name",Width="160",SqlForOptions="select region_id,region_name from info_region",GetOptionsOnLoad=false,ButtonUsage="list",MaxRecords = "500"} },
                                            {"rank_id",new DataItem(){Title="等级",LabelFld="rank_name",Width="160",SqlForOptions="select rank_id,rank_name from info_supcust_rank",GetOptionsOnLoad=false,ButtonUsage="list"}},

                                        };
                                        result.Columns=subColumns;
                                        return result;
                                    }
                                }
                            },
                            {"max_arrears",new DataItem(){Title = "欠款额度",Width = "200",Sortable = true} },
                            {"max_arrears_days",new DataItem(){Title = "欠款天数",Width = "200",Sortable = true} },
                            {"max_arrears_bills",new DataItem(){Title = "欠款单数",Width = "200",Sortable = true} }

                        },
                        SelectFromSQL = $@"from arrears_strategy_class
                                        left join (select region_id reid,region_name from info_region where company_id= '~COMPANY_ID') r on arrears_strategy_class.region_id = r.reid
                                        left join (select group_id gid,group_name from info_supcust_group where company_id= '~COMPANY_ID') g on g.gid = arrears_strategy_class.group_id
                                        left join (select rank_id rid,rank_name from info_supcust_rank where company_id= '~COMPANY_ID') sr on sr.rid = arrears_strategy_class.rank_id
                                        where arrears_strategy_class.company_id='~COMPANY_ID' order by flow_id"
                    }
                },
                {
                    "gridClients", new FormDataGrid()
                    {
                        TableName = "arrears_strategy_client",
                        MinRows = 1,
                        AutoAddRow=true,
                        AllowDragRow=true,
                        AllowInsertRemoveRow=true,
                        Columns = new Dictionary<string, DataItem>()
                        {
                             {"supcust_id",new DataItem(){Title="客户/结算单位", LabelFld="sup_name",SearchFields="['sup_name','py_str']", SqlForOptions="select supcust_id,sup_name,py_str from info_supcust where company_id = '~COMPANY_ID' and acct_cust_id is null", Width="180",GetOptionsOnLoad=false,ButtonUsage="list"}},
                             {"max_arrears",new DataItem(){Title = "欠款额度",Width = "200",Sortable = true} },
                             {"max_arrears_days",new DataItem(){Title = "欠款天数",Width = "200",Sortable = true} },
                             {"max_arrears_bills",new DataItem(){Title = "欠款单数",Width = "200",Sortable = true} },
                             {"balance",new DataItem(){Title="欠款金额",SqlFld = "COALESCE(ab.balance,0)",Width="200",SaveToDB = false,EditableInFormGrid = false,Sortable = true}},
                             {"pend_amount",new DataItem(){Title="欠款金额(订单)",SqlFld = "COALESCE(ab.pend_amount,0)",Width="200",SaveToDB = false,EditableInFormGrid = false,Sortable = true}},
                             {"total_amount",new DataItem(){Title="欠款金额(总)",SqlFld = "COALESCE(ab.balance,0) + COALESCE(ab.pend_amount,0)",Width="200",SaveToDB = false,EditableInFormGrid = false,Sortable = true}},
                        },
                        SelectFromSQL=$@"from arrears_strategy_client
                                        left join (select supcust_id sid,sup_name from info_supcust where company_id='~COMPANY_ID') i  on i.sid=arrears_strategy_client.supcust_id
                                        left join arrears_balance ab on arrears_strategy_client.supcust_id=ab.supcust_id and ab.company_id=~COMPANY_ID
                                        where arrears_strategy_client.company_id='~COMPANY_ID' order by flow_id",
                    }
                },
                {
                    "gridOperators", new FormDataGrid()
                    {
                        TableName = "arrears_strategy_operator",
                        MinRows = 1,
                        AutoAddRow=true,
                        AllowDragRow=true,
                        AllowInsertRemoveRow=true,
                        Columns = new Dictionary<string, DataItem>()
                        {
                            {"oper_id", new DataItem() {Title = "业务员", LabelFld = "oper_name",SearchFields="['oper_name','py_str']",SqlForOptions = "select oper_id,oper_name,py_str from info_operator where company_id = '~COMPANY_ID' and COALESCE(status,1) <> 0",Width = "200", GetOptionsOnLoad=false,ButtonUsage="list"} },
                            {"max_arrears",new DataItem(){Title = "欠款额度",Width = "200",Sortable = true} },
                            {"max_arrears_days",new DataItem(){Title = "欠款天数",Width = "200",Hidden = true,HideOnLoad = true,Sortable = true} },
                            {"auxiliary_balance",new DataItem(){Title="欠款金额",SqlFld = "aux.auxiliary_balance",Width="200",SaveToDB = false,EditableInFormGrid = false,Sortable = true}},
                        },
                        SelectFromSQL=$@"from arrears_strategy_operator
                                        left join (select oper_id,oper_name,py_str from info_operator where company_id='~COMPANY_ID') i  on i.oper_id=arrears_strategy_operator.oper_id
                                        left join arrears_balance_auxiliary aux on arrears_strategy_operator.oper_id=aux.auxiliary_id and aux.auxiliary_type='seller'
                                        where arrears_strategy_operator.company_id='~COMPANY_ID' order by flow_id",
                    }
                }
            };
        }
        public async Task OnGet(string forSelect)
        {
			OperKey = CPubVars.RequestV(Request, "operKey");
			if (OperKey == null || OperKey == "")
			{
				throw (new Exception("operKey not specified in request"));
			}
			Security.GetInfoFromOperKey(OperKey, out string companyID, out string operID);
			var result = await CommonTool.CheckToUpdateDb(cmd, companyID,"", true);//在打开欠款策略的时候，对三张表进行数据迁移，以后要删除此方法。
			await InitGet(cmd);
            ForSelect = forSelect == "1";
            
        }
    }

    [Route("api/[controller]/[action]")]
    public class ArrearsStrategyController : BaseController
    {
        public ArrearsStrategyController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }
        [HttpGet]
        public async Task<string> GetColumnOptions(string operKey, string gridID, string colName, string flds, string value, string availValues)
        {
            ArrearsStrategyModel model = new ArrearsStrategyModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.Grids[gridID].Columns, colName, flds, value, availValues);
            return data;
        }

        [HttpPost]
        public async Task<object> Save([FromBody] dynamic data)
        {
            ArrearsStrategyModel model = new ArrearsStrategyModel(cmd);
            dynamic gridClients = data.gridClients;
            for (int i = 0; i < gridClients.Count; i++)
            {
                string gc_supid = gridClients[i].supcust_id;
                if (string.IsNullOrEmpty(gc_supid))
                {
                    return new JsonResult(new { result = "Error", msg = $"第{i + 1}行未选择客户，必须指定客户，请检查，此次保存失败" });
                }
                string gc_max_arrears = gridClients[i].max_arrears;
                string gc_max_arrears_days = gridClients[i].max_arrears_days;
                string gc_max_arrears_bills = gridClients[i].max_arrears_bills;

                if (string.IsNullOrEmpty(gc_max_arrears) && string.IsNullOrEmpty(gc_max_arrears_days) && string.IsNullOrEmpty(gc_max_arrears_bills))
                {
                    return new JsonResult(new { result = "Error", msg = $"第{i + 1}行指定了客户，但是欠款额度、欠款天数和欠款单数同时为空，请检查，此次保存失败" });
                }
                for (int j = i + 1; j < gridClients.Count; j++)
                {
                    if (gridClients[i].supcust_id == gridClients[j].supcust_id)
                    {
                        string result = "Error";
                        string msg = $"第{i + 1}行客户与第{j + 1}行客户出现重复,客户名称{gridClients[i].sup_name}，请检查，此次保存失败";
                        return new JsonResult(new { result, msg });
                    }
                }
            }

            //下面的作用是根据欠款策略的客户表，对比客户档案的欠款额度数据，进而更新客户档案的欠款额度和天数的数据
            CMySbTransaction tran = cmd.Connection.BeginTransaction();
            try
            {
                string company_id = cmd.company_id;
                /*
                string querySql = $"select supcust_id,max_arrears,max_arrears_days from info_supcust where company_id = {company_id} and supcust_flag like '%C%' and status = '1' and max_arrears is not null;";
                dynamic clientsRecord = await CDbDealer.GetRecordsFromSQLAsync(querySql, cmd);
                string exsql = "";
                if (gridClients.Count > 0 && clientsRecord.Count > 0)
                {
                    foreach (dynamic client in clientsRecord)
                    {
                        foreach (dynamic gridClient in gridClients)
                        {
                            string c_sup_id = client.supcust_id;
                            string gc_sup_id = gridClient.supcust_id;
                            string cma = client.max_arrears;
                            string cmad = client.max_arrears_days;
                            string gcma = gridClient.max_arrears;
                            string gcmad = gridClient.max_arrears_days;

                            if (c_sup_id == gc_sup_id && (cma != gcma || cmad != gcmad))
                            {//同一个客户，但是欠款额度和欠款天数不一致，以欠款策略这边为准

                                gcma = gcma == "" ? "null" : gcma;
                                gcmad = gcmad == "" ? "null" : gcmad;
                                exsql += $"update info_supcust set max_arrears={gcma},max_arrears_days={gcmad} where company_id = {company_id} and supcust_id = {gc_sup_id};";
                            }
                        }
                    }
                }
                if (!string.IsNullOrEmpty(exsql))
                {
                    cmd.CommandText = exsql;
                    await cmd.ExecuteNonQueryAsync();
                }
                List<string> sup_id_list = new List<string>();
                foreach(dynamic client in gridClients)
                {
                    string tmp = client.supcust_id;
                    sup_id_list.Add(tmp);
                }
                if(sup_id_list.Count > 0)//欠款策略客户表里面没有的数据，把客户档案的更新为null
                {
                    string ids = String.Join(",", sup_id_list);
                    exsql = $"update info_supcust set max_arrears=null, max_arrears_days=null where company_id = {company_id} and supcust_id not in ({ids});";
                    cmd.CommandText = exsql;
                    await cmd.ExecuteNonQueryAsync();
                }
                */
                var result = await model.SaveTable(cmd, data, tran);

                //回写 info_supcust info_operator 表中数据，以便于兼容老程序
                string sql = @$"
update info_supcust s set max_arrears =c.max_arrears from arrears_strategy_client c where s.company_id={company_id} and c.company_id={company_id} and s.supcust_id=c.supcust_id and c.max_arrears<>coalesce(s.max_arrears, 0);
update info_supcust s set max_arrears =null where s.company_id={company_id} and s.max_arrears is not null and not s.supcust_id in (select supcust_id from  arrears_strategy_client where company_id={company_id});

update info_operator s set seller_max_arrears =c.max_arrears from arrears_strategy_operator c where s.company_id={company_id} and c.company_id={company_id} and s.oper_id=c.oper_id and c.max_arrears<>coalesce(s.seller_max_arrears, 0);
update info_operator s set seller_max_arrears =null where s.company_id={company_id} and s.seller_max_arrears is not null and not s.oper_id in (select oper_id from  arrears_strategy_operator where company_id={company_id});

";

                //刷员工档案欠款金额
                dynamic strategy_opers = await CDbDealer.GetRecordsFromSQLAsync($"select distinct oper_id from arrears_strategy_operator where company_id={company_id}", cmd);
                foreach(dynamic stg_oper in strategy_opers)
                {
                    string sqlArrears = $@"
select round(sum(left_amount)::numeric,2) sum_left_amount 
from 
(
    select sum(round((COALESCE ( total_amount, 0 ) - COALESCE ( disc_amount, 0 ) - COALESCE ( paid_amount, 0 ))::numeric,2)*money_inout_flag) left_amount  from sheet_sale_main where company_id={company_id} and seller_id= {stg_oper.oper_id} and round((COALESCE ( total_amount, 0 ) - COALESCE ( disc_amount, 0 ) - COALESCE ( paid_amount, 0 ))::numeric,2)<>0 and approve_time is not null union
    select sum(round((COALESCE ( total_amount, 0 ) - COALESCE ( disc_amount, 0 ) - COALESCE ( paid_amount, 0 ))::numeric,2)*money_inout_flag) left_amount  from sheet_prepay where company_id={company_id}  and getter_id={stg_oper.oper_id} and round((COALESCE ( total_amount, 0 ) - COALESCE ( disc_amount, 0 ) - COALESCE ( paid_amount, 0 ))::numeric,2)<>0  and approve_time is not null union
    select sum(round((COALESCE ( total_amount, 0 ) - COALESCE ( disc_amount, 0 ) - COALESCE ( paid_amount, 0 ))::numeric,2)*money_inout_flag) left_amount from sheet_fee_out_main where company_id={company_id} and getter_id={stg_oper.oper_id} and round((COALESCE ( total_amount, 0 ) - COALESCE ( disc_amount, 0 ) - COALESCE ( paid_amount, 0 ))::numeric,2)<>0   and approve_time is not null
) t ";

                    dynamic balance = await CDbDealer.Get1RecordFromSQLAsync(sqlArrears, cmd);
                    double sum_left_amount = Convert.ToDouble(balance.sum_left_amount != "" ? balance.sum_left_amount : 0);

                    string earlierDate = CPubVars.GetDateText(DateTime.Now.AddDays(-90));
                    string sqlPendAmount = $@"
select round(sum(left_amount)::numeric,2) sum_left_amount 
from 
(
    select sum(round((COALESCE ( o.total_amount, 0 ) - COALESCE ( o.disc_amount, 0 ) - COALESCE ( o.paid_amount, 0 ))::numeric,2)*o.money_inout_flag) left_amount  
    from sheet_sale_order_main o
    left join sheet_sale_main s on o.sheet_id=s.order_sheet_id and s.company_id=
                {company_id} 
    where o.company_id={company_id} and o.happen_time>'{earlierDate}' and s.happen_time>'{earlierDate}' and o.seller_id= {stg_oper.oper_id} and round((COALESCE ( o.total_amount, 0 ) - COALESCE ( o.disc_amount, 0 ) - COALESCE ( o.paid_amount, 0 ))::numeric,2)<>0  and o.approve_time is not null and s.approve_time is null
    
) t ";

                    dynamic record = await CDbDealer.Get1RecordFromSQLAsync(sqlPendAmount, cmd);
                    double pendAmount = Convert.ToDouble(record.sum_left_amount != "" ? record.sum_left_amount : 0);
                    string sqlUpdateSellerArrears = $"insert into arrears_balance_auxiliary(company_id,auxiliary_type,auxiliary_id,auxiliary_balance,auxiliary_pend_amount) values ({company_id},'seller',{stg_oper.oper_id},{sum_left_amount},{pendAmount}) on conflict(company_id,auxiliary_type,auxiliary_id) do update set auxiliary_balance={sum_left_amount},auxiliary_pend_amount={pendAmount};";
                    sql += sqlUpdateSellerArrears;

                    //record = await CDbDealer.Get1RecordFromSQLAsync($"select auxiliary_balance+auxiliary_pend_amount bal from arrears_balance_auxiliary where company_id={company_id} and auxiliary_id={stg_oper.oper_id}; ", cmd);
                    //if (record != null)
                    //{
                    //    string seller_max_arrears = "";
                    //    if (double.Parse(seller_max_arrears) < double.Parse(record.bal != "" ? record.bal : "0"))
                    //    {
                    //        return new JsonResult(new { result = "Error", msg = $"该业务员已产生过欠款{record.bal}元,超过欠款额度{seller_max_arrears}元" });
                    //    }
                    //}
                }
                

                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();

                tran.Commit();//事务提交
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                MyLogger.LogMsg(model.company_id, "保存失败:" + ex.Message + ex.StackTrace);//打印日志
                tran.Rollback();//事务回滚
                return new JsonResult(new { result = "Error", msg = "保存失败" });//返回错误信息
            }
        }
    }
}