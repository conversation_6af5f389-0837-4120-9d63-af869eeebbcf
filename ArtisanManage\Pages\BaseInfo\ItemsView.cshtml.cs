using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static ArtisanManage.Models.DataItem;

namespace ArtisanManage.Pages.BaseInfo
{
    public class ItemsViewModel : PageQueryModel
    {
        public string m_classTreeStr = "";
        public bool ForSelect = false;
        public bool ForReport = false;
        public string BranchID = "";

        public ItemsViewModel(CMySbCommand cmd) : base(MenuId.infoItem)
        {
            // Debug.WriteLine("OK");
            this.cmd = cmd;
            this.PageTitle = "商品档案";

            DataItems = new Dictionary<string, DataItem>()
            {
                //{"item_query",new DataItem(){title="编号"}}, 
                {"item_brand", new DataItem(){Title = "品牌", LabelFld = "brand_name",Checkboxes=true,Width="200",NullEqualValue="-1", ButtonUsage = "list", QueryOnChange = true,MaxRecords="500",
                        CompareOperator = "=",SqlForOptions = CommonTool.selectBrands}},
                {"searchString",new DataItem(){Title = "检索字符串", PlaceHolder = "输入名称/助记码", UseJQWidgets = false,SqlFld = "item_name,py_str,s_barcode,m_barcode,b_barcode,item_no,avail_attr_combine::text", QueryOnChange = true, CompareOperator = "like",UseFlexLikeQuery=true}},
                {"status",new DataItem(){Title = "状态",LabelFld = "cls_status_name", LabelInDB = false, Value = "normal", Label = "正常",Width="50", ButtonUsage = "list", QueryOnChange = true,  CompareOperator = "=", NullEqualValue = "normal",

                     Source = @"[{v:'normal',l:'正常',condition:""(p.status = '1' or p.status is null)""},
                               {v:'stop',l:'停用',condition:""p.status = '0' ""},
                               {v:'all',l:'所有',condition:""true""}]"

                 }},
                 {"mall_status",new DataItem(){Title = "商城状态",FldArea = "divHead",Hidden=true,LabelFld = "cls_mall_status_name",Width="140", LabelInDB = false, Value = "all", Label = "所有", ButtonUsage = "list", QueryOnChange = true,  CompareOperator = "=", NullEqualValue = "normal",
                     Source = @"[{v:'normal',l:'已上架',condition:""(p.mall_status = 1 or p.mall_status is null)""},
                               {v:'stop',l:'已下架',condition:""p.mall_status = 0 ""},
                               {v:'all',l:'所有',condition:""true""}]"

                 }},
                {"showHasStock",new DataItem(){Title = "显示有库存", ForQuery = false, LabelInDB = false,UseJQWidgets = false}},
                {"branch_id",new DataItem(){Title = "仓库", FldArea = "divHead", Hidden = true, ForQuery = false, LabelInDB = false,HideOnLoad=true}},

                {"supcust_id",new DataItem(){Title = "客户", FldArea = "divHead", Hidden = true,HideOnLoad=true, ForQuery = false, LabelInDB = false}},
                {"sheetType",new DataItem(){Title = "单据类型", FldArea = "divHead", Hidden = true,HideOnLoad=true, ForQuery = false, LabelInDB = false}},
           //     {"showSonItems",new DataItem(){Title = "显示子商品", FldArea = "divHead", Hidden = true,HideOnLoad=true, ForQuery = false, LabelInDB = false}},
                {"other_class", new DataItem(){Title = "父类", LikeWrapper = "/", CtrlType = "jqxTree",MumSelectable = true,GetOptionsOnLoad = true, QueryOnChange = true, CompareOperator = "like",CONDI_DATA_ITEM="item_brand",
                   Value="often",   MaxRecords = "1000",
                   // SqlForOptions ="select class_id as v,class_name as l,batch_level,py_str as z,mother_id as pv,cls_status as status from info_item_class where class_id<>-1 [and (brand_id in (~OPER_BRANDS)  or mother_id =0)] order by order_index,class_name,class_id",
					SqlForOptions ="select class_id as v,class_name as l,batch_level,py_str as z,mother_id as pv,cls_status as status from info_item_class where class_id<>-1 ~BRANDS_CONDI_CLASS order by order_index,class_name,class_id",


				  JSBeforeCreate=@"
var treeHeight = $('#other_class').height()
console.log('treeheight:' + treeHeight)
if (treeHeight <30) {
    treeHeight = $('body').height() - $('#divHead').height() - 55
    $('#other_class').height(treeHeight)
}"
                }},
                {"showAttrSonItems",new DataItem(){Title = "显示口味子商品", FldArea = "divHead", ForQuery = false, CtrlType="jqxCheckBox",Hidden=true}},
                //{"brand_no",new DataItem(){title="品牌",labelFld="brand_name",buttonUsage="list",
                //  SqlForOptions ="select brand_no as v,brand_name as l from info_item_brand"}}
            };
            Func<string, Dictionary<string, string>, string> funcDealCellValue = (value, row) =>
            {
                string optQty = value;
                if (optQty == "") return "";

                var arr = optQty.Split(',');

                string res = "";
                foreach (var s in arr)
                {
                    var ss = s.Split(':');
                    string opt = ss[0];
                    string qty = ss[1];
                    decimal dQty = Decimal.Parse(qty, System.Globalization.NumberStyles.Float);
                    string unitQty = SheetBase<SheetRowBase>.GetUnitQty(dQty, row["b_unit_no"], row["m_unit_no"], row["s_unit_no"], row["b_unit_factor"], row["m_unit_factor"]);
                    if (res != "") res += ",";
                    res += opt + ":" + unitQty;
                }
                return res;
            };
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     HasCheck=true,KeepCheckForQueries=false,
                     IdColumn="i",TableName="info_item_prop",
                     JSBeforeCreate=@"
var gridHeight= $('#gridItems').height()
if(gridHeight==0) { 
    var headHeight=$('#divHead').height()
    var bodyHeight=$('body').height()
    gridHeight=bodyHeight-headHeight-50
    $('#gridItems').height(gridHeight)
}",
                     JsFuncGetRowKeyForCheck=@"
function(row){
   var key= row.i + '_'
   if(row.disp_flow_id){
        key+='_disp_' + row.disp_flow_id +'_' + row.disp_month_id
   }
   if(row.order_sub_id){
       key+='_order_' + row.order_sub_id +'_'+ row.order_unit_factor+'_' + row.order_price 
   }
   return key

}
",
                     ShowContextMenu=true,
                     ContextMenuHTML="<ul><li id='edit'>编辑</li><li id='remove'>删除</li><li id='BatchOperation'>批量操作</li></ul>",
                     Sortable=true,
                     RowsHeight=40,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"qtyInput",new DataItem(){Title="数量",GetFromDb=false,editable=true,Pinned=true, Hidden=true,HideOnLoad=true, Width="76",Sortable=true,
                           JSCellRender=@"qtyInputRender",JSCreateEditor=@"qtyInputCreateEditor",JSInitEditor=@"qtyInputInitEditor",JSGetEditorValue=@"qtyInputGetEditorValue"

                       }},
                       {"i",new DataItem(){Title="商品编号",SqlFld="p.item_id", Width="200",Hidden=true,HideOnLoad = true}},
                       {"n",new DataItem(){Title="商品名称",SqlFld="item_name", Width="220",Linkable=true,Sortable=true,Pinned=true,
                            JSCellRender="itemNameRenderer"
                       }},
                        {"item_images",new DataItem(){Title="图片",SqlFld="item_images ->>'main'",CellsAlign="center",
                        FuncDealMe = value =>
                        {
                            if (value!=null && value!="")
                            {
                                    return @$"<img height='50' onclick='myPreview(""{ObsBucketLinkHref}/{value}"")' src='{ObsBucketLinkHref}/{value}' /> ";
                            }
                            else 
                            {
                                return "<div></div>";
                            }
                        },Hidden=true, Width="50"}},
						  {"item_image_url",new DataItem(){Title="图片URL",SqlFld="item_images ->>'main'",CellsAlign="center",
						   FuncDealMe = value =>
						    {
							    if (value!=null && value!="")
							    {
									    return $"{ObsBucketLinkHref}/{value}";
							    }
							    else
							    {
								    return "";
							    }
						    },  Hidden=true, Width="50"}},
						{"item_videos",new DataItem(){Title="视频",SqlFld="item_videos ->>'main'",CellsAlign="center",
                        FuncDealMe = value =>
                        {
                            if (value!=null && value!="")
                            {
                                    return @$"<video height='50' onclick='myPreview(""{ObsBucketLinkHref}/{value}"")' src='{ObsBucketLinkHref}/{value}' /> ";
                            }
                            return "";
                        },Hidden=true, Width="50"}},
                       {"approve_status",new DataItem(){Title="审核状态", Width="100",Hidden=true,Linkable=true,
                        JSCellRender=@"function (row, column, value,p4,p5,rowData) { 
                            let v = value?`<div style = ""height:100%;width:100%;display:flex;align-items:center;justify-content:center;color:red"" >待审核</div>`:`<div style = ""height:100%;width:100%;display:flex;align-items:center;justify-content:center;color:#49f"" >正常</div>`
                            return v
                            }"
                        }},
                       {"stock_qty_unit",new DataItem(){Title="库存",GetFromDb=false,Hidden=true,HideOnLoad=true, SqlFld="yj_get_bms_qty(stock_qty,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)", Width="70",Sortable=true,
                            JSCellRender="stockQtyRenderer"
                       }},
                       {"son_stock_qty",new DataItem(){Title="属性库存",GetFromDb=false,Hidden=true,HideOnLoad=true, Width="70",Sortable=true,
                        FuncDealCellValue=funcDealCellValue
                       }},
                        {"son_usable_stock_qty",new DataItem(){Title="属性可用库存",GetFromDb=false,Hidden=true,HideOnLoad=true, Width="70",Sortable=true,
                          FuncDealCellValue=funcDealCellValue
                       }},
                       {"son_sell_pend_qty",new DataItem(){Title="属性占用库存",GetFromDb=false,Hidden=true,HideOnLoad=true, Width="70",Sortable=true,
                          FuncDealCellValue=funcDealCellValue
                       }},
                       {"usable_stock_qty",new DataItem(){Title="可用库存",GetFromDb=false,Hidden=true,HideOnLoad=true, SqlFld="yj_get_bms_qty(stock_qty-sell_pend_qty,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)", Width="70",Sortable=true,SortFld="stock_qty-sell_pend_qty",
                        JSCellRender="usableStockQtyRenderer"
                       }},
                       {"sell_pend_stock_qty",new DataItem(){Title="占用库存",GetFromDb=false,Hidden=true,HideOnLoad=true, SqlFld="yj_get_bms_qty(sell_pend_qty,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)", Width="70",Sortable=true,
                        JSCellRender="sellPendStockQtyRenderer"
                       }},
                       {"disp_flow_id",new DataItem(){Title="陈列协议flow",GetFromDb = false,Width="100" ,Hidden=true,HideOnLoad=true} },
                       {"fee_sub_name",new DataItem(){Title="陈列协议科目",GetFromDb = false,Width="100" ,Hidden=true,HideOnLoad=true} },
                       {"disp_sheet_id",new DataItem(){Title="陈列协议单号",GetFromDb = false,Width="100" ,Hidden=true,HideOnLoad=true} },
                       {"disp_left_qty",new DataItem(){Title="陈列协议剩余量",GetFromDb = false,Width="100" ,Hidden=true,HideOnLoad=true} },
                       {"disp_unit_no",new DataItem(){Title="陈列协议单位",GetFromDb = false,Width="100" ,Hidden=true,HideOnLoad=true} },
                       {"disp_month",new DataItem(){Title="陈列协议月份",GetFromDb = false,Width="100" ,Hidden=true,HideOnLoad=true} },
                       {"disp_month_id",new DataItem(){Title="陈列协议第几个月", GetFromDb = false,Width="100" ,Hidden=true,HideOnLoad=true} },
                       {"disp_items_id",new DataItem(){Title="陈列协议商品名称",GetFromDb = false,Width="100" ,Hidden=true,HideOnLoad=true} },
                       {"disp_items_name",new DataItem(){Title="陈列协议商品",GetFromDb = false,Width="100" ,Hidden=true,HideOnLoad=true} },
                       {"order_qty_unit",new DataItem(){Title="剩余数量",Width="150",GetFromDb = false,Hidden=true,HideOnLoad=true} },
                       {"order_price",new DataItem(){Title="定货价",GetFromDb = false,Width="100" ,Hidden=true,HideOnLoad=true} },
                       {"order_balance",new DataItem(){Title="定货款余额",Width = "150",GetFromDb = false,Hidden=true,HideOnLoad=true}},
                       {"order_flow_id",new DataItem(){Title="定货会flow",GetFromDb = false,Width="100" ,Hidden=true,HideOnLoad=true} },


                       {"order_qty", new DataItem(){Title="可用还货数量(无单位)",GetFromDb = false,Hidden=true,HideOnLoad=true}},
                       {"cost_amt",new DataItem(){Title = "货值",Width="100",Hidden=true,HideOnLoad=true} },
                       //{"quantity",new DataItem(){Title="订货价",GetFromDb = false } },
                       {"order_unit_factor",new DataItem(){Title="定货单位",GetFromDb = false,Hidden = true,HideOnLoad=true } },
                       {"order_sub_id",new DataItem(){Title="定货款账户",GetFromDb = false,Hidden=true,HideOnLoad=true} },
                       {"order_sub_name",new DataItem(){Title="定货款账户",Width = "150",GetFromDb = false,Hidden=true,HideOnLoad=true}},
                       {"order_item_sheets_id",new DataItem(){Title="定货会sheet_id",GetFromDb = false,Hidden=true,HideOnLoad=true} },
                       {"order_item_sheets_no",new DataItem(){Title="定货会单号",GetFromDb = false,Hidden=true,HideOnLoad=true} },
                       {"borrowed_qty",new DataItem(){Title="借货数量(无单位)",Width = "150",GetFromDb = false,Hidden=true,HideOnLoad=true}},
                       {"item_no",new DataItem(){Title="商品编号",SqlFld="item_no", Width="150",Hidden=false,Sortable=true}},
                       {"item_spec",new DataItem(){Title="规格",Width="150"}},
                       {"valid_days",new DataItem(){Title="保质期",SqlFld="valid_days||case valid_day_type when 'm' then '月' when 'y' then '年' else '天' end", Hidden = true,Width="150"}},
                       {"approaching_days",new DataItem(){Title="临期节点",SqlFld="approaching_days||case valid_day_type when 'm' then '月' when 'y' then '年' else '天' end", Hidden = true,Width="150"}},
                       {"s_barcode",new DataItem(){Title="小单位条码",SqlFld="s_barcode", Width="200"}},

                       {"b_barcode",new DataItem(){Title="大单位条码",Hidden=true, Width="200"}},
                       {"m_barcode",new DataItem(){Title="中单位条码",Hidden=true, Width="200"}},
                       {"s_unit_no",new DataItem(){Title="小单位", Width="00"}},
                       {"b_unit_no",new DataItem(){Title="大单位",Hidden=true, Width="70"}},
                       {"m_unit_no",new DataItem(){Title="中单位",Hidden=true, Width="00"}},
                       {"m_unit_factor",new DataItem(){Title="中包装率",Hidden=true, Width="10"}},
                       {"b_unit_factor",new DataItem(){Title="大包装率",Hidden=true, Width="10"}},

                       {"item_provenance",new DataItem(){Title="原产地",Hidden = true, Width="150"} },

                       {"mum_attributes",new DataItem(){Hidden=true,HideOnLoad=true}},
                       {"brand_name",new DataItem(){Title="品牌", Width="150"}},
                       {"category",new DataItem(){Title="类别",GetFromDb=false, Width="150",
                           FuncGetValueFromRowData=(colName,row)=>
                           {
                               string s = "",q="";
                               q = row["class3_name"]; if (q != "" && q != "全部") {
                                   s += q;
                               }
                               q = row["class2_name"]; if (q != "" && q != "全部"){
                                  if(s!="") s+="/"; s += q;
                               }
                               q = row["class1_name"]; if (q != ""){
                                  if(s!="") s+="/"; s += q;
                               }
                               return s;
                           }}
                        },
                       {"class1_name",new DataItem(){Hidden=true,HideOnLoad=true}},
                       {"class2_name",new DataItem(){Hidden=true,HideOnLoad=true}},
                       {"class3_name",new DataItem(){Hidden=true,HideOnLoad=true}},
                       {"status",new DataItem(){Title="状态",Width="180",SqlFld="(case WHEN status='0' THEN '停用' ELSE '正常' END)"}},
                       {"unit_conv",new DataItem(){Title="单位换算",Width="200"}},
                       {"wholesale_price",new DataItem(){Title="批发价", Width="60",Sortable=true,
                            SubColumns=new Dictionary<string,DataItem>()
                            {
                                {"b_wholesale_price",new DataItem(){Title = "大",InnerTitle="批发价大",OrigTitle="批发价大",Hidden = true,Width = "70",CellsAlign="right",Sortable=true,SqlFld="b_wholesale_price::numeric"}},
                                {"m_wholesale_price",new DataItem(){Title="中",Hidden=true,Width="70",CellsAlign="right"} },
                                {"s_wholesale_price",new DataItem(){Title="小",Hidden=true,Width="70",CellsAlign="right",Sortable=true,SqlFld="s_wholesale_price::numeric"} },
                            }
                       }},
                       {"retail_price",new DataItem(){Title="零售价", Width="60",  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                {
                                    {"b_retail_price",new DataItem(){Title = "大",Hidden =true,Width = "70",CellsAlign="right",Sortable=true}},
                                    {"m_retail_price",new DataItem(){Title="中",Hidden=true,Width="70",CellsAlign="right"} },
                                    {"s_retail_price",new DataItem(){Title="小",Hidden=true,Width="70",CellsAlign="right",Sortable=true} },
                                }
                            }
                       } },
                       {"buy_price",new DataItem(){Title="进价", Width="60",  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                {
                                    {"b_buy_price",new DataItem(){Title = "大",Hidden = true,Width = "70",CellsAlign="right",Sortable=true}},
                                    {"m_buy_price",new DataItem(){Title="中",Hidden=true,Width="70",CellsAlign="right"} },
                                    {"s_buy_price",new DataItem(){Title="小",Hidden=true,Width="70",CellsAlign="right",Sortable=true} },
                                }
                            }
                       }},
                      {"lowest_price",new DataItem(){Title="最低售价", Width="60",  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                {
                                    {"b_lowest_price",new DataItem(){Title = "大",Hidden = true,Width = "70",CellsAlign="right",Sortable=true}},
                                    {"m_lowest_price",new DataItem(){Title="中",Hidden=true,Width="70",CellsAlign="right"} },
                                    {"s_lowest_price",new DataItem(){Title="小",Hidden=true,Width="70",CellsAlign="right",Sortable=true} },
                                }
                            }
                       }},
                       {"cost_price_avg",new DataItem(){Title="加权价", Width="60",  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                {
                                    {"b_cost_price",new DataItem(){Title="大",Hidden = true,Width = "70",CellsAlign="right",SqlFld="round((cost_price_avg*b_unit_factor)::numeric,2)", Sortable=true}},
                                    {"m_cost_price",new DataItem(){Title="中",Hidden=true,Width="70",SqlFld="round((cost_price_avg*m_unit_factor)::numeric,2)  ",CellsAlign="right"} },
                                    {"s_cost_price",new DataItem(){Title="小",Hidden=true,Width="70",SqlFld="round((cost_price_avg*s_unit_factor)::numeric,2)  ",CellsAlign="right",Sortable=true} },
                                }
                            }
                       }},
                       //GetFromDb=false
                       {"cost_price_recent",new DataItem(){Title="最近平均进价", Width="60",  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                {
                                    {"cost_price_recent1",new DataItem(){Title="1次",Width="70",Hidden=true, SqlFld="ROUND((cost_price_recent->'avg1')::numeric, 3)",CellsAlign="right",Sortable=true}},
                                    {"cost_price_recent2",new DataItem(){Title="2次",Width="70",Hidden=true,SqlFld="ROUND((cost_price_recent->'avg2')::numeric, 3)",CellsAlign="right",Sortable=true} },
                                    {"cost_price_recent3",new DataItem(){Title="3次",Width="70",Hidden=true,SqlFld="ROUND((cost_price_recent->'avg3')::numeric, 3)",CellsAlign="right",Sortable=true} },
                                }
                            }
                       }},
                        {"profit_rate",new DataItem(){Title="利润率", Width="60", SqlFld="case when s_wholesale_price::numeric =0 then null else round(100*(s_wholesale_price::numeric-s_buy_price::numeric)/s_wholesale_price::numeric,1)||'%' end",
                        Hidden=true
                       }},
                       {"weight",new DataItem(){Title="重量(kg)", Width="60",  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                {
                                    {"b_weight",new DataItem(){Title = "大",Hidden = true,Width = "70",CellsAlign="right", Sortable=true}},
                                    {"m_weight",new DataItem(){Title="中",Hidden=true,Width="70",CellsAlign="right" ,Sortable=true} },
                                    {"s_weight",new DataItem(){Title="小",Hidden=true,Width="70",CellsAlign="right",Sortable=true} },
                                }
                            }
                       }},
                       {"mall_status",new DataItem(){Title="商城状态",Width="180",SqlFld="(case WHEN mall_status=0 THEN '已下架' ELSE '已上架' END)",Linkable=true}}
                     },
                     QueryFromSQL=$@"
from info_item_prop p 
 ~SQL_VARIABLE1
left join
(
  select brand_id,brand_name from info_item_brand where company_id = ~COMPANY_ID
) b on p.item_brand = brand_id
LEFT JOIN 
(
    SELECT ic1.class_id,ic1.class_name class1_name, class2_name,class3_name
    FROM info_item_class ic1
	LEFT JOIN
    (
	    SELECT class_id, class_name class2_name,mother_id FROM info_item_class WHERE class_id <>- 1 and company_id=~COMPANY_ID 
    ) ic2 on ic1.mother_id=ic2.class_id
    LEFT JOIN
    (
	    SELECT class_id, class_name class3_name FROM info_item_class WHERE class_id <>- 1 and company_id=~COMPANY_ID 
    ) ic3 on ic2.mother_id=ic3.class_id
    WHERE ic1.company_id = ~COMPANY_ID 
) c ON P.item_class = c.class_id
LEFT JOIN
(
    SELECT item_id,
        s_unit_no,s_unit_factor,s_wholesale_price,s_retail_price,s_buy_price,s_barcode,s_weight,s_lowest_price,
        m_unit_no,m_unit_factor,m_wholesale_price,m_retail_price,m_buy_price,m_barcode,m_weight,m_lowest_price,
        b_unit_no,b_unit_factor,b_wholesale_price,b_retail_price,b_buy_price,b_barcode,b_weight,b_lowest_price,
(
         case when b_unit_factor is not null and m_unit_factor is     null then concat(s_unit_factor,b_unit_no,'=',b_unit_factor,s_unit_no)  
			  when b_unit_factor is not null and m_unit_factor is not null then concat(s_unit_factor,b_unit_no,'=',floor(b_unit_factor::numeric/m_unit_factor::numeric),m_unit_no,'=',b_unit_factor,s_unit_no)
			  when b_unit_factor is null then concat(s_unit_factor,s_unit_no)  end
        ) as unit_conv
    FROM 
    (
        SELECT item_id,(s->>'f1')::real as s_unit_factor,s->>'f2' as s_unit_no,s->>'f3' as s_wholesale_price,s->>'f4' as s_retail_price,s->>'f5' s_barcode,s->>'f6' as s_buy_price,s->>'f7' as s_weight,s->>'f8' as s_lowest_price,
                       (m->>'f1')::real as m_unit_factor,m->>'f2' as m_unit_no,m->>'f3' as m_wholesale_price,m->>'f4' as m_retail_price,m->>'f5' m_barcode,m->>'f6' as m_buy_price,m->>'f7' as m_weight,m->>'f8' as m_lowest_price,
                       (b->>'f1')::real as b_unit_factor,b->>'f2' as b_unit_no,b->>'f3' as b_wholesale_price,b->>'f4' as b_retail_price,b->>'f5' b_barcode,b->>'f6' as b_buy_price,b->>'f7' as b_weight,b->>'f8' as b_lowest_price
        FROM crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,wholesale_price,retail_price,barcode,buy_price,weight,lowest_price)) as json from info_item_multi_unit where company_id = ~COMPANY_ID ORDER BY item_id',$$values('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s jsonb,m jsonb, b jsonb) 
    ) t 
) d on d.item_id = p.item_id
~VAR_stock_sql
~VAR_showAttrSonItems_join
where p.company_id=~COMPANY_ID ~SQL_VARIABLE2 ~SQL_VARIABLE3 ~VAR_son_items_condi ~VAR_stock_condi" ,
                     QueryOrderSQL="order by p.item_order_index,p.item_id desc"
                  }
                }
            };
        }


        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            if (DataItems["other_class"].Label == "全部")
            {
                DataItems["other_class"].Value = "";
            }
            string supcust_id = DataItems["supcust_id"].Value;//这个值不选的时候不提供常用选择功能
            string other_class = DataItems["other_class"].Value;
            string sheetType = DataItems["sheetType"].Value;
            var dispCondi = " and ((disp_year < (to_char(now(),'YYYY')::int)) OR (disp_month<=(to_char(now(),'MM')::int) and disp_year = (to_char(now(),'YYYY')::int))) ";
            // var dispCondi = " and disp_month<=(to_char(now(),'MM')::int) and disp_year=(to_char(now(),'YYYY')::int) ";
            //查询提前兑付权限
            string rightSql = $"select rights->'delicacy'->'allowAdvanceDisplayFee'->'value' allow_advance_disp_fee from info_operator o left join info_role r on r.role_id= o.role_id where o.company_id={company_id} and oper_id={OperID}";
            dynamic advanceDisp = await CDbDealer.Get1RecordFromSQLAsync(rightSql, cmd);
            if (advanceDisp != null && advanceDisp.allow_advance_disp_fee == "true")
                dispCondi = $" ";
            // DataItems["supcust_id"].Value = "";
            if (supcust_id != "" && other_class == "often")
            {
                var sup_select = !string.IsNullOrWhiteSpace(supcust_id);
                string supQuery = sup_select ? $" and b.supcust_id = {supcust_id} " : "";
                string supQuery1 = sup_select ? $" and supcust_id = {supcust_id} " : "";
                string isOften = "or is_often";
                //判断采购和销售
                string often_main = "sheet_sale_main";
                string often_detail = "sheet_sale_detail";
                string often_order_main = "sheet_sale_order_main";
                string often_order_detail = "sheet_sale_order_detail";
                if (sheetType == "CG" || sheetType == "CT")
                {
                    often_main = "sheet_buy_main";
                    often_detail = "sheet_buy_detail";
                    often_order_main = "sheet_buy_order_main";
                    often_order_detail = "sheet_buy_order_detail";
                }

                //判断借货和还货，结果为true，就不需要查询订货会和陈列商品
                if (sheetType == "JH" || sheetType == "HH")
                {
                    this.SQLVariable1 =
                    @$" 
                        left join 
                        (
                            select item_id,true as is_often 
                            from 
                            (
                                select item_id,count,row_number() over(order by count desc) rn 
                                from 
                                (
                                    select s.item_id,count1+count2 count from 
                                    (
                                        select item_id,count(item_id) count1 from sheet_sale_detail sd
                                        left join sheet_sale_main sm on sd.sheet_id = sm.sheet_id  and sd.company_id = sm.company_id
                                        where sd.company_id = {this.company_id} {supQuery1} and (sm.happen_time::date >= (now()-interval'3 months') and sm.happen_time::date <=now()) group by item_id
                                    ) s
                                    left join 
                                    (
                                        select item_id,count(item_id) count2 from sheet_sale_order_detail od left join sheet_sale_order_main om on od.sheet_id = om.sheet_id  and od.company_id = om.company_id
                                        where od.company_id = {company_id} {supQuery1} and (om.happen_time::date >= (now()-interval'3 months') and om.happen_time::date<= now())group by item_id
                                    ) o
                                    on o.item_id = s.item_id
                                )  t 
                            ) tt where rn<=200
                        ) often on often.item_id = p.item_id

                        left join 
                        ( 
                                select null order_flow_id,  null disp_flow_id,null disp_sheet_id,null fee_sub_name,item_id,null disp_items_id,null disp_items_name,null disp_left_qty,null disp_unit_no,to_char(now(),'MM')::int disp_month,to_char(now(),'YYYY')::int disp_year,null disp_month_id,null order_sub_id,null order_sub_name,null order_price,null order_unit_factor,null order_balance,null order_qty,null order_item_sheets_id,null order_item_sheets_no,borrowed_qty::text
                                from borrowed_cust_items 
                                where company_id = {company_id} and borrowed_qty<>0 and cust_id={supcust_id} 

                        ) tt on tt.item_id = p.item_id {dispCondi}";

                    if (sheetType == "HH")
                    {
                        isOften = "";
                        this.SQLVariable1 =
                        @$" 
                            left join 
                            ( 
                                    select null order_flow_id,  null disp_flow_id,null disp_sheet_id,null fee_sub_name,item_id,null disp_items_id,null disp_items_name,null disp_left_qty,null disp_unit_no,to_char(now(),'MM')::int disp_month,to_char(now(),'YYYY')::int disp_year,null disp_month_id,null order_sub_id,null order_sub_name,null order_price,null order_unit_factor,null order_balance,null order_qty,null order_item_sheets_id,null order_item_sheets_no,borrowed_qty::text
                                    from borrowed_cust_items 
                                    where company_id = {company_id} and borrowed_qty<>0 and cust_id={supcust_id} 

                            ) tt on tt.item_id = p.item_id {dispCondi}";
                    }
                }
                else
                {
                    this.SQLVariable1 =
                    @$" 
                        left join 
                        (
                            select item_id,true as is_often 
                            from 
                            (
                                select item_id,count,row_number() over(order by count desc) rn 
                                from 
                                (
                                    select s.item_id,count1+count2 count from 
                                    (
                                        select item_id,count(item_id) count1 from {often_detail} sd
                                        left join {often_main} sm on sd.sheet_id = sm.sheet_id  and sd.company_id = sm.company_id
                                        where sd.company_id = {this.company_id} {supQuery1} and (sm.happen_time::date >= (now()-interval'3 months') and sm.happen_time::date <=now()) group by item_id
                                    ) s
                                    left join 
                                    (
                                        select item_id,count(item_id) count2 from {often_order_detail} od left join {often_order_main} om on od.sheet_id = om.sheet_id  and od.company_id = om.company_id
                                        where od.company_id = {company_id} {supQuery1} and (om.happen_time::date >= (now()-interval'3 months') and om.happen_time::date<= now())group by item_id
                                    ) o
                                    on o.item_id = s.item_id
                                )  t 
                            ) tt where rn<=200
                        ) often on often.item_id = p.item_id

                        left join 
                        ( 
                            select null order_flow_id, flow_id disp_flow_id,sheet_id disp_sheet_id, fee_sub_name,split_part(items_id, ',',1)::int item_id,items_id disp_items_id,items_name disp_items_name,disp_left_qty,unit_no disp_unit_no,
                            (case when start_month+months-1>12 then start_month+months-13 else start_month+months-1 end) disp_month,
                            (case when start_month+months-1>12 then start_year+1 else start_year end) as disp_year,months as disp_month_id,null order_sub_id,null order_sub_name,null order_price,null order_unit_factor,null order_balance,null order_qty,null order_item_sheets_id,null order_item_sheets_no,null borrowed_qty
    
                            from
                            (
                                select d.sheet_id,d.flow_id, sub.sub_name as fee_sub_name,items_id,items_name,unit_no,unnest(string_to_array((								
                                        COALESCE(month1_qty,0)-COALESCE(month1_given,0)||','||COALESCE(month2_qty,0)-COALESCE(month2_given,0)||','||
                                        COALESCE(month3_qty,0)-COALESCE(month3_given,0)||','||COALESCE(month4_qty,0)-COALESCE(month4_given,0)||','||
                                        COALESCE(month5_qty,0)-COALESCE(month5_given,0)||','||COALESCE(month6_qty,0)-COALESCE(month6_given,0)||','||
                                        COALESCE(month7_qty,0)-COALESCE(month7_given,0)||','||COALESCE(month8_qty,0)-COALESCE(month8_given,0)||','||
                                        COALESCE(month9_qty,0)-COALESCE(month9_given,0)||','||COALESCE(month10_qty,0)-COALESCE(month10_given,0)||','||
                                        COALESCE(month11_qty,0)-COALESCE(month11_given,0)||','||COALESCE(month12_qty,0)-COALESCE(month12_given,0)) ,','))::numeric disp_left_qty,
                                        unnest(string_to_array('01,02,03,04,05,06,07,08,09,10,11,12',','))::int months,to_char(m.start_time,'YYYY')::int start_year,to_char(m.start_time,'MM')::int start_month 
                                    from display_agreement_detail d 
                                    left join display_agreement_main m on m.sheet_id = d.sheet_id and d.company_id = m.company_id
                                    left join cw_subject sub on sub.company_id={company_id} and m.fee_sub_id=sub.sub_id
                                    where d.company_id = {company_id} and red_flag is null and approve_time is not null and supcust_id = {supcust_id} and items_id!='money' 
                            ) t
                            where disp_left_qty > 0 GROUP BY items_id,items_name,fee_sub_name,flow_id,sheet_id,months,disp_left_qty,unit_no,start_year,start_month 

	                        union
		                        select b.flow_id order_flow_id, null disp_flow_id,null disp_sheet_id,null fee_sub_name,item_id,null disp_items_id,null disp_items_name,null disp_left_qty,null disp_unit_no,to_char(now(),'MM')::int disp_month,to_char(now(),'YYYY')::int disp_year,null disp_month_id,prepay_sub_id order_sub_id,COALESCE(sub_name,'未指定') as order_sub_name,order_price,b.unit_factor as order_unit_factor,b.quantity * b.order_price as order_balance,quantity*unit_factor order_qty, order_item_sheets_id,order_item_sheets_no, null borrowed_qty
                                from items_ordered_balance b
                                left join prepay_balance pb on b.supcust_id = pb.supcust_id and b.prepay_sub_id = pb.sub_id
                                left join cw_subject pw on pw.sub_id = b.prepay_sub_id
                                where b.company_id={company_id}  and b.supcust_id = {supcust_id} and b.quantity!=0 and case when prepay_sub_id <>'-1' then  pw.sub_name is not null else true end 

                            union
                                select null order_flow_id,  null disp_flow_id,null disp_sheet_id,null fee_sub_name,item_id,null disp_items_id,null disp_items_name,null disp_left_qty,null disp_unit_no,to_char(now(),'MM')::int disp_month,to_char(now(),'YYYY')::int disp_year,null disp_month_id,null order_sub_id,null order_sub_name,null order_price,null order_unit_factor,null order_balance,null order_qty,null order_item_sheets_id,null order_item_sheets_no,borrowed_qty::text
                                from borrowed_cust_items 
                                where company_id = {company_id} and borrowed_qty<>0 and cust_id={supcust_id} 

                        ) tt on tt.item_id = p.item_id {dispCondi}";
                }


                this.SQLVariable2 = @$" and (order_sub_id is not null {isOften} or disp_flow_id is not null or borrowed_qty is not null)";
                DataItems["other_class"].Value = "";
                this.Grids.First().Value.Columns["fee_sub_name"].GetFromDb = true;
            }

            string branch_id = DataItems["branch_id"].Value;
            this.SQLVariables["stock_sql"] = "";
            this.SQLVariables["stock_condi"] = "";

            if (branch_id != "")
            {
                var col = Grids.First().Value.Columns["stock_qty_unit"];
                var son_col = Grids.First().Value.Columns["son_stock_qty"];
                son_col.GetFromDb = true;
                var son_usable_col = Grids.First().Value.Columns["son_usable_stock_qty"];
                son_usable_col.GetFromDb = true;
                var usable_col = Grids.First().Value.Columns["usable_stock_qty"];
                col.Hidden = false;
                col.GetFromDb = true;

                col.HideOnLoad = false;
                usable_col.Hidden = false;
                usable_col.GetFromDb = true;
                usable_col.HideOnLoad = false;
                /*this.SQLVariables["stock_sql"] = $@"  
LEFT JOIN stock on p.item_id=stock.item_id and p.company_id = stock.company_id and stock.branch_id={branch_id}
";
                */
                this.SQLVariables["stock_sql"] = $@"  
LEFT JOIN 
(
    select stock.company_id,case when son_mum_item is null then stock.item_id else son_mum_item end stock_item_id,sum(stock_qty) stock_qty,sum(sell_pend_qty) sell_pend_qty,
          string_agg(opt_name||':'|| stock_qty,',') son_stock_qty,
          string_agg(opt_name||':'|| sell_pend_qty,',') son_sell_pend_qty,
          string_agg(opt_name||':'|| (stock_qty-sell_pend_qty),',') son_usable_stock_qty
 
    FROM stock
    LEFT join info_item_prop ip on stock.company_id=ip.company_id and stock.item_id=ip.item_id
    LEFT JOIN info_attr_opt opt on ip.company_id=opt.company_id and (case when position ('_' in coalesce(ip.son_options_id,''))=0 then ip.son_options_id::integer else 0 end)=opt.opt_id 
    where stock.company_id={company_id} and stock.branch_id={branch_id}
    group by stock.company_id,stock_item_id
) stock
on p.item_id=stock.stock_item_id
";

                string showHasStock = DataItems["showHasStock"].Value;
                if (showHasStock.ToLower() == "true")
                    this.SQLVariables["stock_condi"] = " and stock_qty >0";
            }

            this.SQLVariables["son_items_condi"] = "";
            this.SQLVariables["showAttrSonItems_join"] = "";
            //string showSonItems = DataItems["showSonItems"].Value;
            string showAttrSonItems = DataItems["showAttrSonItems"].Value;
            if (showAttrSonItems.ToLower() == "true")
            {
                this.SQLVariables["son_items_condi"] = " and  (mum_attributes IS NULL OR NOT EXISTS (SELECT 1  FROM jsonb_array_elements(mum_attributes) AS elem  WHERE elem->>'distinctStock' = 'true'))";
                var columns = this.Grids.First().Value.Columns;
                columns["n"].SqlFld = "case when opts.value is null then item_name else item_name|| '(' || (opts.value->>'optName') || ')' end";
                columns["s_barcode"].SqlFld = "case when opts.value is null then s_barcode else opts.value->>'sBarcode'   end";
                columns["b_barcode"].SqlFld = "case when opts.value is null then b_barcode else opts.value->>'bBarcode' end";
                columns["m_barcode"].SqlFld = "case when opts.value is null then m_barcode else opts.value->>'mBarcode' end";
                this.SQLVariables["showAttrSonItems_join"] = "LEFT JOIN LATERAL jsonb_array_elements(avail_attr_combine) AS opts on avail_attr_combine is not null  ";
            }
            else
            {
                this.SQLVariables["son_items_condi"] = " and son_mum_item is null";
            }



        }
        public async Task OnGet(string forSelect, string branch_id, string forDisplay)
        {
            ForSelect = forSelect == "1";
            ForReport = forSelect == "2";
            if (ForSelect || ForReport)
            {
                Grids["gridItems"].HasCheck = true;
                Grids["gridItems"].KeepCheckForQueries = true;

                var dataItem = DataItems["other_class"];
                dataItem.JSAfterCreate = @"
                  var items = $('#other_class').jqxTree('getItems')
                  if (items.length > 1) {
                        $('#other_class').jqxTree('addBefore', { value: 'often', label: '常用' }, items[1].element, false);
                        $('#other_class').jqxTree('render'); 
                        var div = $('#other_class').find('div:contains(常用)');
                        if(div.length){
                           var li=div[0].parentNode
                           $('#other_class').jqxTree('selectItem',li)
                        }
                  }


";
                if (ForSelect)
                {
                    var col = Grids.First().Value.Columns["qtyInput"];
                    col.Hidden = false;
                }


            }

            if (branch_id != null && branch_id != "")
            {
                //var col = Grids.First().Value.Columns["stock_qty_unit"];
                //var usable_col = Grids.First().Value.Columns["usable_stock_qty"];
                //col.Hidden = false;
                //usable_col.Hidden = false;
                //col.GetFromDb = true;
                //usable_col.GetFromDb = true;
                this.BranchID = branch_id;
                // col.HideOnLoad = false;
            }

            if (forDisplay == "1")
            {
                DataItems["showAttrSonItems"].Disabled = true;
            }

            await InitGet(cmd);

            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }
        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            bool seeInPrice = false;
            if (JsonOperRights.IsValid())
            {
                dynamic operRights = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonOperRightsOrig);
                if (operRights?.delicacy?.seeInPrice?.value is not null)
                    seeInPrice = ((string)operRights.delicacy.seeInPrice.value).ToLower() == "true";
            }
            if (!seeInPrice)
            {
                var columns = await Grids["gridItems"].GetAllColumns();
                columns["s_buy_price"].HideOnLoad = columns["s_buy_price"].Hidden = true;
                columns["m_buy_price"].HideOnLoad = columns["m_buy_price"].Hidden = true;
                columns["b_buy_price"].HideOnLoad = columns["b_buy_price"].Hidden = true;
                columns["b_cost_price"].HideOnLoad = columns["b_cost_price"].Hidden = true;
                columns["m_cost_price"].HideOnLoad = columns["m_cost_price"].Hidden = true;
                columns["s_cost_price"].HideOnLoad = columns["s_cost_price"].Hidden = true;
                columns["cost_price_recent1"].HideOnLoad = columns["cost_price_recent1"].Hidden = true;
                columns["cost_price_recent2"].HideOnLoad = columns["cost_price_recent2"].Hidden = true;
                columns["cost_price_recent3"].HideOnLoad = columns["cost_price_recent3"].Hidden = true;

                columns["profit_rate"].HideOnLoad = columns["profit_rate"].Hidden = true;
            }
            if (BranchID != null && BranchID != "")
            {
                var col = Grids.First().Value.Columns["stock_qty_unit"];
                var usable_col = Grids.First().Value.Columns["usable_stock_qty"];
                col.Hidden = false;
                col.HideOnLoad = false;
                usable_col.Hidden = false;
                col.GetFromDb = true;
                usable_col.GetFromDb = true;

            }

        }
        //public override async Task<string> CheckBeforeDeleteRecords(string rowIDs)
        //{ 
        //    cmd.CommandText = $"select item_id from stock where item_id in ({rowIDs}) and company_id={company_id} limit 1";
        //    object ov = await cmd.ExecuteScalarAsync();
        //    if (ov != null && ov != DBNull.Value)
        //    {
        //        return "商品已产生库存记录,无法删除";
        //    }

        //    cmd.CommandText = $"select item_id from sheet_order_item_detail where item_id in ({rowIDs}) and company_id={company_id} limit 1";
        //    ov = await cmd.ExecuteScalarAsync();
        //    if (ov != null && ov != DBNull.Value)
        //    {
        //        return "商品已在定货会中使用过,无法删除";
        //    }
        //    return "";
        //}


        public static async Task<string> CheckBeforeDeleteRecords_item(CMySbCommand cmd, string id, string company_id)
        {


            cmd.CommandText = @$"select item_id::text from sheet_sale_detail where item_id = '{id}'  and company_id={company_id}  
                union all
                select item_id::text from sheet_sale_order_detail where item_id = '{id}'   and company_id = {company_id}
                    union all
                select item_id::text from sheet_order_item_detail where item_id = '{id}'   and company_id = {company_id}
                    union all
                select item_id::text from sheet_item_ordered_adjust_detail where item_id = '{id}'   and company_id = {company_id}
                      union all
                select item_id::text from sheet_buy_detail where item_id = '{id}'   and company_id = {company_id}
                    union all
                select item_id::text from sheet_move_detail where item_id = '{id}'   and company_id = {company_id}
                    union all
                select item_id::text from sheet_inventory_detail where item_id = '{id}'   and company_id = {company_id}
                    limit 1;";
            var ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value) return "商品已使用,无法删除";

            cmd.CommandText = @$"
                select item_id::text from sheet_sale_detail where item_id in (select item_id from info_item_prop ip where son_mum_item = '{id}'  and company_id={company_id})  and company_id={company_id}  
                    union all
                select item_id::text from sheet_sale_order_detail where item_id in (select item_id from info_item_prop ip where son_mum_item = '{id}'  and company_id={company_id})  and company_id = {company_id}
                    union all
                select item_id::text from sheet_order_item_detail where item_id in (select item_id from info_item_prop ip where son_mum_item = '{id}'  and company_id={company_id})   and company_id = {company_id}
                    union all
                select item_id::text from sheet_item_ordered_adjust_detail where item_id in (select item_id from info_item_prop ip where son_mum_item = '{id}'  and company_id={company_id})   and company_id = {company_id}
                      union all
                select item_id::text from sheet_buy_detail where item_id in (select item_id from info_item_prop ip where son_mum_item = '{id}'  and company_id={company_id})   and company_id = {company_id}
                    union all
                select item_id::text from sheet_move_detail where item_id in (select item_id from info_item_prop ip where son_mum_item = '{id}'  and company_id={company_id})   and company_id = {company_id}
                    union all
                select item_id::text from sheet_inventory_detail where item_id in (select item_id from info_item_prop ip where son_mum_item = '{id}'  and company_id={company_id})   and company_id = {company_id}
                    limit 1;";
            var ov2 = await cmd.ExecuteScalarAsync();
            if (ov2 != null && ov2 != DBNull.Value) return "商品存在子商品已使用,无法删除";



            string checkResellerItem = await ResellerService.DeleteResellerItemInfoCheck(company_id, id, cmd);
            if (checkResellerItem != "OK")
            {
                return "商品在分销商账号已使用,无法删除";
            }
            cmd.CommandText = $"delete from info_item_prop where rs_mum_id = {id}";
            await cmd.ExecuteNonQueryAsync();

            return "";




        }


        public override async Task<string> CheckBeforeDeleteRecords(string rowIDs)
        {
            return await CheckBeforeDeleteRecords_item(cmd, rowIDs, company_id);


        }


    }

    public class Test
    {
        public string gridID;
        public int startRow;
        public int endRow;
        public bool GetRowsCount;
        public string other_class;
    }
    [Route("api/[controller]/[action]")]
    public class ItemsViewController : BaseController
    {
        public ItemsViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }
        [HttpGet]
        public async Task<IActionResult> GetClassInfo(string operKey, string classId)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            cmd.CommandText = $@"select batch_level from info_item_class where company_id = {companyID} and class_id = {classId}";
            object val = await cmd.ExecuteScalarAsync();
            if (val != null)
            {
                return Json(new { result = "OK", data = val });
            }
            return Json(new { result = "ERROR", data = val });

        }
        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            ItemsViewModel model = new ItemsViewModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            ItemsViewModel model = new ItemsViewModel(cmd);
            var columns = model.Grids.GetValueOrDefault("gridItems").Columns;
            //选择常用后，部分列名参与查询 设置 GetFromDb = true; 
            string supcust_id = CPubVars.RequestV(Request, "supcust_id");
            string other_class = CPubVars.RequestV(Request, "other_class");
            string sheetType = CPubVars.RequestV(Request, "sheetType");
            if (supcust_id.IsValid() && other_class == "often")
            {
                foreach (KeyValuePair<string, DataItem> col in columns)
                {
                    if (col.Key.IndexOf("order") >= 0 || col.Key.IndexOf("disp") >= 0 || col.Key.IndexOf("borrowed") >= 0)
                    {
                        string tempCondition = "when order_qty=0 then '' when order_qty!=0 then unit_from_s_to_bms (order_qty::float4,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) when disp_flow_id is not null then concat(disp_left_qty,disp_unit_no) ";
                        if (sheetType == "JH" || sheetType == "HH") tempCondition = "";
                        if (col.Key == "order_qty_unit") col.Value.SqlFld = @$"(case {tempCondition}
                                                                                  when borrowed_qty='0' then '' when borrowed_qty!='0' then unit_from_s_to_bms (borrowed_qty::float4,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)
                                                                            end)";
                        col.Value.GetFromDb = true;
                    }
                }
                model.Grids.GetValueOrDefault("gridItems").QueryOrderSQL = "order by order_price,disp_flow_id,disp_month_id,borrowed_qty,p.item_order_index,p.item_id";

            };
            object records = await model.GetRecordFromQuerySQL(Request, cmd);// gridID, startRow, endRow, bNewQuery);
            return records;
        }

        [HttpPost]
        public async Task<object> DeleteRecords([FromBody] dynamic data)
        {
            ItemsViewModel model = new ItemsViewModel(cmd);
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID, out string operID);
            dynamic jsonRights = await CDbDealer.Get1RecordFromSQLAsync($@"SELECT rights FROM info_operator o 
            left join info_role r on o.company_id = r.company_id and o.role_id = r.role_id
            where o.company_id = {companyID} and o.oper_id = {operID}", cmd);
            dynamic rights = Newtonsoft.Json.JsonConvert.DeserializeObject(jsonRights.rights);
            if (rights != null && rights.info.infoItem.delete == "false")
            {
                return new JsonResult(new { result = "ERROR", msg = "暂无删除权限" }); ;
            }
            dynamic records = await model.DeleteRecords(data, cmd, "info_item_prop");// gridID, startRow, endRow, bNewQuery);
            // 删除分销商商品

            return records;
        }

        [HttpPost]
        public async Task<IActionResult> RemoveClass([FromBody] dynamic value)
        {
            ClassEditModel model = new ClassEditModel(cmd);
            Security.GetInfoFromOperKey((string)value.operKey, out string companyID);
            string id = value[model.m_idFld];
            string result = "OK";
            if (id == "")
            {
                result = "请传入类编号";
                goto end;
            }
            CDbDealer db = new CDbDealer();

            object o = null;

            cmd.CommandText = @$"select class_id from info_item_class where mother_id='{id} ' and company_id = {companyID}";
            o = await cmd.ExecuteScalarAsync();
            if (o != null && o != DBNull.Value)
            {
                result = "请删除该类的子类后再删除该类"; goto end;
            }
            cmd.CommandText = @$"select item_name from info_item_prop where item_class=' {id}' and company_id = {companyID}";
            o = await cmd.ExecuteScalarAsync();
            if (o != null && o != DBNull.Value)
            {
                result = "请删除该类的商品后再删除该类"; goto end;
            }
            string sql = @$"delete from info_item_class where class_id='{id}' and company_id = {companyID}";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();


        //  var tt = Convert.ToString(value.uid); 
        //var rr = new { UserID = value.UserID, UserName = value.UserName };
        //return value;
        end:
            return Json(new { result, class_id = id });
            //return JsonObject<object> (new { UserID = value.UserID, UserName = value.UserName });
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            ItemsViewModel model = new ItemsViewModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }
        public async Task<JsonResult> BatchSetClass([FromBody] dynamic data)
        {

            ItemsViewModel model = new ItemsViewModel(cmd);

            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);

            string item_class = data.item_class;
            string other_class = data.other_class;
            string sql = "";
            string rows = string.Join(",", data.rows);
            //cmd.CommandText = $" select brand_id  from info_item_class where  company_id ={companyID} AND class_id = {item_class}";
            //var item_brand = await cmd.ExecuteScalarAsync();             

            sql = $"UPDATE info_item_prop SET item_class = {item_class} ,other_class = '{other_class}' WHERE company_id ={companyID} AND item_id in  ({rows} );";

            sql += $"UPDATE info_item_prop SET item_class = {item_class} ,other_class = '{other_class}' WHERE company_id ={companyID} AND son_mum_item in  ({rows} );";


            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();


            // 分销商商品同步
            dynamic syncRet = await ResellerService.BatchEditItemInfoSync(new
            {
                itemIdList = rows,
                editType = "class",
                operKey = (string)data.operKey
            }, cmd);
            cmd.CommandText = sql;
            if (syncRet.result != "OK")
            {

                return Json(new { result = "Error", msg = syncRet.msg });
            }
            return Json(new { result = "OK", msg = "" });
        }
        [HttpPost]
        public async Task<JsonResult> BatchSetBrand([FromBody] dynamic data)
        {

            ItemsViewModel model = new ItemsViewModel(cmd);

            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);

            string item_brand = data.item_brand;
            string sql = "";
            string rows = string.Join(",", data.rows);
            //cmd.CommandText = $" select brand_id  from info_item_class where  company_id ={companyID} AND class_id = {item_class}";
            //var item_brand = await cmd.ExecuteScalarAsync();             

            sql = $"UPDATE info_item_prop SET item_brand = {item_brand}  WHERE company_id ={companyID} AND item_id in ({rows});";
            sql += $"UPDATE info_item_prop SET item_brand = {item_brand}  WHERE company_id ={companyID} AND son_mum_item in ({rows});";

            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();

            // 分销商商品同步
            dynamic syncRet = await ResellerService.BatchEditItemInfoSync(new
            {
                itemIdList = rows,
                editType = "brand",
                operKey = (string)data.operKey
            }, cmd);
            cmd.CommandText = sql;
            if (syncRet.result != "OK")
            {

                return Json(new { result = "Error", msg = syncRet.msg });
            }
            return Json(new { result = "OK", msg = "" });
        }

        [HttpPost]
        public async Task<JsonResult> BatchSetStatus([FromBody] dynamic data)
        {

            ItemsViewModel model = new ItemsViewModel(cmd);
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
            string status = data.status;
            string rows = string.Join(",", data.rows);
            string sql = "";
            status = status == "正常" ? "1" : "0";

            sql = $"UPDATE info_item_prop SET status = '{status}' WHERE company_id ={companyID} AND item_id in ({rows});";
            sql += $"UPDATE info_item_prop SET status = '{status}' WHERE company_id ={companyID} AND son_mum_item in ({rows});";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            // 分销商商品同步
            dynamic syncRet = await ResellerService.BatchEditItemInfoSync(new
            {
                itemIdList = rows,
                editType = "status",
                operKey = (string)data.operKey,
                status = status
            }, cmd);
            cmd.CommandText = sql;
            if (syncRet.result != "OK")
            {

                return Json(new { result = "Error", msg = syncRet.msg });
            }
            return Json(new { result = "OK", msg = "" });
        }
        [HttpPost]
        public async Task<JsonResult> BatchSetSupplier([FromBody] dynamic data)
        {

            ItemsViewModel model = new ItemsViewModel(cmd);
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
            string status = data.status;
            string rows = string.Join(",", data.rows);
            string sql = @$"UPDATE info_item_prop SET supplier_id = {data.supplierID} WHERE company_id ={companyID} AND item_id in  ({rows} )";

            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            return Json(new { result = "OK", msg = "" });
        }
        [HttpPost]
        public async Task<JsonResult> BatchDelete([FromBody] dynamic data)
        {
            ItemsViewModel model = new ItemsViewModel(cmd);
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);

            var msg = "";
            var item_name = "";
            //List<ExpandoObject> dat = null;
            if (data.type == "勾选")
            {
                ////2.加载已使用商品
                #region
                string sql = "";
                Dictionary<string, string> dicUsedItem = new Dictionary<string, string>();
                CMySbDataReader dr;
                SQLQueue QQ = new SQLQueue(cmd);
                sql = @$"
	        select t.item_id,item_name
	        from (
	        select item_id from sheet_sale_detail where  company_id={Token.CompanyID}
	        union 
	        select item_id from sheet_sale_order_detail where  company_id = {Token.CompanyID}
			        union 
             select item_id from sheet_order_item_detail where  company_id = {Token.CompanyID}
			            union 
             select item_id from sheet_item_ordered_adjust_detail where  company_id = {Token.CompanyID}
			             union 
             select item_id from sheet_buy_detail where  company_id ={Token.CompanyID} 
			            union 
            select item_id from sheet_move_detail where  company_id = {Token.CompanyID} 
			            union 
             select item_id from sheet_inventory_detail where    company_id = {Token.CompanyID} ) t
             LEFT JOIN info_item_prop ip on t.item_id =ip.item_id and ip.company_id ={Token.CompanyID}
         where item_name is not null

        ;";
                QQ.Enqueue("usedItems", sql);
                dr = await QQ.ExecuteReaderAsync();
                while (QQ.Count > 0)
                {
                    string sqlName = QQ.Dequeue();
                    if (sqlName == "usedItems")
                    {
                        var lstUsedItem = CDbDealer.GetRecordsFromDr(dr, false);
                        foreach (dynamic b in lstUsedItem)
                        {
                            dicUsedItem.Add(b.item_id, b.item_name);
                        }

                    }

                }
                QQ.Clear();
                #endregion



                foreach (var id in data.rows)
                {
                    var item_id = id.ToString();
                    if (dicUsedItem.ContainsKey(item_id))
                    {
                        item_name = dicUsedItem[item_id];
                        msg += $" {item_name} <br/>";
                    }



                }
            }
            if (msg != "") return Json(new { msg = $"无法删除  以下商品已使用： <br/> {msg}" });
            else
            {
                cmd.ActiveDatabase = "";
                var tran = cmd.Connection.BeginTransaction();
                try
                {

                    string rows = string.Join(",", data.rows);
                    cmd.CommandText = @$"                 
                    delete from info_item_prop where  company_id ={companyID} AND item_id in  ({rows} );
                    DELETE from info_item_multi_unit where company_id={companyID} and item_id in({rows} )
                    ";
                    await cmd.ExecuteNonQueryAsync();
                    tran.Commit();
                    return Json(new { result = "OK" });
                }
                catch (Exception e)
                {
                    tran.Rollback();
                    throw new Exception("批量删除出现错误", e);
                }



            }

        }

        [HttpPost]
        public async Task<JsonResult> BatchSetBatchLevel([FromBody] dynamic data)
        {

            ItemsViewModel model = new ItemsViewModel(cmd);
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
            string batchLevel = data.batchLevel;
            string rows = string.Join(",", data.rows);
            string sql = "";

            if (batchLevel == "produceDate")
                sql = $"UPDATE info_item_prop SET batch_level = 1 WHERE company_id ={companyID} AND item_id in  ({rows} )";
            else if (batchLevel == "batch")
            {
                sql = $"UPDATE info_item_prop SET batch_level = 2 WHERE company_id ={companyID} AND item_id in  ({rows} )";
            }
            else if (batchLevel == "none")
            {
                sql = $"UPDATE info_item_prop SET batch_level = null WHERE company_id ={companyID} AND item_id in  ({rows} )";
            }

            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            return Json(new { result = "OK", msg = "" });
        }

        [HttpPost]
        public async Task<JsonResult> UpdateItemMallStatus([FromBody] dynamic data)
        {
            // CMySbTransaction tran = cmd.Connection.BeginTransaction();
            try
            {
                Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
                int mallStatus = data.mall_status;
                bool mallStatusFlag = mallStatus == 1 ? true : false;
                string itemIds = string.Join(",", data.rows);
                string sql = $"UPDATE info_item_prop SET mall_status = {mallStatus} WHERE company_id ={companyID} AND item_id in ({itemIds} )";
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
                if (!mallStatusFlag)
                {
                    sql = $"UPDATE mall_items SET on_sale = false WHERE company_id ={companyID} AND item_id in ({itemIds} )";
                    cmd.CommandText = sql;
                    await cmd.ExecuteNonQueryAsync();
                }


                // tran.Commit();
                return Json(new { result = "OK", msg = "" });
            }
            catch (Exception e)
            {
                // tran.Rollback();
                return Json(new { result = "Error", msg = "上下架失败" });
            }


        }

        [HttpPost]
        public async Task<JsonResult> SetMallUnits([FromBody] dynamic data)
        {
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
            Console.WriteLine(data);
            string mall_units_name = data.mall_units_name;
            var arr_mall_units_name = mall_units_name.Split(',');
            string itemIds = string.Join(",", data.rows);
            string mall_units = string.Join(",", arr_mall_units_name
                    .Select(unit => unit.Trim())  // 清除可能的前后空格
                    .Distinct()                  // 去除重复项
                    .Select(unit => unit.ToLower() == "大" ? "b" :
                      unit.ToLower() == "中" ? "m" :
                      unit.ToLower() == "小" ? "s" : ""));
            CMySbTransaction tran = cmd.Connection.BeginTransaction();
            try
            {
                string sql = $"UPDATE info_item_prop SET mall_units = '{mall_units}' WHERE company_id ={companyID} AND item_id in ({itemIds} )";
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
                tran.Commit();
                return Json(new { result = "OK", msg = "" });
            }
            catch (Exception e)
            {
                tran.Rollback();
                Console.WriteLine(e);
                return Json(new { result = "Error", msg = "上架单位设置失败" });
            }

        }

        [HttpPost]
        public async Task<JsonResult> BatchSetMallStatus([FromBody] dynamic data)
        {
            CMySbTransaction tran = cmd.Connection.BeginTransaction();
            try
            {
                Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
                int mallStatus = data.mall_status;
                bool mallStatusFlag = mallStatus == 1 ? true : false;
                string itemIds = string.Join(",", data.rows);
                if (data.rows == null || ((JArray)data.rows).Count == 0)
                {
                    return Json(new { result = "Error", msg = "请勾选要操作的商品" });
                }

                // 更新商品档案的商城状态
                string sql = $"UPDATE info_item_prop SET mall_status = {mallStatus} WHERE company_id = {companyID} AND item_id in ({itemIds})";
                cmd.CommandText = sql;
                int affectedRows = await cmd.ExecuteNonQueryAsync();
                if (!mallStatusFlag)
                {
                    sql = $"UPDATE mall_items SET on_sale = false WHERE company_id = {companyID} AND item_id in ({itemIds})";
                    cmd.CommandText = sql;
                    await cmd.ExecuteNonQueryAsync();
                }

                tran.Commit();
                string statusText = mallStatusFlag ? "上架" : "下架";
                return Json(new { result = "OK", msg = $"成功{statusText} {affectedRows} 个商品" });
            }
            catch (Exception e)
            {
                tran.Rollback();
                return Json(new { result = "Error", msg = $"批量操作失败：{e.Message}" });
            }
        }
    }


    public class GoodClass
    {
        public string id = "";
        public string name = "";
        public string owner = "";
        public bool hasSonClass = false;
        public int ClassIndex = -1;
        public string combine_sta = "";
        public string select_style = "";
    }
}
