﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.WebAPI;
using ArtisanManage.YingJiangMallMini.Model;
using System.Linq;
using Microsoft.CodeAnalysis.Elfie.Diagnostics;
using HuaweiCloud.SDK.Ocr.V1.Model;
using static OfficeOpenXml.ExcelErrorValue;

namespace ArtisanManage.YingJiangMallMini.Dao;

public class MallMiniRedPacketDao
{
    private readonly CMySbCommand _cmd;

    public MallMiniRedPacketDao(CMySbCommand cmd)
    {
        _cmd = cmd;
    }


    /// <summary>
    /// 红包余额
    /// </summary>
    /// <param name="parameter"></param>
    /// <returns></returns>
    public async Task<MallMiniAccountSummary> GetRedPacketBalanceDao(MallMiniSummaryAccountParameter parameter)
    {
        string companyId = parameter.companyId;
        int supcustId = parameter.supcustId;
        string sql = $@"
select 
    rpb.company_id, 
    rpb.supcust_id, 
    rpb.balance
from red_packet_balance rpb 
where rpb.company_id = @companyId and rpb.supcust_id = @supcustId;
";
        _cmd.CommandText = sql;
        _cmd.Parameters.AddWithValue("@companyId", int.Parse(companyId));
        _cmd.Parameters.AddWithValue("@supcustId", supcustId);
        MallMiniAccountSummary redPacketBalance =
            await CDbDealer.Get1RecordFromSQLAsync<MallMiniAccountSummary>(sql, _cmd) ??
            new MallMiniAccountSummary(companyId, supcustId, 0);
        return redPacketBalance;
    }

    /// <summary>
    /// 红包历史
    /// </summary>
    /// <param name="parameter"></param>
    /// <returns></returns>
    public async Task<List<MiniRedPacketHistory>> GetRedPacketHistoryDao(MallMiniGetRedPacketHistoryParameter parameter)
    {
        string companyId = parameter.companyId;
        int supcustId = parameter.supcustId;
        string startDate = parameter.startDate;
        string endDate = parameter.endDate;
        int pageSize = parameter.pageSize;
        int pageNo = parameter.pageNo;
        string sqlCount = pageNo == 1 ? "count(*) over () as record_total " : "-1 as record_total";

        string sql = $@"

select 
    rph.flow_id, 
    rph.company_id, 
    rph.supcust_id, 
    rph.happen_time, 
    rph.change_amount, 
    rph.change_type, 
    rph.relate_sheet_id, 
    rph.relate_sheet_type,
    {sqlCount}
from red_packet_history rph
where rph.company_id = @companyId
and rph.supcust_id = @supcustId
and happen_time >= @startDate::timestamp
and happen_time <= @endDate::timestamp
order by rph.happen_time desc 
limit @pageSize offset (@pageNo - 1) * @pageSize;
";
        _cmd.Parameters.AddWithValue("@companyId", int.Parse(companyId));
        _cmd.Parameters.AddWithValue("@supcustId", supcustId);
        _cmd.Parameters.AddWithValue("@startDate", startDate);
        _cmd.Parameters.AddWithValue("@endDate", endDate);
        _cmd.Parameters.AddWithValue("@pageSize", pageSize);
        _cmd.Parameters.AddWithValue("@pageNo", pageNo);
        List<MiniRedPacketHistory> result = await CDbDealer.GetRecordsFromSQLAsync<MiniRedPacketHistory>(sql, _cmd) ??
                                            new List<MiniRedPacketHistory>();
        return result;
    }


    public async Task<MallMiniRedPacketBaseInfo> GetMallMiniRedPacketBaseInfoDao(MallMinBaseParameter parameter)
    {
        string customer_condi = " rgc.customers::TEXT LIKE '%\"supcust_id\": \"" + parameter.supcustId + "\"%'OR";
        string sql = $@"
WITH rg AS (
    SELECT region_id, sup_group, company_id
    FROM info_supcust
    WHERE company_id =  @companyId
    AND supcust_id = @supcustId
),
    rgc as (
        select
            rr.restrict_range->'group_id' AS group_id,
            rr.restrict_range->'customers' AS customers,
            rr.restrict_range->'region_id' AS region_id,
            company_id,status,red_packet_id
        from
            (SELECT restrict_range,company_id,red_packet_type,status,red_packet_id
                FROM sheet_red_packet_plan) rr
        WHERE company_id =  @companyId
        AND red_packet_type = 'register_reward'
        AND status = 1
    )
SELECT
    CASE
    WHEN r.register_log_amount > 0 THEN 0
    WHEN reco.register_reward1 IS NOT NULL THEN reco.register_reward1::NUMERIC
    ELSE s.register_reward
    END AS register_reward,
    CASE
        WHEN (reco.register_reward1 IS NOT NULL and r.register_log_amount = 0) THEN reco.red_packet_id::TEXT
        WHEN r.register_log_amount > 0 THEN null
        WHEN r.register_log_amount = 0 THEN 'setting'
        ELSE null
    END AS red_packet_id,
    s.purchase_reward,
    s.use_limit_type,
    s.use_limit_amount,
    m.balance,
    (CASE WHEN r.register_log_amount > 0 THEN TRUE ELSE FALSE END) AS user_registed,
    rg.region_id,
    rg.sup_group
FROM
    red_packet_setting s
LEFT JOIN
    red_packet_balance m ON m.company_id = s.company_id AND m.supcust_id = @supcustId
LEFT JOIN rg ON rg.company_id = s.company_id
LEFT JOIN (
    SELECT
        CASE
        WHEN plan.red_packet_amount ~ '^[0-9]+(\.[0-9]+)?~[0-9]+(\.[0-9]+)?$' THEN
            CAST(
                (
                    random() *
                    (CAST(split_part(plan.red_packet_amount, '~', 2) AS NUMERIC) -
                     CAST(split_part(plan.red_packet_amount, '~', 1) AS NUMERIC))
                ) + CAST(split_part(plan.red_packet_amount, '~', 1) AS NUMERIC)
                AS NUMERIC(10,2)
            )
        ELSE
            CAST(plan.red_packet_amount AS NUMERIC(10,2))
        END AS register_reward1,
        plan.company_id,
        plan.red_packet_id
    FROM sheet_red_packet_plan plan
    LEFT JOIN rg ON rg.company_id = plan.company_id
    LEFT JOIN rgc ON rgc.company_id = plan.company_id and rgc.red_packet_id=plan.red_packet_id
    WHERE plan.company_id =  @companyId
    AND (
        {customer_condi} rgc.group_id::TEXT LIKE '%""group_id"":[' || rg.sup_group || ']%'
        OR rgc.region_id::TEXT LIKE '%""region_id"":[' || rg.region_id || ']%'
        OR (rgc.customers::TEXT = '[]'
        AND rgc.group_id::TEXT = '[]'
        AND rgc.region_id::TEXT = '[]')
    )
    AND plan.red_packet_type = 'register_reward'
    AND plan.status = 1
    ORDER BY register_reward1 DESC
    LIMIT 1
) reco ON reco.company_id = s.company_id
LEFT JOIN (
	select count(*) as register_log_amount
    from red_packet_history
    where company_id = @companyId and supcust_id = @supcustId and change_type = '{RedPacketController.RedPacketChangeType.RegisterReward}'
    limit 10
) r on true
WHERE s.company_id =  @companyId;";
        _cmd.Parameters.AddWithValue("@companyId", int.Parse(parameter.companyId));
        _cmd.Parameters.AddWithValue("@supcustId", parameter.supcustId);
        MallMiniRedPacketBaseInfo result =
            await CDbDealer.Get1RecordFromSQLAsync<MallMiniRedPacketBaseInfo>(sql, _cmd);
        if (result == null)
        {
            result = new MallMiniRedPacketBaseInfo();
        }
        return result;
    }
    public async Task<List<MallMiniRedPacketSend>> GetMallMiniRedPacketSendDao(MallMinSendParameter parameter)
    {
        string customer_condi = " rgc.customers::TEXT LIKE '%\"supcust_id\": \"" + parameter.supcustId + "\"%'OR";
        string sql = $@"
WITH rg AS (
    SELECT rtrim(other_region, '/') AS other_region, sup_group, company_id
    FROM info_supcust
    WHERE company_id = @companyId
    AND supcust_id = @supcustId
),
    rgc as (
        select
            rr.restrict_range->'group_id' AS group_id,
            rr.restrict_range->'customers' AS customers,
            rr.restrict_range->'region_id' AS region_id,
            company_id,status,red_packet_id
        from
            (SELECT
            restrict_range,company_id,red_packet_type,status,red_packet_id
        FROM sheet_red_packet_plan) rr
        WHERE company_id = @companyId
        AND red_packet_type = 'proactively_send'
        AND status = 1
    ),
r as (
    select count(*) as register_log_amount,relate_plan_id
    from red_packet_history
    where company_id = @companyId  and change_type = 'proactively_send'
    group by relate_plan_id
)
SELECT
    reco.*,
    m.balance,
    COALESCE(r.register_log_amount, 0) as user_picked_num,
    rg.other_region,
    rg.sup_group,
    can_pick_amount,
    GREATEST(
    reco.red_packet_max_num 
    - COALESCE(r.register_log_amount, 0) 
    - COALESCE(r1.register_log_amount, 0),
    0
) AS can_pick_num
FROM
    red_packet_setting s
LEFT JOIN
    red_packet_balance m ON m.company_id = s.company_id AND m.supcust_id = @supcustId
LEFT JOIN rg ON rg.company_id = s.company_id
LEFT JOIN (
    SELECT
        CASE
        WHEN plan.red_packet_amount ~ '^[0-9]+(\.[0-9]+)?~[0-9]+(\.[0-9]+)?$' THEN
            CAST(
                (
                    random() *
                    (CAST(split_part(plan.red_packet_amount, '~', 2) AS NUMERIC) -
                     CAST(split_part(plan.red_packet_amount, '~', 1) AS NUMERIC))
                ) + CAST(split_part(plan.red_packet_amount, '~', 1) AS NUMERIC)
                AS NUMERIC(10,2)
            )
        WHEN plan.red_packet_amount ~ '^[0-9]+(\.[0-9]+)?$' THEN
            CAST(plan.red_packet_amount AS NUMERIC(10,2))
        ELSE
            0
        END AS can_pick_amount,
        plan.red_packet_amount,
        plan.red_packet_name,
        plan.red_packet_id,
        plan.restrict_time,
        plan.red_packet_max_num,
        plan.company_id
    FROM sheet_red_packet_plan plan
    LEFT JOIN rg ON rg.company_id = plan.company_id
    LEFT JOIN rgc ON rgc.company_id = plan.company_id and rgc.red_packet_id=plan.red_packet_id
    WHERE plan.company_id = @companyId
    AND (
        {customer_condi} rgc.group_id::TEXT = '[""all""]'
        OR EXISTS (
            SELECT 1
            FROM jsonb_array_elements_text(rgc.group_id) AS gid
            WHERE  rg.sup_group::TEXT LIKE gid
        )
        OR rgc.region_id::TEXT = '[""all""]'
        OR EXISTS (
            SELECT 1
            FROM jsonb_array_elements_text(rgc.region_id) AS rid
            WHERE  rg.other_region::TEXT LIKE rid
        )
        OR (rgc.customers::TEXT = '[]'
        AND rgc.group_id::TEXT = '[]'
        AND rgc.region_id::TEXT = '[]')
    )
    AND plan.red_packet_type = 'proactively_send'
    AND plan.status = 1
) reco ON reco.company_id = s.company_id
LEFT JOIN  r on reco.red_packet_id::TEXT=r.relate_plan_id and supcust_id = @supcustId
LEFT JOIN  r as r1 on reco.red_packet_id::TEXT=r1.relate_plan_id
WHERE s.company_id = @companyId
AND (reco.restrict_time ='{{}}' OR CURRENT_TIMESTAMP BETWEEN (reco.restrict_time->>'start_time')::timestamp AND (reco.restrict_time->>'end_time')::timestamp)
AND (reco.red_packet_max_num IS NULL OR reco.red_packet_max_num > COALESCE(r.register_log_amount, 0));
";
        _cmd.Parameters.AddWithValue("@companyId", int.Parse(parameter.companyId));
        _cmd.Parameters.AddWithValue("@supcustId", parameter.supcustId);
        List<MallMiniRedPacketSend> result =
            await CDbDealer.GetRecordsFromSQLAsync<MallMiniRedPacketSend>(sql, _cmd) ??
            new List<MallMiniRedPacketSend>();
        if (result.Count == 0 || result == null)
        {
            result = new List<MallMiniRedPacketSend>();
        }
        return result;
    }
    public async Task<List<GetPurchaseRewardDao>> GetMallMiniRedPacketPurchaseRewardDao(MallMinPurchaseRewardParameter parameter)
    {
        string itemId_condi = "";
        string json_condi = " AND rp.restrict_range @> '{\"customers\": [{\"supcust_id\": \""+parameter.supcustId+"\"}]}'";
        string itemIdList = "";
        if (parameter.sheet_rows != null && parameter.sheet_rows.Count > 0)
        {
            itemIdList = parameter.sheet_rows != null ? "(" + string.Join(",", parameter.sheet_rows.Select(row => row.item_id)) + ")": "";
            itemId_condi = $" and item_id in {itemIdList}";
        }
        string sql = $@"
WITH s AS (SELECT supcust_id, other_region, sup_group, company_id
           FROM info_supcust
           WHERE company_id = @companyId AND supcust_id = @supcustId),
    r as (select count(*) as picked_log_amount, relate_plan_id
           from red_packet_history
           where company_id = @companyId
             and change_type = 'purchase_reward'
             and supcust_id = @supcustId
           group by relate_plan_id),
     sup_basic_reached as (SELECT DISTINCT rp.*
                           FROM sheet_red_packet_plan rp
                                    LEFT JOIN s ON s.company_id = rp.company_id
                                    LEFT JOIN r ON r.relate_plan_id = rp.red_packet_id::TEXT
                           WHERE rp.company_id = @companyId
                             AND rp.status = 1
                             AND rp.red_packet_type = 'purchase_reward'
                             AND (restrict_time::jsonb = '{{}}'::jsonb
                               OR (
                                              CURRENT_TIMESTAMP >= (restrict_time::jsonb ->> 'start_time')::timestamp
                                          AND CURRENT_TIMESTAMP <= (restrict_time::jsonb ->> 'end_time')::timestamp
                                      )
                               )
                             AND COALESCE(r.picked_log_amount, 0) < COALESCE(rp.red_packet_max_num, 0)),
     customers_matched AS (SELECT DISTINCT rp.*
                           FROM sup_basic_reached rp
                                    LEFT JOIN s ON s.company_id = rp.company_id
                           WHERE true
                             {json_condi}
                            ),
     other_matched AS (SELECT DISTINCT rp.*
                       FROM sup_basic_reached rp
                                LEFT JOIN s ON s.company_id = rp.company_id
                       WHERE  (
                           rp.restrict_range -> 'group_id' @> to_jsonb(s.sup_group::TEXT)
                               OR EXISTS (SELECT 1
                                          FROM jsonb_array_elements_text(rp.restrict_range -> 'region_id') AS region_value
                                          WHERE s.other_region LIKE region_value || '%')
                               OR (
                                    (rp.restrict_range -> 'group_id')::jsonb = '[]'::jsonb
                                    AND (rp.restrict_range -> 'region_id')::jsonb = '[]'::jsonb
                                    AND (rp.restrict_range -> 'customers')::jsonb = '[]'::jsonb
                                  ))),
     supcondi_reached AS (SELECT *
                          FROM customers_matched
                          UNION ALL
                          SELECT *
                          FROM other_matched
                          WHERE NOT EXISTS (SELECT 1 FROM customers_matched)
     ),
     i AS (SELECT item_id,
                  item_name,
                  other_class,
                  item_class,
                  class_name,
                  item_brand,
                  brand_name,
                  b.company_id
           FROM info_item_prop iip
                    LEFT JOIN info_item_class c ON iip.company_id = c.company_id AND iip.item_class = c.class_id
                    LEFT JOIN info_item_brand b ON iip.company_id = b.company_id AND iip.item_brand = b.brand_id
           WHERE iip.company_id = @companyId
             {itemId_condi}),
     itemlist_matched AS (SELECT DISTINCT sr.*, i.item_id, 1 AS priority
                          FROM supcondi_reached sr
                                   JOIN i ON sr.company_id = i.company_id
                          WHERE sr.company_id = @companyId
                            AND EXISTS (SELECT 1
                                        FROM jsonb_array_elements(sr.restrict_goods -> 'itemList') AS elem
                                        WHERE (elem ->> 'item_id')::BIGINT = i.item_id)),
     itemlist_policy_json AS (SELECT im.item_id,
                                     jsonb_build_object(
                                             'reach_cost', im.reach_cost,
                                             'red_packet_amount', im.red_packet_amount,
                                             'red_packet_id', red_packet_id
                                     ) AS policy
                              FROM itemlist_matched im),
     itemlist_policy_grouped AS (SELECT item_id,
                                        NULL::INTEGER     AS item_brand,
                                        NULL::TEXT        AS item_class,
                                        jsonb_agg(policy) AS policy,
                                        'item' as type
                                 FROM itemlist_policy_json
                                 GROUP BY item_id),
     othergoods_matched_brand AS (SELECT DISTINCT sr.*, i.item_id, 2 AS priority, i.item_brand AS item_brand, NULL AS item_class,'brand' as type
                                  FROM supcondi_reached sr
                                           LEFT JOIN i ON sr.company_id = i.company_id
                                  WHERE sr.company_id = @companyId
                                    AND NOT EXISTS (SELECT 1
                                                    FROM itemlist_policy_grouped ilpg
                                                    WHERE ilpg.item_id = i.item_id)
                                    AND (
                                      sr.restrict_goods ->> 'brands' = '[""all""]'
                                          OR sr.restrict_goods -> 'brands' @> to_jsonb(i.item_brand::TEXT)
                                      )
                                    AND (
                                      sr.restrict_goods -> 'items_ban' IS NULL
                                          OR NOT (sr.restrict_goods -> 'items_ban') @> to_jsonb(i.item_id::TEXT)
                                      )),
-- class 匹配的部分
     othergoods_matched_class AS (SELECT DISTINCT sr.*, i.item_id, 2 AS priority, NULL AS item_brand, i.other_class AS item_class,'class' as type
                                  FROM supcondi_reached sr
                                           LEFT JOIN i ON sr.company_id = i.company_id
                                  WHERE sr.company_id = @companyId
                                    AND NOT EXISTS (SELECT 1
                                                    FROM itemlist_policy_grouped ilpg
                                                    WHERE ilpg.item_id = i.item_id)
                                    AND (
                                      sr.restrict_goods ->> 'classes' = 'all'
                                          OR EXISTS (SELECT 1
                                                     FROM jsonb_array_elements_text(sr.restrict_goods -> 'classes') AS classes_value
                                                     WHERE i.other_class LIKE classes_value || '%')
                                      )
                                    AND (
                                      sr.restrict_goods -> 'items_ban' IS NULL
                                          OR NOT (sr.restrict_goods -> 'items_ban') @> to_jsonb(i.item_id::TEXT)
                                      )),
    othergoods_matched_all AS (SELECT DISTINCT sr.*, i.item_id, 2 AS priority, NULL AS item_brand, NULL AS item_class,'all' as type
                                  FROM supcondi_reached sr
                                           LEFT JOIN i ON sr.company_id = i.company_id
                                  WHERE sr.company_id = @companyId
                                    AND NOT EXISTS (SELECT 1
                                                    FROM itemlist_policy_grouped ilpg
                                                    WHERE ilpg.item_id = i.item_id)
                                    AND (
                                                sr.restrict_goods -> 'brands' = '[]'::jsonb
                                            AND sr.restrict_goods -> 'classes' = '[]'::jsonb
                                            AND sr.restrict_goods -> 'itemList' = '[]'::jsonb
                                        )
                                    AND (
                                      sr.restrict_goods -> 'items_ban' = '[]'::jsonb
                                          OR NOT (sr.restrict_goods -> 'items_ban') @> to_jsonb(i.item_id::TEXT)
                                      )),
     othergoods_union AS (SELECT item_id, item_brand, null as item_class, reach_cost, red_packet_amount, red_packet_id,type
                          FROM othergoods_matched_brand
                          UNION ALL
                          SELECT item_id, null as item_brand, item_class, reach_cost, red_packet_amount, red_packet_id,type
                          FROM othergoods_matched_class
                          UNION ALL
                          SELECT item_id, null as item_brand, null as item_class, reach_cost, red_packet_amount, red_packet_id,type
                          FROM othergoods_matched_all),
     othergoods_policy_grouped AS (SELECT item_id,
                                          item_brand,
                                          item_class,
                                          jsonb_agg(
                                                  jsonb_build_object(
                                                          'reach_cost', reach_cost,
                                                          'red_packet_amount', red_packet_amount,
                                                          'red_packet_id', red_packet_id
                                                  )
                                          ) AS policy,
                                            type
                                   FROM othergoods_union
                                   GROUP BY item_id, item_brand, item_class,type),
     union_policy AS (SELECT *
                      FROM itemlist_policy_grouped
                      UNION ALL
                      SELECT ogpg.*
                      FROM othergoods_policy_grouped ogpg
                      WHERE NOT EXISTS (SELECT 1
                                        FROM itemlist_policy_grouped ilpg
                                        WHERE ilpg.item_id = ogpg.item_id)),
     purchase_reward_setting AS (SELECT -1            AS item_id,
                                        NULL::INTEGER AS item_brand,
                                        NULL::TEXT    AS item_class,
                                        jsonb_agg(
                                                jsonb_build_object(
                                                        'reach_cost', 0,
                                                        'red_packet_amount', purchase_reward
                                                )
                                        )             AS policy,
                                     'all' as type
                                 FROM red_packet_setting
                                 WHERE company_id = @companyId),
     final_union_policy AS (SELECT *
                            FROM union_policy
                            UNION ALL
                            SELECT *
                            FROM purchase_reward_setting)
SELECT *
FROM final_union_policy;

";
        _cmd.Parameters.AddWithValue("@companyId", int.Parse(parameter.companyId));
        _cmd.Parameters.AddWithValue("@supcustId", parameter.supcustId);
        List<GetPurchaseRewardDao> result = await CDbDealer.GetRecordsFromSQLAsync<GetPurchaseRewardDao>(sql, _cmd);
        return result;
    }
    public async Task<List<GetRedpacketPlanDao>> GetMallMiniRedPacketPlanListDao(GetPurchaseRewardParameter parameter)
    {
        if (parameter.redPacketId != null)
        {
            var red_packet_id_condi = " and red_packet_id in  ("+parameter.redPacketId+")";
            string sql = $@"
 select red_packet_id,red_packet_name,red_packet_amount,restrict_goods,reach_cost
 from  sheet_red_packet_plan rp
 where company_id=@companyId {red_packet_id_condi}

";
            _cmd.Parameters.AddWithValue("@companyId", int.Parse(parameter.companyId));
            List<GetRedpacketPlanDao> result = await CDbDealer.GetRecordsFromSQLAsync<GetRedpacketPlanDao>(sql, _cmd);
            return result;
        }
        else
        {
            return new List<GetRedpacketPlanDao>();
        }
    }
}