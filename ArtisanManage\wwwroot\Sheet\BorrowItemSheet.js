function onPageReady(sheetRows) {

    console.log(window.g_queriedItems)
    mmSheetInit();
    function renderCopySheet() {
        if (requestString('copy')) {
            var params = paramsFromSrcWindow
            if ((params.sheet_type).indexOf('BUY') != -1) params.supcust_id = "";

            GetRowsByCopyFromSheets(params)

        }
    }

    document.all.ckPrintSmallBarcode.checked = true
    if (window.g_companySetting) {
        if (window.g_companySetting.sheetShowBarcodeStyle != '2') {
            document.all.ckPrintSmallBarcode.checked = false
        }
    }

    var theme = "";
    var datafields = [];
    var data = new Array; var rowscount = 10;

    var approve_time = $('#approve_time').text();
    /*
    var canSave = window.getRightValue('sale.sheetSale.save')
    var canApprove = window.getRightValue('sale.sheetSale.approve')
    var canReview = window.getRightValue('sale.sheetSale.review')
    var canRed = window.getRightValue('sale.sheetSale.red')

    if (canMake != "true") $('#btnSave').css('display', 'none');
    if (canApprove != "true") $('#btnApprove').css('display', 'none');
    if (canReview != "true") $('#btnReview').css('display', 'none');
    if (canRed != "true") $('#btnRed').css('display', 'none');
    if (canRed != "true") $('#btnRedAndChange').css('display', 'none');
    */
    if (approve_time) {
        var red_flag = $('#red_flag').val()
        if (red_flag) {
            $("#btnRedAndChange").attr('disabled', true);
        }
    } else {
        $("#btnRedAndChange").attr('disabled', true);
    }

    var source =
    {
        sort: funcSortByColumn,
        localdata: data,
        unboundmode: true,
        totalrecords: 10,
        datafields: datafields,
        updaterow: function (rowid, rowdata) {
        }
    }
    window.g_gridSource = source
    var dataAdapter = new $.jqx.dataAdapter(source);

    var fixColCss = 'jqx-widget-header';
    if (theme !== '') fixColCss += ' jqx-widget-header-' + theme;


    var canSeeBuyPrice = false;
    var canSeeSalePrice = true;
    if (window.g_operRights.delicacy) {
        if (window.g_operRights.delicacy.seeInPrice
            && window.g_operRights.delicacy.seeInPrice.value) {
            canSeeBuyPrice = true;
        }
        if (window.g_operRights.delicacy.seeSalePrice && !window.g_operRights.delicacy.seeSalePrice.value) {
            canSeeSalePrice = false;
        }
    }
    function createeditor_trade_type_isborrow(row, cellvalue, editor, cellText, width, height) {

        var element = $('<div id="txtUnitNo"></div >');
        editor.append(element);
        var inputElement = editor.find('div')[0];

        var dataFields = new Array({ datafield: "trade_type", text: "", width: 50, hidden: true },
            { datafield: "trade_type_name", text: "", width: 50 }
        );
        
        $(inputElement).jqxInput({
            placeHolder: "交易方式", height: height, width: width,
            borderShape: "none",
            buttonUsage: 'list',
            showHeader: true,
            dropDownHeight: 160,
            displayMember: "trade_type_name",
            valueMember: "trade_type",
            dataFields: dataFields,
            searchFields: [],
            maxRecords: 4,
            url: '',
            source: [{ trade_type: 'J', trade_type_name: '借货' }, { trade_type: 'H', trade_type_name: '还货' }],
            renderer: function (itemValue, inputValue) {
                var terms = inputValue.split(/,\s*/);
                terms.pop();
                terms.push(itemValue);
                debugger;
                return itemValue;
            }
        }); 
        $(inputElement).on('optionSelected',
            function (a, b) {
                var value = $(inputElement).val();
                var trade_type = value.value;

                var name = '';
                if (value.name)
                    name = value.name;
                var cell = $('#jqxgrid').jqxGrid('getselectedcell');
                var rowIndex = cell.rowindex;
                var item_id = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, "item_id");
                if (item_id) {
                    if (trade_type == 'J') {
                        // 根据借货模式设置价格和金额
                        var borrowMode = $('#borrow_mode').val();
                        if (borrowMode && borrowMode.value === 'QTY') {
                            // 按数量模式：价格和金额为0
                            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, "real_price", '0');
                            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, "sub_amount", '0');
                        }
                        $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, "remark", '借货');

                        var rowData = $('#jqxgrid').jqxGrid('getrowdata', cell.rowindex)
                        cellendedit({ args: { datafield: 'trade_type', rowindex: cell.rowindex, row: rowData, value: value, oldvalue: '' } })
                    }
                }
            })
    }
    var sheetShowPriceList = true
    if (window.g_companySetting && window.g_companySetting.sheetShowPriceList && window.g_companySetting.sheetShowPriceList.toLowerCase() == 'false')
        sheetShowPriceList = false;

    window.GridData = {
        source: dataAdapter,
        showaggregates: true,
        showstatusbar: true,
        columnsheight: 36,
        rowsheight: 36,
        statusbarheight: 30,
        pageable: false,
        // autoheight: true,
        // sortable: true,sortmode:'many',
        editable: true,
        columnsresize: true,
        ready: function () {
            $("#jqxgrid").jqxGrid('focus');
        },
        renderstatusbar: function (statusbar) {
        },

        editmode: 'click', // 'selectedcell',
        selectionmode: 'multiplecellsadvanced', //'singlecell',// 'multiplecellsadvanced',
        hoverrow: true,
        theme: theme,
        sortable: true,
        cellhover: cellhover,
        handlekeyboardnavigation: handlekeyboardnavigation,
        columns: [
            {
                text: '',
                sortable: false,
                filterable: false,
                editable: false,
                pinned: true,
                groupable: false,
                draggable: false,
                resizable: false,
                datafield: '',
                columntype: 'number',
                width: 45,
                cellclassname: fixColCss,
                cellsrenderer: pinCellsRenderer,
                renderer: leftTopCellRenderer
            },
            {
                text: '商品名称',
                sortable: true,
                datafield: 'item_id',
                displayfield: 'item_name',
                width: '200', alwaysShow: true, align: 'center',
                columntype: 'template',
                createeditor: createeditor_item_name,
                initeditor: initeditor_item_name,
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val();
                    return v;
                },
                cellsrenderer: function (row, column, value, p4, p5, rowData) {
                    var order_sub_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'order_sub_id');
                    var disp_flow_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'disp_flow_id');
                    var trade_type = $('#jqxgrid').jqxGrid('getcellvalue', row, 'trade_type');
                    var order_item_sheets_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'order_item_sheets_id');
                    var order_item_sheets_no = $('#jqxgrid').jqxGrid('getcellvalue', row, 'order_item_sheets_no');
                    if (order_item_sheets_id && order_item_sheets_no) {
                        order_item_sheets_id = order_item_sheets_id.replace(',,', ',')
                        order_item_sheets_no = order_item_sheets_no.replace(',,', ',')
                        var arr = order_item_sheets_no.split(',')
                        order_item_sheets_no = ''
                        arr.forEach(sheet_no => {
                            if (sheet_no) {
                                if (order_item_sheets_no) order_item_sheets_no += ','
                                order_item_sheets_no += sheet_no
                            }
                        })
                    }
                    var attrOptNames = ''
                    if (rowData.attr_qty) {
                        if (!Array.isArray(rowData.attr_qty))
                            rowData.attr_qty = JSON.parse(rowData.attr_qty)

                        rowData.attr_qty.forEach(opt => {

                            var optName = ''
                            for (var propName in opt) {
                                if (propName.indexOf('optName_') == 0) {
                                    var optName = opt[propName]
                                    break
                                }
                            }
                            if (rowData.attr_qty.length > 1) {
                                optName += '(' + opt.qty + ')'
                            }
                            attrOptNames += optName

                        })
                    }
                    var lblAttrOpt = ''
                    if (attrOptNames) {
                        lblAttrOpt =
                            `<label style ="margin-left:10px;background:white;color:#333388;border-radius:5px;font-size:14px;text-align:center;line-height:20px" >${attrOptNames}</label>`;

                    }


                    var label = '';
                    if (order_sub_id) {
                        label =
                            `<label style ="margin-left:4px;background:#e6214a;color:white;border-radius:5px;width:16px;font-size:10px;text-align:center;line-height:17px" >定 </label>`;
                        label += `<label style="color:#88f;cursor:pointer;">${order_item_sheets_no} </label>`
                    }

                    if (disp_flow_id)
                        label =
                            '<label style ="margin-left:4px;background:#e6214a;color:white;border-radius:5px;width:30px;font-size:10px;text-align:center;line-height:17px" >陈列</label>';
                    //if (trade_type === 'J')
                    //    label =
                    //        '<label style ="margin-left:4px;background:#e6214a;color:white;border-radius:5px;width:16px;font-size:10px;text-align:center;line-height:17px" >借</label>';
                    //if (trade_type === 'H')
                    //    label =
                    //        '<label style ="margin-left:4px;background:#e6214a;color:white;border-radius:5px;width:16px;font-size:10px;text-align:center;line-height:17px" >还</label>';
                    if (order_sub_id || disp_flow_id || trade_type || attrOptNames) {
                        var div =
                            `<div style = "height:100%;display:flex;align-items:center;"><label style="text-align:left;margin-left:4px">${value}</label>${lblAttrOpt}${label}</div>`
                        return div;

                    }
                },
            },
            {
                text: '商品编号', datafield: 'item_no', width: '70', align: 'center', cellsalign: 'center', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '仓库',
                hidden: true,
                datafield: 'branch_id',
                displayfield: 'branch_name',
                width: 150, align: 'center',
                columntype: 'template',
                createeditor: createeditor_branch_name,
                initeditor: initeditor_branch_name,
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val();
                    return v;
                },
                cellbeginedit: function (row, datafield, columntype, value) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    if (!item_id) return false
                },
            },
            {
                text: '库位',
                sortable: true,
                hidden: true,
                datafield: 'branch_position',
                displayfield: 'branch_position_name',
                width: 150, align: 'center',
                columntype: 'template',
                createeditor: createeditor_branch_position_name,
                initeditor: initeditor_branch_position_name,
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val();
                    return v;
                },
                cellbeginedit: function (row, datafield, columntype, value) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var row_branch_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'branch_id');
                    var branch_id = $('#branch_id').jqxInput('val').value
                    if ((!row_branch_id && !branch_id) || !item_id) return false;
                },
            },
            {
                text: '规格',
                datafield: 'item_spec',
                width: '60',
                align: 'center',
                cellsalign: 'center',
                hidden: true
            },
            {
                text: '品牌',
                datafield: 'brand_name',
                width: '60',
                align: 'center',
                cellsalign: 'center',
                hidden: true
            },
            {
                text: '保质期',
                datafield: 'valid_days',
                width: '80',
                align: 'center',
                cellsalign: 'right',
                hidden: true
            },
            {
                text: '单位',
                datafield: 'unit_no',
                width: '80',
                align: 'center',
                cellsalign: 'center',
                columntype: 'template',
                alwaysShow: false,
                cellbeginedit: function (row, datafield, columntype, value) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var disp_flow_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'disp_flow_id');
                    if ((datafield === "unit_no" && !item_id) || disp_flow_id) return false;
                },
                createeditor: createeditor_unit_no,
                initeditor: initeditor_unit_no,
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val();
                    return v;
                }

            },
            {
                text: '包装率', datafield: 'unit_factor', width: '80', align: 'center', cellsalign: 'center', alwaysShow: false,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '单位关系', datafield: 'unit_relation1', width: '100', align: 'center', cellsalign: 'center', alwaysShow: false, hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '条码',
                datafield: 'barcode',
                width: '150',
                align: 'center',
                cellsalign: 'right',
                cellbeginedit: function (row, datafield, columntype, value) {
                    return true;
                }
            },
            {
                text: '条码(小)', datafield: 's_barcode', width: '150', align: 'center', cellsalign: 'center', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return true;
                }
            },
            {
                text: '虚拟产期',
                sortable: false,
                datafield: 'virtual_produce_date',
                width: '100px',
                align: 'center',
                cellsalign: 'center',
                cellbeginedit: function (row, datafield, columntype, value, c, d, e) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    if (datafield === "virtual_produce_date" && !item_id) return false;
                }
            },
            {
                text: '生产日期', datafield: 'batch_id', displayfield: "produce_date", width: '150', align: 'center', cellsalign: 'center', columntype: 'template', hidden: true,
                createeditor: createeditor_produce_date,
                initeditor: initeditor_produce_date,
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val()
                    return v;
                },
                cellbeginedit: function (row, datafield, columntype, value) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var rowsData = $("#jqxgrid").jqxGrid('getrows')
                    var rowData = rowsData[row]
                    if ((datafield === "batch_id" && !item_id) || rowData.batch_level == "" || rowData.batch_level == "0") return false;
                },
            },
            {
                text: '批次', datafield: 'batch_id_f', displayfield: 'batch_no', width: '60', align: 'center', cellsalign: 'center', columntype: 'template', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var rowsData = $("#jqxgrid").jqxGrid('getrows')
                    var rowData = rowsData[row]
                    if ((datafield === "batch_id_f" && !item_id) || rowData.batch_level !== "2") return false;
                },
                createeditor: createeditor_batch_no,
                initeditor: initeditor_batch_no,
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val(); return v;
                }

            },
            {
                text: '交易类型', datafield: 'trade_type', displayfield: 'trade_type_name', width: '100', align: 'center', hidden: true, hideOnLoad:true, columntype: 'template',
                cellbeginedit: function (row, datafield, columntype, value) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var disp_flow_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'disp_flow_id');
                    var order_sub_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'order_sub_id');
                    if ((datafield === "trade_type" && !item_id) || disp_flow_id || order_sub_id) return false;
                },
                createeditor: createeditor_trade_type_isborrow,
                initeditor: initeditor_trade_type,
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val();
                    return v;
                }
            },

            {
                text: '数量',
                datafield: 'quantity',
                width: '80',
                alwaysShow: false,
                cellsrenderer: cellsrenderer_quantity,
                align: 'center',
                cellsalign: 'right',
                aggregates: aggregates_quantity,
                aggregatesrenderer: aggregatesrenderer_quantity,
                geteditorvalue: function (row, cellvalue, editor) {
                    var qty = editor.val();
                    var regex = /^[0-9+\-*/.()]+$/;
                    var sheetType = $('#sheetType').val()
                    var trade_type = "J"
                    var trade_type_name = "借货"

                    // 增加检查输入是否小于0
                    if (parseFloat(qty) < 0) {
                        bw.toast('数量不能小于0', 1000);
                        return cellvalue;
                    }
                    if (!regex.test(qty))
                        return qty;
                    let result = eval(qty);
                    var borrowMode = $('#borrow_mode').val();
                    if (borrowMode && borrowMode.value === 'QTY') {
                       $('#jqxgrid').jqxGrid('setcellvalue', row, "real_price", '0');
                    }
                    $('#jqxgrid').jqxGrid('setcellvalue', row, "remark", '');
                    
                    if (sheetType === 'HH') {
                        trade_type = "H"
                        trade_type_name = "还货"
                    }
                    $('#jqxgrid').jqxGrid('setcellvalue', row, "trade_type", trade_type);
                    $('#jqxgrid').jqxGrid('setcellvalue', row, "trade_type_name", trade_type_name);
                    return result
                },
                cellbeginedit: function (row, datafield, columntype, value) {
                    var gridRow = $('#jqxgrid').jqxGrid('getrows')[row]
                    var item_id = gridRow.item_id
                    if (!item_id) return false
                    if (gridRow.mum_attributes) {
                        if (typeof gridRow.mum_attributes === "string") {
                            gridRow.mum_attributes = JSON.parse(gridRow.mum_attributes)
                        }
                        if (gridRow.mum_attributes.find(attr => attr.distinctStock))
                            return false
                        if (gridRow.mum_attributes.find(attr => !attr.distinctStock)) {
                            if (gridRow.attr_qty && gridRow.attr_qty.length > 0) {
                                return false
                            }
                        }
                    }

                }
            },
            {
                text: '辅助数量',
                datafield: 'quantity_unit_conv',
                width: '100',
                hidden: true,
                cellsrenderer: cellsrenderer_multi_qty,
                columntype: 'template',
                align: 'center',
                cellsalign: 'right',
                //aggregates: aggregates_quantity,
                //aggregatesrenderer: aggregatesrenderer_quantity,
                createeditor: createeditor_multi_qty,
                initeditor: initeditor_multi_qty,
                geteditorvalue: geteditorvalue_multi_qty,
                cellbeginedit: function (row, datafield, columntype, value) {
                    var gridRow = $('#jqxgrid').jqxGrid('getrows')[row]
                    var item_id = gridRow.item_id
                    if (!item_id) return false
                    if (gridRow.mum_attributes) {
                        if (gridRow.mum_attributes.find(attr => attr.distinctStock))
                            return false
                        if (gridRow.mum_attributes.find(attr => !attr.distinctStock)) {
                            if (gridRow.attr_qty && gridRow.attr_qty.length > 0) {
                                return false
                            }
                        }
                    }

                }
            },

            {
                text: '原价',
                datafield: 'orig_price',
                width: '70',
                align: 'center',
                hidden: !canSeeSalePrice,
                hideOnLoad: !canSeeSalePrice,
                cellsalign: 'right',
                cellbeginedit: function (row, datafield, columntype, value) {
                     return false;
                    //else if (order_sub_id) return false;
                }
            },
            {
                text: '原价金额', datafield: 'orig_amount', width: '100', align: 'center', cellsalign: 'right', hidden: true,
                hideOnLoad: !canSeeSalePrice,
                aggregates: aggregates_sub_amount,
                aggregatesrenderer: aggregatesrenderer_sub_amount
            },

            {
                text: '系统售价',
                datafield: 'sys_price',
                width: '100',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeSalePrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '上次售价',
                datafield: 'last_time_price',
                width: '100',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeSalePrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '折扣(%)',
                datafield: 'discount',
                width: '100',
                hidden: true,
                hideOnLoad: true,
                align: 'cnter',
                cellsalign: 'center',
                cellbeginedit: function (row, datafield, columntype, value) {

                    return false;
                }
            },
            {
                text: '价格',
                datafield: 'real_price',
                width: '90',
                align: 'center',
                cellsalign: 'right',
                columntype: sheetShowPriceList ? 'template' : undefined,
                createeditor: sheetShowPriceList ? createeditor_real_price : undefined,
                initeditor: sheetShowPriceList ? initeditor_real_price : undefined,
                geteditorvalue: sheetShowPriceList ? function (row, cellvalue, editor) {
                    var v = editor.find('input').val();
                    if (v.label && !v.value) {
                        v.value = v.label.toString()
                    }

                    return v;
                } : undefined,
                alwaysShow: canSeeSalePrice,
                hidden: !canSeeSalePrice,
                hideOnLoad: !canSeeSalePrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    // 按金额借货模式时允许编辑价格
                    var borrowMode = $('#borrow_mode').val();
                    var tradeType = $('#jqxgrid').jqxGrid('getcellvalue', row, 'trade_type');

                    if (borrowMode && borrowMode.value === 'AMT') {
                        return true; // 按金额借货时允许编辑价格
                    }
                    return false;
                },

                cellsrenderer: function (row, column, value, p4, p5, rowData) {
                    var sys_price = rowData.sys_price;
                    var real_price = rowData.real_price;
                    if (rowData.item_id && sys_price) {
                        if (toMoney(sys_price) != toMoney(real_price || 0)) {
                            var div =
                                `<div onmouseenter='onMouseEnterRealPrice(event,${row},${sys_price
                                })' onmousedown='onMouseLeaveRealPrice()' onmouseleave='onMouseLeaveRealPrice()'  style = "height:100%;margin-right:6px;color:#f00;text-align:right;line-height:37px;">${value}</div>`
                            return div;
                        }
                    }

                },

            },
            {
                text: '小单位价',
                datafield: 's_real_price',
                width: '100',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeSalePrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    if (!item_id) return false;

                },

            },
            {
                text: '金额',
                datafield: 'sub_amount',
                width: '90',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: true,
                alwaysShow: canSeeSalePrice,
                hidden: !canSeeSalePrice,
                hideOnLoad: !canSeeSalePrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    let order_sub_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'order_sub_id');
                    let disp_flow_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'disp_flow_id');
                    let trade_type = $('#jqxgrid').jqxGrid('getcellvalue', row, 'trade_type');
                    let item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    if (!item_id) return false;
                    if (order_sub_id || disp_flow_id) return false;

                    // 按金额借货模式时允许编辑金额
                    var borrowMode = $('#borrow_mode').val();
                    if (borrowMode && borrowMode.value === 'AMT' && trade_type === 'J') {
                        return true; // 按金额借货时允许编辑金额
                    }

                    if (trade_type === 'J' || trade_type === 'H') return false;
                    return true;
                },
                aggregates: aggregates_sub_amount,
                aggregatesrenderer: aggregatesrenderer_sale_sub_amount
            },

            {
                // to-do: show a feather svg like multi-flavour item's quantity
                text: '序列号', datafield: 'sn_code', width: '70', sortable: false, align: 'center', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return true; // 先禁止编辑
                },
            },
            {
                text: '备注', columntype: 'template', datafield: 'remark', width: '70', sortable: false, align: 'center',
                createeditor: function (row, cellvalue, editor, cellText, width, height) {
                    var element = $('<div></div >');
                    editor.append(element);
                    var inputElement = editor.find('div')[0];
                    var datafields = new Array({ datafield: 'remark', text: '', width: 120 });
                    $(inputElement).jqxInput({
                        height: height, width: width,
                        borderShape: 'none',
                        buttonUsage: 'list',
                        dropDownHeight: 160,
                        keepNoValueLabel: true,
                        displayMember: 'remark',
                        valueMember: 'remark',
                        datafields: datafields,
                        searchFields: ['remark'],
                        maxRecords: 9,
                        url: '/api/BorrowItemSheet/GetSheetRemarks?sheetType=X&operKey=' + window.g_operKey,
                        // source: []
                    });
                }, align: 'center',
                cellbeginedit: function (row, datafield, columntype, value) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    if (!item_id) return false;
                },
                initeditor: function (row, cellvalue, editor, celltext, pressedkey) {
                    var inputField = editor.find('input');
                    if (pressedkey) {
                        inputField.val(pressedkey);
                        inputField.jqxInput('selectLast');
                    }
                    else {
                        inputField.val({ value: cellvalue, label: celltext });
                        if (cellvalue == '') inputField.val('');
                        inputField.jqxInput('selectAll');
                    }
                },
                geteditorvalue: function (row, cellvalue, editor) {
                    var e = editor.find('input')
                    if (e.length > 0) {
                        var v = e[0]
                        return v.value
                    }
                    return '';
                }
            },
            {
                text: '批发价',
                datafield: 'wholesale_price',
                hidden: true,
                width: '100',
                align: 'center',
                cellsalign: 'right',
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '零售价',
                datafield: 'retail_price',
                width: '100',
                align: 'center',
                cellsalign: 'right',
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                },

            },
            {
                text: '最近零售价',
                datafield: 'recent_retail_price',
                width: '10%',
                align: 'center',
                cellsalign: 'right',
                columntype: 'template',
                createeditor: create_recent_retail_price_editor,
                initeditor: init_editor,
                geteditorvalue: function (row, cellvalue, editor) {

                    var v = editor.find('input').val();
                    if (v.label && !v.value) {
                        v.value = v.label.toString()
                    }
                    //if (v.label) v = v.label;
                    return v;
                },
                cellbeginedit: function (row, datafield, columntype, value) {

                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    if (!item_id) return false;
                    // return false;
                },

            },
            {
                text: '小单位零售价',
                datafield: 's_retail_price',
                width: '120',
                align: 'center',
                cellsalign: 'right',
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '成本价',
                datafield: 'cost_price',
                width: '100',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeBuyPrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '预设进价',
                datafield: 'cost_price_buy',
                width: '100',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeBuyPrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                },
                cellsrenderer: function (row, columnfield, value, defaulthtml, columnproperties, rowData) {
                    var unit_factor = rowData.unit_factor
                    var cost_price_buy = rowData.cost_price_buy
                    if (cost_price_buy && unit_factor) {
                        var html = '<div class="jqx-grid-cell-right-align" style="margin-top: 10px;">' + (cost_price_buy * unit_factor) + '</div>';
                        return html;
                    }
                }
            },
            {
                text: '加权平均价',
                datafield: 'cost_price_avg',
                width: '120',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeBuyPrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                },
                cellsrenderer: function (row, columnfield, value, defaulthtml, columnproperties, rowData) {
                    var unit_factor = rowData.unit_factor
                    var cost_price_avg = rowData.cost_price_avg
                    if (cost_price_avg && unit_factor) {
                        var html = '<div class="jqx-grid-cell-right-align" style="margin-top: 10px;">' + (cost_price_avg * unit_factor) + '</div>';
                        return html;
                    }
                }
            },
            {
                text: '最近平均进价',
                datafield: 'cost_price_recent',
                width: '120',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeBuyPrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                },
                cellsrenderer: function (row, columnfield, value, defaulthtml, columnproperties, rowData) {
                    var unit_factor = rowData.unit_factor
                    var cost_price_recent = rowData.cost_price_recent
                    if (cost_price_recent && unit_factor) {
                        var html = '<div class="jqx-grid-cell-right-align" style="margin-top: 10px;">' + (cost_price_recent * unit_factor) + '</div>';
                        return html;
                    }
                }
            },
            {
                text: '预设成本',
                datafield: 'cost_price_prop',
                width: '100',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeBuyPrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
                ,
                cellsrenderer: function (row, columnfield, value, defaulthtml, columnproperties, rowData) {
                    var unit_factor = rowData.unit_factor
                    var cost_price_prop = rowData.cost_price_prop
                    if (cost_price_prop && unit_factor) {
                        var html = '<div class="jqx-grid-cell-right-align" style="margin-top: 10px;">' + (cost_price_prop * unit_factor) + '</div>';
                        return html;
                    }
                }
            },

            {
                text: '成本金额',
                datafield: 'cost_amount',
                width: '100',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeBuyPrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                },
                aggregates: aggregates_sub_amount,
                aggregatesrenderer: aggregatesrenderer_sub_amount
            },
            {
                text: '利润',
                datafield: 'profit',
                width: '60',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeBuyPrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                },
                aggregates: aggregates_sub_amount,
                aggregatesrenderer: aggregatesrenderer_sub_amount
            },
            {
                text: '利润率(%)',
                datafield: 'profit_rate',
                width: '100',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeBuyPrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '实际库存',
                datafield: 'stock_qty_unit',
                width: '100',
                align: 'center',
                cellsalign: 'center',
                cellbeginedit: function (row, datafield, columntype, value) {

                    return false;
                }
            },
            {
                text: '占用库存',
                datafield: 'sell_pend_qty_unit',
                width: '100',
                align: 'center',
                cellsalign: 'center', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {

                    return false;
                }
            },
            {
                text: '可用库存',
                datafield: 'usable_stock_qty_unit',
                width: '100',
                align: 'center',
                cellsalign: 'center',
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '可还数量',
                datafield: 'specific_qty_unit',
                width: '100',
                align: 'center',
                cellsalign: 'center',
                hidden: true,
                hideOnLoad: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    if (datafield === "specific_qty_unit") return false;

                }
            },
            {
                text: '可用还货数',
                datafield: 'order_qty',
                width: '100',
                align: 'center',
                cellsalign: 'center',
                hidden: true,
                hideOnLoad: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '定货款账户',
                datafield: 'order_sub_name',
                align: 'center',
                cellsalign: 'center',
                hidden: true,
                hideOnLoad: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: 'order_item_sheets_id',
                datafield: 'order_item_sheets_id',
                align: 'center',
                cellsalign: 'center',
                hidden: true,
                hideOnLoad: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: 'order_item_sheets_no',
                datafield: 'order_item_sheets_no',
                align: 'center',
                cellsalign: 'center',
                hidden: true,
                hideOnLoad: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            { text: '单位重量', datafield: 'unit_weight', hidden: true },
            { text: '重量', datafield: 'weight', hidden: true },

            { text: '陈列flow_id', datafield: 'disp_flow_id', hidden: true, hideOnLoad: true },
            { text: '定货款账户', datafield: 'order_sub_id', hidden: true, hideOnLoad: true },
            { text: '定货flow_id', datafield: 'order_flow_id', hidden: true, hideOnLoad: true },
            { text: '定货价(大)', datafield: 'b_order_price', hidden: true, hideOnLoad: true },
            { text: '陈列sheet_id', datafield: 'disp_sheet_id', hidden: true, hideOnLoad: true },
            { text: '陈列月', datafield: 'disp_month_id', hidden: true, hideOnLoad: true },
            { text: '陈列月份', datafield: 'disp_month', hidden: true, hideOnLoad: true },
            { text: '陈列剩余', datafield: 'disp_left_qty', hidden: true, hideOnLoad: true }

            // 新增：借货单选择相关列
            //{
            //    text: '对应借货单', datafield: 'target_borrow_sheet_id', displayfield: 'target_borrow_sheet_no',
            //    width: '150', align: 'center', columntype: 'custom',
            //    hidden: true, // 默认隐藏，通过代码控制显示
            //    editable: true,
            //    createeditor: createeditor_borrow_sheet,
            //    initeditor: initeditor_borrow_sheet,
            //    geteditorvalue: geteditorvalue_borrow_sheet,
            //    cellbeginedit: function (row, datafield, columntype, value) {
            //        var trade_type = $('#jqxgrid').jqxGrid('getcellvalue', row, 'trade_type');
            //        console.log('cellbeginedit - row:', row, 'trade_type:', trade_type);
            //        return trade_type === 'H'; // 只有还货时才能编辑
            //    }
            //},
            //{
            //    text: '可还金额', datafield: 'available_amount', width: '120', align: 'center',
            //    hidden: function() {
            //        return $('#sheet_type').val() !== 'SHEET_RETURN_ITEM';
            //    },
            //    cellsrenderer: function (row, columnfield, value, defaulthtml, columnproperties) {
            //        return '<span style="color: #5cb85c;">￥' + (parseFloat(value || 0).toFixed(2)) + '</span>';
            //    }
            //}
        ]
    }

    adjustColumnsBySetting()

    $("#jqxgrid").jqxGrid(
        GridData
    );
     let borrowMode = $('#borrow_mode').val();
    if (borrowMode && borrowMode.value === 'QTY') {
        $('#div_target_borrow_sheet_id').attr('hidden', true);  
    } else {
        $('#div_target_borrow_sheet_id').attr('hidden', false);  
    }
    //$('#available_amount').attr('disable', true);  
    $('#available_amount').jqxInput({ disabled: true })
    window.destroyWindow = function () {

        $('#jqxgrid').jqxGrid('clear');
        $('#jqxgrid').jqxGrid('destroy');
        window.GridData = null
    }
    renderCopySheet()

    window.loadSheetData = function (sheetRows) {
        //setFormDataItems(sheet)
        var supcustID = $('#supcust_id').val().value;
        var approve_time = $('#approve_time').text();
        if (supcustID) getClientAccountInfo(supcustID)

        //使用定货会商品时，更新支付方式
        sheetRows.forEach((row) => {
            if (row.order_sub_id) {
                var payway1_id = $('#payway1_id').jqxInput('val')
                var payway1_name = payway1_id.label
                if (row.order_sub_name = payway1_name) $('#jqxgrid').jqxGrid('selectcell', row.index, 'order_sub_name');
            }

        })
        initProduceDate(sheetRows)
        loadSheetRows(sheetRows)

        mmRefreshStockQty()

        // ReturnItemsRender()
    }

    loadSheetData(sheetRows)

    enableHideCostColumn()

    // 初始化借货模式相关的界面
    initBorrowModeInterface();

    $("#jqxgrid").on('cellendedit', cellendedit);

    // 保存原有的cellendedit函数
    if (typeof window.cellendedit === 'function') {
        window.originalCellendedit = window.cellendedit;
    }

    // 自定义的cellendedit函数，处理按金额借货模式的价格和金额编辑
    window.cellendedit = function(event) {
        var args = event.args;
        var colName = args.datafield;
        var rowIndex = args.rowindex;
        var cellValue = args.value;
        var oldValue = args.oldvalue;

        if (oldValue === cellValue) return;

        if (cellValue && cellValue.value !== undefined) cellValue = cellValue.value;

        // 处理借货模式的特殊逻辑
        var borrowMode = $('#borrow_mode').val();
        var tradeType = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'trade_type');

        if (tradeType === 'J' || tradeType === 'H') { // 处理借货和还货行
            if (borrowMode && borrowMode.value === 'AMT') {
                // 按金额模式：允许价格和金额互相计算
                if (colName === 'sub_amount') {
                    // 金额变化时反算价格
                    var quantity = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'quantity');
                    if (quantity && quantity != 0) { // 修复：检查不为0而不是大于0
                        var newPrice = Math.abs(parseFloat(cellValue) / parseFloat(quantity)); // 单价取绝对值
                        $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, "real_price", newPrice.toFixed(2));
                    }
                } else if (colName === 'real_price') {
                    // 价格变化时重新计算金额
                    var quantity = $('#jqxgrid').jqxGrid('getcellvalue', rowIndex, 'quantity');
                    if (quantity && quantity != 0) { // 修复：检查不为0而不是大于0
                        // 金额 = 数量 * 单价，保持数量的符号
                        var newAmount = parseFloat(cellValue) * parseFloat(quantity);
                        $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, "sub_amount", newAmount.toFixed(2));
                    }
                }
            } else if (borrowMode && borrowMode.value === 'QTY') {
                // 按数量模式：强制价格和金额为0
                if (colName === 'real_price' || colName === 'sub_amount') {
                    $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, "real_price", '0');
                    $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, "sub_amount", '0');
                }
            }
        }

        // 调用原有的cellendedit逻辑
        if (window.originalCellendedit && typeof window.originalCellendedit === 'function') {
            window.originalCellendedit(event);
        }
    };

    $("#btnSave").on('click', function () {
        // updateTheCostAmount();
        var res = GetSheetData();
        if (res.result === "Error") {
            bw.toast(res.msg, 5000); return;
        }
        var sheet = res.sheet;


        $.ajax({
            url: '/api/BorrowItemSheet/Save',
            type: 'POST',
            contentType: 'application/json',
            // data: JSON.stringify({sheet_id:'1',supcust_id:'2'}),
            data: JSON.stringify(sheet),
            success: function (data) {
                if (data.result === 'OK') {
                    $('#sheet_id').val(data.sheet_id);
                    $('#sheet_no').text(data.sheet_no);
                    if ($('#maker_name').text() == '') $('#maker_name').text(window.g_operName);

                    $('#make_time').text(data.make_time);

                    updateSheetState()
                    removeSheetFromCach()
                    bw.toast('保存成功', 3000);
                    var canDelete = window.getRightValue('sale.sheetSale.delete')
                    if (canDelete && canDelete.toLowerCase() == "false") {
                        $('#btnDelete').css('display', 'none');
                    }
                    if (window.g_companySetting) {
                        if (window.g_companySetting.newSheetAfterSave == 'True') {
                            btnCopySheetHead_click()
                        }
                    }
                }
                else {
                    bw.toast(data.msg, 3000);
                }
            },
            error: function (xhr) {

                console.log("返回响应信息：" + xhr.responseText);
            }
        })
    })


    $("#btnAdd").on('click', function () {

        var sheetType = $('#sheet_type').val();
        if (sheetType == 'SHEET_BORROW_ITEM') window.parent.newTabPage('借货单', `Sheets/BorrowItemSheet?forReturn=false`);
        if (sheetType == 'SHEET_RETURN_ITEM') window.parent.newTabPage('还货单', `Sheets/BorrowItemSheet?forReturn=true`);

        if (!window.firstCachTime) {
            setTimeout(() => {
                window.parent.closeTab(window);
            }, 50)

        }
    })

    function approveSheet(bReview) {
        var res = GetSheetData();
        console.log(res)
        if (res.result === "Error") {
            bw.toast(res.msg, 5000); return;
        }
        var sheet = res.sheet;
        // 这里做一些数据修改，借还货不涉及支付金额
        sheet.payway1_amount = "0"
        sheet.now_pay_amount = 0
        sheet.paid_amount = 0
        if (sheet.sheet_type === "SHEET_BORROW_ITEM") {
            sheet.SheetRows.forEach(row => {
                row.quantity *= -1
            })
        }
        
        var alertMsg = ''
        var alertCondi = "审核"
        if (bReview) alertCondi = "复核"
        function approve() {
            jConfirm('确定要' + alertCondi + '吗?<br>' + alertMsg, function () {

                if (res.result === "Error") {
                    bw.toast(res.msg, 5000); return;
                }

                $("#btnApprove").attr('disabled', true)
                var previousPage = null
                if (window.srcWindow) previousPage = window.srcWindow.app

                var sheets = ""
                var checkAccount_sellerID = ""
                if (previousPage) {
                    sheets = previousPage.sheets;

                    if (sheets) {
                        checkAccount_sellerID = sheets.sellerID;
                        sheets = getPreviousSheetsList(sheets);
                    }
                }

                var tempi = null
                if (window.g_temp_index) tempi = window.g_temp_index
                if (bReview) sheet.bReview = true
                $.ajax({
                    url: '/api/BorrowItemSheet/SaveAndApprove',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(sheet),
                    success: function (data) {
                        if (data.result === 'OK') {
                            $('#sheet_id').val(data.sheet_id);
                            $('#sheet_no').text(data.sheet_no);
                            $('#approve_time').text(data.approve_time);
                            $('#happen_time').jqxDateTimeInput('val', data.happen_time);
                            updateSheetState()
                            removeSheetFromCach()
                            if (bReview) bw.toast('复核成功', 3000);
                            else bw.toast('审核成功', 3000);
                            if (srcWindow && srcWindow.updateGridRow)
                                srcWindow.updateGridRow(data.sheet_id, { sheet_status: '已审', approve_time: data.approve_time })
                            //srcWindow.refreshPrintCount(data.sheet_id, { sheet_status: '已审', approve_time: data.approve_time })
                            // window.parent.refreshSheetPrintCount1(window.srcWindow)

                            var sellerID = $('#seller_id').val()
                            var order_sheet_id = $('#order_sheet_id').val()
                            var senders_id = $('#senders_id').val()
                            var flag = false
                            var senderID = senders_id
                            if (senders_id.length > 1) senderID = senders_id[0]

                            var newRes = GetSheetData();
                            if (newRes.result === "Error") {
                                bw.toast(res.msg, 5000); return;
                            }
                            var newSheetInfo = newRes.sheet
                            if (order_sheet_id) {
                                if (senderID.value == checkAccount_sellerID) {
                                    flag = true
                                }
                            } else {
                                if (sellerID.value == checkAccount_sellerID) {
                                    flag = true
                                }
                            }

                            if (flag) {
                                newSheetInfo.isChecked = true
                                newSheetInfo.isFromWeb = false
                                newSheetInfo.isSum = false
                                newSheetInfo.is_imported = false
                                newSheetInfo.fixinG_ARREARS = false
                                newSheetInfo.sheetRows = newSheetInfo.SheetRows
                                if (tempi) {
                                    sheets.splice(tempi, 0, newSheetInfo)
                                } else {
                                    sheets.unshift(newSheetInfo)
                                }
                                //sheets.splice(tempi, 0, newSheetInfo)
                                //sheets.push(newSheetInfo)
                                assignSheets(sheets)
                            }
                            if (window.g_companySetting) {
                                if (window.g_companySetting.newSheetAfterApprove == 'True') {
                                    btnCopySheetHead_click()
                                }
                            }


                        }
                        else {
                            $("#btnApprove").attr('disabled', false)

                            bw.toast(data.msg, 3000);
                        }
                    },
                    error: function (xhr) {
                        console.log("返回响应信息：" + xhr.responseText);
                    }
                });
            }, "");
        }
        if (sheet.sheet_id != '') {

            checkHasInventory(sheet, 'BorrowItemSheet').then((data) => {
                let itemNameList = data.inventoryItemNames
                itemNameList.forEach((itemName) => {
                    if (alertMsg == '') {
                        alertMsg = itemName
                    } else {
                        alertMsg += ',' + itemName
                    }
                })
                if (alertMsg != '') {
                    alertMsg = alertMsg + '<br>以上商品单据保存期间发生过盘点，审核可能会造成库存不准'
                }
                approve()
            })
        } else {
            approve()
        }
    }

    $("#btnApprove").on('click', function () {

        approveSheet(false)

    });

    $("#btnReview").on('click', function () {
        var sheet_id = $('#sheet_id').val()
        var approve_time = $('#approve_time').val()
        if (approve_time) {
            $.ajax({
                // url: '/api/SaleOrderSheet/Review?operKey=' + g_operKey,
                url: '/api/BorrowItemSheet/Review',
                type: 'POST',
                contentType: 'application/json',
                // data: JSON.stringify({sheet_id:'1',supcust_id:'2'}),
                data: JSON.stringify({ sheet_id: sheet_id, operKey: g_operKey }),
                success: function (data) {
                    if (data.result === 'OK') {
                        $('#sheet_id').val(data.sheet_id);
                        $('#sheet_no').text(data.sheet_no);
                        $('#review_time').text(data.review_time)
                        updateSheetState();
                        removeSheetFromCach()
                        bw.toast('复核成功', 3000);
                    }
                    else {
                        bw.toast(data.msg, 3000);
                    }
                },
                error: function (xhr) {
                    console.log("返回响应信息：" + xhr.responseText);
                }
            })
        }
        else {
            approveSheet(true)
        }

    });

    $("#btnDelete").on('click', function () {
        /*var res = GetSheetData();
       
        if (res.result === "Error") {
            bw.toast(res.msg, 5000); return;
        }
        var sheet = res.sheet;
        */

        var sheet_id = $('#sheet_id').val()

        jConfirm('确定要删除本单据吗?', function () {
            $.ajax({
                url: '/api/BorrowItemSheet/Delete',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ operKey: g_operKey, sheet_id: sheet_id }),
                success: function (data) {
                    if (data.result === 'OK') {
                        //$("#btnApprove").attr('disabled', true);
                        $("#btnSave").attr('disabled', true)
                        $("#btnApprove").attr('disabled', true)
                        $("#btnDelete").attr('disabled', true)
                        $("#btnPrint").attr('disabled', true)
                        $("#btnSyncAccessTicketSys").attr('disabled', true);
                        $("#btnCopy").attr('disabled', true)
                        $("#btnAdd").attr('disabled', true)
                        removeSheetFromCach()
                        bw.toast('删除成功,即将关闭窗口', 3000);
                        setTimeout(function () {
                            window.parent.closeTab(window);
                        }, 2000);

                    }
                    else {
                        bw.toast(data.msg, 3000);
                    }
                },
                error: function (xhr) {
                    console.log("返回响应信息：" + xhr.responseText);
                }
            });
        }, "");

    });

    $("#btnPrint").on('click', function () {


        //获取表格的类型
        var sheetType = $('#sheet_type').val();
        var sheet_id = $('#sheet_id').val();
        //获取客户的id
        var clientID = $('#supcust_id').jqxInput('val');
        if (clientID) clientID = clientID.value;

        // var sheet = getSheetToPrint()
        // if (!sheet) return
        $.ajax({
            url: '/api/PrintTemplate/GetTemplateToUse',
            type: 'GET',
            contentType: 'application/json',
            data: {
                operKey: g_operKey,
                sheetType: sheetType,
                clientID: clientID
            },
            success: function (printInfo) {
                if (printInfo.result === 'OK') {
                    if (printInfo.templateList.length == 0) {
                        bw.toast("没有可用打印模板", 5000)
                        return
                    }

                    var tmp = printInfo.templateList[0]
                    var sTmp = tmp.template_content
                    tmp = JSON.parse(tmp.template_content)


                    var printTemplate = []


                    if (sTmp.indexOf('"prepay_balance"') >= 0) printTemplate.push({ name: "prepay_balance" })
                    if (sTmp.indexOf('"arrears_balance"') >= 0) printTemplate.push({ name: "arrears_balance" })
                    if (sTmp.indexOf('"order_item_balance"') >= 0) printTemplate.push({ name: "order_item_balance" })
                    if (sTmp.indexOf('"print_count"') >= 0) printTemplate.push({ name: "print_count" })
                    if (sTmp.indexOf('"give_qty_unit"') >= 0) printTemplate.push({ name: "give_qty_unit" })

                    var smallUnitBarcode = document.all.ckPrintSmallBarcode.checked

                    $.ajax({
                        url: '/api/BorrowItemSheet/GetSheetToPrint',
                        type: 'GET',
                        contentType: 'application/json',
                        data: {
                            operKey: g_operKey,
                            sheet_id: sheet_id,
                            smallUnitBarcode: smallUnitBarcode,
                            printTemplate: JSON.stringify(printTemplate)

                        },
                        success: function (data) {
                            if (data.result === 'OK') {
                                var sheet = data.sheet


                                var container = window.parent.CEFPrinter
                                if (!container)
                                    container = window.parent.CefGlue

                                if (!container)
                                    container = window.parent

                                if (!container.printSheetByTemplate) {

                                    if (printInfo.cloudPrinters.length == 0) {
                                        bw.toast("请到客户端打印，或是添加一个云打印机", 5000)
                                        return
                                    }
                                    var ptr = printInfo.cloudPrinters[0]
                                    var device_id = ptr.device_id
                                    var check_code = ptr.check_code
                                    var printer_brand = ptr.printer_brand
                                    var url = '/AppApi/CloudPrint/PrintSheetWithTemplate';
                                    if (window.CoolieServerUri) {
                                        url = window.CoolieServerUri + url;
                                    }

                                    $.ajax({
                                        url: url,
                                        type: 'POST',
                                        contentType: 'application/json',
                                        data: JSON.stringify({
                                            operKey: g_operKey,
                                            device_id: device_id,
                                            check_code: check_code,
                                            printer_brand: printer_brand,
                                            sheet: sheet,
                                            tmp: tmp,
                                            cus_orderid: sheet.sheet_no,
                                            copies: "1"
                                        }),
                                        success: function (res) {
                                            console.log(res)
                                            var msg = res.msg == "" ? "打印请求已发送,请等待" : res.msg
                                            bw.toast(msg)
                                            console.log("Print post sent to " + device_id)
                                        },
                                        error: function (xhr) {
                                            bw.toast('发送打印请求失败')
                                            console.log("返回响应信息：" + xhr.responseText)
                                        }
                                    });
                                    return
                                }

                                window.parent.g_SheetsWindowForPrint = window.srcWindow
                                container.printSheetByTemplate(sheet, tmp, true, printInfo.cloudPrinters, printInfo.variables)

                            }
                            else {
                                bw.toast(data.msg, 3000)
                            }
                        },
                        error: function (xhr) {
                            bw.toast('获取单据信息失败')
                            console.log("返回响应信息：" + xhr.responseText)
                        }
                    })
                }
                else {
                    bw.toast(data.msg, 3000)
                }
            },
            error: function (xhr) {
                bw.toast('网络连接失败')
                console.log("返回响应信息：" + xhr.responseText)
            }
        })
    })

    $('#choosetemplate').on('click', function () {

        var maskBg = document.getElementById('topCoverDiv');
        var dia = document.getElementById('dia');
        maskBg.style.display = (maskBg.style.display == 'none') ? 'block' : 'none';
        dia.style.display = (dia.style.display == 'none') ? 'block' : 'none';


        //获取表格的类型
        var sheetType = $('#sheet_type').val();
        //获取客户的id
        var clientID = $('#supcust_id').jqxInput('val');
        if (clientID) clientID = clientID.value;
        //获取表单的数据
        // var sheet = getSheetToPrint()
        //if (!sheet) return
        $.ajax({
            url: '/api/PrintTemplate/GetTemplateToUse',
            type: 'GET',
            contentType: 'application/json',
            data: {
                operKey: g_operKey,
                sheetType: sheetType,
                clientID: clientID
            },
            success: function (printInfo) {
                if (printInfo.result === 'OK') {
                    if (printInfo.templateList.length == 0) {
                        bw.toast("没有可用打印模板", 5000)
                        return
                    }


                    var templateList = printInfo.templateList;
                    var templateShowInHTML = $('#template-list');
                    templateShowInHTML.empty();
                    for (let i = 0; i < templateList.length; i++) {
                        templateHTML = `<button id="templatePrint" value="${i}" style="margin: 10px 0px 0px 10px;width:auto;">${templateList[i].template_name}</button>`
                        templateShowInHTML.append(templateHTML);
                    }


                    $("[id='templatePrint']").on('click', function () {
                        var index = parseInt(this.value);
                        var tmp = printInfo.templateList[index];
                        var sTmp = tmp.template_content
                        tmp = JSON.parse(tmp.template_content);

                        var printTemplate = []


                        if (sTmp.indexOf('"prepay_balance"') >= 0) printTemplate.push({ name: "prepay_balance" })
                        if (sTmp.indexOf('"arrears_balance"') >= 0) printTemplate.push({ name: "arrears_balance" })
                        if (sTmp.indexOf('"order_item_balance"') >= 0) printTemplate.push({ name: "order_item_balance" })
                        if (sTmp.indexOf('"print_count"') >= 0) printTemplate.push({ name: "print_count" })
                        if (sTmp.indexOf('"give_qty_unit"') >= 0) printTemplate.push({ name: "give_qty_unit" })

                        var smallUnitBarcode = document.all.ckPrintSmallBarcode.checked
                        var sheet_id = $('#sheet_id').val();

                        $.ajax({
                            url: '/api/BorrowItemSheet/GetSheetToPrint',
                            type: 'GET',
                            contentType: 'application/json',
                            data: {
                                operKey: g_operKey,
                                sheet_id: sheet_id,
                                smallUnitBarcode: smallUnitBarcode,
                                printTemplate: JSON.stringify(printTemplate)

                            },
                            success: function (data) {
                                if (data.result === 'OK') {
                                    var sheet = data.sheet

                                    var container = window.parent.CEFPrinter
                                    if (!container)
                                        container = window.parent.CefGlue

                                    if (!container)
                                        container = window.parent

                                    if (!container.printSheetByTemplate) {

                                        if (printInfo.cloudPrinters.length == 0) {
                                            bw.toast("请到客户端打印，或是添加一个云打印机", 5000)
                                            return
                                        }
                                        templateShowInHTML.empty();
                                        templateShowInHTML.append("请选择打印机：<br><br>");
                                        for (let i = 0; i < printInfo.cloudPrinters.length; i++) {
                                            templateHTML = `<button id="cloudPrinter" value="${i}" style="margin: 10px 0px 0px 10px;width:auto;">${printInfo.cloudPrinters[i].printer_name}</button>`
                                            templateShowInHTML.append(templateHTML);
                                        }
                                        $("[id='cloudPrinter']").on('click', function () {
                                            var index = parseInt(this.value);

                                            var ptr = printInfo.cloudPrinters[index]
                                            var device_id = ptr.device_id
                                            var check_code = ptr.check_code
                                            var printer_brand = ptr.printer_brand

                                            $.ajax({
                                                url: '/AppApi/CloudPrint/PrintSheetWithTemplate',
                                                type: 'POST',
                                                contentType: 'application/json',
                                                data: JSON.stringify({
                                                    operKey: g_operKey,
                                                    device_id: device_id,
                                                    check_code: check_code,
                                                    printer_brand: printer_brand,
                                                    sheet: sheet,
                                                    tmp: tmp,
                                                    variables: printInfo.variables,
                                                    cus_orderid: sheet.sheet_no,
                                                    copies: "1"
                                                }),
                                                success: function (res) {
                                                    console.log(res)
                                                    var msg = res.msg == "" ? "打印请求已发送,请等待" : res.msg
                                                    bw.toast(msg)
                                                    console.log("Print post sent to " + device_id)
                                                },
                                                error: function (xhr) {
                                                    bw.toast('发送打印请求失败')
                                                    console.log("返回响应信息：" + xhr.responseText)
                                                }
                                            });
                                        })


                                        return
                                    }

                                    window.parent.g_SheetsWindowForPrint = window.srcWindow
                                    container.printSheetByTemplate(sheet, tmp, true, printInfo.cloudPrinters, printInfo.variables)

                                    var clientVersion = 0
                                    if (window.parent && window.parent.CefGlue) {
                                        clientVersion = window.parent.CefGlue.getClientVersion()
                                    }

                                    if (parseFloat(clientVersion) < 3.32) {
                                        $.ajax({
                                            url: '/api/Printer/PrintMark',
                                            type: 'POST',
                                            contentType: 'application/json',
                                            data: JSON.stringify({
                                                operKey: g_operKey,
                                                sheetType: 'X',
                                                sheetIDs: sheet.sheet_id,
                                                printEach: true,
                                                printSum: false
                                            }),
                                            success: function (data) {
                                                if (data.result === 'OK') {
                                                }
                                                else {

                                                }
                                            },
                                            error: function (xhr) {
                                                // console.log("返回响应信息：" + xhr.responseText)
                                            }
                                        })
                                    }
                                }
                                else {
                                    bw.toast(data.msg, 3000)
                                }
                            },
                            error: function (xhr) {
                                console.log("返回响应信息：" + xhr.responseText)
                            }
                        })

                    });


                    return;

                }
                else {
                    bw.toast(data.msg, 3000);
                }
            },
            error: function (xhr) {
                console.log("返回响应信息：" + xhr.responseText);
            }
        });
    });
    let supcustWhenFocus
    $('#supcust_id>input').on('focus', function () {
        supcustWhenFocus = $(this).val()
    })
    $('#supcust_id').on('optionSelected', function (a, b) {
        debugger
        let oldValue = supcustWhenFocus
        let value = $('#supcust_id').val()
        let sheetType = $('#sheet_type').val();

        // 更新客户借货余额
        updateCustomerBorrowBalance(value.value);

        if (sheetType == 'SHEET_SALE' && oldValue != '' && JSON.stringify(oldValue) != JSON.stringify(value)) {
            $('#cancle').click(function () {
                $('#changeClientAlertBox').hide();
                $('#supcust_id').jqxInput('val', { value: oldValue.value, label: oldValue.label })
                // 恢复原客户的借货余额
                updateCustomerBorrowBalance(oldValue.value);
            })
            $('#confirm').click(function () {
                let refreshFlag = $("input[name='changeClient']:checked").val()
                if (refreshFlag == 'true') {

                    let id = ''
                    if (value) id = value.value
                    getClientAccountInfo(id)
                    const branch_id = $('#branch_id').jqxInput('val').value
                    $('#changeClientAlertBox').hide();
                    refreshRowsBySupChange(id, branch_id)
                } else {
                    $('#changeClientAlertBox').hide();

                }
                $('#confirm').off('click')
            })
            $('#changeClientAlertBox').show()

        } else {
            let id = ''
            if (value) id = value.value
            getClientAccountInfo(id)
            const branch_id = $('#branch_id').jqxInput('val').value
            refreshRowsBySupChange(id, branch_id)
        }
    })
    window.onresize()
    window.onCopySheet = function () {
        // order_source不清除，自动同步的单据复制后不能再生成同步单据
        $('#order_source').val('')
    }
    $('#getOrder').on('click', function () {
        /* let order_sheet_no = $('#order_sheet_no').val()*/
        let sheet_id = $('#order_sheet_id').val()
        let url = `/Sheets/SaleOrderSheet?sheet_id=${sheet_id}`
        window.parent.newTabPage('销售订单', url, window);
    })
}

function initProduceDate(rows) {
    rows.forEach((row) => {
        if (row.item_id && row.batch_level && row.batch_level !== "0" && !row.produce_date) {
            row.produce_date = "无产期"
            row.batch_id = "0"
        }
    })
}
function getOrderById(sheet_id, operKey) {
    var win = this;
    $.get('/appapi/AppSheetSaleOrder/Load?operKey=' + operKey + '&sheetId=' + sheet_id).then(result => {
        if (result.result == 'OK') {
            var sheet = result.sheet
            console.log(sheet)
            loadSheetRows(sheet.sheetRows)
            win.$('#supcust_id').jqxInput('val', { value: sheet.supcust_id, label: sheet.sup_name })
            win.$('#seller_id').jqxInput('val', { value: sheet.seller_id, label: sheet.seller_name })
            win.$('#branch_id').jqxInput('val', { value: sheet.branch_id, label: sheet.branch_name })
            win.$('#inputhappen_time').val(sheet.happen_time)
            win.$('#order_sheet_no').text(sheet.sheet_no)
            win.$('#order_sheet_id').text(sheet.sheet_id)
            win.updateTotalAmount()
            win.updatePayAmount()
            return true
        }
    })
}

//if (document.addEventListener) {
//  document.addEventListener("DOMMouseScroll", onMouseLeaveRealPrice, false);
//}
//window.onmousewheel = document.onmousewheel = onMouseLeaveRealPrice;

window.onmousewheel = function (e) {
    onMouseLeaveRealPrice;
}


function btnItemInfoSyncTest_click() {
    console.log("cync click")
    var sendData = { clientID: "113", planID: "40", operKey: g_operKey }
    $.ajax({
        url: '../api/ImportInfo/ItemInfoSync',
        type: 'POST',
        contentType: 'application/json',
        // data
        data: JSON.stringify(sendData),
        success: function (res) {

        },
        error: function (response, ajaxOptions, thrownError) {

        },

    })
}

function btnExportExcel() {
    // 只能导出表体
    debugger
    let url = '/api/BorrowItemSheet/JqxExportExcel?operKey=' + g_operKey
    let data = $("#jqxgrid").jqxGrid('exportdata', 'json');
    $("#jqxgrid").jqxGrid('exportdata', 'xls', '借货单', true, null, false, url)

    //$("#jqxgrid").jqxGrid('exportdata', 'xls', '收款单')
}

// 新增：更新客户借货余额显示
function updateCustomerBorrowBalance(supcustId) {
    if (!supcustId || supcustId == '0') {
        $('#customer_borrow_balance').jqxInput('val', '0.00');
        return;
    }

    $.ajax({
        url: '/api/BorrowItemSheet/GetCustomerBorrowBalance',
        type: 'GET',
        data: {
            operKey: g_operKey,
            supcustId: supcustId
        },
        success: function (data) {
            if (data.result === 'OK') {
                var balance = parseFloat(data.balance || 0).toFixed(2);
                $('#customer_borrow_balance').jqxInput('val', balance);

                // 如果有借货余额，显示提示信息
                if (parseFloat(balance) > 0) {
                    console.log('客户借货余额：' + balance);
                }
            }
        },
        error: function (xhr) {
            console.log("获取客户借货余额失败：" + xhr.responseText);
        }
    });
}


// 刷新表格列显示
function refreshGridColumns() {
    var sheetType = $('#sheet_type').val();
    var borrowMode = $('#borrow_mode').val();

    if (sheetType === 'SHEET_RETURN_ITEM') {
        // 还货单显示借货单选择列
        //$("#jqxgrid").jqxGrid('showcolumn', 'target_borrow_sheet_id');
        //$("#jqxgrid").jqxGrid('showcolumn', 'available_amount');
    } else {
        // 借货单隐藏这些列
        //$("#jqxgrid").jqxGrid('hidecolumn', 'target_borrow_sheet_id');
        //$("#jqxgrid").jqxGrid('hidecolumn', 'available_amount');
    }
}

// 借货单选择编辑器
function createeditor_borrow_sheet(row, cellvalue, editor, celltext, cellwidth, cellheight) {
    console.log('createeditor_borrow_sheet called - row:', row);

    // 创建加载中的select元素
    var select = $('<select style="width:100%;height:100%;border:none;"></select>');
    select.append('<option value="">正在加载...</option>');
    select.appendTo(editor);

    // 异步获取可还的借货单列表
    getAvailableBorrowSheets(function(availableSheets) {
        console.log('Available sheets:', availableSheets);

        // 清空现有选项
        select.empty();

        var source = [];
        if (availableSheets && availableSheets.length > 0) {
            source = availableSheets.map(function(sheet) {
                return {
                    value: sheet.borrow_sheet_id,
                    label: sheet.borrow_sheet_no + ' (余额:￥' + sheet.balance_amount.toFixed(2) + ')'
                };
            });
        } else {
            source = [{ value: '', label: '暂无可还借货单' }];
        }

        // 添加选项
        source.forEach(function(item) {
            var option = $('<option></option>').attr('value', item.value).text(item.label);
            select.append(option);
        });

        // 设置当前值
        if (cellvalue) {
            select.val(cellvalue);
        }
    });
}

function initeditor_borrow_sheet(row, cellvalue, editor, celltext, pressedkey) {
    console.log('initeditor_borrow_sheet called - row:', row, 'cellvalue:', cellvalue);

    var select = editor.find('select');

    // 设置当前值
    if (cellvalue && cellvalue !== '') {
        select.val(cellvalue);
    }

    // 聚焦
    select.focus();
}

function geteditorvalue_borrow_sheet(row, cellvalue, editor) {
    console.log('geteditorvalue_borrow_sheet called - row:', row);

    var select = editor.find('select');
    var selectedValue = select.val();

    console.log('Selected value:', selectedValue);

    if (selectedValue && selectedValue !== '') {
        // 获取可还借货单列表
        var availableSheets = getAvailableBorrowSheets();
        var selectedSheet = availableSheets.find(function(sheet) {
            return sheet.borrow_sheet_id == selectedValue;
        });

        if (selectedSheet) {
            // 更新显示字段
            $('#jqxgrid').jqxGrid('setcellvalue', row, 'target_borrow_sheet_no', selectedSheet.borrow_sheet_no);
            // 更新可还金额
            $('#jqxgrid').jqxGrid('setcellvalue', row, 'available_amount', selectedSheet.balance_amount);

            console.log('Updated row with sheet:', selectedSheet.borrow_sheet_no, 'amount:', selectedSheet.balance_amount);
        }

        return selectedValue;
    }

    // 空值处理
    $('#jqxgrid').jqxGrid('setcellvalue', row, 'target_borrow_sheet_no', '');
    $('#jqxgrid').jqxGrid('setcellvalue', row, 'available_amount', 0);
    return '';
}

// 刷新表格编辑权限
function refreshGridEditPermissions() {
    // 强制刷新表格，使cellbeginedit函数重新生效
    $("#jqxgrid").jqxGrid('refresh');
}

// 处理借货模式切换时的数据清理
function handleBorrowModeSwitch(borrowMode) {
    console.log('handleBorrowModeSwitch called with:', borrowMode);

    // 兼容不同的值格式
    var modeValue = borrowMode;
    if (borrowMode && borrowMode.value) {
        modeValue = borrowMode.value;
    } else if (typeof borrowMode === 'string') {
        modeValue = borrowMode;
    }

    console.log('Mode value:', modeValue);



    if (modeValue === 'QTY') {
        console.log('切换到按数量模式，开始清零价格和金额');

        // 切换到按数量模式时，清零所有借货行的价格和金额
        var rows = $('#jqxgrid').jqxGrid('getrows');
        console.log('表格总行数:', rows.length);

        for (var i = 0; i < rows.length; i++) {
            var tradeType = $('#jqxgrid').jqxGrid('getcellvalue', i, 'trade_type');
            console.log('第' + i + '行，trade_type:', tradeType);

            if (tradeType === 'J') { // 只处理借货行
                console.log('处理借货行:', i);
                $('#jqxgrid').jqxGrid('setcellvalue', i, 'real_price', 0);
                $('#jqxgrid').jqxGrid('setcellvalue', i, 'sub_amount', 0);
                // 更新备注为借货
                $('#jqxgrid').jqxGrid('setcellvalue', i, 'remark', '借货');
            }
        }

        // 强制刷新表格
        $('#jqxgrid').jqxGrid('refresh');

        // 更新总金额
        if (typeof updateTotalAmount === 'function') {
            updateTotalAmount();
        }
        if (typeof updatePayAmount === 'function') {
            updatePayAmount();
        }

        console.log('按数量模式切换完成');
    }
}

// 初始化借货模式相关的界面
function initBorrowModeInterface() {
    // 刷新表格列显示
    refreshGridColumns();

    // 根据当前借货模式显示提示
    var borrowMode = $('#borrow_mode').val();
    if (borrowMode && borrowMode.value === 'AMT') {
        console.log('当前为按金额借货模式，可编辑价格和金额');
    }
}

// 手动测试函数 - 重置借货行价格和金额
window.testResetBorrowPrices = function() {
    console.log('手动测试重置借货行价格和金额');
    var rows = $('#jqxgrid').jqxGrid('getrows');
    console.log('表格总行数:', rows.length);

    for (var i = 0; i < rows.length; i++) {
        var tradeType = $('#jqxgrid').jqxGrid('getcellvalue', i, 'trade_type');
        var itemId = $('#jqxgrid').jqxGrid('getcellvalue', i, 'item_id');
        console.log('第' + i + '行，item_id:', itemId, 'trade_type:', tradeType);

        if (tradeType === 'J' && itemId) { // 只处理有商品的借货行
            console.log('重置第' + i + '行的价格和金额');
            $('#jqxgrid').jqxGrid('setcellvalue', i, 'real_price', 0);
            $('#jqxgrid').jqxGrid('setcellvalue', i, 'sub_amount', 0);
            $('#jqxgrid').jqxGrid('setcellvalue', i, 'remark', '借货');
        }
    }

    $('#jqxgrid').jqxGrid('refresh');
    console.log('重置完成');
};

// 测试借货模式切换
window.testBorrowModeSwitch = function() {
    console.log('测试借货模式切换到按数量');
    handleBorrowModeSwitch({value: 'QTY'});
};

// 检查当前借货模式
window.checkBorrowMode = function() {
    var borrowMode = $('#borrow_mode').val();
    console.log('当前借货模式:', borrowMode);
    return borrowMode;
};

// 获取可还借货单列表（异步版本）
function getAvailableBorrowSheets(callback) {
    var supcustObj = $('#supcust_id').jqxInput('val');
    var supcustId = supcustObj ? supcustObj.value : '';
    var operKey = window.g_operKey; // 使用全局变量

    if (!supcustId || supcustId === '' || supcustId === '0') {
        console.log('客户ID为空，无法获取可还借货单');
        if (callback) callback([]);
        return;
    }

    // 异步AJAX请求获取可还借货单
    $.ajax({
        url: '/AppApi/AppSheetBorrowItem/GetAvailableBorrowSheets',
        type: 'GET',
        data: {
            operKey: operKey,
            supcustId: supcustId
        },
        success: function(response) {
            if (response.result === 'OK' && response.data) {
                console.log('获取到可还借货单:', response.data.length, '个');
                if (callback) callback(response.data);
            } else {
                console.log('获取可还借货单失败:', response.msg);
                if (callback) callback([]);
            }
        },
        error: function(xhr, status, error) {
            console.error('获取可还借货单时发生错误:', error);
            if (callback) callback([]);
        }
    });
}

// 获取可还借货单列表（同步版本，仅用于兼容性）
function getAvailableBorrowSheetsSync() {
    var supcustObj = $('#supcust_id').jqxInput('val');
    var supcustId = supcustObj ? supcustObj.value : '';
    var operKey = window.g_operKey; // 使用全局变量

    if (!supcustId || supcustId === '' || supcustId === '0') {
        console.log('客户ID为空，无法获取可还借货单');
        return [];
    }

    var availableSheets = [];

    // 同步AJAX请求（不推荐，仅用于兼容性）
    $.ajax({
        url: '/AppApi/AppSheetBorrowItem/GetAvailableBorrowSheets',
        type: 'GET',
        async: false,
        data: {
            operKey: operKey,
            supcustId: supcustId
        },
        success: function(response) {
            if (response.result === 'OK' && response.data) {
                availableSheets = response.data;
                console.log('获取到可还借货单:', availableSheets.length, '个');
            } else {
                console.log('获取可还借货单失败:', response.msg);
            }
        },
        error: function(xhr, status, error) {
            console.error('获取可还借货单时发生错误:', error);
        }
    });

    return availableSheets;
}

// 当客户改变时，清空可还借货单缓存
$(document).ready(function() {
    // 监听客户选择变化
    $('#supcust_id').on('change', function() {
        console.log('客户已改变，清空借货单缓存');
        // 可以在这里添加缓存清理逻辑
    });
    
    // 监听单据类型变化
    $('#sheet_type').on('change', function() {
        var sheetType = $(this).val();
        console.log('单据类型已改变:', sheetType);

        // 刷新表格列显示
        if (typeof $('#jqxgrid').jqxGrid === 'function') {
            $('#jqxgrid').jqxGrid('refresh');
        }
    });
    // 新增：借货模式切换处理
$('#borrow_mode').on('change', function (event, args) {
    var borrowMode = $('#borrow_mode').val();
    console.log('借货模式optionSelected事件触发：', borrowMode);

    // 处理模式切换时的数据清理
    handleBorrowModeSwitch(borrowMode);

    // 刷新表格列显示
    refreshGridColumns();

    

    // 界面提示
    if (borrowMode && borrowMode.value === 'AMT') {
        bw.toast('已切换到按金额借还货模式，可编辑价格和金额，支持借A单还B单场景', 2000);
        $('#div_target_borrow_sheet_id').attr('hidden', false);  

    } else if (borrowMode && borrowMode.value === 'QTY') {
        bw.toast('已切换到按数量借还货模式，价格和金额已重置', 2000);
        $('#div_target_borrow_sheet_id').attr('hidden', true);  
    }
    $("#jqxgrid").jqxGrid('refresh');
    // 刷新表格编辑权限
    //refreshGridEditPermissions();
});
    // 页面头部借货单选择事件处理 - 需要在控件初始化后设置
    setTimeout(function() {
        if ($('#target_borrow_sheet_id').length > 0) {
            $('#target_borrow_sheet_id').jqxInput({
                onButtonClick: function(event) {
                    console.log('页面头部借货单选择按钮点击');
                    const borrow_mode = $("#borrow_mode").val()
                    if(borrow_mode && borrow_mode.value === "QTY") {
                        bw.toast('按数量还货无需指定还货单据', 2000);
                        $('#target_borrow_sheet_id').jqxInput('val', {
                            value: "",
                            label: ""
                        });
                        return
                    }
                    showBorrowSheetSelectionDialog();
                }
            });
        }
    }, 1000); // 延迟确保控件已初始化
});

// 调试函数：检查还货单状态
window.debugReturnSheet = function() {
    console.log('=== 还货单调试信息 ===');
    console.log('sheet_type:', $('#sheet_type').val());
    console.log('sheetType:', $('#sheetType').val());

    var supcustObj = $('#supcust_id').jqxInput('val');
    console.log('客户信息:', supcustObj);

    var operKey = window.g_operKey; // 使用全局变量
    console.log('operKey:', operKey);

    // 检查表格列显示状态
    var targetColumnVisible = $('#jqxgrid').jqxGrid('getcolumn', 'target_borrow_sheet_id').hidden;
    console.log('借货单选择列是否隐藏:', targetColumnVisible);

    // 测试获取可还借货单
    if (supcustObj && supcustObj.value) {
        console.log('尝试获取可还借货单...');
        getAvailableBorrowSheets(function(sheets) {
            console.log('可还借货单数量:', sheets.length);
            console.log('可还借货单列表:', sheets);
        });
    } else {
        console.log('客户未选择，无法获取可还借货单');
    }

    console.log('=== 调试信息结束 ===');
};

// 调试函数：强制刷新列显示
window.forceRefreshColumns = function() {
    console.log('强制刷新表格列显示...');
    refreshGridColumns();
    console.log('刷新完成');
};

// 调试函数：测试借货单选择按钮
window.testBorrowSheetButton = function() {
    console.log('=== 测试借货单选择按钮 ===');

    // 检查控件是否存在
    var element = $('#target_borrow_sheet_id');
    console.log('控件是否存在:', element.length > 0);

    if (element.length > 0) {
        console.log('控件可见性:', element.is(':visible'));
        console.log('控件HTML:', element[0].outerHTML);

        // 手动触发按钮点击
        console.log('手动触发借货单选择对话框...');
        showBorrowSheetSelectionDialog();
    } else {
        console.log('target_borrow_sheet_id 控件不存在');
    }

    console.log('=== 测试结束 ===');
};

// 获取借货单商品明细并带出到表格
function loadBorrowSheetItems(borrowSheetId, borrowSheetNo) {
    console.log('开始获取借货单商品明细:', borrowSheetId);

    bw.toast('正在获取借货单商品明细...', 2000);

    $.ajax({
        url: '/AppApi/AppSheetBorrowItem/GetBorrowSheetItems',
        type: 'GET',
        data: {
            operKey: window.g_operKey,
            borrowSheetId: borrowSheetId
        },
        success: function(response) {
            if (response.result === 'OK' && response.data) {
                console.log('获取到借货单商品明细:', response.data.length, '个商品');

                // 询问用户是否要清空现有商品行
                var existingRows = $('#jqxgrid').jqxGrid('getrows');
                var hasExistingItems = existingRows && existingRows.length > 0 && existingRows.some(row => row.item_name);

                if (hasExistingItems) {
                    if (confirm('表格中已有商品，是否清空现有商品后添加借货单商品？\n\n点击"确定"清空现有商品\n点击"取消"在现有商品基础上追加')) {
                        // 清空现有商品行
                        $('#jqxgrid').jqxGrid('clear');
                        addEmptyRows(25);
                    }                   
                }
                 
                // 添加借货单商品到表格
                addBorrowItemsToGrid(response.data, borrowSheetId, borrowSheetNo);

                //bw.toast(`已添加${response.data.length}个商品到还货单`, 3000);
            } else {
                console.log('获取借货单商品明细失败:', response.msg);
                bw.toast('获取借货单商品明细失败: ' + (response.msg || '未知错误'), 3000);
            }
        },
        error: function(xhr, status, error) {
            console.error('获取借货单商品明细时发生错误:', error);
            bw.toast('获取借货单商品明细时发生错误: ' + error, 3000);
        }
    });
}

// 将借货单商品添加到表格中
function addBorrowItemsToGrid(borrowItems, borrowSheetId, borrowSheetNo) {
    console.log('开始添加商品到表格:', borrowItems.length, '个商品');

    // 找到第一个空白行的索引
    var currentRows = $('#jqxgrid').jqxGrid('getrows') || [];
    var firstEmptyRowIndex = -1;
    for (var i = 0; i < currentRows.length; i++) {
        if (!currentRows[i].item_name || currentRows[i].item_name === '') {
            firstEmptyRowIndex = i;
            break;
        }
    }

    // 如果没有找到空白行，使用表格末尾
    if (firstEmptyRowIndex === -1) {
        firstEmptyRowIndex = currentRows.length;
    }

    // 获取当前客户ID和仓库信息
    var supcust_id = $('#supcust_id').val() ? $('#supcust_id').val().value : 0;
    var branch_id = $('#branch_id').val() ? $('#branch_id').val().value : 0;
    var branch_position = "0"; // 默认库位

    // 为每个借货单商品添加额外的字段
    var processedItems = borrowItems.map(function(item) {
        return {
            ...item,
            trade_type: 'H',
            //target_borrow_sheet_id: borrowSheetId,
            //target_borrow_sheet_no: borrowSheetNo,
            remark: item.remark || '从借货单带出',
            inout_flag: -1
        };
    });

    // 调用现有的AddItemRows函数
    AddItemRows(firstEmptyRowIndex, supcust_id, branch_id, branch_position, processedItems, true);

    console.log('商品添加完成，共添加', borrowItems.length, '个商品');
}

// 调试函数：测试获取借货单商品明细
window.testLoadBorrowSheetItems = function(borrowSheetId) {
    if (!borrowSheetId) {
        console.log('请提供借货单ID，例如：testLoadBorrowSheetItems(123)');
        return;
    }

    console.log('=== 测试获取借货单商品明细 ===');
    console.log('借货单ID:', borrowSheetId);

    loadBorrowSheetItems(borrowSheetId, '测试借货单');

    console.log('=== 测试结束 ===');
};

// 显示借货单选择对话框
function showBorrowSheetSelectionDialog() {
    var supcustObj = $('#supcust_id').jqxInput('val');
    if (!supcustObj || !supcustObj.value || supcustObj.value === '0') {
        bw.toast('请先选择客户', 3000);
        return;
    }

    // 显示加载提示
    bw.toast('正在获取可还借货单...', 1000);

    // 异步获取可还借货单列表
    getAvailableBorrowSheets(function(availableSheets) {
        if (!availableSheets || availableSheets.length === 0) {
            bw.toast('该客户暂无可还的借货单', 3000);
            return;
        }

        // 创建并显示对话框
        createBorrowSheetSelectionDialog(availableSheets, supcustObj);
    });
}

// 创建借货单选择对话框
function createBorrowSheetSelectionDialog(availableSheets, supcustObj) {
    console.log('创建借货单选择对话框，可选借货单数量:', availableSheets.length);

    // 先销毁已存在的对话框
    if ($('#borrowSheetModal').length > 0) {
        console.log('销毁已存在的对话框');
        try {
            $('#borrowSheetModal').jqxWindow('destroy');
        } catch (e) {
            console.log('销毁对话框时出错:', e.message);
        }
        $('#borrowSheetModal').remove();
    }

    // 确保DOM中没有残留的对话框元素
    $('body').find('#borrowSheetModal').remove();

    // 创建选择对话框的HTML
    var dialogHtml = `
        <div id="borrowSheetDialog" style="padding: 20px;">
            <h4>选择要还货的借货单</h4>
            <div style="margin: 15px 0;">
                <label>客户：${supcustObj.label}</label>
            </div>
            <div style="margin: 15px 0;">
                <label for="borrowSheetSelect">借货单：</label>
                <select id="borrowSheetSelect" style="width: 100%; padding: 8px; margin-top: 5px;">
                    <option value="">请选择借货单</option>
                </select>
            </div>
            <div id="selectedSheetInfo" style="margin: 15px 0; padding: 10px; background: #f5f5f5; display: none;">
                <div><strong>借货单号：</strong><span id="selectedSheetNo"></span></div>
                <div><strong>借货时间：</strong><span id="selectedSheetTime"></span></div>
                <div><strong>借货总额：</strong><span id="selectedSheetTotal"></span></div>
                <div><strong>剩余余额：</strong><span id="selectedSheetBalance"></span></div>
            </div>
            <div style="margin-top: 20px; text-align: right;">
                <button id="btnConfirmBorrowSheet" style="margin-right: 10px; padding: 8px 16px;">确定</button>
                <button id="btnCancelBorrowSheet" style="padding: 8px 16px;">取消</button>
            </div>
        </div>
    `;

    // 重新创建模态对话框
    $('body').append('<div id="borrowSheetModal"></div>');
    $('#borrowSheetModal').html(dialogHtml);

    // 填充借货单选项
    var select = $('#borrowSheetSelect');
    availableSheets.forEach(function(sheet) {
        var option = $('<option></option>')
            .attr('value', sheet.borrow_sheet_id)
            .text(sheet.borrow_sheet_no + ' (余额:￥' + sheet.balance_amount.toFixed(2) + ')');
        select.append(option);
    });

    // 绑定选择变化事件（使用off先解绑，避免重复绑定）
    $('#borrowSheetSelect').off('change').on('change', function() {
        var selectedId = $(this).val();
        if (selectedId) {
            var selectedSheet = availableSheets.find(s => s.borrow_sheet_id == selectedId);
            if (selectedSheet) {
                $('#selectedSheetNo').text(selectedSheet.borrow_sheet_no);
                $('#selectedSheetTime').text(new Date(selectedSheet.borrow_time).toLocaleString());
                $('#selectedSheetTotal').text('￥' + selectedSheet.total_amount.toFixed(2));
                $('#selectedSheetBalance').text('￥' + selectedSheet.balance_amount.toFixed(2));
                $('#selectedSheetInfo').show();
            }
        } else {
            $('#selectedSheetInfo').hide();
        }
    });

    // 绑定按钮事件（使用off先解绑，避免重复绑定）
    $('#btnConfirmBorrowSheet').off('click').on('click', function() {
        var selectedId = $('#borrowSheetSelect').val();
        if (!selectedId) {
            bw.toast('请选择借货单', 3000);
            return;
        }

        var selectedSheet = availableSheets.find(s => s.borrow_sheet_id == selectedId);
        if (selectedSheet) {
            // 设置页面头部的借货单字段
            $('#target_borrow_sheet_id').jqxInput('val', {
                value: selectedSheet.borrow_sheet_id,
                label: selectedSheet.borrow_sheet_no
            });
            // 设置底部可还总额
            $('#available_amount').jqxInput('val', selectedSheet.balance_amount);
            // 设置还货单对应的借货单sheet_id
            //$('#target_borrow_sheet_id').val(selectedSheet.borrow_sheet_id);
            //$('#target_borrow_sheet_id').label(selectedSheet.borrow_sheet_no);
            // 获取借货单商品明细并带出到表格
                loadBorrowSheetItems(selectedSheet.borrow_sheet_id, selectedSheet.borrow_sheet_no);
            
            // 关闭对话框
            closeBorrowSheetDialog();
        }
    });

    $('#btnCancelBorrowSheet').off('click').on('click', function() {
        closeBorrowSheetDialog();
    });

    // 初始化并显示对话框
    $('#borrowSheetModal').jqxWindow({
        title: '选择借货单',
        width: 500,
        height: 400,
        isModal: true,
        modalOpacity: 0.3,
        autoOpen: true,
        showCloseButton: true,
        theme: 'summer'
    });

    // 绑定关闭事件（使用正确的事件绑定方式）
    $('#borrowSheetModal').off('closed').on('closed', function() {
        console.log('对话框关闭事件触发');
        // 不在这里销毁，保留DOM元素供下次使用
    });

    console.log('借货单选择对话框创建完成');
}

// 关闭借货单选择对话框
function closeBorrowSheetDialog() {
    console.log('关闭借货单选择对话框');
    if ($('#borrowSheetModal').length > 0) {
        try {
            $('#borrowSheetModal').jqxWindow('close');
        } catch (e) {
            console.log('关闭对话框时出错:', e.message);
        }
    }
}

// 调试函数：测试借货单选择对话框
window.testBorrowSheetDialog = function() {
    console.log('=== 测试借货单选择对话框 ===');

    // 检查客户是否已选择
    var supcustObj = $('#supcust_id').jqxInput('val');
    console.log('客户信息:', supcustObj);

    if (!supcustObj || !supcustObj.value || supcustObj.value === '0') {
        console.log('请先选择客户');
        bw.toast('请先选择客户', 3000);
        return;
    }

    // 手动触发对话框显示
    console.log('手动触发借货单选择对话框...');
    showBorrowSheetSelectionDialog();

    console.log('=== 测试结束 ===');
};

// 调试函数：检查对话框状态
window.checkDialogStatus = function() {
    console.log('=== 检查对话框状态 ===');
    console.log('borrowSheetModal元素数量:', $('#borrowSheetModal').length);
    console.log('borrowSheetModal是否可见:', $('#borrowSheetModal').is(':visible'));

    if ($('#borrowSheetModal').length > 0) {
        try {
            var isOpen = $('#borrowSheetModal').jqxWindow('isOpen');
            console.log('对话框是否打开:', isOpen);
        } catch (e) {
            console.log('检查对话框状态时出错:', e.message);
        }
    }

    console.log('=== 检查结束 ===');
};
