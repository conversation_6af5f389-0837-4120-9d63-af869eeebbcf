﻿@page
@model ArtisanManage.Pages.Report.AttenanceReportViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.addEventListener('message', function (rs) {
            console.log(rs)
            if (rs.data.msgHead === "AttendanceSettingEdit") {
                console.log($("#popItem"))
                $('#popItem').jqxWindow('close');
            }
        })
        $(document).ready(function () {
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)
            QueryData();


        })
        function distanceRender(row, column, value, p4, p5, rowData) {
            if (Number(rowData.check_distance)!=0 && Number(rowData.distance) > Number(rowData.check_distance)) {
                return `<div style="color:#c40000;height:20px;line-height:30px;" >${Number(rowData.distance).toFixed(2)}米</div>`
            }
            return '<div style="height:20px;line-height:30px;">' + (rowData.distance ? `${Number(rowData.distance).toFixed(2)}米` : '') + '</div>';
        }
    </script>
</head>
<script src="~/js/Vue.js"></script>
<script src="~/js/FileSaverVue.js"></script>
<script src="~/js/Blob.js"></script>
<script src="~/js/jszip.js"></script>
<script src="~/js/ods.js"></script>
<script src="~/js/cpexcel.js"></script>
<script src="~/js/xlsx.full.min.js"></script>
<script src="~/js/xlsx-style.js"></script>
<script src="~/js/Export2Excel.js?v=@Html.Raw(Model.Version)"></script>
<script src="~/js/YJExportSheet.js?v=@Html.Raw(Model.Version)"></script>
<script src="~/js/site.js"></script>
<script type="text/javascript" src="../wwwroot/jqwidgets/jqwidgets/jqxtabs.js"></script>
<script type="text/javascript">
    // 导出详细, 获取单据信息代码来自 function  btnExportTable_click()
    function handleExportAttendance() {
        getPrintAttendance((rows) => {
            handleExportDetailExcel(rows)
        })
        /**
           this.pritntSheetsInfo = []
           if (exportType === 'detail') {
               this.getPrintSheetDetail(() => {
                   console.log('pritntSheetsInfo', this.pritntSheetsInfo)
                   this.handleExportDetailExcel()
               })
           }
           */
    }
    function handleExportDetailExcel(attendanceRows) {

        // excel表头
        let header = ["业务员", "签到时间", "签退时间", "地址", "考勤距离"]
        //请求数据的字段
        const filterVal = ['oper_name', 'start_time', 'end_time', 'addr', 'distance']
        // 数据来源
        const list = attendanceRows
        console.log(attendanceRows)
        // 拼接数据
        const data = formatJson(filterVal, attendanceRows)
        const selectStartTime = $($("#start_time>>input"))[0].value
        export_json_to_excel(header, data, selectStartTime + '_' + '考勤报表')

    }
    // 拼接数据
    function formatJson(filterVal, jsonData) {
        return jsonData.map(v => filterVal.map(j => {
            return v[j]
        }))
    }
    // callBack 为ajax 成功获取数据后，进行执行
    function getPrintAttendance(callBack) {
        const selectStartTime = $($("#start_time>>input"))[0].value
        $.ajax({
            url: `/api/AttenanceReportView/GetQueryRecords?gridID=gridItems&startRow=0&endRow=200&GetRowsCount=true&gridSetting=&operKey=@Model.OperKey&start_time=${selectStartTime}&sortColumn=&sortDirection=`,
            success: (res) => {
                callBack(res.rows)
            }
        })
    }
</script>
<body class='default' style="overflow:hidden;">
    <style>
        .jqx-popover {
            border-color: #e2e2e2;
            border-radius: 20px;
            box-shadow: 20px 20px 50px 0px rgba(0, 0, 0, 0.25);
        }
    </style>

 
            <div id="divHead" class="headtail" style="width:calc(100% - 110px);">

                <div style="float:none;height:0px; clear:both;"></div>

            </div>
            <button onclick="QueryData()" style="margin-left:20px;margin-bottom:10px;" class="main-button">查询</button>
    <button onclick="handleExportAttendance()">导出</button>

            <div style="flex-grow:1;display:flex;width:100%;height:100%;">
                <div style="width:calc(100% - 200px);height:100%; margin-left:10px;">
                    <div><div style="float:right;margin-right:50px;height:20px;font-size:12px;color:#999;">共<label id="rows_count">0</label>行</div></div>
                    <div id="gridItems" style="margin-top:0px;margin-left:10px; margin-bottom:2px;width:calc(100% - 20px);height:calc(100% - 20px);"></div>
                </div>
            </div>
            <div id="popItem" style="display:none;">
                <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">客户信息</span></div>
                <div style="overflow:hidden;"> </div>
            </div>
   
</body>
</html>
