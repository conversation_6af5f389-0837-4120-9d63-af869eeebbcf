{
  "profiles": {
    "IIS Express": {
      "commandName": "IISExpress",
      "launchBrowser": true,
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "develop"
      }
    },
    "ArtisanManage": {
      "commandName": "Project",
      "environmentVariables": {
        //"ASPNETCORE_ENVIRONMENT": "Product" 
        //"ASPNETCORE_ENVIRONMENT": "Development"

 
        "ASPNETCORE_ENVIRONMENT": "Slave"
 

        //"ASPNETCORE_ENVIRONMENT": "Slave1"
      },
      "sqlDebugging": false,
      "hotReloadEnabled": false,
      "applicationUrl": "http://0.0.0.0:80"
    },
    "Docker": {
      "commandName": "Docker",
      "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}",
      "environmentVariables": {
        "ASPNETCORE_HTTPS_PORTS": "8081",
        "ASPNETCORE_HTTP_PORTS": "8080"
      },
      "publishAllPorts": true,
      "useSSL": true
    }
  },
  "iisSettings": {
    "windowsAuthentication": false,
    "anonymousAuthentication": true,
    "iisExpress": {
      "applicationUrl": "http://localhost:51750",
      "sslPort": 44305
    }
  }
}
