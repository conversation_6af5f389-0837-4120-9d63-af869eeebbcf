# 按金额借还货功能说明

## 功能概述

本功能扩展了原有的借货系统，支持**按金额借还货**模式，并实现了**借A单还B单**的跨单据借还货场景。

### 核心特性

1. **双模式支持**：
   - 按数量借还货（原有模式）
   - 按金额借还货（新增模式）

2. **按借货单维度管理**：
   - 每个借货单独立管理余额
   - 支持借A单，还B单的灵活场景
   - 精确跟踪每笔借货的还货情况

3. **完整的业务流程**：
   - 借货时创建借货单余额记录
   - 还货时可选择对应的借货单
   - 完整的历史记录和余额查询

## 数据库设计

### 新增表结构

#### 1. 借货单余额表 (borrow_sheet_balance)
```sql
CREATE TABLE borrow_sheet_balance (
    company_id integer NOT NULL,
    borrow_sheet_id integer NOT NULL,        -- 借货单ID
    supcust_id integer NOT NULL,             -- 客户ID
    borrow_mode varchar(10) NOT NULL,        -- 借货模式：QTY=按数量，AMT=按金额
    total_amount numeric(15,2) DEFAULT 0,    -- 借货总金额
    returned_amount numeric(15,2) DEFAULT 0, -- 已还金额
    balance_amount numeric(15,2) DEFAULT 0,  -- 剩余金额
    borrow_time timestamp NOT NULL,         -- 借货时间
    last_return_time timestamp,             -- 最后还货时间
    status varchar(20) DEFAULT 'ACTIVE',    -- 状态：ACTIVE=有效，CLOSED=已结清
    CONSTRAINT pk_borrow_sheet_balance PRIMARY KEY (company_id, borrow_sheet_id)
);
```

#### 2. 还货借货映射表 (return_borrow_mapping)
```sql
CREATE TABLE return_borrow_mapping (
    company_id integer NOT NULL,
    return_sheet_id integer NOT NULL,       -- 还货单ID
    return_detail_id integer NOT NULL,      -- 还货明细ID
    borrow_sheet_id integer NOT NULL,       -- 对应的借货单ID
    return_amount numeric(15,2) DEFAULT 0,  -- 本次还货金额
    return_time timestamp DEFAULT now(),
    CONSTRAINT pk_return_borrow_mapping PRIMARY KEY (company_id, return_sheet_id, return_detail_id, borrow_sheet_id)
);
```

### 字段扩展

#### 借货单明细表 (sheet_sale_detail)
- 新增 `borrow_mode` 字段：借货模式（QTY=按数量，AMT=按金额）

## 业务逻辑

### 借货流程

1. **选择借货模式**：
   - 按数量：传统模式，按商品数量借货
   - 按金额：新模式，按商品金额借货

2. **借货处理**：
   - 按数量模式：更新 `borrowed_cust_items` 表
   - 按金额模式：更新 `borrow_balance` 表
   - 记录借货历史到 `borrow_history` 表

### 还货流程

1. **还货验证**：
   - 按数量模式：验证商品借货数量
   - 按金额模式：验证客户借货余额

2. **还货处理**：
   - 按数量模式：减少对应商品的借货数量
   - 按金额模式：减少客户借货余额（支持跨商品）

### 跨单据借还货示例

```
场景：借A单，还B单
1. 借货单001：客户借了价值1000元的商品
   - 借货单001余额：1000元

2. 借货单002：客户又借了价值500元的商品
   - 借货单001余额：1000元
   - 借货单002余额：500元

3. 还货单003：客户还货600元，选择还给借货单001
   - 借货单001余额：1000 - 600 = 400元
   - 借货单002余额：500元（不变）

4. 还货单004：客户还货400元，选择还给借货单001
   - 借货单001余额：400 - 400 = 0元（已结清）
   - 借货单002余额：500元（不变）
```

## 前端界面

### 借货单页面增强

1. **借货模式选择**：
   - 下拉选择：按数量/按金额
   - 实时切换提示

2. **客户借货余额显示**：
   - 实时显示客户当前借货余额
   - 客户切换时自动更新

3. **智能提示**：
   - 还货时提示可用余额
   - 超额还货警告

### 新增报表页面

1. **客户借货余额** (`Report/BorrowBalance`)：
   - 查看所有客户的借货余额
   - 支持按客户、业务员、部门筛选
   - 点击余额查看明细

2. **客户借货历史** (`Report/BorrowHistory`)：
   - 查看借货历史记录
   - 支持时间范围、客户、单据类型筛选
   - 点击单据号打开对应单据

## API接口

### 新增接口

1. **获取客户借货余额**：
   ```
   GET /api/BorrowItemSheet/GetCustomerBorrowBalance
   参数：operKey, supcustId
   返回：{ result, balance }
   ```

## 配置说明

### 菜单配置

在 `menu.json` 中新增菜单项：
```json
{
  "Id": "borrowBalance",
  "Class": "menu",
  "Title": "客户借货余额",
  "Url": "Report/BorrowBalance"
},
{
  "Id": "borrowHistory", 
  "Class": "menu",
  "Title": "客户借货历史",
  "Url": "Report/BorrowHistory"
}
```

### 权限配置

在 `MenuSets.cs` 中新增权限项：
```csharp
[EnumExt("客户借货余额", report, baseRights: new MenuId[] { see, print, export })]
borrowBalance,
[EnumExt("客户借货历史", report, baseRights: new MenuId[] { see, print, export })]
borrowHistory,
```

## 升级部署

### 数据库升级

执行升级脚本：`SQL/upgrade_borrowed_amount.sql`

该脚本会：
1. 创建新的借货余额表和历史表
2. 添加必要的索引
3. 提供多种数据迁移方案
4. 验证升级结果

### 数据迁移策略

由于原系统只记录借货数量，没有价格信息，提供以下迁移方案：

#### 方案1：使用当前价格估算
- **优点**：自动化程度高
- **缺点**：可能不准确，因为当前价格与借货时价格可能不同
- **适用**：借货数据不多，对准确性要求不高的情况

#### 方案2：从历史单据计算（推荐）
- **优点**：相对准确，使用实际借货时的价格
- **缺点**：需要历史单据数据完整
- **适用**：有完整历史单据记录的情况

#### 方案3：手工设置（最安全）
- **优点**：最准确，可以与业务人员确认
- **缺点**：需要人工操作
- **适用**：重要客户或对准确性要求很高的情况

### 迁移工具

系统提供了**借货数据迁移助手**页面：
- 查看现有借货数据统计
- 选择合适的迁移方式
- 执行数据迁移操作
- 导出现有借货数据供人工核对

### 建议的迁移步骤

1. **数据备份**：升级前备份数据库
2. **执行升级脚本**：创建新表结构
3. **查看现有数据**：使用迁移助手查看现有借货情况
4. **选择迁移方式**：
   - 数据量小：建议手工设置
   - 数据量大且有历史单据：从历史单据计算
   - 其他情况：使用当前价格估算
5. **执行迁移**：通过迁移助手执行
6. **数据核对**：与业务人员核对重要客户的借货余额
7. **手工调整**：使用借货余额设置功能调整不准确的数据

### 兼容性

- **向后兼容**：原有按数量借货功能完全保留
- **双模式并存**：可以同时使用按数量和按金额两种模式
- **渐进升级**：可以逐步从按数量模式切换到按金额模式

## 使用建议

1. **适用场景**：
   - 高价值商品借货
   - 需要灵活还货的业务场景
   - 跨商品类别的借还货需求

2. **最佳实践**：
   - 建议对重要客户启用按金额借货
   - 定期检查客户借货余额
   - 及时处理长期未还的借货

3. **注意事项**：
   - 按金额模式下，还货商品价格应准确
   - 建议设置借货余额上限
   - 定期对账确保数据准确性
