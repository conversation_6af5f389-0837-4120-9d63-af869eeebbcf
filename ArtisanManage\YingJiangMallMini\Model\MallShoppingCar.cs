﻿using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using ArtisanManage.YingJiangMallMini.Dao;
using ArtisanManage.YingJiangMallMini.Services;
using Newtonsoft.Json;
using NPOI.OpenXmlFormats.Dml;

namespace ArtisanManage.YingJiangMallMini.Model;

public class MallShoppingCar
{
    public int WxUserId { get; set; }
    public string CompanyId { get; set; }

    public int SupcustId { get; set; }
    public MallShoppingCarContent ShoppingCarContent { get; set; }

    public MallShoppingCar()
    {
    }

    public MallShoppingCar(int wxUserId, string companyId, int supcustId)
    {
        WxUserId = wxUserId;
        CompanyId = companyId;
        SupcustId = supcustId;
        ShoppingCarContent = new MallShoppingCarContent
        {
            CartSheetRows = new List<CartSheetRow>(),
            CartPromotion = new CartPromotion
            {
                Combo = new List<Combo>(),
                Fullgift = new List<Fullgift>()
            }
        };
    }

    public async Task<dynamic> GetShoppingCarList(CMySbCommand cmd, dynamic queryParams)
    {
        string redisKey = @$"mallMiniShoppingCar_{WxUserId}_{CompanyId}_{SupcustId}";
        string result = await RedisHelper.GetAsync(redisKey);
        if (!string.IsNullOrEmpty(result))
        {
            ShoppingCarContent = JsonConvert.DeserializeObject<MallShoppingCarContent>(result);
        }

        if (ShoppingCarContent.CartSheetRows.Count > 0)
        {
            await ValidateShoppingCart(cmd, queryParams);
        }

        return ShoppingCarContent;
    }

    public async Task<bool> SetShoppingCarList()
    {
        string redisKey = @$"mallMiniShoppingCar_{WxUserId}_{CompanyId}_{SupcustId}";


        return await RedisHelper.SetAsync(redisKey, ShoppingCarContent);
    }

    private async Task ValidateShoppingCart(CMySbCommand cmd, dynamic queryParams)
    {
        List<ExpandoObject> itemBaseInfos = await GetShoppingCarItemsStock(cmd, queryParams);
        List<MallMiniStrategyPromotion> miniStrategyPromotions = new List<MallMiniStrategyPromotion>();
        string uniquePromotionIds = ShoppingCarContent.CartSheetRows
            .Where(row => !string.IsNullOrEmpty(row.PromotionId)) // 过滤掉 null 或空字符串的 PromotionId
            .Select(row => row.PromotionId) // 从每个 CartSheetRow 中选择 PromotionId
            .Distinct() // 移除重复的 PromotionId
            .Aggregate("", (current, promotionId) => current + (promotionId + ",")); // 将 PromotionId 拼接成一个逗号分隔的字符串
        // 由于 Aggregate 方法会在最后多添加一个逗号，我们需要移除最后一个逗号
        uniquePromotionIds = uniquePromotionIds.TrimEnd(',');
        if (!string.IsNullOrEmpty(uniquePromotionIds))
        {
            miniStrategyPromotions = await MallMiniStrategyPromotionDao.GetMallMiniStrategyPromotionWithIdsDao(cmd, new
            {
                companyId = CompanyId,
                promotionIds = uniquePromotionIds
            });
        }
        bool useMallItemClass = queryParams.useMallItemClass;
        string retailWholesaleFlag = queryParams.retailWholesaleFlag;
        bool allowNegativeStockOrder = queryParams.allow_negative_stock_order;
        string? frontBranchId = queryParams.front_branch_id ?? "";
        bool? frontBranchDiffClassValue = queryParams.front_branch_diff_class;
        // List<dynamic> _promotionList;
        // 将上次使用校验信息清空
        foreach (CartSheetRow cartSheetRow in ShoppingCarContent.CartSheetRows)
        {
            cartSheetRow.DelFlag = false;
            if (cartSheetRow.DisabledMsg != "未达门槛") // 前端控制
            {
                cartSheetRow.DisabledMsg = "";
            }

            cartSheetRow.GetShoppingCarCheckFlag = false;
        }

        foreach (Combo combo in ShoppingCarContent.CartPromotion.Combo)
        {
            combo.DisabledMsg = "";
        }

        foreach (Fullgift fullgift in ShoppingCarContent.CartPromotion.Fullgift)
        {
            if (fullgift.DisabledMsg != "未达门槛")
            {
                fullgift.DisabledMsg = "";
            }
        }

        // 校验商品
        foreach (var item in ShoppingCarContent.CartSheetRows)
        {
            dynamic itemBaseInfo = itemBaseInfos.FirstOrDefault(i => ((IDictionary<string, object>)i)["item_id"].ToString() == item.ItemId);
            if (itemBaseInfo == null)
            {
                MarkItemAsInvalid(item, ProductStatusMessages.OffShelf);
                continue;
            }
            // 处理prop
            item.PropClassId = itemBaseInfo.prop_class_id;
            item.PropOtherClass = itemBaseInfo.prop_other_class;
            // 校验促销活动
            if (!item.ItemType.Equals("general"))
            {
                string errMsg = ValidateShoppingCarPromotion(item, miniStrategyPromotions);
                if (!string.IsNullOrEmpty(errMsg))
                {
                    MarkItemAsInvalid(item, errMsg);
                    continue;
                }
            }
            if (item.ItemType.Equals("general"))
            {
                // 处理价格变化
                HandleItemPrice(item, itemBaseInfo, retailWholesaleFlag);
                // 校验最低起送量
                if (!HandleItemMinQty(item, itemBaseInfo)) { item.DisabledMsg = "未达最低起送量"; }

                // 校验最大限购量
                if (!HandleItemMaxQty(item, itemBaseInfo)) { 
                    //item.DisabledMsg = "超过最大限购量"; 
                }
                
            }
            if (item.GetShoppingCarCheckFlag == true)
            {
                continue;
            }
            // 校验商品是否已下架
            if (itemBaseInfo.status == "0" || itemBaseInfo.on_sale == false || (itemBaseInfo.mall_status == "0" && useMallItemClass))
            {
                MarkItemAsInvalid(item, ProductStatusMessages.OffShelf);
                continue;
            }
            // 校验库存
            if (!allowNegativeStockOrder)
            {
                decimal totalQuantity = ShoppingCarContent.CartSheetRows.Where(i => i.ItemId == item.ItemId).Sum(i => i.Quantity * i.UnitFactor);
                if (totalQuantity > itemBaseInfo.stock_qty)
                {
                    MarkItemAsInvalid(item, ProductStatusMessages.InsufficientStock);
                    continue;
                }
            }
            // 校验价格
            if (item.ItemType.Equals("general") && item.RealPrice == 0)
            {
                MarkItemAsInvalid(item, ProductStatusMessages.PriceZeroError);
                continue;
            }
            
            // 校验通过
            item.GetShoppingCarCheckFlag = true;
            if (string.IsNullOrEmpty(item.DisabledMsg) && !item.ItemType.Equals("general"))
            {
                item.Checked = true;
            }
        }
        
     

        foreach (var item in ShoppingCarContent.CartSheetRows.Where(i => i.DelFlag && !i.ItemType.Equals("general")))
        {
            ShoppingCarContent.CartPromotion.Combo.RemoveAll(c => c.Identity == item.Identity);
            ShoppingCarContent.CartPromotion.Fullgift.RemoveAll(f => f.Identity == item.Identity);
        }
   
        ShoppingCarContent.CartSheetRows = ShoppingCarContent.CartSheetRows
            .Where(item => !item.DelFlag)  // 只保留 DelFlag 为 false 的项
            .ToList();
    }

    // 获取所有商品库存情况
    private async Task<List<ExpandoObject>> GetShoppingCarItemsStock(CMySbCommand cmd, dynamic queryParams)
    {
        string itemIds = ShoppingCarContent.CartSheetRows
            .Select(row => row.ItemId) // 从每个 CartSheetRow 中选择 ItemId
            .Distinct() // 移除重复的 ItemId
            .Aggregate("", (current, itemId) => current + (itemId + ",")); // 将 ItemId 拼接成一个逗号分隔的字符串
        itemIds = itemIds.TrimEnd(',');
        if (string.IsNullOrEmpty(itemIds))
        {
            throw new Exception("无商品处理");
        }

        string branchId = queryParams.branch_id;
        string operKey = queryParams.operKey;
        string retailWholesaleFlag = queryParams.retailWholesaleFlag; //r零售 w批发受价格方案
        bool allowNegativeStockOrder = queryParams.allow_negative_stock_order;
        bool useMallItemClass = queryParams.useMallItemClass;
        string? frontBranchId = queryParams.front_branch_id ?? "";
        bool? frontBranchDiffClassValue = queryParams.front_branch_diff_class;
        // 获取所有商品的价格库存等
        dynamic getItemParam = new
        {
            operKey = operKey,
            itemIds = itemIds,
            useMallItemClass = useMallItemClass,
            supcustID = SupcustId,
            branchID = int.Parse(branchId),
            front_branch_id = frontBranchId,
            front_branch_diff_class = frontBranchDiffClassValue,
        };

        List<ExpandoObject> list = await MallMiniItemInfoService.MallMiniShoppingCarItemBaseInfoList(cmd, getItemParam);
        if (list == null)
        {
            return new List<ExpandoObject>();
        }

        return list;
    }

    private void MarkItemAsInvalid(CartSheetRow item, string message)
    {
        item.GetShoppingCarCheckFlag = true;
        item.DisabledMsg = message;
        item.Checked = false;

        if (item.ItemType != "general")
        {
            // 处理具有相同Identity的商品
            foreach (var row in ShoppingCarContent.CartSheetRows.Where(r => r.Identity == item.Identity && string.IsNullOrEmpty(r.DisabledMsg)))
            {
                
                row.GetShoppingCarCheckFlag = true;
                row.DisabledMsg = ProductStatusMessages.PromotionActivityFailure;
                row.Checked = false;
            }

            // 处理促销活动中的商品
            foreach (var combo in ShoppingCarContent.CartPromotion.Combo.Where(c => c.Identity == item.Identity))
            {
                combo.DisabledMsg = ProductStatusMessages.PromotionActivityFailure;
            }

            foreach (var fullgift in ShoppingCarContent.CartPromotion.Fullgift.Where(f => f.Identity == item.Identity))
            {
                fullgift.DisabledMsg = ProductStatusMessages.PromotionActivityFailure;
            }
        }
    }
    
    public void HandleItemPrice(CartSheetRow cartSheetRow, dynamic itemBaseInfo, string retailWholesaleFlag)
    {
        decimal slprice = string.IsNullOrEmpty(itemBaseInfo.slprice) ? 0 : decimal.Parse(itemBaseInfo.slprice);
        decimal mlprice = string.IsNullOrEmpty(itemBaseInfo.mlprice) ? 0 : decimal.Parse(itemBaseInfo.mlprice);
        decimal blprice = string.IsNullOrEmpty(itemBaseInfo.blprice) ? 0 : decimal.Parse(itemBaseInfo.blprice);
        decimal s_price = string.IsNullOrEmpty(itemBaseInfo.s_price) ? 0 : decimal.Parse(itemBaseInfo.s_price);
        decimal m_price = string.IsNullOrEmpty(itemBaseInfo.m_price) ? 0 : decimal.Parse(itemBaseInfo.m_price);
        decimal b_price = string.IsNullOrEmpty(itemBaseInfo.b_price) ? 0 : decimal.Parse(itemBaseInfo.b_price);
        if (retailWholesaleFlag.Equals("r"))
        {
            if ("s".Equals(cartSheetRow.Unit) && (cartSheetRow.RealPrice != slprice))
            {
                UpdateCartSheetRow(cartSheetRow, slprice, slprice);
            }
            else if ("m".Equals(cartSheetRow.Unit) && (cartSheetRow.RealPrice != mlprice))
            {
                UpdateCartSheetRow(cartSheetRow, mlprice, mlprice);
            }
            else if ("b".Equals(cartSheetRow.Unit) && (cartSheetRow.RealPrice != blprice))
            {
                UpdateCartSheetRow(cartSheetRow, blprice, blprice);
            }
        }
        else if (retailWholesaleFlag.Equals("w"))
        {
            if ("s".Equals(cartSheetRow.Unit) && (cartSheetRow.RealPrice != s_price))
            {
                UpdateCartSheetRow(cartSheetRow, s_price, slprice);
            }
            else if ("m".Equals(cartSheetRow.Unit) && (cartSheetRow.RealPrice != m_price))
            {
                UpdateCartSheetRow(cartSheetRow, m_price, mlprice);
            }
            else if ("b".Equals(cartSheetRow.Unit) && (cartSheetRow.RealPrice != b_price))
            {
                UpdateCartSheetRow(cartSheetRow, b_price, blprice);
            }
        }
    }

    public bool HandleItemMinQty(CartSheetRow cartSheetRow, dynamic itemBaseInfo)
    {
        // 使用字典将单位映射到相应的最小数量
        var minQtyMapping = new Dictionary<string, int>
        {
            { "s", string.IsNullOrEmpty(itemBaseInfo.s_mall_min_qty) ? 0 : int.Parse(itemBaseInfo.s_mall_min_qty) },
            { "m", string.IsNullOrEmpty(itemBaseInfo.m_mall_min_qty) ? 0 : int.Parse(itemBaseInfo.m_mall_min_qty) },
            { "b", string.IsNullOrEmpty(itemBaseInfo.b_mall_min_qty) ? 0 : int.Parse(itemBaseInfo.b_mall_min_qty) }
        };
        // 检查是否包含该单位
        if (minQtyMapping.ContainsKey(cartSheetRow.Unit))
        {
            int unitMinQty = minQtyMapping[cartSheetRow.Unit];

            if(unitMinQty == 0)
            {
                return true;
            }
            // 设置最小数量
            if (cartSheetRow.MinQty < unitMinQty)
            {
                //cartSheetRow.MinQty = unitMinQty;
                //给前端返回提示信息
                return  false; 
            }

            // 如果数量小于最小数量，更新数量和小计
            if (cartSheetRow.Quantity < cartSheetRow.MinQty)
            {
                //cartSheetRow.Quantity = cartSheetRow.MinQty;
                //cartSheetRow.SubAmount = cartSheetRow.Quantity * cartSheetRow.RealPrice;
                //给前端返回提示信息
                return false;
            }
        }
        return true;
    }
    public bool HandleItemMaxQty(CartSheetRow cartSheetRow, dynamic itemBaseInfo)
    {
        // 使用字典将单位映射到相应的最小数量
        var maxQtyMapping = new Dictionary<string, int>
        {
            { "s", string.IsNullOrEmpty(itemBaseInfo.s_mall_max_qty) ? 999 : int.Parse(itemBaseInfo.s_mall_max_qty) },
            { "m", string.IsNullOrEmpty(itemBaseInfo.m_mall_max_qty) ? 999 : int.Parse(itemBaseInfo.m_mall_max_qty) },
            { "b", string.IsNullOrEmpty(itemBaseInfo.b_mall_max_qty) ? 999 : int.Parse(itemBaseInfo.b_mall_max_qty) }
        };
        // 检查是否包含该单位
        if (maxQtyMapping.ContainsKey(cartSheetRow.Unit))
        {
            int unitMaxQty = maxQtyMapping[cartSheetRow.Unit];
            // 设置最大数量
            if (cartSheetRow.MaxQty > unitMaxQty)
            {
                //cartSheetRow.MaxQty = unitMaxQty;
                //给前端返回提示信息
                return false;
            }

            if (cartSheetRow.Quantity > cartSheetRow.MaxQty)
            {
                //cartSheetRow.Quantity = cartSheetRow.MaxQty;
                //cartSheetRow.SubAmount = cartSheetRow.Quantity * cartSheetRow.RealPrice;
                //给前端返回提示信息
                return false;
            }
        }
            return true;
    }

    private void UpdateCartSheetRow(CartSheetRow cartSheetRow, decimal price, decimal lprice)
    {
        cartSheetRow.RealPrice = price;
        cartSheetRow.OrigPrice = price;
        cartSheetRow.SysPrice = price;
        cartSheetRow.RetailPrice = lprice;
        cartSheetRow.SubAmount = cartSheetRow.RealPrice * cartSheetRow.Quantity;
    }

    private string ValidateShoppingCarPromotion(CartSheetRow item,  List<MallMiniStrategyPromotion> miniStrategyPromotions)
    {
        MallMiniStrategyPromotion strategyPromotion = miniStrategyPromotions.FirstOrDefault(promotion => promotion.promotion_id+"" == item.PromotionId);
        // 活动删除 PromotionId不存在
        if (strategyPromotion == null)
        {
            item.DelFlag = true;
            return ProductStatusMessages.PromotionDelete;
        }
        // 活动范围 wechatmini,yjapp
        if (!string.IsNullOrEmpty(strategyPromotion.promotion_scope) && !strategyPromotion.promotion_scope.Contains("wechatmini"))
        {
            item.DelFlag = true;
            return ProductStatusMessages.PromotionScopeNotAllow;
        }
        // 活动时间
        if (DateTime.Now > strategyPromotion.end_time)
        {
            item.DelFlag = true;
            return ProductStatusMessages.PromotionOffShelf;
        }
        // 渠道、等级、片区
        // 活动移除
        if (item.PromotionType.Equals("combosrc") || item.PromotionType.Equals("combogif"))
        {
            // 促销组合移除未找到
            
        }
        if (item.PromotionType.Equals("seckill"))
        {
            // 特价商品移除
        }
        if (item.PromotionType.Equals("fullgift"))
        {
            // 满减赠品移除
        }
        // 限时特价的情况
        
        // 活动限购情况
        return "";
    }
}


// genComboIdentity(combo) {
//     const cb = i => i.amount + i.unit_no + i.items_id
//     const sources = combo.sources.map(cb)
//     const gifts = combo.gifts.map(cb)
//
//     return `${combo.title}:买${sources.join(';')}送${gifts.join(';')}`
// }
// genFullgiftIdentity(fullgift) {
//     const cb = i => i.amount + i.unit_no + i.items_id
//     const gifts = fullgift.gifts.map(cb)
//
//     return `满${fullgift.threshold}送${gifts.join(';')}`
// }
// genSeckillIdentity(seckill) {
//     return `period:${seckill.__killPeriod.join('-')}item_id:${seckill.item_id}unit_no:${seckill.unit_no}price:${seckill.wholesale_price}`
// },
public static class ProductStatusMessages
{
    // 商品下架
    public const string OffShelf = "商品下架";

    // 库存不足
    public const string InsufficientStock = "补货中";

    // 价格异常
    public const string PriceZeroError = "暂无价格";

    // 促销活动删除
    public const string PromotionDelete = "活动下架";
    
    // 促销活动范围
    public const string PromotionScopeNotAllow = "活动下架";
    
    // 促销活动下架
    public const string PromotionOffShelf = "活动下架";

    // 超出限时特价时间范围
    public const string ExpiredTimeOffer = "超出限时";
    
    public const string PromotionActivityFailure = "活动缺货";
}

public class ShoppingCartValidatorItemBaseInfo
{
    public string item_id { get; set; }
    public bool? on_sale { get; set; }
    public string son_mum_item { get; set; }
    public string item_name { get; set; }
    public string status { get; set; }
    public string mall_status { get; set; }
    public decimal stock_qty { get; set; }
    public decimal bstock { get; set; }
    public decimal mstock { get; set; }
    public decimal sstock { get; set; }
    public string sunit { get; set; }
    public string munit { get; set; }
    public string bunit { get; set; }

    public decimal sfactor { get; set; }
    public decimal mfactor { get; set; }
    public decimal bfactor { get; set; }

    public string s_barcode { get; set; }
    public string m_barcode { get; set; }
    public string b_barcode { get; set; }

    public string spprice { get; set; }
    public string mpprice { get; set; }
    public string bpprice { get; set; }


    public string slprice { get; set; }
    public string mlprice { get; set; }
    public string blprice { get; set; }

    public string s_recent_price { get; set; }
    public string m_recent_price { get; set; }
    public string b_recent_price { get; set; }


    public string s_buy_price { get; set; }
    public string m_buy_price { get; set; }
    public string b_buy_price { get; set; }


    public string s_recent_orig_price { get; set; }
    public string m_recent_orig_price { get; set; }
    public string b_recent_orig_price { get; set; }

    public string s_lowest_price { get; set; }
    public string m_lowest_price { get; set; }
    public string b_lowest_price { get; set; }

    public dynamic? availAttrCombine { get; set; }
    public dynamic? itemMumAttributes { get; set; }

    public string s_price { get; set; }
    public string m_price { get; set; }
    public string b_price { get; set; }


    public string s_plan_price { get; set; }
    public string m_plan_price { get; set; }
    public string b_plan_price { get; set; }
    public string plan_id { get; set; }
    public string plan_name { get; set; }
    
    public string prop_class_id { get; set; }
    public string prop_other_class { get; set; }
    
    public string s_mall_min_qty { get; set; }
    public string m_mall_min_qty { get; set; }
    public string b_mall_min_qty { get; set; }
    
    public string s_mall_max_qty { get; set; }
    public string m_mall_max_qty { get; set; }
    public string b_mall_max_qty { get; set; }
}

public class ShoppingCartValidatorItemBaseOnSaleInfo
{
    public string item_id { get; set; }
    public bool on_sale { get; set; }
}

public class MallShoppingCarContent
{
    [JsonProperty("cartSheetRows")] public List<CartSheetRow> CartSheetRows { get; set; }

    [JsonProperty("cartPromotion")] public CartPromotion CartPromotion { get; set; }
}

public class CartSheetRow
{
    [JsonProperty("attr_qty")] public string AttrQty { get; set; }

    [JsonProperty("classId")] public string ClassId { get; set; }

    [JsonProperty("item_id")] public string ItemId { get; set; }
    [JsonProperty("item_brand")] public string ItemBrand { get; set; }
    [JsonProperty("other_class")] public string OtherClass { get; set; }

    [JsonProperty("son_mum_item")] public string SonMumItem { get; set; }

    [JsonProperty("item_name")] public string ItemName { get; set; }

    [JsonProperty("barcode")] public string Barcode { get; set; }

    [JsonProperty("remark")] public string Remark { get; set; }

    [JsonProperty("unit_no")] public string UnitNo { get; set; }

    [JsonProperty("unit_factor")] public decimal UnitFactor { get; set; }

    [JsonProperty("orig_price")] public decimal? OrigPrice { get; set; }

    [JsonProperty("real_price")] public decimal RealPrice { get; set; }

    [JsonProperty("sys_price")] public decimal? SysPrice { get; set; }

    [JsonProperty("retail_price")] public decimal? RetailPrice { get; set; }

    [JsonProperty("sub_amount")] public decimal SubAmount { get; set; }

    [JsonProperty("quantity")] public decimal Quantity { get; set; }

    [JsonProperty("trade_type")] public string TradeType { get; set; }

    [JsonProperty("promotion_id")] public string PromotionId { get; set; }

    [JsonProperty("promotion_type")] public string PromotionType { get; set; }
    [JsonProperty("prop_class_id")] public string PropClassId { get; set; }
    [JsonProperty("prop_other_class")] public string PropOtherClass { get; set; }
    /*
     普通商品: general
     促销组合本品: combosrc
     促销组合赠品: combogif
     特价商品: seckill  //
     满减赠品: fullgift
     * */
    [JsonProperty("__item_type")] public string ItemType { get; set; }

    //  b m s
    [JsonProperty("__unit")] public string Unit { get; set; }
    [JsonProperty("__min_qty")] public int MinQty { get; set; }
    [JsonProperty("__max_qty")] public int MaxQty { get; set; }

    [JsonProperty("__img")] public string Img { get; set; }

    [JsonProperty("__attr")] public List<string> Attr { get; set; }

    // 小单位库存
    [JsonProperty("__stock")] public decimal? Stock { get; set; }

    [JsonProperty("__disabled_msg")] public string DisabledMsg { get; set; }

    // false 失效
    [JsonProperty("__checked")] public bool Checked { get; set; }

    [JsonProperty("__identity")] public string Identity { get; set; }

    [JsonProperty("__get_shopping_car_check_flag")]
    public bool? GetShoppingCarCheckFlag { get; set; }

    [JsonProperty("__del_flag")] public bool DelFlag { get; set; }
    /*
     * 对购物车内容进行处理
     * 对于商品的处理可依据ProductStatusMessages 进行处理，对于处理过的商品就不需要进行二次判断验证，即GetShoppingCarCheckFlag为true说明是判断过的
     * 商品类型依据ItemType进行区分 普通商品: general 促销组合本品: combosrc促销组合赠品: combogif 特价商品: seckill  // 满减赠品: fullgift
     *
     * 在校验之前需要将GetShoppingCarCheckFlag都置为false，或者在存储的时候就把GetShoppingCarCheckFlag置为false
     *
     * List<ShoppingCartValidatorItemBaseInfo> 可以用于判断商品商品已下架、库存不足
     * List<promotionItem> promotionList
     * 每个促销活动（促销组合、满减、满赠、限时特价） 就是promotionItem
     *
     *
     *  对于不满足校验的数据进行如下处理
     *   如果商品是普通商品
     *  (1) GetShoppingCarCheckFlag 为 true ,
     *  (2) DisabledMsg 赋值 填写具体的不满足提交
     *  (3) Checked 为 false
     *  如果是非普通商品，除了进行上述操作之外
     *  （1） 将购物车列表中其他的具有Identity 相同商品也进行同样的操作
     *  （2） 将Combo和Fullgift中Identity相同的，将要将DisabledMsg进行赋值
     *
     * 对于处理过的商品就不需要进行二次判断验证，即GetShoppingCarCheckFlag为true说明是判断过的
     * 校验规则如下
     * 1.商品已下架
     *  1.1 下架依据ShoppingCartValidatorItemBaseInfo中status == '0'
     *  1.2 如果status != '0', 且 useMallItemClass 为true 需要继续判断 mall_status== '0'
     *
     *
     * 2. 判断库存  ShoppingCartValidatorItemBaseInfo中stock_qty 记录了item_id的库存情况
     *  2.1 判断依据购物车中相同的ItemId一组商品 进行求和 quantity * unit_factor ，如果结果 大于 stock_qty 意味着 库存不足
     *      如果库存不足，如果有非普通商品， 那么就按上述流程处理不满足校验的数据进
     *      如果这一组商品中包含普通商品，
     *          1. 先计算最大能满足 quantity * unit_factor <= stock_qty
     *          2. 最大满足之后stock_qty = quantity * unit_factor， 这一组普通商品剩下的接着判断，直到出现 quantity * unit_factor > stock_qty 那就说明这个商品不满足校验的数据
     *
     *
     * 3. 促销活动下架
     * 4.超出限时特价时间范围
     * 3和4暂时不判断，后续进行拓展
     */
}

public class CartPromotion
{
    [JsonProperty("combo")] public List<Combo> Combo { get; set; }

    [JsonProperty("fullgift")] public List<Fullgift> Fullgift { get; set; }
}

public class Combo
{
    [JsonProperty("coefficient")] public decimal Coefficient { get; set; }

    [JsonProperty("identity")] public string Identity { get; set; }

    [JsonProperty("title")] public string Title { get; set; }

    [JsonProperty("disabledMsg")] public string DisabledMsg { get; set; }
}

public class Fullgift
{
    [JsonProperty("identity")] public string Identity { get; set; }

    [JsonProperty("threshold")] public string Threshold { get; set; }

    [JsonProperty("disabledMsg")] public string DisabledMsg { get; set; }
}