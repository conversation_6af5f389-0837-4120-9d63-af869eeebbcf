﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using ArtisanManage.WeChat.Conf;
using MathNet.Numerics.Random;
using Newtonsoft.Json;
using OBS.Internal;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Linq;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using TZBClient.Utils;
using static System.Runtime.InteropServices.JavaScript.JSType;
using System.Dynamic;
using System.Data;

namespace ArtisanManage.WebAPI
{
    public class TzbankApi
    {
        #region Constructor and Constants
        public TzbankApi() { }

        // 静态字段缓存密钥
        private static string merchantId;
        private static string privateCertiKey;
        private static string platformPublicKey;
        private static string myPublicKey;
        private static string appKey;
        private static string appSecret;
        private static string partnerId;
        private static bool isInited = false;

        /// <summary>
        /// 初始化密钥信息（建议在程序启动或首次用到时调用）
        /// </summary>
        public static async Task<bool> InitMerchantKeysAsync(CMySbCommand cmd, string companyId)
        {
            // channelId为TzbankController.PayChannelId
            var channelId = TzbankController.PayChannelId;
            var result = await GetMerchantKeysAsync(cmd, companyId, channelId.ToString());
            if (!result.IsOK) return false;
            dynamic keys = JsonConvert.DeserializeObject(result.data);
            merchantId = keys.merchant_id;
            privateCertiKey = keys.private_certi_key;
            platformPublicKey = keys.platform_public_key;
            myPublicKey = keys.mypublic_key;
            appKey = keys.app_key;
            appSecret = keys.app_secret;
            partnerId = keys.merchant_id;
            isInited = true;
            // 控制台输出日志，展示6个参数
            Console.WriteLine($"[TzbankApi] 从数据库获取密钥：PrivateCertiKey={privateCertiKey}, PlatformPublicKey={platformPublicKey}, MyPublicKey={myPublicKey}, AppKey={appKey}, AppSecret={appSecret}, merchant_id={partnerId}");
            return true;
        }

        /// <summary> 私钥 </summary>
        private static string PrivateCertiKey
        {
            get
            {
                if (!isInited) throw new Exception("TzbankApi密钥未初始化");
                return privateCertiKey;
            }
        }
        /// <summary> 公钥 </summary>
        public static string PlatformPublicKey
        {
            get
            {
                if (!isInited) throw new Exception("TzbankApi密钥未初始化");
                return platformPublicKey;
            }
        }
        public static string MyPublicKey
        {
            get
            {
                if (!isInited) throw new Exception("TzbankApi密钥未初始化");
                return myPublicKey;
            }
        }
        private static string AppKey
        {
            get
            {
                if (!isInited) throw new Exception("TzbankApi密钥未初始化");
                return appKey;
            }
        }
        private static string AppSecret
        {
            get
            {
                if (!isInited) throw new Exception("TzbankApi密钥未初始化");
                return appSecret;
            }
        }
        /// <summary> 商户号 </summary>
        private static string PartnerId
        {
            get
            {
                if (!isInited) throw new Exception("TzbankApi密钥未初始化");
                return partnerId;
            }
        }

        #endregion

        /// <summary>
        /// 交易成功异步通知到商户的后台地址<br></br>
        /// 支持多个url进行异步通知，多个url用分隔符“,”分开，如：url1,url2,url3<br></br>
        /// 支持TLS1.0、TLS1.1、TLS1.2
        /// <para>须保持各地址连接畅通,否则回调会被拉黑</para>
        /// </summary>
        private const string DefaultPayBillCallbackUrls = "https://s3.yingjiang.co/openapi/tzbank/PayBillStatusChanged";
        private const string DefaultRefundBillCallbackUrls = "https://s3.yingjiang.co/openapi/tzbank/RefundBillStatusChanged";

        /// <summary> 场景号 </summary>
        /// 已经问过台州银行对接人员，小程序请使用D0C
        private static class BusinessCodes
        {
            /// <summary> 适用于：PC </summary>
            public const string D0A = "00";
            /// <summary> 适用于：APP(对应使用提供的 sdk 包接入) </summary>
            public const string D0B = "01";
            /// <summary> 适用于：H5(对应使用 http 方式接入) </summary>
            public const string D0C = "02";
        }

        /// <summary>
        /// 台州银行支付订单超时时间<br></br>
        /// 一旦超时，该笔交易就会自动被关闭，范围：1m～15d。
        /// m-分钟，h-小时，d-天。该参数数值不接受小数点
        /// <para>修改此项须同时修改TzbankController.DoBillCreate的Redis存储时限</para>
        /// </summary>
        private const string PaybillTimeout = "10m";
        public static TimeSpan PaybillTimeoutTS
        {
            get
            {
                var unit = PaybillTimeout.Last().ToString();
                var amount = int.Parse(PaybillTimeout.Replace(unit, ""));
                if (unit == "d") return TimeSpan.FromDays(amount);
                else if (unit == "h") return TimeSpan.FromHours(amount);
                else return TimeSpan.FromMinutes(amount); // "m"
            }
        }
        // #endregion

        #region Sub-classes
        public class TzbankTradeNo
        {
            /* 商户系统生成的订单号，须保证在商户端不重复
             */

            /// <summary>
            /// 传入信息参数生成订单号
            /// 字母 a 是分隔符
            /// 隐患：公司数量和订单数量的增加是否会超过32位
            /// </summary>
            public static string Encrypt(string sheetId, string sheetType, string billId, string companyId, DateTime? billTime = null)
            {
                billTime ??= DateTime.Now;
                return $"{billTime:yyyyMMdd}v2a{sheetType}a{sheetId}a{billId}a{companyId}";
            }
            /// <summary>
            /// 传入订单号获取信息参数
            /// </summary>
            public static void Decrypt(string tradeNo, out string sheetId, out string sheetType, out string billId, out string companyId)
            {
                tradeNo ??= "";
                var tradeInfos = tradeNo.Split('a');
                sheetType = tradeInfos.Length >= 2 ? tradeInfos[1] : string.Empty;
                sheetId = tradeInfos.Length >= 3 ? tradeInfos[2] : string.Empty;
                billId = tradeInfos.Length >= 4 ? tradeInfos[3] : string.Empty;
                companyId = tradeInfos.Length >= 5 ? tradeInfos[4] : string.Empty;
            }
        }

        public class TzbankRefundNo
        {
            /* 商户系统生成的订单号，须保证在商户端不重复
             */

            /// <summary>
            /// 传入信息参数生成订单号
            /// 字母 a 是分隔符
            /// 隐患：公司数量和订单数量的增加是否会超过32位
            /// </summary>
            public static string Encrypt(string sheetId, string sheetType, string billId, string companyId, DateTime? billTime = null)
            {
                billTime ??= DateTime.Now;
                return $"{billTime:yyyyMMdd}v2b{sheetType}b{sheetId}b{billId}b{companyId}";
            }
            /// <summary>
            /// 传入订单号获取信息参数
            /// </summary>
            public static void Decrypt(string tradeNo, out string sheetId, out string sheetType, out string billId, out string companyId)
            {
                tradeNo ??= "";
                var tradeInfos = tradeNo.Split('b');
                sheetType = tradeInfos.Length >= 2 ? tradeInfos[1] : string.Empty;
                sheetId = tradeInfos.Length >= 3 ? tradeInfos[2] : string.Empty;
                billId = tradeInfos.Length >= 4 ? tradeInfos[3] : string.Empty;
                companyId = tradeInfos.Length >= 5 ? tradeInfos[4] : string.Empty;
            }
        }


        public class TzbankTradeStatus
        {
            public static string ParseToDbStatusTrade(string tz_status)
            {
                switch (tz_status)
                {
                    case "70":
                    case "80":
                        return PayBillController.BillStatus.Canceled;
                    case "90":
                        return PayBillController.BillStatus.Paid;
                    default: 
                        return PayBillController.BillStatus.Unpaid;
                }
            }

            public static string ParseToDbStatusRefund(string tz_status)
            {
                switch (tz_status)
                {
                    case "90":
                        return PayBillController.BillStatus.Returned;
                    default:
                        return PayBillController.BillStatus.Unpaid;
                }
            }
        }
        #endregion


        #region Private Functions
        /// <summary>
        /// 构建请求头
        /// </summary>
        /// tranDate: 请求时间（yyyyMMdd）
        /// tranSeq: 请求流水号（yyyyMMddHHmmss+随机数）
        /// <returns></returns>
        private static Dictionary<string, string> BuildReqHeader()
        {
            string timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
            RandomNumberGenerator rng = RandomNumberGenerator.Create();
            byte[] randomNumberBytes = new byte[4]; 
            rng.GetBytes(randomNumberBytes);
            int randomNumber = BitConverter.ToInt32(randomNumberBytes, 0);
            randomNumber = Math.Abs(randomNumber % 1000); 
            string tranSeq = timestamp + randomNumber.ToString("D3");
            return new Dictionary<string, string>()
            {
                { "tranDate", DateTime.Now.ToString("yyyyMMdd") },
                { "tranSeq",  tranSeq}
            };
        }
        #endregion

        #region Public: 获取token和randomSec
        /// <summary>
        /// 获取token和randomSec
        /// </summary>
        /// <returns></returns>
        public static async Task<dynamic> GetToken()
        {
            string appKey = TzbankApi.AppKey;
            string redisKey = $"TzbankToken:" + TzbankApi.merchantId;

            // 1. 检查 Redis 中是否存在有效 Token
            string cachedResponseJson = await RedisHelper.GetAsync<string>(redisKey);
            if (!string.IsNullOrEmpty(cachedResponseJson))
            {
                // 直接返回缓存的原始响应
                return JsonConvert.DeserializeObject<dynamic>(cachedResponseJson);
            }

            // 2. 缓存不存在或已过期，调用 API 获取新 Token
            string requestUrl = "https://open.tzbank.com/ApiGateWay/auth/getToken";
            string appSecret = TzbankApi.AppSecret;
            string param = "114.222.117.104";
            string publicKey = TzbankApi.PlatformPublicKey;
            string privateKey = TzbankApi.PrivateCertiKey;


            // 3. 调用 API 获取结果
            string result = TZBClient.TZBClient.tokenRequest(
                requestUrl, appKey, appSecret, param, publicKey, privateKey
            );


            // 4. 解析响应并存储到 Redis
            dynamic response = JsonConvert.DeserializeObject<dynamic>(result);
            if (response?.retType == "S") // 仅当成功时缓存
            {
                // 存储原始 API 响应（包含 Token 和 RandomSec）
                // 有效期 30 分钟
                await RedisHelper.SetAsync(redisKey, result, 1795);
            }


            // 5. 返回原始 API 响应
            return response;
        }
        #endregion


        #region Public: 支付申请（static）
        /// <summary>
        /// 支付申请
        /// </summary>
        /// <param name="tradeNo">我方的订单号</param>
        /// <param name="payAmount">支付金额</param>
        /// <param name="merchantId">商户号</param>
        /// <param name="companyId">商户公司编号</param>
        /// <returns></returns>
        public static async Task<dynamic> PayBill(
            string tradeNo, decimal payAmount, string merchantId, string companyId, string name, string phone,
            string Token, string RandomSec, string callbackUrl = null)
        {
            var url = "https://open.tzbank.com/ApiGateWay/apihandle/prod/1.0/payCreateOrder";
            long futureTimestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() + 10 * 60 * 1000;
            string timestampString = futureTimestamp.ToString();
            var reqBody = new Dictionary<string, dynamic>()
            {
                { "orderNo", tradeNo },
                { "orderSource", BusinessCodes.D0C },
                { "trxAmt", payAmount },
                { "currency", "CNY" },
                { "transType", "2001"},
                { "tradeMerCstNo", merchantId },
                { "platMerCstNo", PartnerId },
                { "businessCstNo", companyId },
                { "goodsInfo", tradeNo },
                { "payNotifyUrl", callbackUrl ?? DefaultPayBillCallbackUrls },
                { "payerName", name},
                { "payerMobileNo", phone},
                { "needLedger", "01"},
                { "expiredTime", timestampString}
            };

            var reqHeader = BuildReqHeader();

            var param = new Dictionary<string, dynamic>
            {
                { "reqHeader", reqHeader },
                { "reqBody", reqBody }
            };


            // 台州银行提供的客户端是同步方法
            var res = await Task.Run(() => 
                TZBClient.TZBClient.clientRequest(
                    url, AppKey, RandomSec, JsonConvert.SerializeObject(param),
                    PlatformPublicKey, PrivateCertiKey, Token
                )
            );


            dynamic response = JsonConvert.DeserializeObject<dynamic>(res);
            return response;
        }
        #endregion

        #region Public: 唤起收银台小程序支付（static）
        /// <summary>
        /// 唤起收银台
        /// </summary>
        /// <param name="companyId">业务系统客户号</param>
        /// <param name="tradeNo">支付系统订单号</param>
        /// <returns></returns>
        public static async Task<dynamic> WakeupCashierPay(
        string companyId, string tradeNo, string Token, string RandomSec)
        {
            var url = "https://open.tzbank.com/ApiGateWay/apihandle/prod/1.0/miniProgramCode";
            var reqBody = new Dictionary<string, dynamic>()
            { 
                { "platMerCstNo", PartnerId },
                { "businessCstNo", companyId },
                { "orderNo", tradeNo }
            };
            var reqHeader = BuildReqHeader();

            var param = new Dictionary<string, dynamic>
            {
                { "reqHeader", reqHeader },
                { "reqBody", reqBody }
            };


            // 台州银行提供的客户端是同步方法
            var res = await Task.Run(() =>
                TZBClient.TZBClient.clientRequest(
                    url, AppKey, RandomSec, JsonConvert.SerializeObject(param),
                    PlatformPublicKey, PrivateCertiKey, Token
                )
            );


            dynamic response = JsonConvert.DeserializeObject<dynamic>(res);
            return response;
        }
        #endregion

        #region Public: 查询订单状态 (Static)
        /// <summary>
        /// 查询订单状态
        /// </summary>
        /// <param name="orderFlowNo">行方返回的订单号</param>
        /// <returns></returns>
        public static async Task<dynamic> GetPayOrderStatus(
            string orderFlowNo, string RandomSec, string Token)
        {
            var url = "https://open.tzbank.com:9085/ApiGateWay/apihandle/prod/1.0/payQueryOrder";
            var reqBody = new Dictionary<string, dynamic>()
            {
                { "orderFlowNo", orderFlowNo },
                { "platMerCstNo", PartnerId }
            };
            var reqHeader = BuildReqHeader();
            var param = new Dictionary<string, dynamic>
            {
                { "reqHeader", reqHeader },
                { "reqBody", reqBody }
            };



            var res = await Task.Run(() =>
                TZBClient.TZBClient.clientRequest(
                    url, AppKey, RandomSec, JsonConvert.SerializeObject(param),
                    PlatformPublicKey, PrivateCertiKey, Token
                )
            );


            dynamic response = JsonConvert.DeserializeObject<dynamic>(res);
            return response;
        }
        #endregion

        #region Public: 申请订单退款 (Static)
        /// <summary>
        /// 申请订单退款
        /// </summary>
        /// <param name="tradeNo">应与YseTradeNo.Encrypt()生成的订单号一致</param>
        /// <param name="refundOrderNo">退款订单号</param>
        /// <param name="trxSendTime">退款请求时间</param>
        /// <param name="refundAmount">退款金额</param>
        /// <returns></returns>
        public static async Task<dynamic> RefundBill(
            string tradeNo, string refundOrderNo, string trxSendTime, decimal refundAmount, string Token, string RandomSec, string notifyURL = null)
        {
            var url = "https://open.tzbank.com/ApiGateWay/apihandle/prod/1.0/payRefund";
            var out_request_no = Security.ConvertToMd5($"RD{tradeNo}");
            var reqBody = new Dictionary<string, dynamic>()
            {
                { "oldPayOutOrderNo", tradeNo },
                { "refundOrderNo", refundOrderNo },
                { "trxSendTime", trxSendTime },
                { "platMerCstNo", PartnerId },
                { "refundOrdTransAmt", refundAmount },
                { "notifyURL", notifyURL ?? DefaultRefundBillCallbackUrls }
            };
            var reqHeader = BuildReqHeader();
            var param = new Dictionary<string, dynamic>
            {
                { "reqHeader", reqHeader },
                { "reqBody", reqBody }
            };



            var res = await Task.Run(() => 
                TZBClient.TZBClient.clientRequest(
                    url, AppKey, RandomSec, JsonConvert.SerializeObject(param),
                    PlatformPublicKey, PrivateCertiKey, Token
                )
            );


            dynamic response = JsonConvert.DeserializeObject<dynamic>(res);
            return response;
        }
        #endregion

        #region Public: 退款结果查询 (Static)
        /// <summary>
        /// 退款结果查询
        /// </summary>
        /// <param name="refundOrderNo">商户退款订单号</param>
        /// <param name="trxSendTime">退款时间</param>
        /// <returns></returns>
        public static async Task<dynamic> GetRefundBillStatus(
            string refundOrderNo, string trxSendTime, string RandomSec, string Token)
        {
            var url = "https://open.tzbank.com:9085/ApiGateWay/apihandle/prod/1.0/payRefundQuery";
            var reqBody = new Dictionary<string, dynamic>()
            {
                { "refundOrderNo", refundOrderNo },
                { "trxSendTime", trxSendTime },
                { "platMerCstNo", PartnerId }
            };
            var reqHeader = BuildReqHeader();
            var param = new Dictionary<string, dynamic>
            {
                { "reqHeader", reqHeader },
                { "reqBody", reqBody }
            };


            var res = await Task.Run(() =>
                TZBClient.TZBClient.clientRequest(
                    url, AppKey, RandomSec, JsonConvert.SerializeObject(param),
                    PlatformPublicKey, PrivateCertiKey, Token
                )
            );


            dynamic response = JsonConvert.DeserializeObject<dynamic>(res);
            return response;
        }
        #endregion

        #region Public: 解密处理 (Static)
        /// <summary>
        /// 回调报文解密处理
        /// </summary>
        /// <param name="encryptedJson">行方发回的通知</param>
        /// <returns></returns>
        public static string DecryptMessage(string encryptedJson)
        {
           return SM4Utils.Decrypt(TzbankApi.AppSecret, encryptedJson);
        }
        #endregion

        #region Public: 获取平台商户号 (Static)
        /// <summary>
        /// 获取平台商户号
        /// </summary>
        public static string getPartnerId()
        {
            return PartnerId;
        }
        #endregion

        /// <summary>
        /// 根据 company_id 和 channel_id 查询台州银行商户密钥信息
        /// </summary>
        public static async Task<CallResult> GetMerchantKeysAsync(CMySbCommand cmd, string companyId, string channelId)
        {
            var _sql_queue = new SQLQueue(cmd);
            var merchantKeys = new List<ExpandoObject>();
            var sql = $@"
                SELECT
                    company_id,merchant_id,merchant_name, private_certi_key, platform_public_key,mypublic_key,app_key,app_secret
                FROM
                    pay_merchant
                WHERE
                    company_id = {companyId} AND channel_id = {channelId}
                LIMIT 1;
            ";
            _sql_queue.Enqueue("merchant_keys", sql);
            var _dr = await _sql_queue.ExecuteReaderAsync();
            while (_sql_queue.Count > 0)
            {
                var sqlName = _sql_queue.Dequeue();
                if (sqlName == "merchant_keys")
                {
                    merchantKeys = CDbDealer.GetRecordsFromDr(_dr, false);
                }
            }
            _dr.Close();
            _sql_queue.Clear();

            if (merchantKeys.Count == 0)
                return new CallResult("Error", "未找到商户密钥信息");
            var result = JsonConvert.SerializeObject(merchantKeys[0]);
            return new CallResult("OK", "", result);
        }
                
    }

    
}
  