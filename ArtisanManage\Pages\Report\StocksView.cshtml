@page
@model ArtisanManage.Pages.BaseInfo.StocksViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>

    <partial name="_QueryPageHead" model="Model.PartialViewModel"/> 

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
  
    	var newCount = 1;

    	var itemSource = {};
    	$(document).ready(function () {
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)
            $("#popBatch").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 400, width: 600, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

            
            $("#gridItems").on("cellclick", function (event) {
               var args = event.args;

              // debugger
                var item_id = args.row.bounddata.item_id;
                var item_name = args.row.bounddata.item_name;
                var branch_id = args.row.bounddata.branch_id ? args.row.bounddata.branch_id:"";
                var branch_name = args.row.bounddata.branch_name ? args.row.bounddata.branch_name : "";
                if (!branch_id) {
                    let branchs = $("#branch_id").val()
                    if (branchs) {
                        if (branchs instanceof Array && branchs.length == 1){
                            branch_id = branchs[0].value
                            branch_name = branchs[0].label
                        }
                        if (branchs instanceof Object){
                            branch_id = branchs.value
                            branch_name = branchs.label
                        }
                    }
                }
                var branch_position = args.row.bounddata.branch_position?args.row.bounddata.branch_position:"0";
                var branch_position_name = args.row.bounddata.branch_position_name? args.row.bounddata.branch_position_name:"";
                if (!branch_position) {
                    let branchpositions = $("#branch_position").val()
                    if (branchpositions) {
                        if (branchpositions instanceof Array && branchpositions.length == 1) {
                            branch_position_name = branchpositions[0].label
                        }
                        if (branchpositions instanceof Object) {
                            branch_position_name = branchpositions.label
                        }
                    }
                }
                let showBatch = $("#showMultiBatch").val().toString().toLowerCase() == "true" ? true : false;
                var produce_date = args.row.bounddata && args.row.bounddata.produce_date 
                    ? (args.row.bounddata.produce_date !== '' && args.row.bounddata.produce_date.substr(1,1) === '[') 
                        ? JSON.parse(args.row.bounddata.produce_date) 
                        : args.row.bounddata.produce_date
                    : '';
                var batch_no = args.row.bounddata && args.row.bounddata.batch_no ? args.row.bounddata.batch_no : "";
                var batch_id = args.row.bounddata.batch_id;
                if (!showBatch) {
                    produce_date = ""
                    batch_no = ""
                    batch_id = ""
                }
                let onlyNoBatch = false
                if (batch_id == "0") {
                   onlyNoBatch = true
                }
               var startDay = "2021-01-01 00:00";
               var myDate = new Date();
               var endDay = myDate.getFullYear() + "-" + (myDate.getMonth() + 1) + "-" + myDate.getDate() + " 23:59";
               var transfer_status = "untransfered";
               var move_status = "move_f";
               var sheetType = $('#sheetType').val();
                var isConfinement = false;
               if (args.datafield == "sell_pend_qty" && item_id) {
                    if (args.row.bounddata.sell_pend_qty) {
                        var url = `Report/SalesDetail?sheetType=xd&item_id=${item_id}&item_name=${encodeURIComponent(item_name)}&startDay=${startDay}&endDay=${endDay}&transfer_status=${transfer_status}&move_stock=${move_status}`;
                        if (branch_id) url += `&branch_id=${branch_id}&branch_name=${encodeURIComponent(branch_name)}&branch_position=${branch_position}&branch_position_name=${branch_position_name}&produce_date=${produce_date}&batch_no=${batch_no}`;
                        else url += `&produce_date=${produce_date}&batch_no=${batch_no}`;
                        if (produce_date === "") isConfinement = true;
                            url += `&isConfinement=${isConfinement}`;
                        debugger
                            window.parent.newTabPage('订单明细表', `${url}`);
                   }
               } 
               if (args.datafield == "item_name" && item_id) {
                       var url = `Report/StockChangeSum?&item_id=${item_id}&item_name=${encodeURIComponent(item_name)}`;
                        if (branch_id) url += `&branch_id=${branch_id}&branch_name=${encodeURIComponent(branch_name)}`;
                        if (branch_position) url += `&branch_position=${branch_position}`;
                        if (branch_position_name) url += `&branch_position_name=${branch_position_name}`;
                        if (onlyNoBatch) {
                           url += `&onlyNoBatch=${onlyNoBatch}`;
                        }
                        else {
                            url += `&produce_date=${produce_date}&batch_no=${batch_no}`;
                        }
                        window.parent.newTabPage('库存变化表', `${url}`);

               }                 
            });
           // $('#gridItems').jqxGrid({ statusbarheight: 34, columnsheight: 30 });
            $('#item_id').jqxInput({
                onButtonClick: function (event) {
                    $('#popItem').jqxWindow('open');
                    $("#popItem").jqxWindow('setContent', `<iframe src="/BaseInfo/ItemsView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                }
            });
            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 1000, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
 
            QueryData();
        });

        function fixOtherClassSize() {
            var height = $(window).height() - $('#divHead').height() - 60;
            $('#other_class').height(height);
        }
        window.addEventListener('load', fixOtherClassSize);
        window.addEventListener('resize', fixOtherClassSize);

        window.addEventListener('message', function (rs) {
            if (rs.data.msgHead === "ItemsView") {
                if (rs.data.action === "selectMulti") {
                    if (rs.data.checkedRows.length == 1) {
                        var item_id = rs.data.checkedRows[0].item_id;
                        var item_name = rs.data.checkedRows[0].item_name;
                    }
                    
                    var rows = rs.data.checkedRows
                    var items_id = ''
                    var items_name =''
                    rows.forEach(function (row) {
                        if (items_id != '') items_id += ','
                        if (items_name != '') items_name += ','
                        items_id += row.item_id
                        items_name += row.item_name
                    })
                    $('#item_id').jqxInput('val', { value: items_id, label: items_name });

                    $.ajax({
                        url: '/api/SaleSheet/GetItemInfo',
                        type: 'GET',
                        contentType: 'application/json',
                        data: { operKey: g_operKey, item_id: items_id },
                        success: function (data) {
                            if (data.result === 'OK') {
                                if (!window.g_queriedItems) window.g_queriedItems = {};
                                window.g_queriedItems[item_id] = data.item;
                            }
                        }
                    });
                }

                $('#popItem').jqxWindow('close');
            }

        })

        function beforeQuery() {
            console.log("123")
            debugger
            var multi = $('#showMultiBranch').val()
            if (multi) {
                 $('#gridItems').jqxGrid('showcolumn', 'branch_name')
            }
            else
                 $('#gridItems').jqxGrid('hidecolumn', 'branch_name')

            
        }
        
        function itemNameRender(row, column, value, p4, p5, rowData) {
            let stoped = rowData.status=='0'
            var span=''
            if (stoped) {
                span='<span style="color:#f66;">停用<span>'
            }
            return `<div style="padding-left:3px;height:100%;width:100%;display:flex;align-items:center;cursor:pointer;color:#49f;">${span}${value}</div>`
             

        }
        function viewProduceDate(row, column, value, p4, p5, rowData) {
            let showBatch = $("#showMultiBatch").val().toString().toLowerCase() == "true"?true:false;
            if (!rowData.item_id) {
                return ''
            } else if (showBatch) {
                let cellValue = value
                return `<div style="height:100%;width:100%;display: flex;align-items:center;justify-content:center;">${cellValue ? cellValue:'无产期'}</div>`
            } else {
                let cellValue = value ? JSON.parse(value) : []
                if (cellValue.length == 0) return ""
                if (cellValue.length == 1) {
                    let e = cellValue[0]
                    if (!e.produce_date) return `<div style="height:100%;width:100%;display: flex;align-items:center;justify-content:center;">无产期</div>`
                    else {
                        return `<div style="height:100%;width:100%;display: flex;align-items:center;justify-content:center;">${e.produce_date}</div>`
                    }
                }
                if (cellValue.length > 1) return `<div onclick='showBatchDetail(${value})' style="height:100%;display:flex;align-items:center;justify-content:center;color:#4499ff;" >查看</div>`
            }
            
        }
        function viewBatchNo(row, column, value, p4, p5, rowData) {
            let showBatch = $("#showMultiBatch").val().toString().toLowerCase() == "true" ? true : false;
            if (showBatch) {
                let cellValue = value
                return `<div style="height:100%;width:100%;display: flex;align-items:center;justify-content:center;">${cellValue}</div>`
            } else {
                console.log('lll', value)
                //判断是否为正确JSON格式
                if (typeof value === 'string' && value.trim().startsWith('{') && value.trim().endsWith('}')) {
                    try {
                        cellValue = JSON.parse(value);
                    } catch (error) {
                        console.error("Parsing error:", error);
                        cellValue = [];
                    }
                } else {
                    cellValue = [];
                }
                if (cellValue.length == 0) return ""
                if (cellValue.length == 1) {
                    let e = cellValue[0]
                    if (!e.batch_no) return ""
                    else {
                        return `<div style="height:100%;width:100%;display: flex;align-items:center;justify-content:center;">${e.batch_no}</div>`
                    }
                }
                if (cellValue.length > 1) return `<div onclick='showBatchDetail(${value})' style="height:100%;display:flex;align-items:center;justify-content:center;color:#4499ff;" >查看</div>`
            }
        }
        var cellsrenderer = function (row, columnfield, value, defaulthtml, columnproperties) {
            // return '<div style="height:100%;display:flex;align-items:center;justify-content:center;color: #4499ff;">' + value + '</div>';
            return '<div style="height:100%;display:flex;align-items:center;justify-content:center">' + value + '</div>';
        }
        function showBatchDetail(cellValue) {
            let gridData = []
            cellValue.forEach(cell => {
                if (!cell.stock_qty || !cell.sel_pend_qty) { 
                    gridData.push({
                        produce_date: cell.produce_date ? cell.produce_date : "无产期",
                        batch_no: cell.produce_date ? "" : "无批次",
                        stock_qty: cell.stock_qty,
                        sell_pend_qty: cell.sell_pend_qty,
                    })
                }
            })
            var source =
            {
                localdata: gridData,
                datatype: "array"
            };

            let columns = [
                {
                    text:"生产日期",
                    datafield:"produce_date",
                    width:100,
                },
                {
                    text: "批次",
                    datafield: "batch_no",
                    width: 100,
                },
                {
                    text: "实际库存(小)",
                    datafield: "stock_qty",
                    width: 100,
                    cellsrenderer: cellsrenderer,
                },
                {
                    text: "占用库存(小)",
                    datafield: "sell_pend_qty",
                    width: 100,
                    cellsrenderer: cellsrenderer,
                },
            ]
            var dataAdapter = new $.jqx.dataAdapter(source);
            $('#batchDetailGrid').jqxGrid({
                source: dataAdapter,
                columns: columns,
                width: "100%",
                height: "100%"
            })
            $('#popBatch').jqxWindow('open');


        }
    </script>
</head>

<body style="overflow:hidden;">
    
    <div style="display:flex;justify-content:space-around;margin-top:20px;">
        <div id="divHead" class="headtail">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>

        <button onclick="QueryData()" style="margin-left:20px;">查询</button>
        <button id="btnExport" onclick="ExportExcel()" style="margin-left:20px;">导出</button>
    </div>
    <!--
     <div id='other_class' style="position:fixed; width:200px;height:calc(100% - 90px);margin-top:20px;margin-bottom:2px;overflow-y:scroll">
            </div> 
    <div id="gridItems" style="margin-top:0px;margin-bottom:2px;width:calc(100% - 10px);height:calc(100% - 0px);"></div>
    -->
    <div style="display: flex;  flex: 1;">
        <!-- 现已使用fixOtherClassSize函数实时计算other_class高度，见上方代码 -->
        <div id='other_class' style="width: 200px; margin-top: 20px; margin-bottom: 2px; overflow-y: scroll">
        </div>

        <div style="width:calc(100% - 200px);height:auto; margin-left:10px;">

            <div>
                <div style="float:right;margin-right:50px;height:20px;font-size:12px;color:#999;">共<label id="rows_count">0</label>行</div>
            </div>
            <div id="gridItems" style="margin-top:0px;margin-bottom:2px;width:calc(100% - 10px);height:calc(100% - 20px);"></div>

        </div>


    </div>
    <div style="display:flex;height:20px;width:100%;margin-bottom:0px;"></div>

    <div id="popItem" style="display:none;">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">单位信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    <div id="popBatch" style="display:none">
        <div id="batchCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">产期信息</span></div>
        <div id="batchBox" style="width:100%;height:calc(100%-30px);box-sizing:border-box;padding:20px;overflow:hidden;">
            <div id="batchDetailGrid">
            </div>
        </div>
    </div>


</body>
</html>