﻿function onPageReady(sheetRows) {
    mmSheetInit();
    function checkBatchForSheetRows(sheetRows) {
        let needBatchOnReview = false
        if (window.g_companySetting && window.g_companySetting.needBatchOnReview && window.g_companySetting.needBatchOnReview == "True") {
            needBatchOnReview = true
        }
        if (!needBatchOnReview) return
        let flag = sheetRows.some((sheetRow, index) => {
            if (sheetRow.batch_level && !sheetRow.produce_date) {
                bw.toast(`您开启了复核时检查产期，请输入第${index + 1}行商品的生产日期`)
                return true
            }
            if (sheetRow.batch_level == "2" && !sheetRow.batch_no) {
                bw.toast(`您开启了复核时检查产期，请输入第${index + 1}行商品的批次`)
                return true
            }
        })
        return flag
    }
    function renderCopySheet() {
        if (requestString('copy')) {
            var params = paramsFromSrcWindow
            if ((params.sheet_type).indexOf('BUY') != -1) params.supcust_id = "";
            /*AddItemRows(0, params.supcust_id, params.branch_id, params.SheetRows, true)
            $('#no_disc_amount').jqxInput('val', toDecimal(params.no_disc_amount));
            $('#now_disc_amount').jqxInput('val', toDecimal(params.now_disc_amount));
            $('#total_amount').jqxInput('val', toDecimal(params.total_amount));*/

            GetRowsByCopyFromSheets(params)

        }
    }

    if (window.g_companySetting) {
        if (window.g_companySetting.sheetShowBarcodeStyle == '2') {
            document.all.ckPrintSmallBarcode.checked = true
        }
    }
    let supcustWhenFocus
    //var supcustIdWhenFocus=''
    $('#supcust_id>input').on('focus', function () {
        supcustWhenFocus = $(this).val()
        //if (supcustWhenFocus.v) supcustWhenFocus = v;
        // supcustWhenFocus=''

    })
    $('#supcust_id').on('optionSelected', function (a, b) {

        let oldValue = supcustWhenFocus
        var oldSupcustID = supcustWhenFocus.value || ''

       
        const branch_id = $('#branch_id').jqxInput('val').value
       
         
        let sheetType = $('#sheet_type').val();
        var supcust_id = $('#supcust_id').val().v

        getClientAccountInfo(supcust_id, true)

        if (sheetType =='SHEET_SALE_DD') {
            SetBranchAndSellerOnSupcustUpdate(supcust_id, rememberBranch, saleAndOrderRemeberSeller)
        }

        if (sheetType == 'SHEET_SALE_DD' && $('#jqxgrid').jqxGrid('getrows').filter(r => r.item_id != '' && r.item_id != undefined).length > 0 && oldSupcustID != supcust_id) {
            $('#cancle').click(function () {
                $('#changeClientAlertBox').hide();
                $('#supcust_id').jqxInput('val', { value: oldValue.value, label: oldValue.label })

            })
            $('#confirm').click(function () {
                //SetBranchAndSellerOnSupcustUpdate(value.v, saleRememberBranch, saleAndOrderRemeberSeller)
                let refreshFlag = $("input[name='changeClient']:checked").val()
               
               // getClientAccountInfo(id, true)
                if (refreshFlag == 'true') {

                  //  const branch_id = $('#branch_id').jqxInput('val').value
                    $('#changeClientAlertBox').hide();
                    refreshRowsBySupChange(supcust_id, branch_id)
                } else {
                    $('#changeClientAlertBox').hide();

                }
                $('#confirm').off('click')
            })
            $('#changeClientAlertBox').show()

        } else {
          
           // getClientAccountInfo(id, true)
           // const branch_id = $('#branch_id').jqxInput('val').value
            refreshRowsBySupChange(supcust_id, branch_id)
        }
    })
    
    //let sheetType = $("#sheetType").val()
    //if ((sheetType == "T" || sheetType == "TD") && window.g_companySetting && window.g_companySetting.backBranchPositionType) {
    //    $('#branch_id').on('change', function (a, b) {
    //        var rowsData = $("#jqxgrid").jqxGrid('getrows')
    //        let rowindexs = []
    //        for (let i = 0; i < rowsData.length; i++) {
    //            let row = rowsData[i]
    //            if (!row.item_id || row.branch_id) continue
    //            rowindexs.push(i)
    //        }
    //        var branch_id = $('#branch_id').val().value
    //        branch_id = branch_id ? branch_id : "-1"
    //        var type_id = window.g_companySetting.backBranchPositionType
    //        if (rowindexs.length) getBranchPositionForReturn(rowindexs, branch_id, type_id)
    //    })
    //}
    /*
    $('#supcust_id').jqxInput({
        onButtonClick: function (event) {
            $('#popClient').jqxWindow('open');
            $("#popClient").jqxWindow('setContent', `<iframe src="/BaseInfo/ClientsView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
        }
    });

    $("#popClient").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 900, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
    $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 900, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
    */
     
    var approve_time = $('#approve_time').text();
    var canRedAndChange = window.getRightValue('sale.sheetSaleOrder.red')
    debugger
    if (canRedAndChange != "true") $('#btnRedAndChange').css('display', 'none');  
    if (approve_time) {
        $('#btnApprove').attr('disabled', true);
        $("#btnSave").attr('disabled', true);
        var red_flag = $('#red_flag').val()
        if (red_flag) {
            $("#btnRedAndChange").attr('disabled', true);
        }
    }else {
        $("#btnRedAndChange").attr('disabled', true);
    }
    $('#no_disc_amount').jqxInput('disabled', true);
    
 
    var theme = "";

    var datafields = [];
    var data = new Array; var rowscount = 10;

    var source =
    {
        sort: funcSortByColumn,
        localdata: data,
        unboundmode: true,
        totalrecords: 10,
        datafields: datafields,
        updaterow: function (rowid, rowdata) {
        }
    };

    var dataAdapter = new $.jqx.dataAdapter(source);


    var fixColCss = 'jqx-widget-header';
    if (theme !== '') fixColCss += ' jqx-widget-header-' + theme;


    var canSeeBuyPrice = false;
    var canSeeProfit = true;
    var canSeeSalePrice = true;

    var saleAndOrderRemeberSeller = true;
    var rememberBranch = false;
    if (window.g_operRights.delicacy) {
        if (window.g_operRights.delicacy.seeInPrice
            && window.g_operRights.delicacy.seeInPrice.value) {
            canSeeBuyPrice = true;
        }
        if (window.g_operRights.delicacy.seeProfit
            && window.g_operRights.delicacy.seeProfit.value) {
            canSeeProfit = true;
        }
        if (window.g_operRights.delicacy.seeSalePrice && !window.g_operRights.delicacy.seeSalePrice.value) {
            canSeeSalePrice = false;
        }
        if (window.g_operRights.delicacy.saleAndOrderRemeberSeller && !window.g_operRights.delicacy.saleAndOrderRemeberSeller.value) {
            saleAndOrderRemeberSeller = false;
        }
        if (window.g_operRights.delicacy.saleOrderDefaultBranch && window.g_operRights.delicacy.saleOrderDefaultBranch.value && window.g_operRights.delicacy.saleOrderDefaultBranch.value=='lasttime') {
            rememberBranch = true;
        }
    }

    var sheetShowPriceList = true
    if (window.g_companySetting && window.g_companySetting.sheetShowPriceList && window.g_companySetting.sheetShowPriceList.toLowerCase() == 'false')
        sheetShowPriceList = false;

    window.GridData = {
        source: dataAdapter,
        showaggregates: true,
        showstatusbar: true,
        columnsheight: 36,
        rowsheight: 36,
        statusbarheight: 30,
        pageable: false,
        // autoheight: true,
        sortable: true,
        editable: true,
        columnsresize: true,
        ready: function () {
            $("#jqxgrid").jqxGrid('focus');
        },
        renderstatusbar: function (statusbar) {
        },
        editmode: 'click',// 'selectedcell',
        selectionmode: 'multiplecellsadvanced',//'singlecell',// 
        hoverrow: true,
        theme: theme,
        cellhover: cellhover,
        handlekeyboardnavigation: handlekeyboardnavigation,
        columns: [
            {
                text: '', sortable: false, filterable: false, editable: false, pinned: true,
                groupable: false, draggable: false, resizable: false,
                datafield: '', columntype: 'number', width: 45,
                cellclassname: fixColCss,
                cellsrenderer: pinCellsRenderer,
                renderer: leftTopCellRenderer
            },
            {
                text: '仓库',
                sortable: true,
                hidden: true,
                datafield: 'branch_id',
                displayfield: 'branch_name',
                width: 150, align: 'center',
                columntype: 'template',
                createeditor: createeditor_branch_name,
                initeditor: initeditor_branch_name,
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val();
                    return v;
                },
                cellbeginedit: function (row, datafield, columntype, value) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    if (!item_id) return false;
                },
            },
            {
                text: '库位',
                sortable: true,
                hidden: true,
                datafield: 'branch_position',
                displayfield: 'branch_position_name',
                width: 150, align: 'center',
                columntype: 'template',
                createeditor: createeditor_branch_position_name,
                initeditor: initeditor_branch_position_name,
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val();
                    return v;
                },
                cellbeginedit: function (row, datafield, columntype, value) {
                    var branch_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'branch_id');
                    if (datafield === "branch_position" && !branch_id) return false;
                },
                cellbeginedit: function (row, datafield, columntype, value) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var row_branch_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'branch_id');
                    var branch_id = $('#branch_id').jqxInput('val').value
                    if ((!row_branch_id && !branch_id) || !item_id) return false;
                },
            },

            //{
            //    text: '客户编号',
            //    datafield: 'supcust_id',
            //    width: '70', align: 'center', cellsalign: 'center', hidden: false,
            //    cellbeginedit: function (row, datafield, columntype, value) {
            //        return false;
            //    }
            //},

            {
                text: '商品名称', datafield: 'item_id', displayfield: 'item_name', width:'200', columntype: 'template',alwaysShow:true,
                createeditor: createeditor_item_name,
                align: 'center',
                initeditor: initeditor_item_name,
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val();
                    return v;
                },
                cellsrenderer: function (row, column, value, p4, p5, rowData) {
                    var order_sub_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'order_sub_id');
                    var disp_flow_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'disp_flow_id');
                    var trade_type = $('#jqxgrid').jqxGrid('getcellvalue', row, 'trade_type');
                    var order_item_sheets_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'order_item_sheets_id');
                    var order_item_sheets_no = $('#jqxgrid').jqxGrid('getcellvalue', row, 'order_item_sheets_no');
                    if (order_item_sheets_id && order_item_sheets_no) {
                        order_item_sheets_id = order_item_sheets_id.replace(',,', ',')
                        order_item_sheets_no = order_item_sheets_no.replace(',,', ',')
                        var arr = order_item_sheets_no.split(',')
                        order_item_sheets_no = ''
                        arr.forEach(sheet_no => {
                            if (sheet_no) {
                                if (order_item_sheets_no) order_item_sheets_no += ','
                                order_item_sheets_no += sheet_no
                            }
                        })
                    }
                    var attrOptNames = ''
                    if (rowData.attr_qty) {
                        if (!Array.isArray(rowData.attr_qty))
                            rowData.attr_qty = JSON.parse(rowData.attr_qty)

                        rowData.attr_qty.forEach(opt => {

                            var optName = ''
                            for (var propName in opt) {
                                if (propName.indexOf('optName_') == 0) {
                                    var optName = opt[propName]
                                    break
                                }
                            }
                            if (rowData.attr_qty.length > 1) {
                                optName += '(' + opt.qty + ')'
                            }
                            attrOptNames += optName

                        })
                    }
                    var lblAttrOpt = ''
                    if (attrOptNames) {
                        lblAttrOpt =
                            `<label style ="margin-left:10px;background:white;color:#333388;border-radius:5px;font-size:14px;text-align:center;line-height:20px" >${attrOptNames}</label>`;

                    }


                    var label = '';
                    if (order_sub_id) {
                        label =
                            `<label style ="margin-left:4px;background:#e6214a;color:white;border-radius:5px;width:16px;font-size:10px;text-align:center;line-height:17px" >定 </label>`;
                        label += `<label style="color:#88f;cursor:pointer;">${order_item_sheets_no} </label>`
                    }

                    if (disp_flow_id)
                        label =
                            '<label style ="margin-left:4px;background:#e6214a;color:white;border-radius:5px;width:30px;font-size:10px;text-align:center;line-height:17px" >陈列</label>';
                    if (trade_type === 'J')
                        label =
                            '<label style ="margin-left:4px;background:#e6214a;color:white;border-radius:5px;width:16px;font-size:10px;text-align:center;line-height:17px" >借</label>';
                    if (trade_type === 'H')
                        label =
                            '<label style ="margin-left:4px;background:#e6214a;color:white;border-radius:5px;width:16px;font-size:10px;text-align:center;line-height:17px" >还</label>';
                    if (order_sub_id || disp_flow_id || trade_type || attrOptNames) {
                        var div =
                            `<div style = "height:100%;display:flex;align-items:center;"><label style="text-align:left;margin-left:4px">${value}</label>${lblAttrOpt}${label}</div>`
                        return div;

                    }
                },
            },
        
            {
                text: '商品编号', datafield: 'item_no', width: '150', align: 'center', cellsalign: 'center', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '规格', datafield: 'item_spec', width: '60',
                align: 'center', cellsalign: 'center', hidden: false
            },
            {
                text: '别名',
                datafield: 'item_alias',
                width: '60',
                align: 'center',
                cellsalign: 'center',
                hidden: true
            },
            {
                text: '商品备注',
                datafield: 'item_remark',
                width: '60',
                align: 'center',
                cellsalign: 'center',
                hidden: true
            },
            {
                text: '保质期', datafield: 'valid_days', width: '80',
                align: 'center', cellsalign: 'center', hidden: false
            },
            {
                text: '单位', datafield: 'unit_no', width: '70', align: 'center', cellsalign: 'center', columntype: 'template', alwaysShow: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var disp_flow_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'disp_flow_id');
                    if ((datafield === "unit_no" && !item_id) || disp_flow_id) return false;
                },
                createeditor: createeditor_unit_no,
                initeditor: initeditor_unit_no,
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val(); return v;
                }
            },
            {
                text: '包装率', datafield: 'unit_factor', width: '80', align: 'center', cellsalign: 'center', alwaysShow: false,
                cellbeginedit: function (row, datafield, columntype, value) {
                    if (datafield === "unit_factor") return false;
                }
            },
            {
                text: '单位换算', datafield: 'unit_relation1', width: '100', align: 'center', cellsalign: 'center', alwaysShow: false, hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '条码', datafield: 'barcode', width: '150', align: 'center', cellsalign: 'center',
                cellbeginedit: function (row, datafield, columntype, value) {
                   return true;
                },
                cellsrenderer: function(row, column, value, p4, p5, rowData) {
                    let attrOptNames = '';
                    rowData.barcode = rowData.barcode || ""
                    if (rowData.barcode.indexOf("主商品(") != -1) {
                        rowData.barcode = rowData.barcode.substring(rowData.barcode.indexOf("主商品(") + 4, rowData.barcode.indexOf(")"))
                    }
                    if (isNaN(rowData.barcode)) {
                        rowData.barcode = ""
                    }
                    if (rowData.attr_qty && !rowData.son_mum_item) {
                        if (rowData.barcode != "" && rowData.attr_qty.length != 0) {
                            rowData.barcode = `主商品(${rowData.barcode})`
                        }
                        if (typeof rowData.attr_qty === 'string') {
                            rowData.attr_qty = JSON.parse(rowData.attr_qty);
                        }
                        rowData.attr_qty.forEach(opt => {
                            for (const propName in opt) {
                                if (propName.startsWith('optName_') && Number(opt.qty) !== 0) {
                                    switch (rowData.unit_no) {
                                        case rowData.b_unit_no:
                                            code = opt.bBarcode;
                                            break;
                                        case rowData.m_unit_no:
                                            code = opt.mBarcode;
                                            break;
                                        case rowData.s_unit_no:
                                            code = opt.sBarcode;
                                            break;
                                    }
                                    if (code)
                                        attrOptNames += `${opt[propName]}(${code})`;
                                    break;
                                }
                            }
                        });
                        rowData.barcode += attrOptNames;
                    }

                    const lblAttrOpt = attrOptNames ?
                        `<label style="margin-left:10px;background:white;color:#333388;border-radius:5px;font-size:14px;text-align:center;line-height:20px">${attrOptNames}</label>` :
                        '';
                    const div = `
    <div style="height:100%;display:flex;align-items:center;">
        <label style="text-align:left;margin-left:4px">${rowData.barcode}</label>
        
    </div>
`;
                    return div;
                }
            },
            {
                text: '条码(小)', datafield: 's_barcode', width: '150', align: 'center', cellsalign: 'center', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return true;
                },
                cellsrenderer: function(row, column, value, p4, p5, rowData) {
                    let attrOptNames = '';
                    rowData.s_barcode = rowData.s_barcode || ""
                    if (rowData.s_barcode.indexOf("主商品(") != -1) {
                        rowData.s_barcode = rowData.s_barcode.substring(rowData.s_barcode.indexOf("主商品(") + 4, rowData.s_barcode.indexOf(")"))
                    }
                    if (isNaN(rowData.s_barcode)) {
                        rowData.s_barcode = ""
                    }
                    if (rowData.attr_qty && !rowData.son_mum_item) {
                        if (rowData.s_barcode != "" && rowData.attr_qty.length != 0) {
                            rowData.s_barcode = `主商品(${rowData.s_barcode})`
                        }
                        if (typeof rowData.attr_qty === 'string') {
                            rowData.attr_qty = JSON.parse(rowData.attr_qty);
                        }
                        rowData.attr_qty.forEach(opt => {
                            for (const propName in opt) {
                                if (propName.startsWith('optName_') && Number(opt.qty) !== 0) {
                                    if (opt.sBarcode)
                                        attrOptNames += `${opt[propName]}(${opt.sBarcode})`;
                                    break;
                                }
                            }
                        });
                        rowData.s_barcode += attrOptNames;
                    }
                    const lblAttrOpt = attrOptNames ?
                        `<label style="margin-left:10px;background:white;color:#333388;border-radius:5px;font-size:14px;text-align:center;line-height:20px">${attrOptNames}</label>` :
                        '';
                    const div = `
    <div style="height:100%;display:flex;align-items:center;">
        <label style="text-align:left;margin-left:4px">${rowData.s_barcode}</label>
    </div>
`;
                    return div;
                }
            },
            {
                text: '虚拟产期',
                sortable: false,
                datafield: 'virtual_produce_date',
                width: '100px',
                align: 'center',
                hidden: true,
                cellsalign: 'center',
                cellbeginedit: function (row, datafield, columntype, value, c, d, e) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    if (datafield === "virtual_produce_date" && !item_id) return false;
                }
            },
            {
                text: '生产日期', datafield: 'batch_id', displayfield: "produce_date", width: '150', align: 'center', cellsalign: 'center', columntype: 'template', hidden: true,
                createeditor: createeditor_produce_date,
                initeditor: initeditor_produce_date,
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val()
                    return v;
                },
                cellsrenderer: function (row, columnfield, value) {
                    var rowsData = $("#jqxgrid").jqxGrid('getrows')
                    var rowData = rowsData[row]
                    if (rowData.batch_level == "" || rowData.batch_level == "0") value = "无产期"
                    return '<div style="line-height:37px;text-align:center;">' + value + '</div>';
                },
                cellbeginedit: function (row, datafield, columntype, value) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var rowsData = $("#jqxgrid").jqxGrid('getrows')
                    var rowData = rowsData[row]
                    if ((datafield === "batch_id" && !item_id) || rowData.batch_level == "" || rowData.batch_level == "0") return false;

                },
            },
            {
                text: '批次', datafield: 'batch_id_f', displayfield: 'batch_no', width: '80', align: 'center', cellsalign: 'center', columntype: 'template', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var rowsData = $("#jqxgrid").jqxGrid('getrows')
                    var rowData = rowsData[row]
                    if ((datafield === "batch_id_f" && !item_id) || rowData.batch_level !== "2") return false;
                },
                createeditor: createeditor_batch_no,
                initeditor: initeditor_batch_no,
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val(); return v;
                }

            },
            {
                text: '交易类型', datafield: 'trade_type', displayfield: 'trade_type_name', width: '120', align: 'center', columntype: 'template', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var disp_flow_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'disp_flow_id');
                    var order_sub_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'order_sub_id');
                    if ((datafield === "trade_type" && !item_id) || disp_flow_id || order_sub_id) return false;
                },
                createeditor: createeditor_trade_type,
                initeditor: initeditor_trade_type,
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val();
                    return v;
                }
            },

            /*{
                text: '单据用途',
                datafield: 'sheet_usage',
                width: '120',
                align: 'center',
                cellsalign: 'center',

            },*/

            {
                text: '数量', datafield: 'quantity', width: '80', alwaysShow: true,
                cellsrenderer: cellsrenderer_quantity,
                align: 'center', cellsalign: 'right',
                aggregates: aggregates_quantity,
                aggregatesrenderer: aggregatesrenderer_quantity,
                geteditorvalue: geteditorvalue_quantity,
                cellbeginedit: function (row, datafield, columntype, value) {
                    var gridRow = $('#jqxgrid').jqxGrid('getrows')[row]
                    var item_id = gridRow.item_id
                    if (!item_id) return false
                    if (gridRow.mum_attributes) {
                        if (gridRow.mum_attributes.find(attr => attr.distinctStock))
                            return false
                        if (gridRow.mum_attributes.find(attr => !attr.distinctStock)) {
                            if (gridRow.attr_qty && gridRow.attr_qty.length > 0) {
                                return false
                            }
                        } 
                    }

                }
            },
            {
                text: '辅助数量',
                datafield: 'quantity_unit_conv',
                width: '120',
                hidden: true,
                cellsrenderer: cellsrenderer_multi_qty,
                columntype: 'template',
                align: 'center',
                cellsalign: 'right',
                //aggregates: aggregates_quantity,
                //aggregatesrenderer: aggregatesrenderer_quantity,
                createeditor: createeditor_multi_qty,
                initeditor: initeditor_multi_qty,
                geteditorvalue: geteditorvalue_multi_qty,
                cellbeginedit: function (row, datafield, columntype, value) {
                    var gridRow = $('#jqxgrid').jqxGrid('getrows')[row]
                    var item_id = gridRow.item_id
                    if (!item_id) return false
                    if (gridRow.mum_attributes) {
                        if (gridRow.mum_attributes.find(attr => attr.distinctStock))
                            return false
                        if (gridRow.mum_attributes.find(attr => !attr.distinctStock)) {
                            if (gridRow.attr_qty && gridRow.attr_qty.length > 0) {
                                return false
                            }
                        }
                    }

                }
            },

            {
                text: '原价', datafield: 'orig_price', width: '80', align: 'center', cellsalign: 'right',
                hideOnLoad: !canSeeSalePrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    var order_sub_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'order_sub_id');
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    if (!item_id) return false;
                   // else if (order_sub_id && item_id) return false;
                }

            },
            {
                text: '原价金额', datafield: 'orig_amount', width: '120', align: 'center', cellsalign: 'right', hidden: true,
                hideOnLoad: !canSeeSalePrice,
                aggregates: aggregates_sub_amount,
                aggregatesrenderer: aggregatesrenderer_sub_amount
            },
            { text: '系统价格', datafield: 'sys_price', width: '120', align: 'center', cellsalign: 'right', hidden: true, hideOnLoad: !canSeeSalePrice, },
            {
                text: '折扣(%)', datafield: 'discount', width: '100', align: 'center', cellsalign: 'right',
                cellbeginedit: function (row, datafield, columntype, value) {
                    var order_sub_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'order_sub_id');
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    if (!item_id) return false;
                    if (order_sub_id) return false;
                }
            },
            {
                text: '价格',
                datafield: 'real_price',
                width: '80',
                align: 'center',
                cellsalign: 'right',
                columntype: sheetShowPriceList ? 'template' : undefined,
                createeditor: sheetShowPriceList ? createeditor_real_price : undefined,
                initeditor: sheetShowPriceList ? initeditor_real_price : undefined,
                geteditorvalue: sheetShowPriceList ? function (row, cellvalue, editor) {
                    var v = editor.find('input').val();
                    if (v.label && !v.value) {
                        v.value = v.label.toString()
                    }

                    return v;
                } : undefined,
                alwaysShow: canSeeSalePrice,
                hidden: !canSeeSalePrice,
                hideOnLoad: !canSeeSalePrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    var order_sub_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'order_sub_id');
                    var disp_flow_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'disp_flow_id');
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var trade_type = $('#jqxgrid').jqxGrid('getcellvalue', row, 'trade_type');
                    if (!item_id) return false;
                    //if (order_sub_id || disp_flow_id || (trade_type) return false;
                    if (order_sub_id || disp_flow_id || (trade_type === 'J' || trade_type === 'H')) return false;


                }, 
                cellsrenderer: function (row, column, value, p4, p5, rowData) {
                    var sys_price = rowData.sys_price;
                    var real_price = rowData.real_price;
                    if (rowData.item_id) {
                        if (toMoney(sys_price || 0) != toMoney(real_price || 0)) {
                            var div =
                                `<div onmouseenter='onMouseEnterRealPrice(event,${row},${sys_price
                                })' onmousedown='onMouseLeaveRealPrice()' onmouseleave='onMouseLeaveRealPrice()'  style = "height:100%;margin-right:6px;color:#f00;text-align:right;line-height:28px;">${toMoneyText(value,4,2)}</div>`
                            return div;
                        }
                    }

                },

            },
            {
                text: '上次售价',
                datafield: 'last_time_price',
                width: '120',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeSalePrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '小单位价',
                datafield: 's_real_price',
                width: '120',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeSalePrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    //var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    //if (!item_id) return false;
                    var order_sub_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'order_sub_id');
                    var disp_flow_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'disp_flow_id');
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var trade_type = $('#jqxgrid').jqxGrid('getcellvalue', row, 'trade_type');
                    if (!item_id) return false;
                    //if (order_sub_id || disp_flow_id || (trade_type) return false;
                    if (order_sub_id || disp_flow_id || (trade_type === 'J' || trade_type === 'H')) return false;

                },
            },
            {
                text: '中单位价',
                datafield: 'm_real_price',
                width: '100',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeSalePrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    var rows = $('#jqxgrid').jqxGrid('getrows');
                    var curRow = rows[row]
                    if (!curRow.item_id) return false
                    if (!curRow.m_unit_no) return false
                    var order_sub_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'order_sub_id');
                    var disp_flow_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'disp_flow_id');
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var trade_type = $('#jqxgrid').jqxGrid('getcellvalue', row, 'trade_type');
                    if (!item_id) return false;
                    //if (order_sub_id || disp_flow_id || (trade_type) return false;
                    if (order_sub_id || disp_flow_id || (trade_type === 'J' || trade_type === 'H')) return false;

                },

            },
            {
                text: '大单位价',
                datafield: 'b_real_price',
                width: '100',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeSalePrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    var rows = $('#jqxgrid').jqxGrid('getrows');
                    var curRow = rows[row]
                    if (!curRow.item_id) return false
                    if (!curRow.b_unit_no) return false
                    var order_sub_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'order_sub_id');
                    var disp_flow_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'disp_flow_id');
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var trade_type = $('#jqxgrid').jqxGrid('getcellvalue', row, 'trade_type');
                    if (!item_id) return false;
                    //if (order_sub_id || disp_flow_id || (trade_type) return false;
                    if (order_sub_id || disp_flow_id || (trade_type === 'J' || trade_type === 'H')) return false;
                },

            },
            {
                text: '金额', datafield: 'sub_amount', width: '90', align: 'center', cellsalign: 'right', alwaysShow: true,
                hideOnLoad: !canSeeSalePrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    var order_sub_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'order_sub_id');
                    var disp_flow_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'disp_flow_id');
                    var trade_type = $('#jqxgrid').jqxGrid('getcellvalue', row, 'trade_type');
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    if (!item_id) return false;
                    if (order_sub_id || disp_flow_id || (trade_type === 'J' || trade_type === 'H')) return false;
                },
                aggregates: aggregates_sub_amount,
                aggregatesrenderer: aggregatesrenderer_sub_amount
            },
            {
                text: '零售价', datafield: 'retail_price', width: '70', align: 'center', cellsalign: 'right', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },

            {
                text: '批发价', datafield: 'wholesale_price', width: '70', align: 'center', cellsalign: 'right', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '最近零售价',
                datafield: 'recent_retail_price',
                width: '90',
                align: 'center',
                cellsalign: 'right',
                columntype: 'template',
                createeditor: create_recent_retail_price_editor,
                initeditor: init_editor,
                geteditorvalue: function (row, cellvalue, editor) {

                    var v = editor.find('input').val();
                    if (v.label && !v.value) {
                        v.value = v.label.toString()
                    }
                    //if (v.label) v = v.label;
                    return v;
                },
                cellbeginedit: function (row, datafield, columntype, value) {

                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    if (!item_id) return false;
                    // return false;
                },

            },
            {
                text: '小单位零售价', datafield: 's_retail_price', width: '100', align: 'center', cellsalign: 'right',
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '成本价', datafield: 'cost_price', width: '80', align: 'center', cellsalign: 'right', hidden: true,
                hideOnLoad: !canSeeBuyPrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '预设进价',
                datafield: 'cost_price_buy',
                width: '100',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeBuyPrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '加权平均价',
                datafield: 'cost_price_avg',
                width: '120',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeBuyPrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                },
                 cellsrenderer:
                    function (row, columnfield, value, defaulthtml, columnproperties, rowData) {
                        var unit_factor = rowData.unit_factor
                        var cost_price_avg = rowData.cost_price_avg
                        if (cost_price_avg && unit_factor) {
                            var html = '<div class="jqx-grid-cell-right-align" style="margin-top: 10px;">' + (cost_price_avg * unit_factor) + '</div>';
                            return html;
                        }
                    }
            },
            {
                text: '最近平均进价',
                datafield: 'cost_price_recent',
                width: '120',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeBuyPrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                },
                 cellsrenderer: function (row, columnfield, value, defaulthtml, columnproperties, rowData) {
                    var unit_factor = rowData.unit_factor
                    var cost_price_recent = rowData.cost_price_recent
                    if (cost_price_recent && unit_factor) {
                        var html = '<div class="jqx-grid-cell-right-align" style="margin-top: 10px;">' + (cost_price_recent * unit_factor) + '</div>';
                        return html;
                    }
                } 
            },
            {
                text: '预设成本',
                datafield: 'cost_price_prop',
                width: '100',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeBuyPrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '成本金额', datafield: 'cost_amount', width: '100', align: 'center', cellsalign: 'right', hidden: true,
                hideOnLoad: !canSeeBuyPrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                },
                aggregates: aggregates_sub_amount,
                aggregatesrenderer: aggregatesrenderer_sub_amount
            },
            {
                text: '利润', datafield: 'profit', width: '100', align: 'center', cellsalign: 'right', hidden: true,
                hideOnLoad: !canSeeBuyPrice || !canSeeProfit,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                },
                aggregates: aggregates_sub_amount,
                aggregatesrenderer: aggregatesrenderer_sub_amount
            },
            {
                text: '利润率(%)', datafield: 'profit_rate', width: '100', align: 'center', cellsalign: 'right', hidden: true,
                hideOnLoad: !canSeeBuyPrice || !canSeeProfit,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                },
                aggregatesrenderer: aggregatesrenderer_sub_amount_profit_rate
            },
            {
                text: '可还数量',
                datafield: 'specific_qty_unit',
                width: '100',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    if (datafield === "specific_qty_unit") return false;

                }
            },
            {
                text: '可用还货数',
                datafield: 'order_qty',
                width: '100',
                align: 'center',
                cellsalign: 'center',
                
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '定货款账户',
                datafield: 'order_sub_name',
                align: 'center',
                cellsalign: 'center',
                hidden: true,
                hideOnLoad: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: 'order_item_sheets_id',
                datafield: 'order_item_sheets_id',
                align: 'center',
                cellsalign: 'center',
                hidden: true,
                hideOnLoad: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: 'order_item_sheets_no',
                datafield: 'order_item_sheets_no',
                align: 'center',
                cellsalign: 'center',
                hidden: true,
                hideOnLoad: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '备注', columntype: 'template', datafield: 'remark', width: '70', sortable: false, align: 'center',
                createeditor: function (row, cellvalue, editor, cellText, width, height) {
                    var element = $('<div></div >');
                    editor.append(element);
                    var inputElement = editor.find('div')[0];
                    var datafields = new Array({ datafield: 'remark', text: '', width: width });
                    $(inputElement).jqxInput({
                        height: height, width: width,
                        borderShape: 'none',
                        buttonUsage: 'list',
                        dropDownHeight: 160,
                        dropDownWidth: width,
                        displayMember: 'remark',
                        valueMember: 'remark',
                        datafields: datafields,
                        searchFields: ['remark'],
                        maxRecords: 9,
                        url: '/api/SaleSheet/GetSheetRemarks?sheetType=XD&operKey=' + window.g_operKey,
                        // source: []
                    });
                }, align: 'center',
                cellbeginedit: function (row, datafield, columntype, value) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    if (!item_id) return false;
                },
                initeditor: function (row, cellvalue, editor, celltext, pressedkey) {
                    var inputField = editor.find('input');
                    if (pressedkey) {
                        inputField.val(pressedkey);
                        inputField.jqxInput('selectLast');
                    }
                    else {
                        inputField.val({ value: cellvalue, label: celltext });
                        if (cellvalue == '') inputField.val('');
                        inputField.jqxInput('selectAll');
                    }
                },
                geteditorvalue: function (row, cellvalue, editor) {
                    var e = editor.find('input')
                    if (e.length > 0) {
                        var v = e[0]
                        return v.value
                    }
                    return '';
                }
            },
            {
                text: '库存', datafield: 'stock_qty_unit', width: '80', align: 'center', cellsalign: 'center',
                cellbeginedit: function (row, datafield, columntype, value) {
                    if (datafield === "stock_qty_unit") return false;
                }
            },
            {
                text: '占用库存', datafield: 'sell_pend_qty_unit', width: '100', align: 'center', cellsalign: 'center', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    if (datafield === "sell_pend_qty_unit") return false;
                }
            },
            
            {
                text: '可用库存', datafield: 'usable_stock_qty_unit', width: '100', align: 'center', cellsalign: 'center',
                cellbeginedit: function (row, datafield, columntype, value) {
                    if (datafield === "usable_stock_qty_unit") return false;
                }
            },
            { text: '重量(小)', datafield: 's_weight', hidden: true, hideOnLoad: true },
            { text: '重量(中)', datafield: 'm_weight', hidden: true, hideOnLoad: true },
            { text: '重量(大)', datafield: 'b_weight', hidden: true, hideOnLoad: true },
            {
                text: '单位重量', datafield: 'unit_weight', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '重量',
                datafield: 'weight',
                hidden: true,
                aggregates: aggregates_sub_amount,
                aggregatesrenderer: aggregatesrenderer_sub_amount,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            { text: '体积(小)', datafield: 's_volume', hidden: true, hideOnLoad: true },
            { text: '体积(中)', datafield: 'm_volume', hidden: true, hideOnLoad: true },
            { text: '体积(大)', datafield: 'b_volume', hidden: true, hideOnLoad: true },
            {
                text: '单位体积', datafield: 'unit_volume', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '体积',
                datafield: 'volume',
                hidden: true,
                aggregates: aggregates_sub_amount,
                aggregatesrenderer: aggregatesrenderer_sub_amount,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            { text: '陈列flow_id', datafield: 'disp_flow_id', hidden: true, hideOnLoad: true },
            { text: '陈列sheet_id', datafield: 'disp_sheet_id', hidden: true, hideOnLoad: true },
            { text: '陈列月', datafield: 'disp_month_id', hidden: true, hideOnLoad: true },
            { text: '定货款账户', datafield: 'order_sub_id', hidden: true, hideOnLoad: true },
            { text: '定货fow_id', datafield: 'order_flow_id', hidden: true, hideOnLoad: true },
            { text: '陈列月份', datafield: 'disp_month', hidden: true, hideOnLoad: true },
            { text: '陈列剩余', datafield: 'disp_left_qty', hidden: true, hideOnLoad: true },
            // 会员积分用
            {
                text: '是否为会员积分兑换商品', datafield: 'vip_is_redeem', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '消耗会员积分', datafield: 'vip_used_point', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '积分兑换单位', datafield: 'vip_redeem_unit', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '积分兑换价格', datafield: 'vip_redeem_price', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '积分兑换数量', datafield: 'vip_redeem_amount', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '积分兑换详情', datafield: 'vip_redeem_description', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '连锁删除ID', datafield: 'deleteLinkId', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
        ]
    }

    var payway2_amount = $('#payway2_amount').jqxInput('val');
    if (parseFloat(payway2_amount))
        onShowGroupBtnClicked('payway');
    adjustColumnsBySetting()
    $("#jqxgrid").jqxGrid(
        GridData
    );
    renderCopySheet()

    window.loadSheetData = function (sheetRows) {
        console.log("loadSheetData")
        console.log(sheetRows)
        initProduceDate(sheetRows)
        loadSheetRows(sheetRows);
        mmRefreshStockQty()
        ReturnItemsRender()
        var supcustID = $('#supcust_id').val().value;
        if (supcustID) getClientAccountInfo(supcustID)
        //使用定货会商品时，更新支付方式
        sheetRows.forEach((row) => {
            if (row.order_sub_id) {
                var payway1_id = $('#payway1_id').jqxInput('val')
                var payway1_name = payway1_id.label
                $('#payway1_id').jqxInput({ disabled: true })
                $('#payway1_amount').jqxInput({ disabled: true })
                $('#payway1_id .row-oper').remove()
                $('#payway1_amount .row-oper').remove()
                if (row.order_sub_name = payway1_name) $('#jqxgrid').jqxGrid('selectcell', row.index, 'order_sub_name');
            }

        })
    }
    loadSheetData(sheetRows)
    function loginTicketAccessSys(companySetting, callBack) {
        const data = {
            ticketAccessSysAccount: companySetting.ticketAccessSysAccount,
            ticketAccessSysPwd: companySetting.ticketAccessSysPwd,
            ticketAccessSysKey: companySetting.ticketAccessSysKey,
        }

        $.ajax({
            //modified by xiang wei
            //url: "@Startup.Localhost/AppApi/login/login",
            dataType: "json", //返回的数据类型
            contentType: 'application/json',
            url: `/AppApi/login/LoginTicketAccessSys`,
            data: JSON.stringify(data),
            type: "POST"
        }).then(res => {
            callBack(res)
        })
     }
     function initProduceDate(rows) {
         rows.forEach((row) => {
             if (row.item_id && row.batch_level && row.batch_level !== "0" && !row.produce_date) {
                 row.produce_date = "无产期"
                 row.batch_id = "0"
             }
         })
     }
 
    $("#jqxgrid").on('cellendedit', cellendedit);

    $("#btnSave").on('click', function () {
        //updateTheCostAmount();
        var res = GetSheetData();
        if (res.result === "Error") {
            bw.toast(res.msg, 5000); return;
        }
        var sheet = res.sheet;

        $.ajax({
            url: '/api/SaleOrderSheet/Save',
            type: 'POST',
            contentType: 'application/json',
            // data: JSON.stringify({sheet_id:'1',supcust_id:'2'}),
            data: JSON.stringify(sheet),
            success: function (data) {
                if (data.result === 'OK') {
                    $('#sheet_id').val(data.sheet_id);
                    $('#sheet_no').text(data.sheet_no);
                    updateSheetState();
                    removeSheetFromCach()
                    var canDelete = window.getRightValue('sale.sheetSaleOrder.delete')
                    if (canDelete && canDelete.toLowerCase() == "false") {
                        $('#btnDelete').css('display', 'none');
                    }
                    bw.toast('保存成功', 3000); 
                    if (window.g_companySetting) {
                        if (window.g_companySetting.newSheetAfterSaleOrderSheetSave == 'True') {
                            btnCopySheetHead_click()
                        }
                    }
                    // 单据同步
                    // saleOrderToBuyOrderSync(sheet)
                }
                else {
                    bw.toast(data.msg, 3000);
                }
            },
            error: function (xhr) {
                console.log("返回响应信息：" + xhr.responseText);
            }
        })
    });

    $("#btnApprove").on('click', function () {
        var isRedAndChange = $('#isRedAndChange').val()
        if (isRedAndChange.toLowerCase()=="true") {
            jConfirm('冲改订单时,如果订单有装车记录会同步修改该订单的装车记录，确定要审核吗？', function () {
                approve(false)
            }, "");
        } else {
            jConfirm('确定要审核吗？', function () {
                approve(false)
            }, "");
        }
        
    });

    async function approve(bReview) {
        // 获取表单数据
        var res = GetSheetData();

        // 检查表单数据是否获取成功
        if (res.result === "Error") {
            bw.toast(res.msg, 5000);
            return;
        }

        try {
            // 获取分单设置
            const isAllowDivideSheet = await getMallMiniSetting(res);

            // 根据仓库信息和分单设置决定处理方式
            // if (res.sheet.branch_id === "" && isAllowDivideSheet) {
            //     console.log("--------------divideApproveSheet--------------")
            //     divideApproveSheet(res, bReview);
            // } else {
            //     console.log("-----------------approveSheet-----------------")
            //     approveSheet(res, bReview);
            // }
            if (res.sheet.branch_id === "") {
                if (isAllowDivideSheet) {
                    console.log("--------------divideApproveSheet--------------")
                    divideApproveSheet(res, bReview);
                }
                else{
                    bw.toast("请选择仓库",3000)
                    return
                }
            }
            else{
                console.log("-----------------approveSheet-----------------")
                approveSheet(res, bReview);
            }
        } catch (error) {
            // 处理异步操作中的错误
            console.error("审批流程出错：", error);
            bw.toast("操作失败：" + error.message, 5000);
        }
    }

    function divideApproveSheet(res, bReview) {
        // 创建并显示"正在审核中"弹窗
        const loadingModal = createLoadingModal();
        document.body.appendChild(loadingModal);

        var sheet = res.sheet;
        var old_sheet_id = $('#old_sheet_id').val()
        if (sheetType == "XD") {
            sheet.placeholder_sheet_id = $('#placeholder_sheet_id').val()
            sheet.placeholder_sheet_no = $('#placeholder_sheet_no').val()
        }
        sheet.old_sheet_id = old_sheet_id
        $('#btnApprove').attr('disabled', true);
        if (bReview) sheet.bReview = true

        $.ajax({
            url: '/api/SaleOrderSheet/DivideSaveAndApprove',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(sheet),
            success: function (data) {
                // 隐藏加载弹窗
                removeLoadingModal(loadingModal);
                if (data.result === 'OK') {
                    $("#btnSave").attr('disabled', true)
                    $("#btnApprove").attr('disabled', true)
                    $("#btnDelete").attr('disabled', true)
                    $("#btnPrint").attr('disabled', true)
                    $("#btnCopy").attr('disabled', true)
                    $("#btnAdd").attr('disabled', true)
                    removeSheetFromCach()
                    bw.toast('分单并审核成功,即将关闭窗口', 5000)
                    setTimeout(function () {
                        window.parent.closeTab(window)
                    }, 2000);
                }
                else {
                    $('#btnApprove').attr('disabled', false);
                    bw.toast(data.msg, 3000);
                }
            },
            error: function (xhr) {
                // 隐藏加载弹窗
                removeLoadingModal(loadingModal);
                console.log("返回响应信息：" + xhr.responseText);
            }
        });
    }

    function approveSheet(res,bReview) {
        // //updateTheCostAmount();
        // var res = GetSheetData();
        // if (res.result === "Error") {
        //     bw.toast(res.msg, 5000); return;
        // }
        var sheet = res.sheet;
        var old_sheet_id = $('#old_sheet_id').val()
        if (sheetType == "XD") {
            sheet.placeholder_sheet_id = $('#placeholder_sheet_id').val()
            sheet.placeholder_sheet_no = $('#placeholder_sheet_no').val()
        }
        sheet.old_sheet_id = old_sheet_id
        $('#btnApprove').attr('disabled', true);
        if (bReview) sheet.bReview = true
        //var isRedAndChange = $('#isRedAndChange').val()
        //sheet.isRedAndChange = isRedAndChange
        $.ajax({
            url: '/api/SaleOrderSheet/SaveAndApprove',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(sheet),
            success: function (data) {
                if (data.result === 'OK') {
                    $('#sheet_id').val(data.sheet_id)
                    $('#sheet_no').text(data.sheet_no)
                    $('#approve_time').text(data.approve_time)
                    $('#approve_time').val(data.approve_time)
                    $('#happen_time').jqxDateTimeInput('val', data.happen_time)
                    $('#review_time').text(data.review_time)
                    $('#placeholder_sheet_id').val(data.placeholder_sheet_id)
                    $('#placeholder_sheet_no').text(data.placeholder_sheet_no)
                    if ($('#placeholder_sheet_no').text() != "") $("#placeholder-sheet").css("display", "inline-block")
                    if ($('#maker_name').text() == '') $('#maker_name').text(window.g_operName);
                    updateSheetState()
                    removeSheetFromCach()
                    if (bReview) bw.toast('复核成功', 3000);
                    else bw.toast(data.msg, 3000); 
                    if (window.g_companySetting) {
                        if (window.g_companySetting.newSheetAfterSaleOrderSheetApprove == 'True') {
                            btnCopySheetHead_click()
                        }
                    }
                    //$('#red_sheet_id').val("")
                    //$('#sheet_id').val(data.sheet_id)
                    //saleOrderToBuyOrderSync(sheet)
                }
                else {
                    $('#btnApprove').attr('disabled', false);

                    bw.toast(data.msg, 3000);
                }
            },
            error: function (xhr) {
                console.log("返回响应信息：" + xhr.responseText);
            }
        });
    }

    // 获取商城分单设置
    function getMallMiniSetting(res) {
        const params = {
            operKey: res.sheet.OperKey,
            supcustId: res.sheet.supcust_id,
            wx_user_id: res.sheet.wx_user_id,
            companyId: ""
        };

        return new Promise((resolve, reject) => {
            $.ajax({
                url: '/MallMini/MallMiniBaseInfo/GetMallMiniSupSettingBranchInfo',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(params),
                success: function (data) {
                    let isAllowDivide = false;
                    if (data.message === 'success') {
                        const key = "mall_allow_divide_sheet_by_branch";
                        const settings = data.result.mallMiniSetting.mallMiniSetting;

                        // 遍历设置项查找目标配置
                        for (let prop in settings) {
                            if (settings.hasOwnProperty(prop) && settings[prop].key === key) {
                                isAllowDivide = settings[prop].value;
                                break;
                            }
                        }
                    }
                    resolve(isAllowDivide);
                },
                error: function (xhr) {
                    console.error("获取商城设置失败：" + xhr.responseText);
                    reject(new Error("获取配置信息失败"));
                }
            });
        });
    }

    $("#btnReview").on('click', function () {
        var rows = $('#jqxgrid').jqxGrid('getrows');
        rows.filter(row => !row.item_id)
        let flag = checkBatchForSheetRows(rows)
        var sheet_id = $('#sheet_id').val()
        console.log(sheet_id)
        var approve_time = $('#approve_time').val()
        console.log(approve_time)
        if (flag) return
        if (approve_time) {
            $.ajax({
                // url: '/api/SaleOrderSheet/Review?operKey=' + g_operKey,
                url: '/api/SaleOrderSheet/Review',
                type: 'POST',
                contentType: 'application/json',
                // data: JSON.stringify({sheet_id:'1',supcust_id:'2'}),
                data: JSON.stringify({ sheet_id: sheet_id, operKey: g_operKey, approve_time: approve_time }),
                success: function (data) {
                    if (data.result === 'OK') {
                        $('#sheet_id').val(data.sheet_id);
                        $('#sheet_no').text(data.sheet_no);
                        $('#review_time').text(data.review_time)
                        updateSheetState();
                        removeSheetFromCach()
                        bw.toast('复核成功', 3000);
                    }
                    else {
                        bw.toast(data.msg, 3000);
                    }
                },
                error: function (xhr) {
                    console.log("返回响应信息：" + xhr.responseText);
                }
            })
        }
        else {
            jConfirm('确定要复核吗？', function () {
                approveSheet(true)

            }, "");
        }
      
    });
    $("#btnDelete").on('click', function () {
        /*var res = GetSheetData();
        if (res.result === "Error") {
            bw.toast(res.msg, 5000); return;
        }
        var sheet = res.sheet;
        */
        var sheet_id = $('#sheet_id').val()
        jConfirm('确定要删除本单据吗？', function () {
            $.ajax({
                url: '/api/SaleOrderSheet/Delete',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ operKey: g_operKey, sheet_id: sheet_id }),
                success: function (data) {
                    if (data.result === 'OK') {
                        //$("#btnApprove").attr('disabled', true);
                        $("#btnSave").attr('disabled', true)
                        $("#btnApprove").attr('disabled', true)
                        $("#btnDelete").attr('disabled', true)
                        $("#btnPrint").attr('disabled', true)
                        $("#btnCopy").attr('disabled', true)
                        $("#btnAdd").attr('disabled', true)
                        removeSheetFromCach()
                        bw.toast('删除成功,即将关闭窗口', 3000)
                        setTimeout(function () {
                            window.parent.closeTab(window)
                        }, 2000);

                    }
                    else {
                        bw.toast(data.msg, 3000);
                    }
                },
                error: function (xhr) {
                    console.log("返回响应信息：" + xhr.responseText);
                }
            })
        }, "")

    })
   
      
    $("#btnAdd").on('click', function () {
        var sheetType = $('#sheet_type').val();
        if (sheetType == "SHEET_SALE_DD") window.parent.newTabPage('销售订单', `Sheets/SaleOrderSheet`);
        if (sheetType == "SHEET_SALE_DD_RETURN") window.parent.newTabPage('退货订单', `Sheets/SaleOrderSheet?forReturn=true`);
        if (!window.firstCachTime) {
            setTimeout(() => {
                window.parent.closeTab(window);
            }, 50)

        }
    })
    /*function getSheetToPrint() {
        var res = GetSheetData();
        if (res.result === "Error") {
            bw.toast(res.msg, 5000); return;
        }

        res.sheet.sup_addr == $('#sup_addr').val();
        res.sheet.mobile == $('#mobile').val();

        var sheetType = $('#sheet_type').val();
        var clientID = $('#supcust_id').jqxInput('val');
        if (clientID) clientID = clientID.value;
        res.sheet.SheetRows.forEach(function (row) {
            var item = window.g_queriedItems[row.item_id]
            var smallQty = Number(row.quantity) * Number(row.unit_factor)
            var leftQty = 0
            var unit, qty, leftQty
            var sQty = ''
            leftQty = smallQty
            var unit_relation = ''
            var absLeftQty = Math.abs(leftQty)
            var flag = leftQty / absLeftQty
            for (var i = 0; i <= item.units.length - 1; i++) {
                unit = item.units[i]
                if (!unit.unit_factor) continue
                if (unit.unit_type == 'b') unit_relation = "1*" + unit.unit_factor.toString()

                qty = Math.floor(absLeftQty / unit.unit_factor)

                if (qty > 0)
                    sQty += (qty * flag).toString() + unit.unit_no

                absLeftQty = absLeftQty % unit.unit_factor
                if (absLeftQty <= 0) break
            }

            row.unit_relation = unit_relation
            row.quantity_unit_conv = sQty
            row.retail_price = item.retail_price
            if (document.all.ckPrintSmallBarcode.checked) {
                row.barcode = row.s_barcode
            }

        })
        if (res.sheet.payway1_amount > 0) {
            res.sheet.payway1 = res.sheet.payway1_name + ":" + res.sheet.payway1_amount
        }

        if (res.sheet.payway2_amount > 0) {
            res.sheet.payway2 = res.sheet.payway2_name + ":" + res.sheet.payway2_amount
        }
        return res.sheet
    }
    */
    $("#btnPrint").on('click', function () {
        //获取表格的类型
        var sheetType = $('#sheet_type').val();
        //获取客户的id
        var clientID = $('#supcust_id').jqxInput('val');
        if (clientID) clientID = clientID.value;

        //var sheet = getSheetToPrint()
        //if (!sheet) return
        $.ajax({
            url: '/api/PrintTemplate/GetTemplateToUse',
            type: 'GET',
            contentType: 'application/json',
            data: {
                operKey: g_operKey,
                sheetType: sheetType,
                clientID: clientID
            },
            success: function (printInfo) {
                if (printInfo.result === 'OK') {
                    if (printInfo.templateList.length == 0) {
                        bw.toast("没有可用打印模板", 5000)
                        return
                    }
                    var tmp = printInfo.templateList[0]
                    var sTmp = tmp.template_content
                    tmp = JSON.parse(tmp.template_content)

                    var printTemplate = []

                    if (sTmp.indexOf('"prepay_balance"') >= 0) printTemplate.push({ name: "prepay_balance" })
                    if (sTmp.indexOf('"arrears_balance"') >= 0) printTemplate.push({ name: "arrears_balance" })
                    if (sTmp.indexOf('"order_item_balance"')>=0) printTemplate.push({ name: "order_item_balance" })
                    if (sTmp.indexOf('"print_count"') >= 0) printTemplate.push({ name: "print_count" })
                    if (sTmp.indexOf('"name":"give_qty_unit"') >= 0) printTemplate.push({ name: "give_qty_unit" })

                    var sheet_id = $('#sheet_id').val();
                    var smallUnitBarcode = document.all.ckPrintSmallBarcode.checked
                    $.ajax({
                        url: '/api/SaleOrderSheet/GetSheetToPrint',
                        type: 'GET',
                        contentType: 'application/json',
                        data: {
                            operKey: g_operKey,
                            sheet_id: sheet_id,
                            smallUnitBarcode: smallUnitBarcode,
                            printTemplate: JSON.stringify(printTemplate)
                        },
                        success: function (data) {
                            if (data.result === 'OK') {
                                var sheet = data.sheet


                                var container = window.parent.CEFPrinter
                                if (!container)
                                    container = window.parent.CefGlue

                                if (!container)
                                    container = window.parent

                                if (!container.printSheetByTemplate) {
                               
                                    if (printInfo.cloudPrinters.length == 0) {
                                        bw.toast("请到客户端打印，或是添加一个云打印机", 5000)
                                        return
                                    }
                                    var ptr = printInfo.cloudPrinters[0]
                                         
                                            var device_id = ptr.device_id
                                            var check_code = ptr.check_code
                                            var printer_brand = ptr.printer_brand
                                            var url = '/AppApi/CloudPrint/PrintSheetWithTemplate';
                                            if (window.CoolieServerUri) {
                                                url = window.CoolieServerUri + url;
                                            }

                                            $.ajax({
                                                url: url,
                                                type: 'POST',
                                                contentType: 'application/json',
                                                data: JSON.stringify({
                                                    operKey: g_operKey,
                                                    device_id: device_id,
                                                    check_code: check_code,
                                                    printer_brand: printer_brand,
                                                    sheet: sheet,
                                                    tmp: tmp,
                                                    variables: printInfo.variables,
                                                    cus_orderid: sheet.sheet_no,
                                                    copies: "1"
                                                }),
                                                success: function (res) { 
                                                    var msg = res.return_msg == "" ? "打印请求已发送,请等待" : res.return_msg
                                                    bw.toast(msg)
                                                    console.log("Print post sent to " + device_id)
                                                },
                                                error: function (xhr) {
                                                    bw.toast('发送打印请求失败')
                                                    console.log("返回响应信息：" + xhr.responseText)
                                                }
                                            });
                                        
                                    //bw.toast('在客户端程序中才可以打印', 3000)
                                    return
                                }
                             
                                window.parent.g_SheetsWindowForPrint = window.srcWindow
                                
                                container.printSheetByTemplate(sheet, tmp, true, printInfo.cloudPrinters, printInfo.variables)
                                var clientVersion = 0
                                if (window.parent && window.parent.CefGlue) {
                                    clientVersion = window.parent.CefGlue.getClientVersion()
                                }

                                if (parseFloat(clientVersion) < 3.32) {
                                    $.ajax({
                                        url: '/api/Printer/PrintMark',
                                        type: 'POST',
                                        contentType: 'application/json',
                                        data: JSON.stringify({
                                            operKey: g_operKey,
                                            sheetType: 'X',
                                            sheetIDs: sheet.sheet_id,
                                            printEach: true,
                                            printSum: false
                                        }),
                                        success: function (data) {
                                            if (data.result === 'OK') {
                                            }
                                            else {

                                            }
                                        },
                                        error: function (xhr) {
                                            // console.log("返回响应信息：" + xhr.responseText)
                                        }
                                    })
                                }
                            }
                            else {
                                bw.toast(data.msg, 3000)
                            }
                        },
                        error: function (xhr) {
                            bw.toast('获取单据信息失败')
                            console.log("返回响应信息：" + xhr.responseText)
                        }
                    }) 
                     
                  
                }
                else {
                    bw.toast(data.msg, 3000)
                }
            },
            error: function (xhr) {
                bw.toast('网络连接失败')
                console.log("返回响应信息：" + xhr.responseText)
            }
        })
    })

    $('#choosetemplate').on('click', function () {

        var maskBg = document.getElementById('topCoverDiv');
        var dia = document.getElementById('dia');
        maskBg.style.display = (maskBg.style.display == 'none') ? 'block' : 'none';
        dia.style.display = (dia.style.display == 'none') ? 'block' : 'none';


        //获取表格的类型
        var sheetType = $('#sheet_type').val();
        //获取客户的id
        var clientID = $('#supcust_id').jqxInput('val');
        if (clientID) clientID = clientID.value;
        //获取表单的数据
       // var sheet = getSheetToPrint()
        //if (!sheet) return
        $.ajax({
            url: '/api/PrintTemplate/GetTemplateToUse',
            type: 'GET',
            contentType: 'application/json',
            data: {
                operKey: g_operKey,
                sheetType: sheetType,
                clientID: clientID
            },
            success: function (printInfo) {
                if (printInfo.result === 'OK') {
                    if (printInfo.templateList.length == 0) {
                        bw.toast("没有可用打印模板", 5000)
                        return
                    }              

                    var templateList = printInfo.templateList;
                    var templateShowInHTML = $('#template-list');
                    templateShowInHTML.empty();
                    for (let i = 0; i < templateList.length; i++) {
                        templateHTML = `<button id="templatePrint" value="${i}" style="margin: 10px 0px 0px 10px">${templateList[i].template_name}</button>`
                        templateShowInHTML.append(templateHTML);
                    }
                    if (window.g_companySetting) {
                        if (window.g_companySetting.sheetShowBarcodeStyle == '2') {
                            document.all.ckPrintSmallBarcode.checked = true
                        }
                    }
                    //document.all.ckPrintSmallBarcode.checked = true

                    $("[id='templatePrint']").on('click', function () {
                        var index = parseInt(this.value);
                        var tmp = printInfo.templateList[index];
                        var sTmp = tmp.template_content
                        tmp = JSON.parse(tmp.template_content);

                        var printTemplate=[]

                        if (sTmp.indexOf('"prepay_balance"') >= 0) printTemplate.push({ name: "prepay_balance" })
                        if (sTmp.indexOf('"arrears_balance"') >= 0) printTemplate.push({ name: "arrears_balance" })
                        if (sTmp.indexOf('"order_item_balance"') >= 0) printTemplate.push({ name: "order_item_balance" })
                        if (sTmp.indexOf('"print_count"') >= 0) printTemplate.push({ name: "print_count" })
                        if (sTmp.indexOf('"name":"give_qty_unit"') >= 0) printTemplate.push({ name: "give_qty_unit" })

                        var sheet_id = $('#sheet_id').val();
                        var smallUnitBarcode = document.all.ckPrintSmallBarcode.checked

                        $.ajax({
                            url: '/api/SaleOrderSheet/GetSheetToPrint',
                            type: 'GET',
                            contentType: 'application/json',
                            data: {
                                operKey: g_operKey,
                                sheet_id: sheet_id,
                                smallUnitBarcode: smallUnitBarcode,
                                printTemplate: JSON.stringify(printTemplate)
                            },
                            success: function (data) {
                                if (data.result === 'OK') {
                                    var sheet = data.sheet


                                    var container = window.parent.CEFPrinter
                                    if (!container)
                                        container = window.parent.CefGlue

                                    if (!container)
                                        container = window.parent

                                    if (!container.printSheetByTemplate) {
                                        
                                        if (printInfo.cloudPrinters.length == 0) {
                                            bw.toast("请到客户端打印，或是添加一个云打印机", 5000)
                                            return
                                        }
                                        templateShowInHTML.empty();
                                        templateShowInHTML.append("请选择打印机：<br><br>");
                                        for (let i = 0; i < printInfo.cloudPrinters.length; i++) {
                                            templateHTML = `<button id="cloudPrinter" value="${i}" style="margin: 10px 0px 0px 10px;width:auto;">${printInfo.cloudPrinters[i].printer_name}</button>`
                                            templateShowInHTML.append(templateHTML);
                                        }
                                        $("[id='cloudPrinter']").on('click', function () {
                                            var index = parseInt(this.value);
                                            var ptr = printInfo.cloudPrinters[index]                                                  
                                            var device_id = ptr.device_id
                                            var check_code = ptr.check_code
                                            var printer_brand = ptr.printer_brand
                                            var url = '/AppApi/CloudPrint/PrintSheetWithTemplate';
                                            if (window.CoolieServerUri) {
                                                url = window.CoolieServerUri + url;
                                            }

                                                    $.ajax({
                                                        url: url,
                                                        type: 'POST',
                                                        contentType: 'application/json',
                                                        data: JSON.stringify({
                                                            operKey: g_operKey,
                                                            device_id: device_id,
                                                            check_code: check_code,
                                                            printer_brand: printer_brand,
                                                            sheet: sheet,
                                                            tmp: tmp,
                                                            cus_orderid: sheet.sheet_no,
                                                            copies: "1"
                                                        }),
                                                        success: function (res) { 
                                                            var msg = res.return_msg == "" ? "打印请求已发送,请等待" : res.return_msg
                                                            bw.toast(msg)
                                                            console.log("Print post sent to " + device_id)
                                                        },
                                                        error: function (xhr) {
                                                            bw.toast('发送打印请求失败')
                                                            console.log("返回响应信息：" + xhr.responseText)
                                                        }
                                                    });
                                                });

                              
                                        //bw.toast('在客户端程序中才可以打印', 3000)
                                        return
                                    }
                              

                                    window.parent.g_SheetsWindowForPrint = window.srcWindow
                      
                                    container.printSheetByTemplate(sheet, tmp, true, printInfo.cloudPrinters, printInfo.variables)
                                    var clientVersion = 0
                                    if (window.parent && window.parent.CefGlue) {
                                        clientVersion = window.parent.CefGlue.getClientVersion()
                                    }

                                    if (parseFloat(clientVersion) < 3.32) {
                                        $.ajax({
                                            url: '/api/Printer/PrintMark',
                                            type: 'POST',
                                            contentType: 'application/json',
                                            data: JSON.stringify({
                                                operKey: g_operKey,
                                                sheetType: 'XD',
                                                sheetIDs: sheet.sheet_id,
                                                printEach: true,
                                                printSum: false
                                            }),
                                            success: function (data) {
                                                if (data.result === 'OK') {
                                                }
                                                else {

                                                }
                                            },
                                            error: function (xhr) {
                                                // console.log("返回响应信息：" + xhr.responseText)
                                            }
                                        })
                                    }
                                }
                                else {
                                    bw.toast(data.msg, 3000)
                                }
                            },
                            error: function (xhr) {
                                console.log("返回响应信息：" + xhr.responseText)
                            }
                        })
                         
                        
                    });
              

                    return;

                }
                else {
                    bw.toast(data.msg, 3000);
                }
            },
            error: function (xhr) {
                console.log("返回响应信息：" + xhr.responseText);
            }
        });
    });
    window.onresize()

}

function saleOrderToBuyOrderSync(sheet) {
    $.ajax({
        url: '/api/SaleOrderSheet/SaveAsBuyOrderSheet',
        type: 'POST',
        contentType: 'application/json',
        // data: JSON.stringify({sheet_id:'1',supcust_id:'2'}),
        data: JSON.stringify(sheet),
        success: function (data) {
            if (data.result === 'OK') {
                removeSheetFromCach()
                bw.toast('同步成功', 3000);
               
            }
            else {
                bw.toast(data.msg, 3000);
            }
        },
        error: function (xhr) {
            console.log("返回响应信息：" + xhr.responseText);
        }
    })
}

function btnExportExcel() {
    // 只能导出表体
    debugger
    let url = '/api/SaleOrderSheet/JqxExportExcel?operKey=' + g_operKey
    let data = $("#jqxgrid").jqxGrid('exportdata', 'json');
    $("#jqxgrid").jqxGrid('exportdata', 'xls', '销售订单', true, null, false, url)

    //$("#jqxgrid").jqxGrid('exportdata', 'xls', '收款单')
}

function SetBranchAndSellerOnSupcustUpdate(supcust_id, rememberBranch,rememberSeller) {
    var params = { operKey: g_operKey, supcust_id: supcust_id }
    $.ajax({
        url: '/api/SaleOrderSheet/SetBranchAndSellerOnSupcustUpdate',
        type: 'GET',
        contentType: 'application/json',
        processData: true,
        data: params,
        success: function (data) {
            if (data.result === 'OK') {
                if (rememberBranch && data.data[0] && data.data[0].branch_id) {
                    $('#branch_id').jqxInput('val', { value: data.data[0].branch_id, label: data.data[0].branch_name })
                }

                //else {

                //    $('#branch_id').jqxInput('val', { value: "", label: "" })
                //}

                if (rememberSeller && data.data[0] && data.data[0].seller_id) {
                    $('#seller_id').jqxInput('val', { value: data.data[0].seller_id, label: data.data[0].seller_name })
                }
               // else {
               //     $('#seller_id').jqxInput('val', { value: "", label: "" })
               // }
            }
        }
    })
}

function getGridMenuHtml() {
    var divMenu = `<div id='gridMenu'>
                    <ul>
                        <li id='copyRow'>复制</li>
                        <li id='addOtherUnitRow'>加其他单位(+)</li>
                        <li id='hideCostPrice'>隐藏成本(F8)</li>
                        <li id='changeToBorrowItem'>借货商品</li>
                        <li id='exchangeItem'>换货</li>
                        <li id='itemHistory'>历史记录</li>
                        <li id='customerHistory'>历史单据</li>
                    </ul>
                   </div>`;
    return divMenu
}

/*
function getClientAccountInfo(supcust_id) {


    $('#div_get_account').empty()
    $.ajax({
        url: '/api/SaleSheet/GetClientAccountInfo',
        type: 'GET',
        contentType: 'application/json',
        data: {
            operKey: g_operKey,
            supcust_id: supcust_id
        },
        success: function (data) {
            if (data.result === 'OK') {
                var arrears = "";
                var prepay = "";
                var dispLabel = "";
                $('#mobile').val(data.mobile)
                $('#sup_addr').val(data.sup_addr)
                $('#boss_name').val(data.boss_name)
                window.dispAccounts = data.disp
                if (data.disp.length > 0) {
                    for (var i = 0; i < data.disp.length; i++) {
                        //var sub_id = data.disp[i].sub_id
                        var sub_name = data.disp[i].sub_name
                        var disp_amount = data.disp[i].disp_amount
                        dispLabel += `<label id='DispTip' style="padding:17px;color:#f66;font-size:14px;" title = "本月前(含本月)的陈列剩余费用">${sub_name}:${disp_amount}</label>`
                    }
                }

                if (data.arrears.length > 0) arrears = '应收款:' + toMoney(data.arrears[0].balance) + ' ;  '
               

                window.prepayAccounts = data.prepay
                if (data.prepay.length > 0) {
                    for (var i = 0; i < data.prepay.length; i++) {
                        var sub_id = data.prepay[i].sub_id
                        var sub_name = data.prepay[i].sub_name
                        var prepay_balance = data.prepay[i].balance
                        if (!sub_name && prepay_balance) sub_name = '预收款'
                        prepay += sub_name + ': ' + toMoney(prepay_balance) + ' ; ' + ' '

                    }
                }
                $labelArrearsInfo = $('<label style="padding:17px;color:#f66;font-size:14px"></label>')
                $labelPrepayInfo = $('<label style="padding:17px;color:#f66;font-size:14px"></label>')
                if (dispLabel) $('#div_get_account').append(dispLabel)

                if (arrears) {
                    $('#div_get_account').append($labelArrearsInfo)
                    $labelArrearsInfo.text(arrears)
                }
                if (prepay) {
                    $('#div_get_account').append($labelPrepayInfo)
                    $labelPrepayInfo.text(prepay)
                }

            }
            else {
                bw.toast(data.msg, 3000);
            }
        },
        error: function (xhr) {
            console.log("返回响应信息：" + xhr.responseText);
        }
    });
}

*/

// 创建加载弹窗的辅助函数
function createLoadingModal() {
    const modal = document.createElement('div');
    modal.id = 'loadingModal';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.3s ease;
    `;
    
    // 弹窗内容
    modal.innerHTML = `
        <div style="
            background: white;
            padding: 30px 40px;
            border-radius: 8px;
            text-align: center;
            min-width: 280px;
        ">
            <!-- 加载动画 -->
            <div style="
                width: 40px;
                height: 40px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #3498db;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 15px;
            "></div>
            <!-- 文字提示 -->
            <p style="margin: 0; font-size: 16px; color: #333;">正在分单并审核中，请稍候...</p>
        </div>
        <style>
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        </style>
    `;
    
    return modal;
}

// 移除加载弹窗的辅助函数
function removeLoadingModal(modal) {
    // 添加淡出效果
    modal.style.opacity = '0';
    setTimeout(() => {
        if (modal && modal.parentNode) {
            modal.parentNode.removeChild(modal);
        }
    }, 300);
}

