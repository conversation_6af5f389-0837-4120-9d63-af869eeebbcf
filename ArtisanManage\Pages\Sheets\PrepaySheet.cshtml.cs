﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using ArtisanManage.Models;
using ArtisanManage.Services;
using ArtisanManage.MyJXC;
using static ArtisanManage.MyJXC.SheetSale;
using System.Dynamic;
using System.Net.Http;

namespace ArtisanManage.Pages
{
    public class PrepaySheetModel : PageSheetModel<SheetRowBase>
    { 
        public string SheetTitle = "";
        public PrepaySheetModel(CMySbCommand cmd) : base(Services.MenuId.sheetPrepay)
        {
            this.cmd = cmd;
			DataItems = new Dictionary<string, DataItem>()
            {
                {"sheet_no",new DataItem(){Title="单号",UseJQWidgets=false}},
                {"sheet_id",new DataItem(){Title="sheet_id", CtrlType="hidden", FldArea="divHead"}},
                {"red_flag",new DataItem(){Title="red_flag", CtrlType="hidden", FldArea="divHead"}},
                {"isRedAndChange",new DataItem(){Title="isRedAndChange",CtrlType="hidden",  FldArea="divHead"} },//冲改用的
                {"old_sheet_id",new DataItem(){Title="old_sheet_id",CtrlType="hidden",  FldArea="divHead"} },//冲改用的
                {"sheet_type",new DataItem(){Title="sheet_type", CtrlType="hidden", FldArea="divHead"}},
                {"sheetType",new DataItem(){Title="sheetType", CtrlType="hidden", FldArea="divHead"}},
                {"supcust_id",new DataItem(){FldArea="divHead",Title="客    户",LabelFld="sup_name", ButtonUsage="event",SqlForOptions="select supcust_id as v,sup_name as l,py_str as z from info_supcust where company_id=~COMPANY_ID and acct_cust_id is null  and supcust_flag in ('C','CS') and COALESCE(status,'1')='1'",DropDownWidth = "200"}},
                {"getter_id",new DataItem(){FldArea="divHead",Title="业务员",LabelFld="getter_name",ButtonUsage="list", SqlForOptions=CommonTool.selectSellers} },//SqlForOptions="select oper_id as v,oper_name as l,py_str as z from info_operator"}},
                {"prepay_sub_id",new DataItem(){FldArea="divHead",Title="账户",LabelFld="prepay_sub_name",ButtonUsage="list",SqlForOptions="select sub_id as v,sub_name as l,py_str as z from cw_subject where sub_type = 'YS'  and is_order is not true and COALESCE(status,'1')='1'"}},
                //{"branch_id",new DataItem(){FldArea="divHead",Title="仓    库",LabelFld="branch_name", ButtonUsage="list",SqlForOptions="select branch_id as v,branch_name as l,py_str as z from info_branch"}},
                //{"oper_date",new DataItem(){title="制单日期",ctrlType="jqxDateTimeInput"}},
                {"happen_time",new DataItem(){FldArea="divHead",Title="交易日期",CtrlType="jqxDateTimeInput",PlaceHolder="当前时间" }},
                {"make_brief",new DataItem(){FldArea="divHead",Title="备    注",Width="200"}},
                {"total_amount",new DataItem(){FldArea="divHead",Title="金额",Value="0"}},
                {"now_disc_amount",new DataItem(){FldArea="divTail",Title="优惠金额",Width="80",Value="0"}},
                {"no_disc_amount",new DataItem(){FldArea="divTail",Title="惠后合计",Width="80",Disabled=true}},
                {"payway1_id",new DataItem(){FldArea="divTail",HideGroup="payway1", HideClass="payway",Title="",LabelFld="payway1_name",ClassName="itemLeft",  PlaceHolder="支付方式",ButtonUsage="list", Width="80",GetOptionsOnLoad=true,FirstOptionAsDefault=true,
                 SqlForOptions=CommonTool.selectPayWayPreget,
				}},
                {"payway1_amount",new DataItem(){FldArea="divTail",HideGroup="payway1",HideClass="payway", Title="",ClassName="itemRight",PlaceHolder="支付金额",Width="80"}},
                {"payway2_id",new DataItem(){FldArea="divTail",HideGroup="payway2",HideClass="payway", Title="",LabelFld="payway2_name",ClassName="titleItem",PlaceHolder="支付方式", ButtonUsage="list",Width="80",GetOptionsOnLoad=true,
                 SqlForOptions=CommonTool.selectPayWayPreget,
				}},
                {"payway2_amount",new DataItem(){FldArea="divTail",HideGroup="payway2",HideClass="payway", Title="",PlaceHolder="支付金额",Width="80"}},               
                {"LeftAmount",new DataItem(){FldArea="divTail",Title="欠款",Width="80",Disabled = true}},
                {"maker_id",new DataItem(){UseJQWidgets=false, Title="制单人", Width="80"}},
                {"maker_name",new DataItem(){UseJQWidgets=false, Title="制单人", Width="80"}},
                {"make_time",new DataItem(){UseJQWidgets=false, Title="审核时间"}},
                {"approver_id",new DataItem(){UseJQWidgets=false, Title="审核人", Width="80"}},
                {"approver_name",new DataItem(){UseJQWidgets=false, Title="审核人", Width="80"}},
                {"approve_time",new DataItem(){UseJQWidgets=false, Title="审核时间"}},
                {"order_adjust_sheet_id",new DataItem(){UseJQWidgets=false, Title="order_adjust_sheet_id"}},
                {"order_adjust_sheet_no",new DataItem(){UseJQWidgets=false, Title="order_adjust_sheet_no"}},
                {"red_sheet_id",new DataItem(){Title="red_sheet_id", CtrlType="hidden", FldArea="divHead"}},
                //{"red_sheet_no",new DataItem(){UseJQWidgets=false, Title="red_sheet_no"}},
                {"sheet_id_red_me",new DataItem(){Title="sheet_id_red_me", CtrlType="hidden", FldArea="divHead"}},
                {"appendix_photos",new DataItem(){Title="appendix_photos", CtrlType="hidden", FldArea="divHead"}},
               // {"branch_state",new DataItem(){title="状态",labelFld="branch_status_name",labelInDB=false,value="1",label="正常", buttonUsage="list", source = "[{v:1,l:'正常'},{v:0,l:'停用'}]"}}
            };

           // m_idFld = "sheet_id"; 
           // m_tableName = "sheet_item_master";
          //  m_selectFromSQL = "from sheet_item_master sht left join info_supcust on sht.supcust_id=info_supcust.supcust_id left join info_branch on sht.branch_id=info_branch.brand_id left join (select oper_id,oper_name as order_man_name from info_operator) tb_order_man on sht.order_man=tb_order_man.oper_id where sheet_id='~ID'";
            /*
            Grids = new Dictionary<string, FormDataGrid>()
            {
                {"gridUnit" ,new FormDataGrid(){
                   Columns = new Dictionary<string, DataItem>()
                   {
                       //{"unit_no",new DataItem(){title="单位",width="100",url="../api/ItemEdit/GetUnits"}},
                       {"unit_no",new DataItem(){title="单位",width="100",SqlForOptions="select unit_no from info_item_unit",getOptionsOnLoad=true,buttonUsage="list"}},
                       {"unit_factor",new DataItem(){title="包装率",width="80"}},
                   },
                   TableName="info_item_multi_unit",
                   IdFld="item_no",
                   SelectFromSQL="from info_item_multi_unit where item_no='~ID'"
                }}
            };*/
        }

        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            if (CPubVars.RequestV(Request, "forPayOrGet") != "")
            {
                bool forPayOrGet = Convert.ToBoolean(CPubVars.RequestV(Request, "forPayOrGet"));
                if (forPayOrGet)
                {
                    DataItems["supcust_id"].Title = "供应商";
                    DataItems["supcust_id"].OtherQueryStrForOptions = "sheetType=YF";
                    DataItems["prepay_sub_id"].OtherQueryStrForOptions = "sheetType=YF";
					DataItems["payway1_id"].SqlForOptions =  CommonTool.selectPayWayPrepay;
					DataItems["payway2_id"].SqlForOptions =  CommonTool.selectPayWayPrepay;
				}
            }

		}

        public async Task OnGet(bool forPayOrGet)
        {
            if (forPayOrGet) PageMenuID = MenuId.sheetPrepay;//否则会默认使用预付款的权限
            else PageMenuID = MenuId.sheetPreget;
            SheetPrepay<SheetRowBase> sheet = new SheetPrepay<SheetRowBase>(forPayOrGet?SHEET_PREPAY.IS_PREPAY:SHEET_PREPAY.NOT_PREPAY, LOAD_PURPOSE.SHOW);
            await InitGet(cmd,sheet);
            SheetTitle = sheet.sheet_type==SHEET_TYPE.SHEET_PRE_GET_MONEY ? "预收款单" : "预付款单";
			SheetRowsJson = Newtonsoft.Json.JsonConvert.SerializeObject(sheet.SheetRows);
        }
    }
    [ApiController]
    [Route("api/[controller]/[action]")]
    public class PrepaySheetController : QueryController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        public PrepaySheetController(CMySbCommand cmd,IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }

        [HttpGet]
        public async Task<JsonResult> GetDataItemOptions(string operKey, string dataItemName, string flds, string value,string sheetType, string availValues)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var model = new PrepaySheetModel(cmd);
            if (sheetType == "YF")
            {
                model.DataItems["supcust_id"].SqlForOptions = $"select supcust_id as v,sup_name as l,py_str as z from info_supcust where company_id={companyID} and supcust_flag ilike '%S%'";
                model.DataItems["prepay_sub_id"].SqlForOptions = $"select sub_id as v,sub_name as l,py_str as z from cw_subject where sub_type = 'YF' and company_id = {companyID} and (status!='0' or status is null)";
            }
            return await PageBaseModel.GetDataItemOptionsJson(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
             
        }


        


        public class test {
            public string id = "", name = "", tt="";
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody] SheetPrepay<SheetRowBase> sheet)  //[FromBody] dynamic sheet)
        {
            sheet.Init();
            sheet._httpClientFactory = this._httpClientFactory;
            await sheet.ProcessPcAppendix();
            string msg = await sheet.Save(cmd);
            string result = msg== "" ? "OK": "Error";
            return new JsonResult(new { result, msg,sheet.sheet_id,sheet.sheet_no});
        }

        [HttpPost]
        public async Task<IActionResult> SyncPrepay([FromBody] SheetPrepay<SheetRowBase> sheet)  //[FromBody] dynamic sheet)
        {

            sheet.Init();
            string operKey = sheet.OperKey;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            sheet.OperID = "1";
            sheet.SYNCHRONIZE_SHEETS = true;
            if (sheet.sheet_type == SHEET_TYPE.SHEET_PRE_GET_MONEY)
            {
                // TODO 客户 此处为厂家 supplier
                string resellerInfoSql = $"select * from rs_seller where company_id={companyID} and client_id = {sheet.supcust_id}";
                dynamic resellerInfo = await CDbDealer.GetRecordsFromSQLAsync(resellerInfoSql, cmd);
                
                sheet.company_id = (string)resellerInfo[0].reseller_company_id;
                sheet.supcust_id = (string)resellerInfo[0].supplier_id;
                sheet.sheet_type = SHEET_TYPE.SHEET_PRE_PAY_MONEY;
            }
            if (sheet.sheet_type == SHEET_TYPE.SHEET_PRE_PAY_MONEY)
            {
                // TODO 客户 此处为分销商 client
                string resellerInfoSql = $"select * from rs_seller where reseller_company_id={companyID} and supplier_id = {sheet.supcust_id}";
                dynamic resellerInfo = await CDbDealer.GetRecordsFromSQLAsync(resellerInfoSql, cmd);

                sheet.company_id = (string)resellerInfo[0].company_id;
                sheet.supcust_id = (string)resellerInfo[0].client_id;
                sheet.sheet_type = SHEET_TYPE.SHEET_PRE_GET_MONEY;
            }
            
            sheet.sheet_id = "";
            sheet.sheet_no = "";
            sheet.maker_id = "1";
            sheet.maker_name = "";
            sheet.getter_id = "";
            sheet.getter_name = "";
            sheet.payway1_id = "";
            sheet.payway1_amount = 0;
            sheet.prepay_sub_id = "";
            sheet.prepay_sub_name = "";
            
            
            
            string msg = await sheet.Save(cmd);
            string result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no });
        }


        [HttpPost]
        public async Task<IActionResult> SaveAndApprove([FromBody] SheetPrepay<SheetRowBase> sheet)
        {
            sheet.Init();
            sheet._httpClientFactory = this._httpClientFactory;
            await sheet.ProcessPcAppendix();
            string msg = "";
            Console.WriteLine("冲改属性是：", sheet.isRedAndChange);
            if (sheet.isRedAndChange)
            {
                if (!sheet.old_sheet_id.IsValid())
                {
                    msg = "修改时没有获取原单据的编号";
                }
                else
                {
                    Console.WriteLine("进入到冲改界面");
                    msg = await sheet.RedAndChange<SheetPrepay<SheetRowBase>>(cmd);
                }
            }
            else
            {
                msg = await sheet.SaveAndApprove(cmd);
            }
            //string msg = await sheet.SaveAndApprove(cmd);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error"; 
            return new JsonResult(new { result, msg , sheet.sheet_id,sheet.sheet_no,sheet.happen_time, sheet.approve_time });  
        }



        [HttpPost]
        public async Task<IActionResult> Red([FromBody] dynamic data)
        {
            string sheet_id = data.sheet_id;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetPrepay<SheetRowBase> sheet = new SheetPrepay<SheetRowBase>(SHEET_PREPAY.EMPTY,LOAD_PURPOSE.SHOW);
            string msg = await sheet.Red(cmd, companyID, sheet_id, operID,"");
            sheet._httpClientFactory = this._httpClientFactory;
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time });
        }


        [HttpPost]
        public async Task<JsonResult> Delete([FromBody] dynamic data)
        { 
            string sheet_id = data.sheet_id;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
 
            SheetPrepay<SheetRowBase> sheet = new SheetPrepay<SheetRowBase>(SHEET_PREPAY.EMPTY, LOAD_PURPOSE.SHOW);

            string msg = await sheet.Delete(cmd, companyID, sheet_id, operID);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg,  sheet_id});

        }


        [HttpGet]
        public async Task<IActionResult> GetPrepayInfo(string operKey, string supcust_id)
        {
            var sheet_type = "YS";
            if (CPubVars.RequestV(Request, "forPayOrGet") != "")
            {
                bool forPayOrGet = Convert.ToBoolean(CPubVars.RequestV(Request, "forPayOrGet"));
                if (forPayOrGet)
                {
                   sheet_type="YF";
                }
            }
            Security.GetInfoFromOperKey(operKey, out string companyID);
            SQLQueue QQ = new SQLQueue(cmd);
            string sql = $"select sub_name,round(balance::numeric,2) balance from prepay_balance b left join cw_subject p on p.sub_id = b.sub_id where b.company_id = {companyID} and supcust_id = {supcust_id} and balance <> 0 and p.sub_type = '{sheet_type}' and is_order is not true;";
            QQ.Enqueue("prepay", sql); 
            sql = $"select mobile,sup_addr,boss_name from info_supcust where company_id = {companyID} and supcust_id = {supcust_id}";
            QQ.Enqueue("info", sql);
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            var prepay = new List<ExpandoObject>();
            dynamic info = null;
            while (QQ.Count > 0)
            {
                string sqlName = QQ.Dequeue();
                if (sqlName == "prepay")
                {
                    prepay = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "info")
                {
                    info = CDbDealer.Get1RecordFromDr(dr, false);
                }
            }
            QQ.Clear();
            return new JsonResult(new { result = "OK", prepay, info.mobile, info.sup_addr, info.boss_name });
        }

        [HttpPost]
        public async Task<IActionResult> AppendBrief([FromBody] dynamic data)
        {
            string sheetID = data.sheetID;
            string newBrief = data.newBrief;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
           
            SheetPrepay<SheetRowBase> sheet = new SheetPrepay<SheetRowBase>(SHEET_PREPAY.EMPTY, LOAD_PURPOSE.SHOW);

            string msg = await sheet.AppendBrief(cmd, companyID, sheetID, newBrief);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, });
        }


    }
}