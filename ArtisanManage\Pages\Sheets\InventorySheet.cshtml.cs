﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using ArtisanManage.Models;
using ArtisanManage.Services;
using ArtisanManage.MyJXC;
using static ArtisanManage.MyJXC.SheetSale;
using static ArtisanManage.MyJXC.SheetInventChange;
using System.Dynamic;
using Newtonsoft.Json;
using Microsoft.AspNetCore.Http;
using System.Text;
using NPOI.SS.Formula.Functions;
using System.Text.RegularExpressions;
using Match = System.Text.RegularExpressions.Match;

namespace ArtisanManage.Pages
{
    public class InventorySheetModel : PageSheetModel<SheetRowInventory>
    {
        public string SheetTitle = "";
        public InventorySheetModel(CMySbCommand cmd) : base(MenuId.sheetInvent)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"sheet_no",new DataItem(){Title="单号",UseJQWidgets=false}},
                {"sheet_id",new DataItem(){Title="sheet_id", CtrlType="hidden", FldArea="divHead"}},
                {"sheet_type",new DataItem(){Title="sheet_type", CtrlType="hidden", FldArea="divHead"}},
                {"sheetType",new DataItem(){Title="sheetType", CtrlType="hidden", FldArea="divHead"}},
                {"red_flag",new DataItem(){Title="red_flag", CtrlType="hidden", FldArea="divHead"}},

                {"seller_id",new DataItem(){FldArea="divHead",Title="盘点人员",LabelFld="seller_name",ButtonUsage="list",SqlForOptions=CommonTool.selectSellers } },//SqlForOptions="select oper_id as v,oper_name as l,py_str as z from info_operator"}},
                {"branch_id",new DataItem(){FldArea="divHead",Restrict="sheet_pd",Title="仓库",LabelFld="branch_name", ButtonUsage="list",SqlForOptions=CommonTool.selectBranch}},
                {"inventory_type",new DataItem(){FldArea="divHead",Necessary=true,Title="盘点类型", ButtonUsage="list",Value="0",Label="部分盘点",Source="[{'v':'0',l:'部分盘点'},{'v':'1',l:'整仓盘点'}]" }},
             
                //{"oper_date",new DataItem(){title="制单日期",ctrlType="jqxDateTimeInput"}},
                {"happen_time",new DataItem(){FldArea="divHead",Title="盘点时间",CtrlType="jqxDateTimeInput",PlaceHolder="当前时间" }},
                {"TempHappenTime",new DataItem(){Title="TempHappenTime", CtrlType="hidden",  FldArea="divHead"}},
                {"make_brief",new DataItem(){FldArea="divHead",Title="备    注"}},
                {"wholesale_amount",new DataItem(){FldArea="divTail",  Title="批发总额",Width="80",Hidden=true}},
                {"cost_amount_avg",new DataItem(){FldArea="divTail",  Title="加权成本",Width="80",Hidden=true}},
                {"buy_amount",new DataItem(){FldArea="divTail",  Title="进价成本",Width="80",Hidden=true}},
                {"maker_id",new DataItem(){UseJQWidgets=false, Title="制单人", Width="80"}},
                {"maker_name",new DataItem(){UseJQWidgets=false, Title="制单人", Width="80"}},
                {"make_time",new DataItem(){UseJQWidgets=false, Title="审核时间"}},
                {"approver_id",new DataItem(){UseJQWidgets=false, Title="审核人", Width="80"}},
                {"approver_name",new DataItem(){UseJQWidgets=false, Title="审核人", Width="80"}},
                {"approve_time",new DataItem(){UseJQWidgets=false, Title="审核时间"}},
                {"red_sheet_id",new DataItem(){Title="red_sheet_id", CtrlType="hidden", FldArea="divHead"}},
                //{"red_sheet_no",new DataItem(){UseJQWidgets=false, Title="red_sheet_no"}},
                {"sheet_id_red_me",new DataItem(){Title="sheet_id_red_me", CtrlType="hidden", FldArea="divHead"}},
                {"cost_price_type",new DataItem(){Title="cost_price_type", CtrlType="hidden", FldArea="divHead"}}
               // {"branch_state",new DataItem(){title="状态",labelFld="branch_status_name",labelInDB=false,value="1",label="正常", buttonUsage="list", source = "[{v:1,l:'正常'},{v:0,l:'停用'}]"}}
            };

            // m_idFld = "sheet_id"; 
            // m_tableName = "sheet_item_master";
            //  m_selectFromSQL = "from sheet_item_master sht left join info_supcust on sht.supcust_id=info_supcust.supcust_id left join info_branch on sht.branch_id=info_branch.brand_id left join (select oper_id,oper_name as order_man_name from info_operator) tb_order_man on sht.order_man=tb_order_man.oper_id where sheet_id='~ID'";
            /*
            Grids = new Dictionary<string, FormDataGrid>()
            {
                {"gridUnit" ,new FormDataGrid(){
                   Columns = new Dictionary<string, DataItem>()
                   {
                       //{"unit_no",new DataItem(){title="单位",width="100",url="../api/ItemEdit/GetUnits"}},
                       {"unit_no",new DataItem(){title="单位",width="100",SqlForOptions="select unit_no from info_item_unit",getOptionsOnLoad=true,buttonUsage="list"}},
                       {"unit_factor",new DataItem(){title="包装率",width="80"}},
                   },
                   TableName="info_item_multi_unit",
                   IdFld="item_no",
                   SelectFromSQL="from info_item_multi_unit where item_no='~ID'"
                }}
            };*/
        }
        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            var costPriceType = "3";
            if (JsonCompanySetting.IsValid())
            {
                dynamic setting = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonCompanySetting);
                if (setting != null && setting.costPriceType != null) costPriceType = setting.costPriceType;
            }
            DataItems["cost_price_type"].Value = costPriceType;
            bool seeInPrice = false;
            if (JsonOperRights.IsValid())
            {
                dynamic operRights = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonOperRightsOrig);
                if (operRights?.delicacy?.seeInPrice?.value is not null)
                    seeInPrice = ((string)operRights.delicacy.seeInPrice.value).ToLower() == "true";
            }

            if (!seeInPrice)
            {
                DataItems["cost_amount_avg"].Hidden = DataItems["cost_amount_avg"].HideOnLoad = true;
                DataItems["buy_amount"].Hidden = DataItems["buy_amount"].HideOnLoad = true;

            }
            var inventoryType = DataItems["inventory_type"].Value;
            if (inventoryType.IsValid() && inventoryType == "1")
            {
                DataItems["inventory_type"].Label = "整仓盘点";
            }

        }
        public async Task OnGet(bool forReduce)
        {
            SheetInventory sheet = new SheetInventory(LOAD_PURPOSE.SHOW);
            await InitGet(cmd, sheet);
            SheetTitle = "盘点单";
            SheetRowsJson = Newtonsoft.Json.JsonConvert.SerializeObject(sheet.SheetRows);
        }
    }
    [ApiController]
    [Route("api/[controller]/[action]")]
    public class InventorySheetController : QueryController
    {
        public InventorySheetController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            var model = new InventorySheetModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }

        [HttpGet]
        public async Task<IActionResult> GetItems(string operKey, string query)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            //var model = new SaleSheetModel(cmd);
            string flexStr = CPubVars.GetFlexLikeStr(query);
            string py_flexStr = query;
            if (query.Length >= 3) py_flexStr = flexStr;

            string sql = $"select  distinct item_name as name,i.item_id as id,py_str as zjm,string_agg(u.barcode,',') as code from info_item_prop i left join info_item_multi_unit u on i.item_id=u.item_id where i.company_id={companyID} and u.company_id={companyID} and son_mum_item is null and (item_name ilike '%{flexStr}%' or py_str ilike '%{query}%' or u.barcode ilike '%{query}%' or item_name ilike '%{query}%') group by item_name,i.item_id,py_str limit 30";

            dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            return new JsonResult(new { result = "OK", records });
        }
        [HttpGet]
        public async Task<IActionResult> GetItemInfo(string operKey, string item_id)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            // var model = new SaleSheetModel();
            string sql = $"select unit_no,unit_factor,wholesale_price as price from info_item_multi_unit where company_id={companyID} and item_id={item_id}";

            dynamic units = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            //string items=Newtonsoft.Json.JsonConvert.SerializeObject(rec); 

            return new JsonResult(new { result = "OK", item = new { item_id, units } });
        }
        [HttpGet]
        public async Task<IActionResult> GetUnits(string operKey, string item_id, string query)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            // var model = new SaleSheetModel();
            string sql = $"select  distinct unit_no,unit_factor from info_item_multi_unit where company_id={companyID} and item_id={item_id} and unit_no like '%{query}%' limit 30";

            dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            //string items=Newtonsoft.Json.JsonConvert.SerializeObject(rec); 
            return new JsonResult(new { result = "OK", records });
        }

        #region
        /*
        [HttpGet]
        public async Task<IActionResult> GetStockQtyList(string operKey, string branch_id,string items_id,bool stockOnly,bool bGetAttrs)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            // var model = new SaleSheetModel();
            var condi = "";
            var orderBy = "";
            if (items_id != null) { 
                condi = $"and ip.item_id in ({items_id})";
                orderBy = $"order by position(ip.item_id::text in '{items_id}')";
            }
            if (stockOnly) condi += $" and branch_id = {branch_id}";
            //condi += $"and branch_id = {branch_id}";
            SQLQueue QQ = new SQLQueue(cmd);
            string sql = @$"select item_id,item_name,mum_attributes,stock_qty,s_wholesale_price wholesale_price,cost_price_avg,cost_price_spec,s_buy_price buy_price,yj_get_bms_qty(stock_qty,bunit,b_unit_factor::float4,munit,m_unit_factor::float4,sunit) as current_qty,
                                    (case when m_unit_factor is null and b_unit_factor is not null then concat(s_unit_factor,bunit,'=',b_unit_factor,sunit)  
			                              when (b_unit_factor is not null) and (m_unit_factor is not null) then concat(s_unit_factor,bunit,'=',floor(b_unit_factor::numeric/m_unit_factor::numeric),munit,'=',b_unit_factor,sunit)
										  when b_unit_factor is null then concat(s_unit_factor,sunit)  end ) as unit_conv,
                                  bUnit as b_unit_no,munit as m_unit_no,sunit as unit_no,branch_id,s_barcode,b_barcode,m_barcode,m_unit_factor,b_unit_factor,bstock,mstock,sstock  
                            from(
                                SELECT ip.item_id, ip.item_name,mum_attributes,stock_qty,ip.wholesale_price,ip.cost_price_avg,ip.cost_price_spec,stock.branch_id,
                                        (case when b is not null then sign(COALESCE(stock.stock_qty,0))*floor(COALESCE(abs(stock.stock_qty),0) / (t.b->> 'f1')::numeric) else null end ) as bStock,
                                        (CASE WHEN(t.m->>'f1') is null THEN null ELSE sign(COALESCE(stock.stock_qty,0))*floor((COALESCE(abs(stock.stock_qty),0)%(t.b->>'f1')::numeric)/(t.m->>'f1')::numeric) END) as mStock,
                                        (CASE WHEN(t.b->>'f1') is NOT NULL AND(t.m->>'f1') is NOT NULL THEN sign(stock.stock_qty)*floor(COALESCE(abs(stock.stock_qty),0)%(t.b->>'f1')::numeric%(t.m->>'f1')::numeric)
			                             WHEN(t.b->>'f1') is NOT NULL AND(t.m->>'f1') is NULL THEN round(COALESCE(stock.stock_qty,0) % (t.b->> 'f1')::numeric)
                                         WHEN(t.b->>'f1') is NULL AND (t.m->>'f1') is NULL THEN round(COALESCE(stock.stock_qty,0)) END) sStock,
                                        (t.s->>'f1') as s_unit_factor, (t.s->> 'f2') as sUnit, t.s->>'f3' s_barcode,s->>'f4' as s_wholesale_price,t.s->>'f6' s_buy_price,
                                        (t.b->>'f1') as b_unit_factor, (t.b->> 'f2') as bUnit, t.b->>'f3' b_barcode,s->>'f4' as b_wholesale_price,t.b->>'f6' b_buy_price,
                                        (t.m->>'f1') as m_unit_factor, (t.m->> 'f2') as mUnit, t.m->>'f3' m_barcode                                FROM info_item_prop as ip
                                LEFT JOIN
                                (select item_id, s, m, b from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,barcode,wholesale_price,retail_price,buy_price)) as json from info_item_multi_unit where company_id = {companyID} ORDER BY item_id',$$values('s'::text),('m'::text),('b'::text)$$) 
                                 as errr(item_id int, s jsonb,m jsonb, b jsonb)) t
                                 on ip.item_id = t.item_id
 
                                LEFT JOIN (select * from stock where company_id = {companyID} and branch_id = {branch_id} ) stock on ip.item_id = stock.item_id
                                LEFT JOIN (select * from info_item_class where company_id = {companyID}) as ic on ip.item_class = ic.class_id where  ip.company_id={companyID}  {condi}
                           {orderBy}
                           ) tem ";
            QQ.Enqueue("items", sql);

            sql = @$" 
SELECT array_to_json(array_agg(row_to_json(t))) attr_options FROM
(
    SELECT opt_id, opt_name, attr.attr_id FROM info_attr_opt opt left join info_attribute attr on opt.attr_id = attr.attr_id where opt.company_id ={ companyID} and not attr.spec_opt_in_item order by opt.order_index
) t";
            QQ.Enqueue("attr_options", sql);

            if (bGetAttrs)
            {
                sql = $"select attr_id,attr_name,distinct_stock,order_index from info_attribute where company_id={companyID} order by order_index";
                QQ.Enqueue("attrs", sql);
            }
            dynamic records = null;
            List <ExpandoObject> units = null;
            List<ExpandoObject> attributes = null;
            dynamic attrOptions = null;
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                string tbl = QQ.Dequeue();
                if (tbl == "items")
                {
                    records = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (tbl == "attr_options")
                {
                    dr.Read();
                    string s = CPubVars.GetTextFromDr(dr, "attr_options");
                    if (s != "")
                    {
                        attrOptions = JsonConvert.DeserializeObject(s);
                    }
                }
                else if (tbl == "attrs")
                {
                    attributes = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();
            return new JsonResult(new { result = "OK", records,attributes,attrOptions });
        }
        */
        #endregion

        [HttpPost]
        public async Task<IActionResult> GetStockQtyList_old([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            string branch_id = data.branch_id;
            string items_id = data.items_id;
            bool stockOnly = data.stockOnly ?? false;
            bool bGetAttrs = data.bGetAttrs ?? false;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            // var model = new SaleSheetModel();
            var condi = "";
            var orderBy = "";
            if (items_id != null)
            {
                condi = $"and ip.item_id in ({items_id})";
                orderBy = $"order by position(ip.item_id::text in '{items_id}')";
            }
            if (stockOnly) condi += $" and branch_id = {branch_id}";

            //condi += $"and branch_id = {branch_id}";
            SQLQueue QQ = new SQLQueue(cmd);
            string sql = @$"
select item_id,item_name,batch_stock,mum_attributes,stock_qty,s_wholesale_price wholesale_price,cost_price_avg,cost_price_spec,s_buy_price buy_price,yj_get_bms_qty(stock_qty,bunit,b_unit_factor::float4,munit,m_unit_factor::float4,sunit) as current_qty,
                                    yj_get_bms_qty (COALESCE (sell_pend_qty, 0)::NUMERIC, b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no ) AS sell_pend_qty,
                                    yj_get_bms_qty (COALESCE (invent_time_stock_qty - sell_pend_qty, 0)::NUMERIC, b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no ) AS avail_qty,
                                    (case when m_unit_factor is null and b_unit_factor is not null then concat(s_unit_factor,bunit,'=',b_unit_factor,sunit)  
			                              when (b_unit_factor is not null) and (m_unit_factor is not null) then concat(s_unit_factor,bunit,'=',floor(b_unit_factor::numeric/m_unit_factor::numeric),munit,'=',b_unit_factor,sunit)
										  when b_unit_factor is null then concat(s_unit_factor,sunit)  end ) as unit_conv,
                                  bUnit as b_unit_no,munit as m_unit_no,sunit as unit_no,branch_id,s_barcode,b_barcode,m_barcode,m_unit_factor,b_unit_factor,bstock,mstock,sstock  
from(
    SELECT ip.item_id, ip.item_name,mum_attributes,stock_qty,ip.wholesale_price,ip.cost_price_avg,ip.cost_price_spec,stock.branch_id,
        (case when b is not null then sign(COALESCE(stock.stock_qty,0))*floor(COALESCE(abs(stock.stock_qty),0) / (t.b->> 'f1')::numeric) else null end ) as bStock,
        (CASE WHEN(t.m->>'f1') is null THEN null ELSE sign(COALESCE(stock.stock_qty,0))*floor((COALESCE(abs(stock.stock_qty),0)%(t.b->>'f1')::numeric)/(t.m->>'f1')::numeric) END) as mStock,
        (CASE WHEN(t.b->>'f1') is NOT NULL AND(t.m->>'f1') is NOT NULL THEN sign(stock.stock_qty)*floor(COALESCE(abs(stock.stock_qty),0)%(t.b->>'f1')::numeric%(t.m->>'f1')::numeric)
			WHEN(t.b->>'f1') is NOT NULL AND(t.m->>'f1') is NULL THEN round(COALESCE(stock.stock_qty,0) % (t.b->> 'f1')::numeric)
            WHEN(t.b->>'f1') is NULL AND (t.m->>'f1') is NULL THEN round(COALESCE(stock.stock_qty,0)) END) sStock,
        (t.s->>'f1') as s_unit_factor, (t.s->> 'f2') as sUnit, t.s->>'f3' s_barcode,s->>'f4' as s_wholesale_price,t.s->>'f6' s_buy_price,
        (t.b->>'f1') as b_unit_factor, (t.b->> 'f2') as bUnit, t.b->>'f3' b_barcode,s->>'f4' as b_wholesale_price,t.b->>'f6' b_buy_price,
        (t.m->>'f1') as m_unit_factor, (t.m->> 'f2') as mUnit, t.m->>'f3' m_barcode                          
    FROM info_item_prop as ip
    LEFT JOIN
    (
        select item_id, s, m, b from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,barcode,wholesale_price,retail_price,buy_price)) as json from info_item_multi_unit where company_id = {companyID} ORDER BY item_id',$$values('s'::text),('m'::text),('b'::text)$$) 
           as errr(item_id int, s jsonb,m jsonb, b jsonb)
    ) t
    on ip.item_id = t.item_id
    LEFT JOIN 
    (
       /* NEED_NOTICE
        select s.company_id,case when son_mum_item is null then s.item_id else son_mum_item end item_id,branch_id,sum(stock_qty) as stock_qty from stock s
        LEFT JOIN info_item_prop ip on {companyID} = ip.company_id and s.item_id = ip.item_id
         where s.company_id = {companyID} and branch_id = {branch_id}
         GROUP BY s.company_id ,case when son_mum_item is null then s.item_id else son_mum_item end,branch_id
       */
        select company_id,item_id,stock_qty,branch_id FROM stock where company_id =  {companyID} and branch_id = {branch_id}
    ) stock on ip.item_id = stock.item_id
 left join(
                                select s.item_id,json_agg(json_build_object('stock_qty',COALESCE(stock_qty,0),'batch_id',itb.batch_id,'produce_date',itb.produce_date,'batch_no',itb.batch_no,
                                'stock_qty_unit',unit_from_s_to_bms (COALESCE(stock_qty,0)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no),
                                'sell_pend_qty_unit',unit_from_s_to_bms (COALESCE(sell_pend_qty,0)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no),
                                'usable_stock_qty_unit',unit_from_s_to_bms (COALESCE(stock_qty,0)::numeric-COALESCE(sell_pend_qty,0)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)
                            )) as batch_stock
                                    from stock s
                                    left join info_item_batch itb on itb.company_id ={companyID} and itb.batch_id = s.batch_id
                                    left join (select item_id,(b->>'f1')::real as b_unit_factor,(m->>'f1')::real as m_unit_factor,(s->>'f1')::real as s_unit_factor,
                                                                                     b->>'f2' as b_unit_no,m->>'f2' as m_unit_no,s->>'f2' as s_unit_no,s->>'f3' as s_buy_price
                                                                        from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,buy_price)) as json from info_item_multi_unit where company_id={companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
                                                                            as errr(item_id int, s jsonb,m jsonb,b jsonb)) mu1 on s.item_id = mu1.item_id
                                    where s.company_id = {companyID} and branch_id={branch_id} and stock_qty is not null group by s.item_id
                            )ss on ss.item_id = mu1.item_id    
    LEFT JOIN 
    (
        select * from info_item_class where company_id = {companyID}
    ) ic on ip.item_class = ic.class_id 
    where ip.company_id={companyID} and (coalesce(ip.status,'1')='1' or stock.stock_qty<>0)  {condi}
                           {orderBy}
                           ) tem ";
            QQ.Enqueue("items", sql);

            sql = @$" 
SELECT array_to_json(array_agg(row_to_json(t))) attr_options FROM
(
    SELECT opt_id, opt_name, attr.attr_id FROM info_attr_opt opt left join info_attribute attr on opt.attr_id = attr.attr_id where opt.company_id ={companyID} and not attr.spec_opt_in_item order by opt.order_index
) t";
            QQ.Enqueue("attr_options", sql);

            if (bGetAttrs)
            {
                sql = $"select attr_id,attr_name,distinct_stock,order_index from info_attribute where company_id={companyID} order by order_index";
                QQ.Enqueue("attrs", sql);
            }
            dynamic records = null;
            List<ExpandoObject> units = null;
            List<ExpandoObject> attributes = null;
            dynamic attrOptions = null;
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                string tbl = QQ.Dequeue();
                if (tbl == "items")
                {
                    records = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (tbl == "attr_options")
                {
                    dr.Read();
                    string s = CPubVars.GetTextFromDr(dr, "attr_options");
                    if (s != "")
                    {
                        attrOptions = JsonConvert.DeserializeObject(s);
                    }
                }
                else if (tbl == "attrs")
                {
                    attributes = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();
            return new JsonResult(new { result = "OK", records, attributes, attrOptions });
        }

        //5种情况会调用getStockQtyList

        //1.手动选择商品 stockOnly=false
        //2.点击加载商品 stockOnly=true 
        //3.导入EXCEL盘点数据 stockOnly=false
        //4.整仓盘点时检查是否有有库存的商品没有加入盘点 stockOnly=true
        //5.保存或审核前检查系统数据是否变化  stockOnly=false
        //

        
        [HttpPost]
        public async Task<IActionResult> GetStockQtyList()
        {
            string branch_id = Request.Form["branch_id"].ToString();
            string items_id = Request.Form["items_id"];
            bool stockOnly = Request.Form["stockOnly"] == "true";  //stockOnly:1.选商品时getItemInfo  stockOnly为false  2.保存或审核前检查的时候 stockOnly为false 3.
			bool ignoreZeroStock = Request.Form["ignoreZeroStock"] == "true";
			bool bGetAttrs = Request.Form["bGetAttrs"] == "true";
            bool bImportFile = Request.Form["importFile"] == "true";  //importFile:是否是导入EXCEL
            bool bWholeStock = Request.Form["wholeStock"] == "true";  //wholeStock只有在importFile 为True时才起作用，true代表不在excel中的商品只要有库存也加入盘点
            string orderByStr = Request.Form["orderBy"];

            string inventTime = Request.Form["happen_time"];
            string limit0StockCondi = "and coalesce(m.stock_qty,0)<>0";

			if (string.IsNullOrEmpty(inventTime))
            {
                inventTime = CPubVars.GetDateText(DateTime.Now.AddYears(10));
            }
            else
            {
                limit0StockCondi = "";
			}
                //  DateTime minHappenTime = Convert.ToDateTime(inventTime);//用来提高查询速度,假定不会开三个月以前的单据

            CMySbDataReader dr;

            string companyID = Token.CompanyID;
			string orderedItemsID = "";
			string result = "OK", msg = "";

            string importItemsName = "";
            string importItemsBarcode = "";

            List<DataRow> lstImportItemRows = new List<DataRow>();
            Dictionary<string, dynamic> dicMatchedItems = new Dictionary<string, dynamic>();
            if (bImportFile)
            {
                items_id = "";
                var import_info_stock = new KeyValuePair<string, string>[]
                {
                    new KeyValuePair<string, string>("商品名称","item_name"),
                    new KeyValuePair<string, string>("条码","barcode"),
                    new KeyValuePair<string, string>("生产日期","produce_date"),
                    new KeyValuePair<string, string>("批次","batch_no"),
                    new KeyValuePair<string, string>("小单位数","real_quantity"),
                    new KeyValuePair<string, string>("多单位数量","unit_quantity"),
                };

                try
                {
                    IFormFile file = Request.Form.Files[0];
                    var uploadDir = Environment.CurrentDirectory + $"/wwwroot/uploads/{DateTime.Today:yyyyMM}/";

                    await file.UploadAsync(uploadDir);

                    var path = uploadDir + file.FileName;

                    NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                    logger.Error("in ImportStock. path:" + path);

                    var stock_lk = ExcelHelper.ReadExcel(path, "库存表");
                    var stockTable = stock_lk.GetPartialTable(import_info_stock, "stock");

                    lstImportItemRows = stockTable.DataRows;
                    var sbarcodeList = new List<string>();
                    lstImportItemRows.ForEach(row => {
                        if (row.GetCell("item_name").Value.IsInvalid() && row.GetCell("barcode").Value.IsInvalid()) return;
                        if (row.GetCell("item_name").Value.IsValid())
                        {
                            if (importItemsName != "") importItemsName += ",";
                            importItemsName += "'" + row.GetCell("item_name").Value + "'";
                        }


                        if (row.GetCell("barcode").Value.IsValid())
                        {
                            if (importItemsBarcode != "") importItemsBarcode += ",";
                            importItemsBarcode += "'" + row.GetCell("barcode").Value + "'";
                        }

                    });


                }
                catch (MyException ex)
                {
                    result = "Failed"; msg = ex.Message;
                }

                if (msg != "")
                    return new JsonResult(new { result = "Error", msg });
                string sqlQuery = "";
                if (importItemsName != "")
                {
                    //string pattern = @"\s+";
                    //string replacement = " ";
                    //importItemsName = Regex.Replace(importItemsName, pattern, replacement);
                    //sqlQuery = $@"select item_id,item_name from (select item_id,REGEXP_REPLACE(item_name, ' +', ' ', 'g') AS item_name from info_item_prop where company_id={companyID}) t where t.item_name in ({importItemsName}) ";
                    sqlQuery = $@"select item_id,item_name from info_item_prop where company_id={companyID} and ( item_name in ({importItemsName})) ";

                }
                if (importItemsBarcode != "")
                {
                    if (sqlQuery != "") sqlQuery += " union ";
                    sqlQuery += $"SELECT iip.item_id,item_name FROM info_item_prop iip   LEFT JOIN info_item_multi_unit iimu on iip.item_id = iimu.item_id and iimu.company_id = iip.company_id where iip.company_id = {companyID}  and iimu.barcode in ({importItemsBarcode})";
                }
                cmd.CommandText = sqlQuery;
                // cmd.CommandText=@$"select item_id,item_name from info_item_prop where company_id={companyID} and ( item_name in ({importItemsName})) 
                //                 UNION  
                //                 (SELECT iip.item_id,item_name FROM info_item_prop iip   LEFT JOIN info_item_multi_unit iimu on iip.item_id = iimu.item_id and iimu.company_id = iip.company_id where iip.company_id = {companyID}  and iimu.barcode in ({importItemsBarcode}))";
                dr = await cmd.ExecuteReaderAsync();
                Dictionary<string, string> dicItems = new Dictionary<string, string>();
                while (dr.Read())
                {
                    string item_id = CPubVars.GetTextFromDr(dr, "item_id");
                    string item_name = CPubVars.GetTextFromDr(dr, "item_name");
                    if (!dicItems.ContainsKey(item_name)) dicItems.Add(item_name, item_id);

                }
                dr.Close();

                string notFoundItems = "";
                int nNotMatched = 0;
               
                lstImportItemRows.ForEach(row => {
                    string item_name = row.GetCell("item_name").Value;
                    if (item_name.IsInvalid()) return;
                    if (!dicItems.ContainsKey(item_name))
                    {
                        if (notFoundItems != "") notFoundItems += "<br>";
                        notFoundItems += item_name;
                        nNotMatched++;
                    }
                    else
                    {
                        if (orderedItemsID != "") orderedItemsID += ",";
                        orderedItemsID += dicItems[item_name];

					}
                });

                items_id = string.Join(",", dicItems.Values);

                if (notFoundItems != "")
                {
                    msg = "以下" + nNotMatched.ToString() + "个商品在系统中没有找到:" + notFoundItems;
                }
                else if (items_id == "")
                {
                    msg = "导入的EXCEL表中没有记录";
                }
                if (msg != "")
                    return new JsonResult(new { result = "Error", msg });
            }

            var condi = "";

            /* 修改排序功能，按商品类别排序*/
            var orderBy = @"order by  
                cls1.order_index,cls1.class_name,cls2.order_index,cls2.class_name,cls3.order_index,cls3.class_name,
                ip.item_order_index;";

            if (!string.IsNullOrEmpty(items_id))
            { 
                if (bWholeStock)//只有在bImportFile时起作用，代表整仓盘点时要把不在EXCEL中但是有库存的商品也加载出来
                {
                    condi = $" and (ip.item_id in ({items_id}) or s.branch_id is not null) ";
                }
                else
                    condi = $" and ip.item_id in ({items_id}) ";
                orderBy = $"order by position(ip.item_id::text in '{orderedItemsID}')";
            }
            else if (stockOnly)
            {
                condi += $" and s.branch_id is not null";
            }
          
            if (ignoreZeroStock)
            {
				condi += $" and s.now_stock_qty<>0 ";
			}
           
			if (!string.IsNullOrEmpty(orderByStr))
            {
                orderBy = $"order by {orderByStr}";
            }

            SQLQueue QQ = new SQLQueue(cmd);
            //string statusCondi = "coalesce(ip.status,'1')='1' or ";
            string statusCondi = " false or ";
            if (bImportFile) statusCondi = " true or ";

            string sql = @$"
SELECT
	ip.item_id, item_name,item_spec,ip.item_no,batch_id,SUBSTRING(COALESCE(produce_date::text,''),1,10) produce_date,COALESCE(batch_no,'') as batch_no,mum_attributes,invent_time_stock_qty stock_qty,cost_price_avg,cost_price_spec,s_wholesale_price wholesale_price,s_buy_price buy_price,ip.batch_level,
    yj_get_bms_qty (COALESCE (invent_time_stock_qty, 0)::NUMERIC, b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no ) AS current_qty,
    yj_get_bms_qty (COALESCE (sell_pend_qty, 0)::NUMERIC, b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no ) AS sell_pend_qty,
    yj_get_bms_qty (COALESCE (invent_time_stock_qty - sell_pend_qty, 0)::NUMERIC, b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no ) AS avail_qty,
    yj_get_unit_relation(b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no) as unit_conv,
	b_unit_no, m_unit_no, s_unit_no, branch_id, s_barcode, b_barcode, m_barcode, m_unit_factor, b_unit_factor,
	yj_get_unit_qty ( 'b', invent_time_stock_qty::NUMERIC, b_unit_factor, m_unit_factor ,false) bstock,
	yj_get_unit_qty ( 'm', invent_time_stock_qty::NUMERIC, b_unit_factor, m_unit_factor ,false) mstock,
	yj_get_unit_qty ( 's', invent_time_stock_qty::NUMERIC, b_unit_factor, m_unit_factor ,false) sstock,
    branch_position,branch_position_name,
    cls2.class_name as class_name

FROM info_item_prop ip
LEFT JOIN
(
    SELECT s.item_id AS item_id,s.batch_id,produce_date,batch_no, branch_id,sell_pend_qty, stock_qty now_stock_qty,COALESCE ( stock_qty - COALESCE ( xs_qty, 0 ) - COALESCE ( cg_qty, 0 ) - COALESCE ( yk_qty, 0 ) - COALESCE ( db_add_qty, 0 ) + COALESCE ( db_reduce_qty, 0 ) - COALESCE ( zz_add_qty, 0 ) + COALESCE ( zz_reduce_qty, 0 ) - COALESCE(cr_qty,0) ,0) AS invent_time_stock_qty
,s.branch_position,branch_position_name 
    FROM
    (
        SELECT item_id, stock_qty,sell_pend_qty,M.branch_id,M.batch_id,batch_no,produce_date,M.branch_position,branch_position_name FROM stock M 
	    left join info_item_batch itb on itb.company_id = {companyID} and itb.batch_id = M.batch_id
        LEFT JOIN info_branch_position ibp on ibp.company_id = {companyID}  and ibp.branch_position = M.branch_position and M.branch_id = ibp.branch_id
        WHERE M.company_id = {companyID} AND M.branch_id = {branch_id} {limit0StockCondi}
    ) s
    LEFT JOIN
    (
        SELECT d.item_id AS item_id, SUM (quantity * unit_factor * inout_flag) AS xs_qty ,COALESCE(batch_id,0) as batch_id,COALESCE(branch_position,0) as branch_position
        FROM sheet_sale_detail d
        LEFT JOIN sheet_sale_main M ON d.sheet_id = M.sheet_id AND d.company_id = M.company_id 
        WHERE  d.company_id = {companyID} AND red_flag IS NULL  AND M.approve_time IS NOT NULL AND COALESCE(d.branch_id,M.branch_id) = {branch_id} and m.happen_time>'{inventTime}'  and d.happen_time>'{inventTime}' AND M.is_imported IS NULL
        GROUP BY d.item_id ,COALESCE(batch_id,0),COALESCE(branch_position,0)
    ) xs ON s.item_id = xs.item_id and s.batch_id = xs.batch_id and s.branch_position = xs.branch_position
    LEFT JOIN 
    (
        SELECT d.item_id AS item_id, SUM (quantity * unit_factor*inout_flag) AS cg_qty,COALESCE(batch_id,0) as batch_id,COALESCE(branch_position,0) as branch_position
        FROM sheet_buy_detail d
        LEFT JOIN sheet_buy_main M ON d.company_id = M.company_id  and d.sheet_id = M.sheet_id
        WHERE  d.company_id = {companyID} AND red_flag IS NULL  AND M.approve_time IS NOT NULL  AND COALESCE(d.branch_id,M.branch_id) = {branch_id} and m.happen_time>'{inventTime}'  and d.happen_time>'{inventTime}'  
        GROUP BY d.item_id ,COALESCE(batch_id,0),COALESCE(branch_position,0)
    ) cg ON s.item_id = cg.item_id and s.batch_id = cg.batch_id and s.branch_position = cg.branch_position
    LEFT JOIN 
    (
        SELECT d.item_id AS item_id, SUM (quantity * unit_factor*inout_flag) AS yk_qty,COALESCE(batch_id,0) as batch_id,COALESCE(branch_position,0) as branch_position
        FROM sheet_invent_change_detail d
        LEFT JOIN sheet_invent_change_main M ON d.company_id = M.company_id and d.sheet_id = M.sheet_id  
        WHERE  d.company_id = {companyID}  and red_flag IS NULL AND M.approve_time IS NOT NULL AND M.branch_id = {branch_id}  and m.happen_time>'{inventTime}'  and d.happen_time>'{inventTime}'
        GROUP BY d.item_id ,COALESCE(batch_id,0),COALESCE(branch_position,0)
    ) yk ON s.item_id = yk.item_id and s.batch_id = yk.batch_id and s.branch_position = yk.branch_position
    LEFT JOIN 
    (
        SELECT d.item_id AS item_id,SUM ( quantity * unit_factor ) AS db_add_qty ,COALESCE(batch_id,0) as batch_id,COALESCE(to_branch_position,0) as branch_position
        FROM sheet_move_detail d
        LEFT JOIN sheet_move_main M ON  d.company_id = M.company_id AND d.sheet_id = M.sheet_id 
        WHERE d.company_id = {companyID} and red_flag IS NULL AND M.approve_time IS NOT NULL AND to_branch_id = {branch_id} and m.happen_time>'{inventTime}'  and d.happen_time>'{inventTime}'
        GROUP BY d.item_id ,COALESCE(batch_id,0),COALESCE(to_branch_position,0)
    ) dr ON s.item_id = dr.item_id and s.batch_id = dr.batch_id and s.branch_position = dr.branch_position
    LEFT JOIN 
    (
        SELECT d.item_id AS item_id,SUM ( quantity * unit_factor ) AS db_reduce_qty ,COALESCE(batch_id,0) as batch_id,COALESCE(from_branch_position,0) as branch_position
        FROM sheet_move_detail d
        LEFT JOIN sheet_move_main M ON d.company_id = M.company_id  and d.sheet_id = M.sheet_id  
        WHERE d.company_id = {companyID}  and red_flag IS NULL AND M.approve_time IS NOT NULL AND from_branch_id = {branch_id} and m.happen_time>'{inventTime}' and d.happen_time>'{inventTime}'
        GROUP BY d.item_id ,COALESCE(batch_id,0),COALESCE(from_branch_position,0)
    ) dc ON s.item_id = dc.item_id and s.batch_id = dc.batch_id and s.branch_position = dc.branch_position
    LEFT JOIN 
        (
            SELECT d.item_id AS item_id,SUM ( quantity * unit_factor ) AS zz_reduce_qty ,COALESCE(batch_id,0) as batch_id,COALESCE(branch_position,0) as branch_position
            FROM sheet_combine_detail d
            LEFT JOIN sheet_combine_main M ON d.company_id = M.company_id  and d.sheet_id = M.sheet_id  
            WHERE d.company_id = {companyID}  and red_flag IS NULL AND M.approve_time IS NOT NULL AND from_branch_id = {branch_id} and m.happen_time>'{inventTime}' and d.happen_time>'{inventTime}' and inout_flag = -1
            GROUP BY d.item_id ,COALESCE(batch_id,0),COALESCE(branch_position,0)
        ) zzc ON s.item_id = zzc.item_id and s.batch_id = zzc.batch_id and s.branch_position = zzc.branch_position
    LEFT JOIN 
        (
            SELECT d.item_id AS item_id,SUM ( quantity * unit_factor ) AS zz_add_qty ,COALESCE(batch_id,0) as batch_id,COALESCE(branch_position,0) as branch_position
            FROM sheet_combine_detail d
            LEFT JOIN sheet_combine_main M ON d.company_id = M.company_id  and d.sheet_id = M.sheet_id  
            WHERE d.company_id = {companyID}  and red_flag IS NULL AND M.approve_time IS NOT NULL AND to_branch_id = {branch_id} and m.happen_time>'{inventTime}' and d.happen_time>'{inventTime}' and inout_flag = 1
            GROUP BY d.item_id ,COALESCE(batch_id,0),COALESCE(branch_position,0)
        ) zzr ON s.item_id = zzr.item_id and s.batch_id = zzr.batch_id and s.branch_position = zzr.branch_position
    LEFT JOIN 
        (
            SELECT d.item_id AS item_id,SUM ( quantity * unit_factor * inout_flag) AS cr_qty ,COALESCE(batch_id,0) as batch_id,COALESCE(branch_position,0) as branch_position
            FROM sheet_stock_in_out_detail d
            LEFT JOIN sheet_stock_in_out_main M ON d.company_id = M.company_id  and d.sheet_id = M.sheet_id  
            WHERE d.company_id = {companyID}  and red_flag IS NULL AND M.approve_time IS NOT NULL AND M.  branch_id = {branch_id} and m.happen_time>'{inventTime}' and d.happen_time>'{inventTime}'
            GROUP BY d.item_id ,COALESCE(batch_id,0),COALESCE(branch_position,0)
        ) cr ON s.item_id = cr.item_id and s.batch_id = cr.batch_id and s.branch_position = cr.branch_position
) s 
ON ip.item_id = s.item_id 
LEFT JOIN
(
    select item_id, 
            (b->>'f1')::numeric as b_unit_factor, b->>'f2' as b_unit_no, b->>'f3' b_barcode,
            (m->>'f1')::numeric as m_unit_factor, m->>'f2' as m_unit_no, m->>'f3' m_barcode,
            (s->>'f1')::numeric as s_unit_factor, s->>'f2' as s_unit_no ,s->>'f3' s_barcode,s->>'f4' AS s_wholesale_price,s->>'f6' s_buy_price
    from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,barcode,wholesale_price,retail_price,buy_price)) as json from info_item_multi_unit where company_id= {companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
        as errr(item_id int, s jsonb,m jsonb,b jsonb) 
) t on ip.item_id=t.item_id
LEFT JOIN  info_item_class cls1 on REGEXP_REPLACE(SPLIT_PART(ip.other_class, '/', 2), '^$', '0')::int = cls1.class_id and cls1.company_id = {companyID}
LEFT JOIN  info_item_class cls2 on REGEXP_REPLACE(SPLIT_PART(ip.other_class, '/', 3), '^$', '0')::int = cls2.class_id and cls2.company_id = {companyID}
LEFT JOIN  info_item_class cls3 on REGEXP_REPLACE(SPLIT_PART(ip.other_class, '/', 4), '^$', '0')::int = cls3.class_id and cls3.company_id = {companyID}

where ip.company_id={companyID} and ({statusCondi} true) {condi}
{orderBy}

            ";


            QQ.Enqueue("items", sql);

            sql = @$" 
SELECT array_to_json(array_agg(row_to_json(t))) attr_options FROM
(
    SELECT opt_id, opt_name, attr.attr_id FROM info_attr_opt opt left join info_attribute attr on opt.attr_id = attr.attr_id where opt.company_id ={companyID} and not attr.spec_opt_in_item order by opt.order_index
) t";
            QQ.Enqueue("attr_options", sql);
            if (bGetAttrs)
            {
                sql = $"select attr_id,attr_name,distinct_stock,order_index from info_attribute where company_id={companyID} order by order_index";
                QQ.Enqueue("attrs", sql);
            }
            List<ExpandoObject> records = null;
            // List<ExpandoObject> units = null;
            List<ExpandoObject> attributes = null;
            dynamic attrOptions = null;
            //MyLogger.LogSQL("report", QQ.SQL, companyID,"");
            //  MyLogger.LogFileMsg("getstockQty:"+QQ.SQL,companyID);

            dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                string tbl = QQ.Dequeue();
                if (tbl == "items")
                {
                    records = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (tbl == "attr_options")
                {
                    dr.Read();
                    string s = CPubVars.GetTextFromDr(dr, "attr_options");
                    if (s != "")
                    {
                        attrOptions = JsonConvert.DeserializeObject(s);
                    }
                }
                else if (tbl == "attrs")
                {
                    attributes = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();

            //var lstRecordsForImport = new List<ExpandoObject>();
            #region set s_unit_qty of rows according to imported excel file
            //records和lstImportItemRows总和
            //int containFlag = 0;

            foreach (dynamic item in records)
            {
                Console.Write(item.item_name);
                string item_name = item.item_name;
                // string barcode = item.barcode;
                string batch_id = item.batch_id;
                string produce_date = item.produce_date == null ? "" : item.produce_date;
                string batch_no = item.batch_no == null ? "" : item.batch_no;
                if (!dicMatchedItems.ContainsKey(item_name))
                {
                    dicMatchedItems.Add(item_name, item);
                }
                var qty = "";
                var unit_qty = "";

                foreach (var lst in lstImportItemRows)
                {
                    if (lst.GetCell("item_name").Value == null) lst.GetCell("item_name").Value = "";
                    if (lst.GetCell("barcode").Value == null) lst.GetCell("barcode").Value = "";
                    if (lst.GetCell("produce_date").Value == null) lst.GetCell("produce_date").Value = "";
                    if (lst.GetCell("batch_no").Value == null) lst.GetCell("batch_no").Value = "";
                    if (lst.GetCell("real_quantity").Value == null) lst.GetCell("real_quantity").Value = "";
                    if (lst.GetCell("unit_quantity").Value == null) lst.GetCell("unit_quantity").Value = "";
                    string excelItemName = lst.GetCellValue("item_name");

                    if (excelItemName == item_name)
                    {
                        if (lst.GetCellValue("produce_date") == produce_date && lst.GetCellValue("batch_no") == batch_no)
                        {
                            qty = lst.GetCellValue("real_quantity");
                            unit_qty = lst.GetCellValue("unit_quantity");
                            produce_date = lst.GetCellValue("produce_date");
                            batch_no = lst.GetCellValue("batch_no");

                        }
                    }

                }

                lstImportItemRows.RemoveAll(lst => lst.GetCellValue("item_name") == item_name && lst.GetCellValue("produce_date") == produce_date && lst.GetCellValue("batch_no") == batch_no);

                var item_id = item.item_id;


                if (qty.IsInvalid() && unit_qty.IsValid())
                {
                    float b_qty = 0;
                    float nQty = 0;
                    float b_unit_factor = 0;
                    if (item.b_unit_factor != "") b_unit_factor = Convert.ToSingle(item.b_unit_factor);
                    float m_unit_factor = 0;
                    if (item.m_unit_factor != "") m_unit_factor = Convert.ToSingle(item.m_unit_factor);
                    string[] arr;

                    string b_unit_no = item.b_unit_no;
                    string m_unit_no = item.m_unit_no;
                    string s_unit_no = item.s_unit_no;
                    string pattern = "[^0-9.]+"; // 匹配所有非数字和小数点的字符

                    // 创建正则表达式对象
                    Regex regex = new Regex(pattern);
                    //正则表达式匹配
                    MatchCollection matches = regex.Matches(unit_qty);

                    // 遍历得到的单位
                    foreach (Match match in matches)
                    {
                        //与现有的单位进行比较
                        if (match.Value != s_unit_no && match.Value != m_unit_no && match.Value != b_unit_no)
                        {
                            msg += $"{item.item_name}不存在 '{match.Value}' 单位<br>";
                        }
                    }
                    //如果msg不为空，表示有商品单位不存在
                    if (msg.IsValid())
                    {
                        continue;
                    }

                    if (b_unit_no != "" && s_unit_no.Contains(b_unit_no))
                    {
                        unit_qty = unit_qty.Replace(s_unit_no, "s");
                        s_unit_no = "s";
                    }

                    if (b_unit_no != "" && m_unit_no != "" && m_unit_no.Contains(b_unit_no))
                    {
                        unit_qty = unit_qty.Replace(m_unit_no, "m");
                        m_unit_no = "m";
                    }

                    if (m_unit_no != "" && s_unit_no.Contains(m_unit_no))
                    {
                        unit_qty = unit_qty.Replace(s_unit_no, "s");
                        s_unit_no = "s";
                    }


                    if (b_unit_no != "")
                    {
                        arr = unit_qty.Split(b_unit_no);
                        if (arr.Length == 2)
                        {
                            b_qty = Convert.ToSingle(arr[0]);
                            nQty += b_qty * b_unit_factor;
                            unit_qty = arr[1];
                        }
                    }

                    if (m_unit_no != "")
                    {
                        arr = unit_qty.Split(m_unit_no);
                        if (arr.Length == 2)
                        {
                            try
                            {
                                float m_qty = Convert.ToSingle(arr[0]);
                                nQty += m_qty * m_unit_factor;
                                unit_qty = arr[1];
                            }
                            catch (Exception e)
                            {
                                return new JsonResult(new { result = "Error", msg = $"{item_name}中单位数量格式不正确" });
                            }

                        }
                    }

                    arr = unit_qty.Split(s_unit_no);
                    if (arr.Length == 2)
                    {
                        float s_qty = Convert.ToSingle(arr[0]);
                        nQty += s_qty;
                    }
                    qty = CPubVars.FormatMoney(nQty, 4);
                }
                item.s_unit_qty = qty;
                item.real_quantity = qty;
                item.produce_date = produce_date;
                item.batch_no = batch_no;
                //var row = lstImportItemRows.FirstOrDefault(x => x.Columns[0].Value == item_name && x.Columns[1].Value == produce_date && x.Columns[2].Value == batch_no);
            }
            //返回又错误的商品
            if (msg.IsValid()) return new JsonResult(new { result = "Error", msg = msg });
            if (lstImportItemRows.Count > 0)
            {
                foreach (dynamic keyValue in dicMatchedItems)
                {
                    foreach (var lst in lstImportItemRows)
                    {
                        if (lst.GetCellValue("item_name") == keyValue.Key)
                        {
                            var item = JsonConvert.DeserializeObject<ExpandoObject>(JsonConvert.SerializeObject(keyValue.Value));
                            var qty = lst.GetCellValue("real_quantity");
                            var unit_qty = lst.GetCellValue("unit_quantity");
                            if (qty.IsInvalid() && unit_qty.IsValid())
                            {
                                float b_qty = 0;
                                float nQty = 0;
                                float b_unit_factor = 0;
                                if (item.b_unit_factor != "") b_unit_factor = Convert.ToSingle(item.b_unit_factor);
                                float m_unit_factor = 0;
                                if (item.m_unit_factor != "") m_unit_factor = Convert.ToSingle(item.m_unit_factor);
                                string[] arr;

                                string b_unit_no = item.b_unit_no;
                                string m_unit_no = item.m_unit_no;
                                string s_unit_no = item.s_unit_no;

                                if (b_unit_no != "" && s_unit_no.Contains(b_unit_no))
                                {
                                    unit_qty = unit_qty.Replace(s_unit_no, "s");
                                    s_unit_no = "s";
                                }

                                if (b_unit_no != "" && m_unit_no != "" && m_unit_no.Contains(b_unit_no))
                                {
                                    unit_qty = unit_qty.Replace(m_unit_no, "m");
                                    m_unit_no = "m";
                                }

                                if (m_unit_no != "" && s_unit_no.Contains(m_unit_no))
                                {
                                    unit_qty = unit_qty.Replace(s_unit_no, "s");
                                    s_unit_no = "s";
                                }


                                if (b_unit_no != "")
                                {
                                    arr = unit_qty.Split(b_unit_no);
                                    if (arr.Length == 2)
                                    {
                                        b_qty = Convert.ToSingle(arr[0]);
                                        nQty += b_qty * b_unit_factor;
                                        unit_qty = arr[1];
                                    }
                                }

                                if (m_unit_no != "")
                                {
                                    arr = unit_qty.Split(m_unit_no);
                                    if (arr.Length == 2)
                                    {
                                        try
                                        {
                                            float m_qty = Convert.ToSingle(arr[0]);
                                            nQty += m_qty * m_unit_factor;
                                            unit_qty = arr[1];
                                        }
                                        catch (Exception e)
                                        {
                                            return new JsonResult(new { result = "Error", msg = $"{item.item_name}中单位数量格式不正确" });
                                        }

                                    }
                                }

                                arr = unit_qty.Split(s_unit_no);
                                if (arr.Length == 2)
                                {
                                    float s_qty = Convert.ToSingle(arr[0]);
                                    nQty += s_qty;
                                }
                                qty = CPubVars.FormatMoney(nQty, 4);
                            }
                            item.s_unit_qty = qty;
                            item.real_quantity = qty;
                            if (lst.GetCellValue("produce_date").IsNumeric())
                            {
                                item.produce_date = DateTime.FromOADate(double.Parse(lst.GetCellValue("produce_date"))).ToString("yyyy-MM-dd");
                            }
                            else
                            {
                                item.produce_date = lst.GetCellValue("produce_date");
                            }
                            item.batch_no = lst.GetCellValue("batch_no");
                            records.Add(item);
                        }
                    }
                }
            }
            #endregion

            return new JsonResult(new { result, msg, records, attributes, attrOptions });
        }

        [HttpPost]
        public async Task<IActionResult> GetStockQtyList_new()
        {
            string branch_id = Request.Form["branch_id"].ToString();
            string items_id = Request.Form["items_id"];
            bool bGetAttrs = Request.Form["bGetAttrs"] == "true";
            bool bImportFile = Request.Form["importFile"] == "true";//导入
            bool stockOnly = Request.Form["stockOnly"] == "true";//全部库存
            bool bWholeStock = Request.Form["wholeStock"] == "true";//整仓盘点
            string orderByStr = Request.Form["orderBy"];//按品牌、按类别
            //导入和全部库存无items_id
            string defaultBranchPositionID = Request.Form["defaultBranchPositionID"];
            string defaultBranchPositionType = Request.Form["defaultBranchPositionType"];
            bool isShowNegativeStock = Request.Form["isShowNegativeStock"] == "true";
            if (defaultBranchPositionType.IsValid() && !CPubVars.IsNumeric(defaultBranchPositionType))
            {
                defaultBranchPositionType = "";
            }
            dynamic defaultBranchPosition = null;//返回

            string inventTime = Request.Form["happen_time"];

            if (string.IsNullOrEmpty(inventTime)) inventTime = CPubVars.GetDateText(DateTime.Now);
            //  DateTime minHappenTime = Convert.ToDateTime(inventTime);//用来提高查询速度,假定不会开三个月以前的单据

            CMySbDataReader dr;

            string companyID = Token.CompanyID;

            string result = "OK", msg = "";

            string importItemsName = "";
            List<DataRow> lstImportItemRows = new List<DataRow>();
            Dictionary<string, dynamic> dicMatchedItems = new Dictionary<string, dynamic>();
            string stockCondi = "";
            if (bImportFile)
            {
                items_id = "";
                var import_info_stock = new KeyValuePair<string, string>[]
                {
                    new KeyValuePair<string, string>("库存信息商品名称","item_name"),
                    new KeyValuePair<string, string>("库存信息生产日期","produce_date"),
                    new KeyValuePair<string, string>("库存信息批次","batch_no"),
                    new KeyValuePair<string, string>("库存信息实际库存","real_quantity"),
                    new KeyValuePair<string, string>("库存信息多单位库存","unit_quantity"),
                };

                try
                {
                    IFormFile file = Request.Form.Files[0];
                    var uploadDir = Environment.CurrentDirectory + $"/wwwroot/uploads/{DateTime.Today:yyyyMM}/";

                    await file.UploadAsync(uploadDir);

                    var path = uploadDir + file.FileName;

                    NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                    logger.Error("in ImportStock. path:" + path);

                    var stock_lk = ExcelHelper.ReadExcel(path);
                    var stockTable = stock_lk.GetPartialTable(import_info_stock, "stock");

                    lstImportItemRows = stockTable.DataRows;

                    lstImportItemRows.ForEach(row => {
                        if (row.Columns[0].Value.IsInvalid()) return;

                        if (importItemsName != "") importItemsName += ",";
                        importItemsName += "'" + row.Columns[0].Value + "'";


                    });

                }
                catch (MyException ex)
                {
                    result = "Failed"; msg = ex.Message;
                }
                cmd.CommandText = $"select item_id,item_name from info_item_prop where company_id={companyID} and item_name in ({importItemsName})";
                dr = await cmd.ExecuteReaderAsync();
                Dictionary<string, string> dicItems = new Dictionary<string, string>();
                while (dr.Read())
                {
                    string item_id = CPubVars.GetTextFromDr(dr, "item_id");
                    string item_name = CPubVars.GetTextFromDr(dr, "item_name");
                    if (!dicItems.ContainsKey(item_name)) dicItems.Add(item_name, item_id);

                }
                dr.Close();

                string notFoundItems = "";
                int nNotMatched = 0;
                lstImportItemRows.ForEach(row => {
                    string item_name = row.Columns[0].Value;
                    if (item_name.IsInvalid()) return;
                    if (!dicItems.ContainsKey(item_name))
                    {
                        if (notFoundItems != "") notFoundItems += "<br>";
                        notFoundItems += item_name;
                        nNotMatched++;
                    }
                    else
                    {
                        string produceDate = row.Columns[2].Value;
                        string batchNo = row.Columns[3].Value;
                        if (produceDate.IsInvalid()) produceDate = "";
                        if (batchNo.IsInvalid()) batchNo = "";
                        if (stockCondi != "") stockCondi += ",";
                        stockCondi += $"({dicItems[item_name]},'{produceDate}','{batchNo}')";

                    }
                });
                stockCondi = $" and (item_id,substring(produce_date::text,1,10),batch_no) in ({stockCondi})";
                items_id = string.Join(",", dicItems.Values);

                if (notFoundItems != "")
                {
                    msg = "以下" + nNotMatched.ToString() + "个商品在系统中没有找到:" + notFoundItems;
                }
                else if (items_id == "")
                {
                    msg = "导入的EXCEL表中没有记录";
                }
                if (msg != "")
                    return new JsonResult(new { result = "Error", msg });
            }
            else
            {
                stockCondi = $" and M.branch_position = 0 and M.batch_id = 0";
            }

            var condi = "";
            var orderBy = "order by item_name collate \"zh_CN.utf8\"";
            if (!string.IsNullOrEmpty(items_id))
            {
                if (bWholeStock)
                {
                    condi = $" and (ip.item_id in ({items_id}) or s.branch_id is not null) ";
                    stockCondi = "";
                }
                else
                    condi = $" and  ip.item_id in ({items_id}) ";
                orderBy = $"order by position(ip.item_id::text in '{items_id}')";
            }
            else if (stockOnly)
            {
                condi += $" and s.branch_id is not null";
                stockCondi = "";
            }
            if (!string.IsNullOrEmpty(orderByStr))
            {
                orderBy = $"order by {orderByStr}";
                stockCondi = "";
            }

            SQLQueue QQ = new SQLQueue(cmd);
            string statusCondi = "coalesce(ip.status,'1')='1' or ";
            if (bImportFile) statusCondi = " true or ";

            string sql = @$"
SELECT
	ip.item_id, item_name,item_spec,batch_id,SUBSTRING(COALESCE(produce_date::text,''),1,10) produce_date,COALESCE(batch_no,'') as batch_no,mum_attributes,invent_time_stock_qty stock_qty,cost_price_avg,cost_price_spec,s_wholesale_price wholesale_price,s_buy_price buy_price,ip.batch_level,
    yj_get_bms_qty (COALESCE (invent_time_stock_qty, 0)::NUMERIC, b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no ) AS current_qty,
     yj_get_bms_qty (COALESCE (sell_pend_qty, 0)::NUMERIC, b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no ) AS sell_pend_qty,
    yj_get_bms_qty (COALESCE (invent_time_stock_qty - sell_pend_qty, 0)::NUMERIC, b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no ) AS avail_qty,
    yj_get_unit_relation(b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no) as unit_conv,
	b_unit_no, m_unit_no, s_unit_no, branch_id, s_barcode, b_barcode, m_barcode, m_unit_factor, b_unit_factor,
	yj_get_unit_qty ( 'b', invent_time_stock_qty::NUMERIC, b_unit_factor, m_unit_factor ,false) bstock,
	yj_get_unit_qty ( 'm', invent_time_stock_qty::NUMERIC, b_unit_factor, m_unit_factor ,false) mstock,
	yj_get_unit_qty ( 's', invent_time_stock_qty::NUMERIC, b_unit_factor, m_unit_factor ,false) sstock,
branch_position,branch_position_name,
    cls2.class_name as class_name

FROM info_item_prop ip
LEFT JOIN
(
    SELECT s.item_id AS item_id,s.batch_id,produce_date,batch_no, branch_id,sell_pend_qty, COALESCE ( stock_qty - COALESCE ( xs_qty, 0 ) - COALESCE ( cg_qty, 0 ) - COALESCE ( yk_qty, 0 ) - COALESCE ( db_add_qty, 0 ) + COALESCE ( db_reduce_qty, 0 ) - COALESCE ( zz_add_qty, 0 ) + COALESCE ( zz_reduce_qty, 0 ) - COALESCE(cr_qty,0) ,0) AS invent_time_stock_qty
,s.branch_position,branch_position_name 
    FROM
    ( 
        SELECT item_id, stock_qty,sell_pend_qty,M.branch_id,M.batch_id,batch_no,produce_date,M.branch_position,branch_position_name FROM stock M 
	    left join info_item_batch itb on itb.company_id = {companyID} and itb.batch_id = M.batch_id
        LEFT JOIN info_branch_position ibp on ibp.company_id = {companyID}  and ibp.branch_position = M.branch_position and M.branch_id = ibp.branch_id
        WHERE M.company_id = {companyID} AND M.branch_id = {branch_id} {stockCondi}
    ) s
    LEFT JOIN 
    (
        SELECT d.item_id AS item_id, SUM (quantity * unit_factor * inout_flag) AS xs_qty ,COALESCE(batch_id,0) as batch_id,COALESCE(branch_position,0) as branch_position
        FROM sheet_sale_detail d
        LEFT JOIN sheet_sale_main M ON d.sheet_id = M.sheet_id AND d.company_id = M.company_id 
        WHERE  d.company_id = {companyID} AND red_flag IS NULL  AND M.approve_time IS NOT NULL AND COALESCE(d.branch_id,M.branch_id) = {branch_id} and m.happen_time>'{inventTime}'  and d.happen_time>'{inventTime}' AND M.is_imported IS NULL
        GROUP BY d.item_id ,batch_id,branch_position
    ) xs ON s.item_id = xs.item_id and s.batch_id = xs.batch_id and s.branch_position = xs.branch_position
    LEFT JOIN 
    (
        SELECT d.item_id AS item_id, SUM (quantity * unit_factor*inout_flag) AS cg_qty,COALESCE(batch_id,0) as batch_id,COALESCE(branch_position,0) as branch_position
        FROM sheet_buy_detail d
        LEFT JOIN sheet_buy_main M ON d.company_id = M.company_id  and d.sheet_id = M.sheet_id
        WHERE  d.company_id = {companyID} AND red_flag IS NULL  AND M.approve_time IS NOT NULL  AND COALESCE(d.branch_id,M.branch_id) = {branch_id} and m.happen_time>'{inventTime}'  and d.happen_time>'{inventTime}'  
        GROUP BY d.item_id ,batch_id,branch_position
    ) cg ON s.item_id = cg.item_id and s.batch_id = cg.batch_id and s.branch_position = cg.branch_position
    LEFT JOIN 
    (
        SELECT d.item_id AS item_id, SUM (quantity * unit_factor*inout_flag) AS yk_qty,COALESCE(batch_id,0) as batch_id,COALESCE(branch_position,0) as branch_position
        FROM sheet_invent_change_detail d
        LEFT JOIN sheet_invent_change_main M ON d.company_id = M.company_id and d.sheet_id = M.sheet_id  
        WHERE  d.company_id = {companyID}  and red_flag IS NULL AND M.approve_time IS NOT NULL AND M.branch_id = {branch_id}  and m.happen_time>'{inventTime}'  and d.happen_time>'{inventTime}'
        GROUP BY d.item_id ,batch_id,branch_position
    ) yk ON s.item_id = yk.item_id and s.batch_id = yk.batch_id and s.branch_position = yk.branch_position
    LEFT JOIN 
    (
        SELECT d.item_id AS item_id,SUM ( quantity * unit_factor ) AS db_add_qty ,COALESCE(batch_id,0) as batch_id,COALESCE(to_branch_position,0) as branch_position
        FROM sheet_move_detail d
        LEFT JOIN sheet_move_main M ON  d.company_id = M.company_id AND d.sheet_id = M.sheet_id 
        WHERE d.company_id = {companyID} and red_flag IS NULL AND M.approve_time IS NOT NULL AND to_branch_id = {branch_id} and m.happen_time>'{inventTime}'  and d.happen_time>'{inventTime}'
        GROUP BY d.item_id ,batch_id,branch_position
    ) dr ON s.item_id = dr.item_id and s.batch_id = dr.batch_id and s.branch_position = dr.branch_position
    LEFT JOIN 
    (
        SELECT d.item_id AS item_id,SUM ( quantity * unit_factor ) AS db_reduce_qty ,COALESCE(batch_id,0) as batch_id,COALESCE(from_branch_position,0) as branch_position
        FROM sheet_move_detail d
        LEFT JOIN sheet_move_main M ON d.company_id = M.company_id  and d.sheet_id = M.sheet_id  
        WHERE d.company_id = {companyID}  and red_flag IS NULL AND M.approve_time IS NOT NULL AND from_branch_id = {branch_id} and m.happen_time>'{inventTime}' and d.happen_time>'{inventTime}'
        GROUP BY d.item_id ,batch_id,branch_position
    ) dc ON s.item_id = dc.item_id and s.batch_id = dc.batch_id and s.branch_position = dc.branch_position
    LEFT JOIN 
        (
            SELECT d.item_id AS item_id,SUM ( quantity * unit_factor ) AS zz_reduce_qty ,COALESCE(batch_id,0) as batch_id,COALESCE(branch_position,0) as branch_position
            FROM sheet_combine_detail d
            LEFT JOIN sheet_combine_main M ON d.company_id = M.company_id  and d.sheet_id = M.sheet_id  
            WHERE d.company_id = {companyID}  and red_flag IS NULL AND M.approve_time IS NOT NULL AND from_branch_id = {branch_id} and m.happen_time>'{inventTime}' and d.happen_time>'{inventTime}' and inout_flag = -1
            GROUP BY d.item_id ,batch_id,branch_position
        ) zzc ON s.item_id = zzc.item_id and s.batch_id = zzc.batch_id and s.branch_position = zzc.branch_position
    LEFT JOIN 
        (
            SELECT d.item_id AS item_id,SUM ( quantity * unit_factor ) AS zz_add_qty ,COALESCE(batch_id,0) as batch_id,COALESCE(branch_position,0) as branch_position
            FROM sheet_combine_detail d
            LEFT JOIN sheet_combine_main M ON d.company_id = M.company_id  and d.sheet_id = M.sheet_id  
            WHERE d.company_id = {companyID}  and red_flag IS NULL AND M.approve_time IS NOT NULL AND to_branch_id = {branch_id} and m.happen_time>'{inventTime}' and d.happen_time>'{inventTime}' and inout_flag = 1
            GROUP BY d.item_id ,batch_id,branch_position
        ) zzr ON s.item_id = zzr.item_id and s.batch_id = zzr.batch_id and s.branch_position = zzr.branch_position
    LEFT JOIN 
        (
            SELECT d.item_id AS item_id,SUM ( quantity * unit_factor * inout_flag) AS cr_qty ,COALESCE(batch_id,0) as batch_id,COALESCE(branch_position,0) as branch_position
            FROM sheet_stock_in_out_detail d
            LEFT JOIN sheet_stock_in_out_main M ON d.company_id = M.company_id  and d.sheet_id = M.sheet_id  
            WHERE d.company_id = {companyID}  and red_flag IS NULL AND M.approve_time IS NOT NULL AND M.branch_id = {branch_id} and m.happen_time>'{inventTime}' and d.happen_time>'{inventTime}'
            GROUP BY d.item_id ,batch_id,branch_position
        ) cr ON s.item_id = cr.item_id and s.batch_id = cr.batch_id and s.branch_position = cr.branch_position
) s 
ON ip.item_id = s.item_id 
LEFT JOIN
(
    select item_id, 
            (b->>'f1')::numeric as b_unit_factor, b->>'f2' as b_unit_no, b->>'f3' b_barcode,
            (m->>'f1')::numeric as m_unit_factor, m->>'f2' as m_unit_no, m->>'f3' m_barcode,
            (s->>'f1')::numeric as s_unit_factor, s->>'f2' as s_unit_no ,s->>'f3' s_barcode,s->>'f4' AS s_wholesale_price,s->>'f6' s_buy_price
    from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,barcode,wholesale_price,retail_price,buy_price)) as json from info_item_multi_unit where company_id= {companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
        as errr(item_id int, s jsonb,m jsonb,b jsonb) 
) t on ip.item_id=t.item_id
where ip.company_id={companyID} and ({statusCondi} s.invent_time_stock_qty<>0) {condi}
{orderBy}               
            ";


            QQ.Enqueue("items", sql);

            sql = @$" 
SELECT array_to_json(array_agg(row_to_json(t))) attr_options FROM
(
    SELECT opt_id, opt_name, attr.attr_id FROM info_attr_opt opt left join info_attribute attr on opt.attr_id = attr.attr_id where opt.company_id ={companyID} and not attr.spec_opt_in_item order by opt.order_index
) t";
            QQ.Enqueue("attr_options", sql);
            if (bGetAttrs)
            {
                sql = $"select attr_id,attr_name,distinct_stock,order_index from info_attribute where company_id={companyID} order by order_index";
                QQ.Enqueue("attrs", sql);
            }
            #region 查库位
            if (defaultBranchPositionID == "0" && defaultBranchPositionType.IsValid())
            {
                sql = @$"select branch_id,branch_position, branch_position_name from info_branch_position
where company_id = {companyID} and branch_id = {branch_id} and type_id = {defaultBranchPositionType} and COALESCE(position_status,'1')='1' ORDER BY branch_position;";
                QQ.Enqueue("branch_position", sql);
            }
            #endregion
            List<ExpandoObject> records = null;
            // List<ExpandoObject> units = null;
            List<ExpandoObject> attributes = null;
            dynamic attrOptions = null;
            //MyLogger.LogSQL("report", QQ.SQL, companyID,"");
            //  MyLogger.LogFileMsg("getstockQty:"+QQ.SQL,companyID);
            List<ExpandoObject> batchStock = null;
            Dictionary<string, List<Dictionary<string, string>>> batchStockTotal = new Dictionary<string, List<Dictionary<string, string>>>();
            Dictionary<string, List<Dictionary<string, string>>> batchStockForShow = new Dictionary<string, List<Dictionary<string, string>>>();
            dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                string tbl = QQ.Dequeue();
                if (tbl == "items")
                {
                    records = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (tbl == "attr_options")
                {
                    dr.Read();
                    string s = CPubVars.GetTextFromDr(dr, "attr_options");
                    if (s != "")
                    {
                        attrOptions = JsonConvert.DeserializeObject(s);
                    }
                }
                else if (tbl == "attrs")
                {
                    attributes = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (tbl == "branch_position")
                {
                    defaultBranchPosition = CDbDealer.Get1RecordFromDr(dr, false);
                    defaultBranchPositionID = defaultBranchPosition == null ? "0" : defaultBranchPosition.branch_position;
                }
            }
            QQ.Clear();

            //var lstRecordsForImport = new List<ExpandoObject>();
            #region set s_unit_qty of rows according to imported excel file
            //
            foreach (dynamic item in records)
            {
                string item_name = item.item_name;
                string batch_id = item.batch_id;
                string produce_date = item.produce_date == null ? "" : item.produce_date;
                string batch_no = item.batch_no == null ? "" : item.batch_no;
                if (!dicMatchedItems.ContainsKey(item_name))
                {
                    dicMatchedItems.Add(item_name, item);
                }
                var qty = "";
                var unit_qty = "";
                foreach (var lst in lstImportItemRows)
                {
                    if (lst.Columns[1].Value == null) lst.Columns[1].Value = "";
                    if (lst.Columns[2].Value == null) lst.Columns[2].Value = "";
                    if (lst.Columns[3].Value == null) lst.Columns[3].Value = "";
                    if (lst.Columns[4].Value == null) lst.Columns[4].Value = "";
                    if (lst.Columns[0].Value == item_name && lst.Columns[1].Value == produce_date && lst.Columns[2].Value == batch_no)
                    {
                        qty = lst.Columns[3].Value;
                        unit_qty = lst.Columns[4].Value;
                        produce_date = lst.Columns[1].Value;
                        batch_no = lst.Columns[2].Value;
                    }
                }
                lstImportItemRows.RemoveAll(lst => lst.Columns[0].Value == item_name && lst.Columns[1].Value == produce_date && lst.Columns[2].Value == batch_no);

                var item_id = item.item_id;


                if (qty.IsInvalid() && unit_qty.IsValid())
                {
                    float b_qty = 0;
                    float nQty = 0;
                    float b_unit_factor = 0;
                    if (item.b_unit_factor != "") b_unit_factor = Convert.ToSingle(item.b_unit_factor);
                    float m_unit_factor = 0;
                    if (item.m_unit_factor != "") m_unit_factor = Convert.ToSingle(item.m_unit_factor);
                    string[] arr;

                    string b_unit_no = item.b_unit_no;
                    string m_unit_no = item.m_unit_no;
                    string s_unit_no = item.s_unit_no;

                    if (b_unit_no != "" && s_unit_no.Contains(b_unit_no))
                    {
                        unit_qty = unit_qty.Replace(s_unit_no, "s");
                        s_unit_no = "s";
                    }

                    if (b_unit_no != "" && m_unit_no != "" && m_unit_no.Contains(b_unit_no))
                    {
                        unit_qty = unit_qty.Replace(m_unit_no, "m");
                        m_unit_no = "m";
                    }

                    if (m_unit_no != "" && s_unit_no.Contains(m_unit_no))
                    {
                        unit_qty = unit_qty.Replace(s_unit_no, "s");
                        s_unit_no = "s";
                    }


                    if (b_unit_no != "")
                    {
                        arr = unit_qty.Split(b_unit_no);
                        if (arr.Length == 2)
                        {
                            b_qty = Convert.ToSingle(arr[0]);
                            nQty += b_qty * b_unit_factor;
                            unit_qty = arr[1];
                        }
                    }

                    if (m_unit_no != "")
                    {
                        arr = unit_qty.Split(m_unit_no);
                        if (arr.Length == 2)
                        {
                            try
                            {
                                float m_qty = Convert.ToSingle(arr[0]);
                                nQty += m_qty * m_unit_factor;
                                unit_qty = arr[1];
                            }
                            catch (Exception e)
                            {
                                return new JsonResult(new { result = "Error", msg = $"{item_name}中单位数量格式不正确" });
                            }

                        }
                    }

                    arr = unit_qty.Split(s_unit_no);
                    if (arr.Length == 2)
                    {
                        float s_qty = Convert.ToSingle(arr[0]);
                        nQty += s_qty;
                    }
                    qty = CPubVars.FormatMoney(nQty, 4);
                }
                item.s_unit_qty = qty;
                item.real_quantity = qty;
                item.produce_date = produce_date;
                item.batch_no = batch_no;
                //var row = lstImportItemRows.FirstOrDefault(x => x.Columns[0].Value == item_name && x.Columns[1].Value == produce_date && x.Columns[2].Value == batch_no);
            }
            if (bImportFile)
            {
                records.RemoveAll(item => JsonConvert.DeserializeObject<dynamic>(JsonConvert.SerializeObject(item)).batch_id == "");
            }
            foreach (var lst in lstImportItemRows)
            {
                if (dicMatchedItems.ContainsKey(lst.Columns[0].Value))
                {
                    var item = JsonConvert.DeserializeObject<ExpandoObject>(JsonConvert.SerializeObject(dicMatchedItems[lst.Columns[0].Value]));
                    var qty = lst.Columns[3].Value;
                    var unit_qty = lst.Columns[4].Value;
                    if (qty.IsInvalid() && unit_qty.IsValid())
                    {
                        float b_qty = 0;
                        float nQty = 0;
                        float b_unit_factor = 0;
                        if (item.b_unit_factor != "") b_unit_factor = Convert.ToSingle(item.b_unit_factor);
                        float m_unit_factor = 0;
                        if (item.m_unit_factor != "") m_unit_factor = Convert.ToSingle(item.m_unit_factor);
                        string[] arr;

                        string b_unit_no = item.b_unit_no;
                        string m_unit_no = item.m_unit_no;
                        string s_unit_no = item.s_unit_no;

                        if (b_unit_no != "" && s_unit_no.Contains(b_unit_no))
                        {
                            unit_qty = unit_qty.Replace(s_unit_no, "s");
                            s_unit_no = "s";
                        }

                        if (b_unit_no != "" && m_unit_no != "" && m_unit_no.Contains(b_unit_no))
                        {
                            unit_qty = unit_qty.Replace(m_unit_no, "m");
                            m_unit_no = "m";
                        }

                        if (m_unit_no != "" && s_unit_no.Contains(m_unit_no))
                        {
                            unit_qty = unit_qty.Replace(s_unit_no, "s");
                            s_unit_no = "s";
                        }


                        if (b_unit_no != "")
                        {
                            arr = unit_qty.Split(b_unit_no);
                            if (arr.Length == 2)
                            {
                                b_qty = Convert.ToSingle(arr[0]);
                                nQty += b_qty * b_unit_factor;
                                unit_qty = arr[1];
                            }
                        }

                        if (m_unit_no != "")
                        {
                            arr = unit_qty.Split(m_unit_no);
                            if (arr.Length == 2)
                            {
                                try
                                {
                                    float m_qty = Convert.ToSingle(arr[0]);
                                    nQty += m_qty * m_unit_factor;
                                    unit_qty = arr[1];
                                }
                                catch (Exception e)
                                {
                                    return new JsonResult(new { result = "Error", msg = $"{item.item_name}中单位数量格式不正确" });
                                }

                            }
                        }

                        arr = unit_qty.Split(s_unit_no);
                        if (arr.Length == 2)
                        {
                            float s_qty = Convert.ToSingle(arr[0]);
                            nQty += s_qty;
                        }
                        qty = CPubVars.FormatMoney(nQty, 4);
                    }
                    item.s_unit_qty = qty;
                    item.real_quantity = qty;
                    item.produce_date = lst.Columns[1].Value;
                    item.batch_no = lst.Columns[2].Value;
                    item.stock_qty = "";
                    item.sell_pend_qty = "";
                    item.avail_qty = "";
                    item.current_qty = "";
                    item.bstock = "";
                    item.mstock = "";
                    item.sstock = "";
                    item.batch_id = "";
                    records.Add(item);
                }
            }
            #endregion
            bool needQueryBatchStock = false;
            foreach (dynamic unit in records)
            {
                if (unit.batch_level != "")
                {
                    needQueryBatchStock = true;
                    break;
                }
            }
            if (needQueryBatchStock || (defaultBranchPositionID.IsValid() && defaultBranchPositionID != "0"))
            {
                string batchCondi = "";
                if (!bImportFile && !stockOnly && !bWholeStock && orderByStr.IsInvalid())
                {
                    batchCondi = $" and s.branch_position = {defaultBranchPositionID}";
                }
                if (items_id.IsValid())
                {
                    batchCondi += $@" and s.item_id in ({items_id})";
                }
                string batchSql = $@"select COALESCE(s.batch_id,0)::int as batch_id,COALESCE(s.batch_id,0)::int as batch_id_f,COALESCE(batch_no,'') as batch_no,COALESCE(SUBSTRING(produce_date::text,1,10),'') as produce_date,COALESCE(stock_qty,0) as stock_qty,s.branch_id,s.branch_position,COALESCE(branch_position_name,'') as branch_position_name,
unit_from_s_to_bms (COALESCE(stock_qty,0)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) as stock_qty_unit,
unit_from_s_to_bms (COALESCE(sell_pend_qty,0)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) as sell_pend_qty_unit,
unit_from_s_to_bms (COALESCE(stock_qty,0)::numeric-COALESCE(sell_pend_qty,0)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) as usable_stock_qty_unit,s.item_id
                    from stock s 
                    left join info_branch_position ibp on ibp.company_id = {companyID} and ibp.branch_id = s.branch_id and ibp.branch_position = s.branch_position
                    left join info_item_batch itb on itb.batch_id = s.batch_id and itb.company_id = s.company_id
                    left join (select item_id,(b->>'f1')::real as b_unit_factor,(m->>'f1')::real as m_unit_factor,(s->>'f1')::real as s_unit_factor,
                                                    b->>'f2' as b_unit_no,m->>'f2' as m_unit_no,s->>'f2' as s_unit_no,s->>'f3' as s_buy_price
                                    from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,buy_price)) as json from info_item_multi_unit where company_id={companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
                                        as errr(item_id int, s jsonb,m jsonb,b jsonb)) mu1 on s.item_id = mu1.item_id
                    where s.company_id = {companyID} and s.branch_id = {branch_id} {batchCondi} and COALESCE(stock_qty,0)<> 0 order by produce_date,batch_no limit 200";
                batchStock = await CDbDealer.GetRecordsFromSQLAsync(batchSql, cmd);
                if (batchStock.Count > 0)
                {
                    List<Dictionary<string, string>> newData = JsonConvert.DeserializeObject<List<Dictionary<string, string>>>(JsonConvert.SerializeObject(batchStock));
                    foreach (Dictionary<string, string> v in newData)
                    {
                        string key = v["item_id"].ToString() + v["branch_id"].ToString() + v["branch_position"].ToString();
                        decimal stockQty = CPubVars.ToDecimal(v["stock_qty"]);
                        if (batchStockTotal.ContainsKey(key)) batchStockTotal[key].Add(v);
                        else
                        {
                            batchStockTotal[key] = new List<Dictionary<string, string>>();
                            batchStockTotal[key].Add(v);
                        }
                        if (!isShowNegativeStock && stockQty > 0)
                        {
                            if (batchStockForShow.ContainsKey(key)) batchStockForShow[key].Add(v);
                            else
                            {
                                batchStockForShow[key] = new List<Dictionary<string, string>>();
                                batchStockForShow[key].Add(v);
                            }
                        }
                    }
                    if (isShowNegativeStock) batchStockForShow = batchStockTotal;
                }

            }
            return new JsonResult(new { result, msg, records, attributes, attrOptions, defaultBranchPosition, batchStockTotal, batchStockForShow });
        }

        [HttpPost]
        public async Task<IActionResult> GetStockQtyList_old()
        {
            string branch_id = Request.Form["branch_id"].ToString();
            string items_id = Request.Form["items_id"];
            bool stockOnly = Request.Form["stockOnly"] == "true";
            bool bGetAttrs = Request.Form["bGetAttrs"] == "true";
            bool bImportFile = Request.Form["importFile"] == "true";
            bool bWholeStock = Request.Form["wholeStock"] == "true";

            string inventTime = Request.Form["happen_time"];

            if (string.IsNullOrEmpty(inventTime)) inventTime = CPubVars.GetDateText(DateTime.Now);
            DateTime minHappenTime = Convert.ToDateTime(inventTime);//用来提高查询速度,假定不会开三个月以前的单据

            CMySbDataReader dr;

            string companyID = Token.CompanyID;

            string result = "OK", msg = "";

            string importItemsName = "";
            List<DataRow> lstImportItemRows = new List<DataRow>();
            Dictionary<string, string> dicMatchedItems = new Dictionary<string, string>();
            if (bImportFile)
            {
                items_id = "";
                var import_info_stock = new KeyValuePair<string, string>[]
                {
                    new KeyValuePair<string, string>("库存信息商品名称","item_name"),
                    new KeyValuePair<string, string>("库存信息实际库存","real_quantity"),
                    new KeyValuePair<string, string>("库存信息多单位库存","unit_quantity"),
                };

                try
                {
                    IFormFile file = Request.Form.Files[0];
                    var uploadDir = Environment.CurrentDirectory + $"/wwwroot/uploads/{DateTime.Today:yyyyMM}/";

                    await file.UploadAsync(uploadDir);

                    var path = uploadDir + file.FileName;

                    NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                    logger.Error("in ImportStock. path:" + path);

                    var stock_lk = ExcelHelper.ReadExcel(path);
                    var stockTable = stock_lk.GetPartialTable(import_info_stock, "stock");

                    lstImportItemRows = stockTable.DataRows;

                    lstImportItemRows.ForEach(row => {
                        if (row.Columns[0].Value.IsInvalid()) return;

                        if (importItemsName != "") importItemsName += ",";
                        importItemsName += "'" + row.Columns[0].Value + "'";

                    });

                }
                catch (MyException ex)
                {
                    result = "Failed"; msg = ex.Message;
                }
                cmd.CommandText = $"select item_id,item_name from info_item_prop where company_id={companyID} and item_name in ({importItemsName})";
                dr = await cmd.ExecuteReaderAsync();
                Dictionary<string, string> dicItems = new Dictionary<string, string>();
                while (dr.Read())
                {
                    string item_id = CPubVars.GetTextFromDr(dr, "item_id");
                    string item_name = CPubVars.GetTextFromDr(dr, "item_name");
                    if (!dicItems.ContainsKey(item_name)) dicItems.Add(item_name, item_id);

                }
                dr.Close();

                string notFoundItems = "";
                int nNotMatched = 0;
                lstImportItemRows.ForEach(row => {
                    string item_name = row.Columns[0].Value;
                    if (item_name.IsInvalid()) return;
                    if (!dicItems.ContainsKey(item_name))
                    {
                        if (notFoundItems != "") notFoundItems += "<br>";
                        notFoundItems += item_name;
                        nNotMatched++;
                    }
                });
                items_id = string.Join(",", dicItems.Values);

                if (notFoundItems != "")
                {
                    msg = "以下" + nNotMatched.ToString() + "个商品在系统中没有找到:" + notFoundItems;
                }
                else if (items_id == "")
                {
                    msg = "导入的EXCEL表中没有记录";
                }
                if (msg != "")
                    return new JsonResult(new { result = "Error", msg });
            }

            var condi = "";
            var orderBy = "order by item_name collate \"zh_CN.utf8\"";
            if (!string.IsNullOrEmpty(items_id))
            {
                // stockOnly = false;
                if (bWholeStock)
                {
                    condi = $" and (ip.item_id in ({items_id}) or s.branch_id is not null) ";
                }
                else
                    condi = $" and  ip.item_id in ({items_id}) ";
                orderBy = $"order by position(ip.item_id::text in '{items_id}')";
            }
            else if (stockOnly)
            {
                condi += $" and s.branch_id is not null";
            }

            SQLQueue QQ = new SQLQueue(cmd);

            string sql = @$"
SELECT
	ip.item_id, item_name,item_spec,mum_attributes,invent_time_stock_qty stock_qty,cost_price_avg,cost_price_spec,s_wholesale_price wholesale_price,s_buy_price buy_price,
    yj_get_bms_qty (COALESCE (invent_time_stock_qty, 0)::NUMERIC, b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no ) AS current_qty,
     yj_get_bms_qty (COALESCE (sell_pend_qty, 0)::NUMERIC, b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no ) AS sell_pend_qty,
    yj_get_bms_qty (COALESCE (invent_time_stock_qty - sell_pend_qty, 0)::NUMERIC, b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no ) AS avail_qty,
    yj_get_unit_relation(b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no) as unit_conv,
	b_unit_no, m_unit_no, s_unit_no, branch_id, s_barcode, b_barcode, m_barcode, m_unit_factor, b_unit_factor,
	yj_get_unit_qty ( 'b', invent_time_stock_qty::NUMERIC, b_unit_factor, m_unit_factor ,false) bstock,
	yj_get_unit_qty ( 'm', invent_time_stock_qty::NUMERIC, b_unit_factor, m_unit_factor ,false) mstock,
	yj_get_unit_qty ( 's', invent_time_stock_qty::NUMERIC, b_unit_factor, m_unit_factor ,false) sstock 

FROM info_item_prop ip
LEFT JOIN
(
    SELECT s.item_id AS item_id, branch_id,sell_pend_qty, COALESCE ( stock_qty - COALESCE ( xs_qty, 0 ) - COALESCE ( cg_qty, 0 ) - COALESCE ( yk_qty, 0 ) - COALESCE ( db_add_qty, 0 ) + COALESCE ( db_reduce_qty, 0 ),0) AS invent_time_stock_qty 
    FROM
    ( 
        SELECT item_id, stock_qty,sell_pend_qty, branch_id FROM stock M WHERE company_id = {companyID} AND M.branch_id = {branch_id}  
    ) s
    LEFT JOIN 
    (
        SELECT d.item_id AS item_id, SUM (quantity * unit_factor * inout_flag) AS xs_qty 
        FROM sheet_sale_detail d
        LEFT JOIN sheet_sale_main M ON d.sheet_id = M.sheet_id AND d.company_id = M.company_id 
        WHERE  d.company_id = {companyID} AND red_flag IS NULL  AND M.approve_time IS NOT NULL AND M.branch_id = {branch_id} and m.happen_time>'{minHappenTime}'  and d.happen_time>'{minHappenTime}' AND M.approve_time > '{inventTime}' AND M.is_imported IS NULL
        GROUP BY d.item_id 
    ) xs ON s.item_id = xs.item_id
    LEFT JOIN 
    (
        SELECT d.item_id AS item_id, SUM (quantity * unit_factor*inout_flag) AS cg_qty
        FROM sheet_buy_detail d
        LEFT JOIN sheet_buy_main M ON d.company_id = M.company_id  and d.sheet_id = M.sheet_id
        WHERE  d.company_id = {companyID} AND red_flag IS NULL  AND M.approve_time IS NOT NULL AND M.branch_id = {branch_id} and m.happen_time>'{minHappenTime}'  and d.happen_time>'{minHappenTime}' AND M.approve_time > '{inventTime}'  
        GROUP BY d.item_id 
    ) cg ON s.item_id = cg.item_id
    LEFT JOIN 
    (
        SELECT d.item_id AS item_id, SUM (quantity * unit_factor*inout_flag) AS yk_qty
        FROM sheet_invent_change_detail d
        LEFT JOIN sheet_invent_change_main M ON d.company_id = M.company_id and d.sheet_id = M.sheet_id  
        WHERE  d.company_id = {companyID}  and red_flag IS NULL AND M.approve_time IS NOT NULL AND M.branch_id = {branch_id}  and m.happen_time>'{minHappenTime}'  and d.happen_time>'{minHappenTime}' AND M.approve_time > '{inventTime}' 
        GROUP BY d.item_id 
    ) yk ON s.item_id = yk.item_id
    LEFT JOIN 
    (
        SELECT d.item_id AS item_id,SUM ( quantity * unit_factor ) AS db_add_qty 
        FROM sheet_move_detail d
        LEFT JOIN sheet_move_main M ON  d.company_id = M.company_id AND d.sheet_id = M.sheet_id 
        WHERE d.company_id = {companyID} and red_flag IS NULL AND M.approve_time IS NOT NULL AND to_branch_id = {branch_id} and m.happen_time>'{minHappenTime}'  and d.happen_time>'{minHappenTime}' AND M.approve_time > '{inventTime}' 
        GROUP BY d.item_id 
    ) dr ON s.item_id = dr.item_id
    LEFT JOIN 
    (
        SELECT d.item_id AS item_id,SUM ( quantity * unit_factor ) AS db_reduce_qty 
        FROM sheet_move_detail d
        LEFT JOIN sheet_move_main M ON d.company_id = M.company_id  and d.sheet_id = M.sheet_id  
        WHERE d.company_id = {companyID}  and red_flag IS NULL AND M.approve_time IS NOT NULL AND from_branch_id = {branch_id} and m.happen_time>'{minHappenTime}'  and d.happen_time>'{minHappenTime}' AND M.approve_time > '{inventTime}' 
        GROUP BY d.item_id 
    ) dc ON s.item_id = dc.item_id
) s 
ON ip.item_id = s.item_id 
LEFT JOIN
(
    select item_id, 
            (b->>'f1')::numeric as b_unit_factor, b->>'f2' as b_unit_no, b->>'f3' b_barcode,
            (m->>'f1')::numeric as m_unit_factor, m->>'f2' as m_unit_no, m->>'f3' m_barcode,
            (s->>'f1')::numeric as s_unit_factor, s->>'f2' as s_unit_no ,s->>'f3' s_barcode,s->>'f4' AS s_wholesale_price,s->>'f6' s_buy_price
    from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,barcode,wholesale_price,retail_price,buy_price)) as json from info_item_multi_unit where company_id= {companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
        as errr(item_id int, s jsonb,m jsonb,b jsonb) 
) t on ip.item_id=t.item_id
where ip.company_id={companyID} and (coalesce(ip.status,'1')='1' or s.invent_time_stock_qty<>0)  {condi}
{orderBy}               
            ";


            QQ.Enqueue("items", sql);

            sql = @$" 
SELECT array_to_json(array_agg(row_to_json(t))) attr_options FROM
(
    SELECT opt_id, opt_name, attr.attr_id FROM info_attr_opt opt left join info_attribute attr on opt.attr_id = attr.attr_id where opt.company_id ={companyID} and not attr.spec_opt_in_item order by opt.order_index
) t";
            QQ.Enqueue("attr_options", sql);

            if (bGetAttrs)
            {
                sql = $"select attr_id,attr_name,distinct_stock,order_index from info_attribute where company_id={companyID} order by order_index";
                QQ.Enqueue("attrs", sql);
            }
            List<ExpandoObject> records = null;
            // List<ExpandoObject> units = null;
            List<ExpandoObject> attributes = null;
            dynamic attrOptions = null;
            dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                string tbl = QQ.Dequeue();
                if (tbl == "items")
                {
                    records = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (tbl == "attr_options")
                {
                    dr.Read();
                    string s = CPubVars.GetTextFromDr(dr, "attr_options");
                    if (s != "")
                    {
                        attrOptions = JsonConvert.DeserializeObject(s);
                    }
                }
                else if (tbl == "attrs")
                {
                    attributes = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();

            //var lstRecordsForImport = new List<ExpandoObject>();
            #region set s_unit_qty of rows according to imported excel file
            foreach (dynamic item in records)
            {
                string item_name = item.item_name;
                if (!dicMatchedItems.ContainsKey(item_name))
                {
                    dicMatchedItems.Add(item_name, item_name);
                }

                var row = lstImportItemRows.FirstOrDefault(x => x.Columns[0].Value == item_name);
                if (row != null)
                {
                    var qty = row.Columns[2].Value;
                    var unit_qty = row.Columns[3].Value;

                    var item_id = item.item_id;


                    if (qty.IsInvalid() && unit_qty.IsValid())
                    {
                        float b_qty = 0;
                        float nQty = 0;
                        float b_unit_factor = 0;
                        if (item.b_unit_factor != "") b_unit_factor = Convert.ToSingle(item.b_unit_factor);
                        float m_unit_factor = 0;
                        if (item.m_unit_factor != "") m_unit_factor = Convert.ToSingle(item.m_unit_factor);
                        string[] arr;

                        string b_unit_no = item.b_unit_no;
                        string m_unit_no = item.m_unit_no;
                        string s_unit_no = item.s_unit_no;

                        if (b_unit_no != "" && s_unit_no.Contains(b_unit_no))
                        {
                            unit_qty = unit_qty.Replace(s_unit_no, "s");
                            s_unit_no = "s";
                        }

                        if (b_unit_no != "" && m_unit_no != "" && m_unit_no.Contains(b_unit_no))
                        {
                            unit_qty = unit_qty.Replace(m_unit_no, "m");
                            m_unit_no = "m";
                        }

                        if (m_unit_no != "" && s_unit_no.Contains(m_unit_no))
                        {
                            unit_qty = unit_qty.Replace(s_unit_no, "s");
                            s_unit_no = "s";
                        }


                        if (b_unit_no != "")
                        {
                            arr = unit_qty.Split(b_unit_no);
                            if (arr.Length == 2)
                            {
                                b_qty = Convert.ToSingle(arr[0]);
                                nQty += b_qty * b_unit_factor;
                                unit_qty = arr[1];
                            }
                        }

                        if (m_unit_no != "")
                        {
                            arr = unit_qty.Split(m_unit_no);
                            if (arr.Length == 2)
                            {
                                try
                                {
                                    float m_qty = Convert.ToSingle(arr[0]);
                                    nQty += m_qty * m_unit_factor;
                                    unit_qty = arr[1];
                                }
                                catch (Exception e)
                                {
                                    return new JsonResult(new { result = "Error", msg = $"{item_name}中单位数量格式不正确" });
                                }

                            }
                        }

                        arr = unit_qty.Split(s_unit_no);
                        if (arr.Length == 2)
                        {
                            float s_qty = Convert.ToSingle(arr[0]);
                            nQty += s_qty;
                        }
                        qty = CPubVars.FormatMoney(nQty, 4);
                    }
                    item.s_unit_qty = qty;
                    item.real_quantity = qty;

                }
            }
            #endregion

            return new JsonResult(new { result, msg, records, attributes, attrOptions });
        }
        /// <summary>
        /// 不保存到数据库
        /// </summary>
        /// <returns></returns>

        public class test
        {
            public string id = "", name = "", tt = "";
        }
        [HttpPost]
        public async Task<IActionResult> GetItemsByBrands()
        {
            string brands_id = Request.Form["brands_id"];
            string branch_id = Request.Form["branch_id"];
            bool isLoadNoStock = Request.Form["isLoadNoStock"] == "true";
            string companyID = Token.CompanyID;
            string condi = "";
            string rightJoinSql = "";
            if (!string.IsNullOrEmpty(brands_id))
            {
                condi = $" and item_brand in ({brands_id})";
            }
            if (!isLoadNoStock)
            {
                rightJoinSql = $" right join (select * from stock where stock.stock_qty<>0)s on s.company_id = {companyID} and s.item_id = info_item_prop.item_id";
                //string groupBySql = $"group by info_item_prop.item_id";
                condi += $" and branch_id = {branch_id}";
            }
            string sql = $@"select info_item_prop.item_id from info_item_prop {rightJoinSql} where info_item_prop.company_id ={companyID} and (info_item_prop.status =1 or info_item_prop.status is null)  {condi} ";
            var data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });

        }
        [HttpGet]
        public async Task<IActionResult> GetClassTreeData()
        {
            string companyID = Token.CompanyID;
            string msg = "";
            string data = "";
            string sql = $@"select class_id as v,class_name as l,py_str as z,mother_id as pv,cls_status as status 
                            from info_item_class where class_id<>-1 and info_item_class.company_id={companyID} 
                            order by order_index,class_name,class_id limit 1000 ";
            //var data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            try
            {
                data = await PageBaseModel.GetJsonFromSQL(sql, cmd);
            }
            catch (Exception e)
            {
                msg = "请重试";
            }
            string result = (msg == "") ? "OK" : "ERROOR";
            return Json(new { result, msg, data });

        }
        [HttpPost]
        public async Task<IActionResult> GetItemsByClasses()
        {
            string classes_id = Request.Form["classes_id"];
            string branch_id = Request.Form["branch_id"];
            bool isLoadNoStock = Request.Form["isLoadNoStock"] == "true";
            string companyID = Token.CompanyID;
            string condi = "";
            string rightJoinSql = "";
            string msg = "";
            object data = null;
            if (!string.IsNullOrEmpty(classes_id))
            {
                condi = $" and item_class in ({classes_id})";
            }
            //不加载无库存商品
            if (!isLoadNoStock)
            {
                rightJoinSql = $" right join (select * from stock where stock.stock_qty<>0)s on s.company_id = {companyID} and s.item_id = info_item_prop.item_id";
                //string groupBySql = $"group by info_item_prop.item_id";
                condi += $" and branch_id = {branch_id}";
            }
            string sql = $@"select info_item_prop.item_id from info_item_prop {rightJoinSql} where info_item_prop.company_id ={companyID} and (info_item_prop.status =1 or info_item_prop.status is null) {condi} ";
            try
            {
                data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            }
            catch (Exception e)
            {
                msg = "请重试";
            }
            string result = (msg == "") ? "OK" : "ERROOR";


            return Json(new { result, msg, data });

        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic dSheet)  //[FromBody] dynamic sheet)
        {
            //
            string s = JsonConvert.SerializeObject(dSheet);
            string operKey = dSheet.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);

            MyLogger.LogMsg("in InventorySheet,sheet:" + s, companyID);

            SheetInventory sheet = JsonConvert.DeserializeObject<SheetInventory>(s);
            sheet.Init();
            string msg = await sheet.Save(cmd);
            string result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no });
        }

        [HttpPost]
        public async Task<IActionResult> SaveAndApprove([FromBody] SheetInventory sheet)
        {
            sheet.Init();
            //InventorySheetModel model = new InventorySheetModel(cmd);
            //string msg = await sheet.CheckBatch(cmd, model, sheet.OperKey);
            //if (msg == "")
            //{
            //    msg = await sheet.SaveAndApprove(cmd);
            //}
            string msg = await sheet.SaveAndApprove(cmd);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time, sheet.approve_time });
        }

        [HttpPost]
        public async Task<IActionResult> Red([FromBody] dynamic data)
        {
            string sheet_id = data.sheet_id;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetInventory sheet = new SheetInventory(LOAD_PURPOSE.APPROVE);
            SheetInventChange sheetC = new SheetInventChange(SheetInventChange.IS_REDUCE.NOT_REDUCE, LOAD_PURPOSE.SHOW);
            string msg = await sheet.Red(cmd, companyID, sheet_id, operID, "");
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time });
        }
        [HttpPost]
        public async Task<IActionResult> Delete([FromBody] dynamic data)
        {
            string sheet_id = data.sheet_id;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetInventory sheet = new SheetInventory(LOAD_PURPOSE.SHOW);

            string msg = await sheet.Delete(cmd, companyID, sheet_id, operID);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time });
        }
        [HttpGet]
        public async Task<IActionResult> GetSheetToPrint(string operKey, string sheet_id, SHEET_TYPE sheet_type)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetInventory sheet = new SheetInventory(LOAD_PURPOSE.SHOW);
            cmd.ActiveDatabase = "";
            await sheet.Load(cmd, companyID, sheet_id);
            await sheet.LoadInfoForPrint(cmd, false);
            string msg = "";
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, sheet });

        }
        [HttpPost]
        public async Task<IActionResult> AppendBrief([FromBody] dynamic data)
        {
            string sheetID = data.sheetID;
            string newBrief = data.newBrief;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetInventory sheet = new SheetInventory(LOAD_PURPOSE.APPROVE);

            string msg = await sheet.AppendBrief(cmd, companyID, sheetID, newBrief);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, });
        }
    }
}