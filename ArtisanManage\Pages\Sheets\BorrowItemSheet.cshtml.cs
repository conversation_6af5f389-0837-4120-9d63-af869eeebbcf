using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Pages.Sheets;
using ArtisanManage.Services;
using ArtisanManage.Services.SheetService;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Threading.Tasks;
using NPOI.HSSF.Record;
using Newtonsoft.Json.Linq;
using System.Linq;
using System.Net.Http;
using System.Net;
using Microsoft.CodeAnalysis.Operations;
using NPOI.SS.Formula.Functions;
using NPOI.POIFS.Crypt.Dsig;
using System.ComponentModel.Design;
using Org.BouncyCastle.Asn1.X9;
using System.Threading.Tasks.Dataflow;
using Microsoft.VisualStudio.TextTemplating;
using Microsoft.AspNetCore.Http;
using NPOI.XSSF.UserModel;
using System.IO;
using System.Text;
using static ArtisanManage.Models.PageQueryModel;
using SixLabors.ImageSharp.Memory;
using System.Xml;

namespace ArtisanManage.Pages
{
    public class BorrowItemSheetModel : PageSheetModel<SheetRowBorrowItem>
    { 

        public string SheetTitle = "";
        public bool EnablePiaoZhengTong = false;
        public BorrowItemSheetModel(CMySbCommand cmd) : base(MenuId.sheetBorrowItem)
        {
            this.cmd = cmd;
            PageName = "BorrowItemSheet";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"sheet_no",new DataItem(){Title="单号",UseJQWidgets=false}},
                {"order_source_name",new DataItem(){Title="来源",UseJQWidgets=false}},
                {"sheet_id",new DataItem(){Title="sheet_id", CtrlType="hidden", FldArea="divHead"}},
                {"red_flag",new DataItem(){Title="red_flag", CtrlType="hidden", FldArea="divHead"}},
                {"order_source",new DataItem(){Title="order_source", CtrlType="hidden", FldArea="divHead"}},//这里不要注释掉

                {"pay_bill_id",new DataItem(){FldArea="divHead", Title="付款单据", CtrlType="hidden"}},
                {"payb_status_name",new DataItem(){Title="付款单据状态",UseJQWidgets=false}},

                {"sheet_type",new DataItem(){Title="sheet_type", CtrlType="hidden", FldArea="divHead"}},
                {"sheetType",new DataItem(){Title="sheetType", CtrlType="hidden", FldArea="divHead"}},
                //{"supcust_id",new DataItem(){FldArea="divHead",Title="客    户",LabelFld="sup_name", ButtonUsage="event", AlwaysShow=true, SqlForOptions="select supcust_id as v,sup_name as l,py_str as z from info_supcust where supcust_flag ilike '%C%' and COALESCE(status,'1')='1'",Width="220", DropDownWidth = "300"}},
                {"supcust_id",CommonTool.GetDataItem("supcust_id",new DataItemChange{ForQuery=false,Width="200", AlwaysShow=true,ButtonUsage="event"}) },
                {"seller_id",new DataItem(){FldArea="divHead",Title="业务员",LabelFld="seller_name",ButtonUsage="list",SqlForOptions=CommonTool.selectSellers}},//SqlForOptions="select oper_id as v,oper_name as l,py_str as z from info_operator"
                {"senders_id",new DataItem(){FldArea="divHead",Title="送货员",LabelFld="senders_name",ButtonUsage="list",SqlForOptions=CommonTool.selectSenders,GetOptionsOnLoad=true, Checkboxes = true} },//SqlForOptions="select oper_id as v,oper_name as l,py_str as z from info_operator"
                {"getter_id",new DataItem(){FldArea="divHead",Title="收货人",Hidden=true, LabelFld="getter_name",ButtonUsage="list",SqlForOptions=CommonTool.selectGetters}},//SqlForOptions="select oper_id as v,oper_name as l,py_str as z from info_operator"
                //{"department_id",new DataItem(){FldArea="divHead",Title="部门",Hidden=true, CtrlType="jqxDropDownTree", LabelFld="department_name",ButtonUsage="list",SqlForOptions=CommonTool.selectDepartments}},//SqlForOptions="select oper_id as v,oper_name as l,py_str as z from info_operator"
                 {"department_id",new DataItem(){Title="部门",Hidden=true, FldArea="divHead",LabelFld="department_name", CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=false,DropDownWidth="150",
                    SqlForOptions=CommonTool.selectDepartments
                }},
                {"branch_id",new DataItem(){FldArea="divHead", Title="仓    库",LabelFld="branch_name", Restrict = "sheet_jh", ButtonUsage="list",SqlForOptions=CommonTool.selectBranch}},
                {"send_van_id",new DataItem(){FldArea="divHead",Title="送货车辆",LabelFld="send_van_name", ButtonUsage="list",Hidden=true,SqlForOptions=CommonTool.selectVan}},
                {"happen_time",new DataItem(){FldArea="divHead", Title="发生时间",CtrlType="jqxDateTimeInput",PlaceHolder="当前时间"}},
                {"send_time",new DataItem(){FldArea="divHead",Title="送货时间",CtrlType="jqxDateTimeInput",PlaceHolder="当前时间",Hidden=true }},
                {"TempHappenTime",new DataItem(){Title="TempHappenTime", CtrlType="hidden",  FldArea="divHead"}},
                {"isRedAndChange",new DataItem(){Title="isRedAndChange",CtrlType="hidden",  FldArea="divHead"} },
                {"old_sheet_id",new DataItem(){Title="old_sheet_id",CtrlType="hidden",  FldArea="divHead"} },
                {"make_brief",new DataItem(){FldArea="divHead",Title="备    注"}},
                {"approve_brief",new DataItem(){FldArea="divHead",Title="修改记录",HideIfEmpty=true}},
              //  {"visit_id",new DataItem(){Title="visit_id", CtrlType="hidden", FldArea="divHead"}},
               // {"useSmallUnit",new DataItem(){FldArea="divHead",Title="С��λ¼��",CtrlType="jqxCheckBox"}},
             
                {"defaultUnit",new DataItem(){Title="默认单位",FldArea="divHead", ButtonUsage="list", Source = "[{v:'b',l:'大'},{v:'s',l:'小'},{v:'m',l:'中'}]"}},
                {"sup_addr",new DataItem(){Title="客户地址", CtrlType="hidden", FldArea="divHead"}},
                {"receive_addr",new DataItem(){FldArea="divHead",Width="390", Hidden=true,KeepNoValueLabel=true, Title="收货地址",LabelFld="receive_addr_desc",ButtonUsage="list",
                     SqlForOptions="select addr_id as v,addr_desc as l from info_client_address where company_id=~COMPANY_ID and client_id=~CONDI_DATA_ITEM order by addr_order",CONDI_DATA_ITEM="supcust_id"}},
                

                {"boss_name",new DataItem(){Title="老板姓名", CtrlType="hidden", FldArea="divHead"}},
                {"acct_cust_id",new DataItem(){Title="结算单位", CtrlType="hidden", FldArea="divHead"}},
                {"mobile",new DataItem(){Title="客户电话", Hidden=true, FldArea="divHead"}},
                {"seller_mobile",new DataItem(){Title="业务电话", CtrlType="hidden", FldArea="divHead"}},

                {"sale_amount",new DataItem(){FldArea="divTail",Title="销售",Width="60",Hidden=true,HideOnLoad=false, Disabled=true}},
                {"return_amount",new DataItem(){FldArea="divTail",Title="退货",Width="60",Hidden=true,HideOnLoad=false,Disabled=true}},
                {"total_amount",new DataItem(){FldArea="divTail", Hidden=true, Title="合计金额",Width="80",Value="0"}},
                {"available_amount",new DataItem(){FldArea="divTail", Hidden=true, Title="可还总额",Width="80",Value="0", editable=false}},
                {"total_quantity",new DataItem(){FldArea="divTail", Hidden=true, Title="合计数量",Width="80"}},
                {"now_disc_amount",new DataItem(){FldArea="divTail",Title="优惠金额",Width="80",Value="0", Hidden = true, HideOnLoad = true}},
                {"no_disc_amount",new DataItem(){FldArea="divTail",Title="折后合计",Width="80", Hidden = true, HideOnLoad = true}},


                {"payway1_id",new DataItem(){FldArea="divTail",HideGroup="payway1",AlwaysShow=true, HideClass="payway",Title="",LabelFld="payway1_name",ClassName="itemLeft",InnerTitle="支付方式1",
                    ButtonUsage="list",SqlForOptions=CommonTool.selectPayWay_Sale, FirstOptionAsDefault=true,Width="80",GetOptionsOnLoad=true, Hidden = true, HideOnLoad = true}},

                {"payway1_amount",new DataItem(){FldArea="divTail",HideGroup="payway1",AlwaysShow=true, HideClass="payway", Title="",ClassName="itemRight",PlaceHolder="支付金额",Width="80",InnerTitle="支付金额1", Hidden = true, HideOnLoad = true}},
                {"payway2_id",new DataItem(){FldArea="divTail",HideGroup="payway2",HideClass="payway", Title="",LabelFld="payway2_name",ClassName="titleItem",PlaceHolder="支付方式",InnerTitle="支付方式2",Hidden=true,
                    ButtonUsage="list",SqlForOptions=CommonTool.selectPayWay_Sale,Width="80",GetOptionsOnLoad=true}},
                {"payway2_amount",new DataItem(){FldArea="divTail",HideGroup="payway2",HideClass="payway", Title="",InnerTitle="支付金额2",PlaceHolder="支付金额",Width="80",Hidden=true}},
                {"payway3_id",new DataItem(){FldArea="divTail",HideGroup="payway3",HideClass="payway", Title="",LabelFld="payway3_name",Hidden=true,InnerTitle="支付方式3",ClassName="titleItem",PlaceHolder="支付方式", ButtonUsage="list",SqlForOptions=CommonTool.selectPayWay_Sale,Width="80",GetOptionsOnLoad=true}},
                {"payway3_amount",new DataItem(){FldArea="divTail",HideGroup="payway3",HideClass="payway", Title="",InnerTitle="支付金额3",PlaceHolder="支付金额",Hidden=true, Width="80"}},

                {"left_amount",new DataItem(){FldArea="divTail",Title="欠款",AlwaysShow=true, Width="80",Disabled=true, Hidden = true, HideOnLoad = true}},
                {"LeftAmount",new DataItem(){FldArea="divTail",Title="总欠", Width="80",Disabled=true, Hidden = true, HideOnLoad = true}},
                {"total_weight",new DataItem(){FldArea="divTail",Title="重量(kg)", Width="80",Disabled=true,Hidden=true}},

                {"maker_id",new DataItem(){CtrlType="hidden", Title="制单人", FldArea="divHead"}},
                {"maker_name",new DataItem(){UseJQWidgets=false, Title="制单人", Width="80"}},
                {"make_time",new DataItem(){UseJQWidgets=false, Title="制单时间"}},
                {"approver_id",new DataItem(){UseJQWidgets=false, Title="审核人", Width="80"}},
                {"approver_name",new DataItem(){UseJQWidgets=false, Title="审核人", Width="80"}},
                {"approve_time",new DataItem(){UseJQWidgets=false, Title="审核时间"}},
                 {"review_time",new DataItem(){UseJQWidgets=false, Title="复核时间"}},
                {"reviewer_name",new DataItem(){UseJQWidgets=false, Title="复核人", Width="80"}},
                {"visit_id",new DataItem(){FldArea="divTail",SqlFld="visit_id",Title="回访",Width="80",Hidden=true}},
                {"order_sheet_id",new DataItem(){UseJQWidgets=false, Title="order_sheet_id"}},
                {"order_sheet_no",new DataItem(){UseJQWidgets=false, Title="order_sheet_no"}},
                {"red_sheet_id",new DataItem(){Title="red_sheet_id", CtrlType="hidden", FldArea="divHead"}},
                //{"red_sheet_no",new DataItem(){UseJQWidgets=false, Title="red_sheet_no"}},
                {"sheet_id_red_me",new DataItem(){Title="sheet_id_red_me", CtrlType="hidden", FldArea="divHead"}},
                {"prepay_sub_ids",new DataItem(){Title="prepay_sub_ids", CtrlType="hidden", FldArea="divHead"}},
                {"appendix_photos",new DataItem(){Title="appendix_photos", CtrlType="hidden", FldArea="divHead"}},
                { "acct_type_way",new DataItem(){FldArea="divTail",Title="结算方式",Disabled=true,Hidden=true,Width="80"}},
                { "acct_type",new DataItem(){FldArea="divTail",Hidden=true,HideOnLoad=true}},
                {"barcode_input",new DataItem(){FldArea="divHead",Title="条码录入",Hidden=true}},

                // 新增：借货模式选择
                {"borrow_mode",new DataItem(){FldArea="divHead",Title="借货模式",LabelFld="borrow_mode_name",ButtonUsage="list",
                    Source="[{v:'QTY',l:'按数量'},{v:'AMT',l:'按金额'}]",Value="QTY",Label="按数量",Width="100"}},

                // 独立还货单选择目标借货单
                {"target_borrow_sheet_id",new DataItem(){FldArea="divHead",Title="还给借货单",LabelFld="target_borrow_sheet_no",
                    ButtonUsage="event",Width="150",Hidden=true}}, // 默认隐藏，在还货单时显示
                { "customer_borrow_balance", new DataItem(){ FldArea = "divHead", Title="客户借货余额", Hidden = true, HideOnLoad = true, editable = false} },
               // {"sheet_no_red_me",new DataItem(){UseJQWidgets=false, Title="sheet_no_red_me"}}
               // {"branch_state",new DataItem(){title="状态",labelFld="branch_status_name",labelInDB=false,value="1",label="正常", buttonUsage="list", source = "[{v:1,l:'正常'},{v:0,l:'停用'}]"}}
            };



            /*
            Grids = new Dictionary<string, FormDataGrid>()
            {
                {"gridUnit" ,new FormDataGrid(){
                   Columns = new Dictionary<string, DataItem>()
                   {
                       {"unit_no",new DataItem(){title="单位",width="100",url="../api/ItemEdit/GetUnits"}},
                       {"unit_no",new DataItem(){title="单位",width="100",SqlForOptions="select unit_no from info_item_unit",getOptionsOnLoad=true,buttonUsage="list"}},
                       {"unit_factor",new DataItem(){title="包装数",width="80"}},
                   },
                   TableName="info_item_multi_unit",
                   IdFld="item_no",
                   SelectFromSQL="from info_item_multi_unit where item_no='~ID'"
                }}
            };*/
        }
        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {

            dynamic setting = JsonConvert.DeserializeObject(JsonCompanySetting);
            if (setting != null)
            {
                if (setting.saleSheetDefaultUnit != null)
                {
                    DataItems["defaultUnit"].Value = setting.saleSheetDefaultUnit;
                    DataItems["defaultUnit"].Label = setting.saleSheetDefaultUnit == "s" ? "小" : setting.saleSheetDefaultUnit == "b" ? "大" : setting.saleSheetDefaultUnit == "m" ? "中" : "";
                }
                if (setting.openTicketAccessSys != null && setting.openTicketAccessSys.ToString().ToLower() == "true")
                {
                    EnablePiaoZhengTong = true;
                }
                if (setting.branchForReturn != null && DataItems["sheet_id"].Value =="" && (DataItems["sheetType"].Value=="T"|| DataItems["sheetType"].Value == "TD"))
                {
                    DataItems["branch_id"].InitValue = (string)setting.branchForReturn;                    
                }
            }

            if (!string.IsNullOrEmpty(DataItems["defaultUnit"].Value))
            {
                DataItems["defaultUnit"].Label = DataItems["defaultUnit"].Value == "s" ? "小" : DataItems["defaultUnit"].Value == "b" ? "大" : DataItems["defaultUnit"].Value == "m" ? "中" : "";
            }
            else if (setting != null)
            {
                if (setting.buySheetDefaultUnit != null)
                {
                    DataItems["defaultUnit"].Value = setting.buySheetDefaultUnit;
                    DataItems["defaultUnit"].Label = setting.buySheetDefaultUnit == "s" ? "小" : setting.buySheetDefaultUnit == "b" ? "大" : setting.buySheetDefaultUnit == "m" ? "中" : "";
                }
            }

            if (DataItems["order_sheet_id"].Value != "")
            {
                DataItems["senders_id"].Necessary = true;
            }

            if (DataItems["LeftAmount"].Value == "" || DataItems["LeftAmount"].Value == "0")
            {
                DataItems["LeftAmount"].Hidden = true;
            }

             

            string sqlPrepay = $"select string_agg(sub_id::text,',') sub_ids from cw_subject where company_id = {company_id} and sub_type in ('YS') and not coalesce(is_order,false)";
            dynamic recordPrepay = await CDbDealer.Get1RecordFromSQLAsync(sqlPrepay, cmd);
            if (recordPrepay != null) DataItems["prepay_sub_ids"].Value = recordPrepay.sub_ids;//payway1_id设置支付方式列表时，根据这个列表决定是否显示
            if (DataItems["sheet_type"].Value == "SHEET_RETURN_ITEM")
            {
                PageMenuID = MenuId.sheetReturnItem;
            }

            bool seeSalePrice = true;
            if (JsonOperRights.IsValid())
            {
                dynamic operRights = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonOperRightsOrig);
                if (operRights != null && operRights.delicacy != null && operRights.delicacy.seeSalePrice!=null) seeSalePrice = ((string)operRights.delicacy.seeSalePrice.value).ToLower() != "false";
            }
             if (DataItems["sheet_type"].Value == "SHEET_BORROW_ITEM")
            {
                DataItems["target_borrow_sheet_id"].Hidden = DataItems["target_borrow_sheet_id"].HideOnLoad = true;
                DataItems["available_amount"].Hidden = DataItems["available_amount"].HideOnLoad = true;
            }
                DataItems["now_disc_amount"].Hidden = DataItems["now_disc_amount"].HideOnLoad = true;
                DataItems["no_disc_amount"].Hidden = DataItems["no_disc_amount"].HideOnLoad = true;
                DataItems["payway1_id"].Hidden = DataItems["payway1_id"].HideOnLoad = true;
                DataItems["payway1_amount"].Hidden = DataItems["payway1_amount"].HideOnLoad = true;
                DataItems["payway2_id"].Hidden = DataItems["payway2_id"].HideOnLoad = true;
                DataItems["payway2_amount"].Hidden = DataItems["payway2_amount"].HideOnLoad = true;
                DataItems["payway3_id"].Hidden = DataItems["payway3_id"].HideOnLoad = true;
                DataItems["payway3_amount"].Hidden = DataItems["payway3_amount"].HideOnLoad = true;
                DataItems["left_amount"].Hidden = DataItems["left_amount"].HideOnLoad = true;
                DataItems["available_amount"].editable = false;
            if (!seeSalePrice)
            {
                DataItems["total_amount"].Hidden = DataItems["total_amount"].HideOnLoad = true;
                DataItems["now_disc_amount"].Hidden = DataItems["now_disc_amount"].HideOnLoad = true;
                DataItems["no_disc_amount"].Hidden = DataItems["no_disc_amount"].HideOnLoad = true;
                DataItems["payway1_id"].Hidden = DataItems["payway1_id"].HideOnLoad = true;
                DataItems["payway1_amount"].Hidden = DataItems["payway1_amount"].HideOnLoad = true;
                DataItems["payway2_id"].Hidden = DataItems["payway2_id"].HideOnLoad = true;
                DataItems["payway2_amount"].Hidden = DataItems["payway2_amount"].HideOnLoad = true;
                DataItems["payway3_id"].Hidden = DataItems["payway3_id"].HideOnLoad = true;
                DataItems["payway3_amount"].Hidden = DataItems["payway3_amount"].HideOnLoad = true;
                DataItems["left_amount"].Hidden = DataItems["left_amount"].HideOnLoad = true;
            }

        }
        public class FlowSetting
        {
            public bool FlowExistMove { get; set; }
            public bool FlowExistMoveBack { get; set; }
            public bool PrintCheckWhole { get; set; }
            public bool PrintCheckOpenStock { get; set; }
        }

        //public override async Task<SheetBase<SheetRowBorrowItem>> GetSheetByOrder(CMySbCommand cmd, string operKey, string order_sheet_id, bool isDefaultFromSheetType)
        //{
        //    dynamic orderSheet = null;
        //    if (isDefaultFromSheetType) orderSheet = new SheetSaleOrder(SHEET_RETURN.EMPTY, LOAD_PURPOSE.SHOW);
        //    else orderSheet = new SheetPlaceholderOrder(SHEET_RETURN.EMPTY, LOAD_PURPOSE.SHOW);

        //    await orderSheet.Load(cmd, company_id, order_sheet_id);
        //    //if(!isDefaultFromSheetType) orderSheet = JsonConvert.DeserializeObject<SheetSaleOrder>(JsonConvert.SerializeObject(orderSheet));
        //    SheetSale saleSheet = orderSheet.ToSaleSheet(operKey);
        //    if (orderSheet.van_id != "" && orderSheet.move_stock.ToLower() == "true")
        //    {
        //        saleSheet.branch_id = orderSheet.van_id;
        //        saleSheet.branch_name = orderSheet.van_name;
        //    }
        //    return saleSheet;
        //}
        public async Task OnGet(bool forReturn)
        {
            SheetBorrowItem sheet = new SheetBorrowItem(forReturn ? SHEET_BORROW_RETURN.IS_RETURN : SHEET_BORROW_RETURN.NOT_RETURN, LOAD_PURPOSE.SHOW);
            if (forReturn)
            {
                PageMenuID = MenuId.sheetReturnItem;
                // 设置单据类型为还货单
                DataItems["sheet_type"].Value = "SHEET_RETURN_ITEM";
                DataItems["sheetType"].Value = "HH";
                // 还货单时显示借货单选择字段
                DataItems["target_borrow_sheet_id"].Hidden = false;
            }
            else
            {
                // 设置单据类型为借货单
                DataItems["sheet_type"].Value = "SHEET_BORROW_ITEM";
                DataItems["sheetType"].Value = "JH";
                // 借货单时隐藏借货单选择字段
                DataItems["target_borrow_sheet_id"].Hidden = true;
            }
            //string isOpenPlaceholderOrder = CPubVars.RequestV(Request, "isOpenPlaceholderOrder");
            await InitGet(cmd, sheet);

            //string items_id = "";
            //foreach (SheetRowItem sheetRow in this.Sheet.SheetRows)
            //{
            //    if (items_id != "") items_id += ","; items_id += sheetRow.item_id;
            //}
            sheet = (SheetBorrowItem)this.Sheet;
            List<dynamic> sheetRows = JsonConvert.DeserializeObject<List<dynamic>>(JsonConvert.SerializeObject(sheet.SheetRows));
            await GetItemsInfoBySheetRowsInfo(this.company_id, sheetRows,new {  branch_id=sheet.branch_id, supcust_id = sheet.supcust_id });
            //await GetItemsInfoBySheetInfo(this.company_id, items_id, new { sheet.supcust_id, sheet.branch_id,branch_position="0" });

            // 获取客户借货余额
            if (!string.IsNullOrEmpty(sheet.supcust_id) && sheet.supcust_id != "0")
            {
                string sql = $"select balance_amount from borrow_sheet_balance where company_id = {company_id} and supcust_id = {sheet.supcust_id}";
                dynamic balanceRec = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                if (balanceRec != null)
                {
                    DataItems["customer_borrow_balance"].Value = balanceRec.balance_amount.ToString();
                    sheet.customer_borrow_balance = CPubVars.ToDecimal(balanceRec.balance_amount);
                }
            }

            SheetTitle = sheet.sheet_type == SHEET_TYPE.SHEET_RETURN_ITEM ? "还货单" : "借货单";
        }
        class PricePlan
        {
            public string plan_id="";
            public string plan_name="";
        }
        public class PriceSet
        {
            public string item_id = "", class_id = "", class_discount = "", s_price = "", m_price = "", b_price = "";
        }
        public class PriorityItem
        {
            public string plan_id = "", item_id = "", priority = "";
            public List<PriceSet> PriceList = new List<PriceSet>();

        }
        public class ItemInfo {
            public string item_id = "", item_name = "",item_no="", item_spec = "", valid_days = "",brand_name="", virtual_produce_date="",branch_id="",branch_position="",batch_id="";

            public string s_barcode = "", m_barcode = "", b_barcode = "";
 
    
            public string produce_date = "",stock_qty_unit = "",sell_pend_qty_unit ="", oldest_stock_qty="", oldest_stock_qty_unit="",oldest_usable_stock_qty_unit="",oldest_sell_pend_qty_unit=""; 
            public string usable_stock_qty_unit = "", recent_unit_no = "", mum_attributes ="";
            public string s_retail_price = "";
            public string stock_qty = "", batch_level = "",batch_no="";
            public string son_mum_item = "";
            public List<ItemUnit> units = new List<ItemUnit>();  
        }
        public class ItemUnit {
            public string unit_no = "", unit_factor = "", unit_type = "", price = "", recent_orig_price = "";
            public string recent_price = "",recent_time="", wholesale_price = "", retail_price = "", barcode = "", buy_price = "",lowest_price="",cost_price_spec = "",cost_price_buy="", cost_price_recent="";
            public string cost_price_avg = "", unit_weight = "", recentpricetime="", recent_retail_price="";
            public Dictionary<string, string> planPrices = null;
        }
        public override async Task<JsonResult> GetItemsInfoBySheetRows(string companyID, bool bGetAttrs, List<dynamic> sheetRows,dynamic otherInfo)
        {
             return await GetItemsInfoBySheetRows_static(cmd,companyID, bGetAttrs,sheetRows,otherInfo);
        }

        public static async Task<JsonResult> GetItemsInfoBySheetRows_static(CMySbCommand cmd,string companyID, bool bGetAttrs,  List<dynamic> sheetRows,dynamic otherInfo)
        {
            string condi = "";
            string orderBy = "";
            string items_id = "";
            string sqlForSheetRow = "";
            string supcust_id = otherInfo.supcust_id;
            string branch_id = otherInfo.branch_id==""?"-1":otherInfo.branch_id;
            foreach (dynamic sheetRow in sheetRows)
            {
                string rowBranchId = sheetRow.branch_id.ToString() == "" ? branch_id : sheetRow.branch_id;
                // ͬ������û��ָ��branch_id�������
                // if (rowBranchId == "") rowBranchId = "0";
                string rowBranchPosition = sheetRow.branch_position.ToString() == "" ? "0" : sheetRow.branch_position;
                string rowBatchId = sheetRow.batch_id.ToString() == "" ? "0" : sheetRow.batch_id;
                if (items_id != "") items_id += ","; items_id += sheetRow.item_id;
                if (sqlForSheetRow != "")
                {
                    sqlForSheetRow += $@"or (branch_id = {rowBranchId} and branch_position = {rowBranchPosition} and batch_id = {rowBatchId} and item_id = {sheetRow.item_id})";
                }
                else
                {
                    sqlForSheetRow = $@"(branch_id = {rowBranchId} and branch_position = {rowBranchPosition} and batch_id = {rowBatchId} and item_id = {sheetRow.item_id})";
                }
            }
            if (sqlForSheetRow != "") sqlForSheetRow = "and (" + sqlForSheetRow + ")";
            if (items_id != "")
            {
                condi = $" and mu.item_id in ({items_id})";
                orderBy = $" order by position(mu.item_id::text in '{items_id}'),unit_factor desc";
            }
            else
                orderBy = $" order by mu.item_id desc";

            SQLQueue QQ = new SQLQueue(cmd);
            string sql = @$"
select mu.item_id,ip.item_name,ip.item_no,brand_name,mu.unit_no,other_class,ip.mum_attributes,rpd.produce_date as virtual_produce_date,ip.cost_price_spec,branch_position,branch_id,batch_id,s.setting->>'recentPriceTime' as recentpricetime,
(case when rp.is_recent then rp.unit_no end) recent_unit_no,mu.retail_price,rp.happen_time as recent_time, mu.weight unit_weight, s_retail_price,unit_factor,unit_type,stock.stock_qty,batch_level,
      
        unit_from_s_to_bms ((stock.stock_qty::numeric),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) stock_qty_unit,
        unit_from_s_to_bms ((stock.sell_pend_qty::numeric),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) sell_pend_qty_unit,
        unit_from_s_to_bms ((stock.usable_stock_qty::numeric),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) usable_stock_qty_unit,
        mu.barcode,b_barcode,m_barcode,s_barcode,mu.wholesale_price,mu.buy_price,mu.lowest_price,ip.cost_price_avg,ip.cost_price_recent,recent_price,coalesce(recent_orig_price,mu.wholesale_price)  recent_orig_price,item_spec,valid_days
from info_item_multi_unit mu
left join 
(
    select item_id,(b->>'f1')::real as b_unit_factor,b->>'f2' as b_unit_no,                             b->>'f4' as b_barcode,
                   (m->>'f1')::real as m_unit_factor,m->>'f2' as m_unit_no,                             m->>'f4' as m_barcode,
                   (s->>'f1')::real as s_unit_factor,s->>'f2' as s_unit_no, s->>'f3' as s_retail_price, s->>'f4' as s_barcode
    from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,retail_price,barcode)) as json from info_item_multi_unit where company_id={companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s jsonb,m jsonb,b jsonb)
) mu1 on mu.item_id = mu1.item_id
left join company_setting s on s.company_id = mu.company_id
left join (select item_id,produce_date from item_recent_produce_date where company_id={companyID}) rpd on mu.item_id=rpd.item_id
left join (select item_id,branch_id,branch_position,batch_id,stock_qty,sell_pend_qty, (COALESCE(stock_qty,0)-COALESCE(sell_pend_qty,0)) usable_stock_qty from stock where company_id = {companyID}  {sqlForSheetRow}) stock on stock.item_id = mu.item_id
left join (select * from client_recent_prices where company_id = {companyID} and supcust_id = {supcust_id}) rp on rp.item_id = mu.item_id and rp.unit_no = mu.unit_no
left join info_item_prop ip on mu.item_id=ip.item_id  
left join info_item_brand iib on iib.brand_id = ip.item_brand
where mu.company_id={companyID} {condi} {orderBy};";
            QQ.Enqueue("items", sql);
            sql = @$" 
                    SELECT array_to_json(array_agg(row_to_json(t))) attr_options FROM
                    (
                        SELECT opt_id, opt_name, attr.attr_id FROM info_attr_opt opt left join info_attribute attr on opt.attr_id = attr.attr_id where opt.company_id ={companyID} and not attr.spec_opt_in_item order by opt.order_index
                    ) t";
            QQ.Enqueue("attr_options", sql);

            if (bGetAttrs)
            {
                sql = $"select attr_id,attr_name,distinct_stock,order_index from info_attribute where company_id={companyID} order by order_index";
                QQ.Enqueue("attrs", sql);
            }

            List<ExpandoObject> units = null;
            List<ExpandoObject> attributes = null;
            dynamic attrOptions = null;
            //List<ExpandoObject> batchStock = null;
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                string tbl = QQ.Dequeue();
                if (tbl == "items")
                {
                    units = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (tbl == "attr_options")
                {
                    dr.Read();
                    string s = CPubVars.GetTextFromDr(dr, "attr_options");
                    if (s != "")
                    {
                        attrOptions = JsonConvert.DeserializeObject(s);
                    }
                }
                else if (tbl == "attrs")
                {
                    attributes = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();


            Dictionary<string, ItemInfo> items = new Dictionary<string, ItemInfo>();

            #region ��ȡ�۸����
            bool doPricePlan = false;
            // Dictionary<string, string> planIDs = new Dictionary<string, string>();
            Dictionary<string, PricePlan> dicPlans = new Dictionary<string, PricePlan>();
            if (supcust_id != "-1" && supcust_id != "0")
            {
                sql = $"select region_id,other_region,sup_rank,sup_group from info_supcust where company_id = {companyID} and supcust_id={supcust_id}";
                dynamic supInfo = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                var groupID = supInfo.sup_group == "" ? "null" : supInfo.sup_group;
                var otherRegion = supInfo.other_region;
                var rankID = supInfo.sup_rank == "" ? "null" : supInfo.sup_rank;
                /*sql = $@" select null flow_id,supcust_id,null group_id,null region_id,null rank_id,price1,price2,price3 from price_strategy_client where company_id = {companyID} and supcust_id={supcust_id}
                                union
                          select flow_id,null supcust_id,group_id,region_id,rank_id,price1,price2,price3 from price_strategy_class  where company_id={companyID} and (position(concat('/',region_id,'/') in '{otherRegion}')>0   or region_id is null) and (group_id::text = '{groupID}' or group_id is null) and (rank_id is null or rank_id::text = '{rankID}') and not (region_id is null and group_id is null and rank_id is null)  order by supcust_id,flow_id desc";
                */
                sql = $@" select null flow_id,supcust_id,null group_id,null region_id,null rank_id,price1,price2,price3 from price_strategy_client where company_id = {companyID} and supcust_id={supcust_id}
                                union
                          select flow_id,null supcust_id,group_id,region_id,rank_id,price1,price2,price3 from price_strategy_class  where company_id={companyID} and (position(concat('/',region_id,'/') in '{otherRegion}')>0   or region_id is null) and (group_id::text = '{groupID}' or group_id is null) and (rank_id is null or rank_id::text = '{rankID}') order by supcust_id,flow_id desc";
                sql = $@"
select a.*,m1.plan_name plan1_name,m2.plan_name plan2_name,m3.plan_name plan3_name from ({sql}) 
a
left join price_plan_main m1 on isnumeric(a.price1) and yj_parse_int(a.price1)=m1.plan_id and m1.company_id={companyID}
left join price_plan_main m2 on isnumeric(a.price2) and yj_parse_int(a.price2)=m2.plan_id and m2.company_id={companyID}
left join price_plan_main m3 on isnumeric(a.price3) and yj_parse_int(a.price3)=m3.plan_id and m3.company_id={companyID}
 ";

                List<ExpandoObject> supPlans = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                if (supPlans.Count > 0)
                {
                    foreach (dynamic plan in supPlans)
                    {
                        if (plan.supcust_id != "") doPricePlan = true;
                        else if (plan.supcust_id == "" && plan.flow_id != "") doPricePlan = true;

                        if (doPricePlan)
                        {
                            //if (plan.price1 != "") planIDs.Add("1", plan.price1);
                            //if (plan.price2 != "") planIDs.Add("2", plan.price2);
                            //if (plan.price3 != "") planIDs.Add("3", plan.price3);
                            if (plan.price1 != "") dicPlans.Add("1", new PricePlan { plan_id = plan.price1, plan_name = plan.plan1_name });
                            if (plan.price2 != "") dicPlans.Add("2", new PricePlan { plan_id = plan.price2, plan_name = plan.plan2_name });
                            if (plan.price3 != "") dicPlans.Add("3", new PricePlan { plan_id = plan.price3, plan_name = plan.plan3_name });

                            break;
                        }
                    }
                }
                else
                {
                    doPricePlan = true;
                    dicPlans.Add("1", new PricePlan { plan_id = "recent", plan_name = "" });
                    dicPlans.Add("2", new PricePlan { plan_id = "wholesale", plan_name = "" });
                }
            }
            #endregion
            #region ʹ�ü۸����
            //Dictionary<string, dynamic> prices = new Dictionary<string, dynamic>();
            Dictionary<string, PriorityItem> prices = new Dictionary<string, PriorityItem>();

            if (doPricePlan && dicPlans.Count > 0)
            {
                string priceSql = "";
                var fld = ""; var orderCondi = "";
                foreach (var p in dicPlans)
                {
                    var plan = p.Value;
                    if (priceSql != "") priceSql += " union ";
                    if (p.Key != null && p.Key != "")
                    {
                        fld = $"{p.Key} as priority,'{plan.plan_id}' plan_id,";
                        orderCondi = $" order by priority,price_item ";
                    }
                    // ���Ҳ�ͬ�����ļ۸�
                    priceSql += @$"
select p.item_id,pi.item_id price_item,{fld}pi.class_id,s_price,m_price,b_price,class_discount
from info_item_prop p 
left join 
(
    select null item_id,null s_price,null m_price,null b_price,     class_id,discount class_discount from price_plan_class where company_id = {companyID} and plan_id::text = '{plan.plan_id}' and class_id is not null
        union
    select      item_id,     s_price,     m_price,     b_price,null class_id,null     class_discount from price_plan_item  where company_id = {companyID} and plan_id::text = '{plan.plan_id}' and item_id in ({items_id})
) pi on (pi.item_id = p.item_id or pi.item_id is null) and (position(concat('/',pi.class_id,'/') in other_class)>0 or pi.class_id is null)
where p.company_id = {companyID} and p.item_id in ({items_id}) ";

                }

                priceSql += orderCondi;
                List<ExpandoObject> plans = await CDbDealer.GetRecordsFromSQLAsync(priceSql, cmd);
                #region �޸Ľ����  { priority_item_id, { priority, plan_id, item_id, prSetting{price_item,class_id.....}}} ���õ�prices

                foreach (dynamic plan in plans)
                {
                    PriorityItem pi = null;
                    if (prices.ContainsKey(plan.priority + '_' + plan.item_id))
                    {
                        pi = prices[plan.priority + '_' + plan.item_id];
                    }
                    else
                    {
                        pi = new PriorityItem();
                        pi.priority = plan.priority;
                        pi.plan_id = plan.plan_id;
                        pi.item_id = plan.item_id;
                        prices.Add((string)plan.priority + '_' + plan.item_id, pi);
                        pi.PriceList = new List<PriceSet>();
                    }

                    if (plan.price_item != "" || plan.class_id != "")
                    {
                        pi.PriceList.Add
                        (
                           new PriceSet
                           {
                               item_id = plan.price_item,
                               class_id = plan.class_id,
                               class_discount = plan.class_discount,
                               s_price = plan.s_price,
                               m_price = plan.m_price,
                               b_price = plan.b_price
                           }
                       );
                    }

                }
                #endregion
            }
            #endregion

            #region ���۵���Ӱ�� --- �ҳ���Ҫʹ�õ��۵ķ�������Ʒ
            dynamic adjustItems = null;
            string adjustPlan = "";

            if (dicPlans.Count > 1 && dicPlans["1"].plan_id == "recent") adjustPlan = dicPlans["2"].plan_id; // ������ѡ����Ϊ����ۼ۵ļ۸����  
            // ��ѡ�޼۸�����һ����Ϊ����ۼ� ���磺  ������ - ����ۼ� - ����һ
            if (dicPlans.Count > 2 && dicPlans["1"].plan_id != "recent" && dicPlans["2"].plan_id == "recent" && dicPlans["3"].plan_id != "recent") adjustPlan = dicPlans["3"].plan_id;
            //if (planIDs.Count ==0) adjustPlan = "wholesale";
            if (adjustPlan != "")
            {
                if (adjustPlan == "wholesale") adjustPlan = "-1";
                if (adjustPlan == "retail") adjustPlan = "0";
                if (adjustPlan == "recent") adjustPlan = "-2";

                //��Ҫ�Ƚ� client_recent_prices ���� happen_time �� item_price_adjust �� adjust_time ��ʱ������
                string selAdjustSql = @$"select string_agg(a.item_id::text,',') items_id from item_price_adjust a left join client_recent_prices r on r.item_id = a.item_id
                                         where a.company_id = {companyID} and r.supcust_id = {supcust_id} and a.plan_id = '{adjustPlan}' and a.adjust_time>r.happen_time and a.item_id in ({items_id}); ";
                adjustItems = await CDbDealer.Get1RecordFromSQLAsync(selAdjustSql, cmd); // �ҳ���Ҫʹ�� �¼۸�� ��Ʒ
            }
            #endregion

            foreach (dynamic unit in units)
            {
                if (unit.unit_factor == "") continue;
                ItemInfo item = null;
                //string rowBranchId = unit.branch_id.ToString()=="" ? branch_id : unit.branch_id.ToString();
                string key = unit.item_id.ToString() +","+ unit.branch_id.ToString() +"_" + unit.branch_position.ToString()+ "_" + unit.batch_id.ToString();
                if (items.ContainsKey(key))
                {
                    item = items[key];
                }
                else
                {
                    item = new ItemInfo();
                    item.item_id = unit.item_id;
                    item.item_name = unit.item_name;
                    item.item_no = unit.item_no;
                    item.branch_id = unit.branch_id;
                    item.branch_position = unit.branch_position;
                    item.batch_id = unit.batch_id;
                    item.item_spec = unit.item_spec;
                    item.s_barcode = unit.s_barcode;
                    item.m_barcode = unit.m_barcode;
                    item.b_barcode = unit.b_barcode;
                    item.valid_days = unit.valid_days;
                    item.virtual_produce_date = unit.virtual_produce_date;
                    item.stock_qty = unit.stock_qty.Replace("-0", "0");
                    item.stock_qty_unit = (unit.stock_qty_unit).Replace("-0", "0");
                    item.sell_pend_qty_unit = (unit.sell_pend_qty_unit).Replace("-0", "0");
                    item.batch_level = unit.batch_level;
                    item.usable_stock_qty_unit = (unit.usable_stock_qty_unit).Replace("-0", "0");
                    item.recent_unit_no = unit.recent_unit_no;
                    item.mum_attributes = unit.mum_attributes;
                    items.Add(key, item);
                    item.units = new List<ItemUnit>();
                    item.brand_name = unit.brand_name;                     
                }
                #region ���ƽ��������Ϣ��ȡ
                float cost_price_recent1;
                float cost_price_recent2;
                float cost_price_recent3;
                dynamic pStr = JsonConvert.DeserializeObject(unit.cost_price_recent);
                //Console.WriteLine("���ת�����ֵ��{0}" + "\n" + "ת��������ͣ�{1} " + "\n", pStr, pStr.GetType());
                if (pStr != null)
                {
                    if (pStr.avg1 != null)
                    {
                        cost_price_recent1 = pStr.avg1;
                    }
                    else
                    {
                        cost_price_recent1 = 0;
                    }
                    if (pStr.avg2 != null)
                    {
                        cost_price_recent2 = pStr.avg2;
                    }
                    else
                    {
                        cost_price_recent2 = 0;
                    }
                    if (pStr.avg3 != null)
                    {
                        cost_price_recent3 = pStr.avg3;
                    }
                    else
                    {
                        cost_price_recent3 = 0;
                    }
                }
                else
                {
                    cost_price_recent1 = 0;
                    cost_price_recent2 = 0;
                    cost_price_recent3 = 0;
                }
                string recent_price_time;
                recent_price_time = unit.recentpricetime;
                if (recent_price_time == "1")
                {
                    unit.cost_price_recent = CPubVars.ToDecimal(cost_price_recent1);
                }
                else if (recent_price_time == "2")
                {
                    unit.cost_price_recent = CPubVars.ToDecimal(cost_price_recent2);
                }
                else if (recent_price_time == "3")
                {
                    unit.cost_price_recent = CPubVars.ToDecimal(cost_price_recent3);
                }
                unit.cost_price_recent = unit.cost_price_recent.ToString();
                #endregion
                #region ʹ�ü۸����
                string planPrice = "";
                foreach (var kp in prices)
                {
                    PriorityItem pi = kp.Value;
                    if (unit.item_id == pi.item_id)
                    {
                        //var plan = pi.plan_id;
                        if (pi.plan_id == "")
                            continue;
                        else if (pi.plan_id == "wholesale")
                        {
                            planPrice = unit.wholesale_price;
                            if (planPrice != "") break;
                        }
                        else if (pi.plan_id == "recent")
                        {
                            // ����ۼ� - ������
                            if (adjustItems != null && adjustItems.items_id.Contains(unit.item_id)) // �����Ҫ�ļۣ���ȡ��һ�εķ����۸�
                                continue;
                            planPrice = unit.recent_price;
                            // unit.recent_time;
                            if (planPrice != "") break;
                        }
                        else if (pi.plan_id == "retail")
                        {
                            planPrice = unit.retail_price;
                            if (planPrice != "") break;
                        }
                        var otherClass = unit.other_class;
                        var selectClass = ""; var selectDisc = ""; var classIndex = -1;
                        foreach (var priceSet in pi.PriceList)
                        {
                            if (priceSet.item_id != "")
                            {
                                if (unit.unit_type == "s") planPrice = priceSet.s_price;
                                if (unit.unit_type == "m") planPrice = priceSet.m_price;
                                if (unit.unit_type == "b") planPrice = priceSet.b_price;
                                if (planPrice != "") break;
                            }
                            else if (priceSet.class_id != "")
                            {
                                var classArr = ((string)otherClass).Split('/');

                                if (Array.IndexOf(classArr, priceSet.class_id) > classIndex)
                                {
                                    classIndex = Array.IndexOf(classArr, priceSet.class_id);
                                    selectClass = priceSet.class_id;
                                    selectDisc = priceSet.class_discount;
                                }
                            }
                            if (planPrice != "") break;
                        }

                        if (selectDisc != "" && unit.wholesale_price != "") planPrice = Convert.ToString(Convert.ToSingle(selectDisc) * Convert.ToSingle(unit.wholesale_price));
                        if (planPrice != "") break;
                    }
                }
                //��ȡ���з�����,�Ա��ڵ���۸�Ԫ��ʱ�������г�
                Dictionary<string, string> dicPlanPrices = null;
                if (dicPlans.Count > 0)
                {
                    foreach (var kp in prices)
                    {
                        PriorityItem pi = kp.Value;

                        if (unit.item_id == pi.item_id)
                        {
                            if (pi.plan_id != "wholesale" && pi.plan_id != "recent" && pi.plan_id != "retail")
                            {
                                var otherClass = unit.other_class;
                                var selectClass = ""; var selectDisc = ""; var classIndex = -1;
                                string showPrice = "";
                                foreach (PriceSet s in pi.PriceList)
                                {
                                    if (s.item_id != "")
                                    {
                                        if (unit.unit_type == "s") showPrice = s.s_price;
                                        if (unit.unit_type == "m") showPrice = s.m_price;
                                        if (unit.unit_type == "b") showPrice = s.b_price;

                                    }
                                    else if (s.class_id != "")
                                    {
                                        var classArr = otherClass.Split('/');
                                        if (Array.IndexOf(classArr, s.class_id) > classIndex)
                                        {
                                            classIndex = Array.IndexOf(classArr, s.class_id);
                                            selectClass = s.class_id;
                                            selectDisc = s.class_discount;
                                        }
                                    }
                                    if (showPrice != "") break;
                                }
                                if (selectDisc != "" && unit.wholesale_price != "") showPrice = Convert.ToString(Convert.ToSingle(selectDisc) * Convert.ToSingle(unit.wholesale_price));
                                var pkp = dicPlans.First(p => p.Value.plan_id == pi.plan_id);
                                if (pkp.Value != null)
                                {
                                    if (dicPlanPrices == null) dicPlanPrices = new Dictionary<string, string>();
                                    if (!dicPlanPrices.ContainsKey(pkp.Value.plan_name))
                                    {
                                        dicPlanPrices.Add(pkp.Value.plan_name, showPrice);
                                    }
                                }
                            }
                        }
                    }
                }


                #endregion
                item.s_retail_price = unit.s_retail_price;
                // List<dynamic> itemUnits = item.units;
                string price = "";
                if (doPricePlan)
                {
                    if (planPrice != "") price = planPrice;
                }
                else if (unit.recent_price != "") price = unit.recent_price;
                else if (unit.wholesale_price != "") price = unit.wholesale_price;
                item.units.Add(new ItemUnit { unit_no = unit.unit_no, planPrices = dicPlanPrices, unit_factor = unit.unit_factor, unit_type = unit.unit_type, price = price, recent_orig_price = unit.recent_orig_price,cost_price_recent=unit.cost_price_recent, recent_price = unit.recent_price, recent_time = CPubVars.GetDateTextNoTime(unit.recent_time), wholesale_price = unit.wholesale_price, retail_price = unit.retail_price, barcode = unit.barcode,cost_price_spec = unit.cost_price_spec, buy_price = unit.buy_price, cost_price_avg = unit.cost_price_avg, unit_weight = unit.unit_weight });
            }
            List<dynamic> spItems = new List<dynamic>();
            if (supcust_id != "" && supcust_id != "-1" && supcust_id != "0" && items.Count > 0)
            {
                string specialPriceSql = @$"SELECT * FROM (SELECT sm.supcust_id,sm.start_time,sm.end_time,(sm.end_time::date-sm.start_time::date)+1 as special_days ,(sm.end_time::date-now()::date)+1 left_days,sd.item_id,sd.unit_no,sd.unit_factor,mu.unit_type,sd.special_price,sd.happen_time,ip.son_mum_item,row_number() over(partition by sd.item_id order by sd.happen_time desc ) rn FROM sheet_special_price_detail sd 
LEFT JOIN sheet_special_price_main sm on sd.company_id = sm.company_id and sd.sheet_id= sm.sheet_id 
LEFT JOIN info_item_multi_unit mu on mu.company_id = sd.company_id and mu.item_id = sd.item_id and mu.unit_factor = sd.unit_factor 
LEFT JOIN info_item_prop ip on ip.company_id = sd.company_id and ip.item_id = sd.item_id
WHERE sd.company_id = {companyID} and sm.approve_time is not null and sm.red_flag is null and sm.end_time::date >= NOW()::date and sm.supcust_id = {supcust_id})t
WHERE t.rn = 1 ";
                List<ExpandoObject> specialPriceItems = await CDbDealer.GetRecordsFromSQLAsync(specialPriceSql, cmd);
                if (specialPriceItems.Count > 0)
                {
                    foreach (dynamic spItem in specialPriceItems)
                    {
                        if (items.ContainsKey(spItem.item_id))
                        {
                            spItems.Add(spItem);
                        }

                    }
                }
            }
            return new JsonResult(new { result = "OK", items, attrOptions, attributes, spItems });
        }
        public override async Task<JsonResult> GetItemsInfo(string companyID, string items_id, string searchStr, bool bGetAttrs, dynamic otherInfo)
        {
            return await GetItemsInfo_static(cmd,companyID, items_id, searchStr, bGetAttrs, otherInfo);
        }
        public static async Task<JsonResult> GetItemsInfo_static(CMySbCommand cmd, string companyID, string items_id, string searchStr, bool bGetAttrs, dynamic otherInfo )
        {
            string supcust_id = otherInfo.supcust_id;
            string branch_id = otherInfo.branch_id;
            string defaultBranchPositionID = otherInfo.defaultBranchPositionID;//Ĭ��"0"
            string defaultBranchPositionType = otherInfo.defaultBranchPositionType;//û����0IDʱ����TYPE��ȡID
            
            if(defaultBranchPositionType.IsValid() && !CPubVars.IsNumeric(defaultBranchPositionType))
			{
                defaultBranchPositionType = ""; 
            }
            dynamic defaultBranchPosition = null;//����

            if (supcust_id.IsInvalid()) supcust_id = "-1";
            if (branch_id.IsInvalid()) branch_id = "-1";
            var orderBy = "";
            string condi = "";
            if (items_id != "")
            {
                condi = $" and mu.item_id in ({ items_id})";
                orderBy = $" order by position(mu.item_id::text in '{items_id}'),unit_factor desc";
            }
            else if (searchStr.IsValid())
			{
                string flexStr=CPubVars.GetFlexLikeStr(searchStr);
                condi = $" and (mu.barcode like '%{searchStr}' or ip.item_name like '%{flexStr}%' or ip.py_str like '%{searchStr}%')";
            }
            else 
                orderBy = $" order by mu.item_id desc";

            SQLQueue QQ = new SQLQueue(cmd);
            string sql = @$"
select mu.item_id,ip.item_name,ip.item_no,brand_name,mu.unit_no,other_class,ip.mum_attributes,rpd.produce_date as virtual_produce_date,(case when rp.is_recent then rp.unit_no end) recent_unit_no,mu.retail_price,rp.happen_time as recent_time, mu.weight unit_weight, s_retail_price,unit_factor,unit_type,stock.stock_qty,batch_level,s.setting->>'recentPriceTime' as recentpricetime,
        unit_from_s_to_bms ((stock.stock_qty::numeric),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) stock_qty_unit,
        unit_from_s_to_bms ((stock.sell_pend_qty::numeric),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) sell_pend_qty_unit,
        unit_from_s_to_bms ((stock.usable_stock_qty::numeric),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) usable_stock_qty_unit,
        mu.barcode,b_barcode,m_barcode,s_barcode,mu.wholesale_price, mu.cost_price_spec,mu.buy_price,mu.lowest_price,ip.cost_price_avg,ip.cost_price_recent,recent_price,recent_retail_price,coalesce(recent_orig_price,mu.wholesale_price)  recent_orig_price,item_spec,valid_days,ip.son_mum_item
from info_item_multi_unit mu
left join
(
    select item_id,(b->>'f1')::real as b_unit_factor,b->>'f2' as b_unit_no,                             b->>'f4' as b_barcode,
                   (m->>'f1')::real as m_unit_factor,m->>'f2' as m_unit_no,                             m->>'f4' as m_barcode,
                   (s->>'f1')::real as s_unit_factor,s->>'f2' as s_unit_no, s->>'f3' as s_retail_price, s->>'f4' as s_barcode
    from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,retail_price,barcode)) as json from info_item_multi_unit where company_id={companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s jsonb,m jsonb,b jsonb)
) mu1 on mu.item_id = mu1.item_id
left join company_setting s on s.company_id = mu.company_id
left join (select item_id,produce_date from item_recent_produce_date where company_id={companyID}) rpd on mu.item_id=rpd.item_id
left join (select item_id,stock_qty,sell_pend_qty, (COALESCE(stock_qty,0)-COALESCE(sell_pend_qty,0)) usable_stock_qty from stock where company_id = {companyID} and branch_id={branch_id} and branch_position = 0 and batch_id = 0) stock on stock.item_id = mu.item_id
left join (select * from client_recent_prices where company_id = {companyID} and supcust_id = {supcust_id}) rp on rp.item_id = mu.item_id and rp.unit_no = mu.unit_no
left join info_item_prop ip on mu.item_id=ip.item_id and ip.company_id={companyID}  
left join info_item_brand iib on iib.brand_id = ip.item_brand and iib.company_id={companyID}
where mu.company_id={companyID} {condi} {orderBy};";
            QQ.Enqueue("items", sql);
            sql = @$" 
                    SELECT array_to_json(array_agg(row_to_json(t))) attr_options FROM
                    (
                        SELECT opt_id, opt_name, attr.attr_id FROM info_attr_opt opt left join info_attribute attr on opt.attr_id = attr.attr_id where opt.company_id ={ companyID} and not attr.spec_opt_in_item order by opt.order_index
                    ) t"; 
            QQ.Enqueue("attr_options", sql);

            if (bGetAttrs)
            {
                sql = $"select attr_id,attr_name,distinct_stock,order_index from info_attribute where company_id={companyID} order by order_index";
                QQ.Enqueue("attrs", sql);
            }

            #region ���λ
            if (defaultBranchPositionID=="0" && defaultBranchPositionType.IsValid())
            {
                sql = @$"select branch_id,branch_position, branch_position_name from info_branch_position
where company_id = {companyID} and branch_id = {branch_id} and type_id = {defaultBranchPositionType} and COALESCE(position_status,'1')='1' ORDER BY branch_position;";
                QQ.Enqueue("branch_position", sql);
            }
            #endregion


            List<ExpandoObject> units = null;
            List<ExpandoObject> attributes = null;
            dynamic attrOptions = null;
            List<ExpandoObject> batchStock = null;
            Dictionary<string, List<Dictionary<string, string>>> batchStockTotal = new Dictionary<string, List<Dictionary<string, string>>>();
            Dictionary<string, List<Dictionary<string, string>>> batchStockForShow = new Dictionary<string, List<Dictionary<string, string>>>();
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                string tbl = QQ.Dequeue();
                if (tbl == "items")
                {
                    units = CDbDealer.GetRecordsFromDr(dr,false);
                }
                else if(tbl == "attr_options")
                {
                    dr.Read();
                    string s = CPubVars.GetTextFromDr(dr, "attr_options");
                    if (s != "")
                    {
                        attrOptions = JsonConvert.DeserializeObject(s);
                    }
                }
                else if(tbl== "attrs")
                { 
                    attributes = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if(tbl== "branch_position")
                {
                    defaultBranchPosition = CDbDealer.Get1RecordFromDr(dr, false);
                    defaultBranchPositionID = defaultBranchPosition==null?"0":defaultBranchPosition.branch_position;
                }
            }
            QQ.Clear();
         
            
            Dictionary<string, ItemInfo> items = new Dictionary<string, ItemInfo>();

            #region ��ȡ�۸����
            bool doPricePlan = false;
           // Dictionary<string, string> planIDs = new Dictionary<string, string>();
            Dictionary<string, PricePlan> dicPlans = new Dictionary<string, PricePlan>();
            if (supcust_id != "-1" && supcust_id != "0")
            {
                sql = $"select region_id,other_region,sup_rank,sup_group from info_supcust where company_id = {companyID} and supcust_id={supcust_id}";
                dynamic supInfo = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                var groupID = supInfo.sup_group == "" ? "null" : supInfo.sup_group;
                var otherRegion = supInfo.other_region;
                var rankID = supInfo.sup_rank == "" ? "null" : supInfo.sup_rank;
                /*sql = $@" select null flow_id,supcust_id,null group_id,null region_id,null rank_id,price1,price2,price3 from price_strategy_client where company_id = {companyID} and supcust_id={supcust_id}
                                union
                          select flow_id,null supcust_id,group_id,region_id,rank_id,price1,price2,price3 from price_strategy_class  where company_id={companyID} and (position(concat('/',region_id,'/') in '{otherRegion}')>0   or region_id is null) and (group_id::text = '{groupID}' or group_id is null) and (rank_id is null or rank_id::text = '{rankID}') and not (region_id is null and group_id is null and rank_id is null)  order by supcust_id,flow_id desc";
                */
                sql = $@" select null flow_id,supcust_id,null group_id,null region_id,null rank_id,price1,price2,price3 from price_strategy_client where company_id = {companyID} and supcust_id={supcust_id}
                                union
                          select flow_id,null supcust_id,group_id,region_id,rank_id,price1,price2,price3 from price_strategy_class  where company_id={companyID} and (position(concat('/',region_id,'/') in '{otherRegion}')>0   or region_id is null) and (group_id::text = '{groupID}' or group_id is null) and (rank_id is null or rank_id::text = '{rankID}') order by supcust_id,flow_id desc";
                sql = $@"
select a.*,m1.plan_name plan1_name,m2.plan_name plan2_name,m3.plan_name plan3_name from ({sql}) 
a
left join price_plan_main m1 on isnumeric(a.price1) and yj_parse_int(a.price1)=m1.plan_id and m1.company_id={companyID}
left join price_plan_main m2 on isnumeric(a.price2) and yj_parse_int(a.price2)=m2.plan_id and m2.company_id={companyID}
left join price_plan_main m3 on isnumeric(a.price3) and yj_parse_int(a.price3)=m3.plan_id and m3.company_id={companyID}
 ";

                List<ExpandoObject> supPlans = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                if (supPlans.Count > 0)
                {
                    foreach (dynamic plan in supPlans)
                    {
                        if (plan.supcust_id != "") doPricePlan = true;
                        else if (plan.supcust_id == "" && plan.flow_id != "") doPricePlan = true;

                        if (doPricePlan)
                        {
                            //if (plan.price1 != "") planIDs.Add("1", plan.price1);
                            //if (plan.price2 != "") planIDs.Add("2", plan.price2);
                            //if (plan.price3 != "") planIDs.Add("3", plan.price3);
                            if (plan.price1 != "") dicPlans.Add("1", new PricePlan { plan_id = plan.price1, plan_name = plan.plan1_name });
                            if (plan.price2 != "") dicPlans.Add("2", new PricePlan { plan_id = plan.price2, plan_name = plan.plan2_name });
                            if (plan.price3 != "") dicPlans.Add("3", new PricePlan { plan_id = plan.price3, plan_name = plan.plan3_name });

                            break;
                        }
                    }
                }
                else
                {
                    doPricePlan = true;
                    dicPlans.Add("1", new PricePlan { plan_id = "recent", plan_name = "" });
                    dicPlans.Add("2", new PricePlan { plan_id = "wholesale", plan_name = "" });
                }
            }
            #endregion
            #region ʹ�ü۸����
            //Dictionary<string, dynamic> prices = new Dictionary<string, dynamic>();
            Dictionary<string, PriorityItem> prices = new Dictionary<string, PriorityItem>();
            
            if (doPricePlan && dicPlans.Count > 0)
            {
                string priceSql = "";
                var fld = ""; var orderCondi = "";
                foreach (var p in dicPlans)
                {
                    var plan = p.Value;
                    if (priceSql != "") priceSql += " union ";
                    if (p.Key != null && p.Key != "")
                    {
                        fld = $"{ p.Key} as priority,'{plan.plan_id}' plan_id,";
                        orderCondi = $" order by priority,price_item ";
                    }
                    // ���Ҳ�ͬ�����ļ۸�
                    priceSql += @$"
select p.item_id,pi.item_id price_item,{fld}pi.class_id,s_price,m_price,b_price,class_discount
from info_item_prop p 
left join 
(
    select null item_id,null s_price,null m_price,null b_price,     class_id,discount class_discount from price_plan_class where company_id = {companyID} and plan_id::text = '{plan.plan_id}' and class_id is not null
        union
    select      item_id,     s_price,     m_price,     b_price,null class_id,null     class_discount from price_plan_item  where company_id = {companyID} and plan_id::text = '{plan.plan_id}' and item_id in ({items_id})
) pi on (pi.item_id = p.item_id or pi.item_id is null) and (position(concat('/',pi.class_id,'/') in other_class)>0 or pi.class_id is null)
where p.company_id = {companyID} and p.item_id in ({items_id}) ";

                }

                priceSql += orderCondi;
                List<ExpandoObject> plans = await CDbDealer.GetRecordsFromSQLAsync(priceSql, cmd);
                #region �޸Ľ����  { priority_item_id, { priority, plan_id, item_id, prSetting{price_item,class_id.....}}} ���õ�prices

                foreach (dynamic plan in plans)
                {
                    PriorityItem pi = null;
                    if (prices.ContainsKey(plan.priority+'_'+ plan.item_id))
                    {
                        pi = prices[plan.priority + '_' + plan.item_id];
                    }
                    else
                    {
                        pi = new PriorityItem();
                        pi.priority = plan.priority;
                        pi.plan_id = plan.plan_id;
                        pi.item_id = plan.item_id;
                        prices.Add((string)plan.priority + '_' + plan.item_id, pi);
                        pi.PriceList = new List<PriceSet>();
                    }

                    if (plan.price_item != "" || plan.class_id != "")
                    {
                        pi.PriceList.Add
                        (
                           new PriceSet
                           {
                               item_id = plan.price_item,
                               class_id = plan.class_id,
                               class_discount = plan.class_discount,
                               s_price = plan.s_price,
                               m_price = plan.m_price,
                               b_price = plan.b_price
                           }
                       );
                    }
                       
                }
                #endregion
            }
            #endregion

            #region ���۵���Ӱ�� --- �ҳ���Ҫʹ�õ��۵ķ�������Ʒ
            dynamic adjustItems = null;
            string adjustPlan = "";
            
            if (dicPlans.Count > 1 && dicPlans["1"].plan_id == "recent") adjustPlan = dicPlans["2"].plan_id; // ������ѡ����Ϊ����ۼ۵ļ۸����  
            // ��ѡ�޼۸�����һ����Ϊ����ۼ� ���磺  ������ - ����ۼ� - ����һ
            if (dicPlans.Count > 2 && dicPlans["1"].plan_id != "recent" && dicPlans["2"].plan_id == "recent" && dicPlans["3"].plan_id != "recent") adjustPlan = dicPlans["3"].plan_id;
            //if (planIDs.Count ==0) adjustPlan = "wholesale";
            if (adjustPlan!="")
            {
                if (adjustPlan == "wholesale") adjustPlan = "-1";
                if (adjustPlan == "retail") adjustPlan = "0";
                if (adjustPlan == "recent") adjustPlan = "-2";

                //��Ҫ�Ƚ� client_recent_prices ���� happen_time �� item_price_adjust �� adjust_time ��ʱ������
                string selAdjustSql = @$"select string_agg(a.item_id::text,',') items_id from item_price_adjust a left join client_recent_prices r on r.item_id = a.item_id
                                         where a.company_id = {companyID} and r.supcust_id = {supcust_id} and a.plan_id = '{adjustPlan}' and a.adjust_time>r.happen_time and a.item_id in ({items_id}); ";
                adjustItems = await CDbDealer.Get1RecordFromSQLAsync(selAdjustSql, cmd); // �ҳ���Ҫʹ�� �¼۸�� ��Ʒ
            }
            #endregion


            foreach (dynamic unit in units)
            {
                if (unit.unit_factor == "") continue;
                ItemInfo item = null;
                if (items.ContainsKey(unit.item_id))
                {
                    item = items[unit.item_id];
                }
                else
                {
                    item = new ItemInfo();
                    item.item_id = unit.item_id;
                    item.item_name = unit.item_name;
                    item.son_mum_item = unit.son_mum_item;
                    item.item_no = unit.item_no;
                    item.item_spec = unit.item_spec;
                    item.s_barcode = unit.s_barcode;
                    item.m_barcode = unit.m_barcode;
                    item.b_barcode = unit.b_barcode;

					item.valid_days = unit.valid_days;
                    //item.produce_date = unit.produce_date;
                     
                    
                    item.virtual_produce_date = unit.virtual_produce_date;
                    item.stock_qty = unit.stock_qty.Replace("-0", "0");
                    item.stock_qty_unit = (unit.stock_qty_unit).Replace("-0", "0");
                    item.sell_pend_qty_unit = (unit.sell_pend_qty_unit).Replace("-0", "0");
                    item.batch_level = unit.batch_level;
                    item.usable_stock_qty_unit = (unit.usable_stock_qty_unit).Replace("-0", "0");
                    item.recent_unit_no = unit.recent_unit_no;
                    //item.oldest_stock_qty = unit.oldest_stock_qty.Replace("-0", "0");
                    //item.oldest_stock_qty_unit = (unit.oldest_stock_qty_unit).Replace("-0", "0");
                    //item.oldest_usable_stock_qty_unit = (unit.oldest_usable_stock_qty_unit).Replace("-0", "0");
                    //item.oldest_sell_pend_qty_unit = (unit.oldest_sell_pend_qty_unit).Replace("-0", "0");
                    //item.produce_date = unit.produce_date;
                    //item.batch_no = unit.batch_no;

                    item.mum_attributes = unit.mum_attributes; 
                    items.Add((string)item.item_id, item);
                    item.units = new List<ItemUnit>();
                    item.brand_name = unit.brand_name;
                }
                float cost_price_recent1;
                float cost_price_recent2;
                float cost_price_recent3;
                dynamic pStr = JsonConvert.DeserializeObject(unit.cost_price_recent);
                //Console.WriteLine("���ת�����ֵ��{0}" + "\n" + "ת��������ͣ�{1} " + "\n", pStr, pStr.GetType());
                if (pStr != null)
                {
                    if (pStr.avg1 != null)
                    {
                        cost_price_recent1 = pStr.avg1;
                    }
                    else
                    {
                        cost_price_recent1 = 0;
                    }
                    if (pStr.avg2 != null)
                    {
                        cost_price_recent2 = pStr.avg2;
                    }
                    else
                    {
                        cost_price_recent2 = 0;
                    }
                    if (pStr.avg3 != null)
                    {
                        cost_price_recent3 = pStr.avg3;
                    }
                    else
                    {
                        cost_price_recent3 = 0;
                    }
                }
                else
                {
                    cost_price_recent1 = 0;
                    cost_price_recent2 = 0;
                    cost_price_recent3 = 0;
                }
                string recent_price_time;
                recent_price_time = unit.recentpricetime;
                if (recent_price_time == "1")
                {
                    unit.cost_price_recent = CPubVars.ToDecimal(cost_price_recent1);
                }
                else if (recent_price_time == "2")
                {
                    unit.cost_price_recent = CPubVars.ToDecimal(cost_price_recent2);
                }
                else if (recent_price_time == "3")
                {
                    unit.cost_price_recent = CPubVars.ToDecimal(cost_price_recent3);
                }
                unit.cost_price_recent = unit.cost_price_recent.ToString();
                
                #region ʹ�ü۸����
                string planPrice = "";
                foreach (var kp in prices)
                {
                    PriorityItem pi = kp.Value;
                    if (unit.item_id == pi.item_id)
                    {
                        //var plan = pi.plan_id;
                        if (pi.plan_id == "")
                            continue;
                        else if (pi.plan_id == "wholesale") {
                            planPrice = unit.wholesale_price;
                            if (planPrice != "") break;
                        }
                        else if (pi.plan_id == "recent") {
                            // ����ۼ� - ������
                            if (adjustItems !=null && adjustItems.items_id.Contains(unit.item_id)) // �����Ҫ�ļۣ���ȡ��һ�εķ����۸�
                                continue; 
                            planPrice = unit.recent_price;
                            // unit.recent_time;
                            if (planPrice != "") break; 
                        }
                        else if (pi.plan_id == "retail") { 
                            planPrice = unit.retail_price; 
                            if (planPrice != "") break;
                        }
                        var otherClass = unit.other_class;
                        var selectClass = ""; var selectDisc = ""; var classIndex = -1;
                        foreach (var priceSet in pi.PriceList)
                        { 
                            if (priceSet.item_id != "")
                            {
                                if (unit.unit_type == "s") planPrice = priceSet.s_price;
                                if (unit.unit_type == "m") planPrice = priceSet.m_price;
                                if (unit.unit_type == "b") planPrice = priceSet.b_price;
                                if (planPrice != "") break;
                            }
                            else if (priceSet.class_id != "")
                            {
                                var classArr = ((string)otherClass).Split( '/');

                                if (Array.IndexOf(classArr, priceSet.class_id) > classIndex)
                                {
                                    classIndex = Array.IndexOf(classArr, priceSet.class_id);
                                    selectClass = priceSet.class_id;
                                    selectDisc = priceSet.class_discount;
                                }
                            }
                            if (planPrice != "") break;
                        }

                        if (selectDisc != "" && unit.wholesale_price != "") planPrice = Convert.ToString(Convert.ToSingle(selectDisc) * Convert.ToSingle(unit.wholesale_price));
                        if (planPrice != "") break;
                    }
                }
                //��ȡ���з�����,�Ա��ڵ���۸�Ԫ��ʱ�������г�
                Dictionary<string, string> dicPlanPrices = null;
                if (dicPlans.Count > 0)
                {
                    foreach (var kp in prices)
                    {
                        PriorityItem pi = kp.Value;

                        if (unit.item_id == pi.item_id)
                        {
                            if (pi.plan_id != "wholesale" && pi.plan_id != "recent" && pi.plan_id != "retail")
                            {
                                var otherClass = unit.other_class;
                                var selectClass = ""; var selectDisc = ""; var classIndex = -1;
                                string showPrice = "";
                                foreach (PriceSet s in pi.PriceList)
                                {
                                    if (s.item_id != "")
                                    {
                                        if (unit.unit_type == "s") showPrice = s.s_price;
                                        if (unit.unit_type == "m") showPrice = s.m_price;
                                        if (unit.unit_type == "b") showPrice = s.b_price;

                                    }
                                    else if (s.class_id != "")
                                    {
                                        var classArr = otherClass.Split( '/');
                                        if (Array.IndexOf(classArr, s.class_id) > classIndex)
                                        {
                                            classIndex = Array.IndexOf(classArr, s.class_id);
                                            selectClass = s.class_id;
                                            selectDisc = s.class_discount;
                                        }
                                    }
                                    if (showPrice != "") break;
                                } 
                                if (selectDisc != "" && unit.wholesale_price != "") showPrice = Convert.ToString(Convert.ToSingle(selectDisc) * Convert.ToSingle(unit.wholesale_price));
                                var pkp = dicPlans.First(p => p.Value.plan_id == pi.plan_id);
                                if (pkp.Value != null)
                                {
                                    if (dicPlanPrices == null) dicPlanPrices = new Dictionary<string, string>();
                                    if (!dicPlanPrices.ContainsKey(pkp.Value.plan_name))
                                    {
                                        dicPlanPrices.Add(pkp.Value.plan_name, showPrice);
                                    }
                                }
                            }
                        }
                    }
                }
             

                #endregion
                item.s_retail_price = unit.s_retail_price;
               // List<dynamic> itemUnits = item.units;
                string price = "";
				if (doPricePlan)
				{
                    if (planPrice != "") price = planPrice;
                } 
                else if(unit.recent_price!="") price = unit.recent_price;
                else if(unit.wholesale_price!="") price = unit.wholesale_price;
                string cost_price_buy = "";
                if (unit.buy_price != "") cost_price_buy =CPubVars.FormatMoney(CPubVars.ToDecimal(unit.buy_price)/CPubVars.ToDecimal(unit.unit_factor),4);

                item.units.Add(new ItemUnit{ unit_no=unit.unit_no,planPrices=dicPlanPrices, unit_factor=unit.unit_factor, unit_type= unit.unit_type, price=price, recent_orig_price= unit.recent_orig_price, recent_price= unit.recent_price, recent_retail_price = unit.recent_retail_price, recent_time = CPubVars.GetDateTextNoTime(unit.recent_time), wholesale_price=unit.wholesale_price, retail_price= unit.retail_price, barcode= unit.barcode, buy_price= unit.buy_price, lowest_price=unit.lowest_price, cost_price_avg= unit.cost_price_avg,cost_price_spec=unit.cost_price_spec, cost_price_buy=cost_price_buy, cost_price_recent =unit.cost_price_recent,unit_weight= unit.unit_weight });
            }
            List<dynamic> spItems = new List<dynamic>();
            if (supcust_id != "" && supcust_id != "-1" && supcust_id != "0" && items.Count > 0)
            {
                string specialPriceSql = @$"SELECT * FROM (SELECT sm.supcust_id,sm.start_time,sm.end_time,(sm.end_time::date-sm.start_time::date)+1 as special_days ,(sm.end_time::date-now()::date)+1 left_days,sd.item_id,sd.unit_no,sd.unit_factor,mu.unit_type,sd.special_price,sd.happen_time,ip.son_mum_item,row_number() over(partition by sd.item_id order by sd.happen_time desc ) rn FROM sheet_special_price_detail sd 
LEFT JOIN sheet_special_price_main sm on sd.company_id = sm.company_id and sd.sheet_id= sm.sheet_id 
LEFT JOIN info_item_multi_unit mu on mu.company_id = sd.company_id and mu.item_id = sd.item_id and mu.unit_factor = sd.unit_factor 
LEFT JOIN info_item_prop ip on ip.company_id = sd.company_id and ip.item_id = sd.item_id
WHERE sd.company_id = {companyID} and sm.approve_time is not null and sm.red_flag is null and sm.end_time::date >= NOW()::date and sm.supcust_id = {supcust_id})t
WHERE t.rn = 1 ";
                List<ExpandoObject> specialPriceItems = await CDbDealer.GetRecordsFromSQLAsync(specialPriceSql, cmd);
                if (specialPriceItems.Count > 0)
                {
                    foreach (dynamic spItem in specialPriceItems)
                    {
                        if (items.ContainsKey(spItem.item_id))
                        {
                            spItems.Add(spItem);
                            //items[spItem.item_id].haveSpecialPrice = true;
                            //foreach (dynamic unit in items[spItem.item_id].units)
                            //{
                            //    unit.isSpeicalPrice = true;
                            //    if (unit.unit_type == spItem.unit_type)
                            //    {
                            //        unit.price = spItem.special_price;
                            //    }
                            //    else
                            //    {
                            //        unit.price = CPubVars.ToDecimal(spItem.special_price) / CPubVars.ToDecimal(spItem.unit_factor) * CPubVars.ToDecimal(unit.unit_factor);
                            //    }
                            //    unit.special_price = unit.price;
                            //}
                        }

                    }
                }
            }

            bool needQueryBatchStock = false;
            foreach(dynamic unit in units)
            {
                if(unit.batch_level!="")
                {
                    needQueryBatchStock = true;
                    break;
                }
            }

            if (needQueryBatchStock || (defaultBranchPositionID.IsValid() && defaultBranchPositionID != "0"))
            {
                string batchCondi = "";
                 
                batchCondi = $" and s.branch_position = {defaultBranchPositionID}";
                 
                if (items_id != "")
                {
                    batchCondi += $@" and s.item_id in ({items_id})";
                }
                string batchSql = $@"select COALESCE(s.batch_id,0)::int as batch_id,COALESCE(s.batch_id,0)::int as batch_id_f,COALESCE(batch_no,'') as batch_no,COALESCE(SUBSTRING(produce_date::text,1,10),'') as produce_date,COALESCE(stock_qty,0) as stock_qty,s.branch_id,s.branch_position,COALESCE(branch_position_name,'') as branch_position_name,
unit_from_s_to_bms (COALESCE(stock_qty,0)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) as stock_qty_unit,
unit_from_s_to_bms (COALESCE(sell_pend_qty,0)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) as sell_pend_qty_unit,
unit_from_s_to_bms (COALESCE(stock_qty,0)::numeric-COALESCE(sell_pend_qty,0)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) as usable_stock_qty_unit,s.item_id
                    from stock s 
                    left join info_branch_position ibp on ibp.company_id = {companyID} and ibp.branch_id = s.branch_id and ibp.branch_position = s.branch_position
                    left join info_item_batch itb on itb.batch_id = s.batch_id and itb.company_id = s.company_id
                    left join (select item_id,(b->>'f1')::real as b_unit_factor,(m->>'f1')::real as m_unit_factor,(s->>'f1')::real as s_unit_factor,
                                                    b->>'f2' as b_unit_no,m->>'f2' as m_unit_no,s->>'f2' as s_unit_no,s->>'f3' as s_buy_price
                                    from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,buy_price)) as json from info_item_multi_unit where company_id={companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
                                        as errr(item_id int, s jsonb,m jsonb,b jsonb)) mu1 on s.item_id = mu1.item_id
                    where s.company_id = {companyID} and s.branch_id = {branch_id} {batchCondi} and COALESCE(stock_qty,0)<> 0 order by produce_date,batch_no limit 200";
                batchStock = await CDbDealer.GetRecordsFromSQLAsync(batchSql, cmd);
                bool isShowNegativeStock = otherInfo.isShowNegativeStock;
                if (batchStock.Count > 0)
                {
                    List<Dictionary<string, string>> newData = JsonConvert.DeserializeObject<List<Dictionary<string, string>>>(JsonConvert.SerializeObject(batchStock));
                    foreach(Dictionary<string, string> v in newData)
                    {
                        // string key = v["item_id"].ToString() + v["branch_id"].ToString() +v["branch_position"].ToString();
                        string key = v["item_id"].ToString() + "_" + v["branch_id"].ToString() + "_" + v["branch_position"].ToString();
                        decimal stockQty = CPubVars.ToDecimal(v["stock_qty"]);
                        if (batchStockTotal.ContainsKey(key)) batchStockTotal[key].Add(v);
                        else
                        {
                            batchStockTotal[key] = new List<Dictionary<string, string>>();
                            batchStockTotal[key].Add(v);
                        }
                        if (!isShowNegativeStock && stockQty>0)
                        {
                            if (batchStockForShow.ContainsKey(key)) batchStockForShow[key].Add(v);
                            else
                            {
                                batchStockForShow[key] = new List<Dictionary<string, string>>();
                                batchStockForShow[key].Add(v);
                            }
                        }
                    }
                    if (isShowNegativeStock) batchStockForShow = batchStockTotal;
                }
                
            }
            return new JsonResult(new { result = "OK", items, attrOptions, attributes, spItems, defaultBranchPosition, batchStockTotal,batchStockForShow });

        }

        public async Task<JsonResult> SetStockOnbranchUpdate(string companyID, string item_id, string branch_id,string branch_position,string produce_date,string batch_no)
        {
            if (item_id.IsInvalid()) item_id = "-1";
            if (branch_id.IsInvalid()) branch_id = "-1";
            if (branch_position.IsInvalid()) branch_position = "0";
                if (produce_date.IsInvalid()) produce_date = "";
                if (batch_no.IsInvalid()) batch_no = "";
            string sql = $@"
select stock_qty,
        unit_from_s_to_bms ((stock_qty::numeric),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) stock_qty_unit,
        unit_from_s_to_bms ((sell_pend_qty::numeric),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) sell_pend_qty_unit,
        unit_from_s_to_bms ((usable_stock_qty::numeric),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) usable_stock_qty_unit
from info_item_multi_unit mu
left join 
(
    select item_id,(b->>'f1')::real as b_unit_factor,b->>'f2' as b_unit_no,
                   (m->>'f1')::real as m_unit_factor,m->>'f2' as m_unit_no,
                   (s->>'f1')::real as s_unit_factor,s->>'f2' as s_unit_no, s->>'f3' as s_retail_price 
    from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,retail_price)) as json from info_item_multi_unit where company_id={companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s jsonb,m jsonb,b jsonb)
) mu1 on mu.item_id = mu1.item_id
left join 
(
    select stock_qty,sell_pend_qty,(COALESCE(stock_qty::numeric,0)- COALESCE(sell_pend_qty::numeric,0)) AS usable_stock_qty
    from stock s
    left join info_item_batch itb on itb.company_id = {companyID} and itb.batch_id = s.batch_id
    where company_id = {companyID} and item_id = {item_id} and branch_id = {branch_id} and branch_position = {branch_position} and produce_date = '{produce_date}' and batch_no = '{batch_no}'
)
where company_id = {companyID} and mu.item_id = {item_id} 
";
            List<ExpandoObject> stockInfo = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            return new JsonResult(new { result = "OK", data = stockInfo });

        }

        public async Task<JsonResult> GetBranchPosition(CMySbCommand cmd, string companyID,string branch_id,string branchPositionType)
        {
            branch_id = branch_id.IsInvalid() ? "-1" : branch_id;
            string sql = "";
            dynamic data = null;
            sql = @$"select branch_id,branch_position, branch_position_name from info_branch_position
where company_id = {companyID} and branch_id = {branch_id} and type_id = {branchPositionType} and COALESCE(position_status,'1') = '1' ORDER BY branch_position;";
            data = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            
            return new JsonResult(new { result = "OK", data });
        }
        public async Task<JsonResult> getBatchStock(CMySbCommand cmd, string companyID,string branch_id,string branch_position,string items_id,bool isShowNegativeStock)
        {
            branch_id = branch_id.IsInvalid() ? "-1" : branch_id;
            branch_position = branch_position.IsInvalid() ? "0" : branch_position;
            string batchSql = $@"select COALESCE(s.batch_id,0)::int as batch_id,COALESCE(s.batch_id,0)::int as batch_id_f,COALESCE(batch_no,'') as batch_no,COALESCE(SUBSTRING(produce_date::text,1,10),'') as produce_date,COALESCE(stock_qty,0) as stock_qty,s.branch_id,s.branch_position,COALESCE(branch_position_name,'') as branch_position_name,
unit_from_s_to_bms (COALESCE(stock_qty,0)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) as stock_qty_unit,
unit_from_s_to_bms (COALESCE(sell_pend_qty,0)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) as sell_pend_qty_unit,
unit_from_s_to_bms (COALESCE(stock_qty,0)::numeric-COALESCE(sell_pend_qty,0)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) as usable_stock_qty_unit,s.item_id
                    from stock s 
                    left join info_branch_position ibp on ibp.company_id = {companyID} and ibp.branch_id = s.branch_id and ibp.branch_position = s.branch_position
                    left join info_item_batch itb on itb.batch_id = s.batch_id and itb.company_id = s.company_id
                    left join (select item_id,(b->>'f1')::real as b_unit_factor,(m->>'f1')::real as m_unit_factor,(s->>'f1')::real as s_unit_factor,
                                                    b->>'f2' as b_unit_no,m->>'f2' as m_unit_no,s->>'f2' as s_unit_no,s->>'f3' as s_buy_price
                                    from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,buy_price)) as json from info_item_multi_unit where company_id={companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
                                        as errr(item_id int, s jsonb,m jsonb,b jsonb)) mu1 on s.item_id = mu1.item_id
                    where s.company_id = {companyID} and s.branch_id in ({branch_id}) and s.branch_position in ({branch_position}) and s.item_id in ({items_id}) and stock_qty <> 0 order by produce_date,batch_no limit 200";
            List<ExpandoObject> batchStock = null;
            Dictionary<string, List<Dictionary<string, string>>> batchStockTotal = new Dictionary<string, List<Dictionary<string, string>>>();
            Dictionary<string, List<Dictionary<string, string>>> batchStockForShow = new Dictionary<string, List<Dictionary<string, string>>>();
            batchStock = await CDbDealer.GetRecordsFromSQLAsync(batchSql, cmd);
            if (batchStock.Count > 0)
            {
                List<Dictionary<string, string>> newData = JsonConvert.DeserializeObject<List<Dictionary<string, string>>>(JsonConvert.SerializeObject(batchStock));
                foreach(Dictionary<string, string> v in newData)
                {
                    string key = v["item_id"].ToString()+"_"+v["branch_id"].ToString() + "_" + v["branch_position"].ToString();
                    decimal stockQty = CPubVars.ToDecimal(v["stock_qty"]);
                    if (batchStockTotal.ContainsKey(key)) batchStockTotal[key].Add(v);
                    else
                    {
                        batchStockTotal[key] = new List<Dictionary<string, string>>();
                        batchStockTotal[key].Add(v);
                    }
                    if (!isShowNegativeStock && stockQty>0)
                    {
                        if (batchStockForShow.ContainsKey(key)) batchStockForShow[key].Add(v);
                        else
                        {
                            batchStockForShow[key] = new List<Dictionary<string, string>>();
                            batchStockForShow[key].Add(v);
                        }
                    }
                }
                if (isShowNegativeStock) batchStockForShow = batchStockTotal;
            }
            
            return new JsonResult(new { result = "OK", batchStockTotal,batchStockForShow });
        }
    }
    [ApiController]
    [Route("api/[controller]/[action]")]
    public class BorrowItemSheetController : BaseController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        public BorrowItemSheetController(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }
        [HttpPost]
        public async Task<Dictionary<string, string>> GetSubNameByKs([FromBody] dynamic postData)
        {
            Dictionary<string, string> subList = new Dictionary<string, string>();
            string operKey = postData.operKey;
            int subId = postData.subId;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string sql = $" select sub_name from cw_subject where company_id={companyID} and sub_id={subId}";
            dynamic rec = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            subList.Add("sub_id", subId.ToString());
            subList.Add("sub_name", rec.sub_name);
            return subList;
        }

        /*[HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            var model = new SaleSheetModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }*/
        [HttpGet]
        public async Task<JsonResult> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues, string condiDataItem)
        {
            var model = new BorrowItemSheetModel(cmd);
            return await PageBaseModel.GetDataItemOptionsJson(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues, condiDataItem);

        }
        [HttpGet]
        public async Task<string> GetSheetRemarks(string operKey, string sheetType)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string sql = $"select brief_text as remark from info_sheet_detail_brief where company_id={companyID} and sheet_type='{sheetType}'";

            List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string s = JsonConvert.SerializeObject(data);
            return s;
        }

        [HttpPost]
        public async Task<JsonResult> LoginPiaoZhengTong([FromBody] dynamic data)
        {
            //����,��ο�"����׼��"��ȡ��������,�滻Ϊʵ��ֵ 
            string apiAddress = $@"https://www.ahspy.cn/infozr_api_common/login.htm?username=" + data.ticketAccessSysAccount + @$"&password={data.ticketAccessSysPwd}&sysToken={data.ticketAccessSysKey}&appType=9&appVersion=0";


            //try
            //{
            //Ϊ��ֹ��HTTPS֤����֤ʧ�����API����ʧ��,��Ҫ�Ⱥ���֤����������
            HttpClient client = CPubVars.GetHttpClientFromFactory(_httpClientFactory);
            // HttpClient client = CPubVars.GetHttpClient();


            var body = new Dictionary<string, string>()
            {


            };

            HttpContent content = new FormUrlEncodedContent(body);

            var response = await client.PostAsync(apiAddress, content);
            Console.WriteLine(response.StatusCode); //��ӡ��Ӧ�����
            var res = await response.Content.ReadAsStringAsync();

            data = JsonConvert.DeserializeObject(res);
            if (data.status != 0)
            {
                return new JsonResult(new { data });
            }
            string accessTicketToken = data.result.token;
            if (CommonTool.IsValid(accessTicketToken))
            {
                Response.Cookies.Append("accessTicketToken", accessTicketToken);
            }
            return new JsonResult(new { data });

            //}
            //catch (Exception e)
            // {

            //}

        }

        [HttpPost]
        public async Task<IActionResult> UploadSheetToPiaoZhengTong([FromBody] dynamic data)
        {
            TicketAccessService ticketAccessService = new TicketAccessService();
            dynamic resp = await ticketAccessService.UploadSheet(_httpClientFactory, data);
            return new JsonResult(new { resp });
        }

        [HttpGet]
        public async Task<IActionResult> GetItems(string operKey, string sheetType, bool canSeePrice, string query, string supcust_id, string branch_id, string showSonItems)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            if (string.IsNullOrEmpty(branch_id))
            {
                branch_id = "0";
            }
            if (string.IsNullOrEmpty(supcust_id))
            {
                supcust_id = "0";
            }
            string priceFields = "";
            string priceLeftJoin = "";
            if (canSeePrice)
            {
                string recent_price_tb = "client_recent_prices";
                if (sheetType == "CG" || sheetType == "CT")
                {
                    recent_price_tb = "supplier_recent_prices";
                }
                priceLeftJoin = $@"left join {recent_price_tb}  cp on  cp.company_id={companyID} and  cp.supcust_id={supcust_id} and i.item_id =  cp.item_id and u.s_unit_no= cp.unit_no
left join {recent_price_tb} cpb on cpb.company_id={companyID} and cpb.supcust_id={supcust_id} and i.item_id = cpb.item_id and u.b_unit_no=cpb.unit_no
";
                priceFields = @" case when  cp.recent_price is not null then  cp.recent_price::text else u.s_wholesale_price end as s_price,
       case when cpb.recent_price is not null then cpb.recent_price::text else u.b_wholesale_price end as b_price,";
                if (sheetType == "CG" || sheetType == "CT")
                {
                    priceFields = priceFields.Replace("_wholesale_price", "_buy_price");
                }
            }
            query = query.Replace("'", "");
            string son_items_condi = "";
            if (showSonItems == "1")
            {
                son_items_condi = " and (mum_attributes is null or mum_attributes::text not like '%\"distinctStock\": true%')";
            }
            else
            {
                son_items_condi = " and son_mum_item is null";
            }
            string attrCondi = "";
            if (query.Length >= 3)
            {
                attrCondi = $" or mum_attributes::text like '%{query}%'";
            }
            string barcodeRight = "";
            if (query.Length >= 6)            
                barcodeRight = "";

            string brcodeCondi = "";
            if (query.Length >= 3 && CPubVars.IsNumeric(query))
            {
                brcodeCondi = $" or u.s_barcode like '%{query}{barcodeRight}' or u.b_barcode like '%{query}{barcodeRight}' or u.m_barcode like '%{query}{barcodeRight}' or i.mum_attributes::text ilike '%{query}%'";

            }
            string flexStr = CPubVars.GetFlexLikeStr(query);

            string py_flexStr = CPubVars.GetPyStrFlexLikeStr(query);
            //if (query.Length >= 3) py_flexStr = flexStr;

            string sql = @$" 
select item_name as name,i.item_id as id,py_str as zjm,item_spec,mum_attributes,
      {priceFields}
       yj_get_bms_qty(stock_qty, b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no) as stock, concat(s_barcode,' ',b_barcode,' ',m_barcode) as code,item_no 
from info_item_prop i
left join (select item_id,branch_id,sum(stock_qty) as stock_qty from stock where company_id = {companyID} group by item_id,branch_id)s on i.item_id=s.item_id and branch_id={branch_id}
left join 
(
    select item_id,
           s->>'f1' as s_unit_no,s->>'f3' as s_barcode, s->>'f4' as s_wholesale_price,s->>'f4' as s_buy_price,  
           m->>'f1' as m_unit_no,m->>'f3' as m_barcode, m->>'f4' as m_wholesale_price,m->>'f4' as m_buy_price,  (m->>'f2')::numeric as m_unit_factor,
           b->>'f1' as b_unit_no,b->>'f3' as b_barcode, b->>'f4' as b_wholesale_price,b->>'f4' as b_buy_price,  (b->>'f2')::numeric as b_unit_factor
 
    from crosstab('
         select item_id,unit_type,row_to_json(row(unit_no,unit_factor,barcode,wholesale_price)) as json 
         from info_item_multi_unit where company_id ={companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$)  as errr(item_id int, s jsonb,m jsonb, b jsonb)
) u 
on i.item_id=u.item_id
{priceLeftJoin}
where i.company_id={companyID} and (item_name ilike '%{flexStr}%' or py_str ilike '%{py_flexStr}%'  or item_no ilike '%{query}%' or item_no ilike '%{query.ToLower()}%' {brcodeCondi} {attrCondi}) and COALESCE(i.status,'1')='1' {son_items_condi} order by case when stock_qty>0 then 0 else 1 end,item_name limit 100 ";

            dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            return new JsonResult(new { result = "OK", records });
        }


        [HttpGet]
        public async Task<IActionResult> GetItemInfo(string operKey, string item_id, string supcust_id)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            // var model = new SaleSheetModel();
            if (supcust_id == null) supcust_id = "-1";
            string sql = @$"select mu.unit_no,unit_factor,mu.barcode,recent_price as price,item_spec,valid_days from info_item_multi_unit mu
                                left join (select * from client_recent_prices where company_id = {companyID} and supcust_id = {supcust_id}) rp on rp.item_id = mu.item_id and rp.unit_no = mu.unit_no
                            left join info_item_prop ip on mu.item_id=ip.item_id where mu.company_id={companyID} and mu.item_id in ({item_id});";

            dynamic units = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            //string items=Newtonsoft.Json.JsonConvert.SerializeObject(rec); 

            return new JsonResult(new { result = "OK", item = new { item_id, units } });
        }
        [HttpGet]
        public async Task<IActionResult> GetItemsInfo(string operKey, string items_id, string searchStr, bool bGetAttrs, string supcust_id, string branch_id, string defaultBranchPositionType, string defaultBranchPositionID, bool isShowNegativeStock)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            BorrowItemSheetModel sheetModel = new BorrowItemSheetModel(cmd);
            //if (branch_position.IsInvalid()) branch_position = "0";
            var rr = await sheetModel.GetItemsInfo(companyID, items_id, searchStr, bGetAttrs, new { supcust_id, branch_id, defaultBranchPositionType, defaultBranchPositionID, isShowNegativeStock });
            return rr;
        }

        [HttpPost]
		public async Task<IActionResult> GetItemsInfo_Post([FromBody] dynamic data)
		{
			string operKey = data.operKey;
			string items_id = data.items_id;
			string searchStr = data.searchStr;
			bool bGetAttrs = data.bGetAttrs;
			string supcust_id = data.supcust_id;
			string branch_id = data.branch_id;
			string defaultBranchPositionType = data.defaultBranchPositionType;
			string defaultBranchPositionID = data.defaultBranchPositionID;
			bool isShowNegativeStock = data.isShowNegativeStock;

			Security.GetInfoFromOperKey(operKey, out string companyID);

			BorrowItemSheetModel sheetModel = new BorrowItemSheetModel(cmd);
			var rr = await sheetModel.GetItemsInfo(companyID, items_id, searchStr, bGetAttrs, new { supcust_id, branch_id, defaultBranchPositionType, defaultBranchPositionID, isShowNegativeStock });
			return rr;
		}

        [HttpGet]
        public async Task<IActionResult> SetStockOnbranchUpdate(string operKey, string item_id, string branch_id, string branch_position, string batch_id, string produce_date, string batch_no)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            if (item_id.IsInvalid()) item_id = "-1";
            if (branch_id.IsInvalid()) branch_id = "-1";
            if (branch_position.IsInvalid()) branch_position = "0";
            string batchSql = "";
            if (batch_id == "0")
            {
                batchSql = $@" and s.batch_id = 0";
            }
            else
            {
                if (batch_no.IsInvalid()) batch_no = "";
                batchSql = $@" and produce_date = '{produce_date}' and batch_no = '{batch_no}'";
            }

            string sql = $@"
select stock_qty,
        unit_from_s_to_bms ((stock_qty::numeric),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) stock_qty_unit,
        unit_from_s_to_bms ((sell_pend_qty::numeric),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) sell_pend_qty_unit,
        unit_from_s_to_bms ((COALESCE(stock_qty::numeric,0)- COALESCE(sell_pend_qty::numeric,0)),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) usable_stock_qty_unit
from stock s
left join info_item_batch itb on itb.company_id = {companyID} and itb.batch_id = s.batch_id
left join 
(
    select item_id,(b->>'f1')::real as b_unit_factor,b->>'f2' as b_unit_no,
                   (m->>'f1')::real as m_unit_factor,m->>'f2' as m_unit_no,
                   (s->>'f1')::real as s_unit_factor,s->>'f2' as s_unit_no, s->>'f3' as s_retail_price 
    from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,retail_price)) as json from info_item_multi_unit where company_id={companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s jsonb,m jsonb,b jsonb)
) mu1 on s.item_id = mu1.item_id 
where s.company_id = {companyID} and s.item_id = {item_id} and branch_id = {branch_id} and branch_position = {branch_position} {batchSql}
";
            List<ExpandoObject> stockInfo = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            return new JsonResult(new { result = "OK", data = stockInfo });
            //SaleSheetModel sheetModel = new SaleSheetModel(cmd);
            //var rr = await sheetModel.GetItemsInfo_branchPosition(companyID, itemsInfo,bGetAttrs, new { supcust_id });
            //return rr;
        }
        [HttpGet]
        public async Task<IActionResult> GetItemsForAttrRows(string operKey, int item_id, string branch_id)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);

            // var lstSonItems = new List<dynamic>();

            // string all_son_options_id = string.Join(",", lstSonItems.Select(t => "'" + t.son_options_id + "'"));
            SQLQueue QQ = new SQLQueue(cmd);
            string sql = @$"
SELECT ip.item_id son_item_id,item_name son_item_name,son_options_id,stock_qty,stock.branch_id,cost_price_spec,
       unit_from_s_to_bms (stock.stock_qty::numeric,b_unit_factor,m_unit_factor,1,b_unit_no,m_unit_no,s_unit_no) stock_qty_unit,s_buy_price,s_wholesale_price,s_cost_price,batch_stock,ip.batch_level,attr.remember_price
FROM info_item_prop ip
LEFT JOIN (select sum(stock_qty) as stock_qty,branch_id,item_id from stock where company_id ={companyID} group by item_id,branch_id ) stock on ip.item_id=stock.item_id and stock.branch_id in ({branch_id})
LEFT JOIN
(
    SELECT item_id,cost_price_avg s_cost_price,
           (b->>'f1')::real as b_unit_factor, b->>'f2' as b_unit_no, b->>'f3' as b_retail_price,b->>'f4' as b_buy_price,b->>'f5' as b_wholesale_price,
           (m->>'f1')::real as m_unit_factor, m->>'f2' as m_unit_no, m->>'f3' as m_retail_price,m->>'f4' as m_buy_price,m->>'f5' as m_wholesale_price,
           (s->>'f1')::real as s_unit_factor, s->>'f2' as s_unit_no, s->>'f3' as s_retail_price,s->>'f4' as s_buy_price,s->>'f5' as s_wholesale_price 
    FROM crosstab
    (
       ' SELECT ip.item_id,ip.cost_price_avg,unit_type,row_to_json(row(unit_factor,mu.unit_no,mu.retail_price,mu.buy_price,mu.wholesale_price)) as json 
         FROM info_item_multi_unit mu 
	     LEFT JOIN info_item_prop ip on mu.item_id = ip.son_mum_item and ip.company_id = mu.company_id
         WHERE mu.company_id={companyID} and ip.son_mum_item ={item_id}  order by ip.item_id
       ', $$values ('s'::text),('m'::text),('b'::text)$$
    ) as errr(item_id int,cost_price_avg numeric ,s jsonb,m jsonb,b jsonb)
) unit on unit.item_id = ip.item_id
left join(
    select s.item_id,json_agg(json_build_object('stock_qty',COALESCE(stock_qty,0),'batch_id',itb.batch_id,'produce_date',itb.produce_date,'batch_no',itb.batch_no,
    'stock_qty_unit',unit_from_s_to_bms (COALESCE(stock_qty,0)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no),
    'sell_pend_qty_unit',unit_from_s_to_bms (COALESCE(sell_pend_qty,0)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no),
    'usable_stock_qty_unit',unit_from_s_to_bms (COALESCE(stock_qty,0)::numeric-COALESCE(sell_pend_qty,0)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)
)) as batch_stock
        from stock s
        left join info_item_batch itb on itb.company_id ={companyID} and itb.batch_id = s.batch_id
        left join (select item_id,(b->>'f1')::real as b_unit_factor,(m->>'f1')::real as m_unit_factor,(s->>'f1')::real as s_unit_factor,
                                                            b->>'f2' as b_unit_no,m->>'f2' as m_unit_no,s->>'f2' as s_unit_no,s->>'f3' as s_buy_price
                                            from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,buy_price)) as json from info_item_multi_unit where company_id={companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
                                                as errr(item_id int, s jsonb,m jsonb,b jsonb)) mu1 on s.item_id = mu1.item_id
        where s.company_id = {companyID} and branch_id in({branch_id}) and stock_qty is not null group by s.item_id
)ss on ss.item_id = ip.item_id
LEFT JOIN (SELECT company_id,item_id,mum_attributes FROM  info_item_prop where company_id = {companyID} and mum_attributes is not null ) mum on mum.company_id =  ip.company_id and mum.item_id = ip.son_mum_item
LEFT JOIN info_attribute attr on attr.company_id = ip.company_id
WHERE ip.company_id={companyID} and ip.son_mum_item ={item_id}";

            QQ.Enqueue("son_items", sql);
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();

            var lstSonItems = new List<dynamic>();

            dynamic data = null;
            while (QQ.Count > 0)
            {
                string tbl = QQ.Dequeue();
                if (tbl == "son_items")
                {
                    var lst = CDbDealer.GetRecordsFromDr(dr);
                    lst.ForEach(r =>
                    {
                        lstSonItems.Add(r);
                    });

                    data = lstSonItems.GroupBy(r => new { ((dynamic)r).son_item_id, ((dynamic)r).son_item_name, ((dynamic)r).batch_level, ((dynamic)r).batch_stock, ((dynamic)r).son_options_id, ((dynamic)r).s_buy_price, ((dynamic)r).s_wholesale_price, ((dynamic)r).s_cost_price, ((dynamic)r).remember_price })
                               .Select(g =>
                               {
                                   var stocks = g.Select(c => new { c.branch_id, c.stock_qty, c.stock_qty_unit }).ToList();
                                   string stock_qty = "", stock_qty_unit = "";
                                   foreach (string b in branch_id.Split(","))
                                   {
                                       dynamic s = stocks.Find(s => s.branch_id == b);
                                       if (stock_qty != "")
                                       {
                                           stock_qty += ","; stock_qty_unit += ",";
                                       }
                                       if (s != null)
                                       {
                                           stock_qty += s.stock_qty;
                                           stock_qty_unit += s.stock_qty_unit;
                                       }
                                       else
                                       {
                                           stock_qty += " ";
                                           stock_qty_unit += " ";
                                       }

                                   }
                                   stock_qty = stock_qty.Replace(" ", "");
                                   stock_qty_unit = stock_qty_unit.Replace(" ", "");


                                   return new
                                   {
                                       g.Key.son_item_id,
                                       g.Key.son_item_name,
                                       g.Key.son_options_id,
                                       g.Key.s_buy_price,
                                       g.Key.s_wholesale_price,
                                       g.Key.s_cost_price,
                                       g.Key.batch_stock,
                                       g.Key.batch_level,
                                       g.Key.remember_price,

                                       stock_qty,
                                       stock_qty_unit
                                   };
                               }).ToList();

                }
            }
            QQ.Clear();

            return new JsonResult(new { result = "OK", msg = "", data });

        }

        [HttpGet]
        public async Task<IActionResult> GetSpecialPriceItemsForAttrRows(string operKey, int item_id, string supcust_id)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string sql = $@"SELECT * FROM(
SELECT sm.supcust_id,sm.start_time,sm.end_time,(sm.end_time::date-sm.start_time::date)+1 as special_days ,(sm.end_time::date-now()::date)+1 left_days,sd.item_id,ip.son_mum_item,ip.son_options_id,sd.unit_no,sd.unit_factor,mu.unit_type,sd.special_price,sd.happen_time,ao.attr_id,row_number() over(partition by sd.item_id order by sd.happen_time desc ) rn 
FROM sheet_special_price_detail sd 
LEFT JOIN sheet_special_price_main sm on sd.company_id = sm.company_id and sd.sheet_id = sm.sheet_id 
LEFT JOIN info_item_prop ip on ip.company_id = sd.company_id and ip.item_id = sd.item_id and ip.son_mum_item = {item_id}
LEFT JOIN info_item_multi_unit mu on mu.company_id = sd.company_id and mu.item_id = sd.item_id 
LEFT JOIN info_attr_opt ao on ao.company_id = ip.company_id and ao.opt_id ::text = ip.son_options_id 
WHERE sd.company_id = {companyID} and ip.item_id is not null  and sm.supcust_id = {supcust_id} and sm.approve_time is not null and sm.red_flag is null and sm.end_time::date >= NOW()::date )t
WHERE t.rn =1";
            List<ExpandoObject> specialPriceItems = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            return new JsonResult(new { result = "OK", msg = "", specialPriceItems });

        }
        [HttpPost]
    
        [HttpGet]
        public async Task<IActionResult> GetUnits(string operKey, string item_id, string query)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string sql = $"select  distinct unit_no,unit_factor from info_item_multi_unit where company_id={companyID} and item_id={item_id} and unit_no ilike '%{query}%' limit 30";

            dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            //string items=Newtonsoft.Json.JsonConvert.SerializeObject(rec); 
            return new JsonResult(new { result = "OK", records });
        }


        [HttpGet]
        public async Task<IActionResult> GetClientAccountInfo(string operKey, string supcust_id, SHEET_TYPE sheet_type)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            string prepaySubType = "YS";
            if (sheet_type == SHEET_TYPE.SHEET_BUY || sheet_type == SHEET_TYPE.SHEET_BUY_RETURN || sheet_type == SHEET_TYPE.SHEET_BUY_DD || sheet_type == SHEET_TYPE.SHEET_BUY_DD_RETURN) prepaySubType = "YF";
            var dispCondi = " where t.disp_month<=(to_char(now(),'MM')::int) and disp_year=(to_char(now(),'YYYY')::int) and sub_name is not null";
            string sql = $"select rights->'delicacy'->'allowAdvanceDisplayFee'->'value' allow_advance_disp_fee from info_operator o left join info_role r on r.role_id= o.role_id where o.company_id={companyID} and oper_id={operID}";
            dynamic advanceDisp = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            if (advanceDisp != null && advanceDisp.allow_advance_disp_fee == "true")
                dispCondi = $"where sub_name is not null";
            SQLQueue QQ = new SQLQueue(cmd);

            sql = @$"select sup.mobile mobile,sup_addr,boss_name,license_no,sup.acct_type,acct_way_name,sup.charge_seller,ro.oper_name charge_seller_name
                    from info_supcust sup
                    LEFT JOIN info_operator ro on ro.company_id = sup.company_id and ro.oper_id = sup.charge_seller 
                    left join info_acct_way aw on sup.acct_way_id=aw.acct_way_id and sup.company_id = aw.company_id
                    where sup.company_id = {companyID} and sup.supcust_id = {supcust_id}";
            QQ.Enqueue("info", sql);
            sql = @$"select sup.acct_cust_id,acct.sup_name acct_cust_name  
                     from info_supcust sup 
                     LEFT JOIN info_supcust acct on sup.company_id = acct.company_id and sup.acct_cust_id = acct.supcust_id  
                     where sup.company_id = {companyID} and sup.supcust_id = {supcust_id} ";
            QQ.Enqueue("acctInfo", sql);
            sql = @$"select p.sub_id,sub_name,round(balance::numeric,2) balance 
                     from prepay_balance b 
                     left join cw_subject p on p.sub_id = b.sub_id and p.company_id=b.company_id
                     LEFT JOIN info_supcust sup on sup.company_id = b.company_id 
                     where b.company_id = {companyID} and sub_name is not null and sup.supcust_id = {supcust_id}  and b.supcust_id = case when sup.acct_cust_id is null then sup.supcust_id else sup.acct_cust_id end and  sub_type in ('{prepaySubType}') and balance<>0;";
            QQ.Enqueue("prepay", sql);
            sql = $"select round(balance::numeric,2) balance from arrears_balance where company_id = {companyID} and supcust_id = {supcust_id} and balance<>0;";
            QQ.Enqueue("arrears", sql);

            if (sheet_type == SHEET_TYPE.SHEET_BORROW_ITEM || sheet_type == SHEET_TYPE.SHEET_RETURN_ITEM)
            {
                sql = @$"select t.sub_id,sub_name,sum(disp_left_money) disp_amount from 
                            (select sub_id,disp_left_money,
                                (case when start_month+months-1>12 then start_month+months-13 else start_month+months-1 end) disp_month,
                                (case when start_month+months-1>12 then start_year+1 else start_year end) as disp_year,months as disp_month_id
                                from (
                                select m.fee_sub_id sub_id,d.sheet_id,items_id,items_name,unnest(string_to_array((								
                                        COALESCE(month1_qty,0)-COALESCE(month1_given,0)||','||COALESCE(month2_qty,0)-COALESCE(month2_given,0)||','||
                                        COALESCE(month3_qty,0)-COALESCE(month3_given,0)||','||COALESCE(month4_qty,0)-COALESCE(month4_given,0)||','||
                                        COALESCE(month5_qty,0)-COALESCE(month5_given,0)||','||COALESCE(month6_qty,0)-COALESCE(month6_given,0)||','||
                                        COALESCE(month7_qty,0)-COALESCE(month7_given,0)||','||COALESCE(month8_qty,0)-COALESCE(month8_given,0)||','||
                                        COALESCE(month9_qty,0)-COALESCE(month9_given,0)||','||COALESCE(month10_qty,0)-COALESCE(month10_given,0)||','||
                                        COALESCE(month11_qty,0)-COALESCE(month11_given,0)||','||COALESCE(month12_qty,0)-COALESCE(month12_given,0)) ,','))::numeric disp_left_money,
                                        unnest(string_to_array('01,02,03,04,05,06,07,08,09,10,11,12',','))::int months,to_char(m.start_time,'YYYY')::int start_year,
                                        to_char(m.start_time,'MM')::int start_month 
                                from display_agreement_detail d left join display_agreement_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID}
                                where d.company_id = {companyID} and approve_time is not null and red_flag is null and supcust_id = {supcust_id} and items_id ='money' ) t
                            where disp_left_money > 0 ) t
                    left join cw_subject c on c.sub_id = t.sub_id and c.company_id = {companyID} 
                    {dispCondi} GROUP BY t.sub_id,sub_name;
                    ";
                QQ.Enqueue("disp", sql);
            }

            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            var prepay = new List<ExpandoObject>();
            var arrears = new List<ExpandoObject>();
            var disp = new List<ExpandoObject>();
            dynamic info = null;
            dynamic acctInfo = null;
            while (QQ.Count > 0)
            {
                Console.WriteLine(QQ);
                string sqlName = QQ.Dequeue();
                if (sqlName == "prepay")
                {
                    prepay = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "arrears")
                {
                    arrears = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "disp")
                {
                    disp = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "info")
                {
                    info = CDbDealer.Get1RecordFromDr(dr, false);
                }
                else if (sqlName == "acctInfo")
                {
                    acctInfo = CDbDealer.Get1RecordFromDr(dr, false);
                }
            }
            QQ.Clear();
            var response = new JsonResult(new { result = "OK", prepay, arrears, disp, info.mobile, info.sup_addr, info.boss_name, info.license_no, info.acct_type, info.acct_way_name, acctInfo, info.charge_seller, info.charge_seller_name });
            return response;
        }
        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic dSheet)  //[FromBody] dynamic sheet)
        {
            SheetBorrowItem sheet = null;
            string result;
            string msg = "";
            try
            {
                sheet = CPubVars.FromJsonObj<SheetBorrowItem>(dSheet);
            }
            catch (Exception e)
            {
                msg = e.Message;
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error("in SaleSheet.cshtml.Save:" + msg + JsonConvert.SerializeObject(dSheet));
                msg = "保存失败,请联系技术支持";
                return new JsonResult(new { result = "Error", msg });
            }
            /*if (await SaleSheetService.CheckOrderBindRelation(cmd, sheet.order_sheet_id, sheet.sheet_id))
            {
                return new JsonResult(new { result = "Error", msg = "该订单对应的销售订单已被绑定不能重复使用", sheet.sheet_id, sheet.sheet_no, sheet.happen_time, sheet.approve_time });
            }*/
            if (sheet.red_flag != "")
            {
                return new JsonResult(new { result = "Error", msg = "红冲单据不能重复审核" });

            }
            sheet.Init();
            msg = await sheet.Save(cmd);
            result = msg == "" ? "OK" : "Error";

            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.make_time });
        }
        [HttpPost]
        public async Task<IActionResult> CheckHasInventoryBeforeApprove([FromBody] dynamic dSheet)
        {
            string sql = "";
            List<string> list = new List<string>();
            SheetBorrowItem sheet = CPubVars.FromJsonObj<SheetBorrowItem>(dSheet);
            if (!string.IsNullOrEmpty(sheet.OperKey))
            {
                Security.GetInfoFromOperKey(sheet.OperKey, out string companyID, out string operID);
                sheet.OperID = operID;
                sheet.company_id = companyID;
            }
            if (sheet.sheet_id != "")
            {
                //sql = sheet.GetSqlCheckHasInventoryBeforeApprove();
                // list = await sheet.DealHasInventory(sql, cmd);
            }
            return new JsonResult(new { inventoryItemNames = list });
        }

        [HttpPost]
        public async Task<IActionResult> Review([FromBody] dynamic data)
        {
            string sheet_id = data.sheet_id;
            string now = CPubVars.GetDateText(DateTime.Now);
            string msg = "";
            dynamic rec = await CDbDealer.Get1RecordFromSQLAsync($"select approve_time,red_flag from borrow_item_main where company_id={Token.CompanyID} and sheet_id={sheet_id};", cmd);
            if (rec == null)
            {
                msg = "数据不存在";
            }
            else if (rec.red_flag != "")
            {
                msg = "红冲单据不能修改";
            }
            else if (rec.approve_time == "")
            {
                msg = "单据未审核不能修改";
            }
            else
            {
                cmd.CommandText = $"update borrow_item_main set review_time ='{now}',reviewer_id={Token.OperID} where company_id={Token.CompanyID} and sheet_id={sheet_id};";
                await cmd.ExecuteNonQueryAsync();
            }

            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";

            return new JsonResult(new { result, msg, review_time = now });
        }

        [HttpPost]
        public async Task<IActionResult> SaveAndApprove([FromBody] dynamic dSheet)
        {
            /*�߼��Ƶ�SheetSaleʵ�֣�Ŀǰ����жϷ��ڸ������д������©���õ��������û�������
             
            if (await SaleSheetService.CheckOrderBindRelation(cmd, sheet.order_sheet_id, sheet.sheet_id))
            {
                return new JsonResult(new { result= "Error", msg = "订单已关联到销售订单", sheet.sheet_id, sheet.sheet_no, sheet.happen_time, sheet.approve_time });
            }*/
            SheetBorrowItem sheet = null;
            string msg = "";
            try
            {
                sheet = CPubVars.FromJsonObj<SheetBorrowItem>(dSheet);
            }
            catch (Exception e)
            {
                msg = e.Message;
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error("in SaleSheet.cshtml.SaveAndApprove:" + msg + JsonConvert.SerializeObject(dSheet));
                msg = "审核失败,请联系技术支持";
                return new JsonResult(new { result = "Error", msg });
            }

            if (sheet.red_flag != "")
            {
                return new JsonResult(new { result = "Error", msg = "红冲单据不能重复审核" });

            }
            sheet.Init();
            if (sheet.bReview)
            {
                sheet.reviewer_id = sheet.OperID;
                sheet.review_time = CPubVars.GetDateText(DateTime.Now);
            }
            sheet._httpClientFactory = this._httpClientFactory;
            if (sheet.isRedAndChange)
            {
                if (!sheet.old_sheet_id.IsValid())
                {
                    msg = "修改时没有获取原数据的标识";
                }
                else
                {
                    msg = await sheet.RedAndChange<SheetBorrowItem>(cmd);
                }
            }
            else
            {
                msg = await sheet.SaveAndApprove(cmd);
            }

            string result = msg == "" ? "OK" : "Error";
            /*if (msg != "") result = "Error";  �Ƶ�sheetSale����
            else
            {
                await SaleOrderSheetService.ChangeReceiptStatus(cmd, sheet);
            }
            */
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time, sheet.approve_time });
        }

        [HttpPost]
        public async Task<IActionResult> Red([FromBody] dynamic data)
        {
            string sheet_id = data.sheet_id;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            //SheetSale sheet = new SheetBorrowItem(SHEET_RETURN.EMPTY, LOAD_PURPOSE.SHOW);
            SheetBorrowItem sheet = new SheetBorrowItem(SHEET_BORROW_RETURN.EMPTY, LOAD_PURPOSE.SHOW);
            //redbrief ����ȷ
            sheet._httpClientFactory = this._httpClientFactory;
            string msg = await sheet.Red(cmd, companyID, sheet_id, operID, "");

            string result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time });
        }

        [HttpPost]
        public async Task<IActionResult> Delete([FromBody] dynamic data)
        {
            string sheet_id = data.sheet_id;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
             SheetBorrowItem sheet = new SheetBorrowItem(SHEET_BORROW_RETURN.EMPTY, LOAD_PURPOSE.SHOW);

            string msg = await sheet.Delete(cmd, companyID, sheet_id, operID);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, sheet_id });
        }

        [HttpPost]
        public async Task<IActionResult> AppendBrief([FromBody] dynamic data)
        {
            string sheetID = data.sheetID;
            string newBrief = data.newBrief;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
             SheetBorrowItem sheet = new SheetBorrowItem(SHEET_BORROW_RETURN.EMPTY, LOAD_PURPOSE.SHOW);

            string msg = await sheet.AppendBrief(cmd, companyID, sheetID, newBrief);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg });
        }

        [HttpGet]
        public async Task<IActionResult> GetSheetToPrint(string operKey, string sheet_id, bool smallUnitBarcode, string printTemplate)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            cmd.ActiveDatabase = "";
            SheetBorrowItem sheet = new SheetBorrowItem(LOAD_PURPOSE.SHOW);
            await sheet.Load(cmd, companyID, sheet_id);
            if (!sheet.supcust_id.IsValid())
            {
                MyLogger.LogMsg("invalid supcust_id in salesheet", companyID);
            }
            sheet.LoadAttrRowsForPrint(smallUnitBarcode);

            dynamic dTemplate = null;
            if (printTemplate.IsValid())
            {
                dTemplate = JsonConvert.DeserializeObject(printTemplate);
            }
            await sheet.LoadInfoForPrint(cmd, smallUnitBarcode, true, dTemplate);

            string msg = "";
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, sheet });
        }

        [HttpGet]
        public async Task<IActionResult> GetSheetToSyncAccessTicketSys(string operKey, string sheet_id, bool smallUnitBarcode)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetBorrowItem sheet = new SheetBorrowItem(LOAD_PURPOSE.SHOW);
            await sheet.Load(cmd, companyID, sheet_id);
            sheet.LoadAttrRowsForPrint(smallUnitBarcode);
            await sheet.LoadInfoForPrint(cmd, smallUnitBarcode);
            string msg = "";
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, sheet });
        }

        [HttpGet]
        public async Task<IActionResult> GetBranchAndPositionInfo(string operKey, string dataItemName, string branchId)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string sql = @$"";
            if (dataItemName == "branch_id")
            {
                sql = $@"select branch_id as v,branch_name as l,py_str as z from info_branch where company_id={companyID}  and COALESCE(status,'1')='1' order by order_index,case when branch_type='store' then 0 else 1 end";
            }
            else if (dataItemName == "branch_position")
            {
                if (branchId.IsInvalid()) return new JsonResult(new { result = "Error", msg = "分库信息不正确" });
                sql = $@"select ibp.branch_position as v,branch_position_name as l,ibp.py_str as z ,type_name as t from info_branch_position  ibp
left join (
select * from info_branch_position_type where company_id = {companyID} union select * from info_branch_position_type where company_id = -1
)ibpt on ibpt.type_id = ibp.type_id
where ibp.company_id={companyID}  and COALESCE(position_status,'1')='1' and branch_id = {branchId}";
            }

            try
            {
                dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                return new JsonResult(new { result = "OK", records });
            }
            catch (Exception e)
            {
                MyLogger.LogMsg($"in GetInfoForApprove. error:${e.Message}, ${e.StackTrace},sql:${sql}", companyID, "approve");
                return new JsonResult(new { result = "Error", msg = "信息获取失败" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetBranchPositionForReturn(string operKey, string branchId, string backBranchPositionType)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            branchId = branchId.IsInvalid() ? "-1" : branchId;
            //backBranchPositionType = backBranchPositionType=="����" ? "0" : (backBranchPositionType=="����"?"-1":(backBranchPositionType=="����"?"1":"-2"));
            string sql = @$"select branch_position, branch_position_name,branch_id from info_branch_position 
where company_id = {companyID} and branch_id = {branchId} and type_id = {backBranchPositionType} and COALESCE(position_status,'1')='1' ORDER BY branch_position limit 1;";
            try
            {
                dynamic records = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);

                return new JsonResult(new { result = "OK", records });
            }
            catch (Exception e)
            {
                MyLogger.LogMsg($"in GetInfoForApprove. error:${e.Message}, ${e.StackTrace},sql:${sql}", companyID, "approve");
                return new JsonResult(new { result = "Error", msg = "信息获取失败" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetBranchPosition(string operKey, string items_id, string branch_id, string branchPositionType, bool isShowNegativeStock)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            BorrowItemSheetModel sheetModel = new BorrowItemSheetModel(cmd);
            dynamic rr = await sheetModel.GetBranchPosition(cmd, companyID, branch_id, branchPositionType);
            dynamic r2 = null;

            if (rr.Value.result == "OK")
            {
                string branch_position = "0";
                if (rr.Value.data != null)
                {
                    branch_position = rr.Value.data.branch_position;
                }
                r2 = await sheetModel.getBatchStock(cmd, companyID, branch_id, branch_position, items_id, isShowNegativeStock);
                if (r2.Value.result == "OK")
                {
                    return new JsonResult(new { result = "OK", branchPosition = rr.Value.data, batchStockForShow = r2.Value.batchStockForShow, batchStockTotal = r2.Value.batchStockTotal });
                }
            }
            return new JsonResult(new { result = "ERROR", msg = "获取数据失败" });
        }
        [HttpGet]
        public async Task<IActionResult> GetBatchStock(string operKey, string items_id, string branch_id, string branch_position, bool isShowNegativeStock)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            BorrowItemSheetModel sheetModel = new BorrowItemSheetModel(cmd);
            dynamic rr = await sheetModel.getBatchStock(cmd, companyID, branch_id, branch_position, items_id, isShowNegativeStock);

            if (rr.Value.result == "OK")
            {
                return new JsonResult(new { result = "OK", batchStockForShow = rr.Value.batchStockForShow, batchStockTotal = rr.Value.batchStockTotal });
            }
            return new JsonResult(new { result = "ERROR", msg = "获取数据失败" });
        }

        [HttpGet]
        public async Task<IActionResult> GetRecentRetailPrice(string operKey, string item_id, string unit_no, string supcust_id)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string supcustSqlCondition = supcust_id.IsValid() ? $"and supcust_id = {supcust_id}" : "";
            string recentPricecSql = $"select recent_retail_price from client_recent_prices where item_id = {item_id} and company_id = {companyID} and unit_no = '{unit_no}' {supcustSqlCondition} order by happen_time desc limit 1";
            dynamic rp = await CDbDealer.Get1RecordFromSQLAsync(recentPricecSql, cmd);
            if(!string.IsNullOrEmpty(supcustSqlCondition) && rp == null)
            {
                recentPricecSql = $"select recent_retail_price from client_recent_prices where item_id = {item_id} and company_id = {companyID} and unit_no = '{unit_no}' and recent_retail_price is not null  order by happen_time desc limit 1";
                rp = await CDbDealer.Get1RecordFromSQLAsync(recentPricecSql, cmd);
            }
            if (rp != null)
            {
                if (string.IsNullOrEmpty((string)rp.recent_retail_price))
                {
                    return new JsonResult(new { result = "EMPTY"});
                }
                return new JsonResult(new { result = "OK", record = rp });
            }
            return new JsonResult(new { result = "ERROR", msg = "获取数据失败" });
        }


    }
}