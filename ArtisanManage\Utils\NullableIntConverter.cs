using System;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace ArtisanManage.Utils
{
    /// <summary>
    /// 可空整数JSON转换器
    /// 处理前端传递空字符串时转换为null的情况
    /// </summary>
    public class NullableIntConverter : JsonConverter<int?>
    {
        public override int? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            switch (reader.TokenType)
            {
                case JsonTokenType.Number:
                    return reader.GetInt32();
                
                case JsonTokenType.String:
                    var stringValue = reader.GetString();
                    if (string.IsNullOrEmpty(stringValue))
                    {
                        return null;
                    }
                    if (int.TryParse(stringValue, out int result))
                    {
                        return result;
                    }
                    return null;
                
                case JsonTokenType.Null:
                    return null;
                
                default:
                    return null;
            }
        }

        public override void Write(Utf8JsonWriter writer, int? value, JsonSerializerOptions options)
        {
            if (value.HasValue)
            {
                writer.WriteNumberValue(value.Value);
            }
            else
            {
                writer.WriteNullValue();
            }
        }
    }

    /// <summary>
    /// 可空整数JSON转换器（Newtonsoft.Json版本）
    /// </summary>
    public class NullableIntNewtonsoftConverter : Newtonsoft.Json.JsonConverter<int?>
    {
        public override int? ReadJson(Newtonsoft.Json.JsonReader reader, Type objectType, int? existingValue, bool hasExistingValue, Newtonsoft.Json.JsonSerializer serializer)
        {
            switch (reader.TokenType)
            {
                case Newtonsoft.Json.JsonToken.Integer:
                    return Convert.ToInt32(reader.Value);
                
                case Newtonsoft.Json.JsonToken.String:
                    var stringValue = reader.Value?.ToString();
                    if (string.IsNullOrEmpty(stringValue))
                    {
                        return null;
                    }
                    if (int.TryParse(stringValue, out int result))
                    {
                        return result;
                    }
                    return null;
                
                case Newtonsoft.Json.JsonToken.Null:
                case Newtonsoft.Json.JsonToken.Undefined:
                    return null;
                
                default:
                    return null;
            }
        }

        public override void WriteJson(Newtonsoft.Json.JsonWriter writer, int? value, Newtonsoft.Json.JsonSerializer serializer)
        {
            if (value.HasValue)
            {
                writer.WriteValue(value.Value);
            }
            else
            {
                writer.WriteNull();
            }
        }
    }
}
