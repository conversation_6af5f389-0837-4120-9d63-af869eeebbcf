﻿using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static ArtisanManage.Services.CommonTool;
using Newtonsoft.Json;
using System.Dynamic;
using System.Data.Odbc;
using System.Net;
using System.IO;
using System.Net.Http;
using System.Data.Common;
using ArtisanManage.YingJiangBackstage.Dao.ContractDao;
using System.Text.RegularExpressions;
using NPOI.SS.UserModel;
using Microsoft.AspNetCore.JsonPatch.Internal;
using Newtonsoft.Json.Linq;
using NPOI.HSSF.Record.Chart;
using System.DrawingCore;
using System.DrawingCore.Imaging;
using Antlr4.Runtime.Misc;
using Org.BouncyCastle.Asn1.X509;
using NPOI.HSSF.Record;
using NPOI.SS.Formula.PTG;
using Org.BouncyCastle.Asn1.Ocsp;
using System.Numerics;
using System.Collections;
using NPOI.Util;
using Quartz.Util;
using Org.BouncyCastle.Asn1.Cmp;
using System.ComponentModel.Design;

namespace ArtisanManage.Services
{
    public class ResellerService
    {
        /// <summary>
        /// </summary>
        /// <param name="data"> operKey，resellerCompanyId， planID</param>
        /// <param name="cmd"></param>
        /// <param name="otherTran"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static async Task<dynamic> ItemInfoSyncService(dynamic data, CMySbCommand cmd, CMySbTransaction otherTran = null)
        {
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID, out string operID);
            bool selfTran = true;
            CMySbTransaction tran = null;
            if (otherTran != null)
            {
                tran = otherTran;
                selfTran = false;
            }
            else
            {
                tran = await cmd.Connection.BeginTransactionAsync();
            }
            string errMsg = "商品档案同步失败";
            try
            {
                string resellerCompanyId = data.resellerCompanyId;
                string planID = data.planID;
                dynamic brandsId = await CDbDealer.Get1RecordFromSQLAsync($"SELECT brand_id FROM rs_plan where plan_id = {planID}", cmd);
                
                string[] brandIdList = ((string)brandsId.brand_id).Split(",");

                string brandsSqlCondition = string.Join(" or brand_id = ", brandIdList);

                # region 品牌同步
                errMsg = "商品品牌同步失败";
                string brandsInfoSql = $"SELECT * FROM info_item_brand where company_id={companyID} and(brand_id = {brandsSqlCondition})";
                dynamic brandsInfo = await CDbDealer.GetRecordsFromSQLAsync(brandsInfoSql, cmd);

                string brandsSyncSqlHeader = $"insert into info_item_brand (company_id, brand_name, remark,brand_status, brand_order_index, rs_mum_id) ";
                string brandsSyncSqlBody = "values ";
                foreach (var brand in brandsInfo)
                {
                    var old_brand_id = brand.brand_id;
                    var brand_name = brand.brand_name;
                    var remark = brand.remark == "" ? null : brand.remark;
                    var brand_status = brand.brand_status == "" ? "null" : brand.brand_status;
                    var brand_order_index = brand.brand_order_index == "" ? "null" : brand.brand_order_index;
                    brandsSyncSqlBody = brandsSyncSqlBody + $"({resellerCompanyId},'{brand_name}','{remark}',{brand_status},{brand_order_index},{old_brand_id}) ,";
                }
                brandsSyncSqlBody = brandsSyncSqlBody.Trim(',');
                string brandsSyncSql = brandsSyncSqlHeader + brandsSyncSqlBody + " returning rs_mum_id,brand_id";

                dynamic brandsRet = await CDbDealer.GetRecordsFromSQLAsync(brandsSyncSql, cmd);
                Dictionary<string, string> brandsPair = new Dictionary<string, string>();
                foreach (var pair in brandsRet)
                {
                    brandsPair[pair.rs_mum_id] = pair.brand_id;
                }
                #endregion

                # region   商品档案同步

                errMsg = "商品档案同步失败";
                // 统一创建一个供应商类别 TODO
                dynamic companyName = await CDbDealer.Get1RecordFromSQLAsync($"SELECT company_name FROM g_company WHERE company_id = {companyID}", cmd);
                dynamic retClass = await CDbDealer.Get1RecordFromSQLAsync($"SELECT class_id,mother_id FROM info_item_class WHERE company_id = {resellerCompanyId} AND class_name LIKE '%{companyName.company_name}%';", cmd);
                string otherClass = "";
                // TODO 类别创建逻辑
                // NOTICE 可能存在问题 
                if (retClass == null)
                {
                    dynamic motherClass = await CDbDealer.Get1RecordFromSQLAsync($"SELECT class_id FROM info_item_class WHERE company_id = {resellerCompanyId} AND mother_id = 0", cmd);
                    string allTypeID = motherClass.class_id;
                    retClass = await CDbDealer.Get1RecordFromSQLAsync($"insert into info_item_class (company_id,class_name,mother_id) values({resellerCompanyId},'{companyName.company_name}同步商品',{allTypeID}) returning class_id", cmd);
                    otherClass = $"/{allTypeID}/{retClass.class_id}/";
                }
                else
                {
                    otherClass = $"/{retClass.mother_id}/{retClass.class_id}/";

                }
                brandsSqlCondition = string.Join(" or item_brand = ", brandIdList);
                // string itemsInfoSql = $"SELECT item_id, item_name, item_class, other_class, item_spec, item_brand FROM info_item_prop where company_id={companyID} and (item_brand = {brandsSqlCondition})";
                string itemsInfoSql = $"SELECT * FROM info_item_prop where company_id={companyID} and (item_brand = {brandsSqlCondition})";
                dynamic itemsInfo = await CDbDealer.GetRecordsFromSQLAsync(itemsInfoSql, cmd);
                // string itemsSyncSqlHeader = $"insert into info_item_prop (item_name, item_class, other_class, item_spec,item_brand, company_id, rs_mum_id) ";
                // 选择全部的表头语句
                // string itemInfoPropHeaderSql = $"SELECT string_agg( column_name,',') all_column_name FROM information_schema.columns WHERE table_name = 'info_item_prop'";
                // dynamic itemInfoPropHeader = await CDbDealer.Get1RecordFromSQLAsync(itemInfoPropHeaderSql, cmd);
                // string itemInfoHeaderValue = ((string)itemInfoPropHeader.all_column_name).Replace("item_id,", "");

                string itemInfoHeaderValue = @"company_id,item_name,item_no,barcode,item_spec,simple_name,item_class,item_brand,unit_no,  py_str, py_str1,
retail_price,status,item_images,item_alias,rs_mum_id ";
                string[] itemHeaderList = itemInfoHeaderValue.Replace(" ","").Split(",").Select(s => s.Trim()).ToArray(); 
                string itemsSyncSqlHeader = $"insert into info_item_prop ({itemInfoHeaderValue}) ";
                string itemsSyncSqlBody = "values ";

                foreach (ExpandoObject item in itemsInfo)
                {
                    var itemProperties = (IDictionary<string, object>)item;
                    string value = "(";

                    foreach (string header in itemHeaderList)
                    {
                        if (header == "company_id")
                        {
                            value += $"{resellerCompanyId}";
                        }
                        else if (header == "rs_mum_id")
                        {
                            value += itemProperties["item_id"];
                        }
                        else if (header == "item_brand")
                        {
                            value += brandsPair[(string)itemProperties["item_brand"]];
                        }
                       /* else if (header == "item_class")
                        {
                            value += $"{retClass.class_id}";
                        }*/
                        /*else if (header == "buy_price")
                        {
                            value += (string)itemProperties["wholesale_price"] == "" ? "null" : $"'{((string)itemProperties["wholesale_price"]).Trim()}'";
                        }*/
                        /*else if (header == "other_class")
                        {
                            value += $"'{otherClass}'";
                        }*/
                        else
                        {
                            value += (string)itemProperties[header] == "" ? "null" : $"'{((string)itemProperties[header]).Trim()}'";
                        }
                        value += ",";
                    }
                    value = value.Trim(',');
                    value = value + "),";
                    itemsSyncSqlBody += value;
                }
                itemsSyncSqlBody = itemsSyncSqlBody.Trim(',');
                string itemSyncSql = itemsSyncSqlHeader + itemsSyncSqlBody + " returning item_id, rs_mum_id";
                dynamic itemsRet = await CDbDealer.GetRecordsFromSQLAsync(itemSyncSql, cmd);
                Dictionary<string, string> itemsPair = new Dictionary<string, string>();

                #endregion

                #region 单位同步
                errMsg = "商品单位信息同步失败";
                string itemSqlCondition = "";
                foreach (var pair in itemsRet)
                {
                    itemSqlCondition = itemSqlCondition + $"'{pair.rs_mum_id}',";
                    itemsPair[(string)pair.rs_mum_id] = (string)pair.item_id;
                }
                itemSqlCondition = itemSqlCondition.Trim(',');
                string unitsInfoSql = $"SELECT * FROM info_item_multi_unit where company_id={companyID} and item_id in ({itemSqlCondition})";
                dynamic unitsInfo = await CDbDealer.GetRecordsFromSQLAsync(unitsInfoSql, cmd);

                // 查询所有表头
                // string unitsInfoPropHeaderSql = $"SELECT string_agg( column_name,',') all_column_name FROM information_schema.columns WHERE table_name = 'info_item_multi_unit'";
                // dynamic unitsInfoPropHeader = await CDbDealer.Get1RecordFromSQLAsync(unitsInfoPropHeaderSql, cmd);
                // string unitsInfoHeaderValue = ((string)unitsInfoPropHeader.all_column_name);

                string unitsInfoHeaderValue = @"item_id,company_id,unit_no,unit_factor,retail_price,barcode,unit_type";
                dynamic priceOptions = await CDbDealer.Get1RecordFromSQLAsync($"SELECT price_sync_options FROM rs_plan where plan_id = {planID}", cmd);
                if (!string.IsNullOrEmpty((string)priceOptions.price_sync_options))
                {
                    unitsInfoHeaderValue = unitsInfoHeaderValue + $",{priceOptions.price_sync_options}";
                }
                string[] unitsHeaderList = unitsInfoHeaderValue.Replace(" ","").Split(",").Select(s => s.Trim()).ToArray(); 
                string unitsSyncSqlHeader = $"insert into info_item_multi_unit ({unitsInfoHeaderValue}) ";
                string unitsSyncSqlBody = "values ";

                foreach (ExpandoObject unit in unitsInfo)
                {
                    var unitProperties = (IDictionary<string, object>)unit;
                    string value = "(";

                    foreach (string header in unitsHeaderList)
                    {
                        if (header == "company_id")
                        {
                            value += $"{resellerCompanyId}";
                        }
                        else if (header == "item_id")
                        {
                            value += itemsPair[(string)unitProperties["item_id"]];
                        }
                        /*else if (header == "buy_price")
                        {
                            value += (string)unitProperties["wholesale_price"] == "" ? "null" : $"'{((string)unitProperties["wholesale_price"]).Trim()}'";
                        }*/
                        else
                        {
                            value += (string)unitProperties[header] == "" ? "null" : $"'{((string)unitProperties[header]).Trim()}'";
                        }
                        value += ",";
                    }
                    value = value.Trim(',');
                    value = value + "),";
                    unitsSyncSqlBody += value;
                }
                unitsSyncSqlBody = unitsSyncSqlBody.Trim(',');
                string unitsSyncSql = unitsSyncSqlHeader + unitsSyncSqlBody;
                // 
                // await cmd.ExecuteReaderAsync();
                await CDbDealer.GetRecordsFromSQLAsync(unitsSyncSql, cmd);

                #endregion
                if (tran != null && selfTran)
                {
                    tran.Commit();
                }

                return new { result = "OK", msg = "" };

            }
            catch (Exception e)
            {
                if (tran != null && selfTran)
                {

                    tran.Rollback();

                }
                string logErrMsg = $"In ReSellerEdit ItemInfoSyncService ,msg:{e.Message},code:{e.StackTrace},targetSite:{e.TargetSite}";
                NLogger.Error(e.ToString());
                MyLogger.LogMsg(logErrMsg, companyID);
                Console.WriteLine(e);
                // throw new Exception(e.Message);
                return new { result = "Error", msg = errMsg };
            }
        }

        public static async Task<dynamic> BrandChangeSyncService(dynamic data, CMySbCommand cmd, CMySbTransaction otherTran = null)
        {
            // 修改分销方案调用
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID, out string operID);
            bool selfTran = true;
            CMySbTransaction tran = null;
            if (otherTran != null)
            {
                tran = otherTran;
                selfTran = false;
            }
            else
            {
                tran = await cmd.Connection.BeginTransactionAsync();
            }
            string errMsg = "关联品牌变更同步失败";
            try
            {
                string[] brandList = ((string)data.brandId).Split(',');
                string fatherCompanyId = (string)data.fatherCompanyId;
                string sonCompanyId = (string)data.sonCompanyId;
                string planId = (string)data.planId;
                string priceSyncOptions = (string)data.priceSyncOptions;
                if (sonCompanyId != "")
                {
                    // 如果指定子公司ID，调用为分销商修改了分销方案引起的
                    foreach (string fatherBrandId in brandList)
                    {
                        // 每个品牌轮流查看，首先看分销商有没有这个品牌
                        // dynamic sonBrandRet = await CDbDealer.Get1RecordFromSQLAsync($"select brand_id from info_item_brand where company_id = {sonCompanyId} and rs_mum_id = {fatherBrandId}", cmd);
                        // 有这个品牌也得考虑到是曾经同步的，后来取消，又新增的，商品档案可能发生了变化
                        // if(sonBrandRet != null) { continue; }
                        // 没有这个品牌就把商品档案同步过去
                        dynamic addItemRet = await AddBrandItemInfoSync(new
                        {
                            operKey = (string)data.operKey,
                            fatherCompanyId = fatherCompanyId,
                            sonCompanyId = sonCompanyId,
                            fatherBrandId = fatherBrandId,
                            priceSyncOptions = priceSyncOptions

                        }, cmd, tran);
                        if (addItemRet.result != "OK")
                        {
                            if (tran != null && selfTran)
                            {

                                tran.Rollback();

                            }
                            return new { result = "Error", msg = "分销商品牌/商品档案同步失败" };
                        }
                    }
                }
                else
                {
                    // 如果没有指定分销公司ID，调用为修改了某个分销方案，所有相关分销商都要发生同步
                    dynamic rsCompanyIdList = await CDbDealer.GetRecordsFromSQLAsync($"select reseller_company_id from rs_seller where plan_id = {planId}", cmd);
                    foreach (var rsCompanyId in rsCompanyIdList)
                    {
                        sonCompanyId = (string)rsCompanyId.reseller_company_id;
                        foreach (string fatherBrandId in brandList)
                        {
                            // 每个品牌轮流查看，首先看分销商有没有这个品牌
                            // dynamic sonBrandRet = await CDbDealer.Get1RecordFromSQLAsync($"select brand_id from info_item_brand where company_id = {sonCompanyId} and rs_mum_id = {fatherBrandId}", cmd);
                            // 有这个品牌也得考虑到是曾经同步的，后来取消，又新增的，商品档案可能发生了变化
                            // if(sonBrandRet != null) { continue; }
                            // 没有这个品牌就把商品档案同步过去
                            dynamic addItemRet = await AddBrandItemInfoSync(new
                            {
                                operKey = (string)data.operKey,
                                fatherCompanyId = fatherCompanyId,
                                sonCompanyId = sonCompanyId,
                                fatherBrandId = fatherBrandId,
                                priceSyncOptions = priceSyncOptions

                            }, cmd, tran);
                            if (addItemRet.result != "OK")
                            {
                                if (tran != null && selfTran)
                                {

                                    tran.Rollback();

                                }
                                return new { result = "Error", msg = "分销商品牌/商品档案同步失败" };
                            }

                            
                        }
                        dynamic classInfoSyncResult = await ResellerService.ClassSyncService(new
                        {
                            fatherItemId = "",
                            fatherCompanyId = fatherCompanyId,
                            sonCompanyId = sonCompanyId,
                            planId = "",
                            brandId = data.brandId,
                            reverseBind = false,
                        }, cmd, tran);
                        if (classInfoSyncResult.result != "OK")
                        {
                            if (tran != null && selfTran)
                            {

                                tran.Rollback();

                            }
                            return new { result = "Error", msg = "分销商商品档案类别同步失败" };
                        }
                    }
                }
                if (tran != null && selfTran)
                {

                    tran.Commit();

                }
                return new { result = "OK", msg = "" };
            }
            catch (Exception e)
            {
                if (tran != null && selfTran)
                {

                    tran.Rollback();

                }
                string logErrMsg = $"In ReSellerEdit Save,msg:{e.Message},code:{e.StackTrace},targetSite:{e.TargetSite}";
                NLogger.Error(e.ToString());
                MyLogger.LogMsg(logErrMsg, companyID);
                Console.WriteLine(e);
                // throw new Exception(e.Message);

                return new { result = "Error", msg = "商品档案同步失败" };
            }
        }

        public static async Task<dynamic> ClassSyncService(dynamic data, CMySbCommand cmd, CMySbTransaction otherTran = null)
        {
            // Security.GetInfoFromOperKey((string)data.operKey, out string companyID, out string operID);
            bool selfTran = true;
            CMySbTransaction tran = null;
            if (otherTran != null)
            {
                tran = otherTran;
                selfTran = false;
            }
            else
            {
                tran = await cmd.Connection.BeginTransactionAsync();
            }
            try
            {
                string fatherItemId = (string)data.fatherItemId;
                string planId = (string)data.planId;
                string fatherCompanyId = (string)data.fatherCompanyId;
                string sonCompanyId = (string)data.sonCompanyId;
                string brandId = (string)data.brandId;
                bool reverseBind = (bool)data.reverseBind;
                string fatherClassSql = @$"
                                           SELECT DISTINCT iip.item_class, iip.other_class, iic.class_name, iic.mother_id
                                            FROM info_item_prop iip
                                            LEFT JOIN info_item_class iic ON iip.item_class = iic.class_id
                                            WHERE iip.company_id = {fatherCompanyId} AND iip.item_id = {fatherItemId}
                                            ORDER BY iip.item_class";
                if (!planId.IsNullOrWhiteSpace() && brandId.IsNullOrWhiteSpace() )
                {
                    

                    if (reverseBind)
                    {
                        string brandSql = $"select brand_id from rs_plan where plan_id = {planId} and company_id = {sonCompanyId}";
                        dynamic mumBrandsInfo = await CDbDealer.Get1RecordFromSQLAsync(brandSql, cmd);
                        fatherClassSql = @$"
                                           SELECT DISTINCT iip.item_class, iip.other_class, iic.class_name, iic.mother_id
                                            FROM info_item_prop iip
                                            LEFT JOIN info_item_class iic ON iip.item_class = iic.class_id
                                            LEFT JOIN info_item_brand iib ON iip.item_brand = iib.brand_id
                                            WHERE iip.company_id = {fatherCompanyId} AND iib.rs_mum_id IN ({mumBrandsInfo.brand_id})
                                            ORDER BY iip.item_class";
                    }
                    
                    else
                    {
                        string brandSql = $"select brand_id from rs_plan where plan_id = {planId} and company_id = {fatherCompanyId}";
                        dynamic brandsInfo = await CDbDealer.Get1RecordFromSQLAsync(brandSql, cmd);
                        string brandList = (string)brandsInfo.brand_id;

                        fatherClassSql = @$"
                                           SELECT DISTINCT iip.item_class, iip.other_class, iic.class_name, iic.mother_id
                                            FROM info_item_prop iip
                                            LEFT JOIN info_item_class iic ON iip.item_class = iic.class_id
                                            WHERE iip.company_id = {fatherCompanyId} AND iip.item_brand IN ({brandList})
                                            ORDER BY iip.item_class";
                    }
                    
                    

                }else if (planId.IsNullOrWhiteSpace() && !brandId.IsNullOrWhiteSpace())
                {
                    fatherClassSql = @$"
                                           SELECT DISTINCT iip.item_class, iip.other_class, iic.class_name, iic.mother_id
                                            FROM info_item_prop iip
                                            LEFT JOIN info_item_class iic ON iip.item_class = iic.class_id
                                            WHERE iip.company_id = {fatherCompanyId} AND iip.item_brand IN ({brandId})
                                            ORDER BY iip.item_class";
                }

                dynamic sonMotherClass = await CDbDealer.Get1RecordFromSQLAsync($"SELECT * FROM info_item_class WHERE company_id = {sonCompanyId} AND mother_id = 0", cmd);
                dynamic fatherMotherClass = await CDbDealer.Get1RecordFromSQLAsync($"SELECT class_id FROM info_item_class WHERE company_id = {fatherCompanyId} AND mother_id = 0", cmd);

                Dictionary<string, string> fatherToSonClass = new Dictionary<string, string>();
                
                Dictionary<string, dynamic> fatherClassInfoDic = new Dictionary<string, dynamic>();

                // 加入“全部”类别的对照
                fatherToSonClass[(string)fatherMotherClass.class_id] = (string)sonMotherClass.class_id;

                fatherClassInfoDic[(string)fatherMotherClass.class_id] = fatherMotherClass;
                dynamic fatherClassInfo = await CDbDealer.GetRecordsFromSQLAsync(fatherClassSql, cmd);
                
                foreach (var classInfo in fatherClassInfo)
                {
                    string[] otherClass = ((string)classInfo.other_class).Trim('/').Split('/');
                    foreach (string oc in otherClass)
                    {
                        if (fatherClassInfoDic.ContainsKey(oc))
                        {
                            continue;
                        }

                        fatherClassInfoDic[oc] = await CDbDealer.Get1RecordFromSQLAsync($"SELECT * FROM info_item_class WHERE company_id = {fatherCompanyId} AND class_id ={oc}", cmd); ;

                        dynamic sameClassExist = await AddClassCheck(new
                        {
                            fatherItemId = fatherItemId,
                            fatherCompanyId = fatherCompanyId,
                            fatherClassId = oc,
                            // sonItemId = sonItemId,
                            sonCompanyId = sonCompanyId
                        }, cmd);

                        if ((bool)sameClassExist.result)
                        {
                            fatherToSonClass.Add(oc, (string)sameClassExist.sonClassId);
                            continue;
                        }
                        
                        dynamic addItemRet = await AddClassSync(new
                        {
                            fatherClassInfo = fatherClassInfoDic[oc],
                            sonCompanyId = sonCompanyId,
                            

                        }, cmd, tran);
                        if (addItemRet.result != "OK")
                        {
                            if (tran != null && selfTran)
                            {

                                tran.Rollback();

                            }
                            return new { result = "Error", msg = "分销商品牌商品档案同步失败1" };
                        }
                        fatherToSonClass.Add(oc, (string)addItemRet.classId);
                    }
                }
                
                foreach (string fatherClassId in fatherClassInfoDic.Keys)
                {
                    if (fatherClassId == (string)fatherMotherClass.class_id) continue;
                    dynamic addItemRet = await UpdateClassInfo(new
                    {
                        fatherToSonClass = fatherToSonClass,
                        sonClassId = fatherToSonClass[fatherClassId],
                        sonCompanyId = sonCompanyId,
                        fatherCompanyId = fatherCompanyId,
                        motherId = fatherClassInfoDic[fatherClassId].mother_id,
                        fatherClassId = fatherClassId,

                    }, cmd, tran);
                    if (addItemRet.result != "OK")
                    {
                        if (tran != null && selfTran)
                        {

                            tran.Rollback();

                        }
                        return new { result = "Error", msg = "分销商品牌商品档案同步失败2" };
                    }
                }
                if (tran != null && selfTran)
                {

                    tran.Commit();

                }
                return new { result = "OK", msg = "" };
            }
            catch (Exception e)
            {
                
                Console.WriteLine(e);
                // throw new Exception(e.Message);
                if (tran != null && selfTran)
                {

                    tran.Rollback();

                }
                string logErrMsg = $"In ReSellerEdit Save,msg:{e.Message},code:{e.StackTrace},targetSite:{e.TargetSite}";
                NLogger.Error(e.ToString());
                // MyLogger.LogMsg(logErrMsg);
                return new { result = "Error", msg = "商品类别同步失败" };
            }
        }
        public static async Task<dynamic> ClientItemInfoSyncService(dynamic data, CMySbCommand cmd, CMySbTransaction otherTran = null)
        {
            // 反向绑定
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID, out string operID);
            bool selfTran = true;
            CMySbTransaction tran = null;
            if (otherTran != null)
            {
                tran = otherTran;
                selfTran = false;
            }
            else
            {
                tran = await cmd.Connection.BeginTransactionAsync();
            }
            try
            {
                string fatherCompanyId = data.fatherCompanyId;
                string sonCompanyId = data.sonCompanyId;
                string planID = data.planID;
                dynamic brandsName = await CDbDealer.Get1RecordFromSQLAsync($"SELECT brand_name FROM rs_plan where plan_id = {planID}", cmd);
                string[] brandsNameList = ((string)brandsName.brand_name).Split(",");


                string brandSyncSqlHeader = $"insert into info_item_brand (company_id, brand_name, remark,brand_status, brand_order_index) ";


                Dictionary<string, string> brandsPair = new Dictionary<string, string>();

                // 品牌同步
                for (int i = 0; i < brandsNameList.Length; i++)
                {
                    string brandInfoSql = $"SELECT brand_id FROM info_item_brand where company_id={sonCompanyId} and(brand_name = '{brandsNameList[i]}')";
                    string brandSyncSql = $"";
                    string brandUpdateSql = $"";

                    dynamic sonBrandInfo = await CDbDealer.Get1RecordFromSQLAsync(brandInfoSql, cmd);
                    if (sonBrandInfo == null)
                    {
                        return new { result = "Error", msg = $"分销商商品档案中不存在品牌\"{brandsNameList[i]}\"" };
                    }
                    // 向父公司商品档案插入品牌，但在父公司可以指定分销方案的情况下意味着父公司已经有同名品牌档案，只需要更新rs_mum_id
                    /*
                    var old_brand_id = sonBrandInfo.brand_id;
                    var brand_name = sonBrandInfo.brand_name;
                    var remark = sonBrandInfo.remark == "" ? null : sonBrandInfo.remark;
                    var brand_status = sonBrandInfo.brand_status == "" ? "null" : sonBrandInfo.brand_status;
                    var brand_order_index = sonBrandInfo.brand_order_index == "" ? "null" : sonBrandInfo.brand_order_index;
                    brandSyncSql = brandSyncSqlHeader + $"values ({fatherCompanyId},'{brand_name}','{remark}',{brand_status},{brand_order_index},{old_brand_id}) returning brand_id ";
                    brandsRet = await CDbDealer.GetRecordsFromSQLAsync(brandSyncSql, cmd);
                    */
                    brandInfoSql = $"SELECT brand_id FROM info_item_brand where company_id={fatherCompanyId} and(brand_name = '{brandsNameList[i]}')";
                    dynamic fatherBrandInfo = await CDbDealer.Get1RecordFromSQLAsync(brandInfoSql, cmd);
                    brandsPair[sonBrandInfo.brand_id] = fatherBrandInfo.brand_id;
                    brandUpdateSql = $"UPDATE info_item_brand SET rs_mum_id = {fatherBrandInfo.brand_id} WHERE brand_id = {sonBrandInfo.brand_id} ";
                    cmd.CommandText = brandUpdateSql;
                    await cmd.ExecuteNonQueryAsync();
                }



                // 商品档案同步
                // 统一创建一个供应商类别 TODO
                dynamic sonCompanyName = await CDbDealer.Get1RecordFromSQLAsync($"SELECT company_name FROM g_company WHERE company_id = {sonCompanyId}", cmd);
                dynamic retClass = await CDbDealer.Get1RecordFromSQLAsync($"SELECT class_id,mother_id FROM info_item_class WHERE company_id = {fatherCompanyId} AND class_name LIKE '%{sonCompanyName.company_name}%';", cmd);
                string otherClass = "";
                // TODO 类别创建逻辑
                // NOTICE 可能存在问题 
                if (retClass == null)
                {
                    dynamic motherClass = await CDbDealer.Get1RecordFromSQLAsync($"SELECT class_id FROM info_item_class WHERE company_id = {fatherCompanyId} AND mother_id = 0", cmd);
                    string allTypeID = motherClass.class_id;
                    retClass = await CDbDealer.Get1RecordFromSQLAsync($"insert into info_item_class (company_id,class_name,mother_id) values({fatherCompanyId},'{sonCompanyName.company_name}同步商品',{allTypeID}) returning class_id", cmd);
                    otherClass = $"/{allTypeID}/{retClass.class_id}/";
                }
                else
                {
                    otherClass = $"/{retClass.mother_id}/{retClass.class_id}/";

                }

                // 查分销方案里相关品牌下商品所有的商品档案
                string brandsSqlCondition = string.Join(" or item_brand = ", brandsPair.Keys);
                // string itemsInfoSql = $"SELECT item_id, item_name, item_class, other_class, item_spec, item_brand FROM info_item_prop where company_id={companyID} and (item_brand = {brandsSqlCondition})";
                string itemsInfoSql = $"SELECT * FROM info_item_prop where company_id={sonCompanyId} and (item_brand = {brandsSqlCondition})";
                dynamic itemsInfo = await CDbDealer.GetRecordsFromSQLAsync(itemsInfoSql, cmd);
                // string itemsSyncSqlHeader = $"insert into info_item_prop (item_name, item_class, other_class, item_spec,item_brand, company_id, rs_mum_id) ";

                // string itemsSyncSqlHeader = $"insert into info_item_prop (item_name, item_class, other_class, item_spec,item_brand, company_id, rs_mum_id) ";
                // 选择全部的表头语句
                // string itemInfoPropHeaderSql = $"SELECT string_agg( column_name,',') all_column_name FROM information_schema.columns WHERE table_name = 'info_item_prop'";
                // dynamic itemInfoPropHeader = await CDbDealer.Get1RecordFromSQLAsync(itemInfoPropHeaderSql, cmd);
                // string itemInfoHeaderValue = ((string)itemInfoPropHeader.all_column_name).Replace("item_id,", "");

                string itemInfoHeaderValue = @"company_id, item_name, item_no, barcode, item_spec, simple_name, item_class, item_brand, unit_no, py_str, py_str1,
retail_price, status, item_images, item_alias, rs_mum_id ";
                string[] itemHeaderList = itemInfoHeaderValue.Replace(" ", "").Split(",").Select(s => s.Trim()).ToArray(); 
                string itemsSyncSqlHeader = $"insert into info_item_prop ({itemInfoHeaderValue}) ";

                // 查询所有表头
                // string unitsInfoPropHeaderSql = $"SELECT string_agg( column_name,',') all_column_name FROM information_schema.columns WHERE table_name = 'info_item_multi_unit'";
                // dynamic unitsInfoPropHeader = await CDbDealer.Get1RecordFromSQLAsync(unitsInfoPropHeaderSql, cmd);
                // string unitsInfoHeaderValue = ((string)unitsInfoPropHeader.all_column_name);

                string unitsInfoHeaderValue = "item_id,company_id,unit_no,unit_factor,retail_price,barcode,unit_type";
                dynamic priceOptions = await CDbDealer.Get1RecordFromSQLAsync($"SELECT price_sync_options FROM rs_plan where plan_id = {planID}", cmd);
                if (!string.IsNullOrEmpty((string)priceOptions.price_sync_options))
                {
                    unitsInfoHeaderValue = unitsInfoHeaderValue + $",{priceOptions.price_sync_options}";
                }
                string[] unitsHeaderList = unitsInfoHeaderValue.Replace(" ", "").Split(",").Select(s => s.Trim()).ToArray();
                string unitsSyncSqlHeader = $"insert into info_item_multi_unit ({unitsInfoHeaderValue}) ";

                foreach (ExpandoObject item in itemsInfo)
                {
                    string itemsSyncSqlBody = "values ";

                    var itemProperties = (IDictionary<string, object>)item;
                    string value = "(";

                    foreach (string header in itemHeaderList)
                    {
                        if (header == "company_id")
                        {
                            value += $"{fatherCompanyId}";
                        }
                        else if (header == "item_brand")
                        {
                            value += brandsPair[(string)itemProperties["item_brand"]];
                        }
                        /*else if (header == "item_class")
                        {
                            value += $"{retClass.class_id}";
                        }*/
                        /*else if (header == "buy_price")
                        {
                            value += (string)itemProperties["wholesale_price"] == "" ? "null" : $"'{((string)itemProperties["wholesale_price"]).Trim()}'";
                        }*/
                       /* else if (header == "other_class")
                        {
                            value += $"'{otherClass}'";
                        }*/
                        else
                        {
                            value += (string)itemProperties[header] == "" ? "null" : $"'{((string)itemProperties[header]).Trim()}'";
                        }
                        value += ",";
                    }
                    value = value.Trim(',');
                    value = value + ")";
                    itemsSyncSqlBody += value;
                    string itemSyncSql = itemsSyncSqlHeader + itemsSyncSqlBody + " returning item_id";
                    dynamic itemsRet = await CDbDealer.Get1RecordFromSQLAsync(itemSyncSql, cmd);
                    cmd.CommandText = $"UPDATE info_item_prop SET rs_mum_id = {itemsRet.item_id} where company_id={sonCompanyId} and item_id = {(string)itemProperties["item_id"]}";
                    // 同步填写rs_mum_id
                    await cmd.ExecuteNonQueryAsync();


                    // 单位同步
                    string unitsInfoSql = $"SELECT * FROM info_item_multi_unit where company_id={sonCompanyId} and item_id = {(string)itemProperties["item_id"]}";
                    dynamic unitsInfo = await CDbDealer.GetRecordsFromSQLAsync(unitsInfoSql, cmd);
                    string unitsSyncSqlBody = "values ";

                    // 一件商品可能多个单位，大中小
                    foreach (ExpandoObject unit in unitsInfo)
                    {
                        var unitProperties = (IDictionary<string, object>)unit;
                        string unitSqlValues = "(";

                        foreach (string header in unitsHeaderList)
                        {
                            if (header == "company_id")
                            {
                                unitSqlValues += $"{fatherCompanyId}";
                            }
                            else if (header == "item_id")
                            {
                                unitSqlValues += itemsRet.item_id;
                            }
                            else if (header == "buy_price")
                            {
                                unitSqlValues += (string)unitProperties["wholesale_price"] == "" ? "null" : $"'{((string)unitProperties["wholesale_price"]).Trim()}'";
                            }
                            else
                            {
                                unitSqlValues += (string)unitProperties[header] == "" ? "null" : $"'{((string)unitProperties[header]).Trim()}'";
                            }
                            unitSqlValues += ",";
                        }
                        unitSqlValues = unitSqlValues.Trim(',');
                        unitSqlValues = unitSqlValues + "),";
                        unitsSyncSqlBody += unitSqlValues;
                    }
                    unitsSyncSqlBody = unitsSyncSqlBody.Trim(',');
                    // string unitsSyncSql = unitsSyncSqlHeader + unitsSyncSqlBody;

                    // await CDbDealer.GetRecordsFromSQLAsync(unitsSyncSql, cmd);
                    cmd.CommandText = unitsSyncSqlHeader + unitsSyncSqlBody;
                    // 
                    // await cmd.ExecuteReaderAsync();
                    await cmd.ExecuteNonQueryAsync();
                }



                if (tran != null &&  selfTran)
                {
                    tran.Commit();
                }

                return new { result = "OK", msg = "" };
            }
            catch (Exception e)
            {
                if (tran != null && selfTran)
                {
                    tran.Rollback();

                }
                string errMsg = $"In ReSellerEdit ClientItemInfoSyncService ,msg:{e.Message},code:{e.StackTrace},targetSite:{e.TargetSite}";
                NLogger.Error(errMsg);
                MyLogger.LogMsg(errMsg, companyID);
                Console.WriteLine(e);
                Console.WriteLine(e);
                // throw new Exception(e.Message);
                return new { result = "Error", msg = "商品同步失败" };
            }
        }

        public static async Task<dynamic> ClientToSellerSyncService(dynamic data, CMySbCommand cmd, CMySbTransaction otherTran = null)
        {
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID, out string operID);
            bool selfTran = true;
            CMySbTransaction tran = null;
            if (otherTran != null)
            {
                tran = otherTran;
                selfTran = false;
            }
            else
            {
                tran = await cmd.Connection.BeginTransactionAsync();
            }
            try
            {
                string sonCompanyId = data.sonCompanyId;
                string sonCompanyName = data.sonCompanyName;
                string fatherCompanyId = data.fatherCompanyId;
                string fatherCompanyName = data.fatherCompanyName;
                string operatorSql = $"select oper_id,oper_name,mobile,py_str from info_operator where company_id = {sonCompanyId} and is_seller";
                dynamic operatorInfo = await CDbDealer.GetRecordsFromSQLAsync(operatorSql, cmd);
                string clientInsertSqlHeader = @$"insert into info_supcust (company_id,sup_name,boss_name, mobile, supcust_flag, status, supcust_remark,py_str,rs_seller_id,region_id,other_region)  ";
                // 处理第一个插入的记录，即创建时父公司对应的第一个客户和第一个业务员
                dynamic resellerInfo = await CDbDealer.Get1RecordFromSQLAsync($"select client_id from rs_seller where company_id = {fatherCompanyId} and reseller_company_id = {sonCompanyId}", cmd);
                dynamic firstOperId = "";
                if (resellerInfo != null)
                {
                    // 选出子账号的第一个业务员（开户时自动创建的客户）
                    dynamic firstOperInfo = await CDbDealer.Get1RecordFromSQLAsync($"select oper_id from g_operator where company_id = {sonCompanyId} order by oper_id asc",cmd);
                    firstOperId = (string)firstOperInfo.oper_id;
                    cmd.CommandText = @$"UPDATE info_operator SET rs_client_id = {resellerInfo.client_id} WHERE oper_id  = {firstOperId} and company_id = {sonCompanyId};
UPDATE info_supcust SET rs_seller_id = {firstOperId} WHERE supcust_id  = {resellerInfo.client_id} and company_id = {fatherCompanyId};";
                    await cmd.ExecuteNonQueryAsync();
                }
                foreach (var op in operatorInfo)
                {
                    dynamic clientRet = await CDbDealer.Get1RecordFromSQLAsync($"select rs_client_id from info_operator where company_id = {sonCompanyId} and oper_id = {op.oper_id}", cmd);
                    if (((string)clientRet.rs_client_id).IsValid() || (string)op.oper_name == fatherCompanyName || op.oper_id == firstOperId) {
                        // 已为子公司创建业务员
                        continue;

                    }
                    string clientInsertSqlBody = $@"values ({fatherCompanyId},'{op.oper_name}({sonCompanyName})', '{sonCompanyName}','{op.mobile}','C','1','系统自动创建分销商客户档案--{op.oper_name}', '{op.py_str}',{op.oper_id}, 
                    (select region_id from info_region where company_id = {fatherCompanyId}
                    and mother_id = 0),  '/' || (select region_id from info_region where company_id = {fatherCompanyId}
                    and mother_id = 0) || '/' ) returning supcust_id; ";
                    string clientInsertSql = clientInsertSqlHeader + clientInsertSqlBody;
                    dynamic fatherClientInfo = await CDbDealer.Get1RecordFromSQLAsync(clientInsertSql, cmd);
                    string fatherClientId = fatherClientInfo.supcust_id;
                    // 业务员设置rs_client_id
                    cmd.CommandText = $"UPDATE info_operator SET rs_client_id = {fatherClientId} WHERE oper_id  = {op.oper_id};";
                    await cmd.ExecuteNonQueryAsync();
                }
                
                if (selfTran)
                {
                    tran.Commit();
                }

                return new { result = "OK", msg = "" };
            }
            catch (Exception e)
            {
                if (tran != null && selfTran)
                {
                    Console.WriteLine(e);
                    tran.Rollback();

                }

                Console.WriteLine(e);
                // throw new Exception(e.Message);
                return new { result = "Error", msg = "客户添加失败" };
            }
        }

        public static async Task<dynamic> AddBrandItemInfoSync(dynamic data, CMySbCommand cmd, CMySbTransaction otherTran = null)
        {
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID, out string operID);
            bool selfTran = true;
            CMySbTransaction tran = null;
            if (otherTran != null)
            {
                tran = otherTran;
                selfTran = false;
            }
            else
            {
                tran = await cmd.Connection.BeginTransactionAsync();
            }
            try
            {
                string fatherCompanyId = data.fatherCompanyId;
                string sonCompanyId = data.sonCompanyId;
                string fatherBrandId = data.fatherBrandId;
                string priceSyncOptions = data.priceSyncOptions;
                dynamic resellerRecord = await CDbDealer.Get1RecordFromSQLAsync($"select company_name,reseller_name from rs_seller where company_id = {fatherCompanyId} and reseller_company_id = {sonCompanyId}",cmd);
                dynamic fatherBrandInfo = await CDbDealer.Get1RecordFromSQLAsync($"select * from info_item_brand where company_id = {fatherCompanyId} and brand_id = {fatherBrandId}",cmd);

                dynamic sonBrandInfo = await CDbDealer.Get1RecordFromSQLAsync($"select brand_id from info_item_brand where rs_mum_id = {fatherBrandId} and company_id = {sonCompanyId}",cmd);

                string sonBrandId = "";
                if(sonBrandInfo == null) 
                {
                    string brandsSyncSql = $"insert into info_item_brand (company_id, brand_name, remark,brand_status, brand_order_index, rs_mum_id) ";

                    var brand_name = fatherBrandInfo.brand_name;
                    var remark = fatherBrandInfo.remark == "" ? null : fatherBrandInfo.remark;
                    var brand_status = fatherBrandInfo.brand_status == "" ? "null" : fatherBrandInfo.brand_status;
                    var brand_order_index = fatherBrandInfo.brand_order_index == "" ? "null" : fatherBrandInfo.brand_order_index;
                    brandsSyncSql = brandsSyncSql + $" values ({sonCompanyId},'{brand_name}','{remark}',{brand_status},{brand_order_index},{fatherBrandId}) returning brand_id";

                    dynamic sonBrandRet = await CDbDealer.Get1RecordFromSQLAsync(brandsSyncSql, cmd);
                    sonBrandId = sonBrandRet.brand_id;
                }
                else
                {
                    sonBrandId = sonBrandInfo.brand_id;
                }
                

                dynamic fatherItemInfo = await CDbDealer.GetRecordsFromSQLAsync($"select item_id from info_item_prop where company_id = {fatherCompanyId} and item_brand = {fatherBrandId}", cmd);

                foreach (var item in fatherItemInfo)
                {
                    dynamic addItemRet = await AddItemInfoSync(new
                    {
                        operKey = (string)data.operKey,
                        fatherCompanyName = resellerRecord.company_name,
                        fatherCompanyId = fatherCompanyId,
                        sonBrandId = sonBrandId,
                        fatherBrandId = fatherBrandId,
                        sonCompanyId = sonCompanyId,
                        sonCompanyName = resellerRecord.reseller_name,
                        itemId = item.item_id,
                        changeClass = false,
                        priceSyncOptions = priceSyncOptions,

                    }, cmd, tran);
                    if (addItemRet.result != "OK")
                    {
                        if (tran != null && selfTran)
                        {

                            tran.Rollback();

                        }
                        return new { result = "Error", msg = "分销商品牌商品档案同步失败3" };
                    }
                }
                if (tran != null && selfTran)
                {

                    tran.Commit();

                }
                return new { result = "OK", msg = "" };
            }
            catch (Exception e)
            {
                if (tran != null && selfTran)
                {

                    tran.Rollback();

                }

                Console.WriteLine(e);
                // throw new Exception(e.Message);
                string errMsg = $"In ReSellerEdit AddBrandItemInfoSync,msg:{e.Message},code:{e.StackTrace},targetSite:{e.TargetSite}";
                NLogger.Error(errMsg);
                //MyLogger.LogMsg(errMsg, company_id);
                return new { result = "Error", msg = "分销商品牌商品档案同步失败4" };
            }
            
        }

        public static async Task<dynamic> AddClassSync(dynamic data, CMySbCommand cmd, CMySbTransaction otherTran = null)
        {
            
            bool selfTran = true;
            CMySbTransaction tran = null;
            if (otherTran != null)
            {
                tran = otherTran;
                selfTran = false;
            }
            else
            {
                tran = await cmd.Connection.BeginTransactionAsync();
            }
            try
            {
                var fatherClassInfo = (IDictionary<string, object>)data.fatherClassInfo;
                string sonCompanyId = (string)data.sonCompanyId;

                // item_id,item_class ,class_name,other_class,mother_id,cls_status 
                string classInfoPropHeaderSql = $"SELECT string_agg( column_name,',') all_column_name FROM information_schema.columns WHERE table_name = 'info_item_class'";
                dynamic classInfoPropHeader = await CDbDealer.Get1RecordFromSQLAsync(classInfoPropHeaderSql, cmd);
                string classInfoHeaderValue = ((string)classInfoPropHeader.all_column_name).Replace("class_id,", ""); ;
                string[] classHeaderList = classInfoHeaderValue.Split(",");
                string value = "";
                
                foreach (string header in classHeaderList)
                {
                    if (header == "company_id")
                    {
                        value += sonCompanyId;
                    }
                    else
                    {
                        value += (string)fatherClassInfo[header] == "" ? $" null" : $"'{((string)fatherClassInfo[header]).Trim()}'";
                    }
                    
                    value += ",";
                }
                value = value.Trim(',');
                string classInsertSql =  $"insert into info_item_class ({classInfoHeaderValue}) values({value}) returning class_id";

                dynamic classRet = await CDbDealer.Get1RecordFromSQLAsync(classInsertSql, cmd);
                if (tran != null && selfTran)
                {

                    tran.Commit();

                }
                return new { result = "OK", msg = "",classId = classRet.class_id };
            }
            catch (Exception e)
            {
                if (tran != null && selfTran)
                {

                    tran.Rollback();

                }

                Console.WriteLine(e);
                // throw new Exception(e.Message);
                return new { result = "Error", msg = "商品类别同步失败" };
            }
        }

        public static async Task<dynamic> AddClassCheck(dynamic data, CMySbCommand cmd)
        {
            string fatherItemId = data.fatherItemId;
            string fatherCompanyId = data.fatherCompanyId;
            string fatherClassId = (string)data.fatherClassId;
            // string sonItemId = data.sonItemId;
            string sonCompanyId = data.sonCompanyId;
            dynamic fatherClass = null;
            string fatherOtherClass = "/";

            if (!fatherClassId.IsNullOrWhiteSpace())
            {
                string classId = fatherClassId;
                while (classId != "0")
                {
                    fatherClass = await CDbDealer.Get1RecordFromSQLAsync($"select class_id,class_name,mother_id from info_item_class where company_id = {fatherCompanyId} and class_id = {classId}", cmd);
                    fatherOtherClass = "/" + fatherClass.class_name + fatherOtherClass;
                    classId = fatherClass.mother_id;
                }

            }
            else
            {
                fatherClass = await CDbDealer.Get1RecordFromSQLAsync($"select other_class,item_class from info_item_prop where company_id = {fatherCompanyId} and item_id = {fatherItemId}", cmd);
                string[] fatherClassList = ((string)fatherClass.other_class).Trim('/').Split('/');

                foreach (string fclassId in fatherClassList)
                {
                    dynamic fatherClassName = await CDbDealer.Get1RecordFromSQLAsync($"select class_name from info_item_class where company_id = {fatherCompanyId} and class_id = {fclassId}", cmd);
                    fatherOtherClass += (string)fatherClassName.class_name;
                    fatherOtherClass += "/";
                }
            }

            dynamic sonAllClass = await CDbDealer.GetRecordsFromSQLAsync($"select class_id, mother_id ,class_name from info_item_class where company_id ={sonCompanyId} order by class_id", cmd);

            Dictionary<string, dynamic> sonClassDic = new Dictionary<string, dynamic>();
            Dictionary<string, dynamic> sonOtherClassInfo = new Dictionary<string, dynamic>();


            foreach (var c in sonAllClass)
            {
                if (sonOtherClassInfo.ContainsKey(fatherOtherClass)) { return new { result = true, sonClassId = sonOtherClassInfo[fatherOtherClass].classId, sonOtherClass = sonOtherClassInfo[fatherOtherClass].otherClass }; }

                string otherClass = "/";
                string otherClassNum = "/";
                sonClassDic[(string)c.class_id] = c;
                string motherId = (string)c.class_id;
                bool treePathEnd = false;
                while (!treePathEnd)
                {
                    if(!sonClassDic.ContainsKey(motherId)) { break;  }
                    otherClassNum = "/" + sonClassDic[motherId].class_id + otherClassNum;
                    otherClass = "/" + sonClassDic[motherId].class_name + otherClass;
                    motherId = (string)sonClassDic[motherId].mother_id;
                    if (motherId == "0")
                    {
                        treePathEnd = true;
                        dynamic sonClassVar = new { otherClass = otherClassNum, classId = c.class_id };
                        sonOtherClassInfo[otherClass] = sonClassVar;

                    }
                }

            }
            if (sonOtherClassInfo.ContainsKey(fatherOtherClass)) { return new { result = true, sonClassId = sonOtherClassInfo[fatherOtherClass].classId, sonOtherClass = sonOtherClassInfo[fatherOtherClass].otherClass }; }
            return new { result = false };

        }
        public static async Task<dynamic> UpdateClassInfo(dynamic data, CMySbCommand cmd, CMySbTransaction otherTran = null)
        {
            // 可能存在的类别层次变更调用
            
            bool selfTran = true;
            CMySbTransaction tran = null;
            if (otherTran != null)
            {
                tran = otherTran;
                selfTran = false;
            }
            else
            {
                tran = await cmd.Connection.BeginTransactionAsync();
            }
            try
            {
                var fatherToSonClass = (IDictionary<string, string>)data.fatherToSonClass;
                string sonClassId = (string)data.sonClassId;
                string fatherClassId = (string)data.fatherClassId;
                string fatherCompanyId = (string)data.fatherCompanyId;
                string sonCompanyId = (string)data.sonCompanyId;
                string motherId = (string)data.motherId;

                

                dynamic oldClassBrandInfo = await CDbDealer.Get1RecordFromSQLAsync($"select * from info_item_class where class_id = {fatherClassId} and company_id = {fatherCompanyId}", cmd);
                string oldBrandId = (string)oldClassBrandInfo.brand_id;
                string updateClassSql = $"Update info_item_class set mother_id = {fatherToSonClass[motherId]}  where class_id = {sonClassId}";
                if (!oldBrandId.IsNullOrWhiteSpace())
                {
                    dynamic brandInfo = await CDbDealer.Get1RecordFromSQLAsync($"select brand_id from info_item_brand where rs_mum_id = {oldClassBrandInfo.brand_id} and company_id = {sonCompanyId} ", cmd);
                    updateClassSql = $"Update info_item_class set mother_id = {fatherToSonClass[motherId]} ,brand_id = {oldBrandId} where class_id = {sonClassId}";
                }
                
                
                
                cmd.CommandText = updateClassSql;
                await cmd.ExecuteNonQueryAsync();

                dynamic otherClassInfo = await CDbDealer.Get1RecordFromSQLAsync($"select other_class from info_item_prop where item_class = {fatherClassId} and other_class IS NOT NULL and company_id = {fatherCompanyId}", cmd);
                string updateItemSql = $"Update info_item_prop set item_class = {sonClassId} where item_class ={fatherClassId} and company_id = {sonCompanyId}";
                if (otherClassInfo != null)
                {
                    string[] otherClass = ((string)otherClassInfo.other_class).Trim('/').Split('/');
                    for (int i = 0; i < otherClass.Length; i++)
                    {
                        otherClass[i] = fatherToSonClass[otherClass[i]];
                    }

                    string sonOtherClass = string.Join("/", otherClass);

                    dynamic toUpdate = await CDbDealer.GetRecordsFromSQLAsync($"select item_id from info_item_prop where item_class ={fatherClassId}  and company_id = {sonCompanyId}", cmd);
                    updateItemSql = $"Update info_item_prop set item_class = {sonClassId}, other_class = '/{sonOtherClass}/' where item_class ={fatherClassId} and company_id = {sonCompanyId}";
                }

                cmd.CommandText = updateItemSql; 
                await cmd.ExecuteNonQueryAsync();
                if (tran != null && selfTran)
                {

                    tran.Commit();

                }
                return new { result = "OK", msg = "" };
            }
            catch (Exception e)
            {
                if (tran != null && selfTran)
                {

                    tran.Rollback();

                }

                Console.WriteLine(e);
                // throw new Exception(e.Message);
                return new { result = "Error", msg = "商品类别同步失败" };
            }
        }
        public static async Task<dynamic> AddItemInfoSync(dynamic data, CMySbCommand cmd, CMySbTransaction otherTran = null)
        {
            bool selfTran = true;
            CMySbTransaction tran = null;
            if (otherTran != null)
            {
                tran = otherTran;
                selfTran = false;
            }
            else
            {
                tran = await cmd.Connection.BeginTransactionAsync();
            }
            try
            {
                string itemId = data.itemId;
                string fatherBrandId = data.fatherBrandId;
                string sonBrandId = data.sonBrandId;
                string fatherCompanyId = data.fatherCompanyId;
                string fatherCompanyName = data.fatherCompanyName;
                string sonCompanyId = data.sonCompanyId;
                string priceSyncOptions = data.priceSyncOptions;
                string sonCompanyName = data.sonCompanyName;

                dynamic planInfo = await CDbDealer.Get1RecordFromSQLAsync($"select plan_id from rs_seller where company_id = {fatherCompanyId} and reseller_company_id = {sonCompanyId}",cmd);
                // string itemsSyncSqlHeader = $"insert into info_item_prop (item_name, item_class, other_class, item_spec,item_brand, company_id, rs_mum_id) ";
                // 选择全部的表头语句
                // string itemInfoPropHeaderSql = $"SELECT string_agg( column_name,',') all_column_name FROM information_schema.columns WHERE table_name = 'info_item_prop'";
                // dynamic itemInfoPropHeader = await CDbDealer.Get1RecordFromSQLAsync(itemInfoPropHeaderSql, cmd);
                // string itemInfoHeaderValue = ((string)itemInfoPropHeader.all_column_name).Replace("item_id,", "");

                string itemInfoHeaderValue = @"company_id, item_name, item_no, barcode, item_spec, simple_name, item_class, item_brand, unit_no, py_str, py_str1,
retail_price, status, item_images, item_alias, rs_mum_id ";
                string[] itemHeaderList = itemInfoHeaderValue.Replace(" ","").Split(",").Select(s => s.Trim()).ToArray(); ;

                // 查询所有表头
                // string unitsInfoPropHeaderSql = $"SELECT string_agg( column_name,',') all_column_name FROM information_schema.columns WHERE table_name = 'info_item_multi_unit'";
                // dynamic unitsInfoPropHeader = await CDbDealer.Get1RecordFromSQLAsync(unitsInfoPropHeaderSql, cmd);
                // string unitsInfoHeaderValue = ((string)unitsInfoPropHeader.all_column_name);

                string unitsInfoHeaderValue = "item_id,company_id,unit_no,unit_factor,retail_price,barcode,unit_type";
                // dynamic priceOptions = await CDbDealer.Get1RecordFromSQLAsync($"SELECT price_sync_options FROM rs_plan where plan_id = {planInfo.plan_id}", cmd);
                if (!string.IsNullOrEmpty(priceSyncOptions))
                {
                    unitsInfoHeaderValue = unitsInfoHeaderValue + $",{priceSyncOptions}";
                }
                string[] unitsHeaderList = unitsInfoHeaderValue.Replace(" ", "").Split(",").Select(s => s.Trim()).ToArray(); ;

/*                // 修复rs_mum_id 抹除
                dynamic fatherItemName = await CDbDealer.Get1RecordFromSQLAsync($"select item_name from info_item_prop where company_id={fatherCompanyId} and item_id={itemId}", cmd);
                cmd.CommandText = $"update info_item_prop set rs_mum_id = {itemId} where item_name = '{fatherItemName.item_name}' and company_id = {sonCompanyId}";
                await cmd.ExecuteNonQueryAsync();
                //*/

                string itemInfoSql = $"select * from info_item_prop where rs_mum_id = {itemId} and company_id = {sonCompanyId}";

                dynamic itemInfo = await CDbDealer.Get1RecordFromSQLAsync(itemInfoSql, cmd);
                string sonItemId = "";
                
                dynamic retClass = await CDbDealer.Get1RecordFromSQLAsync($"SELECT class_id,mother_id FROM info_item_class WHERE company_id = {sonCompanyId} AND class_name LIKE '%{fatherCompanyName}%';", cmd);

                if(sonBrandId == "")
                {
                    dynamic sonBrandInfo = await CDbDealer.Get1RecordFromSQLAsync($"select brand_id  from info_item_brand where rs_mum_id = {fatherBrandId} and  company_id = {sonCompanyId}", cmd);
                    sonBrandId = sonBrandInfo.brand_id;
                }


                string otherClass = "";
                if (retClass == null)
                {
                    dynamic motherClass = await CDbDealer.Get1RecordFromSQLAsync($"SELECT class_id FROM info_item_class WHERE company_id = {sonCompanyId} AND mother_id = 0", cmd);
                    string allTypeID = motherClass.class_id;
                    retClass = await CDbDealer.Get1RecordFromSQLAsync($"insert into info_item_class (company_id,class_name,mother_id) values({sonCompanyId},'{fatherCompanyName}同步商品',{allTypeID}) returning class_id", cmd);
                    otherClass = $"/{allTypeID}/{retClass.class_id}/";
                }
                else
                {
                    otherClass = $"/{retClass.mother_id}/{retClass.class_id}/";

                }
                #region 更新已有的商品
                if (itemInfo != null)
                {
                    // 更新 info_item_prop info_multi_unit
                    dynamic fatherItemInfo = await CDbDealer.Get1RecordFromSQLAsync($"select * from info_item_prop where item_id = {itemId}", cmd);
                    var itemProperties = (IDictionary<string, object>)fatherItemInfo;
                    string updateSql = "update info_item_prop set ";
                    string value = "";
                    sonItemId = itemInfo.item_id;



                    foreach (string header in itemHeaderList)
                    {
                        if (header == "company_id" || header == "rs_mum_id" || header == "item_id" 
                            ||((header == "item_class" || header == "other_class") && !(bool)data.changeClass))
                            // header == "item_class" || header == "other_class"
                        {
                            continue;
                        }

                        else if (header == "item_brand")
                        {
                            value += $"{header} = {sonBrandId}";
                        }
                        else
                        {
                            value += (string)itemProperties[header] == "" ? $"{header} = null" : $"{header} = '{((string)itemProperties[header]).Trim()}'";
                        }
                        value += ",";
                    }
                    value = value.Trim(',');
                    updateSql = updateSql + value + $" where item_id = {itemInfo.item_id}";

                    cmd.CommandText = updateSql;
                    await cmd.ExecuteNonQueryAsync();

                    // 删除子商品单位信息重新插入
                    cmd.CommandText = $"DELETE FROM info_item_multi_unit where company_id ={sonCompanyId} and item_id = {itemInfo.item_id} ";
                    await cmd.ExecuteNonQueryAsync();


                }
                #endregion
                #region 插入新的商品
                else
                {
                    
                    itemInfoSql = $"select * from info_item_prop where item_id = {itemId} and company_id = {fatherCompanyId}";
                    itemInfo = await CDbDealer.Get1RecordFromSQLAsync(itemInfoSql, cmd);

                    // 检查子公司是否有同名商品
                    dynamic repeatItemInfo = await CDbDealer.Get1RecordFromSQLAsync($"select item_id from info_item_prop where company_id = {sonCompanyId} and item_name = '{itemInfo.item_name}'", cmd);
                    if (repeatItemInfo != null) return new { result = "ERROR", msg = $"分销商【{sonCompanyName}】商品档案里存在同名的非同步商品" };
                    // 插入

                    var itemProperties = (IDictionary<string, object>)itemInfo;
                    string itemsSyncSql = $"insert into info_item_prop ({itemInfoHeaderValue}) ";
                    string value = "values (";

                    foreach (string header in itemHeaderList)
                    {
                        if (header == "company_id")
                        {
                            value += $"{sonCompanyId}";
                        }
                        else if (header == "rs_mum_id")
                        {
                            value += itemProperties["item_id"];
                        }
                        else if (header == "item_brand")
                        {
                            value += sonBrandId;
                        }
                        /*else if (header == "item_class")
                        {
                            value += $"{retClass.class_id}";
                        }*/
                        /*else if (header == "buy_price")
                        {
                            value += (string)itemProperties["wholesale_price"] == "" ? "null" : $"'{((string)itemProperties["wholesale_price"]).Trim()}'";
                        }*/
                        /*else if (header == "other_class")
                        {
                            value += $"'{otherClass}'";
                        }*/
                        else
                        {
                            value += (string)itemProperties[header] == "" ? "null" : $"'{((string)itemProperties[header]).Trim()}'";
                        }
                        value += ",";
                    }
                    value = value.Trim(',');
                    value = value + ") returning item_id";
                    itemsSyncSql += value;

                    dynamic sonItemInfo = await CDbDealer.Get1RecordFromSQLAsync(itemsSyncSql, cmd);
                    sonItemId = sonItemInfo.item_id;

                }
                #endregion

                #region 查看是否需要插入和更新类别
                
                if ((bool)data.changeClass)
                {
                    // 查询有没有相同类
                    dynamic sameClassExist = await AddClassCheck(new
                    {
                        fatherItemId = itemId,
                        fatherCompanyId = fatherCompanyId,
                        sonItemId = sonItemId,
                        sonCompanyId = sonCompanyId,
                        fatherClassId = ""
                    }, cmd);

                    if (!(bool)sameClassExist.result)
                    {
                        dynamic classInfoSyncResult = await ClassSyncService(new
                        {
                            fatherItemId = itemId,
                            fatherCompanyId = fatherCompanyId,
                            sonCompanyId = sonCompanyId,
                            planId = "",
                            brandId = "",
                            reverseBind = false,
                        }, cmd, tran);
                        if (classInfoSyncResult.result != "OK")
                        {
                            return new { result = "Error", msg = "分销商商品档类别同步失败" };
                        }
                    }
                    else
                    {
                        // 存在相同类
                        string sonClassId = (string)sameClassExist.sonClassId;
                        string sonOtherClass = (string)sameClassExist.sonOtherClass;
                        dynamic sonClassInfo = await CDbDealer.Get1RecordFromSQLAsync($"select * from info_item_class iic left join info_item_prop iip on item_class = class_id where iic.company_id = {sonCompanyId} and iic.company_id = iip.company_id and class_id = {sonClassId}",cmd);
                        string updateClassInfoSql = $"update info_item_prop set item_class = {sonClassId},other_class = '{sonOtherClass}' where item_id = {sonItemId} and company_id = {sonCompanyId}";
                        cmd.CommandText = updateClassInfoSql;
                        await cmd.ExecuteNonQueryAsync();
                        
                    }
                    
                }
                
                #endregion
                #region 插入单位信息
                string unitsInfoSql = $"SELECT * FROM info_item_multi_unit where company_id={fatherCompanyId} and item_id = {itemId}";
                dynamic unitsInfo = await CDbDealer.GetRecordsFromSQLAsync(unitsInfoSql, cmd);



                foreach (ExpandoObject unit in unitsInfo)
                {
                    string unitsSyncSql = $"insert into info_item_multi_unit ({unitsInfoHeaderValue}) values ";
                    var unitProperties = (IDictionary<string, object>)unit;
                    string value = "(";


                    foreach (string header in unitsHeaderList)
                    {
                        if (header == "company_id")
                        {
                            value += $"{sonCompanyId}";
                        }
                        else if (header == "item_id")
                        {
                            value += sonItemId;
                        }
                        /*else if (header == "buy_price")
                        {
                            value += (string)unitProperties["wholesale_price"] == "" ? "null" : $"'{((string)unitProperties["wholesale_price"]).Trim()}'";
                        }*/
                        else
                        {
                            value += (string)unitProperties[header] == "" ? "null" : $"'{((string)unitProperties[header]).Trim()}'";
                        }
                        value += ",";
                    }
                    value = value.Trim(',');
                    value = value + ")";
                    unitsSyncSql += value;

                    cmd.CommandText = unitsSyncSql;
                    await cmd.ExecuteNonQueryAsync();
                    
                }
                #endregion
                if (tran != null && selfTran)
                {

                    tran.Commit();

                }
                return new { result = "OK", msg = "" };
            }
            catch (Exception e)
            {
                if (tran != null && selfTran)
                {

                    tran.Rollback();

                }

                Console.WriteLine(e);
                // throw new Exception(e.Message);
                //return new { result = "Error", msg = $"分销商【{data.sonCompanyName}】商品档案同步失败" };
                return new { result = "Error", msg = $"分销商商品档案同步失败5" };
            }
        }

        public static async Task<dynamic> EditItemInfoSync (dynamic data, CMySbCommand cmd, CMySbTransaction otherTran = null)
        {
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID, out string operID);
            bool selfTran = true;
            CMySbTransaction tran = null;
            if (otherTran != null)
            {
                tran = otherTran;
                selfTran = false;
            }
            else
            {
                tran = await cmd.Connection.BeginTransactionAsync();
            }
            try
            {
                string itemId = data.itemId;
                string brandId = data.brandId;
                string fatherCompanyId = data.companyId;

                string sonCompanyIdSql = @$"SELECT  reseller_company_id,reseller_name,company_name,price_sync_options
                                            FROM rs_seller rs
                                            LEFT JOIN rs_plan rp ON rs.company_id = rp.company_id 
                                            WHERE
                                            	rp.company_id = {fatherCompanyId}
                                            	and rs.plan_id = rp.plan_id
                                            	and ',' || rp.brand_id || ',' LIKE '%,{brandId},%'";

                dynamic rsCompanyInfos = await CDbDealer.GetRecordsFromSQLAsync(sonCompanyIdSql, cmd);

                if (rsCompanyInfos.Count == 0) { return new { result = "OK", msg = "" }; }


                foreach (var sonCompanyInfo in rsCompanyInfos)
                {
                    string fatherCompanyName = sonCompanyInfo.company_name;
                    string sonCompanyId = sonCompanyInfo.reseller_company_id;
                    string sonCompanyName = sonCompanyInfo.reseller_name;

                    dynamic addItemRet = await AddItemInfoSync(new
                    {
                        
                        fatherCompanyName = sonCompanyInfo.company_name,
                        fatherCompanyId = fatherCompanyId,
                        sonBrandId = "",
                        fatherBrandId = brandId,
                        sonCompanyId = sonCompanyInfo.reseller_company_id,
                        sonCompanyName = sonCompanyName,
                        itemId = itemId,
                        changeClass = data.changeClass,
                        priceSyncOptions = sonCompanyInfo.price_sync_options,

                    }, cmd, tran);
                    if (addItemRet.result != "OK")
                    {
                        if (tran != null && selfTran)
                        {

                            tran.Rollback();

                        }
                        
                        return new { result = "Error", msg = addItemRet.msg };
                    }

                }
                if (tran != null && selfTran)
                {

                    tran.Commit();

                }
                return new { result = "OK", msg = "" };
            }
            catch (Exception e)
            {
                if (tran != null && selfTran)
                {

                    tran.Rollback();

                }

                Console.WriteLine(e);
                // throw new Exception(e.Message);
                return new { result = "Error", msg = "分销商商品档案同步失败6" };
            }
        }

        public static async Task<dynamic> BatchEditItemInfoSync(dynamic data, CMySbCommand cmd, CMySbTransaction otherTran = null)
        {
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID, out string operID);
            bool selfTran = true;
            CMySbTransaction tran = null;
            if (otherTran != null)
            {
                tran = otherTran;
                selfTran = false;
            }
            else
            {
                tran = await cmd.Connection.BeginTransactionAsync();
            }
            try
            {
                string err = "";
                string itemIdList = data.itemIdList;
                string editType = data.editType;
               
                // 筛选需要同步的商品
                
                string validItemSql = @$"SELECT STRING_AGG(iip.item_id::TEXT, ',') AS item_ids
FROM info_item_prop AS iip
WHERE iip.company_id = {companyID}
AND iip.item_id IN ({itemIdList})
AND EXISTS (
    SELECT 1
    FROM rs_plan
    WHERE company_id = {companyID}
    AND iip.item_brand::text = ANY(string_to_array(rs_plan.brand_id, ','))
);
";
                dynamic validRet = await CDbDealer.Get1RecordFromSQLAsync(validItemSql, cmd);
                string updateSql = "";
                
                string rsCompanyIdSql = $"select STRING_AGG(reseller_company_id::TEXT, ',' ) as ids from rs_seller where company_id = {companyID}";
                
                dynamic rsIds = await CDbDealer.Get1RecordFromSQLAsync(rsCompanyIdSql, cmd);
                string validItemList = (string)validRet.item_ids;

                if (string.IsNullOrEmpty(validItemList)) { return new { result = "OK", msg = "没有要同步的分销商商品" } ; }
               
                if(editType == "status")
                {
                    // 不同编辑内容传不同参数
                    
                    string status = data.status;
                    updateSql = $"update info_item_prop SET status = '{status}' where rs_mum_id in ({validItemList}) and company_id in ({rsIds.ids})";
                    cmd.CommandText = updateSql ;
                    await cmd.ExecuteNonQueryAsync();
                }else 
                {
                    // 不编辑status的情况下直接调用轮子
                    string[] idList = validItemList.Split(',');
                    
                    foreach(string id in idList)
                    {
                        dynamic brandInfo = await CDbDealer.Get1RecordFromSQLAsync($"select item_brand from info_item_prop where company_id ={companyID} and item_id={id}",cmd);
                        dynamic itemInfoSyncResult = await EditItemInfoSync(new
                        {
                            itemId = id,
                            operKey = (string)data.operKey,
                            brandId = brandInfo.item_brand,
                            companyId = companyID,
                            changeClass = true

                        }, cmd, tran);
                        if (itemInfoSyncResult.result != "OK")
                        {
                            return new { result = "Error", msg = itemInfoSyncResult.msg, added = false, record = new { } };
                        }
                    }
                }

               
                if (tran != null && selfTran)
                {

                    tran.Commit();

                }
                return new { result = "OK", msg = "" };
            }
            catch (Exception e)
            {
                if (tran != null && selfTran)
                {

                    tran.Rollback();

                }

                Console.WriteLine(e);
                // throw new Exception(e.Message);
                return new { result = "Error", msg = "分销商商品档案批量同步失败" };
            }
        }
        public static async Task<string> DeleteResellerItemInfoCheck(string company_id, string item_id, CMySbCommand cmd)
        {
             
             
            dynamic rsItemId = await CDbDealer.GetRecordsFromSQLAsync($"select item_id from info_item_prop where rs_mum_id = {item_id}", cmd);
            if (rsItemId.Count == 0)
            {
                return "OK";
            }
            foreach (var itemId in rsItemId)
             {
                string id = itemId.item_id;
                cmd.CommandText = @$"select item_id::text from sheet_sale_detail where item_id = '{id}'  and company_id={company_id}  
                union all
                select item_id::text from sheet_sale_order_detail where item_id = '{id}'   and company_id = {company_id}
                    union all
               select item_id::text from sheet_order_item_detail where item_id = '{id}'   and company_id = {company_id}
                    union all
               select item_id::text from sheet_item_ordered_adjust_detail where item_id = '{id}'   and company_id = {company_id}
                     union all
               select item_id::text from sheet_buy_detail where item_id = '{id}'   and company_id = {company_id}
                    union all
              select item_id::text from sheet_move_detail where item_id = '{id}'   and company_id = {company_id}
                    union all
               select item_id::text from sheet_inventory_detail where item_id = '{id}'   and company_id = {company_id}
                    limit 1;";
                var ov = await cmd.ExecuteScalarAsync();
                if (ov != null && ov != DBNull.Value) return "商品已使用,无法删除";
             }
            return "OK";
        }
    }
}
