@page
@model ArtisanManage.Pages.Report.BorrowMigrationModel
@{
    ViewData["Title"] = "借货数据迁移助手";
}

<script type="text/javascript">
    var itemSource = {};
    $(document).ready(function () {
        @Html.Raw(Model.m_showFormScript)
        @Html.Raw(Model.m_createGridScript)
        
        // 加载迁移统计信息
        loadMigrationStats();
        
        // 绑定按钮事件
        $("#btnExecuteMigration").on('click', executeMigration);
        $("#btnExportData").on('click', exportData);
        $("#btnRefreshStats").on('click', loadMigrationStats);
    });

    // 加载迁移统计信息
    function loadMigrationStats() {
        $.get('/api/BorrowMigration/GetMigrationStats', { operKey: '@Model.OperKey' })
            .done(function(data) {
                if (data) {
                    $('#totalCustomers').text(data.total_customers || 0);
                    $('#totalQty').text(data.total_qty || 0);
                    $('#totalItems').text(data.total_items || 0);
                    $('#estimatedAmount').text((data.estimated_total_amount || 0).toFixed(2));
                }
            })
            .fail(function() {
                bw.toast('获取统计信息失败', 3000);
            });
    }

    // 执行数据迁移
    function executeMigration() {
        var migrationType = $('#migration_type').val();
        var startDate = $('#start_date').val();
        var minAmount = parseFloat($('#min_amount').val()) || 0;
        
        if (!migrationType || !migrationType.value) {
            bw.toast('请选择迁移方式', 3000);
            return;
        }
        
        if (confirm('确定要执行数据迁移吗？此操作将影响借货余额数据，建议先备份数据库。')) {
            $.post('/api/BorrowMigration/ExecuteMigration', 
                JSON.stringify({
                    migrationType: migrationType.value,
                    startDate: startDate,
                    minAmount: minAmount
                }),
                function(result) {
                    if (result.result === 'OK') {
                        bw.toast(result.message, 3000);
                        // 刷新表格和统计信息
                        $("#gridItems").jqxGrid('updatebounddata');
                        loadMigrationStats();
                    } else {
                        bw.toast(result.message, 5000);
                    }
                },
                'json'
            ).fail(function() {
                bw.toast('迁移执行失败', 3000);
            });
        }
    }

    // 导出数据
    function exportData() {
        $("#gridItems").jqxGrid('exportdata', 'xlsx', '借货数据迁移清单');
    }
</script>

<div class="container-fluid">
    <!-- 功能说明 -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">📋 功能说明</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><strong>借货数据迁移助手</strong></h6>
                        <p>此工具用于将原有的按商品数量借货数据迁移到新的按金额借货系统。由于原系统只记录借货数量，没有价格信息，提供以下迁移方案：</p>
                        <ul class="mb-0">
                            <li><strong>使用当前价格估算</strong>：使用商品当前零售价估算借货金额（可能不准确）</li>
                            <li><strong>从历史单据计算</strong>：从历史借还货单据中计算实际金额（推荐）</li>
                            <li><strong>手工设置</strong>：导出数据后手工设置准确金额（最安全）</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">📊 当前借货数据统计</h5>
                    <button type="button" class="btn btn-sm btn-outline-primary" id="btnRefreshStats">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary" id="totalCustomers">-</h4>
                                <small class="text-muted">借货客户数</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-success" id="totalQty">-</h4>
                                <small class="text-muted">借货总数量</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-info" id="totalItems">-</h4>
                                <small class="text-muted">涉及商品种类</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-warning" id="estimatedAmount">-</h4>
                                <small class="text-muted">估算总金额(元)</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 查询条件 -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">🔍 迁移参数设置</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div id="migration_type"></div>
                        </div>
                        <div class="col-md-3">
                            <div id="start_date"></div>
                        </div>
                        <div class="col-md-2">
                            <div id="min_amount"></div>
                        </div>
                        <div class="col-md-3">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-primary" id="btnExecuteMigration">
                                    <i class="fas fa-play"></i> 执行迁移
                                </button>
                                <button type="button" class="btn btn-success" id="btnExportData">
                                    <i class="fas fa-download"></i> 导出数据
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">📋 待迁移数据列表</h5>
                </div>
                <div class="card-body">
                    <div id="gridItems"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作说明 -->
    <div class="row mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">⚠️ 重要提醒</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h6><strong>迁移前请注意：</strong></h6>
                        <ol class="mb-0">
                            <li><strong>备份数据库</strong>：执行迁移前请务必备份数据库</li>
                            <li><strong>测试环境验证</strong>：建议先在测试环境执行迁移并验证结果</li>
                            <li><strong>业务确认</strong>：重要客户的借货金额建议与业务人员确认</li>
                            <li><strong>分批处理</strong>：可以通过调整最小金额参数分批处理</li>
                            <li><strong>数据验证</strong>：迁移完成后请使用借货余额报表验证数据正确性</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
