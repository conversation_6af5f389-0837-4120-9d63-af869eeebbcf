# 独立借货单按金额借还货功能说明

## 功能概述

本功能为**独立借货单**添加按金额借还货支持，实现**借A单还B单**的跨单据借还货场景。

### 重要说明

- ✅ **支持**：独立借货单（使用 `borrow_item_main` 表）
- ❌ **不支持**：销售单中的借货（使用 `sheet_sale_main` 表，保持原有逻辑）

## 核心特性

1. **双模式支持**：
   - 按数量借还货（原有模式）
   - 按金额借还货（新增模式）

2. **按借货单维度管理**：
   - 每个独立借货单独立管理余额
   - 支持借A单，还B单的灵活场景
   - 精确跟踪每笔借货的还货情况

3. **完整的业务流程**：
   - 借货时创建借货单余额记录
   - 还货时可选择对应的借货单
   - 完整的历史记录和余额查询

## 数据库设计

### 新增表结构

#### 1. 借货单余额表 (borrow_sheet_balance)
```sql
CREATE TABLE borrow_sheet_balance (
    company_id integer NOT NULL,
    borrow_sheet_id integer NOT NULL,        -- 借货单ID（来自borrow_item_main）
    supcust_id integer NOT NULL,             -- 客户ID
    borrow_mode varchar(10) NOT NULL,        -- 借货模式：QTY=按数量，AMT=按金额
    total_amount numeric(15,2) DEFAULT 0,    -- 借货总金额
    returned_amount numeric(15,2) DEFAULT 0, -- 已还金额
    balance_amount numeric(15,2) DEFAULT 0,  -- 剩余金额
    borrow_time timestamp NOT NULL,         -- 借货时间
    last_return_time timestamp,             -- 最后还货时间
    status varchar(20) DEFAULT 'ACTIVE',    -- 状态：ACTIVE=有效，CLOSED=已结清
    CONSTRAINT pk_borrow_sheet_balance PRIMARY KEY (company_id, borrow_sheet_id)
);
```

#### 2. 还货借货映射表 (return_borrow_mapping)
```sql
CREATE TABLE return_borrow_mapping (
    company_id integer NOT NULL,
    return_sheet_id integer NOT NULL,       -- 还货单ID
    return_detail_id integer NOT NULL,      -- 还货明细ID（独立还货单时为0）
    borrow_sheet_id integer NOT NULL,       -- 对应的借货单ID
    return_amount numeric(15,2) DEFAULT 0,  -- 本次还货金额
    return_time timestamp DEFAULT now(),
    CONSTRAINT pk_return_borrow_mapping PRIMARY KEY (company_id, return_sheet_id, return_detail_id, borrow_sheet_id)
);
```

### 字段扩展

#### 独立借货单表 (borrow_item_main)
- 新增 `borrow_mode` 字段：借货模式（QTY=按数量，AMT=按金额）
- 借货模式是单据级别的设置，明细表通过关联主表获取

## 业务逻辑

### 借货流程

1. **选择借货模式**：
   - 按数量：传统模式，按商品数量借货
   - 按金额：新模式，按商品金额借货

2. **借货处理**：
   - 按数量模式：继续使用 `borrowed_cust_items` 表
   - 按金额模式：创建 `borrow_sheet_balance` 记录

### 还货流程

1. **加载可还借货单**：
   - 查询客户的所有有余额的独立借货单
   - 显示借货单号、借货时间、剩余金额等信息

2. **选择目标借货单**：
   - 用户可以选择要还给哪个借货单
   - 支持一次还货对应多个借货单

3. **验证和处理**：
   - 验证还货金额不超过借货单余额
   - 更新借货单余额
   - 记录还货映射关系

### 跨单据借还货示例

```
场景：借A单，还B单

1. 借货单001：客户借了价值1000元的商品
   - 借货单001余额：1000元
   
2. 借货单002：客户又借了价值500元的商品
   - 借货单001余额：1000元
   - 借货单002余额：500元
   
3. 还货单003：客户还货600元，选择还给借货单001
   - 借货单001余额：1000 - 600 = 400元
   - 借货单002余额：500元（不变）
   
4. 还货单004：客户还货400元，选择还给借货单001
   - 借货单001余额：400 - 400 = 0元（已结清）
   - 借货单002余额：500元（不变）
```

## 前端界面

### 借货单页面
- 借货模式选择（按数量/按金额）
- 还货时显示可还借货单列表
- 智能验证和提示

### 新增报表
- **借货单余额查询**：按借货单查看余额情况
- 支持按客户、时间、状态等条件筛选
- **只读查询**：不允许手工修改余额，确保数据完整性

## 数据完整性保证

### 🔒 **借货余额的正确管理**

#### ✅ **应该通过业务流程维护**：
1. **借货时**：系统自动创建余额记录
2. **还货时**：系统自动减少余额
3. **红冲时**：系统自动调整余额
4. **审核流程**：确保数据准确性

#### ❌ **不允许手工修改**：
- 禁止随意修改余额数字
- 禁止绕过业务流程直接改数据
- 所有变更都有完整的审计记录

### 🔄 **数据一致性**
- 余额 = 借货总金额 - 已还金额
- 所有变更都通过 `return_borrow_mapping` 表可追溯
- 支持按历史单据重新计算验证

## 向后兼容

- 原有的 `borrowed_cust_items` 表完全保留
- 按数量借货的逻辑继续工作
- 销售单中的借货逻辑保持不变
- 新旧系统可以并行运行

## 技术实现

### 数据流程
1. **借货时**：
   - 按金额模式：创建 `borrow_sheet_balance` 记录
   - 按数量模式：继续使用 `borrowed_cust_items` 表

2. **还货时**：
   - 按金额模式：更新 `borrow_sheet_balance` 余额，创建 `return_borrow_mapping` 记录
   - 按数量模式：继续使用原有逻辑

3. **查询时**：
   - 从 `borrow_sheet_balance` 表获取借货单余额信息
   - 关联 `borrow_item_main` 表获取借货单基本信息

### 关键SQL查询
```sql
-- 查询客户可还的借货单
SELECT bsb.borrow_sheet_id, bm.sheet_no as borrow_sheet_no, 
       bsb.borrow_time, bsb.borrow_mode,
       bsb.total_amount, bsb.balance_amount, bsb.status
FROM borrow_sheet_balance bsb
INNER JOIN borrow_item_main bm ON bsb.company_id = bm.company_id 
                               AND bsb.borrow_sheet_id = bm.sheet_id
WHERE bsb.company_id = ? AND bsb.supcust_id = ? 
  AND bsb.status = 'ACTIVE' 
  AND bsb.balance_amount > 0
ORDER BY bsb.borrow_time DESC;
```

## 使用场景

适用于需要灵活借还货管理的业务场景：
- 代销业务
- 寄售业务  
- 临时借货
- 跨商品还货需求

通过按借货单维度管理，可以精确跟踪每笔借货的还货情况，支持复杂的借还货业务需求。
