﻿using System;

namespace ArtisanManage.YingJiangMallMini.Model;

public class MiniRedPacket
{
    
}


public class MiniRedPacketHistory
{
    public int flow_id { get; set; }
    public int company_id { get; set; }
    public int supcust_id { get; set; }
    public DateTime happen_time { get; set; }
    public decimal change_amount { get; set; }
    public string change_type { get; set; }
    public int? relate_sheet_id { get; set; }
    public string relate_sheet_type { get; set; }
    public int record_total { get; set; }
}

public class MallMiniRedPacketBaseInfo
{
    public decimal register_reward { get; set; }
    public string red_packet_id { get; set; }
    public decimal purchase_reward { get; set; }
    public string use_limit_type { get; set; }
    public decimal use_limit_amount { get; set; }
    public decimal balance { get; set; }
    public bool user_registed { get; set; }
}
public class MallMiniRedPacketSend
{
    public string red_packet_id { get; set; }
    public string red_packet_amount { get; set; }
    public string restrict_time { get; set; }
    public string red_packet_name { get; set; }
    public int red_packet_max_num { get; set; }
    public int user_picked_num { get; set; }
    public decimal can_pick_amount { get; set; }
    public int can_pick_num { get; set; }
}