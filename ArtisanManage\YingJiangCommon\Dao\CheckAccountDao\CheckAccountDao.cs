﻿using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using ArtisanManage.YingJiangCommon.Model;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.CodeAnalysis.Elfie.Diagnostics;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace ArtisanManage.YingJiangCommon.Dao.CheckAccountDao
{
    public static class CheckAccountDao
    {
        public static async Task<dynamic> QueryCheckAccountBaseInfoDao(dynamic paramModel, CMySbCommand cmd)
        {
            string companyID = paramModel.companyID;
            SQLQueue QQ = new SQLQueue(cmd);
            List<ExpandoObject> operList = null;
            List<ExpandoObject> departList = null;
            List<ExpandoObject> payWayList = null;
            string sql = @$"";
            sql = $@"select oper_id ,oper_name ,depart_path from info_operator WHERE company_id = {companyID} and status <> 0";
            QQ.Enqueue("operList", sql);
            // sql = $@"select depart_id ,depart_name,mother_id from info_department  WHERE company_id = {companyID} ";
            sql = $@"
WITH RECURSIVE dept_paths AS (
  SELECT
    depart_id,
    mother_id,
    depart_name,
    '/' || depart_id || '/' AS path
  FROM
    info_department
  WHERE
    company_id = '{companyID}' AND mother_id = 0 -- 查找根节点部门
  UNION ALL
  SELECT
    info_department.depart_id,
    info_department.mother_id,
    info_department.depart_name,
    dept_paths.path || info_department.depart_id || '/'
  FROM
    info_department
    JOIN dept_paths ON info_department.mother_id = dept_paths.depart_id
  WHERE
    info_department.company_id = '{companyID}'
)
SELECT
  info_department.company_id,
  info_department.depart_id,
  info_department.depart_name,
  dept_paths.path
FROM
  info_department
  JOIN dept_paths ON info_department.depart_id = dept_paths.depart_id
WHERE
  info_department.company_id = '{companyID}';
";
            QQ.Enqueue("departList", sql);
            sql = @$"select sub_id, sub_type, sub_name from cw_subject where company_id = {companyID} and sub_type = 'QT'";
            QQ.Enqueue("payWayList", sql);
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "operList")
                {
                    operList = CDbDealer.GetRecordsFromDr(dr, false);
                } else if (sqlName == "departList")
                {
                    departList = CDbDealer.GetRecordsFromDr(dr, false);
                } else if (sqlName == "payWayList")
                {
                    payWayList = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            return new
            {
                operList,departList,payWayList
            };
        }
        public static async Task<dynamic> QueryCheckAccountPayWayInfoDao(dynamic paramModel, CMySbCommand cmd)
        {
            string companyID = paramModel.companyID;
            string sql = @$"select sub_id, sub_type, sub_name from cw_subject where company_id = {companyID} and sub_type = 'QT'";
            List<ExpandoObject> payWayList = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            return payWayList;
        }

        public static async Task<dynamic> QueryCompanySettingDao(dynamic paramModel, CMySbCommand cmd)
        {
            string companyID = paramModel.companyID;
            string sql = @$"select
    case when cs.setting ->> 'companyName' is not null then cs.setting ->> 'companyName' else gc.company_name end as company_name,
    cs.setting ->> 'checkAccountPayWayType' as check_account_pay_way_type,
    cs.setting ->> 'newCheckAccountShowLineAmountDetail' as new_check_account_show_line_amount_detail
from g_company gc
left join company_setting cs on gc.company_id = cs.company_id
where gc.company_id = {companyID}";
            dynamic result = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            return result;
        }

        public static async Task<JsonResult> MemoryColumnWidthDao(dynamic paramModel, CMySbCommand cmd)
        {
            string companyID = paramModel.companyID;
            string operId = paramModel.operId;
            string setting = paramModel.setting;
            string sql = $"insert into page_set (company_id,oper_id,page_name,setting) values ({companyID},{operId},'CheckAccountNew','{setting}') on conflict(company_id,oper_id,page_name) do update set setting='{setting}';";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            dynamic result = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            return new JsonResult(new { msg = "OK", result = result }); 
        }
        public static async Task<JsonResult> QyeryTableColumnWidthDao(string companyID, string operId, CMySbCommand cmd)
        {
            string sql = $"select setting as column_width_list from page_set where company_id = {companyID} and oper_id = {operId} and page_name = 'CheckAccountNew';";
            dynamic result = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            return new JsonResult(new { message = "OK", result = result }); 
        }
        public static async Task<SummaryListModel> QueryCheckAccountSummaryDao(CheckAccountSummaryParamsModel paramModel, CMySbCommand cmd)
        {
            string companyID = paramModel.CompanyID;
            string sellerID = paramModel.SellerID;
            string supcust_id = paramModel.supcust_id; // 获取客户ID
            string branch_id = paramModel.branch_id;   // 获取仓库ID
            string departPath = paramModel.DepartPath;
            
            string startTime = paramModel.StartTime + " 00:00:00";
            string endTime = paramModel.EndTime + " 23:59:59";
            
            string sql = @$"";
            SQLQueue QQ = new SQLQueue(cmd);

            bool checkAccountBySender = false;
            cmd.CommandText = $"select setting->>'checkAccountBySender' as check_by_sender from company_setting where company_id={companyID}";
            object ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value)
            {
                checkAccountBySender = Convert.ToBoolean(ov?.ToString().ToLower());
            }

            // 使用if语句构建条件，保持代码可读性
            string sellerIDCondi = "";
            if (!string.IsNullOrEmpty(sellerID))
            {
                sellerIDCondi = $" and getter_id = {sellerID} ";
            }

            string sellerIDSaleCondi = "";
            if (!string.IsNullOrEmpty(sellerID))
            {
                sellerIDSaleCondi = $" and (case when ssm.getter_id is not null then ssm.getter_id when ({checkAccountBySender} or ssm.order_sheet_id is not null) and ssm.senders_id is not null then split_part(replace( ssm.senders_id,'undefined,',''), ',',1)::int else seller_id end) = {sellerID} ";
            }

            string departIDCondi = "";
            if (!string.IsNullOrEmpty(departPath))
            {
                departIDCondi = $" and depart_path like '{departPath}%' ";
            }
            
            // 添加客户ID条件
            string supcustIDCondi = "";
            if (!string.IsNullOrEmpty(supcust_id))
            {
                supcustIDCondi = $" and iss.supcust_id = {supcust_id} ";
            }
            
            // 添加仓库ID条件 - 只用于销售单
            string branchIDCondi = "";
            if (!string.IsNullOrEmpty(branch_id))
            {
                branchIDCondi = $" and ssm.branch_id = {branch_id} ";
            }

            string whereCondi = "";
            // 销售净额 - 只有销售单才使用仓库检索
            whereCondi =
                @$"where ssm.company_id = {companyID} {sellerIDSaleCondi} {departIDCondi} {supcustIDCondi} {branchIDCondi} and ssm.red_flag is null and ssm.happen_time >= '{startTime}' and ssm.happen_time <= '{endTime}'";
            sql = GetSaleSheetListSummary(companyID, whereCondi, "", checkAccountBySender);
            QQ.Enqueue("summarySaleSheetsInfo", sql);
            
            // 预收款单、定货会 - 不使用仓库检索
            whereCondi =
                $@" where sp.company_id = {companyID} {sellerIDCondi} {departIDCondi} {supcustIDCondi} and sp.red_flag is null and sp.happen_time >= '{startTime}' and sp.happen_time <= '{endTime}' and (sp.sheet_type = 'YS' OR sp.sheet_type = 'DH') ";
            sql = GetPreGetSheetListSummary(companyID, whereCondi);
            QQ.Enqueue("summaryPreGetSheetsInfo", sql);

            // 预付款单 - 不使用仓库检索
            whereCondi =
                $@" where sp.company_id = {companyID} {sellerIDCondi} {departIDCondi} {supcustIDCondi} and sp.sheet_type = 'YF' and sp.red_flag is null and sp.happen_time >= '{startTime}' and sp.happen_time <= '{endTime}'";
            sql = GetPrePaySheetListSummary(companyID, whereCondi);
            QQ.Enqueue("summaryPrePaySheetsInfo", sql);

            // 收款单/付款单 - 不使用仓库检索
            whereCondi =
                $@"where sgam.company_id = {companyID} {sellerIDCondi} {departIDCondi} {supcustIDCondi} and (sgam.sheet_type = 'SK' OR sgam.sheet_type = 'FK') and sgam.red_flag is null and sgam.happen_time >= '{startTime}' and sgam.happen_time <= '{endTime}'";
            sql = GetGetArrearSheetListSummary(companyID, whereCondi);
            QQ.Enqueue("summaryGetArrearsSheetsInfo", sql);

            // 支出单 - 不使用仓库检索
            whereCondi =
                $@"where sfom.company_id = {companyID} {sellerIDCondi} {departIDCondi} {supcustIDCondi} and sfom.sheet_type = 'ZC' and sfom.red_flag is null and sfom.happen_time >= '{startTime}' and sfom.happen_time <= '{endTime}'";
            sql = GetFeeOutSheetListSummary(companyID, whereCondi);
            QQ.Enqueue("summaryFeeOutSheetsInfo", sql);

            // 收入单 - 不使用仓库检索
            whereCondi =
                $@"where sfom.company_id = {companyID} {sellerIDCondi} {departIDCondi} {supcustIDCondi} and sfom.sheet_type = 'SR' and sfom.red_flag is null and sfom.happen_time >= '{startTime}' and sfom.happen_time <= '{endTime}'";
            sql = GetIncomeSheetListSummary(companyID, whereCondi);
            QQ.Enqueue("summaryIncomeSheetsInfo", sql);

            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            SummaryListModel result = new SummaryListModel();
            while (QQ.Count > 0)
            {
                string sqlName = QQ.Dequeue();
                if (sqlName == "summarySaleSheetsInfo")
                {
                    result.summarySaleSheetsInfo = CDbDealer.GetRecordsFromDr<TableRowModel>(dr, false);
                }
                else if (sqlName == "summaryPreGetSheetsInfo")
                {
                    result.summaryPreGetSheetsInfo = CDbDealer.GetRecordsFromDr<TableRowModel>(dr, false);
                }
                else if (sqlName == "summaryPrePaySheetsInfo")
                {
                    result.summaryPrePaySheetsInfo = CDbDealer.GetRecordsFromDr<TableRowModel>(dr, false);
                }
                else if (sqlName == "summaryGetArrearsSheetsInfo")
                {
                    result.summaryGetArrearsSheetsInfo = CDbDealer.GetRecordsFromDr<TableRowModel>(dr, false);
                }
                else if (sqlName == "summaryFeeOutSheetsInfo")
                {
                    result.summaryFeeOutSheetsInfo = CDbDealer.GetRecordsFromDr<TableRowModel>(dr, false);
                }
                else if (sqlName == "summaryIncomeSheetsInfo")
                {
                    result.summaryIncomeSheetsInfo = CDbDealer.GetRecordsFromDr<TableRowModel>(dr, false);
                }
            }

            QQ.Clear();
            return result;
        }

        public static async Task<SummaryListModel> QueryCheckAccountSummaryBySellerGroupDayDao(CheckAccountSummaryParamsModel paramModel, CMySbCommand cmd)
        {
            string companyID = paramModel.CompanyID;
            string sellerID = paramModel.SellerID;
            
            string startTime = paramModel.StartTime + " 00:00:00";
            string endTime = paramModel.EndTime + " 23:59:59";
            
            string sql = @$"";
            SQLQueue QQ = new SQLQueue(cmd);

            bool checkAccountBySender = false;
            cmd.CommandText = $"select setting->>'checkAccountBySender' as check_by_sender from company_setting where company_id={companyID}";
            object ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value)
            {
                checkAccountBySender = Convert.ToBoolean(ov?.ToString().ToLower());
            }

            string whereCondi = "";
            // 销售净额
            whereCondi =
                @$"where ssm.company_id = {companyID} and (case when ssm.getter_id is not null then ssm.getter_id when ({checkAccountBySender} or ssm.order_sheet_id is not null) and ssm.senders_id is not null then split_part(replace( ssm.senders_id,'undefined,',''), ',',1)::int else seller_id end) = {sellerID} and ssm.red_flag is null and ssm.happen_time >= '{startTime}' and ssm.happen_time <= '{endTime}'";
            sql = GetSaleSheetListSummary(companyID, whereCondi, "", checkAccountBySender);
            QQ.Enqueue("summarySaleSheetsInfo", sql);
            // 预收款单、定货会
            whereCondi =
                $@" where sp.company_id = {companyID} and  sp.getter_id = {sellerID}  and sp.red_flag is null and sp.happen_time >= '{startTime}' and sp.happen_time <= '{endTime}' and (sp.sheet_type = 'YS' OR sp.sheet_type = 'DH') ";
            sql = GetPreGetSheetListSummary(companyID,whereCondi);
            QQ.Enqueue("summaryPreGetSheetsInfo", sql);
            whereCondi =
                $@" where  sp.company_id = {companyID} and  sp.getter_id = {sellerID} and   sp.sheet_type = 'YF' and sp.red_flag is null and sp.happen_time >= '{startTime}' and sp.happen_time <= '{endTime}'";
            sql = GetPrePaySheetListSummary(companyID, whereCondi);
            QQ.Enqueue("summaryPrePaySheetsInfo", sql);
            whereCondi =
                $@"where sgam.company_id = {companyID} and  sgam.getter_id = {sellerID} and (sgam.sheet_type = 'SK' OR sgam.sheet_type = 'FK') and sgam.red_flag is null and sgam.happen_time >= '{startTime}' and sgam.happen_time <= '{endTime}'";
            sql = GetGetArrearSheetListSummary(companyID, whereCondi);
            QQ.Enqueue("summaryGetArrearsSheetsInfo", sql);
            whereCondi =
                $@"where sfom.company_id = {companyID} and  sfom.getter_id = {sellerID} and sfom.sheet_type = 'ZC' and sfom.red_flag is null and sfom.happen_time >= '{startTime}' and sfom.happen_time <= '{endTime}'";
            sql = GetFeeOutSheetListSummary(companyID, whereCondi);
            QQ.Enqueue("summaryFeeOutSheetsInfo", sql);
            whereCondi =
                $@"where sfom.company_id = {companyID} and  sfom.getter_id = {sellerID} and sfom.sheet_type = 'SR' and sfom.red_flag is null and sfom.happen_time >= '{startTime}' and sfom.happen_time <= '{endTime}'";
            sql = GetIncomeSheetListSummary(companyID, whereCondi);
            QQ.Enqueue("summaryIncomeSheetsInfo", sql);
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            SummaryListModel result = new SummaryListModel();
            while (QQ.Count > 0)
            {
                string sqlName = QQ.Dequeue();
                if (sqlName == "summarySaleSheetsInfo")
                {
                    result.summarySaleSheetsInfo = CDbDealer.GetRecordsFromDr<TableRowModel>(dr, false);
                }
                else if (sqlName == "summaryPreGetSheetsInfo")
                {
                    result.summaryPreGetSheetsInfo = CDbDealer.GetRecordsFromDr<TableRowModel>(dr, false);
                }
                else if (sqlName == "summaryPrePaySheetsInfo")
                {
                    result.summaryPrePaySheetsInfo = CDbDealer.GetRecordsFromDr<TableRowModel>(dr, false);
                }
                else if (sqlName == "summaryGetArrearsSheetsInfo")
                {
                    result.summaryGetArrearsSheetsInfo = CDbDealer.GetRecordsFromDr<TableRowModel>(dr, false);
                }
                else if (sqlName == "summaryFeeOutSheetsInfo")
                {
                    result.summaryFeeOutSheetsInfo = CDbDealer.GetRecordsFromDr<TableRowModel>(dr, false);
                }
                else if (sqlName == "summaryIncomeSheetsInfo")
                {
                    result.summaryIncomeSheetsInfo = CDbDealer.GetRecordsFromDr<TableRowModel>(dr, false);
                }
            }

            QQ.Clear();
            return result;
        }

        public static async Task<List<SheetCheckSheetsDetail>> QueryCheckedSheetListDetailSheetIdListDao(CheckedSheetListParamsModel paramModel, CMySbCommand cmd)
        {
            string companyID = paramModel.CompanyID;
            string checkAccountSheetId = paramModel.CheckAccountSheetId;
            List<SheetCheckSheetsDetail> result = new List<SheetCheckSheetsDetail>();

            string sql = $@" select
     company_id,
     sheet_id,
     business_sheet_id,
     business_sheet_type,
     business_sheet_no
     from sheet_check_sheets_detail where company_id = {companyID} and sheet_id = {checkAccountSheetId};";
            result = await CDbDealer.GetRecordsFromSQLAsync<SheetCheckSheetsDetail>(sql, cmd);
            return result;
        }

        public static async Task<SummaryListModel> QueryCheckedSheetListDetailDao(CheckedSheetListParamsModel paramModel, CMySbCommand cmd,
            SheetCheckSheetsDetailGroupId sheetsDetailGroupId)
        {
            string companyID = paramModel.CompanyID;
            string checkAccountSheetId = paramModel.CheckAccountSheetId;
            string sql = @$"";
            SQLQueue QQ = new SQLQueue(cmd);

            bool checkAccountBySender = false;
            cmd.CommandText =
                $"select setting->>'checkAccountBySender' as check_by_sender from company_setting where company_id={companyID}";
            object ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value)
            {
                checkAccountBySender = Convert.ToBoolean(ov?.ToString().ToLower());
            }

            string whereCondi = "";
            string whereSummaryCondi = @$" and scsm.sheet_id = {checkAccountSheetId}  and scsd.sheet_id = {checkAccountSheetId} ";
            // 销售净额
            string sheetIds = "";
            sheetIds = sheetsDetailGroupId.SalesheetsInfo;
            if (!string.IsNullOrEmpty(sheetIds))
            {
                whereCondi = @$"where ssm.company_id = {companyID} and ssm.sheet_id in ({sheetIds})";
                sql = GetSaleSheetListSummary(companyID, whereCondi, whereSummaryCondi, checkAccountBySender);
                QQ.Enqueue("summarySaleSheetsInfo", sql);
            }

            sheetIds = sheetsDetailGroupId.PregetsheetsInfo;
            if (!string.IsNullOrEmpty(sheetIds))
            {
                // 预收款单、定货会
                whereCondi = $@" where sp.company_id = {companyID} and (sp.sheet_type = 'YS' OR sp.sheet_type = 'DH') and sp.sheet_id in ({sheetsDetailGroupId.PregetsheetsInfo}) ";
                sql = GetPreGetSheetListSummary(whereCondi, whereSummaryCondi);
                QQ.Enqueue("summaryPreGetSheetsInfo", sql);
            }

            sheetIds = sheetsDetailGroupId.PrepaysheetsInfo;
            if (!string.IsNullOrEmpty(sheetIds))
            {
                whereCondi = $@" where  sp.company_id = {companyID} and sp.sheet_id in ({sheetIds}) and sp.sheet_type = 'YF'";
                sql = GetPrePaySheetListSummary(whereCondi, whereSummaryCondi);
                QQ.Enqueue("summaryPrePaySheetsInfo", sql);
            }

            sheetIds = sheetsDetailGroupId.GetarrearssheetsInfo;
            if (!string.IsNullOrEmpty(sheetIds))
            {
                whereCondi = $@"where sgam.company_id = {companyID}  and sgam.sheet_id in ({sheetIds}) and (sgam.sheet_type = 'SK' OR sgam.sheet_type = 'FK')";
                sql = GetGetArrearSheetListSummary(companyID, whereCondi, whereSummaryCondi);
                QQ.Enqueue("summaryGetArrearsSheetsInfo", sql);
            }

            sheetIds = sheetsDetailGroupId.FeeoutsheetsInfo;
            if (!string.IsNullOrEmpty(sheetIds))
            {
                whereCondi = $@"where sfom.company_id = {companyID}  and sfom.sheet_id in ({sheetIds}) and sfom.sheet_type = 'ZC'";
                sql = GetFeeOutSheetListSummary(whereCondi, whereSummaryCondi);
                QQ.Enqueue("summaryFeeOutSheetsInfo", sql);
            }

            sheetIds = sheetsDetailGroupId.IncomesheetsInfo;
            if (!string.IsNullOrEmpty(sheetIds))
            {
                whereCondi = $@"where sfom.company_id = {companyID}  and sfom.sheet_id in ({sheetIds}) and sfom.sheet_type = 'SR'";
                sql = GetIncomeSheetListSummary(whereCondi, whereSummaryCondi);
                QQ.Enqueue("summaryIncomeSheetsInfo", sql);
            }

            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            SummaryListModel result = new SummaryListModel();
            while (QQ.Count > 0)
            {
                string sqlName = QQ.Dequeue();
                if (sqlName == "summarySaleSheetsInfo")
                {
                    result.summarySaleSheetsInfo = CDbDealer.GetRecordsFromDr<TableRowModel>(dr, false);
                }
                else if (sqlName == "summaryPreGetSheetsInfo")
                {
                    result.summaryPreGetSheetsInfo = CDbDealer.GetRecordsFromDr<TableRowModel>(dr, false);
                }
                else if (sqlName == "summaryPrePaySheetsInfo")
                {
                    result.summaryPrePaySheetsInfo = CDbDealer.GetRecordsFromDr<TableRowModel>(dr, false);
                }
                else if (sqlName == "summaryGetArrearsSheetsInfo")
                {
                    result.summaryGetArrearsSheetsInfo = CDbDealer.GetRecordsFromDr<TableRowModel>(dr, false);
                }
                else if (sqlName == "summaryFeeOutSheetsInfo")
                {
                    result.summaryFeeOutSheetsInfo = CDbDealer.GetRecordsFromDr<TableRowModel>(dr, false);
                }
                else if (sqlName == "summaryIncomeSheetsInfo")
                {
                    result.summaryIncomeSheetsInfo = CDbDealer.GetRecordsFromDr<TableRowModel>(dr, false);
                }
            }

            QQ.Clear();
            return result;
        }

        /// <summary>
        /// 获取交账单据详细
        /// </summary>
        /// <param name="paramModel"></param>
        /// <param name="cmd"></param>
        /// <returns></returns>
        public static async Task<dynamic> QuerySheetDetailListToCheckDao(QuerySheetDetailListToCheckParamsModel paramModel, CMySbCommand cmd)
        {
            string companyID = paramModel.CompanyID;
            string whereCondi = "";
            string sql = @$"";
            SQLQueue QQ = new SQLQueue(cmd);
            // 销售净额
            string sheetIds = "";
            string whereSummaryCondi = "";
            bool checkAccountBySender = false;
            cmd.CommandText = $"select setting->>'checkAccountBySender' as check_by_sender from company_setting where company_id={companyID}";
            object ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value)
            {
                checkAccountBySender = Convert.ToBoolean(ov?.ToString().ToLower());
            }

            sheetIds = paramModel.SaleSheetIds;
            if (!string.IsNullOrEmpty(sheetIds))
            {
                whereCondi = @$"where ssm.company_id = {companyID} and ssm.sheet_id in ({sheetIds})";
                sql = GetSaleSheetListSummary(companyID, whereCondi, whereSummaryCondi, checkAccountBySender);
                QQ.Enqueue("summarySaleSheetsInfo", sql);
            }

            sheetIds = paramModel.PreGetSheetIds;
            if (!string.IsNullOrEmpty(sheetIds))
            {
                // 预收款单、定货会
                whereCondi = $@" where sp.company_id = {companyID} and (sp.sheet_type = 'YS' OR sp.sheet_type = 'DH') and sp.sheet_id in ({sheetIds}) ";
                sql = GetPreGetSheetListSummary(companyID, whereCondi, whereSummaryCondi);
                QQ.Enqueue("summaryPreGetSheetsInfo", sql);
            }

            sheetIds = paramModel.PrePaySheetIds;
            if (!string.IsNullOrEmpty(sheetIds))
            {
                whereCondi = $@" where  sp.company_id = {companyID} and sp.sheet_id in ({sheetIds}) and sp.sheet_type = 'YF'";
                sql = GetPrePaySheetListSummary(companyID, whereCondi, whereSummaryCondi);
                QQ.Enqueue("summaryPrePaySheetsInfo", sql);
            }

            sheetIds = paramModel.GetArrearsSheetIds;
            if (!string.IsNullOrEmpty(sheetIds))
            {
                whereCondi = $@"where sgam.company_id = {companyID}  and sgam.sheet_id in ({sheetIds}) and (sgam.sheet_type = 'SK' OR sgam.sheet_type = 'FK')";
                sql = GetGetArrearSheetListSummary(companyID, whereCondi, whereSummaryCondi);
                QQ.Enqueue("summaryGetArrearsSheetsInfo", sql);
            }

            sheetIds = paramModel.FeeOutSheetIds;
            if (!string.IsNullOrEmpty(sheetIds))
            {
                whereCondi = $@"where sfom.company_id = {companyID}  and sfom.sheet_id in ({sheetIds}) and sfom.sheet_type = 'ZC'";
                sql = GetFeeOutSheetListSummary(companyID, whereCondi, whereSummaryCondi);
                QQ.Enqueue("summaryFeeOutSheetsInfo", sql);
            }

            sheetIds = paramModel.IncomeSheetIds;
            if (!string.IsNullOrEmpty(sheetIds))
            {
                whereCondi = $@"where sfom.company_id = {companyID}  and sfom.sheet_id in ({sheetIds}) and sfom.sheet_type = 'SR'";
                sql = GetIncomeSheetListSummary(companyID, whereCondi, whereSummaryCondi);
                QQ.Enqueue("summaryIncomeSheetsInfo", sql);
            }

            sql = $"select class_id,class_name,mother_id from info_item_class where company_id={companyID};";
            QQ.Enqueue("itemClassInfo", sql);

            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            SummaryListModel result = new SummaryListModel();
            var ItemClassInfo = new List<ExpandoObject>();
            while (QQ.Count > 0)
            {
                string sqlName = QQ.Dequeue();
                if (sqlName == "summarySaleSheetsInfo")
                {
                    result.summarySaleSheetsInfo = CDbDealer.GetRecordsFromDr<TableRowModel>(dr, false);
                }
                else if (sqlName == "summaryPreGetSheetsInfo")
                {
                    result.summaryPreGetSheetsInfo = CDbDealer.GetRecordsFromDr<TableRowModel>(dr, false);
                }
                else if (sqlName == "summaryPrePaySheetsInfo")
                {
                    result.summaryPrePaySheetsInfo = CDbDealer.GetRecordsFromDr<TableRowModel>(dr, false);
                }
                else if (sqlName == "summaryGetArrearsSheetsInfo")
                {
                    result.summaryGetArrearsSheetsInfo = CDbDealer.GetRecordsFromDr<TableRowModel>(dr, false);
                }
                else if (sqlName == "summaryFeeOutSheetsInfo")
                {
                    result.summaryFeeOutSheetsInfo = CDbDealer.GetRecordsFromDr<TableRowModel>(dr, false);
                }
                else if (sqlName == "summaryIncomeSheetsInfo")
                {
                    result.summaryIncomeSheetsInfo = CDbDealer.GetRecordsFromDr<TableRowModel>(dr, false);
                }
                else if (sqlName == "itemClassInfo")
                {
                    ItemClassInfo = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }

            QQ.Clear();
            List<SheetSale> saleSheetList = new List<SheetSale>();
            sheetIds = paramModel.SaleSheetIds;
            if (!string.IsNullOrEmpty(sheetIds))
            {
                SheetSale sheetSale = new SheetSale(SHEET_RETURN.EMPTY, LOAD_PURPOSE.SHOW);
                List<SheetSale> dSheets = await sheetSale.LoadMultiSheets<SheetSale>(cmd, companyID, sheetIds, "sheet_no", "");
                // 直接使用原始数据，避免序列化/反序列化导致字段丢失
                List<SheetSale> sheets = dSheets;
                foreach (SheetSale sheet1 in sheets)
                {
                    sheet1.total_amount = sheet1.total_amount * sheet1.money_inout_flag;
                    sheet1.payway1_amount = sheet1.payway1_amount * sheet1.money_inout_flag;
                    sheet1.payway2_amount = sheet1.payway2_amount * sheet1.money_inout_flag;
                    sheet1.payway3_amount = sheet1.payway3_amount * sheet1.money_inout_flag;
                    sheet1.now_pay_amount = sheet1.now_pay_amount * sheet1.money_inout_flag;
                    sheet1.real_get_amount = sheet1.real_get_amount * sheet1.money_inout_flag;
                    sheet1.now_disc_amount = sheet1.now_disc_amount * sheet1.money_inout_flag;
                    sheet1.prepay_amount = sheet1.prepay_amount * sheet1.money_inout_flag;
                    //sheet1.left_amount = sheet1.left_amount * sheet1.money_inout_flag;
                }

                saleSheetList = sheets;
            }

            return new
            {
                result,
                ItemClassInfo,
                saleSheetList
            };
        }

        /// <summary>
        /// 检测单据是否已经交账或者红冲
        /// </summary>
        /// <param name="paramModel"></param>
        /// <param name="cmd"></param>
        /// <returns></returns>
        public static async Task<string> QueryCheckRedSheetAndCheckSheetExistDao(string companyID,string real_seller_id, List<SheetToCheckSheet> paramModel, CMySbCommand cmd)
        {
            bool checkAccountBySender = false;
            cmd.CommandText = $"select setting->>'checkAccountBySender' as check_by_sender from company_setting where company_id={companyID}";
            object ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value)
            {
                checkAccountBySender = Convert.ToBoolean(ov?.ToString().ToLower());
            }

            string checkSheetExistSQL = @"";
            string checkSql = @"";
            string sheetsInfo = "";
            foreach (SheetToCheckSheet row in paramModel)
            {
                if (checkSheetExistSQL != "")
                {
                    checkSheetExistSQL += " or ";
                }

                checkSheetExistSQL += $"(business_sheet_type='{row.business_sheet_type}' and business_sheet_id={row.business_sheet_id})";
                if (sheetsInfo != "")
                {
                    sheetsInfo += ",";
                }

                sheetsInfo += String.Concat("'", row.business_sheet_id, row.business_sheet_type, "'");
            }

            SQLQueue QQ = new SQLQueue(cmd);
            checkSheetExistSQL = "(" + checkSheetExistSQL + ")";
            checkSheetExistSQL = @$"
select d.sheet_id
from sheet_check_sheets_detail d 
    left join sheet_check_sheets_main m on d.company_id = m.company_id and d.sheet_id = m.sheet_id 
where d.company_id={companyID} and m.red_flag is null and {checkSheetExistSQL};";
            QQ.Enqueue("checkSheetExist", checkSheetExistSQL);
            // and (case when ({checkAccountBySender}) then split_part(replace( sheet_sale_main.senders_id,'undefined,',''), ',',1)::int else seller_id end) = {real_seller_id}
            checkSql = $@"
SELECT sheet_no FROM sheet_sale_main WHERE company_id = {companyID} and red_flag = 1 and approve_time is not null 
                                       and (case when getter_id is not null then getter_id when ({checkAccountBySender} or order_sheet_id is not null) and senders_id is not null then split_part(replace(senders_id,'undefined,',''), ',',1)::int else seller_id end) = {real_seller_id} 
                                       and sheet_id||sheet_type in ({sheetsInfo})
                                     UNION 
SELECT sheet_no FROM sheet_prepay WHERE company_id = {companyID} and red_flag = 1 and approve_time is not null and getter_id = {real_seller_id}   and  sheet_id||sheet_type in({sheetsInfo})
                                  UNION
SELECT sheet_no FROM sheet_fee_out_main WHERE company_id = {companyID} and red_flag = 1 and approve_time is not null and getter_id = {real_seller_id}  and sheet_id||sheet_type in({sheetsInfo})
                                        UNION
SELECT sheet_no FROM sheet_get_arrears_main WHERE company_id = {companyID} and red_flag = 1 and approve_time is not null and getter_id =  {real_seller_id} and sheet_id||sheet_type in({sheetsInfo})";
            QQ.Enqueue("redSheetNo", checkSql);
            var dr = await QQ.ExecuteReaderAsync();
            List<ExpandoObject> redSheetNo = null;
            List<ExpandoObject> existSheets = null;
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "redSheetNo")
                {
                    redSheetNo = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "checkSheetExist")
                {
                    existSheets = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();
            if (redSheetNo.Count > 0)
            {
                string sheetNoRed = "";
                foreach (dynamic expandoObject in redSheetNo)
                {
                    sheetNoRed += expandoObject.sheet_no + ",";
                }
                return "所选单据已经被红冲," + sheetNoRed;
            }
            if (existSheets.Count > 0)
            {
                // string sheetNoExist = "";
                // foreach (dynamic expandoObject in existSheets)
                // {
                //     sheetNoExist += expandoObject.business_sheet_no + ",";
                // }
                return "所选单据已经交账,请检查"; // + sheetNoExist;
            }

            return "";
        }

        /// <summary>
        /// 提交交账
        /// </summary>
        /// <param name="paramModel"></param>
        /// <param name="cmd"></param>
        /// <returns></returns>
        public static async Task<dynamic> SubmitCheckAccountDao(SheetCheckSheetsMainDbModel paramModel, CMySbCommand cmd)
        {
            string maker_id = paramModel.maker_id;
            string approve_id = paramModel.approve_id;
            string companyID = paramModel.company_id;
            string happen_time = paramModel.happen_time;
            CDbDealer db = new CDbDealer();
            string fld = @$"company_id,getter_id,happen_time,maker_id,make_time,approve_id,approve_time,make_brief,start_time,end_time,sale_amount,sale_total_amount,return_amount,sale_disc_amount,sale_left_amount,sale_prepay_amount,sale_feeout_total_amount,get_preget, preget_total_amount, preget_disc_amount,preget_left_amount,get_arrears,arrears_total_amount,arrears_disc_amount,get_prepay,prepay_total_amount,prepay_disc_amount,prepay_left_amount,fee_out,fee_out_total_amount,fee_out_left_amount,fee_out_disc_amount,income,income_total_amount, income_disc_amount, income_left_amount,payway,credential,check_account_payway1_id,check_account_payway1_amount,check_account_payway2_id,check_account_payway2_amount,arrears_prepay_amount";
            Object tempParam = JsonConvert.DeserializeObject(JsonConvert.SerializeObject(paramModel));
            db.AddFields(tempParam, fld);
            string sqlMaster = db.GetInsertSQL("sheet_check_sheets_main") + " returning sheet_id;";
            List<SheetToCheckSheet> checkSheetsDetails = paramModel.SheetToCheckSheet;
            string sqlDetail = $@"";
            string arrearsBillUpdateSql = "";
            string checkSheetExistSQL = "";
            string sheet_ids_sale = "", sheet_ids_prepay = "", sheet_ids_fee = "", sheet_ids_arrears = "";
            foreach (SheetToCheckSheet sheetCheckSheetsDetail in checkSheetsDetails)
            {
                string business_sheet_id = sheetCheckSheetsDetail.business_sheet_id;
                string business_sheet_type = sheetCheckSheetsDetail.business_sheet_type;
                string company_id = sheetCheckSheetsDetail.company_id;
                sqlDetail += @$"insert into sheet_check_sheets_detail(business_sheet_id,business_sheet_type,company_id, sheet_id)
values ({business_sheet_id},'{business_sheet_type}',{company_id},'@sheet_id');
";

             
                arrearsBillUpdateSql += $"update arrears_bill set keeper_id = {approve_id}, out_company = false where company_id = {companyID} and business_sheet_type = '{business_sheet_type}' and business_sheet_id={business_sheet_id};";

                if (business_sheet_type == "X" || business_sheet_type == "T")
                {
                    if (sheet_ids_sale != "") sheet_ids_sale += ",";
                    sheet_ids_sale += business_sheet_id; 

                }
                else if (business_sheet_type == "YS" || business_sheet_type == "YF"|| business_sheet_type == "DH")
                {
                    if (sheet_ids_prepay != "") sheet_ids_prepay += ",";
                    sheet_ids_prepay += business_sheet_id;
                }
                else if (business_sheet_type == "ZC" || business_sheet_type == "SR")
                {
                    if (sheet_ids_fee != "") sheet_ids_fee += ",";
                    sheet_ids_fee += business_sheet_id;
                }
                else if (business_sheet_type == "SK" || business_sheet_type == "FK")
                {
                    if (sheet_ids_arrears != "") sheet_ids_arrears += ",";
                    sheet_ids_arrears += business_sheet_id;
                }

            }

        
            
            string sqlUpdateSheet = "";
            if (sheet_ids_sale != "")
            {
                sqlUpdateSheet += $"update sheet_sale_main set check_account_time='{happen_time}' where company_id={companyID} and sheet_id in ({sheet_ids_sale});";

                // 2025.04.15 - #7685
                // 同时更新关联销售订单的交账时间 · 审核
                var getOrderSheetsResponse = await CommonTool.GetRelateSaleOrderSheets(cmd, companyID, sheet_ids_sale);
                if (getOrderSheetsResponse.IsOK && getOrderSheetsResponse.data.Length > 0)
                {
                    var sheet_ids_sale_order = getOrderSheetsResponse.data;
                    sqlUpdateSheet += @$"
                            update sheet_sale_order_main
                            set check_account_time='{happen_time}'
                            where company_id={companyID} and sheet_id in ({sheet_ids_sale_order});
                        ";
                }
            }

            if (sheet_ids_prepay != "")
            {
                sqlUpdateSheet += $"update sheet_prepay set check_account_time='{happen_time}' where company_id={companyID} and sheet_id in ({sheet_ids_prepay});";

            }

            if (sheet_ids_fee != "")
            {
              
                 sqlUpdateSheet += $"update sheet_fee_out_main set check_account_time='{happen_time}' where company_id={companyID} and sheet_id in ({sheet_ids_fee});";

            }

            if (sheet_ids_arrears != "")
            { 
                 sqlUpdateSheet += $"update sheet_get_arrears_main set check_account_time='{happen_time}' where company_id={companyID} and sheet_id in ({sheet_ids_arrears});";

            }
            var tran=cmd.Connection.BeginTransaction();
            string sql = $@"SELECT yj_exeSqlByInsertedRowID('{sqlMaster.Replace("'", "''")}','{sqlDetail.Replace("'", "''")}','@sheet_id')";
            cmd.CommandText = sql;
            object ov = await cmd.ExecuteScalarAsync();
            string sheet_id = "";
            if (ov != null && ov != DBNull.Value) sheet_id = ov.ToString();
            sheet_id = sheet_id.Split(",")[0];

            cmd.CommandText = sqlUpdateSheet + arrearsBillUpdateSql;
            await cmd.ExecuteNonQueryAsync();
            tran.Commit();
            string queryOperInfoSql = $@"select oper_id, oper_name from info_operator io where io.company_id = {companyID} and io.oper_id in ({maker_id}, {approve_id})";
            dynamic queryOperInfo = await CDbDealer.GetRecordsFromSQLAsync(queryOperInfoSql, cmd);
            return new {sheet_id,queryOperInfo};
        }

        public static async Task<dynamic> RedCheckAccountDao(dynamic parameter, CMySbCommand cmd)
        {
            string sheet_id = parameter.sheet_id;
            string companyID = parameter.companyID;
            string reder_id = parameter.reder_id;
            string red_time = parameter.red_time;
            string make_brief = parameter.make_brief;
            string red_flag = "";
            cmd.CommandText = $"select red_flag from sheet_check_sheets_main where sheet_id ={sheet_id} and company_id = {companyID}";
            object ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value)
            {
                red_flag = ov?.ToString();
            }

            if (!string.IsNullOrEmpty(red_flag))
            {
                throw new Exception("请勿重复红冲交账");
            } 
            cmd.CommandText = $@"
update sheet_check_sheets_main  
set red_flag = 1,
    red_time = '{red_time}',
    reder_id = {reder_id},
    make_brief = '{make_brief}'
where   company_id = {companyID} and sheet_id ={sheet_id};";
                await cmd.ExecuteScalarAsync();


            // 交账单红冲后，把关联的欠条持有人回退为销售单业务员
            string sheetSql = @$"select business_sheet_id,business_sheet_type, seller_id from sheet_check_sheets_detail  scs
                                left join (select seller_id,sheet_id from sheet_sale_main where company_id={companyID}  ) ssm on business_sheet_id = ssm.sheet_id  and scs.business_sheet_type in ('X','T')
                                where scs.sheet_id = {sheet_id} and scs.company_id = {companyID}";
            dynamic sheetList = await CDbDealer.GetRecordsFromSQLAsync(sheetSql, cmd);
            string rollBackKeeperSql = "";

            string sheet_ids_sale = "", sheet_ids_prepay = "", sheet_ids_fee = "", sheet_ids_arrears = "";
            foreach (dynamic row in sheetList)
            {
                if(row.seller_id!="")
                    rollBackKeeperSql += $"update arrears_bill set keeper_id = {row.seller_id},out_company = true where business_sheet_id = {row.business_sheet_id} and business_sheet_type = '{row.business_sheet_type}' and company_id = {companyID};";

                if (row.business_sheet_type == "X" || row.business_sheet_type == "T")
                {
                    if (sheet_ids_sale != "") sheet_ids_sale += ",";
                    sheet_ids_sale += row.business_sheet_id;
                }
                else if (row.business_sheet_type == "YS" || row.business_sheet_type == "YF" || row.business_sheet_type == "DH")
                {
                    if (sheet_ids_prepay != "") sheet_ids_prepay += ",";
                    sheet_ids_prepay += row.business_sheet_id;
                }
                else if (row.business_sheet_type == "ZC" || row.business_sheet_type == "SR")
                {
                    if (sheet_ids_fee != "") sheet_ids_fee += ",";
                    sheet_ids_fee += row.business_sheet_id;
                }
                else if (row.business_sheet_type == "SK" || row.business_sheet_type == "FK")
                {
                    if (sheet_ids_arrears != "") sheet_ids_arrears += ",";
                    sheet_ids_arrears += row.business_sheet_id;
                }
            }
            string sqlUpdateSheet = "";
            if (sheet_ids_sale != "")
            {
                sqlUpdateSheet += $"update sheet_sale_main set check_account_time=null where company_id={companyID} and sheet_id in ({sheet_ids_sale});";

                // 2025.04.15 - #7685
                // 同时更新关联销售订单的交账时间 · 红冲
                var getOrderSheetsResponse = await CommonTool.GetRelateSaleOrderSheets(cmd, companyID, sheet_ids_sale);
                if (getOrderSheetsResponse.IsOK && getOrderSheetsResponse.data.Length > 0)
                {
                    var sheet_ids_sale_order = getOrderSheetsResponse.data;
                    sqlUpdateSheet += @$"
                            update sheet_sale_order_main
                            set check_account_time=null
                            where company_id={companyID} and sheet_id in ({sheet_ids_sale_order});
                        ";
                }
            }

            if (sheet_ids_prepay != "")
            {
                sqlUpdateSheet += $"update sheet_prepay set check_account_time=null where company_id={companyID} and sheet_id in ({sheet_ids_prepay});";

            }

            if (sheet_ids_fee != "")
            {
                sqlUpdateSheet += $"update sheet_fee_out_main set check_account_time=null where company_id={companyID} and sheet_id in ({sheet_ids_fee});";

            }

            if (sheet_ids_arrears != "")
            {
                sqlUpdateSheet += $"update sheet_get_arrears_main set check_account_time=null where company_id={companyID} and sheet_id in ({sheet_ids_arrears});";

            }


            cmd.CommandText = rollBackKeeperSql + sqlUpdateSheet;
            await cmd.ExecuteNonQueryAsync();


            return new {
                    red_time,reder_id,make_brief
                };
        }

        public static async Task<dynamic> LoadHistoryCheckAccountSheetDao(dynamic parameter, CMySbCommand cmd)
        {
            string sheet_id = parameter.sheet_id;
            string companyID = parameter.companyID;
            string sql = @$"
SELECT
    scsm.company_id,
    scsm.sheet_id,
    scsm.getter_id,
    io.oper_name  as getter_name,
    scsm.happen_time,
    scsm.maker_id,
    scsm.make_time,
    io4.oper_name as maker_name,
    scsm.approve_id,
    scsm.approve_time,
    io2.oper_name as approve_name,
    scsm.make_brief,
    scsm.red_flag,
    scsm.start_time,
    scsm.end_time,
    scsm.check_account_payway1_id,
    scsm.check_account_payway1_amount,
    scsm.check_account_payway2_id,
    scsm.check_account_payway2_amount,
    scsm.reder_id,
     io3.oper_name as reder_name,
    scsm.red_time,
    scsm.credential,
    json_agg(
        jsonb_build_object(
            'business_sheet_id', scsd.business_sheet_id,
            'business_sheet_type', scsd.business_sheet_type
        )
    ) AS business_sheet_list
FROM
    sheet_check_sheets_main scsm
LEFT JOIN
    sheet_check_sheets_detail scsd
ON
    scsm.company_id = scsd.company_id AND scsm.sheet_id = scsd.sheet_id 
         left join info_operator io on io.company_id = scsm.company_id and io.oper_id = scsm.getter_id
         left join info_operator io2 on io2.company_id = scsm.company_id and io2.oper_id = scsm.approve_id
         left join info_operator io3 on io3.company_id = scsm.company_id and io3.oper_id = scsm.reder_id
         left join info_operator io4 on io4.company_id = scsm.company_id and io4.oper_id = scsm.maker_id
WHERE
    scsm.company_id = {companyID} AND scsm.sheet_id = {sheet_id}
GROUP BY
    scsm.company_id,
    scsm.sheet_id,
    scsm.getter_id,
    scsm.happen_time,
    scsm.maker_id,
    scsm.make_time,
    scsm.approve_id,
    scsm.approve_time,
    scsm.make_brief,
    scsm.red_flag,
    scsm.start_time,
    scsm.end_time,
    scsm.check_account_payway1_id,
    scsm.check_account_payway1_amount,
    scsm.check_account_payway2_id,
    scsm.check_account_payway2_amount,
    scsm.reder_id,
    scsm.red_time,
    scsm.credential,
    io.oper_name,
    io2.oper_name,
    io3.oper_name,
    io4.oper_name;
";
            dynamic result = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            return result;
        }

        public static async Task<dynamic> QueryCheckAccountHistoryListDao(dynamic parameter, CMySbCommand cmd)
        {
            string companyId = parameter.companyID;
            string getterId = parameter.getter_id;
            string departPath = parameter.depart_path;
            string approveId = parameter.approve_id;
            string startTime = parameter.startTime;
            string endTime = parameter.endTime;
            bool redFlag = parameter.red_flag;
            string sheetNo = parameter.sheet_no;
            // int pageSize = parameter.pageSize;
            // int pageNo = parameter.pageNo;
            // string sqlCount = pageNo == 1 ? "count(*) over () as total" : "-1 as total";
            
            string condi = @$"";
            string extraLeftjoin = @$"";
            if (!string.IsNullOrEmpty(getterId))
            {
                condi += @$" and scsm.getter_id = {getterId} ";
            }
            if (!string.IsNullOrEmpty(departPath))
            {
                condi += @$" and io.depart_path ilike '%{departPath}%' ";
            }
            if (!string.IsNullOrEmpty(approveId))
            {
                condi += @$" and scsm.approve_id = {approveId} ";
            }
            if (!string.IsNullOrEmpty(startTime) && !string.IsNullOrEmpty(endTime))
            {
                condi += @$" and scsm.happen_time >= '{startTime} 00:00:00' and scsm.happen_time <= '{endTime} 23:59:59' ";
            }


            if (!redFlag)
            {
                condi += " and scsm.red_flag is null ";
            }
            
            if (!string.IsNullOrEmpty(sheetNo))
            {
                condi += @$" and d.sheet_no like '%{sheetNo}%' ";
                extraLeftjoin = @$"
    left join sheet_check_sheets_detail csd on csd.sheet_id = scsm.sheet_id
 left join (
     select       sheet_id,sheet_no,sheet_type from sheet_sale_main        where company_id = {companyId} and red_flag is null and approve_time is not null
     union select sheet_id,sheet_no,sheet_type from sheet_sale_order_main  where company_id = {companyId} and red_flag is null and approve_time is not null
     union select sheet_id,sheet_no,sheet_type from sheet_get_arrears_main where company_id = {companyId} and red_flag is null and approve_time is not null
     union select sheet_id,sheet_no,sheet_type from sheet_prepay           where company_id = {companyId} and red_flag is null and approve_time is not null
     union select sheet_id,sheet_no,sheet_type from sheet_fee_out_main     where company_id = {companyId} and red_flag is null and approve_time is not null
 ) d on d.sheet_id = csd.business_sheet_id and d.sheet_type = csd.business_sheet_type
";
            }

            string sql = $@"
select
scsm.sheet_id,
scsm.getter_id,
io.oper_name as getter_name,
io.depart_path as getter_depart_path,
scsm.happen_time,
scsm.maker_id,
scsm.make_time,
io4.oper_name as maker_name,
scsm.approve_id,
io2.oper_name as approve_name,
scsm.approve_time,
scsm.make_brief,
scsm.reder_id,
io3.oper_name as reder_name,
scsm.red_time,
scsm.red_flag,
scsm.sale_amount,
scsm.return_amount,
scsm.get_arrears,
scsm.get_preget,
scsm.fee_out,
scsm.start_time,
scsm.end_time,
scsm.sale_total_amount,
scsm.sale_disc_amount,
scsm.sale_left_amount,
scsm.preget_total_amount,
scsm.preget_disc_amount,
scsm.preget_left_amount,
scsm.arrears_disc_amount,
scsm.sale_prepay_amount,
scsm.arrears_total_amount,
scsm.arrears_prepay_amount,
scsm.income,
scsm.income_total_amount,
scsm.income_left_amount,
scsm.income_disc_amount,
scsm.get_prepay,
scsm.prepay_total_amount,
scsm.prepay_disc_amount,
scsm.prepay_left_amount,
scsm.payway,
scsm.sale_feeout_total_amount,
scsm.fee_out_total_amount,
scsm.fee_out_left_amount,
scsm.fee_out_disc_amount,
scsm.check_account_payway1_id,
scsm.check_account_payway1_amount,
cs1.sub_name as check_account_payway1_name,
scsm.check_account_payway2_id,
scsm.check_account_payway2_amount,
cs2.sub_name as check_account_payway2_name,
scsm.credential,
scsm.sale_amount + scsm.get_arrears + scsm.get_prepay + scsm.get_preget + scsm.fee_out + scsm.income as paid_total
from sheet_check_sheets_main scsm
left join info_operator io on io.company_id = scsm.company_id and io.oper_id = scsm.getter_id
left join info_operator io2 on io2.company_id = scsm.company_id and io2.oper_id = scsm.approve_id
left join info_operator io3 on io3.company_id = scsm.company_id and io3.oper_id = scsm.reder_id
left join info_operator io4 on io4.company_id = scsm.company_id and io4.oper_id = scsm.maker_id
LEFT JOIN cw_subject cs1 ON cs1.sub_id = scsm.check_account_payway1_id AND cs1.company_id = scsm.company_id
LEFT JOIN cw_subject cs2 ON cs2.sub_id = scsm.check_account_payway2_id AND cs2.company_id = scsm.company_id
 {extraLeftjoin} 
where scsm.company_id = {companyId} {condi}
order by scsm.approve_time desc 
";
            // LIMIT {pageSize} OFFSET {(pageNo - 1) * pageSize}
            List<ExpandoObject> result = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            return result;
        }

        public static async Task<dynamic> LoadPrintTemplateDao(dynamic parameter, CMySbCommand cmd)
        {
            string companyID = parameter.companyID;
            string sql =  @$"
select 
pt.template_id,
pt.template_name, 
pt.sheet_type, 
pt.template_content,
ptc.template_id as choose_template_id
from print_template pt 
left join print_template_choose ptc on pt.template_id =ptc.template_id and ptc.company_id = pt.company_id
where pt.company_id={companyID} and pt.sheet_type in ('JZ_SUMMARY', 'JZ_PAYWAY', 'JZ_SHEETLIST', 'JZ_ITEMLIST')";
            dynamic result = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            return result;
        }
  
        
        #region 获取汇总数据

        private static string GetSaleSheetListSummary(string companyID,string whereCondi, string whereSummaryCondi = "", bool checkAccountBySender = false)
        {
            return $@"
select
    total_amount_seller.sheet_id,
    total_amount_seller.sheet_no,
    total_amount_seller.sheet_type,
    total_amount_seller.real_seller_id as seller_id,
    io.oper_name,
    total_amount_seller.happen_time,
    total_amount_seller.happen_time_origin,
    total_amount_seller.sup_name,
    total_amount_seller.make_brief,
    scsm.sheet_id as check_account_sheet_id,
    scsm.red_flag as check_account_red_flag,
    scsm.red_time as check_account_red_time,
    scsm.happen_time as check_account_sheet_happen_time,
    scsm.maker_id as check_account_sheet_maker_id,
    scsm.make_brief as check_account_sheet_make_brief,
    check_io1.oper_name as check_account_sheet_maker_name,
    scsm.approve_id as check_account_sheet_approve_id,
    check_io2.oper_name as check_account_sheet_approve_name,
    scsm.reder_id as check_account_sheet_reder_id,
    check_io3.oper_name as check_account_sheet_reder_name,
    round(sum(total_amount_seller.sale_total_amount)::numeric,2) as sale_total_amount,
    round(sum(total_amount_seller.return_total_amount)::numeric,2)  as return_total_amount,
    round(sum(total_amount_seller.left_total_amount)::numeric,2)  as left_total_amount,
    round(sum(total_amount_seller.now_disc_amount)::numeric,2)  as now_disc_amount,
    round(sum(total_amount_seller.preget_total_amount)::numeric,2)  as preget_total_amount,
    round(sum(total_amount_seller.feeout_total_amount)::numeric,2)  as feeout_total_amount,
    round((sum(total_amount_seller.sale_total_amount) - sum(total_amount_seller.return_total_amount) -sum(total_amount_seller.left_total_amount) - sum(total_amount_seller.now_disc_amount) - sum(total_amount_seller.preget_total_amount) - sum(total_amount_seller.feeout_total_amount))::numeric,2) as real_get_amount,
    json_agg(json_build_object(
        'payway1_id', total_amount_seller.payway1_id,
        'payway1_amount', total_amount_seller.payway1_amount * total_amount_seller.money_inout_flag ,
        'payway1_sub_type', total_amount_seller.payway1_type,
        'payway1_sub_name', total_amount_seller.payway1_sub_name,
        'payway2_id', total_amount_seller.payway2_id,
        'payway2_amount', total_amount_seller.payway2_amount * total_amount_seller.money_inout_flag ,
        'payway2_sub_type', total_amount_seller.payway2_type,
        'payway2_sub_name', total_amount_seller.payway2_sub_name,
        'payway3_id', total_amount_seller.payway3_id,
        'payway3_amount', total_amount_seller.payway3_amount * total_amount_seller.money_inout_flag ,
        'payway3_sub_type', total_amount_seller.payway3_type,
        'payway3_sub_name', total_amount_seller.payway3_sub_name
    )) as payway_info_list
from (
  {GetSaleSheetList(companyID,whereCondi, checkAccountBySender)} 
) total_amount_seller
left join sheet_check_sheets_detail scsd on scsd.company_id = {companyID} and scsd.business_sheet_id = total_amount_seller.sheet_id and scsd.business_sheet_type = total_amount_seller.sheet_type 
left join sheet_check_sheets_main scsm on  scsm.company_id = {companyID} and scsm.sheet_id = scsd.sheet_id
left join info_operator io ON io.company_id ={companyID} AND io.oper_id = total_amount_seller.real_seller_id
left join info_operator check_io1 ON check_io1.company_id ={companyID} AND check_io1.oper_id = scsm.maker_id 
left join info_operator check_io2 ON check_io2.company_id ={companyID} AND check_io2.oper_id = scsm.approve_id 
left join info_operator check_io3 ON check_io3.company_id ={companyID} AND check_io3.oper_id = scsm.reder_id 
where true {whereSummaryCondi} 
group by total_amount_seller.sheet_id,
total_amount_seller.sheet_no,
total_amount_seller.sheet_type,
total_amount_seller.real_seller_id,
total_amount_seller.happen_time,
total_amount_seller.happen_time_origin,
io.oper_name,
total_amount_seller.sup_name,
total_amount_seller.make_brief,
scsm.sheet_id,
scsm.red_flag,
scsm.happen_time,
scsm.maker_id,
check_io1.oper_name,
scsm.approve_id,
check_io2.oper_name,
scsm.make_brief,
scsm.red_time,
scsm.reder_id,
check_io3.oper_name
order by  total_amount_seller.happen_time_origin desc
";
        }

        private static string GetPreGetSheetListSummary(string companyID, string whereCondi, string whereSummaryCondi = "")
        {
            return @$"
select summary_pre_get_sheet_info.getter_id as seller_id,
       summary_pre_get_sheet_info.sheet_id,
       summary_pre_get_sheet_info.sheet_no,
       summary_pre_get_sheet_info.sheet_type,
       summary_pre_get_sheet_info.happen_time,
       summary_pre_get_sheet_info.happen_time_origin,
       summary_pre_get_sheet_info.oper_name,
       summary_pre_get_sheet_info.sup_name,
       summary_pre_get_sheet_info.make_brief,
       scsm.sheet_id as check_account_sheet_id,
       scsm.red_flag as check_account_red_flag,
scsm.happen_time as check_account_sheet_happen_time,
scsm.maker_id as check_account_sheet_maker_id,
check_io1.oper_name as check_account_sheet_maker_name,
scsm.approve_id as check_account_sheet_approve_id,
check_io2.oper_name as check_account_sheet_approve_name,
scsm.make_brief as check_account_sheet_make_brief,
scsm.red_time as check_account_red_time,
scsm.reder_id as check_account_sheet_reder_id,
check_io3.oper_name as check_account_sheet_reder_name,
       ROUND(sum(summary_pre_get_sheet_info.total_amount)::numeric,2)   as total_amount,
       ROUND(sum(summary_pre_get_sheet_info.now_disc_amount)::numeric,2) as now_disc_amount,
       ROUND(sum((case when summary_pre_get_sheet_info.payway1_type = 'QT' THEN summary_pre_get_sheet_info.payway1_amount ELSE 0 END ) + (case when summary_pre_get_sheet_info.payway2_type = 'QT' THEN summary_pre_get_sheet_info.payway2_amount ELSE 0 END )+(case when summary_pre_get_sheet_info.payway3_type = 'QT' THEN summary_pre_get_sheet_info.payway3_amount ELSE 0 END ))::numeric,2)  as real_get_amount,
       ROUND(sum(summary_pre_get_sheet_info.left_total_amount)::numeric,2)     as left_total_amount,
       json_agg(json_build_object(
               'payway1_id', summary_pre_get_sheet_info.payway1_id,
               'payway1_amount', summary_pre_get_sheet_info.payway1_amount,
               'payway1_sub_type', summary_pre_get_sheet_info.payway1_type,
               'payway1_sub_name', summary_pre_get_sheet_info.payway1_sub_name,
               'payway2_id', summary_pre_get_sheet_info.payway2_id,
               'payway2_amount', summary_pre_get_sheet_info.payway2_amount,
               'payway2_sub_type', summary_pre_get_sheet_info.payway2_type,
               'payway2_sub_name', summary_pre_get_sheet_info.payway2_sub_name,
               'payway3_id', summary_pre_get_sheet_info.payway3_id,
               'payway3_amount', summary_pre_get_sheet_info.payway3_amount,
               'payway3_sub_type', summary_pre_get_sheet_info.payway3_type,
               'payway3_sub_name', summary_pre_get_sheet_info.payway3_sub_name
           ))               as payway_info_list
from ( {GetPreGetSheetList(companyID, whereCondi)} ) summary_pre_get_sheet_info 
left join sheet_check_sheets_detail scsd on scsd.company_id = summary_pre_get_sheet_info.company_id and scsd.business_sheet_id = summary_pre_get_sheet_info.sheet_id and scsd.business_sheet_type = summary_pre_get_sheet_info.sheet_type 
left join sheet_check_sheets_main scsm on summary_pre_get_sheet_info.company_id = scsm.company_id and scsm.sheet_id = scsd.sheet_id
left join info_operator check_io1 ON check_io1.company_id = summary_pre_get_sheet_info.company_id AND check_io1.oper_id = scsm.maker_id
left join info_operator check_io2 ON check_io2.company_id = summary_pre_get_sheet_info.company_id AND check_io2.oper_id = scsm.approve_id 
left join info_operator check_io3 ON check_io3.company_id = summary_pre_get_sheet_info.company_id AND check_io3.oper_id = scsm.reder_id
where true {whereSummaryCondi} 
group by summary_pre_get_sheet_info.getter_id,
       summary_pre_get_sheet_info.sheet_id,
       summary_pre_get_sheet_info.sheet_no,
       summary_pre_get_sheet_info.sheet_type,
       summary_pre_get_sheet_info.happen_time,
       summary_pre_get_sheet_info.happen_time_origin,
       summary_pre_get_sheet_info.oper_name,
       summary_pre_get_sheet_info.sup_name,
       summary_pre_get_sheet_info.make_brief,
       scsm.sheet_id,
scsm.happen_time,
scsm.maker_id,
scsm.red_flag,
check_io1.oper_name,
scsm.approve_id,
check_io2.oper_name,
    scsm.make_brief,scsm.red_time,scsm.reder_id,
check_io3.oper_name
order by  summary_pre_get_sheet_info.happen_time_origin desc;
";
        }

        private static string GetPrePaySheetListSummary(string companyID, string whereCondi, string whereSummaryCondi = "")
        {
            return $@"
select summary_pre_pay_sheet_info.getter_id as seller_id,
       summary_pre_pay_sheet_info.sheet_id,
       summary_pre_pay_sheet_info.sheet_no,
       summary_pre_pay_sheet_info.sheet_type,
       summary_pre_pay_sheet_info.sup_name,
       summary_pre_pay_sheet_info.make_brief,
       summary_pre_pay_sheet_info.happen_time,
       summary_pre_pay_sheet_info.happen_time_origin,
       summary_pre_pay_sheet_info.oper_name,
           scsm.sheet_id as check_account_sheet_id,
    scsm.happen_time as check_account_sheet_happen_time,
    scsm.maker_id as check_account_sheet_maker_id,
    check_io1.oper_name as check_account_sheet_maker_name,
    scsm.approve_id as check_account_sheet_approve_id,
         scsm.red_flag as check_account_red_flag,
    check_io2.oper_name as check_account_sheet_approve_name,
    scsm.make_brief as check_account_sheet_make_brief,
    scsm.red_time as check_account_red_time,
    scsm.reder_id as check_account_sheet_reder_id,
check_io3.oper_name as check_account_sheet_reder_name,
       ROUND(sum(summary_pre_pay_sheet_info.total_amount)::numeric,2)    as total_amount,
       ROUND(sum(summary_pre_pay_sheet_info.now_disc_amount)::numeric,2) as now_disc_amount,
       ROUND(sum((case when summary_pre_pay_sheet_info.payway1_type = 'QT' THEN summary_pre_pay_sheet_info.payway1_amount ELSE 0 END ) + (case when summary_pre_pay_sheet_info.payway2_type = 'QT' THEN summary_pre_pay_sheet_info.payway2_amount ELSE 0 END ))::numeric,2)  as real_get_amount,
       ROUND(sum(summary_pre_pay_sheet_info.left_total_amount)::numeric,2)     as left_total_amount,
       json_agg(json_build_object(
               'payway1_id', summary_pre_pay_sheet_info.payway1_id,
               'payway1_amount', summary_pre_pay_sheet_info.payway1_amount,
               'payway1_sub_type', summary_pre_pay_sheet_info.payway1_type,
               'payway1_sub_name', summary_pre_pay_sheet_info.payway1_sub_name,
               'payway2_id', summary_pre_pay_sheet_info.payway2_id,
               'payway2_amount', summary_pre_pay_sheet_info.payway2_amount,
               'payway2_sub_type', summary_pre_pay_sheet_info.payway2_type,
               'payway2_sub_name', summary_pre_pay_sheet_info.payway2_sub_name
           ))               as payway_info_list
from ( {GetPrePaySheetList(companyID, whereCondi)} ) summary_pre_pay_sheet_info 
left join sheet_check_sheets_detail scsd on scsd.company_id = summary_pre_pay_sheet_info.company_id and scsd.business_sheet_id = summary_pre_pay_sheet_info.sheet_id and scsd.business_sheet_type = summary_pre_pay_sheet_info.sheet_type 
left join sheet_check_sheets_main scsm on summary_pre_pay_sheet_info.company_id = scsm.company_id and scsm.sheet_id = scsd.sheet_id
left join info_operator check_io1 ON check_io1.company_id = summary_pre_pay_sheet_info.company_id AND check_io1.oper_id = scsm.maker_id
left join info_operator check_io2 ON check_io2.company_id = summary_pre_pay_sheet_info.company_id AND check_io2.oper_id = scsm.approve_id 
left join info_operator check_io3 ON check_io3.company_id = summary_pre_pay_sheet_info.company_id AND check_io3.oper_id = scsm.reder_id
where true {whereSummaryCondi} 
group by summary_pre_pay_sheet_info.getter_id,
       summary_pre_pay_sheet_info.sheet_id,
       summary_pre_pay_sheet_info.sheet_no,
       summary_pre_pay_sheet_info.sheet_type,
       summary_pre_pay_sheet_info.sup_name,
       summary_pre_pay_sheet_info.happen_time,
       summary_pre_pay_sheet_info.happen_time_origin,
       summary_pre_pay_sheet_info.oper_name,
       summary_pre_pay_sheet_info.make_brief,
       scsm.sheet_id,
scsm.happen_time,
scsm.maker_id,
check_io1.oper_name,
scsm.approve_id,
scsm.red_flag,
check_io2.oper_name,
scsm.make_brief,
scsm.red_time,
    scsm.reder_id,
check_io3.oper_name
order by  summary_pre_pay_sheet_info.happen_time_origin desc;
";
        }

        private static string GetGetArrearSheetListSummary(string companyID, string whereCondi, string whereSummaryCondi = "")
        {
            return $@"
select summary_get_arrears_sheet_info.getter_id as seller_id,
       summary_get_arrears_sheet_info.sheet_id,
       summary_get_arrears_sheet_info.sheet_no,
       summary_get_arrears_sheet_info.sheet_type,
       summary_get_arrears_sheet_info.sup_name,
       summary_get_arrears_sheet_info.make_brief,
       summary_get_arrears_sheet_info.happen_time,
       summary_get_arrears_sheet_info.happen_time_origin,
       summary_get_arrears_sheet_info.oper_name,
       scsm.sheet_id as check_account_sheet_id,
    scsm.happen_time as check_account_sheet_happen_time,
    scsm.maker_id as check_account_sheet_maker_id,
    check_io1.oper_name as check_account_sheet_maker_name,
    scsm.approve_id as check_account_sheet_approve_id,
    check_io2.oper_name as check_account_sheet_approve_name,
    scsm.make_brief as check_account_sheet_make_brief,
         scsm.red_flag as check_account_red_flag,
             scsm.red_time as check_account_red_time,
             scsm.reder_id as check_account_sheet_reder_id,
check_io3.oper_name as check_account_sheet_reder_name,
    round(sum(summary_get_arrears_sheet_info.preget_total_amount)::numeric,2)  as preget_total_amount,
     round(sum(summary_get_arrears_sheet_info.total_amount)::numeric,2)    as total_amount,
       round(sum(summary_get_arrears_sheet_info.now_disc_amount)::numeric,2)  as now_disc_amount,
       round(sum((case when summary_get_arrears_sheet_info.payway1_type = 'QT' THEN summary_get_arrears_sheet_info.payway1_amount ELSE 0 END ) + (case when summary_get_arrears_sheet_info.payway2_type = 'QT' THEN summary_get_arrears_sheet_info.payway2_amount ELSE 0 END ) + (case when summary_get_arrears_sheet_info.payway3_type = 'QT' THEN summary_get_arrears_sheet_info.payway3_amount ELSE 0 END ))::numeric,2)   as real_get_amount,
       json_agg(json_build_object(
               'payway1_id', summary_get_arrears_sheet_info.payway1_id,
               'payway1_amount', summary_get_arrears_sheet_info.payway1_amount,
               'payway1_sub_type', summary_get_arrears_sheet_info.payway1_type,
               'payway1_sub_name', summary_get_arrears_sheet_info.payway1_sub_name,
               'payway2_id', summary_get_arrears_sheet_info.payway2_id,
               'payway2_amount', summary_get_arrears_sheet_info.payway2_amount,
               'payway2_sub_type', summary_get_arrears_sheet_info.payway2_type,
               'payway2_sub_name', summary_get_arrears_sheet_info.payway2_sub_name,
               'payway3_id', summary_get_arrears_sheet_info.payway3_id,
               'payway3_amount', summary_get_arrears_sheet_info.payway3_amount,
               'payway3_sub_type', summary_get_arrears_sheet_info.payway3_type,
               'payway3_sub_name', summary_get_arrears_sheet_info.payway3_sub_name
           ))               as payway_info_list
from
    ({GetGetArrearSheetList(companyID, whereCondi)}) summary_get_arrears_sheet_info 
left join sheet_check_sheets_detail scsd on scsd.company_id = {companyID} and scsd.business_sheet_id = summary_get_arrears_sheet_info.sheet_id and scsd.business_sheet_type = summary_get_arrears_sheet_info.sheet_type 
left join sheet_check_sheets_main scsm on scsm.company_id = {companyID} and scsm.sheet_id = scsd.sheet_id
left join info_operator check_io1 ON check_io1.company_id = {companyID} AND check_io1.oper_id = scsm.maker_id
left join info_operator check_io2 ON check_io2.company_id = {companyID} AND check_io2.oper_id = scsm.approve_id 
left join info_operator check_io3 ON check_io3.company_id = {companyID} AND check_io3.oper_id = scsm.reder_id
where true {whereSummaryCondi}  
group by summary_get_arrears_sheet_info.getter_id,
       summary_get_arrears_sheet_info.sheet_id,
       summary_get_arrears_sheet_info.sheet_no,
       summary_get_arrears_sheet_info.make_brief,
       summary_get_arrears_sheet_info.sheet_type,
       summary_get_arrears_sheet_info.sup_name,
       summary_get_arrears_sheet_info.happen_time,
       summary_get_arrears_sheet_info.happen_time_origin,
       summary_get_arrears_sheet_info.oper_name,
       scsm.sheet_id,
scsm.happen_time,
scsm.maker_id,
check_io1.oper_name,
scsm.approve_id,
scsm.red_flag,
check_io2.oper_name,
    scsm.make_brief, scsm.red_time,scsm.reder_id,
check_io3.oper_name 
order by  summary_get_arrears_sheet_info.happen_time_origin desc;
";
        }

        private static string GetFeeOutSheetListSummary(string companyID,string whereCondi, string whereSummaryCondi = "")
        {
            return $@"
select summary_feeout_sheet_info.getter_id as seller_id,
       summary_feeout_sheet_info.sheet_id,
       summary_feeout_sheet_info.sheet_no,
       summary_feeout_sheet_info.sheet_type,
       summary_feeout_sheet_info.sup_name,
       summary_feeout_sheet_info.make_brief,
       summary_feeout_sheet_info.happen_time,
       summary_feeout_sheet_info.happen_time_origin,
       summary_feeout_sheet_info.oper_name,
           scsm.sheet_id as check_account_sheet_id,
        summary_feeout_sheet_info.now_disc_amount
            as now_disc_amount,
    scsm.happen_time as check_account_sheet_happen_time,
    scsm.maker_id as check_account_sheet_maker_id,
    check_io1.oper_name as check_account_sheet_maker_name,
    scsm.approve_id as check_account_sheet_approve_id,
         scsm.red_flag as check_account_red_flag,
    check_io2.oper_name as check_account_sheet_approve_name,
    scsm.make_brief as check_account_sheet_make_brief,
        scsm.red_time as check_account_red_time,
        scsm.reder_id as check_account_sheet_reder_id,
check_io3.oper_name as check_account_sheet_reder_name,
       round(sum(summary_feeout_sheet_info.total_amount)::numeric,2)    as total_amount,
       round(sum(summary_feeout_sheet_info.left_total_amount - summary_feeout_sheet_info.now_disc_amount)::numeric,2)      as left_total_amount,
       round(sum((case when summary_feeout_sheet_info.payway1_type = 'QT' THEN summary_feeout_sheet_info.payway1_amount ELSE 0 END ) + (case when summary_feeout_sheet_info.payway2_type = 'QT' THEN summary_feeout_sheet_info.payway2_amount ELSE 0 END ))::numeric,2)  as real_get_amount,
       json_agg(json_build_object(
               'payway1_id', summary_feeout_sheet_info.payway1_id,
               'payway1_amount', summary_feeout_sheet_info.payway1_amount,
               'payway1_sub_type', summary_feeout_sheet_info.payway1_type,
               'payway1_sub_name', summary_feeout_sheet_info.payway1_sub_name,
               'payway2_id', summary_feeout_sheet_info.payway2_id,
               'payway2_amount', summary_feeout_sheet_info.payway2_amount,
               'payway2_sub_type', summary_feeout_sheet_info.payway2_type,
               'payway2_sub_name', summary_feeout_sheet_info.payway2_sub_name
           ))               as payway_info_list
from ( {GetFeeOutSheetList(companyID, whereCondi)}) summary_feeout_sheet_info 
left join sheet_check_sheets_detail scsd on scsd.company_id = {companyID} and scsd.business_sheet_id = summary_feeout_sheet_info.sheet_id and scsd.business_sheet_type = summary_feeout_sheet_info.sheet_type 
left join sheet_check_sheets_main scsm on scsm.company_id = {companyID} and scsm.sheet_id = scsd.sheet_id
left join info_operator check_io1 ON check_io1.company_id = {companyID} AND check_io1.oper_id = scsm.maker_id
left join info_operator check_io2 ON check_io2.company_id = {companyID} AND check_io2.oper_id = scsm.approve_id 
left join info_operator check_io3 ON check_io3.company_id = {companyID} AND check_io3.oper_id = scsm.reder_id
where true {whereSummaryCondi}  
group by summary_feeout_sheet_info.getter_id,
       summary_feeout_sheet_info.sheet_id,
       summary_feeout_sheet_info.sheet_no,
       summary_feeout_sheet_info.sheet_type,
       summary_feeout_sheet_info.sup_name,
       summary_feeout_sheet_info.make_brief,
summary_feeout_sheet_info.now_disc_amount,
       summary_feeout_sheet_info.happen_time,
       summary_feeout_sheet_info.happen_time_origin,
       summary_feeout_sheet_info.oper_name,
       scsm.sheet_id,
scsm.happen_time,
scsm.maker_id,
check_io1.oper_name,
scsm.approve_id,
scsm.red_flag,
check_io2.oper_name,
    scsm.make_brief,scsm.red_time,scsm.reder_id,
check_io3.oper_name
order by  summary_feeout_sheet_info.happen_time_origin desc;
";
        }

        private static string GetIncomeSheetListSummary(string companyID,string whereCondi, string whereSummaryCondi = "")
        {
            return $@"
select summary_feeout_sheet_info.getter_id as seller_id,
       summary_feeout_sheet_info.sheet_id,
       summary_feeout_sheet_info.sheet_no,
       summary_feeout_sheet_info.sheet_type,
       summary_feeout_sheet_info.make_brief,
       summary_feeout_sheet_info.sup_name,
       summary_feeout_sheet_info.happen_time,
       summary_feeout_sheet_info.happen_time_origin,
       summary_feeout_sheet_info.oper_name,
       scsm.sheet_id as check_account_sheet_id,
    scsm.happen_time as check_account_sheet_happen_time,
    scsm.maker_id as check_account_sheet_maker_id,
    check_io1.oper_name as check_account_sheet_maker_name,
    scsm.approve_id as check_account_sheet_approve_id,
    check_io2.oper_name as check_account_sheet_approve_name,
         scsm.red_flag as check_account_red_flag,
         scsm.make_brief as check_account_sheet_make_brief,
             scsm.red_time as check_account_red_time,
             scsm.reder_id as check_account_sheet_reder_id,
check_io3.oper_name as check_account_sheet_reder_name,
now_disc_amount,
       round(sum(summary_feeout_sheet_info.total_amount)::numeric,2)    as total_amount,
       round(sum(summary_feeout_sheet_info.left_total_amount)::numeric, 2) as left_total_amount,
        round(sum((case when summary_feeout_sheet_info.payway1_type = 'QT' THEN summary_feeout_sheet_info.payway1_amount ELSE 0 END ) + (case when summary_feeout_sheet_info.payway2_type = 'QT' THEN summary_feeout_sheet_info.payway2_amount ELSE 0 END ))::numeric,2)  as real_get_amount,
       json_agg(json_build_object(
               'payway1_id', summary_feeout_sheet_info.payway1_id,
               'payway1_amount', summary_feeout_sheet_info.payway1_amount,
               'payway1_sub_type', summary_feeout_sheet_info.payway1_type,
               'payway1_sub_name', summary_feeout_sheet_info.payway1_sub_name,
               'payway2_id', summary_feeout_sheet_info.payway2_id,
               'payway2_amount', summary_feeout_sheet_info.payway2_amount,
               'payway2_sub_type', summary_feeout_sheet_info.payway2_type,
               'payway2_sub_name', summary_feeout_sheet_info.payway2_sub_name
           ))               as payway_info_list
from ({GetIncomeSheetList(companyID, whereCondi)}) summary_feeout_sheet_info 
left join sheet_check_sheets_detail scsd on scsd.company_id = {companyID} and scsd.business_sheet_id = summary_feeout_sheet_info.sheet_id and scsd.business_sheet_type = summary_feeout_sheet_info.sheet_type 
left join sheet_check_sheets_main scsm on scsm.company_id = {companyID} and scsm.sheet_id = scsd.sheet_id
left join info_operator check_io1 ON check_io1.company_id = {companyID} AND check_io1.oper_id = scsm.maker_id
left join info_operator check_io2 ON check_io2.company_id = {companyID} AND check_io2.oper_id = scsm.approve_id 
left join info_operator check_io3 ON check_io3.company_id = {companyID} AND check_io3.oper_id = scsm.reder_id
where true {whereSummaryCondi}  
group by summary_feeout_sheet_info.getter_id, 
       summary_feeout_sheet_info.sheet_id,
       summary_feeout_sheet_info.sheet_no,
       summary_feeout_sheet_info.sheet_type,
       summary_feeout_sheet_info.now_disc_amount,
       summary_feeout_sheet_info.sup_name,
       summary_feeout_sheet_info.make_brief,
       summary_feeout_sheet_info.happen_time,
       summary_feeout_sheet_info.happen_time_origin,
       summary_feeout_sheet_info.oper_name,
       scsm.sheet_id,
scsm.happen_time,
scsm.maker_id,
check_io1.oper_name,
scsm.approve_id,
scsm.red_flag,
check_io2.oper_name, scsm.make_brief, scsm.red_time,scsm.reder_id,
check_io3.oper_name order by  summary_feeout_sheet_info.happen_time_origin desc;
";
        }

        #endregion

        #region 获取列表单据sql

        private static string GetSaleSheetList(string companyID, string condi, bool checkAccountBySender = false)
        {
            return $@"select ssm.company_id,
       ssm.sheet_id,
       ssm.sheet_no,
       ssm.sheet_type,
       ssm.money_inout_flag,
       (case when ssm.getter_id is not null then ssm.getter_id when ({checkAccountBySender} or ssm.order_sheet_id is not null) and ssm.senders_id is not null then split_part(replace( ssm.senders_id,'undefined,',''), ',',1)::int else seller_id end) as real_seller_id,
       TO_CHAR(ssm.happen_time, 'YYYY-MM-DD') as happen_time,
       ssm.happen_time as happen_time_origin,
       ssm.make_brief,
       round(SUM(CASE WHEN ssd.inout_flag * ssd.sub_amount < 0 THEN ssd.sub_amount * ssd.inout_flag * (-1) ELSE 0 END)::numeric,2) AS sale_total_amount,
       round(SUM(CASE WHEN ssd.inout_flag * ssd.sub_amount > 0 THEN ssd.sub_amount * ssd.inout_flag  ELSE 0 END)::numeric,2) AS return_total_amount,
       round((ssm.total_amount - ssm.now_disc_amount - ssm.now_pay_amount)::numeric, 2) * ssm.money_inout_flag AS left_total_amount,
       ssm.now_disc_amount  * ssm.money_inout_flag as now_disc_amount,
       round((CASE WHEN cs1.sub_type = 'YS' THEN ssm.payway1_amount ELSE 0 END + CASE WHEN cs2.sub_type = 'YS' THEN ssm.payway2_amount ELSE 0 END + CASE WHEN cs3.sub_type = 'YS' THEN ssm.payway3_amount ELSE 0 END)::numeric,2) * ssm.money_inout_flag as preget_total_amount,
      round((CASE WHEN cs1.sub_type = 'ZC' THEN ssm.payway1_amount ELSE 0 END + CASE WHEN cs2.sub_type = 'ZC' THEN ssm.payway2_amount ELSE 0 END + CASE WHEN cs3.sub_type = 'ZC' THEN ssm.payway3_amount ELSE 0 END)::numeric,2) * ssm.money_inout_flag AS feeout_total_amount,
      ssm.payway1_id, ssm.payway1_amount ,cs1.sub_type as payway1_type,cs1.sub_name as payway1_sub_name,
      ssm.payway2_id, ssm.payway2_amount ,cs2.sub_type as payway2_type,cs2.sub_name as payway2_sub_name,
      ssm.payway3_id, ssm.payway3_amount ,cs3.sub_type as payway3_type,cs3.sub_name as payway3_sub_name,
      iss.sup_name,
      (case when lower(coalesce(ssm.sheet_attribute->>'bj',''))='true' then '是' else '' end) as bj,
      (case when lower(coalesce(ssm.sheet_attribute->>'free',''))='true' then '是' else '' end) as free
from sheet_sale_main ssm 
left join info_operator io on io.company_id ={companyID} and (case when ssm.getter_id is not null then ssm.getter_id when ({checkAccountBySender} or ssm.order_sheet_id is not null) and ssm.senders_id is not null then split_part(replace( ssm.senders_id,'undefined,',''), ',',1)::int else seller_id end) = io.oper_id 
left join sheet_sale_detail ssd on ssd.company_id = {companyID} and ssm.sheet_id = ssd.sheet_id
left join info_supcust iss on iss.company_id = {companyID} and iss.supcust_id = ssm.supcust_id 
LEFT JOIN cw_subject cs1 ON cs1.sub_id = ssm.payway1_id AND cs1.company_id = {companyID}
LEFT JOIN cw_subject cs2 ON cs2.sub_id = ssm.payway2_id AND cs2.company_id = {companyID}
LEFT JOIN cw_subject cs3 ON cs3.sub_id = ssm.payway3_id AND cs3.company_id = {companyID}
{condi} and ssm.approve_time is not null
group by ssm.company_id,
    ssm.sheet_id,
    ssm.sheet_no,
    ssm.sheet_type,
    ssm.make_brief,
    ssm.order_sheet_id,
    ssm.senders_id,
    ssm.seller_id,
    ssm.now_disc_amount,
    ssm.total_amount,
    ssm.money_inout_flag,
    ssm.now_pay_amount,
    ssm.payway1_id,ssm.payway2_id,ssm.payway3_id,
    payway1_type,payway2_type,payway3_type,
    cs1.sub_type,cs2.sub_type,cs3.sub_type,
    ssm.payway1_amount ,ssm.payway2_amount ,ssm.payway3_amount,
    ssm.sheet_type,
    ssm.getter_id,
    payway1_sub_name,payway2_sub_name,payway3_sub_name,ssm.happen_time,iss.sup_name,ssm.sheet_attribute
";
        }

        private static string GetPreGetSheetList(string companyID, string condi)
        {
            return $@"select sp.company_id,
             sheet_id,
             sheet_no,
             sheet_type,
             total_amount * sp.money_inout_flag as total_amount,
             TO_CHAR(happen_time, 'YYYY-MM-DD') as happen_time,
             happen_time as happen_time_origin,
             sp.getter_id,
             sp.make_brief,
             io.oper_name,
             round((total_amount - now_disc_amount - now_pay_amount)::numeric,2) * sp.money_inout_flag as left_total_amount,
             now_disc_amount * sp.money_inout_flag as now_disc_amount,
             now_pay_amount * sp.money_inout_flag as now_pay_amount,
             sp.payway1_id,
             sp.payway1_amount * sp.money_inout_flag as payway1_amount,
             cs1.sub_type                                      as payway1_type,
             cs1.sub_name                                      as payway1_sub_name,
             sp.payway2_id,
             sp.payway2_amount * sp.money_inout_flag as payway2_amount,
             cs2.sub_type                                      as payway2_type,
             cs2.sub_name                                      as payway2_sub_name,
             sp.payway3_id,
             sp.payway3_amount * sp.money_inout_flag as payway3_amount,
             cs3.sub_type                                      as payway3_type,
             cs3.sub_name                                      as payway3_sub_name,
             iss.sup_name
      from sheet_prepay sp
          left join info_supcust iss on iss.company_id = {companyID} and iss.supcust_id = sp.supcust_id 
               LEFT JOIN cw_subject cs1 ON cs1.sub_id = sp.payway1_id AND cs1.company_id = {companyID}
               LEFT JOIN cw_subject cs2 ON cs2.sub_id = sp.payway2_id AND cs2.company_id = {companyID} 
               LEFT JOIN cw_subject cs3 ON cs3.sub_id = sp.payway3_id AND cs3.company_id = {companyID} 
               left JoIN info_operator io ON io.company_id = {companyID}  AND io.oper_id = sp.getter_id  {condi}  and sp.approve_time is not null";
        }

        private static string GetPrePaySheetList(string companyID, string condi)
        {
            return $@"select sp.company_id,
             sheet_id,
             sheet_no,
             sheet_type,
             total_amount * money_inout_flag as total_amount,
             TO_CHAR(happen_time, 'YYYY-MM-DD') as happen_time,
             happen_time as happen_time_origin,
             sp.getter_id,
             sp.make_brief,
             io.oper_name,
             round((total_amount - now_disc_amount - now_pay_amount)::numeric,2) * money_inout_flag as left_total_amount,
             now_disc_amount * money_inout_flag as now_disc_amount,
             now_pay_amount * money_inout_flag as now_pay_amount,
             sp.payway1_id,
             sp.payway1_amount * money_inout_flag as payway1_amount,
             cs1.sub_type                                      as payway1_type,
             cs1.sub_name                                      as payway1_sub_name,
             sp.payway2_id,
             sp.payway2_amount * money_inout_flag as payway2_amount,
             cs2.sub_type                                      as payway2_type,
             cs2.sub_name                                      as payway2_sub_name,
             iss.sup_name
      from sheet_prepay sp
               left join info_supcust iss on iss.company_id = {companyID} and iss.supcust_id = sp.supcust_id 
               LEFT JOIN cw_subject cs1 ON cs1.sub_id = sp.payway1_id AND cs1.company_id = {companyID}
               LEFT JOIN cw_subject cs2 ON cs2.sub_id = sp.payway2_id AND cs2.company_id = {companyID}
               left JoIN info_operator io ON io.company_id = {companyID} AND io.oper_id = sp.getter_id {condi}  and sp.approve_time is not null ";
        }

        private static string GetGetArrearSheetList(string companyID,string condi)
        {
            return $@"select sgam.company_id,
       sheet_id,
       sheet_no,
       sheet_type, 
       sgam.make_brief,
       round((CASE WHEN cs1.sub_type = 'YS' THEN sgam.payway1_amount ELSE 0 END + CASE WHEN cs2.sub_type = 'YS' THEN sgam.payway2_amount ELSE 0 END + CASE WHEN cs3.sub_type = 'YS' THEN sgam.payway3_amount ELSE 0 END )::numeric,2 ) * sgam.money_inout_flag as preget_total_amount,
       round((now_pay_amount + now_disc_amount)::numeric, 2) * money_inout_flag as total_amount,
       now_disc_amount * money_inout_flag as now_disc_amount,
       now_pay_amount * money_inout_flag  as now_pay_amount,
       getter_id,
       TO_CHAR(happen_time, 'YYYY-MM-DD') as happen_time,
       happen_time as happen_time_origin,
       io.oper_name,
       sgam.payway1_id,
       sgam.payway1_amount  * money_inout_flag as payway1_amount,
       cs1.sub_type                       as payway1_type,
       cs1.sub_name                       as payway1_sub_name,
       sgam.payway2_id,
       sgam.payway2_amount  * money_inout_flag as payway2_amount,
       cs2.sub_type                       as payway2_type,
       cs2.sub_name                       as payway2_sub_name,
       
       sgam.payway3_id,
       sgam.payway3_amount  * money_inout_flag as payway3_amount,
       cs3.sub_type                       as payway3_type,
       cs3.sub_name                       as payway3_sub_name,
       
       iss.sup_name
from sheet_get_arrears_main sgam
    left join info_supcust iss on iss.company_id = {companyID} and iss.supcust_id = sgam.supcust_id
         LEFT JOIN cw_subject cs1 ON cs1.sub_id = sgam.payway1_id AND cs1.company_id = {companyID}
         LEFT JOIN cw_subject cs2 ON cs2.sub_id = sgam.payway2_id AND cs2.company_id = {companyID}
         LEFT JOIN cw_subject cs3 ON cs3.sub_id = sgam.payway3_id AND cs3.company_id = {companyID}
  left JoIN info_operator io ON io.company_id = {companyID} AND io.oper_id = sgam.getter_id {condi}  and sgam.approve_time is not null";
        }

        private static string GetFeeOutSheetList(string companyID, string condi)
        {
            return $@"select sfom.company_id,
       sheet_id,
       sheet_no,
       sheet_type,
       sfom.make_brief,
       TO_CHAR(happen_time, 'YYYY-MM-DD') as happen_time,
       happen_time as happen_time_origin,
       total_amount * money_inout_flag                                       as total_amount,
       now_disc_amount * money_inout_flag                                    as now_disc_amount,
       now_pay_amount * money_inout_flag                                     as now_pay_amount,
       round((total_amount - now_pay_amount)::numeric, 2) * money_inout_flag as left_total_amount,
       getter_id,
       io.oper_name,
       sfom.payway1_id,
       sfom.payway1_amount  * money_inout_flag as payway1_amount,
       cs1.sub_type                                                          as payway1_type,
       cs1.sub_name                                                          as payway1_sub_name,
       sfom.payway2_id,
       sfom.payway2_amount  * money_inout_flag as payway2_amount,
       cs2.sub_type                                                          as payway2_type,
       cs2.sub_name                                                          as payway2_sub_name,
       iss.sup_name
from sheet_fee_out_main sfom
         LEFT JOIN cw_subject cs1 ON cs1.sub_id = sfom.payway1_id AND cs1.company_id = {companyID}
        left join info_supcust iss on iss.company_id = {companyID} and iss.supcust_id = sfom.supcust_id
         LEFT JOIN cw_subject cs2 ON cs2.sub_id = sfom.payway2_id AND cs2.company_id = {companyID}
         LEFT JOIN info_operator io ON io.company_id = {companyID} AND io.oper_id = sfom.getter_id {condi} and sfom.approve_time is not null";
        }

        private static string GetIncomeSheetList(string companyID, string condi)
        {
            return $@"select sfom.company_id,
       sheet_id,
       sheet_no,
       sheet_type,
        total_amount,
       sfom.make_brief,
          now_disc_amount,
    round((total_amount - now_pay_amount - now_disc_amount)::numeric, 2) * money_inout_flag as left_total_amount,
       TO_CHAR(happen_time, 'YYYY-MM-DD') as happen_time,
       happen_time as happen_time_origin,
       now_pay_amount * money_inout_flag                                     as now_pay_amount,
       getter_id,
       io.oper_name,
       sfom.payway1_id,
       sfom.payway1_amount  * money_inout_flag as payway1_amount,
       cs1.sub_type                                                          as payway1_type,
       cs1.sub_name                                                          as payway1_sub_name,
       sfom.payway2_id,
       sfom.payway2_amount  * money_inout_flag as payway2_amount,
       cs2.sub_type                                                          as payway2_type,
       cs2.sub_name                                                          as payway2_sub_name,
       iss.sup_name
from sheet_fee_out_main sfom
         LEFT JOIN cw_subject cs1 ON cs1.sub_id = sfom.payway1_id AND cs1.company_id = {companyID}
        left join info_supcust iss on iss.company_id = {companyID} and iss.supcust_id = sfom.supcust_id 
         LEFT JOIN cw_subject cs2 ON cs2.sub_id = sfom.payway2_id AND cs2.company_id = {companyID}
         LEFT JOIN info_operator io ON io.company_id = {companyID} AND io.oper_id = sfom.getter_id {condi} and sfom.approve_time is not null";
        }

        #endregion
    }
}
