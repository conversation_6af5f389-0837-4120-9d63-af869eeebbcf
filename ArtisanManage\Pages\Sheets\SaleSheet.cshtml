
@page
@model SaleSheetModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html lang="en">

<head>

    <partial name="_MmSheetHead" model="Model.PartialViewModel" />
    @Html.AntiForgeryToken()
    <script>
        var Href = '@Html.Raw(Model.ObsBucketLinkHref)'
    </script>

    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxpopover.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxtooltip.js"></script>
    <script type="text/javascript" src="~/js/ImageDeal.js?v=@Model.PartialViewModel.Version"></script>
    <script type="text/javascript" src="~/Sheet/SheetImport.js?v=@Model.PartialViewModel.Version"></script>
    <script type="text/javascript" src="~/Sheet/SheetExport.js?v=@Model.PartialViewModel.Version"></script>
    <script type="text/javascript" src="~/Sheet/SaleSheet.js?v=@Model.PartialViewModel.Version"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdata.export.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.export.js"></script>


    <script type="text/javascript">
        function isRightClick(event) {
            if (!event) {
                event = window.event;
            }
            if (event.which) {
                return event == 3;
            }
            else if (event.button) {
                return event == 2;
            }
            else {
                return false;
            }
        }
        function attachContextMenu() {
            $('#depart_path').on('mousedown', (event) => {
                if (event.target.tagName == "DIV" && event.target.tagName == "LI") {
                    var target = event.target.parentNode;
                    var contextMeun = event.target.innerText == "全部" ? contextMenu0 : contextMenu1;
                    if (isRightClick(event)) {
                        $("#depart_path").jqxTree('selectItem', target);
                        var scrollTop = $(window).scrollTop();
                        var scrollLeft = $(window).scrollLeft();
                        contextMenu.jqxMenu('open', parseInt(event.clientX) + 5 + scrollLeft, parseInt(event.clientY) + 5 + scrollTop);
                        return false;
                    }
                }
            })
        }
        $(document).ready(() => {

            //attachContextMenu();

            //绑定支付方式改变事件
            var paywayIds = ['payway1_id', 'payway2_id', 'payway3_id'];
            $.each(paywayIds, function (index, id) {
                $('#' + id).on('change', function () {
                    console.log('Selected value for ' + id + ':', $(this).val());
                    var addDom = ''
                    if ('YS' === $(this).val().sub_type) {
                        if (id === "payway1_id") {
                            $('#payway1_amount').val(0)
                            addDom = 'payway1_amount'
                        } else if (id === "payway2_id") {
                            $('#payway2_amount').val(0)
                            addDom = 'payway2_amount'
                        } else {
                            $('#payway3_amount').val(0)
                            addDom = 'payway3_amount'
                        }
                        getPrepayDetail($(this).val().v, function (headerInfo) { })
                        createFeather(addDom, $(this).val().v, id)

                    }
                });
            });
            function updateReceiverMobile(phone) {
                // 1. 客户ID
                // 2.客户地址
                // 3. 查询客户手机
            $('#receiver_mobile').val(phone);
            }
            $('#receive_addr').on('change', function () {
            console.log('123',  $(this).val())
            updateReceiverMobile($(this).val().p)
            })

            $("#prepayDetail").on("cellclick", function (event) {
                var args = event.args;
                var dataField = args.datafield;
                var row = args.rowindex;
                var value = args.value;

                // 点击单号
                if (dataField === 'sheet_no') {
                    var rowData = $('#prepayDetail').jqxGrid('getrowdata', row);
                    if (rowData.sheet_type === 'YS') {
                        window.parent.newTabPage("预收款单", `Sheets/PrepaySheet?sheet_id=${rowData.sheet_id}&forReturn=true`, window);
                    }
                    if (rowData.sheet_type === 'T') {
                        window.parent.newTabPage("退货单", `Sheets/SaleSheet?sheet_id=${rowData.sheet_id}&forReturn=true`, window);
                    }
                }
            })

            let sheetType = $('#sheetType').val();
            if (sheetType == 'X') {
                $('#fee_btn').css('display', 'flex');//仅销售单允许分摊费用
            }
          
            $("#payway1_amount").on('input', function () {
                input_amount("#payway1_amount", false)
            })
            $("#payway2_amount").on('input', function () {
                input_amount("#payway2_amount", false)
            })
            $("#payway3_amount").on('input', function () {
                input_amount("#payway3_amount", false)
            })



            $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                return true;
            });


            $("#popoverCopy").jqxPopover({ showArrow: false, autoClose: false, offset: { left: 0, top: 10 }, position: "top", title: "", showCloseButton: false, selector: $("#btnCopyChooseSheets") });
            $("#popoverMore").jqxPopover({ showArrow: false, autoClose: false, offset: { left: 0, top: 10 }, position: "top", title: "", showCloseButton: false, selector: $("#btnMoreChooseSheets") });

            });
            function choosetemplate() {
                var maskBg = document.getElementById('topCoverDiv');
                var dia = document.getElementById('dia');
                maskBg.style.display = (maskBg.style.display == 'none') ? 'block' : 'none';
                dia.style.display = (dia.style.display == 'none') ? 'block' : 'none';
            }

            function getPrintTemplate() {
                $('.choosetemplate').css('display', 'block');
                //maskBg.style.display('block');
            }

            function hidePrintTemplate() {
                $('.choosetemplate').css('display', 'none');
                //var maskBg = document.getElementsByClassName('choosetemplate');
                //maskBg.style.display('none');
            }
    </script>
    <style>
        .choosetemplate {
            display: none;
            position: absolute;
            top: 748px;
            left: 980px;
            width: 100px;
            height: 25px;
            text-align: center;
            font-size: 14px;
            overflow: hidden;
            border: #e2e2e2 1px solid;
        }

        .svgfeather {
            position: absolute;
            right: 3px;
            bottom: 4px;
            z-index: 99999;
            cursor: pointer;
        }
        #topCoverDiv {
            opacity: 0.4;
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0px;
            background-color: #000;
            z-index: 100;
            text-align: center;
        }

        #dia {
            background: rgba(255, 255, 255, 1);
            z-index: 200;
            position: absolute;
            top: 50%;
            left: 50%;
            width: 300px;
            height: 250px;
            margin: -200px 0 0 -250px;
            border-radius: 10px;
            border-top-width: 10px;
            padding: 10px 10px 10px 10px;
        }

        .show_close {
            height: 20px;
            width: 20px;
            margin-left: 480px;
            cursor: pointer;
            display: inline-block;
            float: right;
        }

        #rp:hover {
            display: block;
        }

        #payway1 {
            overflow-y: auto;
        }

        #uploadButton {
            transition-duration: 0.4s;
            background-color: #f0f9f0;
        }

            #uploadButton:hover {
                background-color: #e2eee8
            }

        #changeClientAlertBox {
            width: 300px;
            height: 200px;
            z-index: 9999;
            position: absolute;
            left: 50%;
            top: 50%;
            margin-left: -150px;
            margin-top: -100px;
            border: 0.5px solid black;
            border-radius: 10px;
            background-color: white
        }

        .jqx-popover {
            border-color: #e2e2e2;
            border-radius: 20px;
            box-shadow: 20px 20px 50px 0px rgba(0, 0, 0, 0.25);
        }

        [v-cloak] {
            display: none;
        }

        .no-dialog-no-close .el-dialog__headerbtn {
            display: none;
        }
    </style>
</head>

<body class='default' style="overflow:hidden;">

    <div id="changeClientAlertBox" style="display:none;">
        <div style="height:30px;background-color: gray ;border-radius:6px 6px 0 0">
        </div>
        <div style="box-sizing:border-box;padding-left:50px;padding-top:20px">
            <div>
                <input type="radio" name="changeClient" id="isRefresh" checked="checked" value="true"> <label for="isRefresh">切换客户时刷新价格</label>
            </div>
            <hr>
            <div>
                <input type="radio" name="changeClient" id="isNotRefresh" value="false"> <label for="isNotRefresh">切换客户时不刷新价格</label>
            </div>
        </div>
        <div style="height:30px"></div>
        <div style="text-align:center">
            <button id="cancle">取消</button>
            <button id="confirm">确认</button>
        </div>

    </div>
    <div id="divTitle" style="text-align:center;height:45px;margin-top:5px;">
        <label id="lblSheetTitle" style="font-weight:500;font-size:25px;">@Html.Raw(Model.SheetTitle)</label>
        <img id="imgState" style="display:none;position:fixed;top:0px;left:calc(50% - 150px);" src="" />
        <div id="orderSourceId" class="makeInfo" style="position:absolute;top:15px; right:100px">
            <div style="font-size: 16px !important; font-weight: bolder">
                <div id="orderSourceText" style="color: #C61848"></div>
                <div id="payBillStatusText" style="color: #C61848"></div>
            </div>
        </div>
        <div class="makeInfo" style="position:absolute;top:5px; right:0px;">
            <div>
                <div><label>单号:</label></div>
                <div id="sheet_no"></div>
            </div>
            <div id="getOrder">
                <div><label>订单:</label></div>
                <div><span id="order_sheet_id" style="display: none;"></span><span id="order_sheet_no"></span></div>
            </div>
            <div>
                <div><label>制单:</label></div>
                <div><span id="make_time"></span><span id="maker_name"></span></div>
            </div>
            <div>
                <div><label>审核:</label></div>
                <div><span id="approve_time"></span><span id="approver_name"></span></div>
            </div>
            <div>
                <div><label>复核:</label></div>
                <div><span id="review_time"></span><span id="reviewer_name"></span></div>
            </div>
        </div>
    </div>

    <div id="divHead" class="headtail" style="margin-bottom:10px;margin-top:0px;">
        <div style="float:none;height:0px; clear:both;"></div>
    </div>

    <div id="jqxgrid" style="margin-left:10px; position:static;width:100%;height:100%;border-bottom-color:#dedede;"></div>

    <div style="display:flex;">
        <div id="divTail" class="headtail" style="margin-top:10px;">

            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <div id="fee_btn" onclick="ShowFeeBox()" style="display:none;cursor:pointer;border-bottom:1px solid #595857;margin:18px 20px 10px 0;color:#595857;font-size:14px;">销售费用：0</div>
        <div style="display:flex;max-width:250px;margin-top:15px; height:20px;overflow-x:auto;overflow-y:hidden;position:relative;">
            <div id="div_get_account" style="display:block;max-width:200px;width:auto;height:auto;">
            </div>
            <div id="btnMoreAcctInfo" style="display:none;cursor:pointer;font-size:14px; margin-top:1px; top:0px; right:0px;width:50px;height:30px;color:#46f;">
                更多
            </div>
        </div>
    </div>

    <div id="divButtons" style="text-align:center;">
        <button id="btnSave" type="button">保存</button>
        <button id="btnApprove" type="button" class="main-button">审核</button>
        <button id="btnReview" type="button" style="margin-right:40px;background-color:#ffc8e0;" class="main-button">复核</button>
        <button id="btnRedAndChange" type="button" style="margin-right:40px;">冲改</button>
        <button id="btnRed" type="button" disabled>红冲</button>
        <button id="btnDelete" type="button" disabled>删除</button>
        @* <button id="btnMemorySetting" type="button">记忆设置</button> *@
        <partial name="copyToSheets" />
        <button id="btnAdd" type="button">新增</button>

        @if (Model.EnablePiaoZhengTong)
        {
            <button id="btnSyncAccessTicketSys" onclick="UploadSheetToPiaoZhengTong()" type="button">传票证通</button>
        }
        <button id="btnPrint" type="button" style="margin-right: 0px; border-right: none;border-radius: 3px 0px 0px 3px">打印</button>
        <button id="choosetemplate" class="btnright" onmouseover="getPrintTemplate()">
            <img src="~/PrintTemplate/img/triangle.svg" style="margin-top: -1px; width: 14px; display: inline-block;vertical-align: middle;" />
        </button>

        <button id="btnMore" type="button" style="margin-right: 0px; border-right: none;border-radius: 3px 0px 0px 3px">更多</button>
        <button id="btnMoreChooseSheets" class="btnright">
            <img src="~/PrintTemplate/img/triangle.svg" style="margin-top: -1px; width: 14px; display: inline-block;vertical-align: middle;" />
        </button>
        <div id="popoverMore" style="position:absolute;display:none;font-size:15px;">
            <div id="ImportExportContainer" style="width:100px;height:150px;">
                <div style="margin:10px;">
                    <button style="border:none;margin-top:10px;" onclick="btnShowImportDlg_click()">导入</button>
                    @* <button style="border:none;margin-top:10px;" onclick="btnExportExcel()">导出</button> *@
                    <button id="btnExport" style="border:none;margin-top:10px;" onclick="btnSaleSheetExportExcel_click()">导出</button>
                    @*<button style="border:none;margin-top:10px;" onclick="btnItemInfoSyncTest_click()">商品档案同步测试</button>*@
                    <button id="btnAppendix" type="button" style="border:none;margin-top:10px;">附件</button>
                    <button id="btnMemorySetting" type="button" style="border:none;margin-top:10px;">记忆设置</button>
                </div>
            </div>
        </div>
        <button id="btnClose" type="button">关闭</button>
        @*      <button id = "btnNextSheet" type="button">下一张</button>
        <button id = "btnpreviousSheet" type="button">上一张</button>*@
    </div>


    <div id="popClient" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;">
            <span style="font-size:20px;">选择客户</span>
        </div>
        <div style="overflow:hidden;"> </div>
    </div>
    <div id="popItem" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;">
            <span style="font-size:20px;">选择商品</span>
        </div>
        <div style="overflow:hidden;"> </div>
    </div>
    <div id="popAppendix" style="display:none;">
        <div id="appendixCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">上传附件</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    <div id="popItemHistory" style="display:none">
        <div id="historyCaption" style="height:30px;background-color:#fff; text-align:center;">
            <span style="font-size:20px;">历史记录</span>
        </div>
        <div style="overflow:hidden;"> </div>
    </div>
    <div id="popSelectItem" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;">
            <span style="font-size:20px;">选择商品</span>
        </div>
        <div id="divItemsToSelect" style="overflow:hidden;">
        </div>
    </div>

    <div id="prepayDialog" style="display:none">
        <div id="DetailTop" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:16px;">预收款单</span></div>
        
        <div id="DetailCenter" style="width: 429px;">
            <div id="prepayDetail" style=""></div>
            <div id="operate" style="">
                <button id="DialogClose" type="button" onclick="closeDialog()">关闭</button>
                <button id="DialogConfirm" type="button" onclick="btnReturnVanConfirm_click()">确认</button>
            </div>
        </div>
        
    </div>
    <div id="popImportItems" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:16px;">导入表单</span></div>
        <div id="drag-field" style="overflow:hidden;display:flex; flex-direction:column;align-items:center;justify-content:center;"
        @*contentEditable="true"*@
             ondragenter="drag(this, event);"
             ondragover="drag(this, event);"
             ondrop="dragUpload(this, event)"
             onpaste="pasteUpload(this, event);"
             onmouseenter="handleMouseEnter(this)"
             onmouseleave="handleMouseLeave(this)">
            <style>
                svg:hover {
                    fill: #f00;
                }

                #import-excel {
                    fill: #A3D1C1;
                }

                    #import-excel:hover {
                        fill: #88CDAA;
                    }

                #import-image {
                    fill: #88BBDD;
                }

                    #import-image:hover {
                        fill: #5599DD;
                    }

                #import-pdf {
                    fill: #999999;
                }

                    #import-pdf:hover {
                        fill: #666666;
                    }

                #import-camera {
                    fill: #999999;
                }

                    #import-camera:hover {
                        fill: #666666;
                    }

                #import-scanner {
                    fill: #dd99aa;
                    transition: 0.5s;
                }

                    #import-scanner:hover {
                        fill: #dd6699;
                    }

                .svg-wrapper:after {
                    content: "扫描";
                    display: block;
                    text-align: center;
                    width: 100%;
                    height: 20px;
                    color: #fff;
                    position: absolute;
                    left: 0px;
                    transition: 0.5s;
                }

                .svg-wrapper:hover:after {
                    color: #555;
                }

                #div-excel:after {
                    content: "EXCEL";
                }

                #div-image:after {
                    content: "图片";
                }

                #div-pdf:after {
                    content: "PDF";
                }

                #div-camera:after {
                    content: "拍照";
                }

                #div-scanner:after {
                    content: "扫描";
                }

                #myCanvas {
                    width: 100%;
                }

                #catchBtn {
                    width: 100px;
                    height: 50px;
                    margin-top: 10px;
                    margin-left: 40%;
                }

                #reCatchBtn {
                    width: 100px;
                    height: 50px;
                    margin-top: 10px;
                    margin-left: 40%;
                }

                #DetailCenter {
                    overflow : auto;
                    margin: 0px 50px 0px 50px;
                    height: 199px;
                    width: 700px !important;
                    
                }

                #DetailCenter #operate {
                    float: right;
                    margin: 10px 35px 0 0 !important;
                }

                #DetailCenter::-webkit-scrollbar { /* 设置滚动条 */
                    width: 4px;
                }

                .prepay-remark-cell {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .prepay-remark {
                    position: relative;
                    display: inline-block;
                }

                    .prepay-remark .prepay-remark-text {
                    visibility: hidden;
                    width: 120px;
                    background-color: #555;
                    color: #fff;
                    text-align: center;
                    padding: 5px 0;
                    border-radius: 6px;
                    position: absolute;
                    z-index: 1;
                    bottom: 125%;
                    left: 50%;
                    margin-left: -60px;
                    opacity: 0;
                    transition: opacity 0.3s;
                }

                    .prepay-remark:hover .prepay-remark-text {
                    visibility: visible;
                    opacity: 1;
                }

                
            </style>
            <!--模型选择-->
            <!--<div id="selector-div" style="display:flex;width:inherit;height:40px;margin-top:0px;justify-content:left">
                <select id="selector" name="selector" style="float:left">
                    <option value="ocr">OCR</option>
                    <option value="gpt-4o">GPT-4o</option>
                    <option value="kimi">Kimi</option>
                </select>
            </div>-->
            <div id="test" style="display:flex;width:300px;height:80px;margin-top:20px;justify-content:space-around;">

                <div class="svg-wrapper" id="div-excel" style="position:relative;">
                    <svg id="import-excel" onclick="btnImportItems_excel_click()" height="48" width="48" style="cursor:pointer">
                        <use xlink:href="/images/import/import.svg#excel" />
                    </svg>
                </div>

                <div class="svg-wrapper" id="div-image" style="position:relative;">
                    <svg id="import-image" onclick="btnImportItems_image_click()" height="48" width="48" style="cursor:pointer">
                        <use xlink:href="/images/import/import.svg#image" />
                    </svg>
                </div>
                <div class="svg-wrapper" id="div-pdf" style="position:relative;">
                    <svg id="import-pdf" onclick="btnImportItems_pdf_click()" height="44" width="48" style="cursor:pointer">
                        <use xlink:href="/images/import/import.svg#pdf" />
                    </svg>
                </div>
            </div>

            @*            <div style="display: flex; width: 180px; height: 100px; position: relative; justify-content: space-around; ">
            <div class="svg-wrapper" id="div-camera" style="position:relative;">
            <svg id="import-camera" onclick="btnImportItems_camera_click()" height="48" width="48" style="cursor:pointer" fill="#dddddd">
            <use xlink:href="/images/import/import.svg#camera" />
            </svg>
            </div>
            <div class="svg-wrapper" id="div-scanner" style="position:relative;">
            <svg id="import-scanner" onclick="btnImportItems_scanner_click()" height="48" width="48" style="display:block; cursor:pointer;position:relative;" fill="#dddddd">
            <use xlink:href="/images/import/import.svg#scanner" />
            </svg>
            </div>

            </div>*@
            <input type="file" name="file" id="dragfile" style="display: none" onchange="fileChange(this, event);" />
            @*display: none ;position: absolute;top: -9999px;opacity: 0*@
            <div id="importHeader" style="margin:20px"></div>
            <span style="color:#808080">支持拖拽上传图片、表格（.xlsx .xls)、PDF文件</span>
        </div>

    </div>
    <div id="quickCreateItems" style="display:none">
        <div id="createItemHeader" style="height: 30px; text-align: center; margin-bottom:10px">
            <span style="font-size: 18px;margin-inline-start:1.2em">销售单导入</span>
        </div>
        <div id="createItemContent" style="overflow:hidden;display:flex; flex-direction:column;align-items:center;justify-content:center;">

            <div id="createItemHint" style="height: 20px; background-color: #fff; text-align: center;margin-bottom:15px">
                <span style="font-size: 18px;">以下商品未能成功匹配，请校对</span>
            </div>

            @*<div id="newAddedItemsTable" style="overflow: auto;">
            <!-- jqxGrid 将在这个 div 中显示 -->
            必须每次动态创建，关闭时销毁，不然多次导入数据有错
            </div>*@
            <div id="gridCheckHint" style="margin-top: 15px; color:#ff7700; display:none">
                @*display:none*@
                多少行多少列你还没填
            </div>
            <div id="createItemBtnGroup" style="padding: 20px; text-align: center; display: flex; justify-content: center; align-items: center;">
                <div style="width:120px"></div>
                <button type="button" id="cancelButton" style="margin-right: 30px;">取消</button>
                <button type="button" id="uploadButton" style="margin-right: 20px;">确定</button>
                <div style="display: flex; align-items: center;width:120px">
                    <input type="checkbox" id="memoryCheck" style="margin-right: 5px;">
                    <label for="checkbox">开启匹配记忆</label>
                </div>
            </div>

        </div>
    </div>
    <div id="popFee" style="display:none">
        <div id="feeCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;"></span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    <!-- 记忆设置对话框 -->
    <div id="memorySettingDialog" style="display:none;">
        <div>
@*             <div style="height:30px;background-color: gray;border-radius:6px 6px 0 0">
                <span style="color:white;line-height:30px;margin-left:10px;font-weight:bold;">当前设置：销售单</span>
            </div> *@
            <div style="padding:10px;">
                <div id="memorySettingGrid"></div>
            </div>
            <div style="text-align:center;padding:10px 0 20px 0;">
                <button id="saveMemorySetting">保存设置</button>
                <button id="cancelMemorySetting" style="margin-left:20px;">取消</button>
            </div>
        </div>
    </div>

    <!-- 多维度记忆选择对话框 -->
    <div id="multiMemoryDialog" style="display:none;">
        <div>
            <div style="padding:20px;">
                <h4 style="margin-bottom:20px;">选择记忆维度</h4>
                <div style="margin-bottom:15px;">
                    <label><input type="checkbox" id="memoryByCustomer" value="customer" /> 客户</label>
                </div>
                <div style="margin-bottom:15px;">
                    <label><input type="checkbox" id="memoryByBusinessType" value="businessType" /> 业务类型</label>
                </div>
                <div style="margin-bottom:15px;">
                    <label><input type="checkbox" id="memoryByMaker" value="maker" /> 制单人</label>
                </div>
                <div style="margin-bottom:15px;">
                    <label><input type="checkbox" id="memoryByDepartment" value="department" /> 部门</label>
                </div>
                <div style="margin-bottom:15px;">
                    <label><input type="checkbox" id="memoryByDirection" value="direction" /> 单据方向</label>
                </div>
            </div>
            <div style="text-align:center;padding:10px 0 20px 0;">
                <button id="confirmMultiMemory">确定</button>
                <button id="cancelMultiMemory" style="margin-left:20px;">取消</button>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // 初始化记忆设置对话框
            $("#memorySettingDialog").jqxWindow({
                width: 750,
                height: 530,
                resizable: false,
                isModal: true,
                autoOpen: false,
                modalOpacity: 0.3,
                title:"默认抬头设置",
                showCloseButton: true
            });

            // 初始化多维度记忆选择对话框
            $("#multiMemoryDialog").jqxWindow({
                width: 400,
                height: 350,
                resizable: false,
                isModal: true,
                autoOpen: false,
                modalOpacity: 0.3,
                title:"选择记忆维度",
                showCloseButton: true
            });
            
            //记忆设置表格数据源
            var memorySettingSource = {
                datatype: "array",
                datafields: [
                    { name: 'id', type: 'number' },
                    { name: 'fieldName', type: 'string' },
                    { name: 'fieldDesc', type: 'string' },
                    { name: 'memoryType', type: 'string' },
                    { name: 'memoryDimensions', type: 'string' }  // 改为string类型，存储JSON字符串
                ],
                localdata: [
                    { id: 1, fieldName: 'supcust_id', fieldDesc: '客户', memoryType: 'none' },
                    { id: 2, fieldName: 'seller_id', fieldDesc: '业务员', memoryType: 'none' },
                    { id: 3, fieldName: 'sender_id', fieldDesc: '送货员', memoryType: 'none' },
                    { id: 4, fieldName: 'getter_id', fieldDesc: '收款人', memoryType: 'none' },
                    { id: 5, fieldName: 'branch_id', fieldDesc: '仓库', memoryType: 'none' },
                    { id: 6, fieldName: 'happen_time', fieldDesc: '交易时间', memoryType: 'none' },
                    { id: 7, fieldName: 'make_brief', fieldDesc: '备注', memoryType: 'none' },
                    { id: 8, fieldName: 'default_unit', fieldDesc: '默认单位', memoryType: 'none' },
                    { id: 9, fieldName: 'mobile', fieldDesc: '客户电话', memoryType: 'none' },
                    { id: 10, fieldName: 'receiver_mobile', fieldDesc: '收货人电话', memoryType: 'none' },
                    { id: 11, fieldName: 'sup_addr', fieldDesc: '客户地址', memoryType: 'none' },
                    { id: 12, fieldName: 'payway1_id', fieldDesc: '支付方式', memoryType: 'none' },
                ],
            };
            
            var memorySettingAdapter = new $.jqx.dataAdapter(memorySettingSource);
            
            // 创建记忆设置表格
            $("#memorySettingGrid").jqxGrid({
                width: '100%',
                height: 380,
                source: memorySettingAdapter,
                columnsresize: true,
                columns: [
                    { text: '序号', datafield: 'id', width: 50, cellsalign: 'center', align: 'center' },
                    { text: '列名', datafield: 'fieldDesc', width: 150, cellsalign: 'center', align: 'center' },
                    {
                        text: '不记忆',
                        datafield: 'none',
                        width: 100,
                        cellsalign: 'center',
                        align: 'center',
                        cellsrenderer: function(row, columnfield, value, defaulthtml, columnproperties, rowdata) {
                            console.log('不记忆列渲染: row=' + row + ', rowdata.id=' + rowdata.id + ', fieldDesc=' + rowdata.fieldDesc + ', memoryType=' + rowdata.memoryType);

                            return '<div style="text-align: center; margin-top: 8px;">' +
                                   '<input type="radio" name="memory_' + rowdata.id + '" value="none" ' +
                                   'data-debug-id="' + rowdata.id + '" data-debug-desc="' + rowdata.fieldDesc + '" ' +
                                   (rowdata.memoryType === 'none' ? 'checked' : '') +
                                   ' onclick="event.stopPropagation(); console.log(\'点击不记忆: 期望ID=' + rowdata.id + ', 描述=' + rowdata.fieldDesc + '\'); safeUpdateMemoryType(' + rowdata.id + ', \'none\'); return false;" />' +
                                   '</div>';
                        }
                    },
                    {
                        text: '按单记忆',
                        datafield: 'single',
                        width: 100,
                        cellsalign: 'center',
                        align: 'center',
                        cellsrenderer: function(row, columnfield, value, defaulthtml, columnproperties, rowdata) {
                            console.log('按单记忆列渲染: row=' + row + ', rowdata.id=' + rowdata.id + ', fieldDesc=' + rowdata.fieldDesc);

                            return '<div style="text-align: center; margin-top: 8px;">' +
                                   '<input type="radio" name="memory_' + rowdata.id + '" value="single" ' +
                                   'data-debug-id="' + rowdata.id + '" data-debug-desc="' + rowdata.fieldDesc + '" ' +
                                   (rowdata.memoryType === 'single' ? 'checked' : '') +
                                   ' onclick="event.stopPropagation(); console.log(\'点击按单记忆: 期望ID=' + rowdata.id + ', 描述=' + rowdata.fieldDesc + '\'); safeUpdateMemoryType(' + rowdata.id + ', \'single\'); return false;" />' +
                                   '</div>';
                        }
                    },
                    {
                        text: '按多维度记忆',
                        datafield: 'multi',
                        width: 280,
                        cellsalign: 'left',
                        align: 'left',
                        cellsrenderer: function(row, columnfield, value, defaulthtml, columnproperties, rowdata) {
                            // 调试信息
                            console.log('cellsrenderer被调用 - 行ID:', rowdata.id, '数据:', rowdata);

                            // 添加调试信息
                            console.log('多维度记忆列渲染: row=' + row + ', rowdata.id=' + rowdata.id + ', fieldDesc=' + rowdata.fieldDesc);

                            var radioButton = '<input type="radio" name="memory_' + rowdata.id + '" value="multi" ' +
                                             'data-debug-id="' + rowdata.id + '" data-debug-desc="' + rowdata.fieldDesc + '" ' +
                                             (rowdata.memoryType === 'multi' ? 'checked' : '') +
                                             ' onclick="event.stopPropagation(); console.log(\'点击多维度记忆: 期望ID=' + rowdata.id + ', 描述=' + rowdata.fieldDesc + '\'); selectMultiMemoryType(' + rowdata.id + '); return false;" />';

                            var dimensionText = '';
                            if (rowdata.memoryType === 'multi' && rowdata.memoryDimensions) {
                                console.log('渲染维度文本 - 行ID:', rowdata.id, '维度字符串:', rowdata.memoryDimensions);

                                try {
                                    // 解析JSON字符串
                                    var dimensions = [];
                                    if (typeof rowdata.memoryDimensions === 'string') {
                                        dimensions = JSON.parse(rowdata.memoryDimensions);
                                    } else if (Array.isArray(rowdata.memoryDimensions)) {
                                        dimensions = rowdata.memoryDimensions;
                                    }

                                    if (dimensions && dimensions.length > 0) {
                                        var dimensionNames = {
                                            'customer': '客户',
                                            'businessType': '业务类型',
                                            'maker': '制单人',
                                            'department': '部门',
                                            'direction': '单据方向'
                                        };
                                        var selectedNames = dimensions.map(function(dim) {
                                            return dimensionNames[dim] || dim;
                                        });
                                        dimensionText = ' <small style="color: #007bff; font-weight: bold; background: #e3f2fd; padding: 2px 6px; border-radius: 3px; font-size: 12px; margin-left: 8px;">' + selectedNames.join('+') + '</small>';

                                        console.log('维度文本已生成:', dimensionText);
                                    }
                                } catch (parseError) {
                                    console.error('解析维度数据失败:', parseError, rowdata.memoryDimensions);
                                }
                            } else {
                                console.log('无维度文本 - 行ID:', rowdata.id, 'memoryType:', rowdata.memoryType, 'dimensions:', rowdata.memoryDimensions);
                            }

                            var result = '<div style="text-align: left; padding: 8px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">' +
                                        radioButton + dimensionText +
                                        '</div>';

                            console.log('cellsrenderer返回结果 - 行ID:', rowdata.id, '结果:', result);
                            return result;
                        }
                    }
                ]
            });
            
            // 打开记忆设置对话框
            $("#btnMemorySetting").click(function() {
                // 加载当前设置
                console.log("加载当前设置");
                loadMemorySettings();
                $("#memorySettingDialog").jqxWindow('open');
            });
            
            // 保存记忆设置
            $("#saveMemorySetting").click(function() {
                console.log("保存记忆设置");
                saveMemorySettings();
                $("#memorySettingDialog").jqxWindow('close');
            });
            
            // 取消记忆设置
            $("#cancelMemorySetting").click(function() {
                console.log("取消记忆设置");
                $("#memorySettingDialog").jqxWindow('close');
            });

            // 监听保存按钮事件 - 保存抬头信息到记忆
            $(document).on('click', '#btnSave, .btn-save, [data-action="save"]', function() {
                console.log('检测到保存按钮点击，准备保存抬头记忆');
                // 延迟执行，确保保存操作完成后再保存记忆
                setTimeout(function() {
                    saveHeaderToMemory();
                }, 1000);
            });

            // 监听新增按钮事件 - 从记忆加载抬头信息
            $(document).on('click', '#btnNew, #btnAdd, .btn-new, .btn-add, [data-action="new"], [data-action="add"]', function() {
                console.log('检测到新增按钮点击，准备加载抬头记忆');
                // 延迟执行，确保新增操作完成后再加载记忆
                setTimeout(function() {
                    loadHeaderFromMemory();
                }, 500);
            });

            // 防重复加载标记
            var memoryLoadedForThisSession = false;

            // 检测是否为新增模式并自动加载记忆设置
            function checkAndLoadMemoryForNewSheet() {
                // 防止重复加载
                if (memoryLoadedForThisSession) {
                    console.log('本次会话已加载过抬头记忆，跳过重复加载');
                    return;
                }

                // 方法1：检查URL中是否没有sheet_id参数（新增模式）
                var urlParams = new URLSearchParams(window.location.search);
                var sheetId = urlParams.get('sheet_id');
                var isNewSheet = !sheetId || sheetId === '' || sheetId === '0';

                // 方法2：检查页面中的sheet_id字段值
                var pageSheetId = $('#sheet_id').val();
                var isNewSheetByPageValue = !pageSheetId || pageSheetId === '' || pageSheetId === '0';

                // 方法3：检查URL关键字
                var hasNewKeyword = window.location.href.indexOf('new') > -1 || window.location.href.indexOf('add') > -1;

                console.log('新增模式检测:');
                console.log('  URL中sheet_id:', sheetId, '-> 是新增:', isNewSheet);
                console.log('  页面sheet_id:', pageSheetId, '-> 是新增:', isNewSheetByPageValue);
                console.log('  URL关键字检测:', hasNewKeyword);

                if (isNewSheet || isNewSheetByPageValue || hasNewKeyword) {
                    console.log('✅ 检测到新增模式，准备加载抬头记忆');
                    memoryLoadedForThisSession = true; // 标记已加载
                    setTimeout(function() {
                        loadHeaderFromMemory();
                    }, 1500); // 增加延迟确保页面完全加载
                } else {
                    console.log('ℹ️ 非新增模式，跳过抬头记忆加载');
                }
            }

            // 页面加载完成后检测新增模式
            setTimeout(function() {
                checkAndLoadMemoryForNewSheet();
            }, 2000); // 确保页面完全初始化后再检测

            // 监听页面可见性变化，当页面变为可见时也检查新增模式
            document.addEventListener('visibilitychange', function() {
                if (!document.hidden) {
                    console.log('页面变为可见，检查是否需要加载抬头记忆');
                    setTimeout(function() {
                        checkAndLoadMemoryForNewSheet();
                    }, 500);
                }
            });

            // 监听窗口焦点事件
            window.addEventListener('focus', function() {
                console.log('窗口获得焦点，检查是否需要加载抬头记忆');
                setTimeout(function() {
                    checkAndLoadMemoryForNewSheet();
                }, 500);
            });

            // 确定多维度记忆选择
            $("#confirmMultiMemory").click(function() {
                console.log('=== 确定按钮被点击 ===');

                var selectedDimensions = [];
                $("#multiMemoryDialog input[type='checkbox']:checked").each(function() {
                    selectedDimensions.push($(this).val());
                });

                console.log('用户选择的维度:', selectedDimensions);

                if (selectedDimensions.length === 0) {
                    alert("请至少选择一个记忆维度！");
                    return;
                }

                // 更新当前行的记忆类型为多维度，并保存选择的维度
                var currentRowId = $("#multiMemoryDialog").data('currentRowId');
                console.log('当前行ID:', currentRowId);

                // 调用新的安全更新函数
                safeUpdateMemoryType(currentRowId, 'multi', selectedDimensions);

                $("#multiMemoryDialog").jqxWindow('close');

                // 显示选择的维度信息
                var dimensionNames = {
                    'customer': '客户',
                    'businessType': '业务类型',
                    'maker': '制单人',
                    'department': '部门',
                    'direction': '单据方向'
                };
                var selectedNames = selectedDimensions.map(function(dim) {
                    return dimensionNames[dim];
                }).join('+');

                console.log('维度显示名称:', selectedNames);
                //alert("已选择记忆维度：" + selectedNames);

                // 验证更新结果
                setTimeout(function() {
                    console.log('=== 验证更新结果 ===');
                    var allRowsAfterUpdate = $("#memorySettingGrid").jqxGrid('getrows');
                    console.log('更新后的所有行数据:', allRowsAfterUpdate);

                    var targetRow = null;
                    for (var i = 0; i < allRowsAfterUpdate.length; i++) {
                        if (allRowsAfterUpdate[i].id == currentRowId) {
                            targetRow = allRowsAfterUpdate[i];
                            break;
                        }
                    }
                    console.log('目标行的最终数据:', targetRow);
                }, 500);
            });

            // 取消多维度记忆选择
            $("#cancelMultiMemory").click(function() {
                $("#multiMemoryDialog").jqxWindow('close');
            });
            
            // 加载记忆设置
            console.log("加载记忆设置");
            function loadMemorySettings() {
                $.ajax({
                    url: "/api/SaleSheet/GetMemorySettings",
                    type: "GET",
                    data: { operKey: g_operKey },
                    success: function(response) {
                        console.log('加载记忆设置响应:', response);
                        if (response.result === "OK" && response.settings) {
                            var settings = response.settings;
                            var data = $("#memorySettingGrid").jqxGrid('getrows');
                            console.log('当前表格数据:', data);
                            console.log('服务器设置:', settings);

                            // 更新数据
                            for (var i = 0; i < data.length; i++) {
                                var fieldName = data[i].fieldName;
                                if (settings[fieldName]) {
                                    var setting = settings[fieldName];
                                    console.log('处理字段:', fieldName, '设置:', setting);

                                    // 更新记忆类型
                                    if (setting.type) {
                                        data[i].memoryType = setting.type;
                                    }

                                    // 更新多维度数据
                                    if (setting.type === 'multi' && setting.dimensions) {
                                        if (typeof setting.dimensions === 'string') {
                                            data[i].memoryDimensions = setting.dimensions;
                                        } else {
                                            data[i].memoryDimensions = JSON.stringify(setting.dimensions);
                                        }
                                    } else {
                                        data[i].memoryDimensions = null;
                                    }
                                }
                            }

                            // 重建数据源以确保更新生效
                            var newSource = {
                                datatype: "array",
                                datafields: [
                                    { name: 'id', type: 'number' },
                                    { name: 'fieldName', type: 'string' },
                                    { name: 'fieldDesc', type: 'string' },
                                    { name: 'memoryType', type: 'string' },
                                    { name: 'memoryDimensions', type: 'string' }
                                ],
                                localdata: data
                            };

                            $("#memorySettingGrid").jqxGrid({ source: new $.jqx.dataAdapter(newSource) });
                            console.log('记忆设置加载完成');
                        } else {
                            console.log('没有找到记忆设置或加载失败');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('加载记忆设置失败:', error);
                    }
                });
            }
            
            // 保存记忆设置
            function saveMemorySettings() {
                console.log('=== 开始保存记忆设置 ===');
                var data = $("#memorySettingGrid").jqxGrid('getrows');
                console.log('当前表格数据:', data);

                var settings = {};

                for (var i = 0; i < data.length; i++) {
                    var rowData = data[i];
                    console.log('处理行数据:', rowData);

                    var setting = {
                        type: rowData.memoryType || 'none'
                    };

                    // 如果是多维度记忆，保存选择的维度
                    if (rowData.memoryType === 'multi' && rowData.memoryDimensions) {
                        try {
                            if (typeof rowData.memoryDimensions === 'string') {
                                setting.dimensions = JSON.parse(rowData.memoryDimensions);
                            } else {
                                setting.dimensions = rowData.memoryDimensions;
                            }
                            console.log('多维度设置:', setting.dimensions);
                        } catch (e) {
                            console.error('解析多维度数据失败:', e);
                            setting.dimensions = [];
                        }
                    }

                    settings[rowData.fieldName] = setting;
                    console.log('字段设置:', rowData.fieldName, setting);
                }

                console.log('最终保存的设置:', settings);

                $.ajax({
                    url: "/api/SaleSheet/SaveMemorySettings",
                    type: "POST",
                    contentType: "application/json",
                    data: JSON.stringify({
                        operKey: g_operKey,
                        settings: settings
                    }),
                    success: function(response) {
                        console.log('保存响应:', response);
                        if (response.result === "OK") {
                            alert("记忆设置保存成功！");
                            $("#memorySettingDialog").jqxWindow('close');
                        } else {
                            alert("保存失败：" + (response.msg || '未知错误'));
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('保存请求失败:', error);
                        alert("保存失败：网络错误");
                    }
                });
            }

            // 保存销售单时，保存抬头信息到记忆设置
            function saveHeaderToMemory() {
                console.log('=== 保存抬头信息到记忆设置 ===');

                // 获取当前记忆设置
                $.ajax({
                    url: "/api/SaleSheet/GetMemorySettings",
                    type: "GET",
                    data: { operKey: g_operKey },
                    success: function(response) {
                        console.log('获取记忆设置响应:', response);

                        var settings = response.settings || {};
                        var headerData = {};

                        // 收集需要记忆的抬头字段
                        var memoryFields = [
                            'supcust_id','seller_id','sender_id','getter_id',
                            'branch_id', 'happen_time','make_brief','default_unit',
                            'mobile','receiver_mobile','sup_addr','payway1_id'
                        ];

                        memoryFields.forEach(function(fieldName) {
                            var setting = settings[fieldName];
                            if (setting && setting.type !== 'none') {
                                var fieldValue = getFieldValue(fieldName);
                                if (fieldValue) {
                                    var memoryKey = generateMemoryKey(fieldName, setting);
                                    headerData[memoryKey] = fieldValue;
                                    console.log('保存字段记忆:', fieldName, memoryKey, fieldValue);
                                }
                            }
                        });

                        if (Object.keys(headerData).length > 0) {
                            // 保存抬头记忆数据
                            $.ajax({
                                url: "/api/HeaderMemory/save",
                                type: "POST",
                                contentType: "application/json",
                                data: JSON.stringify({
                                    operKey: g_operKey,
                                    headerData: headerData
                                }),
                                success: function(response) {
                                    console.log('抬头记忆保存成功:', response);
                                },
                                error: function(xhr, status, error) {
                                    console.error('抬头记忆保存失败:', error);
                                    console.error('状态码:', xhr.status);
                                    console.error('响应文本:', xhr.responseText);
                                }
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('获取记忆设置失败:', error);
                    }
                });
            }

            // 新增时，读取记忆设置并填入抬头
            function loadHeaderFromMemory() {
                console.log('=== 从记忆设置加载抬头信息 ===');

                // 获取当前记忆设置
                $.ajax({
                    url: "/api/SaleSheet/GetMemorySettings",
                    type: "GET",
                    data: { operKey: g_operKey },
                    success: function(response) {
                        console.log('获取记忆设置响应:', response);

                        var settings = response.settings || {};

                        // 获取抬头记忆数据
                        $.ajax({
                            url: "/api/HeaderMemory/get",
                            type: "GET",
                            data: { operKey: g_operKey },
                            success: function(memoryResponse) {
                                console.log('获取抬头记忆响应:', memoryResponse);

                                var headerData = memoryResponse.headerData || {};
                                var memoryFields = [
                                    'supcust_id','seller_id','sender_id','getter_id',
                                    'branch_id', 'happen_time','make_brief','default_unit',
                                    'mobile','receiver_mobile','sup_addr','payway1_id'
                                ];

                                memoryFields.forEach(function(fieldName) {
                                    var setting = settings[fieldName];
                                    if (setting && setting.type !== 'none') {
                                        var memoryKey = generateMemoryKey(fieldName, setting);
                                        var memorizedValue = headerData[memoryKey];

                                        if (memorizedValue) {
                                            setFieldValue(fieldName, memorizedValue);
                                            console.log('恢复字段记忆:', fieldName, memoryKey, memorizedValue);
                                        }
                                    }
                                });
                            },
                            error: function(xhr, status, error) {
                                console.error('获取抬头记忆失败:', error);
                                console.error('状态码:', xhr.status);
                                console.error('响应文本:', xhr.responseText);
                            }
                        });
                    },
                    error: function(xhr, status, error) {
                        console.error('获取记忆设置失败:', error);
                    }
                });
            }

            // 生成记忆键值
            function generateMemoryKey(fieldName, setting) {
                var key = fieldName;

                if (setting.type === 'single') {
                    key += '_single';
                } else if (setting.type === 'multi' && setting.dimensions) {
                    var dimensions = setting.dimensions;
                    var dimensionValues = [];

                    dimensions.forEach(function(dimension) {
                        var value = '';
                        switch(dimension) {
                            case 'customer':
                                value = getFieldValue('supcust_id') || 'default';
                                break;
                            case 'businessType':
                                value = getFieldValue('sheet_type') || 'default';
                                break;
                            case 'maker':
                                value = g_operKey || 'default';
                                break;
                            case 'department':
                                value = getFieldValue('department_id') || 'default';
                                break;
                            case 'direction':
                                value = getFieldValue('sheetType') || 'default';
                                break;
                            default:
                                value = 'default';
                        }
                        dimensionValues.push(dimension + '_' + value);
                    });

                    key += '_multi_' + dimensionValues.join('_');
                }

                return key;
            }

            // 字段名映射表（配置字段名 -> 实际HTML元素ID）
            var fieldNameMapping = {
                'supcust_id': 'supcust_id',
                'seller_id': 'seller_id',
                'sender_id': 'senders_id',      // sender_id映射到senders_id
                'senders_id': 'senders_id',     // 保持向后兼容
                'branch_id': 'branch_id',
                'happen_time': 'happen_time',
                'make_brief': 'make_brief',
                'default_unit': 'defaultUnit',
                'mobile': 'mobile',
                'receiver_mobile': 'receiver_mobile',
                'sup_addr': 'sup_addr',
                'payment_type': 'payway1_id', //payment_type映射到payway1_id
                'payway1_id': 'payway1_id'          
            };

            // 获取字段值的通用函数
            function getFieldValue(fieldName) {
                try {
                    // 使用映射表获取实际的元素ID
                    var actualFieldName = fieldNameMapping[fieldName] || fieldName;

                    // 检查元素是否存在
                    if ($("#" + actualFieldName).length === 0) {
                        console.warn('字段元素不存在:', actualFieldName, '(原字段名:', fieldName, ')');
                        return null;
                    }

                    // 尝试从jqxInput获取
                    try {
                        var jqxValue = $("#" + actualFieldName).jqxInput('val');
                        if (jqxValue !== undefined && jqxValue !== null && jqxValue !== '') {
                            return jqxValue;
                        }
                    } catch (e) {}

                    // 尝试从jqxDropDownList获取
                    try {
                        var dropDownValue = $("#" + actualFieldName).jqxDropDownList('val');
                        if (dropDownValue !== undefined && dropDownValue !== null && dropDownValue !== '') {
                            return dropDownValue;
                        }
                    } catch (e) {}

                    // 尝试从jqxDateTimeInput获取
                    try {
                        var dateValue = $("#" + actualFieldName).jqxDateTimeInput('val');
                        if (dateValue !== undefined && dateValue !== null) {
                            return dateValue;
                        }
                    } catch (e) {}

                    // 尝试从普通input获取
                    var inputValue = $("#" + actualFieldName).val();
                    if (inputValue !== undefined && inputValue !== null && inputValue !== '') {
                        return inputValue;
                    }

                    return null;
                } catch (error) {
                    console.error('获取字段值失败:', fieldName, error);
                    return null;
                }
            }

            // 设置字段值的通用函数
            function setFieldValue(fieldName, value) {
                try {
                    if (!value) return;

                    // 使用映射表获取实际的元素ID
                    var actualFieldName = fieldNameMapping[fieldName] || fieldName;

                    // 检查元素是否存在
                    if ($("#" + actualFieldName).length === 0) {
                        console.warn('设置字段值时元素不存在:', actualFieldName, '(原字段名:', fieldName, ')');
                        return;
                    }

                    // 尝试设置到jqxInput
                    try {
                        if ($("#" + actualFieldName).jqxInput) {
                            $("#" + actualFieldName).jqxInput('val', value);
                            console.log('设置字段值成功(jqxInput):', fieldName, '->', actualFieldName, value);
                            return;
                        }
                    } catch (e) {}

                    // 尝试设置到jqxDropDownList
                    try {
                        if ($("#" + actualFieldName).jqxDropDownList) {
                            $("#" + actualFieldName).jqxDropDownList('val', value);
                            console.log('设置字段值成功(jqxDropDownList):', fieldName, '->', actualFieldName, value);
                            return;
                        }
                    } catch (e) {}

                    // 尝试设置到jqxDateTimeInput
                    try {
                        if ($("#" + actualFieldName).jqxDateTimeInput) {
                            $("#" + actualFieldName).jqxDateTimeInput('val', value);
                            console.log('设置字段值成功(jqxDateTimeInput):', fieldName, '->', actualFieldName, value);
                            return;
                        }
                    } catch (e) {}

                    // 尝试设置到普通input
                    $("#" + actualFieldName).val(value);
                    console.log('设置字段值成功(input):', fieldName, '->', actualFieldName, value);

                } catch (error) {
                    console.error('设置字段值失败:', fieldName, value, error);
                }
            }
        });

        // 选择多维度记忆类型
        function selectMultiMemoryType(id) {
            console.log('=== selectMultiMemoryType 被调用 ===');
            console.log('传入的ID:', id);

            // 清空之前的选择
            $("#multiMemoryDialog input[type='checkbox']").prop('checked', false);
            console.log('已清空之前的选择');

            // 获取当前行的已有设置
            var data = $("#memorySettingGrid").jqxGrid('getrows');
            console.log('获取到的所有行数据:', data);

            var currentRow = null;
            for (var i = 0; i < data.length; i++) {
                if (data[i].id == id) { // 使用 == 而不是 === 以处理类型转换
                    currentRow = data[i];
                    console.log('找到当前行:', currentRow);
                    break;
                }
            }

            // 如果已经有多维度设置，则预选相应的选项
            if (currentRow && currentRow.memoryType === 'multi' && currentRow.memoryDimensions) {
                console.log('预选已有的维度:', currentRow.memoryDimensions);

                try {
                    var dimensions = [];
                    if (typeof currentRow.memoryDimensions === 'string') {
                        dimensions = JSON.parse(currentRow.memoryDimensions);
                    } else if (Array.isArray(currentRow.memoryDimensions)) {
                        dimensions = currentRow.memoryDimensions;
                    }

                    dimensions.forEach(function(dimension) {
                        $("#multiMemoryDialog input[value='" + dimension + "']").prop('checked', true);
                    });
                    console.log('已预选维度:', dimensions);
                } catch (parseError) {
                    console.error('解析已有维度数据失败:', parseError);
                }
            } else {
                console.log('没有已有的多维度设置');
            }

            // 保存当前行ID到对话框数据中
            $("#multiMemoryDialog").data('currentRowId', id);
            console.log('已保存当前行ID到对话框:', id);

            // 打开多维度选择对话框
            $("#multiMemoryDialog").jqxWindow('open');
            console.log('已打开多维度选择对话框');
        }

        // 新的安全更新函数 - 彻底解决所有问题
        function safeUpdateMemoryType(id, type, dimensions) {
            console.log('=== safeUpdateMemoryType 开始 ===');
            console.log('参数: ID=' + id + ', Type=' + type + ', Dimensions=' + JSON.stringify(dimensions));

            // 防护措施1: 参数验证
            if (!id || !type) {
                console.error('参数无效: ID=' + id + ', Type=' + type);
                return;
            }

            // 防护措施2: 检查表格是否存在
            if (!$("#memorySettingGrid").length) {
                console.error('表格不存在');
                return;
            }

            try {
                // 处理维度数据
                var dimensionsStr = null;
                if (type === 'multi' && dimensions && dimensions.length > 0) {
                    dimensionsStr = JSON.stringify(dimensions);
                    console.log('保留多维度数据:', dimensionsStr);
                } else {
                    dimensionsStr = null;
                    console.log('清除多维度数据，因为选择了:', type);
                }

                // 获取当前所有数据
                var allRows = $("#memorySettingGrid").jqxGrid('getrows');
                console.log('当前所有行数据:', allRows);

                // 找到并更新目标行
                var targetRowFound = false;
                for (var i = 0; i < allRows.length; i++) {
                    if (allRows[i].id == id) {
                        console.log('找到目标行:', allRows[i]);

                        // 更新数据
                        allRows[i].memoryType = type;
                        allRows[i].memoryDimensions = dimensionsStr;

                        console.log('更新后的行数据:', allRows[i]);
                        targetRowFound = true;
                        break;
                    }
                }

                if (!targetRowFound) {
                    console.error('未找到目标行，ID:', id);
                    return;
                }

                // 重建数据源以确保更新生效
                var newSource = {
                    datatype: "array",
                    datafields: [
                        { name: 'id', type: 'number' },
                        { name: 'fieldName', type: 'string' },
                        { name: 'fieldDesc', type: 'string' },
                        { name: 'memoryType', type: 'string' },
                        { name: 'memoryDimensions', type: 'string' }
                    ],
                    localdata: allRows
                };

                $("#memorySettingGrid").jqxGrid({ source: new $.jqx.dataAdapter(newSource) });
                console.log('✅ 数据源重建完成');

                // 验证更新结果
                setTimeout(function() {
                    var updatedRowData = $("#memorySettingGrid").jqxGrid('getrowdatabyid', id);
                    console.log('验证更新结果:', updatedRowData);

                    if (updatedRowData && updatedRowData.memoryType === type) {
                        console.log('✅ 更新验证成功');
                    } else {
                        console.error('❌ 更新验证失败');
                    }
                }, 100);

            } catch (error) {
                console.error('safeUpdateMemoryType发生错误:', error);
            }
        }

        // 保留原来的updateMemoryType函数作为备用
        function updateMemoryType(id, type, dimensions) {
            console.log('=== updateMemoryType 开始 ===');
            console.log('参数:', { id: id, type: type, dimensions: dimensions });

            // 验证点击的是否是正确的行
            setTimeout(function() {
                var checkedRadios = $("input[type='radio']:checked");
                console.log('当前选中的单选按钮数量:', checkedRadios.length);
                checkedRadios.each(function(index, radio) {
                    var radioName = $(radio).attr('name');
                    var radioValue = $(radio).val();
                    var debugId = $(radio).attr('data-debug-id');
                    var debugDesc = $(radio).attr('data-debug-desc');
                    console.log('选中的单选按钮:', {
                        name: radioName,
                        value: radioValue,
                        debugId: debugId,
                        debugDesc: debugDesc,
                        expectedId: id
                    });

                    var actualId = radioName.replace('memory_', '');
                    if (actualId != id) {
                        console.error('🚨 点击错位检测: 期望ID=' + id + ', 实际选中ID=' + actualId + ', 描述=' + debugDesc);
                    } else {
                        console.log('✅ 点击正确: ID=' + id + ', 描述=' + debugDesc);
                    }
                });
            }, 50);

            // 防护措施1: 参数验证
            if (!id || !type) {
                console.error('参数无效:', { id: id, type: type });
                return;
            }

            // 防护措施2: 检查表格是否存在
            if (!$("#memorySettingGrid").length) {
                console.error('表格不存在，无法更新');
                return;
            }

            // 防护措施3: 记录更新前的数据状态
            var beforeRows = null;
            try {
                beforeRows = $("#memorySettingGrid").jqxGrid('getrows');
                console.log('更新前数据行数:', beforeRows ? beforeRows.length : 'null');
            } catch (e) {
                console.error('获取更新前数据失败:', e);
            }

            try {
                // 将数组转换为JSON字符串存储
                var dimensionsStr = null;
                if (type === 'multi' && dimensions && dimensions.length > 0) {
                    dimensionsStr = JSON.stringify(dimensions);
                } else {
                    // 如果选择的不是多维度记忆，清除维度数据
                    dimensionsStr = null;
                }

                console.log('转换后的维度字符串:', dimensionsStr);

                // 如果切换到非多维度记忆类型，清除该行的多维度显示
                if (type !== 'multi') {
                    console.log('清除多维度记忆显示，因为选择了:', type);
                }

                // 方法1: 使用更安全的行数据更新方式
                var rowData = $("#memorySettingGrid").jqxGrid('getrowdatabyid', id);
                if (rowData) {
                    console.log('获取到行数据:', rowData);
                    console.log('更新前的memoryDimensions:', rowData.memoryDimensions);

                    // 直接修改行数据对象
                    rowData.memoryType = type;
                    rowData.memoryDimensions = dimensionsStr;

                    // 如果选择的不是多维度记忆，确保清除维度数据
                    if (type !== 'multi') {
                        rowData.memoryDimensions = null;
                        console.log('已清除多维度数据，因为选择了:', type);
                    }

                    console.log('更新后的数据:', rowData);

                    // 使用updaterow方法更新整行
                    $("#memorySettingGrid").jqxGrid('updaterow', id, rowData);
                    console.log('已使用updaterow更新数据');
                } else {
                    console.error('无法获取行数据，ID:', id);
                    return;
                }

                // 方法2: 备用方案 - 完全重建数据源（避免setcellvalue的displayfield错误）
                setTimeout(function() {
                    try {
                        // 获取当前所有数据
                        var allRows = $("#memorySettingGrid").jqxGrid('getrows');
                        console.log('获取当前所有数据，行数:', allRows.length);

                        // 确保目标行已更新
                        var targetRowUpdated = false;
                        for (var i = 0; i < allRows.length; i++) {
                            if (allRows[i].id == id) {
                                allRows[i].memoryType = type;

                                // 如果选择的不是多维度记忆，清除维度数据
                                if (type === 'multi') {
                                    allRows[i].memoryDimensions = dimensionsStr;
                                } else {
                                    allRows[i].memoryDimensions = null;
                                    console.log('在数据源重建中清除多维度数据，因为选择了:', type);
                                }

                                targetRowUpdated = true;
                                console.log('已更新目标行数据:', allRows[i]);
                                break;
                            }
                        }

                        if (!targetRowUpdated) {
                            console.error('未找到目标行，ID:', id);
                            return;
                        }

                        // 重建数据源
                        var newSource = {
                            datatype: "array",
                            datafields: [
                                { name: 'id', type: 'number' },
                                { name: 'fieldName', type: 'string' },
                                { name: 'fieldDesc', type: 'string' },
                                { name: 'memoryType', type: 'string' },
                                { name: 'memoryDimensions', type: 'string' }
                            ],
                            localdata: allRows
                        };

                        // 重新设置数据源
                        $("#memorySettingGrid").jqxGrid({ source: new $.jqx.dataAdapter(newSource) });
                        console.log('✅ 数据源重建完成');

                        // 验证更新结果
                        setTimeout(function() {
                            var updatedRowData = $("#memorySettingGrid").jqxGrid('getrowdatabyid', id);
                            console.log('最终验证 - 更新后的行数据:', updatedRowData);
                        }, 200);

                    } catch (rebuildError) {
                        console.error('数据源重建过程发生错误:', rebuildError);

                        // 最后的应急方案：直接操作DOM
                        try {
                            console.log('尝试最后的应急方案：直接操作DOM');
                            forceUpdateDisplay(id, dimensions);
                        } catch (domError) {
                            console.error('DOM操作也失败:', domError);
                        }
                    }
                }, 100);

                console.log('=== updateMemoryType 成功完成 ===');

            } catch (error) {
                console.error('updateMemoryType 发生错误:', error);
                console.error('错误堆栈:', error.stack);

                // 应急方案：直接操作DOM
                try {
                    console.log('尝试应急方案：直接操作DOM');
                    forceUpdateDisplay(id, dimensions);
                } catch (domError) {
                    console.error('应急方案也失败:', domError);
                }
            }
        }

        // 应急方案：直接操作DOM
        function forceUpdateDisplay(id, dimensions) {
            console.log('执行强制DOM更新:', id, dimensions);

            var radioButton = $("input[name='memory_" + id + "'][value='multi']");
            if (radioButton.length > 0) {
                var cell = radioButton.parent();

                // 移除旧的维度文本
                cell.find('small, span').remove();

                if (dimensions && dimensions.length > 0) {
                    var dimensionNames = {
                        'customer': '客户',
                        'businessType': '业务类型',
                        'maker': '制单人',
                        'department': '部门',
                        'direction': '单据方向'
                    };
                    var selectedNames = dimensions.map(function(dim) {
                        return dimensionNames[dim] || dim;
                    }).join('+');

                    // 添加新的维度文本
                    cell.append(' <small style="color: #007bff; font-weight: bold; background: #e3f2fd; padding: 2px 6px; border-radius: 3px; font-size: 12px; margin-left: 8px;">' + selectedNames + '</small>');
                    console.log('DOM更新完成，显示维度:', selectedNames);
                }
            }
        }
    </script>
</body>
</html>