[
  {
    "Id": "sale",
    "Class": "mainMenu",
    "Title": "销售",
    "Url": "/images/images.svg#sale",
    "SubNodes": {
      "基础单据": [
        {
          "Id": "sheetSaleOrder",
          "Class": "menu",
          "Title": "销售订单",
          "Url": "Sheets/SaleOrderSheet",
          "ViewUrl": "Sheets/SaleOrderSheetView?forReturn=false",
          "View": true
        },
        {
          "Id": "sheetReturnOrder",
          "Class": "menu",
          "Title": "退货订单",
          "Url": "Sheets/SaleOrderSheet?forReturn=true",
          "ViewUrl": "Sheets/SaleOrderSheetView?forReturn=true&sheet_type=TD",
          "View": true
        },
        {
          "Id": "sheetSale",
          "Class": "menu",
          "Title": "销售单",
          "Url": "Sheets/SaleSheet?forReturn=false",
          "ViewUrl": "Sheets/SaleSheetView?forReturn=false",
          "View": true
        },
        {
          "Id": "sheetReturn",
          "Class": "menu",
          "Title": "退货单",
          "Url": "Sheets/SaleSheet?forReturn=true",
          "ViewUrl": "Sheets/SaleSheetView?forReturn=true&sheet_type=T",
          "View": true
        },
        {
          "Id": "sheetBorrowItem",
          "Class": "menu",
          "Title": "借货单",
          "Url": "Sheets/BorrowItemSheet?forReturn=false",
          "ViewUrl": "Sheets/BorrowItemSheetView?forReturn=false&sheet_type=JH",
          "View": true
        },
        {
          "Id": "sheetReturnItem",
          "Class": "menu",
          "Title": "还货单",
          "Url": "Sheets/BorrowItemSheet?forReturn=true",
          "ViewUrl": "Sheets/BorrowItemSheetView?forReturn=true&sheet_type=HH",
          "View": true
        },
        {
          "Id": "sheetPriceAdjust",
          "Class": "menu",
          "Title": "调价单",
          "Url": "Sheets/PriceAdjustSheet",
          "View": true,
          "ViewUrl": "Sheets/PriceAdjustSheetView"
        },
        {
          "Id": "sheetSupermarket",
          "Class": "menu",
          "Title": "连锁超市订单",
          "Url": "Sheets/ChainSupermarketOrder"
        },
        {
          "Id": "sheetPlaceholderOrder",
          "Class": "menu",
          "Title": "占位单",
          "Url": "Sheets/PlaceholderOrderSheet",
          "ViewUrl": "Sheets/PlaceholderOrderSheetView?forModify=false",
          "View": true
        },
        {
          "Id": "sheetLabelPrint",
          "Class": "menu",
          "Title": "标签打印",
          "Url": "Sheets/LabelPrintSheet",
          "View": true,
          "ViewUrl": "Sheets/LabelPrintSheetView"
        },
        {
          "Id": "sheetPriceRebate",
          "Class": "menu",
          "Title": "补差单",
          "Url": "Sheets/PriceRebateSheet",
          "ViewUrl": "Sheets/PriceRebateSheetView",
          "View": true
        },
        {
          "Id": "sheetSaleFeeApportion",
          "Class": "menu",
          "Title": "销售费用分摊单",
          "Url": "Sheets/SaleFeeApportionSheet",
          "ViewUrl": "Sheets/SaleFeeApportionSheetView",
          "View": true
        }

      ],
      "业务流程": [
        {
          "Id": "sheetCheckSheets",
          "Class": "menu",
          "Title": "交账单",
          "Url": "WorkFlow/CheckAccount/CheckSheetsSheet",
          "ViewUrl": "WorkFlow/CheckAccount/CheckSheetsSheetHistory",
          "View": true
        },
        //{
        //  "Id": "sheetCheckSheets",
        //  "Class": "menu",
        //  "Title": "交账单(新)",
        //  "Url": "Sheets/CheckAccountNew",
        //  "ViewUrl": "Sheets/CheckAccountNewHistory",
        //  "View": true
        //},
        {
          "Id": "sheetCheckSheets",
          "Class": "menu",
          "Title": "交账单(新)",
          "Url": "Sheets/CheckAccountNew",
          "ViewUrl": "Sheets/CheckAccountNewestHistory",
          "View": true
        },
        {
          "Id": "orderItemSheet",
          "Class": "menu",
          "Title": "定货会",
          "Url": "Sheets/OrderItemSheet",
          "ViewUrl": "Sheets/OrderItemSheetView?forReturn=false",
          "View": true
        },
        {
          "Id": "orderItemAdjustSheet",
          "Class": "menu",
          "Title": "定货会调整单",
          "Url": "Sheets/OrderItemAdjustSheet",
          "ViewUrl": "Sheets/OrderItemAdjustSheetView?forReturn=false",
          "View": true
        },
        {
          "Id": "sheetDisplayAgreement",
          "Class": "menu",
          "Title": "陈列协议",
          "Url": "Sheets/DisplayAgreementSheet",
          "View": true,
          "ViewUrl": "Sheets/DisplayAgreementSheetView"
        },
        {
          "Id": "promotionView",
          "Class": "menu",
          "Title": "促销活动",
          "Url": "BaseInfo/PromotionView"
        },
        {
          "Id": "redPacket",
          "Class": "menu",
          "Title": "红包",
          "Url": "BaseInfo/RedPacket"
        }

      ],
      "访销流程": [
        {
          "Id": "orderPrint",
          "Class": "menu",
          "Title": "打单",
          "Url": "WorkFlow/OrderManagePrint",
          "ViewUrl": "",
          "View": false
        },
        {
          "Id": "orderReview",
          "Class": "menu",
          "Title": "复核",
          "Url": "WorkFlow/OrderManageReview",
          "ViewUrl": "",
          "View": false
        },
        {
          "Id": "assignVan",
          "Class": "menu",
          "Title": "装车",
          "Url": "WorkFlow/OrderManageAssignVan",
          "ViewUrl": "",
          "View": false
        },
        {
          "Id": "deliverItems",
          "Class": "menu",
          "Title": "发货",
          "Url": "WorkFlow/OrderManageDelivery",
          "ViewUrl": "",
          "View": false
        },
        {
          "Id": "orderToSale",
          "Class": "menu",
          "Title": "转单",
          "Url": "WorkFlow/OrderManageOrderToSale",
          "ViewUrl": "",
          "View": false
        },
        {
          "Id": "backBranch",
          "Class": "menu",
          "Title": "回库",
          "Url": "WorkFlow/OrderManageBackBranch",
          "ViewUrl": "",
          "View": false
        },
        {
          "Id": "backBranch",
          "Class": "menu",
          "Title": "回库（新）",
          "Url": "WorkFlow/OrderManageBackBranchRows",
          "ViewUrl": "",
          "View": false
        }

      ],
      "基本报表": [
        {
          "Id": "salesSummaryBySeller",
          "Class": "menu",
          "Title": "销售汇总(业务员)",
          "Url": "Report/SalesSummaryBySeller?sheetType=x",
          "OrderTable": true,
          "OrderUrl": "Report/SalesSummaryBySeller?sheetType=xd"

        },
        {
          "Id": "salesSummaryByItem",
          "Class": "menu",
          "Title": "销售汇总(商品)",
          "Url": "Report/SalesSummaryByItem?sheetType=x",
          "OrderTable": true,
          "OrderUrl": "Report/SalesSummaryByItem?sheetType=xd"

        },
        {
          "Id": "salesSummaryByClient",
          "Class": "menu",
          "Title": "销售汇总(客户)",
          "Url": "Report/SalesSummaryByClient?sheetType=x",
          "OrderTable": true,
          "OrderUrl": "Report/SalesSummaryByClient?sheetType=xd"

        },
        {
          "Id": "salesSummaryByRegion",
          "Class": "menu",
          "Title": "销售汇总(片区)",
          "Url": "Report/SalesSummaryByRegion?sheetType=x",
          "OrderTable": true,
          "OrderUrl": "Report/SalesSummaryByRegion?sheetType=xd"

        },
        {
          "Id": "salesSummaryByGroup",
          "Class": "menu",
          "Title": "销售汇总(渠道)",
          "Url": "Report/SalesSummaryByGroup?sheetType=x",
          "OrderTable": true,
          "OrderUrl": "Report/SalesSummaryByGroup?sheetType=xd"

        },
        {
          "Id": "salesSummaryByClientAndItem",
          "Class": "menu",
          "Title": "销售汇总(客/品)",
          "Url": "Report/SalesSummaryByClientAndItem?sheetType=x",
          "OrderTable": true,
          "OrderUrl": "Report/SalesSummaryByClientAndItem?sheetType=xd"

        },
        {
          "Id": "salesSummaryBySellerAndItem",
          "Class": "menu",
          "Title": "销售汇总(业/品)",
          "Url": "Report/salesSummaryBySellerAndItem?sheetType=x",
          "OrderTable": true,
          "OrderUrl": "Report/salesSummaryBySellerAndItem?sheetType=xd"
        },
        {
          "Id": "salesSummaryByBrand",
          "Class": "menu",
          "Title": "销售汇总(品牌)",
          "Url": "Report/SalesSummaryByBrand?sheetType=x",
          "OrderTable": true,
          "OrderUrl": "Report/SalesSummaryByBrand?sheetType=xd"

        },
        {
          "Id": "salesSummaryBySender",
          "Class": "menu",
          "Title": "销售汇总(送货员)",
          "Url": "Report/SalesSummaryBySender?sheetType=x",
          "OrderTable": true,
          "OrderUrl": "Report/SalesSummaryBySender?sheetType=xd"

        },
        {
          "Id": "salesSummaryBySenderandItem",
          "Class": "menu",
          "Title": "销售汇总(送../品)",
          "Url": "Report/SalesSummaryBySenderAndltem?sheetType=x",
          "OrderTable": true,
          "OrderUrl": "Report/SalesSummaryBySenderAndltem?sheetType=xd"

        },
        {
          "Id": "salesTrend",
          "Class": "menu",
          "Title": "销量走势图",
          "Url": "Report/SalesTrend"
        },
        {
          "Id": "salesDetaill",
          "Class": "menu",
          "Title": "销售明细表",
          "Url": "Report/SalesDetail?sheetType=x",
          "OrderTable": true,
          "OrderUrl": "Report/SalesDetail?sheetType=xd"
        },
        {
          "Id": "checkSheetBySeller",
          "Class": "menu",
          "Title": "交账汇总",
          "Url": "Report/CheckSheetBySeller",
          "OrderUrl": "Report/CheckSheetBySeller"

        },
        {
          "Id": "orderToSale",
          "Class": "menu",
          "Title": "配送差异一览表",
          "Url": "Report/OrderToSaleDifferencel",
          "OrderUrl": "Report/OrderToSaleDifferencel"

        }

      ],
      "业务流程报表": [
        {
          "Id": "borrowedView",
          "Class": "menu",
          "Title": "借还货汇总",
          "Url": "Report/BorrowedView"
        },
        {
          "Id": "borrowItem",
          "Class": "menu",
          "Title": "借还货明细表",
          "Url": "Report/BorrowItem"
        },
        {
          "Id": "borrowBalance",
          "Class": "menu",
          "Title": "借还货明细(余额)",
          "Url": "Report/BorrowBalance"
        },
        //{
        //  "Id": "borrowItem",
        //  "Class": "menu",
        //  "Title": "借还货余额变化表",
        //  "Url": "Report/BorrowItem"
        //},

        //{
        //  "Id": 517,
        //  "Class": "menu",
        //  "Title": "定货款余额(商品)",
        //  "Url": "Report/ItemsOrderedList"
        //},
        {
          "Id": "itemsOrderedSummary",
          "Class": "menu",
          "Title": "定货汇总",
          "Url": "Report/ItemsOrderedSummary"
        },
        {
          "Id": "itemsOrderedSummary",
          "Class": "menu",
          "Title": "定货汇总(单据)",
          "Url": "Report/ItemsOrderedSummaryBySheet"
        },
        {
          "Id": "itemsOrderedSummaryByItem",
          "Class": "menu",
          "Title": "定货变化明细表",
          "Url": "Report/ItemsOrderedSummaryByItem"
        },
        {
          "Id": "itemsOrderedAdjust",
          "Class": "menu",
          "Title": "定货调整记录",
          "Url": "Report/ItemsOrderedAdjust"
        },
        {
          "Id": "displayAgreementDetail",
          "Class": "menu",
          "Title": "陈列协议明细",
          "Url": "Report/DisplayAgreementDetail"
        },
        {
          "Id": "displayMoneyDetail",
          "Class": "menu",
          "Title": "陈列协议变化(金额)",
          "Url": "Report/DisplayMoneyDetail"
        },
        {
          "Id": "displaySheetBrowser",
          "Class": "menu",
          "Title": "陈列协议全览",
          "Url": "Report/DisplaySheetBrowser"
        },
        {
          "Id": "AssignSummaryByItem",
          "Class": "menu",
          "Title": "装车汇总(商品)",
          "Url": "Report/AssignSummaryByItem"
        }

      ]
    }
  },
  {
    "Id": "buy",
    "Class": "mainMenu",
    "Title": "采购",
    "Url": "/images/images.svg#buy",
    "SubNodes": {
      "采购单据": [
        {
          "Id": "sheetBuy",
          "Class": "menu",
          "Title": "采购单",
          "Url": "Sheets/BuySheet?forReturn=false",
          "View": true,
          "ViewUrl": "Sheets/BuySheetView"
        },
        {
          "Id": "sheetBuyReturn",
          "Class": "menu",
          "Title": "采购退货单",
          "Url": "Sheets/BuySheet?forReturn=true",
          "View": true,
          "ViewUrl": "Sheets/BuySheetView?sheet_type=CT"
        },
        {
          "Id": "sheetBuyOrder",
          "Class": "menu",
          "Title": "采购订单",
          "Url": "Sheets/BuyOrderSheet",
          "View": true,
          "ViewUrl": "Sheets/BuyOrderSheetView?sheet_type=CD"
        },
        {
          "Id": "sheetBuyPriceAdjust",
          "Class": "menu",
          "Title": "采购调价单",
          "Url": "Sheets/BuySheetPriceAdjust",
          "View": true,
          "ViewUrl": "Sheets/BuySheetPriceAdjustView"
        },
        {
          "Id": "sheetFeeApportion",
          "Class": "menu",
          "Title": "采购费用分摊单",
          "Url": "Sheets/FeeApportionSheet",
          "View": true,
          "ViewUrl": "Sheets/FeeApportionSheetView"
        },
        {
          "Id": "sheetPriceAllowance",
          "Class": "menu",
          "Title": "费用补贴单",
          "Url": "Sheets/PriceAllowanceSheet",
          "ViewUrl": "Sheets/PriceAllowanceSheetView",
          "View": true
        }
      ],
      "采购报表": [
        {
          "Id": "buysSummaryByItem",
          "Class": "menu",
          "Title": "采购汇总(商品)",
          "Url": "Report/BuysSummaryByItem"
        },
        {
          "Id": "buysDetail",
          "Class": "menu",
          "Title": "采购明细",
          "Url": "Report/BuysDetail"
        }
      ]


    }
  },
  {
    "Id": "stock",
    "Class": "mainMenu",
    "Title": "仓库",
    "Url": "/images/images.svg#stock",
    "SubNodes": {
      "仓库单据": [
        {
          "Id": "sheetMove",
          "Class": "menu",
          "Title": "调拨单",
          "Url": "Sheets/MoveSheet",
          "View": true,
          "ViewUrl": "Sheets/MoveSheetView",
          "SaveDb": false
        },
        {
          "Id": "sheetInventChange",
          "Class": "menu",
          "Title": "盘点盈亏单",
          "Url": "Sheets/InventChangeSheet?forReduce=false",
          "View": true,
          "ViewUrl": "Sheets/InventChangeSheetView?forReduce=false"
        },
        {
          "Id": "sheetOpeningStock",
          "Class": "menu",
          "Title": "期初库存单",
          "Url": "Sheets/OpeningStockSheet",
          "View": true,
          "ViewUrl": "Sheets/OpeningStockSheetView"
        },
        {
          "Id": "sheetInventReduce",
          "Class": "menu",
          "Title": "报损单",
          "Url": "Sheets/InventChangeSheet?forReduce=true",
          "View": true,
          "ViewUrl": "Sheets/InventChangeSheetView?forReduce=true"
        },
        {
          "Id": "sheetInvent",
          "Class": "menu",
          "Title": "盘点单",
          "Url": "Sheets/InventorySheet",
          "View": true,
          "ViewUrl": "Sheets/InventorySheetView"
        },
        {
          "Id": "sheetCombine",
          "Class": "menu",
          "Title": "组装单",
          "Url": "Sheets/CombineSheet",
          "View": true,
          "ViewUrl": "Sheets/CombineSheetView?forSplit=false&sheet_type=ZZ"
        },
        {
          "Id": "sheetSplit",
          "Class": "menu",
          "Title": "拆分单",
          "Url": "Sheets/CombineSheet?forSplit=true",
          "View": true,
          "ViewUrl": "Sheets/CombineSheetView?forSplit=true&sheet_type=CF"
        },
        {
          "Id": "sheetCostPriceAdjust",
          "Class": "menu",
          "Title": "成本调价单",
          "Url": "Sheets/CostPriceAdjustSheet",
          "View": true,
          "ViewUrl": "Sheets/CostPriceAdjustSheetView"
        },
        {
          "Id": "sheetStockIn",
          "Class": "menu",
          "Title": "其他入库单",
          "Url": "Sheets/StockInOutSheet",
          "View": true,
          "ViewUrl": "Sheets/StockInOutSheetView?forReduce=false&sheet_type=RK"
        },
        {
          "Id": "sheetStockOut",
          "Class": "menu",
          "Title": "其他出库单",
          "Url": "Sheets/StockInOutSheet?forReduce=true",
          "View": true,
          "ViewUrl": "Sheets/StockInOutSheetView?forReduce=true&sheet_type=CK"
        }
      ],
      "仓库报表": [
        {
          "Id": "viewStock",
          "Class": "menu",
          "Title": "库存表",
          "Url": "Report/StocksView",
          "View": false
        },
        {
          "Id": "stockChangeSum",
          "Class": "menu",
          "Title": "库存变化表",
          "Url": "Report/StockChangeSum",
          "View": false
        },
        {
          "Id": "stocksChangeByOrder",
          "Class": "menu",
          "Title": "库存变化明细表",
          "Url": "Report/StocksChangeByOrder",
          "View": false
        },
        {
          "Id": "openingStockReport",
          "Class": "menu",
          "Title": "期初库存表",
          "Url": "Report/OpeningStockByItem",
          "View": false
        },
        {
          "Id": "storeStockReport",
          "Class": "menu",
          "Title": "门店库存上报表",
          "Url": "Report/StoreStockReport",
          "View": false
        },
        {
          "Id": "moveSummary",
          "Class": "menu",
          "Title": "调拨汇总",
          "Url": "Report/MoveSummaryByItem"
        },
        {
          "Id": "movesDetail",
          "Class": "menu",
          "Title": "调拨明细",
          "Url": "Report/MovesDetail"
        },
        {
          "Id": "inventorySummary",
          "Class": "menu",
          "Title": "盘点汇总",
          "Url": "Report/InventorySummary"
        },
        {
          "Id": "inventoryDetail",
          "Class": "menu",
          "Title": "盘点明细",
          "Url": "Report/InventoryDetail"
        },
        {
          "Id": "breakageDetail",
          "Class": "menu",
          "Title": "报损明细",
          "Url": "Report/BreakageDetail"
        },
        {
          "Id": "stockAlert",
          "Class": "menu",
          "Title": "库存预警表",
          "Url": "Report/StockAlertDetail?forSetting=false"
        },
        {
          "Id": "approachingAlert",
          "Class": "menu",
          "Title": "临期预警表",
          "Url": "Report/ApproachingAlert"
        },
        {
          "Id": "contractProfitCompany",
          "Class": "menu",
          "Title": "承包利润表(公司)",
          "Url": "Report/MoveContractorProfit"
        },
        {
          "Id": "contractProfitSeller",
          "Class": "menu",
          "Title": "承包利润表(业务员)",
          "Url": "Report/SaleContractorProfit"
        }
      ]
    }
  },
  {
    "Id": "money",
    "Class": "mainMenu",
    "Title": "资金",
    "Url": "/images/images.svg#money",
    "SubNodes": {
      "资金账户": [
        {
          "Id": "infoPayQrCode",
          "Class": "menu",
          "Title": "账户管理",
          "Url": "BaseInfo/PayQrCodeView"
        },
        {
          "Id": "cashBankOpeningBalance",
          "Class": "menu",
          "Title": "现金银行期初余额",
          "Url": "CwPages/CashBankOpBalView"
        },
        {
          "Id": "cashBankBalance",
          "Class": "menu",
          "Title": "现金银行余额表",
          "Url": "CwPages/CashBankBalance"
        },
        {
          "Id": "cashBankDetail",
          "Class": "menu",
          "Title": "现金银行明细账",
          "Url": "CwPages/CashBankDetail"
        },
        {
          "Id": "sheetCashBankTransfer",
          "Class": "menu",
          "Title": "转账单",
          "Url": "CwPages/CashBankTransferSheet",
          "View": true,
          "ViewUrl": "CwPages/CashBankTransferSheetView"
        },
        {
          "Id": "sheetLoan",
          "Class": "menu",
          "Title": "贷款单",
          "Url": "Sheets/LoanSheet",
          "View": true,
          "ViewUrl": "Sheets/LoanSheetView"
        },
        {
          "Id": "repayPlan",
          "Class": "menu",
          "Title": "还贷款计划",
          "Url": "Sheets/RepayPlan"
        },
        {
          "Id": "sheetRepay",
          "Class": "menu",
          "Title": "还贷款单",
          "Url": "Sheets/RepaySheet",
          "View": true,
          "ViewUrl": "Sheets/RepaySheetView"
        }
      ],
      "往来单据": [
        {
          "Id": "sheetGetArrears",
          "Class": "menu",
          "Title": "收款单",
          "Url": "Sheets/GetArrearsSheet?forPayOrGet=false",
          "View": true,
          "ViewUrl": "Sheets/GetArrearsSheetView?forPayOrGet=false&sheet_type=SK",
          "Display": null,
          "SubNodes": null
        },
        {
          "Id": "sheetGetBulkArrears",
          "Class": "menu",
          "Title": "批量收款单",
          "Url": "Sheets/GetBulkArrearsSheet?forPayOrGet=false",
          "View": true,
          "ViewUrl": "Sheets/GetBulkArrearsSheetView?forPayOrGet=false&sheet_type=PLSK",
          "Display": null,
          "SubNodes": null
        },
        {
          "Id": "sheetPayArrears",
          "Class": "menu",
          "Title": "付款单",
          "Url": "Sheets/GetArrearsSheet?forPayOrGet=true",
          "View": true,
          "ViewUrl": "Sheets/GetArrearsSheetView?forPayOrGet=true&sheet_type=FK",
          "Display": null,
          "SubNodes": null
        },
        {
          "Id": "sheetPreget",
          "Class": "menu",
          "Title": "预收款单",
          "Url": "Sheets/PrepaySheet?forPayOrGet=false",
          "View": true,
          "ViewUrl": "Sheets/PrepaySheetView?forPayOrGet=false&sheet_type=YS",
          "Display": null,
          "SubNodes": null
        },
        {
          "Id": "sheetPrepay",
          "Class": "menu",
          "Title": "预付款单",
          "Url": "Sheets/PrepaySheet?forPayOrGet=true",
          "View": true,
          "ViewUrl": "Sheets/PrepaySheetView?forPayOrGet=true&sheet_type=YF",
          "Display": null,
          "SubNodes": null
        },
        {
          "Id": "sheetFeeOut",
          "Class": "menu",
          "Title": "费用支出单",
          "Url": "Sheets/FeeOutSheet?forOutOrIn=true",
          "View": true,
          "ViewUrl": "Sheets/FeeOutSheetView?forOutOrIn=true&forSelect=0&sheet_type=ZC",
          "Display": null,
          "SubNodes": null
        },
        {
          "Id": "sheetFeeIn",
          "Class": "menu",
          "Title": "其他收入单",
          "Url": "Sheets/FeeOutSheet?forOutOrIn=false",
          "View": true,
          "ViewUrl": "Sheets/FeeOutSheetView?forOutOrIn=false&forSelect=0&sheet_type=SR",
          "Display": null,
          "SubNodes": null
        },
        {
          "Id": "arrearsBill",
          "Class": "menu",
          "Title": "欠条管理",
          "Url": "Sheets/ArrearsBillView",
          "View": false,
          "ViewUrl": "",
          "Display": null,
          "SubNodes": null
        },
        {
          "Id": "arrearsBill",
          "Class": "menu",
          "Title": "查欠条发放单",
          "Url": "Sheets/ArrearsGrantSheetView?forGrantOrRevoke=true&sheet_type=QTF",
          "View": false,
          "ViewUrl": "",
          "Display": null,
          "SubNodes": null
        },
        {
          "Id": "arrearsBill",
          "Class": "menu",
          "Title": "查欠条回收单",
          "Url": "Sheets/ArrearsGrantSheetView?forGrantOrRevoke=false&sheet_type=QTS",
          "View": false,
          "ViewUrl": "",
          "Display": null,
          "SubNodes": null
        }
        /*{
          "Id": "sheetGrantArrears",
          "Class": "menu",
          "Title": "欠条发放单",
          "Url": "Sheets/ArrearsGrantSheet?forGrantOrRevoke=true",
          "View": true,
          "ViewUrl": "Sheets/ArrearsGrantSheetView?fforGrantOrRevoke=true&sheet_type=QTF",
          "Display": null,
          "SubNodes": null
        },
        {
          "Id": "sheetRevokeArrears",
          "Class": "menu",
          "Title": "欠条回收单",
          "Url": "Sheets/ArrearsGrantSheet?forGrantOrRevoke=false",
          "View": true,
          "ViewUrl": "Sheets/ArrearsGrantSheetView?fforGrantOrRevoke=false&sheet_type=QTS",
          "Display": null,
          "SubNodes": null
        }*/

      ],
      "往来报表": [
        {
          "Id": "arrearsBlance",
          "Class": "menu",
          "Title": "应收款",
          "Url": "Report/ClientArrears"
        },
        {
          "Id": "arrearsBlanceDetail",
          "Class": "menu",
          "Title": "应收款明细",
          "Url": "Report/ClientArrearsDetail"
        },
        {
          "Id": "arrearsSheetDetail",
          "Class": "menu",
          "Title": "收款单明细",
          "Url": "Report/ArrearsSheetDetail"
        },
        {
          "Id": "pregetBalance",
          "Class": "menu",
          "Title": "预收款余额",
          "Url": "Report/PregetBalance"
        },
        {
          "Id": "clientBusinessHistory",
          "Class": "menu",
          "Title": "客户往来账",
          "Url": "Report/AccountHistory"
        },
        {
          "Id": "accountChangeSum",
          "Class": "menu",
          "Title": "客户往来汇总表",
          "Url": "Report/AccountChangeSum"
        },
        {
          "Id": "supplierArrears",
          "Class": "menu",
          "Title": "应付款",
          "Url": "Report/SupplierArrears"
        },
        {
          "Id": "prepayBalance",
          "Class": "menu",
          "Title": "预付款余额",
          "Url": "Report/PrepayBalance"
        },
        {
          "Id": "supplierBusinessHistory",
          "Class": "menu",
          "Title": "供应商往来账",
          "Url": "Report/SupplierAccountHistory"
        },
        {
          "Id": "supplierAccountChangeSum",
          "Class": "menu",
          "Title": "供应商往来汇总表",
          "Url": "Report/SupplierAccountChangeSum"
        }
      ],
      "经营报表": [
        {
          "Id": "businessProfit",
          "Class": "menu",
          "Title": "经营利润表",
          "Url": "Report/BusinessProfit"
        },
        {
          "Id": "feeOutSummary",
          "Class": "menu",
          "Title": "费用合计表",
          "Url": "Report/FeeOutSummary"
        },
        {
          "Id": "cashInOut",
          "Class": "menu",
          "Title": "现金收支表",
          "Url": "Report/CashInOut"
        },
        {
          "Id": "feeOutDetail",
          "Class": "menu",
          "Title": "现金收支明细表",
          "Url": "Report/FeeOutDetail"
        }

      ],
      "对账管理": [
        {
          "Id": "sheetGetArrearsOrder",
          "Class": "menu",
          "Title": "待对帐",
          "Url": "Sheets/ArrearsOrderManageView",
          "View": false,
          "ViewUrl": ""
        },
        {
          "Id": "sheetGetArrearsOrder",
          "Class": "menu",
          "Title": "对账中",
          "Url": "Sheets/GetArrearsOrderUnapprovedSheetView",
          "View": false,
          "ViewUrl": "",
          "Display": null,
          "SubNodes": null
        },
        {
          "Id": "sheetGetArrearsOrder",
          "Class": "menu",
          "Title": "待收款",
          "Url": "Sheets/GetArrearsOrderApprovedSheetView",
          "View": false,
          "ViewUrl": "Sheets/GetArrearsOrderSheetView",
          "Display": null,
          "SubNodes": null
        },
        {
          "Id": "sheetGetArrearsOrder",
          "Class": "menu",
          "Title": "对账汇总(客户)",
          "Url": "Sheets/ReconciliationSummaryView",
          "View": false,
          "ViewUrl": "",
          "Display": null,
          "SubNodes": null
        }

      ]

    }
  },
  {
    "Id": "accounting",
    "Class": "mainMenu",
    "Title": "财务",
    "Url": "/images/images.svg#accounting",
    "SubNodes": {
      "设置": [
        {
          "Id": "openingAccount",
          "Class": "menu",
          "Title": "财务开账",
          "Url": "CwPages/OpeningAccount"
        },
        {
          "Id": "paywaysView",
          "Class": "menu",
          "Title": "会计科目",
          "Url": "Setting/PaywaysView"
        },
        {
          "Id": "openingBalance",
          "Class": "menu",
          "Title": "科目期初",
          "Url": "CwPages/OpeningBalance",
          "View": false,
          "ViewUrl": "CwPages/OpeningBalance"
        }

      ],
      "凭证": [
        {
          "Id": "sheetVoucher",
          "Class": "menu",
          "Title": "凭证录入",
          "Url": "CwPages/CwVoucher",
          "View": true,
          "ViewUrl": "CwPages/CwVoucherView"
        },
        {
          "Id": "sheetVoucherLists",
          "Class": "menu",
          "Title": "批量生成凭证",
          "Url": "CwPages/CwVoucherLists",
          "View": false,
          "ViewUrl": "CwPages/CwVoucherListsView"
        },
        {
          "Id": "closingCarryForward",
          "Class": "menu",
          "Title": "期末结转",
          "Url": "CwPages/ClosingCarryForward",
          "View": false,
          "ViewUrl": "CwPages/ClosingCarryForward"
        }
      ],
      /*"固定资产": [
        {
          "Id": "sheetPPE",
          "Class": "menu",
          "Title": "资产管理",
          "Url": "CwPages/CwVoucherView",
          "View": false
        },
        {
          "Id": "sheetPPEType",
          "Class": "menu",
          "Title": "资产类别",
          "Url": "CwPages/PPETypeView",
          "View": false
        }
      ],*/
      "财务报表": [
        {
          "Id": "balanceSheet",
          "Class": "menu",
          "Title": "资产负债",
          "Url": "CwPages/Report/Balance",
          "View": false,
          "ViewUrl": "CwPages/Report/Balance"
        },
        {
          "Id": "profitSheet",
          "Class": "menu",
          "Title": "利润表",
          "Url": "CwPages/Report/Profit",
          "View": false,
          "ViewUrl": "CwPages/Report/Balance"
        },
        {
          "Id": "accountBalance",
          "Class": "menu",
          "Title": "科目余额表",
          "Url": "CwPages/Report/SubBalance",
          "View": false,
          "ViewUrl": "CwPages/Report/SubBalance"
        },
        {
          "Id": "detailBill",
          "Class": "menu",
          "Title": "明细账",
          "Url": "CwPages/Report/DetailAccount",
          "View": false,
          "ViewUrl": "CwPages/Report/DetailAccount"
        },
        {
          "Id": "assistBalance",
          "Class": "menu",
          "Title": "核算项目余额表",
          "Url": "CwPages/Report/AssistBalance"
        },
        {
          "Id": "assistDetail",
          "Class": "menu",
          "Title": "核算项目明细账",
          "Url": "CwPages/Report/AssistDetail"
        }
      ]
    }
  },
  {
    "Id": "report",
    "Class": "mainMenu",
    "Title": "报表",
    "Url": "/images/images.svg#report",
    "SubNodes": {
      "员工报表": [
        {
          "Id": "visitPath",
          "Class": "menu",
          "Title": "外勤轨迹",
          "Url": "Report/VisitPath"
        },
        {
          "Id": "visitPathGIS",
          "Class": "menu",
          "Title": "外勤轨迹新",
          "Url": "Report/VisitPathGIS"
        },
        //{
        //  "Id": 504,
        //  "Class": "menu",
        //  "Title": "排行榜",
        //  "Url": "Report/SellerRank"
        //},
        {
          "Id": "visitRecord",
          "Class": "menu",
          "Title": "拜访记录",
          "Url": "Report/VisitRecord"
        },
        {
          "Id": "visitSummaryBySeller",
          "Class": "menu",
          "Title": "拜访汇总",
          "Url": "Report/VisitSummaryBySeller"
        },



        {
          "Id": "commission",
          "Class": "menu",
          "Title": "提成",
          "Url": "/Report/CommissionSummary"
          //"Url": "/commission/commission"
        },
        {
          "Id": "commission",
          "Class": "menu",
          "Title": "提成明细",
          "Url": "/Report/CommissionDetail"
        },



        {
          "Id": "supcustDistribution",
          "Class": "menu",
          "Title": "客户分布",
          "Url": "Report/SupcustDistributionMap"
        },


        {
          "Id": "AttendanceReport",
          "Class": "menu",
          "Title": "考勤报表",
          "Url": "/Report/AttendanceReportView"
        },
        {
          "Id": "AttendanceReport",
          "Class": "menu",
          "Title": "考勤报表(月)",
          "Url": "/Report/AttendanceMonthReportView"
        },
        {
          "Id": "AttendanceReportAll",
          "Class": "menu",
          "Title": "考勤报表(总)",
          "Url": "/Report/AttendanceReportAllView"
        }


      ],
      "会员积分": [
        {
          "Id": "vipPointsSummary",
          "Class": "menu",
          "Title": "会员积分汇总表",
          "Url": "Report/VipPointsSummary"
        },
        {
          "Id": "vipPointsDetail",
          "Class": "menu",
          "Title": "会员积分明细表",
          "Url": "Report/VipPointsDetail"
        }
      ],
      "其他报表": [
        {
          "Id": "customerLiveness",
          "Class": "menu",
          "Title": "客户活跃度",
          "Url": "Report/CustomerLiveness"
        },
        {
          "Id": "clientValue",
          "Class": "menu",
          "Title": "客户价值分析",
          "Url": "Report/ClientValue"
        },
        {
          "Id": "clientAdd",
          "Class": "menu",
          "Title": "新增客户报表",
          "Url": "Report/ClientAdd"
        },

        {
          "Id": "businessHistoryLists",
          "Class": "menu",
          "Title": "经营历程",
          "Url": "Report/BusinessHistoryLists"
        },
        {
          "Id": "itemsOccupyReport",
          "Class": "menu",
          "Title": "铺市率报表（单品）",
          "Url": "Report/ItemOccupyReport"
        },
        {
          "Id": "brandsOccupyReport",
          "Class": "menu",
          "Title": "铺市率报表（品牌）",
          "Url": "Report/BrandOccupyReport"
        }
      ]

    }
  },
  {
    "Id": "info",
    "Class": "mainMenu",
    "Title": "档案",
    "Url": "/images/images.svg#info",
    "SubNodes": {
      "商品相关": [
        {
          "Id": "infoItem",
          "Class": "menu",
          "Title": "商品档案",
          "Url": "BaseInfo/ItemsView"
        },
        {
          "Id": "infoUnit",
          "Class": "menu",
          "Title": "计量单位",
          "Url": "BaseInfo/UnitsView"
        },
        {
          "Id": "infoBrand",
          "Class": "menu",
          "Title": "品牌",
          "Url": "BaseInfo/BrandsView"
        },
        {
          "Id": "infoBarcodeScale",
          "Class": "menu",
          "Title": "条码秤组",
          "Url": "BaseInfo/BarcodeScaleGroupView"
        },
        {
          "Id": "infoPrice",
          "Class": "menu",
          "Title": "价格方案",
          "Url": "BaseInfo/PriceView"
        },
        {
          "Id": "priceStrategy",
          "Class": "menu",
          "Title": "价格策略",
          "Url": "BaseInfo/PriceStrategy"
        },
        {
          "Id": "preSalePrice",
          "Class": "menu",
          "Title": "上次售价",
          "Url": "BaseInfo/PreSalePrice"
        },
        {

          "Id": "CombineTemp",
          "Class": "menu",
          "Title": "组装拆分模型",
          "Url": "BaseInfo/CombineTempView"
        },
        {
          "Id": "docChangeLog",
          "Class": "menu",
          "Title": "商品档案变化表",
          "Url": "Report/DocChange?obj_name=商品档案"
        }
      ],
      "客户相关": [
        {
          "Id": "infoClient",
          "Class": "menu",
          "Title": "客户档案",
          "Url": "BaseInfo/ClientsView"
        },
        {
          "Id": "infoGroup",
          "Class": "menu",
          "Title": "渠道档案",
          "Url": "BaseInfo/GroupsView"
        },
        {
          "Id": "infoRank",
          "Class": "menu",
          "Title": "客户等级",
          "Url": "BaseInfo/RanksView"
        },
        {
          "Id": "infoSupplier",
          "Class": "menu",
          "Title": "供应商档案",
          "Url": "BaseInfo/SuppliersView"
        },
        {
          "Id": "infoFeeUnit",
          "Class": "menu",
          "Title": "费用单位档案",
          "Url": "BaseInfo/FeeUnitsView"
        },
        {
          "Id": "infoAcctWay",
          "Class": "menu",
          "Title": "结算方式",
          "Url": "BaseInfo/AcctTypeDetailView" //memu中添加支付档案
        },

        {
          "Id": "docChangeLog",
          "Class": "menu",
          "Title": "客户档案变化表",
          "Url": "Report/DocChange?obj_name=客户档案"
        },
        {
          "Id": "arrearsStrategy",
          "Class": "menu",
          "Title": "欠款策略",
          "Url": "BaseInfo/ArrearsStrategy"
        }
      ],
      "员工相关": [
        {
          "Id": "infoOperator",
          "Class": "menu",
          "Title": "员工档案",
          "Url": "BaseInfo/OperatorsView"
        },
        {
          "Id": "infoRole",
          "Class": "menu",
          "Title": "员工角色",
          "Url": "BaseInfo/RolesView"
        },
        {
          "Id": "commissionPlan",
          "Class": "menu",
          "Title": "提成方案",
          "Url": "/commission/plan"
        },
        {
          "Id": "commissionStrategiesView",
          "Class": "menu",
          "Title": "提成策略",
          "Url": "BaseInfo/CommissionStrategiesView"
        },
        {
          "Id": "visitSchedule",
          "Class": "menu",
          "Title": "拜访行程",
          "Url": "BaseInfo/VisitDayView"
        },
        {
          "Id": "attendanceSetting",
          "Class": "menu",
          "Title": "考勤设置",
          "Url": "BaseInfo/AttendanceSettingView"
        },
        {
          "Id": "visitStandard",
          "Class": "menu",
          "Title": "拜访规范",
          "Url": "BaseInfo/VisitStandard"
        }
      ],
      "其他": [
        {
          "Id": "infoBranch",
          "Class": "menu",
          "Title": "仓库档案",
          "Url": "BaseInfo/BranchsView"

        },
        {
          "Id": "infoLoanPartner",
          "Class": "menu",
          "Title": "借贷款单位档案",
          "Url": "BaseInfo/LoanPartnersView"

        },
        {
          "Id": "infoBranchPositionType",
          "Class": "menu",
          "Title": "库位类型档案",
          "Url": "BaseInfo/BranchPositionTypeView"

        },
        {
          "Id": "infoPayQrCode",
          "Class": "menu",
          "Title": "账户管理",
          "Url": "BaseInfo/PayQrCodeView"
        },
        {
          "Id": "infoClientItemCode",
          "Class": "menu",
          "Title": "客户限定商品",
          "Url": "BaseInfo/ItemsMatchesView"
        },
        {
          "Id": "infoClientItemMatch",
          "Class": "menu",
          "Title": "客品匹配表",
          "Url": "BaseInfo/ItemCustomMatchView"
        }
      ],
      "二级分销": [
        {
          "Id": "rsPlan",
          "Class": "menu",
          "Title": "分销方案",
          "Url": "BaseInfo/RsPlanView"
        },
        {
          "Id": "rsSeller",
          "Class": "menu",
          "Title": "分销商管理",
          "Url": "BaseInfo/RsSellerView"
        }
      ]
    }
  },
  {
    "Id": "setting",
    "Class": "mainMenu",
    "Title": "设置",
    "Url": "/images/images.svg#setting",
    "SubNodes": {
      "基本设置": [
        {
          "Id": "companySetting",
          "Class": "menu",
          "Title": "公司设置",
          "Url": "Setting/CompanySetting"
        },
        {
          "Id": "infoImport",
          "Class": "menu",
          "Title": "档案导入",
          "Url": "Setting/ImportInfo"
        },
        {
          "Id": "emartSetting",
          "Class": "menu",
          "Title": "电商对接",
          "Url": "Setting/EmartSetting"
        },
        {
          "Id": "bizMonthClose",
          "Class": "menu",
          "Title": "业务结账",
          "Url": "Setting/BizMonthlyClosing"
        }

        //{
        //  "Id": 702,
        //  "Class": "menu",
        //  "Title": "公司开户",
        //  "Url": "Setting/OpenAccount"
        //},

      ],
      "其他": [
        {
          "Id": "infoPrintTemplate",
          "Class": "menu",
          "Title": "打印模板",
          "Url": "Setting/PrintTemplateView"
        },
        {
          "Id": "infoReceiptTemplate",
          "Class": "menu",
          "Title": "小票打印模板",
          "Url": "Setting/ReceiptTemplateView"
        },
        {
          "Id": "setCloudPrinter",
          "Class": "menu",
          "Title": "云打印机",
          "Url": "Setting/CloudPrintersView"
        },
        {
          "Id": "paywaysView",
          "Class": "menu",
          "Title": "会计科目",
          "Url": "Setting/PaywaysView"
        },
        {
          "Id": "briefsView",
          "Class": "menu",
          "Title": "备注信息",
          "Url": "Setting/BriefsView"
        }
        //{
        //  "Id": 821,
        //  "Class": "menu",
        //  "Title": "支付方式",
        //  "Url": "Setting/PaywaysQuickView"
        //}

      ],
      "消息推送": [
        {
          "Id": "msgSubscribeCompany",
          "Class": "menu",
          "Title": "员工消息订阅",
          "Url": "Setting/MsgSubscribeCompany"
        }
      ],
      "商城": [
        {
          "Id": "mallSetting",
          "Class": "menu",
          "Title": "商城配置",
          "Url": "Mall/MallSetting"
        },
        {
          "Id": "onsaleTemplate",
          "Class": "menu",
          "Title": "上架管理",
          "Url": "Mall/OnSaleTemplate"
        },
        {
          "Id": "mainpageMaintenance",
          "Class": "menu",
          "Title": "装修模板",
          "Url": "Mall/MainpageMaintenance"
        },
        {
          "Id": "mallUser",
          "Class": "menu",
          "Title": "商城用户",
          "Url": "Mall/MallUser"
        },
        {
          "Id": "mallNotice",
          "Class": "menu",
          "Title": "商城公告",
          "Url": "Mall/MallNoticeView"
        }
      ],
      "会员积分": [
        {
          "Id": "vipLevel",
          "Class": "menu",
          "Title": "会员等级",
          "Url": "VipSetting/VipLevelsView"
        },
        {
          "Id": "vipPlan",
          "Class": "menu",
          "Title": "会员方案",
          "Url": "VipSetting/VipPlansView"
        },
        {
          "Id": "vipStrategy",
          "Class": "menu",
          "Title": "会员策略",
          "Url": "VipSetting/VipStrategy"
        }
      ]
    }
  }
]
