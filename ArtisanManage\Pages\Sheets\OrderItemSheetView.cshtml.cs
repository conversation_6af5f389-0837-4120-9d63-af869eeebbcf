﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using static ArtisanManage.Models.DataItem;

namespace ArtisanManage.Pages.BaseInfo
{
    public class OrderItemSheetViewModel : PageQueryModel
    { 
        public OrderItemSheetViewModel(CMySbCommand cmd) : base(Services.MenuId.orderItemSheet)
        {
            this.cmd = cmd;
            CanQueryByApproveTime = true;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="sp.happen_time", CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期", FldArea="divHead",CtrlType="jqxDateTimeInput", SqlFld="sp.happen_time",   CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"depart_path",new DataItem(){Title="部门",Hidden=true, FldArea="divHead",LabelFld="depart_path_label", CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=true,DropDownWidth="150", CompareOperator="like",LikeWrapper="/",
                    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"
                }},
                {"supcust_id",new DataItem(){FldArea="divHead",Title="客户名称",LabelFld="sup_name",ButtonUsage="list",CompareOperator="=",DropDownWidth = "200",SqlFld = "sp.supcust_id",
                SqlForOptions=CommonTool.selectSupcust } },
                {"seller_id",new DataItem(){FldArea="divHead",Title="业务员",LabelFld="seller_name",ButtonUsage="list",SqlFld="sp.getter_id" ,SqlForOptions=CommonTool.selectSellers,CompareOperator="="}},//SqlForOptions="select oper_id as v,oper_name as l,py_str as z from info_operator"
                {"status",new DataItem(){FldArea="divHead",Title="单据状态",LabelFld = "status_name",ButtonUsage = "list",CompareOperator="=",Value="normal",Label="正常单据",
                        Source = @"[{v:'normal',l:'正常单据',condition:""sp.red_flag is null""},
                                   {v:'unapproved',l:'未审核单据',condition:""sp.approve_time is null""},
                                   {v:'approved',l:'已审核单据',condition:""sp.approve_time is not null and red_flag is null""},
                                   {v:'red',l:'红冲单',condition:""sp.red_flag = 1""},
                                   {v:'all',l:'红字单',condition:""sp.red_flag = 2""},
                                    {v:'all',l:'所有',condition:""true""}]"
                }},
                {"sheet_no",new DataItem(){FldArea="divHead",Title="单号", CompareOperator="like"}},
                {"prepay_sub_id",new DataItem(){FldArea="divHead",Title="定货款账户",LabelFld="prepay_sub_name",ButtonUsage="list",CompareOperator="=",SqlFld="sp.prepay_sub_id",
                    SqlForOptions="select sub_id as v,sub_name as l,py_str as z from cw_subject where company_id=~COMPANY_ID and sub_type='YS' and is_order=true order by sub_name"}},
                {"byHappenTime",new DataItem(){FldArea="divHead",Title="按交易时间查询",CtrlType="jqxCheckBox",ForQuery=false}}
            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"sheet_id",   new DataItem(){Title="sheet_id", Hidden=true,HideOnLoad = true, Linkable=true, Width="80"}},
                       {"sheet_no",   new DataItem(){Title="单据编号",  Linkable=true, Width="150"}},
                       {"sheet_type", new DataItem(){Title="单据类型",  Width="80",SqlFld="(case WHEN sp.sheet_type='DH' THEN '定货单' END)"}},
                       {"sheet_status", new DataItem(){Title="状态",  Width="80",SqlFld="(case when sp.red_flag='2' then '红字单' when sp.red_flag='1' then '已红冲' when sp.approve_time is null then '未审' else '已审' END)"}},
                       {"sup_name",   new DataItem(){Title="客户",   Width="180",DropDownWidth = "200"}},
                       {"happen_time",   new DataItem(){Title="交易时间",   Width="150"}},
                       {"approve_time",   new DataItem(){Title="审核时间",   Width="150"}},
                       {"seller_name", new DataItem(){Title="业务员",  Width="80",Sortable=true}},
                       {"maker_name", new DataItem(){Title="制单人",  Width="80",Sortable=true}},
                       {"total_amount", new DataItem(){Title="总额",  Width="80",ShowSum=true}},
					   {"now_pay_amount", new DataItem(){Title="收款", Width="80",ShowSum=true,SqlFld="now_pay_amount"}},
                       {"disc_amount", new DataItem(){Title="优惠金额", Width="80",ShowSum=true,SqlFld="disc_amount"}},
                       {"left_amount", new DataItem(){Title="欠款",  Width="80",ShowSum=true,SqlFld="total_amount-now_pay_amount-now_disc_amount"}},
					   {"follow_pay_amount", new DataItem(){Title="后续收款", Width="80",ShowSum=true,SqlFld="paid_amount-now_pay_amount"}},					  					   
					   {"paid_amount", new DataItem(){Title="已收金额", Width="80",ShowSum=true,SqlFld="paid_amount"}},
					   {"still_debt", new DataItem(){Title="尚欠金额",Width="80",ShowSum=true,SqlFld="(total_amount-paid_amount-now_disc_amount)",
                        }},
                       {"check_appendix_photos", new DataItem(){Title="附件", Width="100",SqlFld="case when sheet_attribute->>'appendixPhotos' is not null and sheet_attribute->>'appendixPhotos' <> '[]'  then '查看' end",Linkable=true,
                            }
                        },
                       { "appendix_photos",new DataItem(){Title="照片对象",SqlFld="sheet_attribute->>'appendixPhotos'", Hidden=true,HideOnLoad=true} },
                       {"pay_ways",  new DataItem(){Title="支付方式", Width="60",
                           FuncGetSubColumns = async (col) =>
                           {
                                ColumnsResult result=new ColumnsResult();
                                Dictionary<string,DataItem> subColumns=new Dictionary<string,DataItem>();
                                List<System.Dynamic.ExpandoObject> payways =  await CDbDealer.GetRecordsFromSQLAsync($"select sub_id,sub_name from cw_subject where company_id={company_id} and sub_type = 'QT';",cmd);
                                string flds="";
                                Dictionary<string,decimal> pwAmtSum= new Dictionary<string,decimal>();
                               foreach(dynamic pw in payways)
                               {
                                   //if(pwAmtSum.ContainsKey(pw.sub_id))
                               }
                                foreach(dynamic pw in payways)
                                {
                                   var subcol=new DataItem();
                                   subcol.Width=col.Width;
                                   subcol.Title=pw.sub_name;
                                   subcol.ShowSum=true;
                                   string colKey="pw_"+ (string)pw.sub_id;
                                   subColumns.Add(colKey, subcol);
                                   //string fld=$@" case when payway1_id='{pw.sub_id}' and payway2_id<>'{pw.sub_id}' and payway3_id<>'{pw.sub_id}' then payway1_amount 
                                   //         when payway1_id<>'{pw.sub_id}' and payway2_id='{pw.sub_id}' and payway3_id<>'{pw.sub_id}' then payway2_amount 
                                   //         when payway1_id<>'{pw.sub_id}' and payway2_id<>'{pw.sub_id}' and payway3_id='{pw.sub_id}' then payway3_amount 
                                   //         when payway1_id='{pw.sub_id}' and payway1_id=payway2_id and payway1_id<>payway3_id then payway1_amount+payway2_amount
                                   //         when payway2_id='{pw.sub_id}' and payway2_id=payway3_id and payway2_id<>payway1_id then payway2_amount+payway3_amount
                                   //         when payway1_id='{pw.sub_id}' and payway1_id=payway3_id and payway1_id<>payway2_id then payway1_amount+payway3_amount
                                   //         when payway1_id='{pw.sub_id}' and payway1_id=payway2_id and payway1_id=payway3_id then payway1_amount+payway2_amount+payway3_amount
                                   //         end as {colKey}";
                                   string fld=$" case when payway1_id='{pw.sub_id}' then payway1_amount*money_inout_flag when payway2_id='{pw.sub_id}' then payway2_amount*money_inout_flag when payway3_id='{pw.sub_id}' then payway3_amount*money_inout_flag else null end as {colKey}";
                                   if(flds!="") flds+=",";
                                   flds+=fld;
                                }
                                result.FldsSQL=flds;
                                result.Columns=subColumns;
                                return result;
                           }
                       }} ,
                       {"make_brief", new DataItem(){Title="备注", Width="100"}},
                     },
                     QueryFromSQL=@"
from sheet_prepay sp 
LEFT JOIN info_supcust s on sp.supcust_id = s.supcust_id and s.company_id=~COMPANY_ID
LEFT JOIN (select oper_id, oper_name as maker_name, depart_path from info_operator where company_id= ~COMPANY_ID) maker on sp.maker_id = maker.oper_id
LEFT JOIN (select oper_id,oper_name as seller_name from info_operator where company_id= ~COMPANY_ID) seller on sp.getter_id = seller.oper_id
LEFT JOIN (select sub_id, sub_name as prepay_sub_name from cw_subject where company_id= ~COMPANY_ID) prepay_sub on sp.prepay_sub_id = prepay_sub.sub_id where sp.company_id= ~COMPANY_ID and sheet_type='DH' ~SQL_VARIABLE1",

                     QueryOrderSQL=" order by happen_time desc"
                  }
                }
            };
        }
        public async Task OnGet()
        {
            await InitGet(cmd);
            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }
    }



    [Route("api/[controller]/[action]")]
    public class OrderItemSheetViewController : QueryController
    { 
        public OrderItemSheetViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            OrderItemSheetViewModel model = new OrderItemSheetViewModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }

        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            OrderItemSheetViewModel model = new OrderItemSheetViewModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }
         [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            OrderItemSheetViewModel model = new OrderItemSheetViewModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }
    }
}
